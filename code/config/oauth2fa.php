<?php

return [

    /*
    |--------------------------------------------------------------------------
    | View Paths for Authentication Screens
    |--------------------------------------------------------------------------
    |
    | These view paths define which Blade templates will be rendered for
    | different authentication-related screens used by Fortify / Passport.
    | You can customize these to point to your own Blade files.
    |
    | 'login'             → The login page view.
    | 'confirmPassword'   → The password confirmation view for the two factor confirm password.
    | 'twoFactorChallenge'→ The two-factor authentication challenge view.
    |
    */
    'views' => [
        'login' => 'login',
        'confirmPassword' => 'oauth-and-2fa::2fa.confirm-password',
        'twoFactorChallenge' => 'oauth-and-2fa::2fa.two-factor-challenge',
    ],

    /*
    |--------------------------------------------------------------------------
    | Pre-Authorization Guards
    |--------------------------------------------------------------------------
    |
    | This array lets you register one or more "pre-authorization guards".
    | Each guard class must implement the PreAuthorizeGuard contract.
    | Before rendering the authorization page or automatically approving a
    | trusted device, the package will call each guard in order.
    |
    | If a checker returns a PreAuthorizeDenial, the request is blocked and
    | an OAuth-style JSON error (error, error_description, hint, message)
    | is sent back to the client. Use this to plug in per-project business
    | rules such as blocking certain roles, restricting IP ranges, time
    | windows, etc., without modifying the package itself.
    |
    */
    'pre_authorize_guards' => [
        \App\Guards\Oauth\CheckSuperAdminGuard::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Passport Access Token Expiry (in minutes)
    |--------------------------------------------------------------------------
    |
    | This value determines how long (in minutes) issued Passport access tokens
    | will remain valid before expiring. After expiry, the client must use the
    | refresh token or re-authenticate to obtain a new access token.
    |
    */
    'access_token_expiry' => env('OAUTH_ACCESS_TOKEN_EXPIRY', 60), // 60 minutes (1 hour)

    /*
    |--------------------------------------------------------------------------
    | Passport Refresh Token Expiry (in minutes)
    |--------------------------------------------------------------------------
    |
    | This value determines how long (in minutes) issued Passport refresh tokens
    | will remain valid before expiring. Once expired, a new login or
    | authorization flow is required to generate fresh tokens.
    |
    | Default here is 30 days: 30 * 24 * 60 = 43,200 minutes.
    |
    */
    'refresh_token_expiry' => env('OAUTH_REFRESH_TOKEN_EXPIRY', 30 * 24 * 60),

    /*
    |--------------------------------------------------------------------------
    | Retention Windows (in hours)
    |--------------------------------------------------------------------------
    | Delete records that are either revoked OR expired OR older than
    | the retention window below (based on created_at).
    */

    // Authorization codes (short-lived; e.g., 24h)
    'auth_codes_retention_hours' => env('OAUTH_CLEANUP_AUTH_CODES_HOURS', 24),

    // Access tokens (e.g., 720h = 30 days) — set to 0 to disable "older than" clause
    'access_tokens_retention_hours' => env('OAUTH_CLEANUP_ACCESS_TOKENS_HOURS', 72),

    // Refresh tokens (e.g., 2160h = 90 days)
    'refresh_tokens_retention_hours' => env('OAUTH_CLEANUP_REFRESH_TOKENS_HOURS', 2160),

    /*    
    |--------------------------------------------------------------------------
    | OAuth Client Request Logging
    |--------------------------------------------------------------------------
    | This option enables logging of OAuth client requests to a dedicated
    | database table. Each request is logged with details such as client ID,
    | user ID (if applicable), request path, method, scopes, etc.
    | This can be useful for auditing and monitoring client activity.
    | You can control log retention and whether to queue inserts.
    */
    'log_client_requests' => env('OAUTH_CLIENT_LOGS_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Retention (in hours)
    |--------------------------------------------------------------------------
    | How long to retain OAuth client request logs.
    | Example: 24 = keep last 24 hours.
    */
    'client_log_retention_hours' => env('OAUTH_CLIENT_LOGS_RETENTION_HOURS', 24),

    /*
    |--------------------------------------------------------------------------
    | Queue Inserts
    |--------------------------------------------------------------------------
    | If true, logs are written via a queued job to avoid adding latency.
    */
    'oauth_log_queue' => env('OAUTH_CLIENT_LOGS_QUEUE', false),

    /*
    |--------------------------------------------------------------------------
    | Check Scopes
    |--------------------------------------------------------------------------
    | If true, the package will check that the scopes requested by the client
    | are valid. (only added to support the older versions and only will be used on client credentials)
    */
    'oauth_check_scopes' => env('OAUTH_CHECK_SCOPES', false),

    'oidc_guard' => env('OIDC_GUARD', 'passport')
];
