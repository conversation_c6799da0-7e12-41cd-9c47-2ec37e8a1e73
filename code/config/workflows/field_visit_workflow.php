<?php

use App\Action\SendNotificationAction;
use App\Guards\ApproveRequestGuard;
use App\Guards\AssignRequestGuard;
use App\Guards\CancelRequestGuard;
use App\Guards\RejectEdfRequestGuard;
use App\Guards\VerifyRequestGuard;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Jobs\SendAllocateNotificationJob;
use App\Jobs\SendAssignNotificationJob;
use App\Jobs\SendTicketNotificationJob;

return [

    'states' => [WorkflowState::SUBMITTED, WorkflowState::APPROVED, WorkflowState::VERIFIED, WorkFlowState::REJECTED],
    'performers' => [WorkflowPerformer::VERIFIER, WorkflowPerformer::APPROVER],
    'initial_state' => WorkflowState::SUBMITTED,
    'final_states' => [WorkflowState::APPROVED, WorkflowState::REJECTED],
    'verification_level' => 1,
    'transitions' => [
        'verify' => [
            'from' => WorkflowState::SUBMITTED,
            'to' => WorkflowState::VERIFIED,
            'guards' => [
                [VerifyRequestGuard::class],
            ],
            'actions' => [
                [SendNotificationAction::class, ['supervisor' => WorkflowState::VERIFIED]],
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [SendTicketNotificationJob::class],
            'failmetaData' => [],
            'failJob' => [],

        ],
        'approve' => [
            'from' => [WorkflowState::VERIFIED, WorkflowState::SUBMITTED],
            'to' => WorkflowState::APPROVED,
            'guards' => [
                [ApproveRequestGuard::class]
            ],
            'actions' => [
                [SendNotificationAction::class, ['hod' => WorkflowState::APPROVED]]
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [SendTicketNotificationJob::class],
            'failMetaData' => [],
            'failJob' => [],
        ],
        'reject' => [
            'from' => [workFlowState::SUBMITTED, WorkflowState::VERIFIED],
            'to' => [WorkflowState::REJECTED],
            'guards' => [
                [RejectEdfRequestGuard::class]
            ],
            'actions' => [],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [SendTicketNotificationJob::class],
            'failMetadata' => [],
            'failJob' => [],

        ],
    ]
];
