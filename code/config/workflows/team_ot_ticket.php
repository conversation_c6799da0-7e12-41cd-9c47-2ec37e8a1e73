<?php

use App\Action\SendNotificationAction;
use App\Guards\ApproveRequestGuard;
use App\Guards\CancelRequestGuard;
use App\Guards\RejectRequestGuard;
use App\Guards\VerifyRequestGuard;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Jobs\SendTicketNotificationJob;

return [
    'states' => [WorkflowState::SUBMITTED, WorkflowState::VERIFIED, WorkflowState::APPROVED, WorkflowState::REJECTED],
    'performers' => [WorkflowPerformer::VERIFIER, WorkflowPerformer::APPROVER],
    'initial_state' => WorkflowState::SUBMITTED,
    'final_states' => [WorkflowState::APPROVED, WorkflowState::REJECTED],
    'verification_level' => 1,
    'transitions' => [
        'cancel' => [
            'from' => [WorkflowState::SUBMITTED],
            'to' => WorkflowState::CANCELLED,
            'guards' => [
                [CancelRequestGuard::class],
            ],
            'actions' => [
                [SendNotificationAction::class, ['supervisor' => WorkflowState::VERIFIED]]
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [],
            'failMetadata' => [],
            'failJob' => []
        ],
        'verify' => [
            'from' => [WorkflowState::SUBMITTED],
            'to' => WorkflowState::VERIFIED,
            'guards' => [
                [VerifyRequestGuard::class],
            ],
            'actions' => [
                [SendNotificationAction::class, ['supervisor' => WorkflowState::VERIFIED]]
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [],
            'failMetadata' => [],
            'failJob' => []
        ],
        'approve' => [
            'from' => [WorkflowState::SUBMITTED, WorkflowState::VERIFIED],
            'to' => WorkflowState::APPROVED,
            'guards' => [
                [ApproveRequestGuard::class],
            ],
            'actions' => [
                [SendNotificationAction::class, ['hod' => WorkflowState::APPROVED]],
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [SendTicketNotificationJob::class],
            'failMetadata' => [],
            'failJob' => [],
        ],
        'reject' => [
            'from' => [WorkflowState::SUBMITTED, WorkflowState::VERIFIED],
            'to' => WorkflowState::REJECTED,
            'guards' => [
                [RejectRequestGuard::class],
            ],
            'actions' => [
                [SendNotificationAction::class, ['hod' => WorkflowState::APPROVED]],
            ],
            'success_metadata' => ['key' => 'value'],
            'success_jobs' => [SendTicketNotificationJob::class],
            'failMetadata' => [],
            'failJob' => [],
        ]
    ]
];
