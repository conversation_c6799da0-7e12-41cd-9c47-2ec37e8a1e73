<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;

// test
Route::prefix('test')->group(function () {
    Route::get('/login', [App\Http\Controllers\TestOauthController::class, 'loginPage'])->name('testLogin');
    Route::get('/home', [App\Http\Controllers\TestOauthController::class, 'homePage'])->name('testHome');
    Route::get('/login/callback', [App\Http\Controllers\TestOauthController::class, 'callback2'])->name('testCallback');
    Route::post('/logout', function () {
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();
    });
    Route::get('/sync-att-logs-from-api', [App\Http\Controllers\TestOauthController::class, 'syncAttendanceLogs']);

    Route::get('/oidc/login', [App\Http\Controllers\TestOidcController::class, 'loginPage'])->name('oidcLogin');
    Route::get('/oidc/callback', [App\Http\Controllers\TestOidcController::class, 'callback'])->name('oidcCallback');
    Route::get('/oidc/profile', [App\Http\Controllers\TestOidcController::class, 'profilePage'])->name('oidcProfile');
});