
(function () {
    // ====== CONFIG (tune safely) ======
    // const BALLOON_COLORS = ["#FFB6C1", "#87CEFA", "#98FB98", "#FFD700", "#FF69B4", "#BA55D3", "#FFA07A"];
    const FIREWORK_COLORS = ["#ff595e", "#ffca3a", "#8ac926", "#1982c4", "#6a4c93"];
    const MAX_BALLOONS = 50;
    const BALLOON_SPAWN_RPS = 1.5;
    const MAX_PARTICLES = 300;
    const PARTICLES_PER_BURST = 48;
    const FIREWORKS_RPS = 1.0;
    const RUN_DURATION_MS = 30000;
    const DPR = Math.max(1, Math.min(window.devicePixelRatio || 1, 2));

    // Wait for DOM to be ready
    function initCelebration() {
        // ====== DOM (cached) ======
        const overlay = document.querySelector(".popup-overlay");
        if (!overlay) return;

        const type = overlay.dataset.celebration || "birthday";
        const BALLOON_COLORS =
            type === "anniversary"
                ? ["#1E90FF", "#FFD700"] // Blue + Yellow
                : type === "work-and-birthday"
                    ? ["#FF69B4", "#FFD700", "#1E90FF","#98FB98"] 
                    : ["#FFB6C1", "#87CEFA", "#98FB98", "#FFD700", "#FF69B4", "#BA55D3", "#FFA07A"];

        const canvasB = document.getElementById("balloonCanvas");
        const canvasF = document.getElementById("fireworksCanvas");
        const canvasC = document.getElementById("confettiCanvas");

        if (!canvasB || !canvasF || !canvasC) {
            console.warn("Celebration canvases not found");
            return;
        }

        const bctx = canvasB.getContext("2d");
        const fctx = canvasF.getContext("2d");

        // ====== SIZING ======
        let vw = 0, vh = 0;

        function resizeCanvases() {
            const w = Math.floor(window.innerWidth);
            const h = Math.floor(window.innerHeight);
            if (w === vw && h === vh) return;
            vw = w;
            vh = h;
            [canvasB, canvasF, canvasC].forEach(cv => {
                cv.width = Math.floor(vw * DPR);
                cv.height = Math.floor(vh * DPR);
                cv.style.width = vw + "px";
                cv.style.height = vh + "px";
                const ctx = cv.getContext("2d");
                ctx.setTransform(DPR, 0, 0, DPR, 0, 0);
                ctx.imageSmoothingEnabled = true;
            });
        }

        let resizeScheduled = false;
        window.addEventListener("resize", () => {
            if (resizeScheduled) return;
            resizeScheduled = true;
            requestAnimationFrame(() => {
                resizeScheduled = false;
                resizeCanvases();
            });
        }, { passive: true });

        resizeCanvases();

        // ====== POOLS ======
        const balloons = [];
        const balloonPool = [];
        const particles = [];
        const particlePool = [];

        function getBalloon() {
            return balloonPool.pop() || {
                x: 0, y: 0, r: 0, color: "#fff",
                vy: 0, stringLen: 0, swing: 0, angle: 0
            };
        }

        function releaseBalloon(b) {
            balloonPool.push(b);
        }

        function getParticle() {
            return particlePool.pop() || {
                x: 0, y: 0, color: "#fff", radius: 0,
                angle: 0, speed: 0, life: 0, maxLife: 0
            };
        }

        function releaseParticle(p) {
            particlePool.push(p);
        }

        // ====== HELPERS ======
        function rand(min, max) {
            return min + Math.random() * (max - min);
        }

        function choose(arr) {
            return arr[(Math.random() * arr.length) | 0];
        }

        function spawnBalloon() {
            if (balloons.length >= MAX_BALLOONS) return;
            const b = getBalloon();
            b.x = Math.random() * vw;
            b.y = vh + 50;
            b.r = rand(28, 52);
            b.color = choose(BALLOON_COLORS);
            b.vy = rand(40, 110);
            b.stringLen = rand(30, 55);
            b.swing = rand(0.6, 1.2);
            b.angle = Math.random() * Math.PI * 2;
            balloons.push(b);
        }

        function spawnFirework(cx, cy) {
            const canAdd = Math.max(0, MAX_PARTICLES - particles.length);
            const toAdd = Math.min(PARTICLES_PER_BURST, canAdd);
            for (let i = 0; i < toAdd; i++) {
                const p = getParticle();
                p.x = cx;
                p.y = cy;
                p.color = choose(FIREWORK_COLORS);
                p.radius = rand(1.5, 3.2);
                p.angle = Math.random() * Math.PI * 2;
                p.speed = rand(100, 200);
                p.maxLife = rand(0.7, 1.2);
                p.life = p.maxLife;
                particles.push(p);
            }
        }

        function confettiBurst() {
            if (typeof window.confetti === "function") {
                window.confetti({
                    particleCount: 90,
                    spread: 90,
                    origin: { y: 0.6 }
                });
            }
        }

        // ====== STATE / LOOP ======
        let running = false;
        let rafId = 0;
        let elapsedMs = 0;
        let lastTs = 0;
        let balloonSpawnAcc = 0;
        let fireworkAcc = 0;

        function startLoop() {
            if (running) return;
            running = true;
            elapsedMs = 0;
            lastTs = performance.now();
            balloonSpawnAcc = 0;
            fireworkAcc = 0;
            [canvasB, canvasF, canvasC].forEach(c => c.style.opacity = "1");
            loop();
        }

        function stopLoop() {
            running = false;
            cancelAnimationFrame(rafId);
            [canvasB, canvasF, canvasC].forEach(c => {
                c.style.transition = "opacity 1s ease";
                c.style.opacity = "0";
                setTimeout(() => {
                    c.getContext("2d").clearRect(0, 0, vw, vh);
                }, 1000);
            });

            setTimeout(() => {
                while (balloons.length) releaseBalloon(balloons.pop());
                while (particles.length) releaseParticle(particles.pop());
            }, 1000);
        }

        function loop() {
            if (!running) return;
            rafId = requestAnimationFrame(loop);
            const now = performance.now();
            let dt = (now - lastTs) / 1000;
            lastTs = now;
            if (dt > 0.05) dt = 0.05;

            elapsedMs += dt * 1000;

            // Spawn control
            balloonSpawnAcc += dt * BALLOON_SPAWN_RPS;
            while (balloonSpawnAcc >= 1) {
                spawnBalloon();
                balloonSpawnAcc -= 1;
            }

            fireworkAcc += dt * FIREWORKS_RPS;
            while (fireworkAcc >= 1) {
                spawnFirework(Math.random() * vw, Math.random() * (vh * 0.5));
                if (Math.random() < 0.3) confettiBurst();
                fireworkAcc -= 1;
            }

            // Update + draw balloons
            bctx.clearRect(0, 0, vw, vh);
            for (let i = balloons.length - 1; i >= 0; i--) {
                const b = balloons[i];
                b.y -= b.vy * dt;
                b.angle += b.swing * dt;
                b.x += Math.sin(b.angle) * 20 * dt;

                // Draw balloon
                bctx.beginPath();
                bctx.ellipse(b.x, b.y, b.r * 0.8, b.r, 0, 0, Math.PI * 2);
                bctx.fillStyle = b.color;
                bctx.fill();
                bctx.strokeStyle = "rgba(255,255,255,0.6)";
                bctx.lineWidth = 2;
                bctx.stroke();

                // Highlight
                bctx.beginPath();
                bctx.arc(b.x - b.r / 3, b.y - b.r / 3, b.r / 6, 0, Math.PI * 2);
                bctx.fillStyle = "rgba(255,255,255,0.35)";
                bctx.fill();

                // String
                bctx.beginPath();
                bctx.moveTo(b.x, b.y + b.r);
                bctx.lineTo(b.x, b.y + b.r + b.stringLen);
                bctx.strokeStyle = "#444";
                bctx.lineWidth = 1;
                bctx.stroke();

                if (b.y < -100) {
                    releaseBalloon(balloons.splice(i, 1)[0]);
                }
            }

            // Update + draw fireworks
            fctx.clearRect(0, 0, vw, vh);
            for (let i = particles.length - 1; i >= 0; i--) {
                const p = particles[i];
                p.x += Math.cos(p.angle) * p.speed * dt;
                p.y += Math.sin(p.angle) * p.speed * dt + 60 * dt;
                p.life -= dt;

                const alpha = Math.max(0, p.life / p.maxLife);
                fctx.save();
                fctx.globalAlpha = alpha;
                fctx.beginPath();
                fctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                fctx.fillStyle = p.color;
                fctx.fill();
                fctx.restore();

                if (p.life <= 0) {
                    releaseParticle(particles.splice(i, 1)[0]);
                }
            }

            if (elapsedMs >= RUN_DURATION_MS) {
                stopLoop();
            }
        }

        // ====== PUBLIC API ======
        window.repeatCelebration = function () {
            stopLoop();
            setTimeout(startLoop, 150);
        };

        window.closePopup = function () {
            stopLoop();
            const ov = document.querySelector(".popup-overlay");
            if (ov) ov.remove();
        };

        // Expose start/stop for external control
        window.startCelebration = startLoop;
        window.stopCelebration = stopLoop;

        // Auto-start
        startLoop();
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCelebration);
    } else {
        initCelebration();
    }
})();