body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

/* Overlay for all celebration popups */
.popup-overlay {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    overflow: hidden;
    backdrop-filter: saturate(120%) blur(2px);
    padding: 20px;
}

/* Canvases for balloons, fireworks, confetti */
#fireworksCanvas,
#confettiCanvas,
#balloonCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    transition: opacity 1s ease;
}

/* Base flyer */
.celebration-flyer {
    position: relative;
    border-radius: 20px;
    padding: 30px;
    max-width: 700px;
    width: 90%;
    min-height: 300px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, .15);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    border: 3px solid rgba(255, 255, 255, .2);
}

/* Variants */
.birthday-flyer {
    background: linear-gradient(135deg, #f8d8ec 0%, #c3def0 100%);
}

.anniversary-flyer {
    background: linear-gradient(135deg, #aad6f1 0%, #247fb8 100%);
}

.double-flyer {
    background: linear-gradient(135deg, #f1eec9ed 0%, #efbb5b 100%);
}

/* Flyer inner content */
.flyer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 25px;
    width: 100%;
    flex: 1;
}


/* Variants just override color */
.birthday-message {
    color: #a91653;
    font-family: "Playwrite GB S", cursive;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, .1);
    line-height: 1.2;
    text-align: center;
    display: block;
    white-space: normal;
    word-wrap: break-word;
}

.anniversary-flex {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    text-align: center;
    margin-bottom: 20px;
}

.anniversary-message,
.anniversary-msg {
    color: #fcfcff;
    font-family: "Playwrite GB S", cursive;
    font-weight: 900;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, .1);
    line-height: 1.2;
    white-space: normal;
    word-wrap: break-word;
}

.highlight-year {
    font-weight: 800;
    font-size: 1.4rem;
    font-family: Playwrite GB S, cursive;
    color: #ffffff;
}

.highlight-text {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #ffebeb;
    font-family: "Playwrite GB S", cursive;
}


/* Double celebration message */
.double-message,
.double-msg {
    color: #2d2d2d;
    font-family: "Playwrite GB S", cursive;
    font-weight: 600;
    margin-bottom: 30px;
    font-size: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, .1);
    line-height: 1.2;
    text-align: center;
    display: block;
    white-space: normal;
    word-wrap: break-word;
}

.double-celebration-message .highlight-year {
    color: #fdf0ea;
    font-weight: 800;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, .1);
    line-height: 1.2;
    text-align: center;
    display: block;
    white-space: normal;
    word-wrap: break-word;
}

/* Profile image */
.profile-img-container {
    flex: 0 0 35%;
    max-width: 160px;
    min-height: 160px;
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fcd5ce 0%, #f8ad9d 100%);
    box-shadow: 0 8px 20px rgba(0, 0, 0, .1);
}

.profile-img-container img {
    width: 100%;
    height: 80%;
    object-fit: cover;
}

/* Text area */
.text-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    text-align: justify;
}
.wb-text{
    font-size: 18px;
    font-family: "Playwrite GB S", cursive;
}

/* Buttons */
.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    border: none;
    border-radius: 25px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 16px;
    color: #fff;
    background: linear-gradient(135deg, #e93422 0%, #b01e1e 100%);
    box-shadow: 0 4px 12px rgba(248, 113, 113, .3);
    transition: transform .2s, box-shadow .2s;
}

.close-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(248, 113, 113, .4);
}

.repeat-btn {
    margin-top: 20px;
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #fff;
    background: linear-gradient(135deg, #5db1d2, #5db1d2);
    box-shadow: 0 4px 12px rgba(200, 181, 216, .3);
    transition: transform .2s, box-shadow .2s;
    font-weight: 600;
}

.repeat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(113, 150, 112, .4);
}

.icon-wrapper {
    position: relative;
    display: inline-block;
    height: 36px;
}

.icon-badge {
    position: absolute;
    right: -7px;
    top: -3px;
    border-radius: 50%;
    font-size: 0.7rem;
    padding: 2px 5px;
    line-height: 1;
    min-width: 1.2rem;
    text-align: center;
    font-weight: 600;
    color: #fff;
}

/* Responsive */
@media (max-width: 600px) {
    .flyer-content {
        flex-direction: column;
        gap: 20px;
    }

    .profile-img-container {
        max-width: 120px;
        min-height: 120px;
    }

    .celebration-flyer {
        padding: 20px;
    }

    .celebration-message {
        font-size: 1.6rem;
    }

    .anniversary-sub {
        font-size: 1.4rem;
    }

    .highlight-year {
        font-size: 1.2rem;
    }

    .highlight-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }
}