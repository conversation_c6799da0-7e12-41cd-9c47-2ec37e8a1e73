.border {
    border: 1px solid #dee2e6;
}

.card-org {
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-width: 220px;
    position: relative;
    z-index: 10;
    background: white;
}

.card-org:hover {
    background-color: #f8f9fa;
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #007bff;
    cursor: pointer;
}

/* Organization chart styling */
.org-structure {
    position: relative;
    padding: 20px 0;
    max-width: 1180px;
}

.position-relative {
    position: relative;
}

.employee-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 15px;
}

/* Adjust spacing between levels */
.supervisor-row {
    margin-top: 50px;
}

/* Hide the long horizontal line and the short down stubs when no subordinates */
.current-employee-row.no-subs::after {
    content: none !important;
}

.current-employee-row.no-subs:has(.current-node)::after {
    content: none !important;
}

/* Also hide the small vertical stub under each current node so nothing dangles */
.current-employee-row.no-subs .current-node::after {
    content: none !important;
}

.current-employee-row {
    margin: 20px 0 10px;
}

.subordinates-row {
    margin-top: 10px;
}

/* Thin lines from supervisors to current employees */
.supervisor-node::after {
    @props ([
                'employeeId' => null,
            ]) content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    width: 1px;
    height: 70px;
    background-color: #6c757d;
    transform: translateX(-50%);
    z-index: 5;
}

/* Connection dots on supervisor bottom */
.supervisor-node::before {
    /* content: ''; */
    position: absolute;
    bottom: 19px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #007bff;
    border-radius: 50%;
    border: 1px solid white;
    z-index: 15;
}

/* Horizontal lines from current employees to subordinates */
.current-node::after {
    content: "";
    position: absolute;
    bottom: -25px;
    left: 50%;
    width: 1px;
    height: 50px;
    background-color: #6c757d;
    transform: translateX(-50%);
    z-index: 5;
}

/* Horizontal connecting line between current employees and subordinates */
.current-employee-row::after {
    /* content: ''; */
    position: absolute;
    bottom: -25px;
    left: 25%;
    right: 25%;
    height: 1px;
    background-color: #6c757d;
    z-index: 5;
}

/* Vertical lines up from subordinates to horizontal line */
.subordinate-node::before {
    /* content: ''; */
    position: absolute;
    top: -25px;
    left: 50%;
    width: 1px;
    height: 25px;
    background-color: #6c757d;
    transform: translateX(-50%);
    z-index: 5;
}

/* Connection dots on subordinates top */
.subordinate-node::after {
    /* content: ''; */
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #007bff;
    border-radius: 50%;
    border: 1px solid white;
    z-index: 15;
}

/* Connection dots on current employee bottom */
.current-node .bottom-dot {
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #007bff;
    border-radius: 50%;
    border: 1px solid white;
    z-index: 15;
}

.current-employee-row.no-subordinates::after {
    display: none;
}

/* For multiple current employees, extend horizontal line properly */
.current-employee-row:has(.current-node:nth-child(2))::after {
    left: 20%;
    right: 20%;
}

.current-employee-row:has(.current-node:nth-child(3))::after {
    left: 15%;
    right: 15%;
}

.current-employee-row:has(.current-node:nth-child(4))::after {
    left: 10%;
    right: 10%;
}

.view-profile-link {
    display: inline-flex;
    /* Keep text & icon in one line */
    align-items: center;
    /* Vertically align them */
    white-space: nowrap;
    /* Prevent text wrapping */
    gap: 4px;
    /* Space between text and icon */
    color: #000000;
    text-decoration: none;
    transition: color 0.3s ease;
}

.view:hover {
    color: rgb(15, 13, 175);
    text-decoration: underline;
    transition-duration: 0.3s ease;
}

.view-profile-link i {
    transition: transform 0.3s ease;
}

.view-profile-link:hover i {
    transform: translateX(5px);
    color: rgb(15, 13, 175);
}

.text-report-line {
    color: #626262;
    font-size: 12px;
    font-weight: 400;
    margin: 0 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .org-structure {
        padding: 10px 0;
    }

    .employee-node {
        padding: 0 8px;
        margin-bottom: 15px;
    }

    .card-org {
        min-width: 180px;
    }

    .supervisor-row {
        margin-bottom: 30px;
    }

    .current-employee-row {
        margin: 30px 0;
    }

    .subordinates-row {
        margin-top: 30px;
    }

    .supervisor-node::after {
        height: 30px;
        bottom: -30px;
    }

    .current-node::after {
        height: 15px;
        bottom: -15px;
    }

    .current-employee-row::after {
        bottom: -15px;
    }

    .subordinate-node::before {
        height: 15px;
        top: -15px;
    }
}

@media (max-width: 576px) {
    .employee-node {
        flex: 1 1 100%;
        max-width: 100%;
    }

    .card-org {
        min-width: 400px !important;
        width: 100% !important;
    }
}

@media (max-width: 450px) {
    .card-org {
        min-width: 360px !important;
        width: 100% !important;
    }
}

@media (max-width: 400px) {
    .card-org {
        min-width: 300px !important;
        width: 100% !important;
    }
}

@media (min-width: 1200px) {
    .org-structure {
        max-width: 1400px;
        /* for big screens */
    }
}

/* Visual hierarchy colors */

.supervisor-row > .col-12,
.current-employee-row > .col-12,
.subordinates-row > .col-12 {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    /* wraps to next line automatically */
}

.card-org-text {
    color: #626262;
    font-weight: 500;
    font-size: 14px;
}

.reporting-to {
    text-align: center;
    position: relative;
    top: -25px;
}

.text-between-lines {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 20px 0 50px;
}

.line {
    flex-grow: 1;
    height: 1px;
    background-color: #000;
}

.text {
    padding: 0 10px;
    white-space: nowrap;
}
