/* Style the Image Used to Trigger the Modal */
.image-popup-picture {
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
    height: 100%;
    max-width: 200px;
}

.image-popup-picture:hover {
    opacity: 0.7;
}

/* The Modal (background) */
.image-popup-modal {
    /* display: none; */
    /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 100;
    /* Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    overflow: auto;
    /* Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /* Fallback color */
    background-color: rgba(0, 0, 0, 0.9);
    /* Black w/ opacity */
}

/* Modal Content (Image) */
.image-popup-modal-content {
    margin: auto;
    display: block;
    width: auto;
    max-width: 90vw;
    max-height: 80vh;
}

/* Caption of Modal Image (Image Text) - Same Width as the Image */
.image-popup-caption {
    margin: auto;
    display: block;
    width: 80%;
    max-width: 700px;
    text-align: center;
    color: #ccc;
    padding: 10px 0;
}

/* Add Animation - Zoom in the Modal */
.image-popup-modal-content,
.image-popup-caption {
    animation-name: zoom;
    animation-duration: 0.6s;
}

@keyframes zoom {
    from {
        transform: scale(0);
    }

    to {
        transform: scale(1);
    }
}

/* The Close Button */
.image-popup-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
}

.image-popup-close:hover,
.image-popup-close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px) {
    .image-popup-modal-content {
        width: 100%;
    }
}

.zoom-in-modal-enter-active,
.zoom-out-modal-leave-active {
    transition: all 0.6s;
}

.zoom-in-modal-enter,
.zoom-out-modal-leave-to {
    opacity: 0;
}

/* This class will apply the zoom animation to the modal content itself,
   mirroring your original CSS logic but for Alpine */
.zoom-in-modal-enter-active .image-popup-modal-content,
.zoom-out-modal-leave-active .image-popup-modal-content {
    animation-name: zoom;
    animation-duration: 0.6s;
}

.zoom-out-modal-leave-to {
    opacity: 0;
}
