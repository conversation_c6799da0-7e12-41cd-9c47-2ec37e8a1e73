@php
    $absentEmployees = [];
    $lateInEmployees = [];
    $earlyOutEmployees = [];

    foreach ($data as $record) {
        $employee = $record['employee_id']; // you can also use 'employee_id'
        $status = $record['status'];

        if ($status == 'Absent') {
            $absentEmployees[$employee] = true;
        }
        if (str_contains($status, 'Late In')) {
            $lateInEmployees[$employee] = true;
        }
        if (str_contains($status, 'Early Out')) {
            $earlyOutEmployees[$employee] = true;
        }
    }

    $absentCount = count($absentEmployees); // distinct Absent employees
    $lateInCount = count($lateInEmployees); // distinct Late In employees
    $earlyOutCount = count($earlyOutEmployees); // distinct Early Out employees

    $hasNoException = $absentCount == 0 && $lateInCount == 0 && $earlyOutCount == 0;
    $counter = 1;
@endphp


@if ($hasNoException)
    <table
        style="width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 11px; text-align: center;">

        <!-- EXCEPTION SUMMARY Heading -->
        <thead>
            <tr style="background-color: #2c3e50; color: white;">
                <th colspan="7"
                    style="padding: 10px; font-weight: bold; text-align: center; background-color: #ecf0f1;">
                    EXCEPTION SUMMARY</th>
            </tr>
            <!-- Gap row -->
            <tr>
                <td colspan="7" style="height: 10px;"></td>
            </tr>

            <tr style="background-color: #2c3e50; color: white;">
                <th colspan="7" style="padding: 10px; text-align: center; background-color: #ecf0f1;">
                    There is no exception record for consecutive attendance</th>
            </tr>
            <!-- Gap row -->
            <tr>
                <td colspan="7" style="height: 10px;"></td>
            </tr>
            <!-- Summary Column Headers -->
            <tr>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">ABSENT</th>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">LATE IN</th>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">EARLY OUT</th>
                <th colspan="4"></th>
            </tr>
        </thead>
        <tbody>
            <!-- Summary Counts -->
            <tr>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ 0 }}</td>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ 0 }}</td>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ 0 }}</td>
                <td colspan="4"></td>
            </tr>
        </tbody>
    </table>
@else
    <table
        style="width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 11px; text-align: center;">

        <!-- EXCEPTION SUMMARY Heading -->
        <thead>
            <tr style="background-color: #2c3e50; color: white;">
                <th colspan="7"
                    style="padding: 10px; font-weight: bold; text-align: center; background-color: #ecf0f1;">
                    EXCEPTION SUMMARY</th>
            </tr>
            <!-- Gap row -->
            <tr>
                <td colspan="7" style="height: 10px;"></td>
            </tr>
            <!-- Summary Column Headers -->
            <tr>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">ABSENT</th>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">LATE IN</th>
                <th style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">EARLY OUT</th>
                <th colspan="4"></th>
            </tr>
        </thead>
        <tbody>
            <!-- Summary Counts -->
            <tr>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ $absentCount }}</td>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ $lateInCount }}</td>
                <td style="border: 1px solid #bdc3c7; padding: 8px; text-align: center;">{{ $earlyOutCount }}</td>
                <td colspan="4"></td>
            </tr>
            <!-- Gap row before detailed report -->
            <tr>
                <td colspan="7" style="height: 15px;"></td>
            </tr>

            <!-- Detailed Attendance Report Heading -->
            <tr>
                <th colspan="7"
                    style="padding: 10px; font-weight: bold; background-color: #ecf0f1; text-align: center;">
                    ATTENDANCE EXCEPTION
                    REPORT</th>
            </tr>
            <tr>
                <td colspan="7" style="height: 15px;"></td>
            </tr>
            <!-- Column Headers for Report Data -->
            <tr style="background-color: #ecf0f1; font-weight: bold;">
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">S.N.</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Employee Code</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Employee Name</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Branch</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Department</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Date</th>
                <th style="border: 1px solid #95a5a6; padding: 8px; font-weight: bold;">Status</th>
            </tr>

            <!-- Report Data Rows -->
            @foreach ($data as $record)
                @php
                    $status = $record['status'];
                    $displayStatus = $status;

                    if ($status == 'Absent') {
                        $bgColor = '#e74c3c';
                    } elseif (str_contains($status, 'Late In')) {
                        $bgColor = '#f39c12';
                    } elseif (str_contains($status, 'Early Out')) {
                        $bgColor = '#3498db';
                    }
                @endphp

                <tr style="color: #080808;">
                    <td style="border: 1px solid #bdc3c7; padding: 6px; text-align: center;">{{ $counter++ }}</td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px;">{{ $record['employee_code'] }}</td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px;">{{ $record['employee_name'] }}</td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px;">{{ $record['branch_name'] }}</td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px;">{{ $record['department_name'] }}</td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px; text-align: center;">{{ $record['date_en'] }}
                    </td>
                    <td style="border: 1px solid #bdc3c7; padding: 6px; text-align: center;">{{ $displayStatus }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endif
