<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">


    <title>{{ $title ?? config('app.name', 'Laravel') }}</title>
    @vite(['resources/scss/app.scss'])
</head>

<body>
    <main>
        <section class="vh-lg-100 mt-5 mt-lg-0 bg-soft d-flex align-items-center">
            <div class="container">
                <div class="row justify-content-center form-bg-image"
                    style="background-image: url('{{ asset('build/img/illustrations/signin.svg') }}'); min-height: 500px;">
                    <div class="col-12 d-flex align-items-center justify-content-center">
                        <div class="bg-white shadow-soft border rounded border-light p-5 p-lg-5 w-100 fmxw-500"
                            style="height: 100%;">
                            <section>
                                <h1>CB-HRM</h1>
                                <div class="mb-4 text-sm text-gray-600">
                                    {{ __('This is a secure area of the application. Please confirm your password before continuing.') }}
                                </div>
                                <form method="POST" action="{{ route('password.confirm') }}">
                                    <!-- Password -->
                                    @csrf
                                    <div>
                                        <x-form.input-label for="password" :value="__('Password')" />

                                        <input id="password"
                                            class="block mt-1 w-full form-control rounded @if ($errors->any()) is-invalid @enderror"
                                            placeholder="Password" type="password" name="password" required
                                            autocomplete="current-password" />
                                            @if ($errors->any())
                                            {!! implode('', $errors->all('<strong class="text-danger">:message</strong>')) !!} @endif

                                        {{-- <x-form.input-error :messages="$errors->get('password')" class="mt-2" /> --}}
                                    </div>

                                    <div class="flex
                                            justify-end mt-4">
                                        <x-form.button color="success" value="Confirm" type="Submit" />
                                    </div>
                                </form>
                            </section>
                        </div>

                    </div>
                </div>
            </div>
        </section>
    </main>
</body>

</html>
