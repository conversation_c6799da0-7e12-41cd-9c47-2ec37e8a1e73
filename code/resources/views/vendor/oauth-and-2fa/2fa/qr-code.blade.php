<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">


    <title>{{ $title ?? config('app.name', 'Laravel') }}</title>
    @vite(['resources/scss/app.scss'])

    <style>
        .two-factor-container {
            display: grid;
            grid-template-columns: 4fr 5fr;
            gap: 50px;
        }

        .two-factor-svg {
            padding: 2px;
        }

        .two-factor-svg svg {
            width: 100%;
        }

        .two-factor-recovery ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .go-back-link {
            color: blue;
            text-decoration: underline;
        }

        .go-back-link:hover {
            color: darkblue;
            text-decoration: underline;
        }

    </style>
</head>

<body>
    <main>
        <section class="vh-lg-100 mt-5 mt-lg-0 bg-soft d-flex align-items-center">
            <div class="container">
                <div class="row justify-content-center form-bg-image"
                    style="background-image: url('{{ asset('build/img/illustrations/signin.svg') }}'); min-height: 500px;">
                    <div class="col-12 d-flex align-items-center justify-content-center">
                        <div class="bg-white shadow-soft border rounded border-light p-4 w-100 fmxw-500"
                            style="height: 100%;">
                            <div>
                                <a class="go-back-link" href="{{ route('dashboard') }}"><< Go Back </a>
                            </div>
                            <section style="padding: 1em;">
                                <h1>CB-HRM</h1>
                                <form method="POST" action="{{ url('/user/two-factor-authentication') }}">
                                    @csrf
                                    @if (auth()->user()->two_factor_secret)
                                        <p>
                                            Use a one-time password authenticator on your mobile device or computer to
                                            enable two-factor authentication (2FA).
                                        </p>
                                        <div class="two-factor-container">
                                            <div class="two-factor-svg">
                                                {!! auth()->user()->twoFactorQrCodeSvg() !!}
                                            </div>
                                            <div class="two-factor-recovery">
                                                <h4>Recovery codes</h4>
                                                <ul>
                                                    @foreach (auth()->user()->recoveryCodes() as $code)
                                                        {{ $code }}<br>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                        @method('DELETE')
                                        <button class="btn btn-danger">Disable</button>
                                    @else
                                        Two factor authentication is not enabled.
                                        <button class="btn btn-primary">Enable</button>
                                    @endif
                                </form>
                            </section>
                        </div>

                    </div>
                </div>
            </div>
        </section>
    </main>
</body>

</html>
