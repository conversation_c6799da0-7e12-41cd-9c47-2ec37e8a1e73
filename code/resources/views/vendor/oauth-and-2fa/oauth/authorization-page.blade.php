<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">


    <title>{{ $title ?? config('app.name', 'Laravel') }}</title>
    @vite(['resources/scss/app.scss'])
</head>

<body>
    <main>
        <section class="vh-lg-100 mt-5 mt-lg-0 bg-soft d-flex align-items-center">
            <div class="container">
                <div class="row justify-content-center form-bg-image"
                    style="background-image: url('{{ asset('build/img/illustrations/signin.svg') }}'); min-height: 500px;">
                    <div class="col-12 d-flex align-items-center justify-content-center">
                        <div class="bg-white shadow-soft border rounded border-light p-4 p-lg-5 w-100 fmxw-500"
                            style="height: 100%;">
                            <section>
                                <h1>YAK HRM</h1>
                                <h3 class="mt-5">Authorization Request</h3>
                                <p style="font-size: 18px">
                                    <b>{{ $client->name }}</b> is requesting permission to access your account.
                                </p>
                                <div>
                                    <h4>This application will able to access your</h4>
                                    <ul>
                                        @foreach($scopes as $scope)
                                            <li>{{ $scope->description }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                                <div class="d-flex gap-2 mt-5">
                                    <form method="post" action="{{ $oldVersion ? route('oauthAllowAuthorize') : route('oauthAllowAuthorizeV2') }}">
                                        @csrf
                                        <input type="hidden" name="state" value="{{ $request->state }}">
                                        <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                                        <input type="hidden" name="auth_token" value="{{ $authToken }}">
                                        <input type="hidden" name="redirect_uri" value="{{ $request->redirect_uri }}">
                                        <x-form.button type="submit" value="Authorize" color="success"></x-form.button>
                                    </form>
                                    <form method="post" action="{{ route('oauthDenyAuthorize') }}">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="state" value="{{ $request->state }}">
                                        <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                                        <input type="hidden" name="auth_token" value="{{ $authToken }}">
                                        <x-form.button value="Cancel" color="gray-400"></x-form.button>
                                    </form>
                                </div>
                            </section>
                        </div>

                    </div>
                </div>
            </div>
        </section>
    </main>
</body>

</html>
