<div class="card-org mb-3" wire:click="selectEmployee({{ $employee['id'] ?? '' }})"
    style="width: 489px; margin: 0 auto; border: 2px solid {{ $employee['is_current'] ? '#0b3fc4' : '#ccc' }};">
    <div class="row g-0">
        <div class="col-md-3 p-2">
            <img src="{{ $employee['picture']
                ? asset('storage/' . $employee['picture'])
                : asset('build/img/team/' . (($employee['gender'] ?? '') === 'female' ? 'female.png' : 'male.png')) }}"
                alt="employee pic" class="rounded-circle mb-2"
                style="width:73px; height:73px; object-fit:cover; display: inline-block; border: 5px solid #F5F5F5;">

            @if ($employee['is_current'])
                <a href="/employee-search/profile/{{ $employee['id'] }}" class="view-profile-link">
                    <span class="view">View Profile</span>
                    <i class="bi bi-arrow-right ms-1"></i>
                </a>
            @endif
        </div>
        <div class="col-md-9 d-flex flex-column p-2">
            <h5 class="card-org-title mb-3 fw-600" style="color: #000000;">
                {{ $employee['employee_name'] }} [{{ $employee['employee_code'] }}]
            </h5>
            <div class="card-org-text mb-lg-0" style="color: #626262;">
                {{ $employee['designation'] ?? 'N/A' }}
            </div>
            <div>
                <span class="card-org-text mb-lg-0" style="color: #626262;">{{ $employee['department'] }}</span>
                @if ($employee['subordinate_count'] > 0)
                    <span class="text-end mt-1 float-end">
                        <i class="bi bi-people-fill" style="font-size: small; color: black;"></i>
                        <span class="fw-bold small">{{ $employee['subordinate_count'] }}</span>
                @endif
                </span>
            </div>
        </div>

    </div>
</div>
