@props([
    'label' => '',
    'value' => '--',
    'delta' => null, // e.g. +5% / -3%
    'icon' => 'bi bi-graph-up',
    'bg' => 'bg-white',
    'type' => 'good', // good, bad
    'popupId' => null,
])

<div class="card border-0 shadow-sm h-100 @if($popupId) popup-card @endif">
    <div class="card-body"
        @if ($popupId) data-bs-toggle="modal" data-bs-target="{{ $popupId }}" @endif>
        <div class="d-flex justify-content-between align-items-start mb-2">
            <span class="text-muted small fw-semibold">{{ $label }}</span>
            <i class="{{ $icon }} text-primary"></i>
        </div>
        <span class="h4 fw-bold mb-0">{{ $value }}</span><br />
        @if (!is_null($delta))
            <span class="badge {{ $type == 'bad' ? 'bg-danger-subtle text-danger' : 'bg-success-subtle text-success' }}">
                {{ $delta }}
            </span>
        @endif
        {{-- <div class="d-flex align-items-baseline gap-2">
        </div> --}}
    </div>
</div>

@pushOnce('style')
<style>
    .popup-card {
        transition: all 300ms ease;
        cursor: pointer;
    }
    .popup-card:hover {
        scale: 1.05;
    }
</style>
@endpushOnce