@props(['detail' => null, 'isPage' => false])
<div>
    <x-tickets.shared.ticket-layout :detail="$detail" :is-page="$isPage" edit-modal-id="#fieldVisitOTRequest"
        :documents="true">

        <!-- Visit Information Section -->
        <div class="bg-white border border-gray-200 rounded-lg p-4 mt-2">
            <div class="d-flex ">
                <i class="bi bi-info me-2"></i>
                <h6 class="text-sm font-semibold text-gray-900 mb-1 pb-1 border-b border-gray-100">Visit Information
                </h6>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-3 text-sm">
                <div class="flex flex-col">
                    <span class="text-gray-500 text-xs mb-1">Requested On:</span>
                    <span
                        class="text-gray-900">{{ $detail->fieldVisit->check_out_date_np . ', ' . $detail->fieldVisit->check_out_time }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-gray-500 text-xs mb-1">Type:</span>
                    <span class="text-gray-900">{{ $detail->fieldVisit->type ?? '-' }}</span>
                </div>
                <div class="flex flex-col sm:col-span-2">
                    <span class="text-gray-500 text-xs mb-1">Purpose:</span>
                    <span class="text-gray-900">{{ $detail->fieldVisit->purpose }}</span>
                </div>
                <div class="flex flex-col sm:col-span-2">
                    <span class="text-gray-500 text-xs mb-1">Description:</span>
                    <span class="text-gray-900">{{ $detail->fieldVisit->description }}</span>
                </div>
                <div class="flex flex-col sm:col-span-2">
                    <span class="text-gray-500 text-xs mb-1">Message:</span>
                    <span class="text-gray-900">{{ $detail->fieldVisit->message ?? '-' }}</span>
                </div>
            </div>
        </div>

        <!-- Check In/Out Details Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
            <!-- Check In Card -->
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="d-flex">
                    <i class="bi bi-clock me-2"></i>
                    <h6 class="text-sm font-semibold text-gray-900 mb-1 pb-1 border-b border-gray-100">Check In</h6>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">Date & Time:</span>
                        <span
                            class="text-gray-900">{{ $detail->fieldVisit->check_in_date_np . ', ' . $detail->fieldVisit->check_in_time ?? '-' }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">Location:</span>
                        <span
                            class="text-gray-900 font-mono text-xs">{{ 'Lat: ' . $detail->fieldVisit->check_in_lat . ', Lng: ' . $detail->fieldVisit->check_in_lon ?? '-' }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">IP Address:</span>
                        <span
                            class="text-gray-900 font-mono text-xs">{{ $detail->fieldVisit->check_in_ip ?? '-' }}</span>
                    </div>
                </div>
            </div>

            <!-- Check Out Card -->
            <div class="bg-white border border-gray-200 rounded-lg p-4 mt-2">
                <div class="d-flex">
                    <i class="bi bi-clock me-2"></i>
                    <h6 class="text-sm font-semibold text-gray-900 mb-1 pb-1 border-b border-gray-100">Check Out
                    </h6>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">Date & Time:</span>
                        <span
                            class="text-gray-900">{{ $detail->fieldVisit->check_out_date_np . ', ' . $detail->fieldVisit->check_out_time ?? '-' }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">Location:</span>
                        <span
                            class="text-gray-900 font-mono text-xs">{{ 'Lat: ' . $detail->fieldVisit->check_out_lat . ', Lng: ' . $detail->fieldVisit->check_out_lon ?? '-' }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-gray-500 text-xs mb-1">IP Address:</span>
                        <span
                            class="text-gray-900 font-mono text-xs">{{ $detail->fieldVisit->check_out_ip ?? '-' }}</span>
                    </div>
                </div>
            </div>
        </div>

    </x-tickets.shared.ticket-layout>
</div>
