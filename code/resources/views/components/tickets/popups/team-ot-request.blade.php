@props([
    'detail' => null,
    'isPage' => false,
])

<div>
    <x-tickets.shared.ticket-layout :detail="$detail" :is-page="$isPage">
        <div class="mt-4">
            <!-- Team Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h5 class="mb-1">{{ $detail->team?->name ?? 'Team' }}</h5>
                    <span class="text-muted small">Team Attendance Details</span>
                </div>
                <div class="d-flex px-3">
                    <span class="badge bg-info px-3 py-2">
                        <i class="fas fa-user-friends me-"></i>
                        {{ count($detail->members_attendance ?? []) }} Members
                    </span>
                </div>
            </div>

            <!-- Card with better spacing -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <!-- Leader Section - Collapsible -->
                    <div class="border border-gray-300 rounded mb-3 mx-3 mt-3">
                        <div class="p-3 bg-gray-100 d-flex justify-content-between align-items-center border-bottom border-gray-300"
                            data-bs-toggle="collapse" href="#leaderDetails" role="button" aria-expanded="false"
                            style="cursor: pointer;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chevron-right me-2 collapse-icon transition-all collapsed"></i>
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-crown me-1"></i>
                                    Leader
                                </span>
                                <h6 class="mb-0">{{ $detail->leader_attendance['name'] ?? 'N/A' }}</h6>
                            </div>
                            <div class="d-flex align-items-center">
                                @php
                                    $status = $detail->leader_attendance['status'] ?? 'Absent';
                                    $statusClass = $status === 'Present' ? 'badge bg-success' : 'badge bg-danger';
                                @endphp
                                <span class="{{ $statusClass }} me-2">
                                    @if ($status === 'Present')
                                        <i class="fas fa-check-circle me-1"></i>
                                    @else
                                        <i class="fas fa-times-circle me-1"></i>
                                    @endif
                                    {{ $status }}
                                </span>
                                <i class="fas fa-chevron-right text-muted collapse-icon transition-all collapsed"></i>
                            </div>
                        </div>

                        <div class="collapse-show" id="leaderDetails">
                            <div class="p-3 bg-white">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-3 col-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-id-badge me-1"></i>
                                            Employee Code
                                        </small>
                                        <span
                                            class="fw-medium">{{ $detail->leader_attendance['employee_code'] ?? '-' }}</span>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-sign-in-alt me-1"></i>
                                            Log In
                                        </small>
                                        <span
                                            class="fw-medium">{{ $detail->leader_attendance['in_time'] ?? '-' }}</span>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-sign-out-alt me-1"></i>
                                            Log Out
                                        </small>
                                        <span
                                            class="fw-medium">{{ $detail->leader_attendance['out_time'] ?? '-' }}</span>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-clock me-1"></i>
                                            Total Time
                                        </small>
                                        <span
                                            class="fw-medium text-primary">{{ $detail->leader_attendance['total_within_range'] ?? '00:00' }}</span>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-business-time me-1"></i>
                                            Total OT
                                        </small>
                                        <span
                                            class="fw-medium text-warning">{{ $detail->leader_attendance['total_ot'] ?? '00:00' }}</span>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            {{ now()->format('M d, Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Members Section - Collapsible -->
                    <div class="mx-3 mb-3">
                        <div class="border border-gray-300 rounded">
                            <div class="p-3 bg-gray-100 d-flex justify-content-between align-items-center border-bottom border-gray-300"
                                data-bs-toggle="collapse" href="#membersSection" role="button" aria-expanded="false"
                                style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-chevron-right me-2 collapse-icon transition-all collapsed"></i>
                                    <h6 class="mb-0">
                                        <i class="fas fa-user-friends me-2"></i>
                                        Team Members ({{ count($detail->members_attendance ?? []) }})
                                    </h6>
                                </div>
                                <div>
                                    <i
                                        class="fas fa-chevron-right text-muted collapse-icon transition-all collapsed"></i>
                                </div>
                            </div>

                            <div class="collapse-show" id="membersSection">
                                <div class="p-3 bg-white">
                                    @if (count($detail->members_attendance ?? []) > 0)
                                        <!-- Scrollable container for multiple members -->
                                        <div class="members-scrollable-container"
                                            style="max-height: 400px; overflow-y: auto;"
                                            data-member-count="{{ count($detail->members_attendance ?? []) }}">
                                            <div class="row g-3">
                                                @foreach ($detail->members_attendance ?? [] as $index => $member)
                                                    <!-- Individual Member - Collapsible -->
                                                    <div class="col-12">
                                                        <div class="border border-gray-300 rounded mb-2">
                                                            <div class="p-3 bg-gray-100 d-flex justify-content-between align-items-center border-bottom border-gray-300"
                                                                data-bs-toggle="collapse"
                                                                href="#member{{ $index }}" role="button"
                                                                aria-expanded="false" style="cursor: pointer;">
                                                                <div class="d-flex align-items-center">
                                                                    <i
                                                                        class="fas fa-chevron-right me-2 collapse-icon transition-all collapsed"></i>
                                                                    <span class="badge bg-info me-2">
                                                                        <i class="fas fa-user me-1"></i>
                                                                        Member {{ $index + 1 }}
                                                                    </span>
                                                                    <h6 class="mb-0">
                                                                        <i class="fas fa-user-circle me-2"></i>
                                                                        {{ $member['name'] ?? 'N/A' }}
                                                                    </h6>
                                                                </div>
                                                                <div class="d-flex align-items-center">
                                                                    @php
                                                                        $status = $member['status'] ?? 'Absent';
                                                                        $statusClass =
                                                                            $status === 'Present'
                                                                                ? 'badge bg-success'
                                                                                : 'badge bg-danger';
                                                                    @endphp
                                                                    <span class="{{ $statusClass }} me-2">
                                                                        @if ($status === 'Present')
                                                                            <i class="fas fa-check-circle me-1"></i>
                                                                        @else
                                                                            <i class="fas fa-times-circle me-1"></i>
                                                                        @endif
                                                                        {{ $status }}
                                                                    </span>
                                                                    <i
                                                                        class="fas fa-chevron-right text-muted collapse-icon transition-all collapsed"></i>
                                                                </div>
                                                            </div>

                                                            <div class="collapse" id="member{{ $index }}">
                                                                <div class="p-3 bg-white">
                                                                    <div class="row g-3 mb-2">
                                                                        <div class="col-md-3 col-6">
                                                                            <small class="text-muted d-block">
                                                                                <i class="fas fa-id-card me-1"></i>
                                                                                Employee Code
                                                                            </small>
                                                                            <span
                                                                                class="fw-medium">{{ $member['employee_code'] ?? '-' }}</span>
                                                                        </div>
                                                                        <div class="col-md-3 col-6">
                                                                            <small class="text-muted d-block">
                                                                                <i class="fas fa-sign-in-alt me-1"></i>
                                                                                Log In
                                                                            </small>
                                                                            <span
                                                                                class="fw-medium">{{ $member['log_in'] ?? '-' }}</span>
                                                                        </div>
                                                                        <div class="col-md-3 col-6">
                                                                            <small class="text-muted d-block">
                                                                                <i
                                                                                    class="fas fa-sign-out-alt me-1"></i>
                                                                                Log Out
                                                                            </small>
                                                                            <span
                                                                                class="fw-medium">{{ $member['log_out'] ?? '-' }}</span>
                                                                        </div>
                                                                        <div class="col-md-3 col-6">
                                                                            <small class="text-muted d-block">
                                                                                <i class="fas fa-clock me-1"></i>
                                                                                Total Time
                                                                            </small>
                                                                            <span
                                                                                class="fw-medium text-primary">{{ $member['total_within_range'] ?? '00:00' }}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row g-3">
                                                                        <div class="col-md-6">
                                                                            <small class="text-muted d-block">
                                                                                <i
                                                                                    class="fas fa-business-time me-1"></i>
                                                                                Total OT
                                                                            </small>
                                                                            <span
                                                                                class="fw-medium text-warning">{{ $member['total_ot'] ?? '00:00' }}</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @else
                                        <div class="text-center py-4 bg-gray-100 rounded">
                                            <i class="fas fa-users text-muted fa-2x mb-3"></i>
                                            <p class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                No team members found.
                                            </p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Remarks Section -->
            <div class="mt-4 p-3 bg-gray-100 rounded border border-gray-300">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-comment text-primary me-2"></i>
                    <h6 class="mb-0">Remarks</h6>
                </div>
                <div class="d-flex align-items-start">
                    <i class="fas fa-quote-left text-muted me-2 mt-1"></i>
                    <p class="mb-0">{{ $detail->remarks ?? 'No remarks provided.' }}</p>
                    <i class="fas fa-quote-right text-muted ms-2 mt-1"></i>
                </div>
            </div>
        </div>
    </x-tickets.shared.ticket-layout>
</div>

<!-- Add CSS for smooth transitions -->
<style>
    .transition-all {
        transition: transform 0.3s ease;
    }

    .collapsed .fa-chevron-down,
    .collapsed .fa-chevron-right {
        transform: rotate(-90deg);
    }

    .members-scrollable-container {
        scrollbar-width: thin;
        scrollbar-color: #dee2e6 #f8f9fa;
        padding-right: 5px;
    }

    .members-scrollable-container::-webkit-scrollbar {
        width: 6px;
    }

    .members-scrollable-container::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 3px;
    }

    .members-scrollable-container::-webkit-scrollbar-thumb {
        background: #dee2e6;
        border-radius: 3px;
    }

    .members-scrollable-container::-webkit-scrollbar-thumb:hover {
        background: #adb5bd;
    }

    /* Grayish background colors */
    .bg-gray-100 {
        background-color: #f8f9fa !important;
    }

    .bg-white {
        background-color: #ffffff !important;
    }

    /* Border colors */
    .border-gray-300 {
        border-color: #dee2e6 !important;
    }

    @media (max-width: 768px) {
        .members-scrollable-container {
            max-height: 300px !important;
        }

        .btn {
            margin-bottom: 5px;
            width: 100%;
        }
    }

    /* Icon styling */
    .badge i {
        font-size: 0.85em;
    }

    /* Hover effects for clickable items */
    [data-bs-toggle="collapse"]:hover {
        background-color: rgba(0, 0, 0, 0.03) !important;
    }

    /* Toggle icon styling */
    [data-bs-toggle="collapse"] .fa-chevron-down,
    [data-bs-toggle="collapse"] .fa-chevron-right {
        color: #6c757d;
        font-size: 0.9em;
    }

    [data-bs-toggle="collapse"]:hover .fa-chevron-down,
    [data-bs-toggle="collapse"]:hover .fa-chevron-right {
        color: #495057;
    }

    .shadow-sm {
        box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important;
    }

    .rounded {
        border-radius: 0.375rem !important;
    }

    /* Improved border styling for cards */
    .border-gray-300 {
        border-color: #dee2e6 !important;
    }
</style>

<!-- Add JavaScript for better functionality -->
@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Rotate chevron icons on collapse
            const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');

            collapseElements.forEach(element => {
                element.addEventListener('click', function() {
                    const icons = this.querySelectorAll('.collapse-icon');
                    if (icons) {
                        icons.forEach(icon => {
                            // Toggle rotation
                            const targetId = this.getAttribute('href');
                            const target = document.querySelector(targetId);
                            if (target.classList.contains('show')) {
                                icon.classList.add('collapsed');
                            } else {
                                icon.classList.remove('collapsed');
                            }
                        });
                    }
                });
            });

            // Auto-scroll adjustment based on member count
            const scrollContainer = document.querySelector('.members-scrollable-container');
            if (scrollContainer) {
                const memberCount = parseInt(scrollContainer.getAttribute('data-member-count') || 0);
                if (memberCount <= 2) {
                    scrollContainer.style.maxHeight = 'none';
                    scrollContainer.style.overflowY = 'visible';
                } else if (memberCount <= 4) {
                    scrollContainer.style.maxHeight = '300px';
                } else {
                    scrollContainer.style.maxHeight = '400px';
                }
            }
        });
    </script>
@endpush
