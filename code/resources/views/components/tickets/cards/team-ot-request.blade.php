@props([
    'request' => collect([]),
])


<div class="bg-white rounded shadow-sm border">
    <div class="d-grid md-grid-cols-3 gap-2 align-items-center border-bottom p-3">
        <div class="d-flex align-items-center gap-2">
            <x-profile-picture :employee="$request->employee" size="md" />
            <div class="d-flex flex-column">
                <span class="fs-6 fw-500">{{ $request->employee?->name }}</span>
            </div>
        </div>
        <div>
            <i class="bi bi-calendar-x me-1"></i> Team OT Request
        </div>
        <div class="fs-6.5">
            <b>Applied On:</b> {{ $request->nep_applied_date }}
        </div>
        <div class="d-flex flex-column">
            <span><b>Duty Start: </b>{{ $request->start_time }}</span>
            <span><b>Duty End: </b>{{ $request->end_time }}</span>
        </div>
    </div>
    <div class="py-2 px-4 fs-6 border-bottom d-grid md-grid-cols-3 gap-2">
        <div><b>Date: </b>{{ $request->nep_date }}</div>
        <div class="d-flex flex-column">
            <span><b>Total Working Hours: </b>{{ $request->total_working_hours }}</span>
        </div>
        {{-- <div class="d-flex flex-column">
            <span><b>Total OT Hours: </b>{{ $request->total_ot_hours }}</span>
        </div> --}}
    </div>
    <div class="px-2 py-2 d-flex justify-content-between align-items-center">
        <div>
            <x-tickets.shared.edit-buttons-group :uuid="$request->uuid" :detail-link="route('ticketPage', ['workflow' => $request->workflow, 'requestId' => $request->id])" />
        </div>
        <div class="d-flex align-items-center gap-2">
            <div>
                <x-tickets.shared.performer-state :performers="$request->performerHistory" />
            </div>
        </div>
    </div>
</div>
