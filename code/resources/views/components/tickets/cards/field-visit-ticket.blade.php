@props([
    'request' => collect([]),
])
<div class="bg-white rounded shadow-sm border">
    <div class="d-grid md-grid-cols-3 gap-2 align-items-center border-bottom p-3">
        <div class="d-flex align-items-center gap-2">
            <x-profile-picture :employee="$request->employee" size="md" />
            <div class="d-flex flex-column">
                <span class="fs-6 fw-500">{{ $request->employee?->name }}</span>
            </div>
        </div>
        <div>
            <i class="bi bi-calendar-x me-1"></i> Field Visit OT
        </div>
        <div class="fs-6.5">
            <b>Applied On:</b> {{ $request->fieldVisit?->check_out_date_np }}
        </div>
    </div>
    <div class="py-2 px-4 fs-6 border-bottom d-grid md-grid-cols-3 gap-2">
        <div class="d-flex flex-column">
        <span><b>Check in Date: </b>{{ $request->fieldVisit?->check_in_date_np }}</span>
        <span><b>Check out Date: </b>{{ $request->fieldVisit?->check_out_date_np }}</span>
        </div>
        <div class="d-flex flex-column">
            <span><b>Check In: </b>{{ \Carbon\Carbon::parse($request->fieldVisit->check_in_time)->format('h:i A') }}</span>
            <span><b>Check Out: </b>{{ \Carbon\Carbon::parse($request->fieldVisit->check_out_time)->format('h:i A') }}</span>
        </div>
        <div class="d-flex flex-column">
            <span><b>Total Working Hours: </b>{{ $request->total_hours }}</span>
        </div>
    </div>
    <div class="px-2 py-2 d-flex justify-content-between align-items-center">
        <div>
            <x-tickets.shared.edit-buttons-group :uuid="$request->uuid" detail-modal-id="#teamRequestDetail" />
        </div>
        <div class="d-flex align-items-center gap-2">
            <div>
                <x-tickets.shared.performer-state :performers="$request->performerHistory" />
            </div>
        </div>
    </div>
</div>
