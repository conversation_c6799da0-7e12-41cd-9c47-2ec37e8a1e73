@props(['src', 'alt' => '', 'height' => '100px', 'meta' => []])

@php
    $extension = strtolower(pathinfo($src, PATHINFO_EXTENSION));

    $iconMap = [
        'pdf' => 'bi-file-pdf-fill',
        'doc' => 'bi-file-word-fill',
        'docx' => 'bi-file-word-fill',
        'xls' => 'bi-file-excel-fill',
        'xlsx' => 'bi-file-excel-fill',
        'ppt' => 'bi-file-earmark-ppt-fill',
        'pptx' => 'bi-file-earmark-ppt-fill',
        'txt' => 'bi-file-text-fill',
        'csv' => 'bi-filetype-csv',
        'zip' => 'bi-file-zip-fill',
    ];

    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    $isVideo = in_array($extension, ['mp4', 'webm', 'ogg']);
    $isFile = !$isImage && !$isVideo;

    $icon = $iconMap[$extension] ?? 'bi-file-earmark-fill';
    $type = $isImage ? 'image' : ($isVideo ? 'video' : 'file');
@endphp

<div x-data="{
    // Media data
    media: {
        src: '{{ $src }}',
        alt: '{{ $alt }}',
        type: '{{ $type }}'
    },
    // Modal state
    isOpen: false,

    // Image transformation state
    zoomLevel: 1.0,
    rotation: 0,

    // Functions for modal management
    openModal() {
        if (this.media.type === 'file') {
            window.open(this.media.src, '_blank');
            return;
        }
        this.isOpen = true;
        this.resetImage(); // Reset transformations on open
    },
    closeModal() {
        this.isOpen = false;
    },

    // Functions for image transformation
    zoomIn() {
        if (this.media.type === 'image') {
            this.zoomLevel = Math.min(3.0, this.zoomLevel + 0.2);
        }
    },
    zoomOut() {
        if (this.media.type === 'image') {
            this.zoomLevel = Math.max(0.5, this.zoomLevel - 0.2);
        }
    },
    rotateImage() {
        if (this.media.type === 'image') {
            this.rotation = (this.rotation + 90) % 360;
        }
    },
    resetImage() {
        this.zoomLevel = 1.0;
        this.rotation = 0;
    },

    // Dynamic style calculation for image
    get imageStyles() {
        return `
            transform: 
                scale(${this.zoomLevel}) 
                rotate(${this.rotation}deg);
            transition: transform 0.15s ease-out;
            cursor: ${this.zoomLevel > 1 ? 'grab' : 'default'};
        `;
    }
}" style="height: {{ $height }}">
    @if (isset($trigger))
        <div x-on:click="openModal">
            {{ $trigger }}
        </div>
    @else
        <template x-if="media.type === 'image'">
            <img :src="media.src" :alt="media.alt" x-on:click="openModal()" title="{{ $alt }}"
                class="image-popup-picture" style="cursor:pointer;">
        </template>

        <template x-if="media.type === 'video'">
            <div class="d-flex align-items-center justify-content-center border rounded p-2 bg-light"
                title="{{ $alt }}" x-on:click="openModal()"
                style="cursor:pointer; height: 100px; width: 100px;">
                <i class="bi bi-camera-video-fill fs-1 "></i>
            </div>
        </template>

        <template x-if="media.type === 'file'">
            <div class="d-flex align-items-center justify-content-center border rounded p-2 bg-light"
                title="{{ $alt }}" x-on:click="openModal()"
                style="cursor:pointer; height: 100px; width: 100px;">
                <i class="bi {{ $icon }} fs-1 "></i>
            </div>
        </template>
    @endif

    <div x-show="isOpen" x-cloak x-transition:enter="zoom-in-modal" x-transition:leave="zoom-out-modal"
        x-on:click.away="closeModal" x-on:keydown.escape.window="closeModal" class="image-popup-modal">

        <span x-on:click="closeModal" class="image-popup-close">&times;</span>

        <div class="d-flex align-items-center justify-content-center h-100 w-100 p-2">
            <div class="text-center">

                <template x-if="media.type === 'image'">
                    <div class="d-inline-block position-relative">
                        <img :src="media.src" :alt="media.alt" class="image-popup-modal-content"
                            :style="imageStyles">

                        {{-- Image Control Buttons (Zoom/Rotate) --}}
                        <div class="position-absolute top-0 end-0 p-3" style="z-index: 101;">
                            <div class="btn-group-vertical shadow" role="group">
                                <button type="button" @click.stop="zoomIn()" class="btn btn-dark" title="Zoom In">
                                    <i class="bi bi-zoom-in"></i>
                                </button>
                                <button type="button" @click.stop="zoomOut()" class="btn btn-dark" title="Zoom Out">
                                    <i class="bi bi-zoom-out"></i>
                                </button>
                                <button type="button" @click.stop="rotateImage()" class="btn btn-dark" title="Rotate">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button type="button" @click.stop="resetImage()" class="btn btn-dark" title="Reset">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </template>

                <template x-if="media.type === 'video'">
                    <video :src="media.src" controls class="image-popup-modal-content"></video>
                </template>

                {{-- Description/Meta Display --}}
                @if ($meta)
                    @foreach ($meta as $key => $value)
                        <div class="image-popup-caption text-start" style="width: auto;">
                            <strong>{{ $key }}:</strong> {{ $value }}
                        </div>
                    @endforeach
                @else
                    <div x-text="media.alt" class="image-popup-caption"></div>
                @endif
            </div>
        </div>
    </div>
</div>
