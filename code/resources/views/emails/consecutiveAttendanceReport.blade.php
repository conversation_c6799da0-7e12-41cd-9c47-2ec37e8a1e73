@extends($layout)

@section('body')
    <tr>
        <td align="center" bgcolor="#f9fafb" style="padding: 40px 30px;">
            <table width="100%" border="0" cellspacing="0" cellpadding="0"
                style="max-width:600px; background:#ffffff; border-radius:12px; box-shadow:0 4px 10px rgba(0,0,0,0.05);">

                @php
                    $hasRecords = collect($data)->contains(fn($r) => $r['status'] != 0);
                @endphp

                @if ($hasRecords)
                    {{-- ✅ CASE 1: There ARE consecutive attendance records --}}
                    <tr>
                        <td align="center"
                            style="background:linear-gradient(90deg, #ec1c24 0%, #ec1c24 100%);
                                   border-top-left-radius:12px;
                                   border-top-right-radius:12px;
                                   padding:20px 10px;">
                            <p
                                style="margin:0; font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;
                                      font-size:22px; font-weight:600; color:#ffffff;">
                                Consecutive Attendance Report
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td
                            style="padding:30px; font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;
                                   color:#333333; font-size:15px; line-height:1.7;">
                            <p><strong>Dear Team,</strong></p>
                            <p>Your <strong>Consecutive Attendance Report</strong> is now ready for download.<br>
                                Please find the attached Excel file which contains the complete attendance data.</p>
                            <p style="color:#555;">Best Regards,<br><strong style="color:#000000;">Vianet Communication
                                    Ltd.</strong></p>
                        </td>
                    </tr>
                @else
                    {{-- 🚫 CASE 2: No records found (status = 0) --}}
                    <tr>
                        <td align="center"
                            style="background:#ec1c24; border-top-left-radius:12px; border-top-right-radius:12px; padding:20px 10px;">
                            <p
                                style="margin:0; font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;
                                      font-size:22px; font-weight:600; color:#ffffff;">
                                No Consecutive Attendance Records
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td
                            style="padding:30px; font-family:'Helvetica Neue', Helvetica, Arial, sans-serif;
                                   color:#333333; font-size:15px; line-height:1.7;">
                            <p><strong>Dear Team,</strong></p>
                            <p>There were <strong>no consecutive attendance exceptions</strong> found during this period.
                            </p>
                            <p style="color:#555;">Best Regards,<br><strong style="color:#000000;">Vianet Communication
                                    Ltd.</strong></p>
                        </td>
                    </tr>
                @endif

                <tr>
                    <td align="center"
                        style="background-color:#f5f7fa; border-bottom-left-radius:12px; border-bottom-right-radius:12px;
                               padding:15px 10px; font-family:Arial, Helvetica, sans-serif; font-size:12px; color:#888888;">
                        <p style="margin:0;">This is an automated message. Please do not reply to this email.</p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
@endsection
