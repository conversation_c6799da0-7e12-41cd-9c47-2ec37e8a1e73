<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{ $title ?? 'Attachment' }}</title>
    <style>
        /* Base Styles */
        body {
            font-family: DejaVu Sans, sans-serif;
            line-height: 1.6;
            font-size: 13px;
            color: #2c3e50;
            margin: 0;
            padding: 20px;
        }

        /* Title Styles */
        .document-title {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
        }

        .document-title h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }

        .document-title .subtitle {
            color: #7f8c8d;
            font-size: 16px;
            margin-top: 5px;
        }

        /* Dynamic Content Container */
        .content-container {
            max-width: 100%;
            margin: 0 auto;
        }

        /* Quill Editor Content Styles */
        .ql-editor {
            font-family: DejaVu Sans, sans-serif;
            line-height: 1.6;
        }

        /* Lists */
        .ql-editor ul,
        .ql-editor ol {
            margin: 15px 0;
            padding-left: 30px;
        }

        .ql-editor li {
            margin: 8px 0;
            padding-left: 5px;
        }

        .ql-editor ul li {
            list-style-type: disc;
        }

        .ql-editor ol li {
            list-style-type: decimal;
        }

        /* Tables */
        .ql-editor table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ql-editor th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #5d6d7e;
        }

        .ql-editor td {
            padding: 10px 15px;
            border: 1px solid #ddd;
            background-color: white;
        }

        .ql-editor tr:nth-child(even) td {
            background-color: #f8f9fa;
        }

        /* Headings */
        .ql-editor h1,
        .ql-editor h2,
        .ql-editor h3,
        .ql-editor h4,
        .ql-editor h5,
        .ql-editor h6 {
            color: #2c3e50;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }

        .ql-editor h1 {
            font-size: 22px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .ql-editor h2 {
            font-size: 20px;
            border-left: 4px solid #3498db;
            padding-left: 12px;
        }

        .ql-editor h3 {
            font-size: 18px;
            color: #3498db;
        }

        .ql-editor h4 {
            font-size: 16px;
        }

        .ql-editor h5 {
            font-size: 14px;
        }

        .ql-editor h6 {
            font-size: 13px;
            font-style: italic;
        }

        /* Text Formatting */
        .ql-editor strong,
        .ql-editor b {
            font-weight: bold;
            color: #2c3e50;
        }

        .ql-editor em,
        .ql-editor i {
            font-style: italic;
            color: #7f8c8d;
        }

        .ql-editor u {
            text-decoration: underline;
            text-decoration-color: #3498db;
        }

        /* Code and Blocks */
        .ql-editor code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }

        .ql-editor pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }

        .ql-editor blockquote {
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
            margin: 20px 0;
            padding: 15px 20px;
            font-style: italic;
            color: #5d6d7e;
        }

        /* Horizontal Rule */
        .ql-editor hr {
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
            margin: 30px 0;
        }

        /* Links */
        .ql-editor a {
            color: #3498db;
            text-decoration: none;
            border-bottom: 1px dotted #3498db;
        }

        .ql-editor a:hover {
            color: #2980b9;
            border-bottom-style: solid;
        }

        /* Images */
        .ql-editor img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin: 15px 0;
        }

        /* Alignment */
        .ql-editor .ql-align-center {
            text-align: center;
        }

        .ql-editor .ql-align-right {
            text-align: right;
        }

        .ql-editor .ql-align-justify {
            text-align: justify;
        }

        /* Colors */
        .ql-editor .ql-color-white {
            color: white;
        }

        .ql-editor .ql-color-red {
            color: #e74c3c;
        }

        .ql-editor .ql-color-green {
            color: #27ae60;
        }

        .ql-editor .ql-color-blue {
            color: #3498db;
        }

        .ql-editor .ql-bg-red {
            background-color: #e74c3c;
            color: white;
            padding: 2px 4px;
        }

        .ql-editor .ql-bg-green {
            background-color: #27ae60;
            color: white;
            padding: 2px 4px;
        }

        .ql-editor .ql-bg-blue {
            background-color: #3498db;
            color: white;
            padding: 2px 4px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            body {
                padding: 15px;
                font-size: 12px;
            }

            .ql-editor table {
                font-size: 11px;
            }

            .ql-editor th,
            .ql-editor td {
                padding: 8px 10px;
            }
        }

        /* Print Styles */
        @media print {
            body {
                padding: 0;
                font-size: 12px;
            }

            .ql-editor table {
                page-break-inside: avoid;
            }

            .ql-editor h1,
            .ql-editor h2,
            .ql-editor h3 {
                page-break-after: avoid;
            }
        }
    </style>
</head>

<body>
    <div class="document-title">
        <h1>{{ $title ?? 'Document' }}</h1>
    </div>

    <div class="content-container">
        <div class="ql-editor">
            @if (isset($content) && !empty($content))
                {!! $content !!}
            @else
                <div style="text-align: center; color: #7f8c8d; padding: 40px;">
                    <p>No content available</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Footer -->
    {{-- <div
        style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #ecf0f1; text-align: center; color: #95a5a6; font-size: 11px;">
        Page <span class="pageNumber"></span> of <span class="totalPages"></span>
    </div>

    <script type="text/php">
        if (isset($pdf)) {
            $text = "Page {PAGE_NUM} of {PAGE_COUNT}";
            $size = 10;
            $font = $fontMetrics->getFont("DejaVu Sans");
            $width = $fontMetrics->get_text_width($text, $font, $size) / 2;
            $x = ($pdf->get_width() - $width) / 2;
            $y = $pdf->get_height() - 35;
            $pdf->page_text($x, $y, $text, $font, $size);
        }
    </script> --}}
</body>

</html>
