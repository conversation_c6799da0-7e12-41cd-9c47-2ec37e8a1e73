<div>
    <x-full-page-loading loading-target='delete' />
    <div class="py-1 d-flex gap-2">
        <x-title icon="hourglass-split">Temporary Access Management</x-title>
        <x-form.button size="sm" icon="plus-circle" icon_color="white" type="button" data-bs-toggle="modal"
            data-bs-target="#temporaryAccessForm" value="Add Temporary Access" />
    </div>
    <section class="card card-body shadow-sm">
        <x-table.table-wrapper :addCard="false" loading-target="delete">
            <x-slot name="settings" placeholder="Search by name" :search="false" :pageLimit="true">
                <x-shared.employee-dropdown font-size="13px" :list="$this->employeeList" wire:model='filter_from_employee_ids'
                    :multiple="true" placeholder="Search From Employee" />
                <x-shared.employee-dropdown font-size="13px" :list="$this->employeeList"
                    wire:model='filter_temporary_employee_ids' :multiple="true"
                    placeholder="Search Temporary Employee" />
            </x-slot>
            <x-slot name="header">
                <x-table.heading>From Employee</x-table.heading>
                <x-table.heading>Temporary Employee</x-table.heading>
                <x-table.heading>From Date</x-table.heading>
                <x-table.heading>To Date</x-table.heading>
                <x-table.heading>Status</x-table.heading>
                <x-table.heading>Approved By</x-table.heading>
                <x-table.heading>Revoked By</x-table.heading>
                <x-table.heading> Action </x-table.heading>
            </x-slot>
            <x-slot name="body">
                @foreach ($this->list as $item)
                    <tr wire:key={{ $item->id }}>
                        <x-table.cell class="">{{ $item->fromEmployee?->name }}</x-table.cell>
                        <x-table.cell class="">{{ $item->temporaryEmployee?->name }}</x-table.cell>
                        <x-table.cell class="">{{ $item->from->format('M d, Y') }}</x-table.cell>
                        <x-table.cell class="">{{ $item->to->format('M d, Y') }}</x-table.cell>
                        <x-table.cell class="">
                            <span
                                class="badge 
                                    @if ($item->status === 'pending') bg-warning text-dark
                                    @elseif($item->status === 'active') bg-success
                                    @elseif($item->status === 'expired') bg-gray-700 text-dark
                                    @else bg-light text-dark @endif
                                ">
                                {{ ucfirst($item->status) }}
                            </span>
                        </x-table.cell>
                        <x-table.cell>
                            <div class="d-flex flex-column text-sm">
                                <span class="fw-semibold"> {{ $item->action_meta['activated']['name'] ?? null}} </span>
                                <small class="text-muted"> {{ $item->action_meta['activated']['at'] ?? null }} </small>
                            </div>
                        </x-table.cell>
                        <x-table.cell>
                            <div class="d-flex flex-column text-sm">
                                <span class="fw-semibold"> {{ $item->action_meta['revoked']['name'] ?? null}} </span>
                                <small class="text-muted"> {{ $item->action_meta['revoked']['at'] ?? null}} </small>
                            </div>
                        </x-table.cell>

                        <x-table.cell>
                            @if ($item->canBeActivate() || $item->isPending() || $item->canBeExpire())
                                <x-table.action>
                                    @if ($item->canBeActivate())
                                        <x-table.action-option label="Activate now" icon="lightning-charge-fill"
                                            color="success" wire:click='activateNow({{ $item->id }})' />
                                    @endif
                                    @if ($item->isPending())
                                        <x-table.action-option type="edit" wire:click='edit({{ $item->id }})'
                                            modal-id="#temporaryAccessForm" />
                                        <x-table.action-option type="delete"
                                            wire:confirm='Are you sure you want to delete this temporary access?'
                                            wire:click='delete({{ $item->id }})' />
                                    @endif
                                    @if ($item->canBeExpire())
                                        <x-table.action-option label="Revoke now" icon="x-circle" color="danger"
                                            wire:click='expireNow({{ $item->id }})' />
                                    @endif
                                </x-table.action>
                            @endif
                        </x-table.cell>
                    </tr>
                @endforeach
            </x-slot>
            <x-slot name="pagination">{{ $this->list->links() }} </x-slot>
        </x-table.table-wrapper>
    </section>

    <form wire:submit.prevent="save" wire:loading.class='d-none' wire:target='edit' :static-backdrop="true">
        <x-modal :title="$editingId ? 'Edit Temporary Access' : 'Add Temporary Access'" id="temporaryAccessForm" size="lg" :static-backdrop="true">
            <x-slot name="body">
                <div wire:loading wire:target='edit'
                    wire:loading.class="d-flex justify-content-center align-items-center">
                    <x-loading size="70px" border-width="6px" />
                </div>
                <div wire:loading.class='d-none' wire:target='edit'>
                    <div class="d-grid md-grid-cols-2 gap-3">
                        <x-shared.employee-dropdown label="From Employee" font-size="13px" :list="$this->employeeList"
                            wire:model='from_employee_id' toggle-on="toggle-from-employee-id" />
                        <x-shared.employee-dropdown label="Temporary Employee" font-size="13px" :list="$this->employeeList"
                            wire:model='temporary_employee_id' toggle-on="toggle-temporary-employee-id" />
                        <x-form.text-input type="date" label="From" :required="true" wire:model="from" />
                        <x-form.text-input type="date" label="To" :required="true" wire:model="to" />
                    </div>
                </div>
            </x-slot>
            <x-slot name="footer">
                <x-form.button color="success" wire:target='save'>Save</x-form.button>
            </x-slot>
        </x-modal>
    </form>

</div>
