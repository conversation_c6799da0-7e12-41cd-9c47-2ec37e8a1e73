<div>
    @if ($showPopUp)
        <div class="popup-overlay" data-celebration="birthday">
            <canvas id="fireworksCanvas"></canvas>
            <canvas id="balloonCanvas"></canvas>
            <canvas id="confettiCanvas"></canvas>


            <div class="celebration-flyer birthday-flyer">
                <button class="close-btn" onclick="closePopup()">✕</button>

                <div class="birthday-flex">
                    <h2 class="birthday-message">
                        Happy Birthday, {{ $userName }}! 🎂🎈
                    </h2>
                </div>

                <div class="flyer-content">
                    <div class="profile-img-container">
                        <img src="{{ auth()->user()->employee->profilePicture() }}"
                            onerror='this.src = "https://ui-avatars.com/api/?name={{ auth()->user()->employee->name }}&size=120&background=random"'
                            alt="{{ auth()->user()->employee->name }}'s profile picture">
                    </div>

                    <div class="text-container">
                        <p>
                            Another year of laughter, love, and light.<br>
                            Dreams that soar to wonderful new heights.<br>
                            May joy dance through your days like morning sun,<br>
                            And every moment sparkle with pure fun!<br><br>

                            May your hard work lead to greater heights.<br>
                            Wishing you success, joy, and good luck<br>
                            in every step of the journey ahead.<br><br>
                        </p>

                        <p>
                            Happy Birthday! May this year be filled with achievements <br>
                            and endless possibilities.
                        </p>
                    </div>

                </div>
                <button class="repeat-btn" onclick="repeatCelebration()">Repeat Celebration 🎊</button>
            </div>
    @endif
</div>
