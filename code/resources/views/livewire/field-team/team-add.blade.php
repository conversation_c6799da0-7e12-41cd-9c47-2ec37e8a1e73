<div>
    <h2 class="h4 mb-0">
        <x-form.icon name="people" size="md" />
        Add Team
    </h2>
    <div class="card card-body shadow">
        <form wire:submit.prevent="save" autocomplete="off">
            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <x-form.text-input name="team_name" label="Team Name" :required="true"></x-form.text-input>
                </div>
                <div class="form-group col-md-6">
                    <label class="input-label m-2" for="employee-dropdown-teamleader">Team Leader</label>
                    <x-form.multi-select id="employee-dropdown-teamleader" placeholder="Select Employee"
                        name="team_leader" toggle-on="toggle-team-leader-id" :multiple="false" />
                </div>
            </div>
            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input name="team_type" label="Team Type" :required="true">
                        @foreach ($this->teamTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input wire:model.live.debounce.300ms="transport_type" label="Transport Type"
                        :required="true">
                        @foreach ($this->transportTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
            </div>
            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input label="Branch" :required="true" wire:model.live.debounce.300ms="branch_id">
                        @foreach ($this->branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input name="operation_center_id" label="Operation Center">
                        @foreach ($this->operationCenter as $centerId => $centerName)
                            <option value="{{ $centerId }}">{{ $centerName }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
            </div>
            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <label class="input-label m-2" for="employee-dropdown-teammember">Team Members</label>

                    <x-form.multi-select id="employee-dropdown-teammember" placeholder="Select Employee"
                        name="team_members" toggle-on="toggle-team-member-id" :multiple="true" />
                </div>
            </div>
            <div class="mt-3 d-flex justify-content-center gap-2">
                <button type="submit" class="btn btn-success text-white">Save</button>
                {{-- <button type="button" class="btn btn-danger text-white">Clear</button> --}}
            </div>
        </form>
    </div>
</div>
