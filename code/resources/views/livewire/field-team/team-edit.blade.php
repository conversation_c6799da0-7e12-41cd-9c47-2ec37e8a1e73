<div>
    <h2 class="h4 mb-2">
        <x-form.icon name="people" size="md" /> Edit Team {{ '(' . $this->team_name . ')' }}
    </h2>

    <div class="card card-body shadow-sm">
        <ul class="nav nav-tabs mb-4" role="tablist">
            <li class="nav-item">
                <a class="nav-link {{ !$isTemporary ? 'active' : '' }}" wire:click="$set('isTemporary', false)"
                    role="tab" style="cursor: pointer;">Regular Team Member</a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ $isTemporary ? 'active' : '' }}" wire:click="$set('isTemporary', true)"
                    role="tab" style="cursor: pointer;">Temporary Team Member</a>
            </li>
        </ul>

        {{-- Loading overlay when switching tabs --}}
        <div wire:loading wire:target="isTemporary" class="position-relative">
            <div class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                style="background: rgba(0, 0, 0, 0.5); z-index: 9999;">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status"
                        style="width: 4rem; height: 4rem; border-width: 0.4rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <form wire:submit.prevent="update" autocomplete="off">
            <div class="row">
                <div class="form-group col-md-6">
                    <x-shared.employee-dropdown label="Team Leader" id="employee-dropdown-teamleaders"
                        placeholder="Select Employee" name="team_leader" toggle-on="set-team-leader" />
                </div>
                <div class="form-group col-md-6">
                    <x-form.list-input name="team_type" label="Team Type" :required="true" :disabled="$isTemporary">
                        @foreach ($this->teamTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
            </div>

            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input wire:model.live.debounce.300ms="transport_type" label="Transport Type"
                        :required="true" :disabled="$isTemporary">
                        @foreach ($this->transportTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input label="Branch" :required="true" wire:model.live.debounce.300ms="branch_id"
                        :disabled="$isTemporary">
                        @foreach ($this->branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
            </div>

            <div class="row">
                <div class="form-group mt-3 col-md-6">
                    <x-form.list-input name="operation_center_id" label="Operation Center" :disabled="$isTemporary">
                        @foreach ($this->operationCenter as $centerId => $centerName)
                            <option value="{{ $centerId }}">{{ $centerName }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>

                {{-- Regular Team Members - Always in DOM, conditionally visible --}}
                <div class="form-group mt-3 col-md-6 {{ $isTemporary ? 'd-none' : '' }}">
                    <x-shared.employee-dropdown label="Team Members" :id="'teamMember-regular'" placeholder="Select Employee"
                        name="regular_members" :toggleOn="'toggle-set-team-member-id-regular'" :multiple="true" />
                </div>

                {{-- Temporary Team Members - Always in DOM, conditionally visible --}}
                <div class="form-group mt-3 col-md-6 {{ !$isTemporary ? 'd-none' : '' }}">
                    <x-shared.employee-dropdown label="Team Members" :id="'teamMember-temp'" placeholder="Select Employee"
                        name="temporary_members" :toggleOn="'toggle-set-team-member-id-temp'" :multiple="true" />
                </div>
            </div>

            <div class="mt-3 d-flex justify-content-center gap-2">
                <button type="submit" class="btn btn-success text-white">Save</button>
            </div>
        </form>
    </div>
</div>
