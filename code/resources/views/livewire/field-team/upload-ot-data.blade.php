<div>
    <div class="mb-3">
        <x-title icon="file-earmark-arrow-up">Upload Ot Data</x-title>
    </div>

    <div class="mb-3">
        <div class="table-responsive">
            <table class="table table-bordered">
                <tr>
                    @foreach (array_keys([...$excelHeaders, ...$optionalExcelHeaders]) as $header)
                        <th class="text-capitalize">
                            {{ $header }}*
                        </th>
                    @endforeach

                </tr>
                <tr>
                    @foreach ([...$excelHeaders, ...$optionalExcelHeaders] as $header => $item)
                        @if ($header === 'date from' || $header === 'date to')
                            <td>mm/dd/yyyy or <br />yyyy-mm-dd</td>
                        @else
                            <td>{{ $item }}</td>
                        @endif
                    @endforeach

                </tr>
            </table>
        </div>
    </div>
    <form wire:submit.prevent='validateExcelData' autocomplete="off">
        <div class="mb-2" style="max-width: 300px">
            <div class="d-flex gap-2 align-items-center">
                <x-form.input-label value="Add Document" />
                <small>(.xls, .xlsx upto 2MB)</small>
            </div>
            <x-form.file-input name='otExcelFile'
                accept=" application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                clear-event="clear-ot-uploads" />
        </div>
        <span wire:loading.class='d-block' wire:target='validateExcelData' class="d-none fst-italic">*Hang
            tight! Please keep this window open and avoid refreshing the page. It might take a moment,
            appreciate your patience!</span>
        <div class="mt-4">
            <x-form.button type="submit" color="success" :disabled="!$excelDataValidated" value="Validate"
                wire:target='validateExcelData' />
        </div>
    </form>

    @if (count($excelData))
        <div class="mt-4 table-responsive" style="max-height: 70vh">
            <table class="table table-bordered ">
                @foreach (array_keys($excelData[0]['data'] ?? []) as $header)
                    <th class="text-capitalize">{{ $header }}</th>
                @endforeach
                @foreach ($excelData as $item)
                    <tr @class(['danger' => $item['error']])>
                        @foreach ($item['data'] as $key => $value)
                            <td>{{ $value }}</td>
                        @endforeach
                    </tr>
                    @if ($item['error'])
                        <tr>
                            <td colspan="{{ count($item['data']) }}" class="px-2 py-1 pb-2">Error of <i
                                    class="bi bi-arrow-up"></i>:
                                {{ $item['error'] }}</td>
                        </tr>
                    @endif
                @endforeach
            </table>
        </div>

        @if ($this->excelDataValidated)
            @if (!$excelUploaded)
                <div class="mt-3">
                    <x-form.button type="button" color="success" wire:click='uploadData' value="Upload Excel Data" />
                </div>
            @endif
            @if ($excelValidationMessage)
                <div class="mt-3">
                    <h3>Log:</h3>
                    {!! $excelValidationMessage !!}
                </div>
            @endif
        @endif
    @endif
</div>
