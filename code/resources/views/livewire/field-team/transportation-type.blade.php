<div>
    <div class="py-1 d-flex gap-2">
        <h2 class="h4 mb-0">
            <x-form.icon name="truck" size="md" />
            Transportation Types
        </h2>
        <x-form.button size="sm" icon="plus-circle" icon_color="white" type="button" data-bs-toggle="modal"
            data-bs-target="#modal" value="Add Transportation Type" :can="PermissionList::ADD_TRANSPORT_TYPES" />
    </div>
    <x-table.table-wrapper>
        <x-slot name="settings" placeholder="Search by transportation type.." :search="true"
            :pageLimit="true"></x-slot>
        <x-slot name="header">
            <x-table.heading :sortable="true" col="name" :sortBy="$sortBy" :sortDirection="$sortDirection"> Name
            </x-table.heading>
            <x-table.heading>Total Number of Seats</x-table.heading>
            <x-table.heading>Status</x-table.heading>
            <x-table.heading> Action </x-table.heading>
        </x-slot>
        <x-slot name="body">
            @foreach ($this->list as $item)
                <tr wire:key={{ $item->id }}>
                    <x-table.cell class="">
                        {{ $item->name }}
                    </x-table.cell>
                    <x-table.cell>{{ $item->total_number_of_seats }}</x-table.cell>
                    <x-table.cell>
                        <span class="badge {{ $item->is_active ? 'bg-success' : 'bg-danger' }}">
                            {{ $item->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </x-table.cell>
                    <x-table.cell>
                        <x-table.action>
                            <x-table.action-option type="edit" wire:click='edit({{ $item->id }})'
                                modal-id="#modal" :can="PermissionList::EDIT_TRANSPORT_TYPES" />
                            <x-table.action-option type="delete" wire:confirm='Are you sure to delete this record?'
                                wire:click='delete({{ $item->id }})' :can="PermissionList::DELETE_TRANSPORT_TYPES" />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @endforeach
        </x-slot>
        <x-slot name="pagination">{{ $this->list->links() }}</x-slot>
    </x-table.table-wrapper>


    @canany([PermissionList::ADD_TRANSPORT_TYPES, PermissionList::EDIT_TRANSPORT_TYPES])
        <!--modal dailog for adding and editing starts from here -->
        <form wire:submit.prevent="save" autocomplete="off">
            <x-modal :title="$isEditing ? 'Edit Transportation Type -> ' . $name : 'Add Transportation Type'" id="modal" size="lg" :static-backdrop="true">
                <x-slot name="body">
                    <div wire:loading wire:loading.class='d-flex justify-content-center align-items-center'
                        wire:target='edit'>
                        <x-loading size="40px" border-width="3px" />
                    </div>
                    <div wire:loading.class='d-none' wire:target='edit'>
                        <div class="row">
                            <div class="form-group mt-3 col-md-5">
                                <x-form.text-input name="name" label="Name" :required="true"></x-form.text-input>
                            </div>
                            <div class="form-group mt-3 col-md-3">
                                <x-form.text-input name="total_number_of_seats" label="Total Number of Seats" :required="true"></x-form.text-input>
                            </div>
                            <div class="form-group mt-3 col-md-2">
                                <x-form.check-input name="is_active" label="Is Active?" :switch="true"
                                    :value="$is_active" />
                            </div>
                        </div>
                    </div>
                </x-slot>
                <x-slot name="footer">
                    <x-form.button color="success">Save</x-form.button>
                </x-slot>
            </x-modal>
        </form>
    @endcanany
</div>
