<div>
    <h2 class="h4 mb-0">
        <x-form.icon name="people" size="md" />
        Add Team Ticket
    </h2>
    <hr>
    <div class="card card-body shadow-sm">
        <div class="row g-2 mb-3">
            <div class="col-md-3">
                <x-form.text-input type="time" name="startTime" label="Minimum Check In Time" placeholder="-- : -- --" />
            </div>
            <div class="col-md-3">
                <x-form.text-input type="time" name="endTime" label="Maximum Check Out Time" placeholder="-- : -- --" />
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <x-form.button color="success" wire:click="confirmTicket" value="Confirm" />
            </div>
        </div>

        {{-- @if ($confirmed)
            <div class="row g-2 mb-3">
                <div class="col-md-3">
                    <x-form.text-input type="time" name="suggestedStartTime" label="Suggested Start (Leader)"
                        wire:model="suggestedStartTime" :readonly="true" :disabled="$suggestedStartTime === null"
                        hint="{{ $suggestedStartTime ? 'Based on leader login' : 'Leader attendance unavailable' }}" />
                </div>
                <div class="col-md-3">
                    <x-form.text-input type="time" name="suggestedEndTime" label="Suggested End (Leader)"
                        wire:model="suggestedEndTime" :readonly="true" :disabled="$suggestedEndTime === null"
                        hint="{{ $suggestedEndTime ? 'Based on leader logout' : 'Leader attendance unavailable' }}" />
                </div>
            </div>
        @endif --}}

        {{-- <hr> --}}
        @if ($confirmed && count($teamAttendance))
            <div class="mt-4">
                @foreach ($teamAttendance as $teamId => $teamData)
                    <div class="card mb-4 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ $teamData['team']['name'] }}</h5>
                            <span class="badge bg-success">{{ count($teamData['members']) }} Members</span>
                        </div>

                        <div class="card-body p-0">
                            <table class="table table-sm table-hover align-middle mb-0">
                                <thead class="text-white bg-gray-300 text-center">
                                    <tr>
                                        <th>Role</th>
                                        <th>Employee</th>
                                        <th>Attendance Status</th>
                                        <th>Log In</th>
                                        <th>Log Out</th>
                                        <th>Total Time (Within Range)</th>
                                        <th>Total OT</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody class="text-center">
                                    {{-- Team Leader --}}
                                    <tr class="fw-bold">
                                        <td class="fw-bold text-success">Leader</td>
                                        <td class="text-center">
                                            {{ $teamData['leader']->employee->name ?? ($teamData['leader']->name ?? '-') }}
                                            [{{ $teamData['leader']->employee_code ?? '-' }}]</td>
                                        <td>{{ $teamData['leader']->status ?? 'Absent' }}</td>
                                        <td>{{ $teamData['leader']->in_time ?? '-' }}</td>
                                        <td>{{ $teamData['leader']->out_time ?? '-' }}</td>
                                        <td>{{ $teamData['leader']->total_within_range ?? '00:00' }}</td>
                                        <td>{{ $teamData['leader']->total_ot ?? '00:00' }}</td>
                                        <td>--</td>
                                    </tr>

                                    {{-- Members --}}
                                    @foreach ($teamData['members'] as $index => $member)
                                        <tr class="fw-bold">
                                            <td class="fw-bold text-info">{{ ucfirst($member->member_type) }}</td>
                                            <td class="text-center">{{ $member->name }}
                                                [{{ $member->employee_code ?? '-' }}]</td>
                                            <td>{{ $member->attendance->status ?? 'Absent' }}</td>
                                            <td>{{ $member->attendance->in_time ?? '-' }}</td>
                                            <td>{{ $member->attendance->out_time ?? '-' }}</td>
                                            <td>{{ $member->attendance->total_within_range ?? '00:00' }}</td>
                                            <td>{{ $member->attendance->total_ot ?? '00:00' }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger"
                                                    wire:click="removeMember({{ $teamId }}, {{ $index }})">
                                                    Remove
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endforeach
            </div>
            <hr>
            <div class="col-md-6 d-flex align-items-end">
                <x-form.text-area label="Remarks" wire:model="remarks" :required="true" />
            </div>
            <div class="col-md-2 mt-2">
                <x-form.list-input label="Verifier" wire:model='verifier_id' :required="true">
                    @foreach ($this->assignVerifiers as $id => $verifier)
                        <option value="{{ $id }}">
                            {{ $verifier['name'] . '[' . $verifier['employee_code'] . ']' }}
                        </option>
                    @endforeach
                </x-form.list-input>
            </div>
            <div class=" mt-3 d-flex align-items-end justify-content-center">
                <x-form.button color="success" wire:click="submitTicket" value="Submit" />
            </div>
        @endif
    </div>
</div>
