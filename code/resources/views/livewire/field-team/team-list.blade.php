<div>
    <div class="py-1 d-flex gap-2">
        <h2 class="h4 mb-0">
            <x-form.icon name="people" size="md" />
            Team List
        </h2>

        @if (auth()->user()->can(PermissionList::ADD_TEAM))
            <a href="{{ route('fieldTeamAdd') }}" class="btn btn-sm btn-gray-800 d-inline-flex align-items-center">
                <x-form.icon name="plus-circle" class="me-2" color="text-white" />
                Add Team
            </a>
        @endif
    </div>
    <hr>

    <div class="card">
        <div class="card-header">
            <div class="row g-3">
                <div class="col-md-3">
                    <x-form.text-input wire:model.live.debounce.300ms="search" name="search"
                        placeholder="Search by team name.." />
                </div>
                <div class="col-md-3">
                    <x-form.list-input wire:model.live.debounce.300ms="team_type" name="team_type" :addEmptyOption="false">
                        <option value="">All Team Types</option>
                        @foreach ($this->teamTypes as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
                <div class="col-md-3">
                    <x-form.list-input wire:model.live.debounce.300ms="branch_id" name="branch_id" :addEmptyOption="false">
                        <option value="">All Branches</option>
                        @foreach ($this->branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
                <div class="col-md-3">
                    <x-form.list-input wire:model.live.debounce.300ms="operation_center" name="operation_center"
                        :addEmptyOption="false">
                        <option value="">All Operation Centers</option>
                        @foreach ($this->operationCenter as $centerId => $centerName)
                            <option value="{{ $centerId }}">{{ $centerName }}</option>
                        @endforeach
                    </x-form.list-input>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row g-3">
            @foreach ($this->list as $item)
                @php
                    $teamLeader = $item->teamMembers->where('is_team_leader', true)->first()?->employee;
                    $members = $item->teamMembers->where('is_team_leader', false)->take(2);
                    $extraCount = $item->teamMembers->where('is_team_leader', false)->count() - $members->count();
                @endphp

                <div class="col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title fw-bold">{{ $item->name }}</h5>

                                @if (auth()->user()->can(PermissionList::DELETE_TEAM))
                                    <button type="button"
                                        @click="if (confirm('Are you sure?')) { $wire.toggleStatus({{ $item->id }}) }"
                                        class="{{ $item->is_active ? 'btn btn-success text-white' : 'btn btn-danger' }}">
                                        {{ $item->is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                @else
                                    <button type="button"
                                        class="{{ $item->is_active ? 'btn btn-success text-white pe-none' : 'btn btn-danger' }}">
                                        {{ $item->is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                @endif
                            </div>

                            <p class="mb-2"><i class="bi bi-person-circle"></i> <strong>Leader:</strong>
                                {{ $teamLeader?->name . ' [' . $teamLeader?->employee_code . ']' }}</p>
                            <p class="mb-2"><i class="bi bi-people"></i> <strong>Type:</strong>
                                {{ $item->teamType->name ?? 'N/A' }}</p>
                            <p class="mb-2"><i class="bi bi-building"></i> <strong>Branch:</strong>
                                {{ $item->branch->name ?? 'N/A' }}</p>
                            <p class="mb-2"><i class="bi bi-geo-alt"></i> <strong>OC:</strong>
                                {{ $item->operationCenter->name ?? 'N/A' }}</p>

                            {{-- Team Members --}}
                            <div x-data="{ expanded: false }" class="mb-2">
                                <strong><i class="bi bi-people"></i> Team Members:</strong><br>
                                @if ($members->count() == 0)
                                    <div>No members found.</div>
                                @else
                                    @foreach ($members as $member)
                                        <div>{{ $member->employee?->name }} [{{ $member->employee?->employee_code }}]
                                        </div>
                                    @endforeach

                                    {{-- Hidden members to expand --}}
                                    <template x-if="expanded">
                                        <div>
                                            @foreach ($item->teamMembers->where('is_team_leader', false)->slice(2) as $member)
                                                <div>{{ $member->employee?->name }}
                                                    [{{ $member->employee?->employee_code }}]
                                                </div>
                                            @endforeach
                                        </div>
                                    </template>

                                    @if ($extraCount > 0)
                                        <a href="#" class="text-primary mt-1 d-block"
                                            @click.prevent="expanded = !expanded">
                                            <span
                                                x-text="expanded ? '👁 View Less' : '👁 View More (+{{ $extraCount }})'"></span>
                                        </a>
                                    @endif
                                @endif
                            </div>
                        </div>

                        {{-- Actions --}}
                        <div class="card-footer bg-light border-top d-flex justify-content-between gap-2">
                            @if (auth()->user()->can(PermissionList::VIEW_TEAM_ATTENDANCE))
                                <button wire:click="view({{ $item->id }})"
                                    class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="bi bi-file-text"></i> View Details
                                </button>
                            @endif

                            @if (auth()->user()->can(PermissionList::EDIT_TEAM))
                                <button wire:click='edit({{ $item->id }})'
                                    class="btn btn-outline-info btn-sm flex-fill">
                                    <i class="bi bi-pencil-square"></i> Edit
                                </button>
                            @endif

                            {{-- <button wire:click='delete({{ $item->id }})'
                                wire:confirm='Are you sure to delete this record?'
                                class="btn btn-outline-danger btn-sm flex-fill">
                                <i class="bi bi-trash"></i> Delete
                            </button> --}}
                        </div>
                    </div>
                </div>
            @endforeach
            @if ($this->list->count() == 0)
                <div class="col-md-12">
                    <div class="card border-0 text-center no-data-card">
                        <div class="d-flex justify-content-center align-items-center mt-3 opacity-60">
                            <p class="fs-3 fw-bold">No Team Found</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <div class="card-footer">
        {{ $this->list->links() }}
    </div>

</div>
