<div>
    <div class="py-1 d-flex gap-2">
        <h2 class="h4 mb-0">
            <x-form.icon name="people" size="md" />
            Team Logs
        </h2>
    </div>

    <div>
        <x-table.table-wrapper class="table" loadingTarget="selectedDate">
            <x-slot name="settings" :search="true" :pageLimit="true">
                <div class="d-flex flex-wrap align-items-end gap-2">
                    <div class="flex-grow-1" style="min-width: 200px;">
                        <x-form.list-input wire:model.live.debounce.300ms="branch_id" name="branch_id"
                            :addEmptyOption="false">
                            <option value="">All Branches</option>
                            @foreach ($this->branches as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </x-form.list-input>
                    </div>

                    <div class="flex-grow-1" style="min-width: 200px;">
                        <x-form.list-input wire:model.live.debounce.300ms="operation_center" name="operation_center"
                            :addEmptyOption="false">
                            <option value="">All Operation Centers</option>
                            @foreach ($this->operationCenter as $oc)
                                <option value="{{ $oc->id }}">{{ $oc->name }}</option>
                            @endforeach
                        </x-form.list-input>
                    </div>

                    <div class="flex-grow-1" style="min-width: 200px;">
                        <x-form.nepali-date-picker-input wire:model.live.debounce.300ms="selectedDate" />
                    </div>

                    <div class="ms-auto">
                        <x-form.button color="info" wire:click="createTicket" value="Create Ticket" :can="PermissionList::ADD_TEAM_TICKET"/>
                    </div>
                </div>
            </x-slot>



            <x-slot name="header">
                <x-table.heading>
                    @if (!$selectAllData)
                        <x-table.header-checkbox :header="false" />
                    @endif
                </x-table.heading>
                <x-table.heading>Team</x-table.heading>
                <x-table.heading>Branch<br>OC</x-table.heading>
                <x-table.heading>Log In<br>Log Out</x-table.heading>
                <x-table.heading>Duty Start<br>Duty End</x-table.heading>
                <x-table.heading>Ticket Status</x-table.heading>
                <x-table.heading>Team Details</x-table.heading>
                <x-table.heading>Action</x-table.heading>
            </x-slot>
            <x-slot name="body">
                @foreach ($this->list as $item)
                    @php
                        $leaderAttendance = $this->teamLeaderAttendances[$item->id] ?? null;
                        $todaysOt = $item->otRequests
                            ->where('nep_date', $this->selectedDate)
                            ->whereIn('state', ['Approved', 'Submitted'])
                            ->first();
                    @endphp
                    <tr wire:key="{{ $item->id }}">
                        @if (!$todaysOt)
                            <x-table.row-checkbox value="{{ $item->id }}" />
                        @else
                            <x-table.cell />
                        @endif
                        <x-table.cell>{{ $item->name }}</x-table.cell>
                        <x-table.cell>{{ $item?->branch?->name }}<br>{{ $item?->operationCenter?->name }}</x-table.cell>
                        <x-table.cell>
                            {{ $leaderAttendance->in_time ?? 'N/A' }}<br>
                            {{ $leaderAttendance->out_time ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            {{ $leaderAttendance->duty_start ?? 'N/A' }}<br>
                            {{ $leaderAttendance->duty_end ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            @if ($todaysOt)
                                <span
                                    class="badge bg-{{ $todaysOt->state === 'Approved' ? 'success' : ($todaysOt->state === 'Rejected' ? 'danger' : 'secondary') }}">
                                    {{ $todaysOt->state }}
                                </span>
                            @else
                                <span class="text-muted">No OT Request</span>
                            @endif
                        </x-table.cell>
                        <x-table.cell>
                            <button type="button" class="dropdown-item rounded-top text-info" data-bs-toggle="modal"
                                data-bs-target="#team-members-modal"
                                wire:click="showAllMembers({{ $item->id }})">View Details
                            </button>
                        </x-table.cell>
                        <x-table.cell>
                            @if ($todaysOt)
                                <x-table.action>
                                    <x-table.action-option label="View Details" icon="eye" :link="route('ticketPage', [
                                        'workflow' => $todaysOt?->workflow,
                                        'requestId' => $todaysOt?->id,
                                    ])" />
                                </x-table.action>
                            @endif
                        </x-table.cell>
                    </tr>
                @endforeach
            </x-slot>
            <x-slot name="pagination">{{ $this->list->links() }}</x-slot>
        </x-table.table-wrapper>
        <x-modal id="team-members-modal" :static-backdrop="true">
            <x-slot name="title">Team: [{{ $teamName }}]</x-slot>
            <x-slot name="body">
                <div class="table-responsive shadow-sm rounded-3 overflow-hidden">
                    <table class="table table-sm table-striped table-hover align-middle mb-0">
                        <thead class="text-white">
                            <tr>
                                <th>Roles</th>
                                <th>Employee Name</th>
                                <th>Attendance Status</th>
                                <th>Duty Start<br>Duty End</th>
                                <th>Log In<br>Log Out</th>
                                <th>Total Attendance Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{-- Team Leader --}}
                            @php
                                $leader = $currentTeamLeaderAttendance;
                            @endphp
                            <tr>
                                <td class="fw-bold">Team Leader</td>
                                <td>
                                    @if ($leader?->employee)
                                        {{ $leader->employee->name }} [{{ $leader->employee->employee_code }}]
                                    @elseif ($leader)
                                        {{ $leader->name }} [{{ $leader->employee_code }}]
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>{{ $leader->status ?? '-' }}</td>
                                <td>
                                    {{ $leader->duty_start ?? '-' }}<br>
                                    {{ $leader->duty_end ?? '-' }}
                                </td>
                                <td>
                                    {{ $leader->in_time ?? '-' }}<br>
                                    {{ $leader->out_time ?? '-' }}
                                </td>
                                <td>{{ $leader->total_hours ?? '-' }}</td>
                            </tr>


                            {{-- Permanent Members --}}
                            @forelse ($this->permanentMembers as $member)
                                @php
                                    $attendance = $member->attendance;
                                @endphp
                                <tr>
                                    <td class="fw-bold text-success">Default</td>
                                    <td>{{ $member?->name . ' [' . $member?->employee_code . ']' }}</td>
                                    <td>{{ $attendance?->status ?? '-' }}</td>
                                    <td>{{ $attendance?->duty_start ?? '-' }}<br>{{ $attendance?->duty_end ?? '-' }}
                                    </td>
                                    <td>{{ $attendance?->in_time ?? '-' }}<br>{{ $attendance?->out_time ?? '-' }}
                                    </td>
                                    <td>{{ $attendance?->total_hours ?? '-' }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No permanent members
                                        found.</td>
                                </tr>
                            @endforelse

                            {{-- Temporary Members --}}
                            @forelse ($this->temporaryMembers as $member)
                                <tr>
                                    <td class="fw-bold text-danger">Temporary</td>
                                    <td>{{ $member?->name . ' [' . $member?->employee_code . ']' }}</td>
                                    <td>{{ $member?->attendance?->status ?? '-' }}</td>
                                    <td>{{ $member?->attendance?->duty_start ?? '-' }}<br>{{ $member?->attendance?->duty_end ?? '-' }}
                                    </td>
                                    <td>{{ $member?->attendance?->in_time ?? '-' }}<br>{{ $member?->attendance?->out_time ?? '-' }}
                                    </td>
                                    <td>{{ $member?->attendance?->total_hours ?? '-' }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No temporary members
                                        found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </x-slot>
        </x-modal>
    </div>
</div>
