<div>
    <h2 class="h4 mb-0">
        <a href="{{ route('fieldTeamList') }}" class="text-decoration-none">
            <x-form.icon name="arrow-left" size="md" />
        </a>
        <x-form.icon name="people" size="md" /> Team Details
    </h2>
    <hr>

    <ul class="nav nav-tabs mb-4" role="tablist">
        <li class="nav-item">
            <a class="nav-link {{ !$isTemporary ? 'active' : '' }}" wire:click="$set('isTemporary', false)"
                role="tab">Regular Team Members</a>
        </li>
        <li class="nav-item">
            <a class="nav-link {{ $isTemporary ? 'active' : '' }}" wire:click="$set('isTemporary', true)"
                role="tab">Temporary Team Members</a>
        </li>
    </ul>
    <div class="row">
        <div class="col-12 mb-4">
            <div class="container-fluid org-structure">
                <div class="col-md-2 ms-auto">
                    <x-form.nepali-date-picker-input wire:model.live.debounce.300ms="selectedDate" />
                </div>
                <!-- Team Leader Section -->
                <div class="row justify-content-center mb-1 current-employee-row">
                    <div class="col-md-6 justify-content-center">
                        <div class="team-leader current-node position-relative mx-2" data-index="0">
                            <div class="card mb-3" style="border: 2px solid #10b981 ;">
                                <div class="row align-items-center g-3 px-3">
                                    <div class="col-md-2 d-flex justify-content-center">
                                        <img src="{{ $teamLeader->employee?->profile_picture ? asset('storage/' . $teamLeader->employee?->profile_picture) : asset('build/img/team/' . ($teamLeader->employee->gender ?? 'other') . '.png') }}"
                                            class="d-block avatar-lg rounded-circle" alt="Profile"
                                            style="width: 80px; height: 80px;" />
                                    </div>

                                    <div class="col-md-10 p-3">
                                        <h5 class="card-title mb-3 fw-800">
                                            {{ $teamLeader->employee?->name . ' [' . $teamLeader->employee?->organizationInfo?->company_employee_code . ']' ?? 'N/A' }}
                                        </h5>
                                        <div class="card-text mb-lg-0" style="color: #626262;">
                                            {{ $teamLeader?->employee?->organizationInfo?->designation?->title ?? 'N/A' }}
                                        </div>
                                        <div>
                                            <span class="card-text mb-lg-0" style="color: #626262;">
                                                {{ $teamLeader?->employee?->department?->name ?? 'N/A' }}
                                            </span>
                                        </div>
                                        <div>
                                            <a href="{{ route('employee-search-profile', $teamLeader->employee) }}"
                                                class="view-profile-link">
                                                <span class="view">View Profile</span>
                                                <i class="bi bi-arrow-right ms-1"></i>
                                            </a>
                                            <span class="text-end mt-1 float-end">
                                                <i class="bi bi-people-fill"
                                                    style="font-size: small; color: black;"></i>
                                                <span class="fw-bold small">{{ $teamMembers->count() }}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Fiber Team Member Section -->
                <div class="row justify-content-center mb-1 team-member-row">
                    <div class="text-between-lines">
                        <span class="line"></span>
                        <span class="text text-report-line">Team members reporting to
                            {{ $teamLeader->employee?->name ?? 'N/A' }}</span>
                        <span class="line"></span>
                    </div>
                    <div class="col-12">
                        <div class="row">
                            @forelse ($teamMembers as $member)
                                <div class="col-md-4 team-member" data-index="0">
                                    <div class="card mb-3">
                                        <div class="row align-items-center g-3 px-3">
                                            <div class="col-md-2 d-flex justify-content-center">
                                                <img src="{{ $teamLeader->employee?->profile_picture ? asset('storage/' . $teamLeader->employee?->profile_picture) : asset('build/img/team/' . ($teamLeader->employee->gender ?? 'other') . '.png') }}"
                                                    class="d-block avatar-lg rounded-circle" alt="Profile"
                                                    style="width: 80px; height: auto;" />
                                            </div>
                                            <div class="col-md-10 p-3">
                                                <h5 class="card-title mb-3 fw-600">
                                                    {{ $member?->name . ' [' . $member?->organizationInfo?->company_employee_code . ']' ?? 'N/A' }}
                                                </h5>
                                                <div class="card-text mb-lg-0" style="color: #626262;">
                                                    {{ $member?->designation?->title ?? 'N/A' }}
                                                </div>
                                                <div>
                                                    <span class="card-text mb-lg-0"
                                                        style="color: #626262;">{{ $member->department?->name ?? 'N/A' }}</span>
                                                </div>
                                                <div>
                                                    <a href="{{ route('employee-search-profile', $member) }}"
                                                        class="view-profile-link">
                                                        <span class="view">View Profile</span>
                                                        <i class="bi bi-arrow-right ms-1"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12">
                                    <div class="alert alert-secondary text-center" role="alert">
                                        No members found for this team.
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .border {
            border: 1px solid #dee2e6;
        }

        .card {
            transition: all 0.3s ease;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-width: 220px;
            position: relative;
            z-index: 10;
        }

        .card:hover {
            background-color: #f8f9fa;
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #10b981;
            cursor: pointer;
        }

        /* Organization chart styling */
        .org-structure {
            position: relative;
            padding: 20px 0;
            max-width: 1180px;
        }

        .current-employee-row {
            margin: 20px 0 10px;
        }

        .col-md-4.team-member.temporary-member {
            opacity: 0.5;
        }

        /* Horizontal lines from current employees to subordinates */
        .current-node::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 50%;
            width: 1px;
            height: 50px;
            background-color: #6c757d;
            transform: translateX(-50%);
            z-index: 5;
        }

        .view-profile-link {
            display: inline-flex;
            /* Keep text & icon in one line */
            align-items: center;
            /* Vertically align them */
            white-space: nowrap;
            /* Prevent text wrapping */
            gap: 4px;
            /* Space between text and icon */
            color: #666666;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 12px;
        }

        .view-profile-link:hover {
            color: #10b981;
            text-decoration: underline;
            transition-duration: 0.3s ease;
        }

        .view-profile-link:hover i {
            transition: transform 0.3s ease;
            transform: translateX(5px);
        }

        .text-report-line {
            color: #626262;
            font-size: 12px;
            font-weight: 400;
            margin: 0 10px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .org-structure {
                padding: 10px 0;
            }

            .card {
                min-width: 180px;
            }

            .supervisor-row {
                margin-bottom: 30px;
            }

            .team-member-row {
                margin-top: 15px;
            }

            .current-node::after {
                height: 40px;
                bottom: -40px;
            }

            .current-employee-row::after {
                bottom: -15px;
            }

            .subordinate-node::before {
                height: 15px;
                top: -15px;
            }
        }

        /* Visual hierarchy colors */
        .supervisor-row h5,
        .current-employee-row h5,
        .team-member-row h5 {
            color: #000000;
            font-weight: 600;
            font-size: 16px;
        }

        .card-text {
            color: #626262;
            font-weight: 500;
            font-size: 14px;
        }

        .reporting-to {
            text-align: center;
            position: relative;
            top: -25px;
        }

        .text-between-lines {
            display: flex;
            align-items: center;
            width: 100%;
            margin: 10px 0 50px;
        }

        .line {
            flex-grow: 1;
            height: 1px;
            background-color: #000;
        }

        .text {
            padding: 0 10px;
            white-space: nowrap;
        }
    </style>
</div>
