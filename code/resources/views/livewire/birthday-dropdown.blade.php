<div>
    <li class="nav-item dropdown ms-3">
        <a class="nav-link dropdown-toggle pt-1 px-0 position-relative" href="#" role="button"
            data-bs-toggle="dropdown" aria-expanded="false" aria-label="Birthdays">
            <div class="icon-wrapper">
                <i class="bi bi-cake-fill birthday-icon"></i>

                @if ($birthdayCount > 0)
                    <span class="position-absolute top-90 right-100 badge rounded-pill bg-danger icon-badge">
                        {{ $birthdayCount > 9 ? '9+' : $birthdayCount }}
                    </span>
                @endif
            </div>
        </a>

        <div class="dropdown-menu dropdown-menu-end mt-2 py-1" style="min-width: 320px; transition: none;">

            <div class="birthday-scroll">

                {{-- TODAY --}}
                @php
                    $todayLabel = \Carbon\Carbon::today()->format('M d, Y');
                @endphp

                <h6 class="dropdown-header fw-bold">
                    🎉 Birthdays Today <span class="text-muted">({{ $todayLabel }})</span>
                </h6>

                @forelse($birthdaysToday as $department => $employees)
                    <div class="dropdown-item">
                        <strong class="d-block mb-1">{{ $department }}</strong>
                        <ul class="list-unstyled ms-2 mb-0">
                            @foreach ($employees as $emp)
                                <li class="text-blue">
                                    🎂
                                    <a href="{{ route('employee-search-profile', $emp['id']) }}"
                                        class="text-decoration-none text-blue">
                                        {{ trim($emp['first_name'] . ' ' . ($emp['middle_name'] ?? '') . ' ' . $emp['last_name']) }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @empty
                    <span class="dropdown-item text-muted">No birthdays today 🎂</span>
                @endforelse

                <div class="dropdown-divider"></div>

                {{-- UPCOMING --}}
                @php
                    $upcomingCount = collect($upcomingBirthdays)->flatten(1)->count();
                    $formatLabel = fn($dateStr) => \Carbon\Carbon::parse($dateStr)->format('M j, Y');
                @endphp

                <h6 class="dropdown-header fw-bold">
                    📅 Upcoming (next {{ $upcomingWindowDays }} days)
                    @if ($upcomingCount > 0)
                        <span class="badge bg-secondary ms-1">{{ $upcomingCount }}</span>
                    @endif
                </h6>

                @forelse($upcomingBirthdays as $date => $people)
                    <div class="dropdown-item">
                        <strong>
                            {{ $formatLabel($date) }}
                            <small class="text-red">
                                @php
                                    $diff = \Carbon\Carbon::today()->diffInDays(\Carbon\Carbon::parse($date));
                                @endphp
                                — in {{ $diff }} {{ \Illuminate\Support\Str::plural('day', $diff) }} </small>
                        </strong>
                        <ul class="list-unstyled ms-2 mb-0">
                            @foreach ($people as $p)
                                <li class="text-blue">
                                    <a href="{{ route('employee-search-profile', $p['id']) }}"
                                        class="text-decoration-none text-blue">
                                        {{ $p['first_name'] }} {{ $p['middle_name'] }} {{ $p['last_name'] }}
                                    </a>
                                    <small class="text-muted">({{ $p['department'] }})</small>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @empty
                    <span class="dropdown-item text-muted">No upcoming birthdays 🎈</span>
                @endforelse

            </div>
        </div>
    </li>


    <style>
        .birthday-icon {
            font-size: 1.5rem;
            color: #010e24;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, .15));
            transition: transform .15s ease, filter .15s ease;
        }

        .nav-link:hover .birthday-icon {
            transform: translateY(-1px);
            filter: drop-shadow(0 2px 2px rgba(0, 0, 0, .18));
        }

        .navbar-nav .nav-link .birthday-icon {
            font-size: 22px;
            margin-right: .3rem;
            color: #010e24;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, .15));
            transition: transform .15s ease, filter .15s ease;
        }

        /* Scroll container */
        .birthday-scroll {
            max-height: 500px;
            overflow-y: auto;
        }

        .birthday-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .birthday-scroll::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, .2);
            border-radius: 1px;
        }
    </style>
</div>
