<div>

    @php
        $enumAgeGroups = new ReflectionClass(\App\Http\Helpers\Enums\AgeGroups::class);
        $ageGroups = array_values($enumAgeGroups->getConstants() ?? []) ?? [];
        $ageGroups = ['45-49', '50-54', '55-59', '60 and above'];
        $genders = ['male', 'female'];

        $totals = [];
        foreach ($ageGroups as $ageGroup) {
            $totals[$ageGroup] = ($genderAge['male'][$ageGroup] ?? 0) + ($genderAge['female'][$ageGroup] ?? 0);
        }
    @endphp

    <div class="age-group-container">
        <h6 class="fw-bold text-primary mb-3">Age Group</h6>

        <div class="responsive-table-container">
            <table class="table text-center border align-middle table-hover responsive-table age-group-table">
                <thead class="thead-light">
                    <tr>
                        <th class="text-start fw-semibold">Gender</th>
                        @foreach ($ageGroups as $ageGroup)
                            <th class="fw-semibold">{{ $ageGroup }}</th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    @foreach ($genders as $gender)
                        <tr>
                            <td class="text-start text-capitalize fw-bold text-primary">{{ $gender }}</td>
                            @foreach ($ageGroups as $ageGroup)
                                @php $count = $genderAge[$gender][$ageGroup] ?? 0; @endphp
                                <td class="fw-semibold text-primary fs-6">
                                    @if ($count)
                                        <a href="{{ route('employeelist', array_merge(request()->query(), ['gender' => $gender, 'ageGroup' => $ageGroup])) }}"
                                            class="text-decoration-none text-primary">
                                            {{ $count }}
                                        </a>
                                    @else
                                        {{ $count }}
                                    @endif
                                </td>
                            @endforeach
                        </tr>
                    @endforeach
                    <tr style="background-color: #f0f4f8;" class="fw-bold">
                        <td class="text-start text-primary">Total</td>
                        @foreach ($ageGroups as $ageGroup)
                            <td class="fw-semibold text-primary fs-6">
                                @if ($totals[$ageGroup])
                                    <a href="{{ route('employeelist', array_merge(request()->query(), ['ageGroup' => $ageGroup])) }}"
                                        class="text-decoration-none text-primary">
                                        {{ $totals[$ageGroup] }}
                                    </a>
                                @else
                                    {{ $totals[$ageGroup] }}
                                @endif
                            </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
