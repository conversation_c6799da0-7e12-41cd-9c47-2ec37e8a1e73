<div>
    <div class="responsive-table-container table-responsive">
        <table class="table text-center align-middle table-hover attendance-table"
            style="border: none; background: white;">
            <thead class="thead-green" style="background-color: #00b894; color: white">
                <tr>
                    <th class="text-start fw-semibold text-white fs-6" style="border: none;">Types</th>
                    @foreach ($statuses as $status)
                        <th class="fw-semibold text-white fs-6" style="border: none;">{{ $status }}</th>
                    @endforeach
                    <th class="fw-semibold text-white fs-6" style="border: none;">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($employeeTypes as $type)
                    <tr style="background-color: white; color: #333;">
                        <td class="text-start fw-semibold text-white fs-5"
                            style="border: none; background-color: #00b894;">
                            {{ $type }}
                        </td>
                        @foreach ($statuses as $status)
                            @php
                                $count = $attendanceData[$type][$status] ?? 0;
                                $link = $attendanceDataLinks[$type][$status] ?? null;
                            @endphp
                            <td class="fw-semibold fs-5" style="border: none; background-color: white;">
                                @if ($count && $link)
                                    <a href="{{ $link }}" class="text-decoration-none text-dark">
                                        {{ $count }}
                                    </a>
                                @else
                                    {{ $count }}
                                @endif
                            </td>
                        @endforeach
                        {{-- Row Total Column --}}
                        <td class="fw-semibold fs-5" style="border: none; background-color: #f8f9fa;">
                            @if (($rowTotals[$type] ?? 0) && ($rowTotalLinks[$type] ?? null))
                                <a href="{{ $rowTotalLinks[$type] }}" class="text-decoration-none text-dark">
                                    {{ $rowTotals[$type] }}
                                </a>
                            @else
                                {{ $rowTotals[$type] ?? 0 }}
                            @endif
                        </td>
                    </tr>
                @endforeach

                {{-- Totals Row --}}
                <tr style="background-color: #f8f9fa;" class="fw-bold">
                    <td class="text-start text-dark fw-semibold fs-5" style="border: none;">Total</td>
                    @foreach ($statuses as $status)
                        <td class="fw-bold text-dark fw-semibold fs-5" style="border: none;">
                            @if (($columnTotals[$status] ?? 0) && ($columnTotalLinks[$status] ?? null))
                                <a href="{{ $columnTotalLinks[$status] }}" class="text-decoration-none text-dark">
                                    {{ $columnTotals[$status] }}
                                </a>
                            @else
                                {{ $columnTotals[$status] ?? 0 }}
                            @endif
                        </td>
                    @endforeach

                    {{-- Grand Total Cell --}}
                    <td class="fw-bold text-dark fw-semibold fs-5" style="border: none;">
                        @if ($grandTotal && ($grandTotalLink ?? null))
                            <a href="{{ $grandTotalLink }}" class="text-decoration-none text-dark">
                                {{ $grandTotal }}
                            </a>
                        @else
                            {{ $grandTotal }}
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <style>
        .attendance-table {
            border: none !important;
        }

        .attendance-table th,
        .attendance-table td {
            border: none !important;
            padding: 12px 8px;
        }

        .responsive-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</div>
