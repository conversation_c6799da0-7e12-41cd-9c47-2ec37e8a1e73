<div x-data="{ showFilters: false }">
    <div>
        <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
            <div class="d-flex align-items-center gap-2">
                <h2 class="h5 mb-0 d-flex align-items-center gap-2">
                    <x-form.icon name="bar-chart-line-fill" size="md" />
                    HR Analytics
                </h2>

                <span class="px-2 py-1 rounded border small" data-bs-toggle="tooltip"
                    title="Dashboard data is cached per filter combination for 1 hour." wire:ignore.self>
                    <i class="bi bi-info-circle me-1"></i> Cached 12h / per filters
                </span>
            </div>

            <div class="d-flex align-items-center gap-2 pe-1">
                <button type="button" class="btn btn-outline-secondary btn-sm" x-on:click="showFilters = !showFilters">
                    <i class="bi" :class="showFilters ? 'bi-sliders2-vertical' : 'bi-sliders2'"></i>
                    Filters
                </button>

                <x-form.button color="danger" outline wire:click="clearCache" size="sm" loading-size="10px">
                    <i class="bi bi-trash3 me-1"></i> Refresh Data
                </x-form.button>
            </div>
        </div>
    </div>

    <div class="card border-0 shadow-sm mt-3" x-show="showFilters" x-transition>
        <div class="card-body">
            <div class="row g-3 align-items-end">
                @if (auth()->user()->can(PermissionList::HR_ANALYTICS_VIEW_ALL))
                    <x-form.list-input div-class="col-12 col-md-3" label="Company"
                        wire:model.live.debounce.300ms="filterCompanyId" name="company"
                        empty-option-placeholder="All Companies" :options="$this->companyList" />
                @endif
                <x-form.list-input div-class="col-12 col-md-3" label="Branch"
                    wire:model.live.debounce.300ms="filterBranchId" name="branch"
                    empty-option-placeholder="All Branches" :options="$this->branchList" />

                <x-form.list-input div-class="col-12 col-md-3" label="Department"
                    wire:model.live.debounce.300ms="filterDepartmentId" name="department"
                    empty-option-placeholder="All Departments" :options="$this->departmentList" />
            </div>
        </div>
    </div>

    <livewire:dashboards.hr-analytical.kpi-row :companyId="$filterCompanyId" />
    <livewire:dashboards.hr-analytical.active-workforce-trend :companyId="$filterCompanyId" />
    <livewire:dashboards.hr-analytical.hiring-trend :companyId="$filterCompanyId" />
    <livewire:dashboards.hr-analytical.termination-trend :companyId="$filterCompanyId" />
    <livewire:dashboards.hr-analytical.attendance-compliance-trend :companyId="$filterCompanyId" />
    <livewire:dashboards.hr-analytical.turn-over-trend :companyId="$filterCompanyId" />
</div>
