<div>
    <x-modal id="terminationTrendModal" size="lg" :has-footer="false" max-width="950px">
        <x-slot:title>
            <i class="bi bi-person-dash-fill me-2"></i>
            Weekly Termination Trend (Prev vs This Week)
        </x-slot:title>
        <x-slot:body>
            <div x-data="terminationTrendBar({ initial: @js($chartPayload) })" x-init="$nextTick(() => init())"
                x-on:chart-termination-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="(payload.series?.[0]?.data?.filter(v=>v!==null).length ?? 0) + (payload.series?.[1]?.data?.filter(v=>v!==null).length ?? 0) === 0"
                    class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
            <div class="small text-muted mt-2">
                Comparing Monday–Sunday. Future days this week are hidden.
            </div>
        </x-slot:body>
    </x-modal>
</div>


@script
    <script>
        Alpine.data('terminationTrendBar', ({
            initial,
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [],
                meta: {}
            },

            options() {
                return {
                    chart: {
                        type: 'bar',
                        height: 300,
                        toolbar: {
                            show: false
                        },
                    },
                    series: this.payload.series, // [{name, data[7], metaDates[7]}, ...]
                    xaxis: {
                        categories: this.payload.categories, // ['Mon','Tue',...]
                        labels: {
                            rotate: 0
                        }
                    },
                    yaxis: {
                        title: {
                            text: 'Terminated Employees Count'
                        },
                        forceNiceScale: true
                    },
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: '45%',
                            borderRadius: 4,
                            borderRadiusApplication: 'end',
                            dataLabels: {
                                position: 'top'
                            }
                        },
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        show: true,
                        width: 1,
                        colors: ['transparent']
                    },
                    colors: ['#94a3b8', '#3b82f6'], // prev, current
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right'
                    },
                    grid: {
                        strokeDashArray: 4
                    },
                    tooltip: {
                        shared: false,
                        y: {
                            formatter: v => (v == null ? '' : Number(v).toLocaleString())
                        },
                        //     custom: ({
                        //         series,
                        //         seriesIndex,
                        //         dataPointIndex,
                        //         w
                        //     }) => {
                        //         const s = w.config.series[seriesIndex];
                        //         const weekday = w.config.xaxis.categories[dataPointIndex];
                        //         const iso = (s.metaDates || [])[dataPointIndex] || '';
                        //         const val = series[seriesIndex][dataPointIndex];
                        //         return `
                    //     <div class="px-2 py-1">
                    //         <div class="small text-muted">${weekday} • ${iso}</div>
                    //         <div class="fw-semibold">${s.name}: ${val ?? 0}</div>
                    //     </div>
                    // `;
                        //     }
                    }
                };
            },

            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },

            update(e) {
                console.log(e);
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;
                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    },
                }, false, true);
                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
