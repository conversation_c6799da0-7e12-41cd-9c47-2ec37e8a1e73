<div>
    <x-modal id="absentTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-person-x-fill me-2"></i>
            Absent Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'absent',
                title: 'Absent',
                color: '#ef4444'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
            <div class="small text-muted mt-2">
                Showing last 14 days (yesterday inclusive). Divider indicates the split between week 1 and week 2.
            </div>
        </x-slot:body>
    </x-modal>

    <x-modal id="lateInTrendModal" size="lg" :has-footer="false" max-width="1200px">

        <x-slot:title>
            <i class="bi bi-alarm-fill me-2"></i>
            Late In Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'lateIn',
                title: 'Late In',
                color: '#f59e0b'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
        </x-slot:body>
    </x-modal>


    <x-modal id="earlyOutTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-box-arrow-right me-2"></i>
            Early Out Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'earlyOut',
                title: 'Early Out',
                color: '#3b82f6'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
        </x-slot:body>
    </x-modal>

    <x-modal id="leaveTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-umbrella me-2"></i>
            Leave Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'leave',
                title: 'Leave',
                color: '#10b981'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
        </x-slot:body>
    </x-modal>

    <x-modal id="presentTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-person-check me-2"></i>
            Present Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'present',
                title: 'Present',
                color: '#16a34a'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
        </x-slot:body>
    </x-modal>

    <x-modal id="punctualTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-stopwatch me-2"></i>
            Punctual Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="attComplianceMetric({
                initial: @js($chartPayload),
                metric: 'punctual',
                title: 'Punctual',
                color: '#14b8a6'
            })" x-init="$nextTick(() => init())"
                x-on:chart-attendance-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="isEmpty" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
        </x-slot:body>
    </x-modal>

</div>

@script
    <script>
        Alpine.data('attComplianceMetric', ({
            initial,
            metric,
            title,
            color
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                dividerIndex: 7,
                metrics: {}
            },
            metric,
            title,
            color,
            get isEmpty() {
                const data = this.payload?.metrics?.[this.metric] || [];
                return !data.length || data.every(v => (v ?? 0) === 0);
            },
            options() {
                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                return {
                    chart: {
                        type: 'area',
                        height: 400,
                        toolbar: {
                            show: false
                        },
                        zoom: {
                            enabled: false
                        }
                    },
                    series: [{
                        name: this.title,
                        data: this.payload?.metrics?.[this.metric] ?? []
                    }],
                    xaxis: {
                        categories: this.payload.categories,
                        labels: {
                            rotate: -30
                        }
                    },
                    yaxis: {
                        min: 0,
                        forceNiceScale: true,
                        title: {
                            text: 'Employees'
                        },
                        labels: {
                            formatter: val => Number(val).toLocaleString()
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        width: 3,
                        curve: 'smooth'
                    },
                    markers: {
                        size: 3,
                        style: 'hollow'
                    },
                    colors: [this.color],
                    legend: {
                        show: false
                    },
                    grid: {
                        strokeDashArray: 4,
                        borderColor: '#e2e8f0'
                    },
                    tooltip: {
                        y: {
                            formatter: v => Number(v ?? 0).toLocaleString()
                        }
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0,
                            stops: [0, 100]
                        }
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }]
                    } : {}
                };
            },
            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },
            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;

                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }]
                    } : {}
                }, false, true);

                this.chart.updateSeries([{
                    name: this.title,
                    data: this.payload?.metrics?.[this.metric] ?? []
                }], true);
            }
        }));
    </script>
@endscript
