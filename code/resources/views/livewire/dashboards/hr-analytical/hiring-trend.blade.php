<div>
    <x-modal id="hiringTrendModal" size="lg" :has-footer="false" max-width="950px">
        <x-slot:title>
            <i class="bi bi-person-plus-fill me-2"></i>
            Hiring Trend (Prev vs This Week)
        </x-slot:title>
        <x-slot:body>
            {{-- <x-chart-card title="Hiring Trend (Prev vs This Week)" icon="bi bi-person-plus-fill" headerBg="#10b981"> --}}
            <div x-data="hiringTrendBar({ initial: @js($chartPayload) })" x-init="$nextTick(() => init())" x-on:chart-hiring-updated.window="update($event.detail)"
                wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="(payload.series?.[0]?.data?.filter(v=>v!==null).length ?? 0) + (payload.series?.[1]?.data?.filter(v=>v!==null).length ?? 0) === 0"
                    class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
            <div class="small text-muted mt-2">
                Comparing Monday–Sunday. Future days this week are hidden.
            </div>
            {{-- </x-chart-card> --}}
        </x-slot:body>
    </x-modal>
</div>


@script
    <script>
        Alpine.data('hiringTrendBar', ({
            initial,
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [],
                meta: {}
            },

            options() {
                return {
                    chart: {
                        type: 'bar',
                        height: 300,
                        toolbar: {
                            show: false
                        }
                    },
                    series: this.payload.series, // [{name, data[7], metaDates[7]}, ...]
                    xaxis: {
                        categories: this.payload.categories, // ['Mon','Tue',...]
                        labels: {
                            rotate: 0
                        },
                    },
                    yaxis: {
                        title: {
                            text: 'New Employees Count'
                        },
                        forceNiceScale: true
                    },
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: '45%',
                            borderRadius: 4,
                            borderRadiusApplication: 'end',
                            dataLabels: {
                                position: 'top',
                                offsetY: -15,
                                style: {
                                    fontSize: '12px',
                                    fontWeight: '600',
                                    colors: ['#1f2937']
                                }
                            }
                        },
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: function(val) {
                            return val; // Show the actual value
                        },
                        offsetY: -20, // Adjust vertical position
                        style: {
                            fontSize: '12px',
                            colors: ['#ffffff'],
                            fontWeight: 'bold'
                        },
                        background: {
                            enabled: false
                        },
                        dropShadow: {
                            enabled: false
                        }
                    },
                    stroke: {
                        show: true,
                        width: 1,
                        colors: ['transparent']
                    },

                    colors: ['#94a3b8', '#3b82f6'], // prev, current
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right'
                    },
                    grid: {
                        strokeDashArray: 4
                    },
                    tooltip: {
                        shared: false,
                        y: {
                            formatter: v => (v == null ? '' : Number(v).toLocaleString())
                        },
                    }
                };
            },

            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },

            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;
                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    },
                }, false, true);
                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
