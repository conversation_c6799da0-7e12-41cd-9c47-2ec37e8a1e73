<div>
    <x-modal id="turnOverTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-arrow-left-right me-2"></i>
            Turnover Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="turnOverTrend({ initial: @js($chartPayload) })" x-init="$nextTick(() => init())"
                x-on:chart-turn-over-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="(payload.series?.every(s => (s.data || []).every(v => v === 0)) ?? true)"
                    class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
            <div class="small text-muted mt-2">
                14 days ending yesterday • Divider marks the split between the two weeks.
            </div>
        </x-slot:body>
    </x-modal>
</div>

@script
    <script>
        Alpine.data('turnOverTrend', ({
            initial
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [],
                dividerIndex: 7,
                dates: []
            },

            options() {
                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                return {
                    chart: {
                        type: 'area',
                        height: 320,
                        toolbar: {
                            show: false
                        },
                        zoom: {
                            enabled: false,
                        }
                    },
                    series: this.payload.series, // Absent, Late In, Early Out
                    xaxis: {
                        categories: this.payload.categories,
                        labels: {
                            rotate: -30
                        }
                    },
                    yaxis: {
                        min: 0,
                        forceNiceScale: true,
                        title: {
                            text: 'Employees Count'
                        },
                        labels: {
                            formatter: val => Number(val).toLocaleString()
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        width: 3,
                        curve: 'smooth'
                    },
                    markers: {
                        size: 3,
                        style: "hollow",
                    },
                    colors: [
                        '#22c55e',
                        '#dc2626',
                    ],
                    legend: {
                        position: 'top',
                        horizontalAlign: 'right'
                    },
                    grid: {
                        strokeDashArray: 4,
                        borderColor: '#e2e8f0'
                    },
                    tooltip: {
                        shared: true,
                        intersect: false,
                        y: {
                            formatter: v => Number(v ?? 0).toLocaleString()
                        }
                    },
                    fill: {
                        type: "gradient",
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0,
                            stops: [0, 100],
                        },
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }]
                    } : {}
                };
            },

            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },

            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;

                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }]
                    } : {}
                }, false, true);

                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
