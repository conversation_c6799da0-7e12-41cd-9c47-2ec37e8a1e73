<div>
    <x-modal id="activeWorkforceTrendModal" size="lg" :has-footer="false" max-width="1200px">
        <x-slot:title>
            <i class="bi bi-activity me-2"></i>
            Active Workforce Trend (Last 2 weeks)
        </x-slot:title>
        <x-slot:body>
            <div x-data="activeWorkforceLine({ initial: @js($chartPayload) })" x-init="$nextTick(() => init())"
                x-on:chart-active-workforce-updated.window="update($event.detail)" wire:ignore>
                <div x-ref="chart"></div>
                <div x-show="(payload.series?.[0]?.data?.length ?? 0) === 0" class="text-muted small text-center w-100">
                    <i class="bi bi-info-circle me-1"></i> No data available
                </div>
            </div>
            <div class="small text-muted mt-2">
                Showing last 14 days (yesterday inclusive). Divider indicates the split between week 1 and week 2.
            </div>
        </x-slot:body>
    </x-modal>
</div>

@script
    <script>
        Alpine.data('activeWorkforceLine', ({
            initial
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [],
                dividerIndex: 7,
                dates: []
            },

            options() {
                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                return {
                    chart: {
                        type: 'area',
                        height: 320,
                        toolbar: {
                            show: false
                        },
                        zoom: {
                            enabled: false,
                        }
                    },
                    series: this.payload.series, // single series
                    xaxis: {
                        categories: this.payload.categories, // all 14 date labels
                        labels: {
                            rotate: -30
                        }
                    },
                    stroke: {
                        width: 3,
                        curve: 'smooth'
                    },
                    colors: ['#3b82f6'],
                    markers: {
                        size: 3,
                        style: "hollow",
                    },
                    legend: {
                        show: true
                    },
                    dataLabels: {
                        enabled: false
                    },
                    tooltip: {
                        shared: true,
                        intersect: false
                    },
                    yaxis: {
                        title: {
                            text: 'Active Employees Count'
                        },
                        min: 0,
                        forceNiceScale: true
                    },
                    grid: {
                        strokeDashArray: 4
                    },
                    fill: {
                        type: "gradient",
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.7,
                            opacityTo: 0,
                            stops: [0, 100],
                        },
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }],
                    } : {}
                };
            },

            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },

            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;

                const dividerX = this.payload.categories?.[this.payload.dividerIndex - 1] ?? null;

                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    },
                    annotations: dividerX ? {
                        xaxis: [{
                            x: dividerX,
                            borderColor: '#9ca3af',
                            strokeDashArray: 4,
                            label: {
                                borderColor: '#9ca3af',
                                style: {
                                    color: '#111827',
                                    background: '#e5e7eb'
                                },
                            }
                        }]
                    } : {}
                }, false, true);

                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
