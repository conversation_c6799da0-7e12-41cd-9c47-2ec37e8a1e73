<div>
    <h5>MRF Settings</h5>
    <hr>

    <div>
        <div class="col-md-3">
            <x-form.list-input wire:model.live.debounce.300ms="company_id" name="company_id" label="Company"
                :addEmptyOption="true" :options="$this->companies" />
        </div>
        <div class="mt-3">
            <form wire:submit.prevent="save">
                <fieldset class="mt-4">
                    <legend>Features</legend>
                    <div class="row">
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <x-form.check-input help="Send Email" :switch="true" name="edf_email"
                                wire:model.live.debounce.300ms="edf_email" />
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6">
                            <x-form.check-input help="Add Employee" :switch="true" name="edf_add_employee"
                                wire:model="edf_add_employee" />
                        </div>
                    </div>
                    <div x-data="{ emailForm: @entangle('emailForm') }" x-show="emailForm">
                        <div class="row mt-3">
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="company_name" label="Company Name" :required="true" />
                            </div>
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="address" label="Address" :required="true" />
                            </div>
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="authorized_signatory" label="Authorized Signatory"
                                    :required="true" />
                            </div>
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="signatory_designation" label="Signatory Designation"
                                    :required="true" />
                            </div>
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="contact_mail" label="Contact Address Mail"
                                    :required="true" />
                            </div>
                            <div>
                                <x-quill id="terms_and_conditions" label="Terms & Condition" name="terms_and_conditions"
                                :required="true" load-text-event="load-terms-and-conditions"
                                :initial-value="$terms_and_conditions" />
                            </div>
                        </div>
                    </div>
                </fieldset>
                @if (isSuperAdmin())
                    <fieldset class="mt-4">
                        <legend>Mail Security</legend>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <x-form.text-input name="url" label="URL" placeholder="https://example.com/api"
                                    help="Enter the API endpoint URL" />
                            </div>
                            <div class="col-md-5 mb-2">
                                <div class="input-group" style="gap: 10px;">
                                    <x-form.text-input name="public_key" label="Public Key"
                                        help="Enter the public key for API authentication" />
                                    <div class="input-group-append" style="margin-top: auto;">
                                        <button type="button" class="btn btn-danger" wire:click="generatePublicKey">
                                            Generate
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                @endif

                <button type="submit" class="btn btn-success text-white mt-3">Save</button>
            </form>
        </div>
    </div>
</div>
