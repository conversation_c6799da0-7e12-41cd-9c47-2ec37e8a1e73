<div>
    <div class="py-1 d-flex gap-2">
        <h2 class="h4 mb-0">
            <x-form.icon name="person-lines-fill" size="md" />
            Employee Category
        </h2>
        <x-form.button size="sm" icon="plus-circle" icon_color="white" type="button" data-bs-toggle="modal"
            data-bs-target="#modal" value="Add Employee Category" :can="PermissionList::EMPLOYEE_CATEGORY_CREATE" />
    </div>
    <x-table.table-wrapper>
        <x-slot name="settings" placeholder="Search by category name" :search="true" :pageLimit="true"></x-slot>
        <x-slot name="header">
            <x-table.heading :sortable="true" col="type" :sortBy="$sortBy" :sortDirection="$sortDirection">Category
            </x-table.heading>
            <x-table.heading>Status</x-table.heading>
            <x-table.heading> Action </x-table.heading>
        </x-slot>
        <x-slot name="body">
            @foreach ($this->list as $item)
                <tr wire:key={{ $item->id }}>
                    <x-table.cell class="">
                        {{ $item->type }}
                    </x-table.cell>
                    <x-table.cell>
                        <span class="badge {{ $item->is_countable ? 'bg-success' : 'bg-danger' }}">
                            {{ $item->is_countable ? 'Active' : 'Inactive' }}
                        </span>
                    </x-table.cell>
                    <x-table.cell>
                        <x-table.action>
                            <x-table.action-option type="edit" wire:click='edit({{ $item->id }})'
                                modal-id="#modal" :can="PermissionList::EMPLOYEE_CATEGORY_UPDATE" />
                            <x-table.action-option type="delete" :can="PermissionList::EMPLOYEE_CATEGORY_DELETE"
                                wire:confirm='Are you sure to delete this category?'
                                wire:click='delete({{ $item->id }})' />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @endforeach
        </x-slot>
        <x-slot name="pagination">{{ $this->list->links() }}</x-slot>
    </x-table.table-wrapper>


    @canany([PermissionList::EMPLOYEE_CATEGORY_CREATE, PermissionList::EMPLOYEE_CATEGORY_UPDATE])
        <!--modal dailog for adding and editing starts from here -->
        <form wire:submit.prevent="save" autocomplete="off">
            <x-modal :title="$isEditing ? 'Edit Employee Category -> ' . $type : 'Add Employee Category'" id="modal" size="lg" :static-backdrop="true">
                <x-slot name="body">
                    <div wire:loading wire:loading.class='d-flex justify-content-center align-items-center'
                        wire:target='edit'>
                        <x-loading size="40px" border-width="3px" />
                    </div>
                    <div wire:loading.class='d-none' wire:target='edit'>
                        <div class="row">
                            <div class="form-group mt-3 col-md-6">
                                <x-form.text-input name="type" label="Category Name"
                                    :required="true"></x-form.text-input>
                            </div>
                            <div class="form-group mt-3 col-md-3">
                                <x-form.check-input name="is_countable" label="Is Countable?" :switch="true"
                                    :value="$is_countable" />
                            </div>
                        </div>
                    </div>
                </x-slot>
                <x-slot name="footer">
                    <x-form.button color="success">Save</x-form.button>
                </x-slot>
            </x-modal>
        </form>
    @endcanany

</div>
