<div>
    <div class="py-2 d-flex justify-content-between align-items-center">
        <x-title icon="person-vcard-fill">Consecutive Attendance Report</x-title>
        {{-- <div wire:loading class="text-muted small">Loading...</div> --}}
    </div>

    <div class="d-grid xl-grid-cols-6 lg-grid-cols-5 md-grid-cols-3 sm-grid-cols-2 gap-3 mt-3 mb-3">
        @foreach ($this->counts as $count)
            <button
                class="card card-body shadow text-center card-hover cursor-pointer @if (strtolower($count['label']) == strtolower($selectedStatus)) bg-selected @endif"
                wire:click="updateStatusFilter('{{ strtolower($count['label']) }}')">
                <h3>{{ $count['count'] }}</h3>
                <h5>{{ $count['label'] }} </h5>
            </button>
        @endforeach
    </div>

    <div class="card shadow-sm p-3">
        <!-- Filters -->
        <div class="row mb-3">
            <x-shared.default-filters :region="false" showLabel labelFontSize="13px" />

            <!-- Start Date -->
            {{-- <div class="col-md-2">
                <label class="form-label">Start Date</label>
                <input type="date" wire:model.live.debounce.300ms="startDate" class="form-control" />
            </div> --}}

            <!-- End Date -->
            <!-- Status / Issue Type -->
            <div class="col-md-2">
                <label class="form-label" style="margin-bottom: 2px;">Status</label>
                <select wire:model.live.debounce.300ms="selectedStatus" class="form-select">
                    @foreach ($issueTypeOptions as $key => $label)
                        <option value="{{ $key }}" wire:key="{{ $key }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>
        </div>


        <!-- Table -->
        <x-table.table-wrapper
            loadingTarget="search,selectedDate,selectedStatus,filterCompanyId,filterBranchId,filterDepartmentId,records,pageLimit,sortBy,sortDirection,updateStatusFilter">
            <x-slot name="settings" :search="true" :pageLimit="true">
                <div class="col-md-2">
                    {{-- <label class="form-label">Date</label> --}}
                    <input type="date" wire:model.live.debounce.300ms="selectedDate" class="form-control" />
                </div>

                <div class="d-flex gap-2 justify-content-first align-items-start flex-wrap">
                    <div>
                        <x-form.button color="info"
                            wire:click='exportConsecutiveAttendanceReport(false)'>Export</x-form.button>
                    </div>
                    <div>
                        <x-form.button color="danger" wire:click='exportConsecutiveAttendanceReport(true)'
                            value="Export All" />
                    </div>
                </div>
            </x-slot>


            <x-slot name="header">
                <x-table.heading :sortable="true" :sortBy="$sortBy" :sortDirection="$sortDirection">
                    Employee Name
                </x-table.heading>
                <x-table.heading>Branch</x-table.heading>
                <x-table.heading>Department</x-table.heading>
                <x-table.heading>Status</x-table.heading>
                <x-table.heading>Date</x-table.heading>
                <x-table.heading>In Time</x-table.heading>
                <x-table.heading>Out Time</x-table.heading>
            </x-slot>

            <x-slot name="body">
                @foreach ($this->records as $record)
                    <tr wire:key="record-{{ $record->id }}">
                        <x-table.cell>
                            {{ $record->employee_name }} <br />
                            <small class="text-muted">[{{ $record->employee_code }}]</small>
                        </x-table.cell>
                        <x-table.cell>{{ $record->branch_name ?? '-' }}</x-table.cell>
                        <x-table.cell>{{ $record->department_name ?? '-' }}</x-table.cell>
                        <x-table.cell>{{ $record->status ?? '-' }}</x-table.cell>
                        <x-table.cell>{{ \Carbon\Carbon::parse($record->date_en)->format('M d, Y') }}</x-table.cell>
                        <x-table.cell>{{ $record->in_time ?? '-' }}</x-table.cell>
                        <x-table.cell>{{ $record->out_time ?? '-' }}</x-table.cell>
                    </tr>
                @endforeach
            </x-slot>

            <x-slot name="pagination">
                {{ $this->records->links() }}
            </x-slot>
        </x-table.table-wrapper>
    </div>

    <style>
        .bg-selected {
            background-color: #e4e4e4;
            color: #ebebeb;
        }
    </style>
</div>
