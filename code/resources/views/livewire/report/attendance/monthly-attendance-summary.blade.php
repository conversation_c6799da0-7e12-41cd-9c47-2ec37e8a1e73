<div>
    <div class="py-1 d-flex gap-2">
        <x-title icon="person-fill-gear" :back-link="route('reportPage')">Monthly Attendance Summary</x-title>
    </div>
    <section class="card card-body">
        <fieldset class="mb-3">
            <legend>Filters</legend>
            <div class="d-grid xl-grid-cols-4 lg-grid-cols-3 md-grid-clos-2 gap-3">
                <x-form.list-input wire:model.live.debounce.300ms="company" label="Company">
                    @foreach ($this->companies as $company)
                        <option value="{{ $company->id }}">{{ $company->name }}</option>
                    @endforeach
                </x-form.list-input>
                <x-form.list-input wire:model.live.debounce.300ms="region" label="Region">
                    @foreach ($this->regions as $region)
                        <option value="{{ $region->id }}">{{ $region->name }}</option>
                    @endforeach
                </x-form.list-input>
                <x-form.list-input wire:model.live.debounce.300ms="branch" label="Branch">
                    @foreach ($this->branches as $branch)
                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                    @endforeach
                </x-form.list-input>
                <x-form.list-input wire:model.live.debounce.300ms="department" label="Department">
                    @foreach ($this->departments as $department)
                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                    @endforeach
                </x-form.list-input>
                <div style="min-width: 300px">
                    <x-shared.employee-dropdown :list="$this->employeeList" label="Employee" font-size="13px"
                        wire:model='employee_ids' :required="true" />
                </div>

            </div>
        </fieldset>
    </section>

    <x-table.table-wrapper class="table-stripped table-responsive" :add-card="true">

        <x-slot name='settings' :search="false">
            <div class="d-flex justify-content-left mt-1 gap-3">
                @can(PermissionList::MONTHLY_ATTENDANCE_SUMMARY_EXPORT)
                    <x-form.button color="info" wire:click="exportAllData(false)" value="Export"></x-form.button>
                @endCan
                @can(PermissionList::MONTHLY_ATTENDANCE_SUMMARY_EXPORT_ALL)
                    <x-form.button color="danger" wire:click="exportAllData(true)" value="Export All"></x-form.button>
                @endcan
            </div>
        </x-slot>

        <x-slot name="header">
            <x-table.heading :sortable="true">Employee Name</x-table.heading>
            <x-table.heading>Branch<br />Department</x-table.heading>
            <x-table.heading>Join Date</x-table.heading>
            <x-table.heading>Last Job Date</x-table.heading>
            <x-table.heading>WeekEnd</x-table.heading>
            <x-table.heading>Holiday</x-table.heading>
            <x-table.heading>Duty Days</x-table.heading>
            <x-table.heading>Present Days</x-table.heading>
            <x-table.heading>Home Leave</x-table.heading>
            <x-table.heading>Sick Leave</x-table.heading>
            <x-table.heading>Total Leave</x-table.heading>
            <x-table.heading>Substitute Leave</x-table.heading>
            <x-table.heading>Total Tenure Days</x-table.heading>

            <x-table.heading>Previous Month Total Leave</x-table.heading>

            <x-table.heading>Total Leave Upto Last Month</x-table.heading>
            <x-table.heading>Previous Month Total Substitute Leave</x-table.heading>
            <x-table.heading>Total Substitute Leave up to Last Month</x-table.heading>
            <x-table.heading>Other Leave Till Last Month</x-table.heading>
            <x-table.heading>Total Leave Till Now</x-table.heading>
            <x-table.heading>Remaining Leave On Monthly Basis(After Deduction)</x-table.heading>

        </x-slot>

        <x-slot name="body">
            <div wire:loading wire:loading.class="d-flex justify-content-center align-item-center">
            </div>
            @foreach ($this->list as $item)
                <tr wire:key={{ $item->id }} @class(['danger' => $item->deleted_at || $item->termination_date])>
                    <x-table.cell>{{ $item->full_name }}<br />
                        ({{ $item->emp_code }})
                    </x-table.cell>
                    <x-table.cell>{{ $item->branch_name }}<br />{{ $item->department_name }}</x-table.cell>
                    <x-table.cell>{{ $item->date_of_join }}</x-table.cell>
                    <x-table.cell>{{ $item->last_date }}</x-table.cell>
                    <x-table.cell>{{ $item->weekends }}</x-table.cell>
                    <x-table.cell>{{ $item->total_holidays }}</x-table.cell>
                    <x-table.heading>{{ $item->total_duty_days }}</x-table.heading>
                    <x-table.heading>{{ $item->total_present_days }}</x-table.heading>
                    <x-table.heading>{{ $item->home_leave }}</x-table.heading>
                    <x-table.heading>{{ $item->total_sick_leave }}</x-table.heading>
                    <x-table.heading>{{ $item->total_combined_leave }}</x-table.heading>
                    <x-table.heading>{{ $item->replacement_leave }}</x-table.heading>
                    <x-table.heading>{{ $item->total_tenure_days }}</x-table.heading>

                    <x-table.heading>{{ $item->prev_month_total_leave }}</x-table.heading>

                    <x-table.heading>{{ $item->total_leave_upto_last_month }}</x-table.heading>

                    <x-table.heading>{{ $item->prev_month_replacement_leave }}</x-table.heading>
                    <x-table.heading>{{ $item->upto_last_month_replacement }}</x-table.heading>
                    <x-table.heading>{{ $item->other_leave_upto_last_month }}</x-table.heading>
                    <x-table.heading>{{ $item->total_leave_taken_till_now }}</x-table.heading>
                    <x-table.heading>{{ $item->total_remaining_leave }}</x-table.heading>

                </tr>
            @endforeach
        </x-slot>

        <x-slot name="pagination">
            {{ $this->list->links() }}
        </x-slot>

    </x-table.table-wrapper>
</div>
