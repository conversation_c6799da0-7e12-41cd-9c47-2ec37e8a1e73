<div>
    <div class="py-1 d-flex gap-2">
        <x-title icon="person-fill-gear" :back-link="route('reportPage')">Field Team Attendance Report</x-title>
    </div>
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <x-form.nepali-date-picker-input label="Start Date" wire:model="startDate" />
                </div>
                <div class="col-md-3">
                    <x-form.nepali-date-picker-input label="End Date" wire:model="endDate" />
                </div>
                <div class="col-md-3">
                    <label for="team_id">Team</label>
                    <select wire:model="team_id" class="form-control">
                        <option value="">All Teams</option>
                        @foreach ($this->teams as $team)
                            <option value="{{ $team->id }}">{{ $team->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 mt-1">
                    <x-shared.employee-dropdown :list="$this->employeeList" label="Employee" font-size="13px" />
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12 d-flex justify-content-center gap-2">
                    <button wire:click="applyFilter" class="btn btn-primary">Apply Filter</button>
                    <button wire:click="export" wire:loading.attr="disabled" class="btn btn-info">Export</button>
                    <button wire:click="export(true)" wire:loading.attr="disabled" class="btn btn-danger">Export All</button>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            @if ($filterApply)
                @if ($employee_id)
                    <h5>Detailed Field Team Attendance Report</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Employee Name</th>
                                    <th>Employee Code</th>
                                    <th>Date</th>
                                    <th>In Time</th>
                                    <th>Out Time</th>
                                    <th>Status</th>
                                    <th>Total Hours</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($this->list as $record)
                                    <tr>
                                        <td>{{ $record['employee_name'] }}</td>
                                        <td>{{ $record['employee_code'] }}</td>
                                        <td>{{ $record['date_np'] }}</td>
                                        <td>{{ $record['in_time'] }}</td>
                                        <td>{{ $record['out_time'] }}</td>
                                        <td>
                                            {{ $record['status'] }}
                                        </td>
                                        <td>{{ $record['total_hours'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <h5>Summary Field Team Attendance Report</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Employee Name</th>
                                    <th>Employee Code</th>
                                    <th>Member Type</th>
                                    <th>Total Hours</th>
                                    <th>Number of Days</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($this->list as $record)
                                    <tr>
                                        <td>{{ $record['employee_name'] }}</td>
                                        <td>{{ $record['employee_code'] }}</td>
                                        <td>
                                            {{ $record['member_type'] }}
                                        </td>
                                        <td>{{ $record['total_hours'] }}</td>
                                        <td>{{ $record['days_count'] }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif

                <!-- Pagination -->
                <div class="mt-3">
                    {{ $attendanceList->links() }}
                </div>
            @else
                <div class="text-center text-muted">
                    <p>Click "Apply Filter" to see the report data</p>
                </div>
            @endif
        </div>
    </div>
</div>
