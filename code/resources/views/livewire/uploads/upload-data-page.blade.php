<div>
    <x-title icon="cloud-upload">Data Upload Center</x-title>
    <p class="text-muted">Select the type of data you wish to upload or update via Excel/CSV.</p>

    @if (collect($this->uploadMenus)->where('condition', true)->count() > 0)

        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xxl-5 g-4">
            @foreach ($this->uploadMenus as $menu)
                @if ($menu['condition'])
                    <div class="col">
                        <a href="{{ $menu['href'] }}" class="card-link text-decoration-none">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body d-flex flex-column align-items-center text-center">
                                    {{-- Using Bootstrap Icons (bi) as specified by the prefix 'bi' --}}
                                    <i class="{{ $menu['icon'] }} display-4 mb-3 text-primary"></i>
                                    <h5 class="card-title text-dark">{{ $menu['label'] }}</h5>
                                </div>
                            </div>
                        </a>
                    </div>
                @endif
            @endforeach
        </div>
    @else
        <div class="alert alert-warning mt-4" role="alert">
            <h4 class="alert-heading">No Upload Permissions</h4>
            <p>You do not have the necessary permissions to access any data upload sections.</p>
        </div>
    @endif
</div>

@pushOnce('style')
    <style>
        .card-link .card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .card-link .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }
    </style>
@endpushOnce
