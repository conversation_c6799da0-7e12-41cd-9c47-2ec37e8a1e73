@php
    $excelHeaders = $this->headers();
    $requiredHeaders = $this->requiredHeaders();
    $dateFields = $this->getDateFields();
    $timeFields = $this->getTimeFields();
@endphp

<div>
    <div class="mb-3">
        <x-title icon="{{ $icon }}">{{ $title }}</x-title>
    </div>
    <div class="card p-3">

        <div class="mb-3">
            <div class="d-flex gap-2 align-items-center mb-2">
                <h4>Sample: </h4>
                <x-form.button color="info" type="button" wire:click='downloadSample' value="Download Sample" />
            </div>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tr>
                        @foreach ($excelHeaders as $header => $item)
                            <th>
                                {{ $header }}
                                @if (in_array($header, $requiredHeaders))
                                    <span class="text-danger">*</span>
                                @endif
                            </th>
                        @endforeach
                    </tr>
                    <tr>
                        @foreach ($excelHeaders as $header => $item)
                            @if (in_array($header, $dateFields))
                                <td>mm/dd/yyyy or <br />yyyy-mm-dd</td>
                            @elseif (in_array($header, $timeFields))
                                <td>hh:mm:ss (24 hr format)</td>
                            @else
                                <td>{{ $item }}</td>
                            @endif
                        @endforeach
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="card p-3 mt-3">

        <form wire:submit.prevent='validateExcelData' autocomplete="off">
            <div class="mb-2" style="max-width: 300px">
                @if ($this->requireCompany && scopeAll())
                    <x-form.list-input label="Select Company" wire:model='companyId' name="companyId"
                        empty-option-placeholder="Select Company" :options="$this->companyList" />
                @endif
                <div class="mt-4">
                    <div class="d-flex gap-2 align-items-center">
                        <x-form.input-label value="Add Document" />
                        <small>(.xls, .xlsx upto 2MB)</small>
                    </div>
                </div>
                <x-form.file-input name='excelFile'
                    accept=" application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                    clear-event="clear-leave-uploads" />
            </div>
            <span wire:loading.class='d-block' wire:target='validateExcelData' class="d-none fst-italic">*Hang
                tight! Please keep this window open and avoid refreshing the page. It might take a moment,
                appreciate your patience!</span>
            <div class="mt-4">
                <x-form.button type="submit" color="success" value="Validate" wire:target='validateExcelData' />
            </div>
        </form>

        @if (count($excelData))
            <div class="mt-4">
                <x-form.button type="button" wire:click='exportOutputData' value="Download Output" />
            </div>
            <div class="mt-4 table-responsive" style="max-height: 70vh">
                <table class="table table-bordered ">
                    @foreach (array_keys($excelData[0]['data'] ?? []) as $header)
                        <th class="text-capitalize">{{ array_flip($this->headerMap())[$header] ?? $header }}</th>
                    @endforeach
                    @foreach ($excelData as $item)
                        <tr @class(['danger' => $item['error']])>
                            @foreach ($item['data'] as $key => $value)
                                <td>{{ $value }}</td>
                            @endforeach
                        </tr>
                        @if ($item['error'])
                            <tr>
                                <td colspan="{{ count($item['data']) }}" class="px-2 py-1 pb-2">Error of <i
                                        class="bi bi-arrow-up"></i>:
                                    {{ $item['error'] }}</td>
                            </tr>
                        @endif
                    @endforeach
                </table>
            </div>

            @if ($this->excelDataValidated)
                @if (!$excelUploaded)
                    <div class="mt-3">
                        <x-form.button type="button" color="success" wire:click='uploadData'
                            value="Upload Excel Data" />
                    </div>
                @endif
                @if ($uploadValidationMessage)
                    <div class="mt-3">
                        <span class="@if ($excelUploaded) @else text-danger @endif"
                            style="font-size: 16px;">
                            {{ $uploadValidationMessage }}
                        </span>
                    </div>
                @endif

                @if ($uploadedData)
                    <p class="mt-3">
                        Uploaded Data:
                    </p>
                    <div class="table-responsive">

                        <table class="table table-bordered ">
                            @foreach (array_keys($excelData[0]['data'] ?? []) as $header)
                                <th class="text-capitalize">{{ array_flip($this->headerMap())[$header] ?? $header }}
                                </th>
                            @endforeach
                            @foreach ($uploadedData as $item)
                                <tr @class(['danger' => $item['error']])>
                                    @foreach ($item['data'] as $key => $value)
                                        <td>{{ $value }}</td>
                                    @endforeach
                                </tr>
                                @if ($item['error'])
                                    <tr>
                                        <td colspan="{{ count($item['data']) }}" class="px-2 py-1 pb-2">Error of <i
                                                class="bi bi-arrow-up"></i>:
                                            {{ $item['error'] }}</td>
                                    </tr>
                                @endif
                            @endforeach
                        </table>
                    </div>
                @endif
            @endif
        @endif
    </div>

</div>
