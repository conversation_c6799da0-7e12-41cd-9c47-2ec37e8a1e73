<div>
    <x-modal id="edfSendEmailModal" title="Send Email" size="lg" on='hidemodal' :has-footer="true"
        nested-modal-id="#ticketPopup" showOn="show-edf-email-modal" closeOn="hide-edf-email-modal">
        <form>
            <x-slot name="body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <x-form.text-input label="Name" name="name" :required="true" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.text-input label="Email" name="email" :required="true" />
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <x-form.nepali-date-picker-input label="Deadline" name="deadline" :required="true" />
                    </div>
                    <div class="col-md-6 mb-3">
                        <x-form.text-input label="CTC Offer" name="ctc_offer" :required="true" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <x-form.check-input wire:model.live.debounce.300ms="show_jd" label="Show Job Description?"
                            :switch="true" :value="$show_jd" />
                    </div>
                </div>
                <div x-data="{ showJD: @entangle('show_jd') }" x-show="showJD">
                    <div class="row">
                        <div class="col-md-12 mb-3 my-3">
                            <x-quill id="responsibilities" label="Responsibilities" name="responsibilities"
                                :required="true" load-text-event="load-offer-letter-responsibility"
                                :initial-value="$responsibilities" />
                        </div>
                    </div>
                    <div class="col-md-12 mb-3">
                        <x-quill id="kra" label="Key Responsibility Area" name="kra" :required="true"
                            load-text-event="load-offer-letter-kra" :initial-value="$kra" />
                    </div>
                    <div class="col-md-12 mb-3">
                        <x-quill id="terms_and_conditions" label="Terms And Conditions" name="terms_and_conditions"
                            :required="true" load-text-event="load-offer-letter-terms-and-conditions"
                            :initial-value="$terms_and_conditions" />
                    </div>
                </div>

            </x-slot>
            <x-slot name="footer">
                <x-form.button color="success" wire:click="sendMail">Send</x-form.button>
            </x-slot>
        </form>
    </x-modal>
    <x-modal id="sentEmailsModal" title="Sent Emails" size="xl" on='hidemodal' :has-footer="false"
        nested-modal-id="#ticketPopup" showOn="show-sent-emails-modal" closeOn="hide-sent-emails-modal">
        <x-slot name="body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>DOJ</th>
                            <th>Deadline</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($sentEmails as $email)
                            <tr>
                                <td>{{ $email->name }}</td>
                                <td>{{ $email->email }}</td>
                                <td>{{ $email->doj }}</td>
                                <td>{{ $email->deadline }}</td>
                                <td>
                                    @php
                                        $badge = 'danger';
                                        if ($email->status === 'Approved') {
                                            $badge = 'success';
                                        } elseif ($email->status === 'Pending') {
                                            $badge = 'warning';
                                        }
                                        $responseEmail = $email->response_meta['form_data']['email'] ?? null;
                                    @endphp
                                    <button class="btn btn-{{ $badge }} btn-sm pe-none text-white">
                                        {{ $email->status }}
                                    </button>
                                </td>
                                @if ($email->status === 'Approved' && !in_array($responseEmail, $employeeStatus))
                                    <td><a href="{{ route('edf', $email->mrf_id) }}?uuid={{ $email->id }}"
                                            target="_blank"
                                            class="btn btn-sm btn-gray-800 d-inline-flex align-items-center">
                                            <x-form.icon name="plus-circle" class="me-2" color="text-white" />
                                            Add
                                        </a>
                                    </td>
                                @elseif ($email->status === 'Approved' && in_array($responseEmail, $employeeStatus))
                                    @php
                                        $requestId = array_search($responseEmail, $employeeStatus);
                                    @endphp
                                    @if ($requestId)
                                        <td>
                                            <a href="{{ route('ticketPage', ['workflow' => 'edf_request', 'requestId' => $requestId]) }}"
                                                target="_blank"
                                                class="btn btn-sm btn-primary d-inline-flex align-items-center">
                                                <x-form.icon name="eye" class="me-2" color="text-white" />
                                                View Ticket
                                            </a>
                                        </td>
                                    @else
                                        <td>N/A</td>
                                    @endif
                                @else
                                    <td>N/A</td>
                                @endif

                            </tr>
                        @endforeach
                        </tr>
                    </tbody>
                </table>
            </div>
        </x-slot>
    </x-modal>
</div>
