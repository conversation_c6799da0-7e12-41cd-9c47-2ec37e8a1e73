<div>
    <div class="py-1 d-flex gap-2">
        <x-title icon="hourglass-split" :back-link="route('ticketDashboard')">Field Visit OT Request List</x-title>
        <x-form.button type="button" color="primary" class="w-fit" wire:click="exportFieldVisitOt('fixed')" :can='PermissionList::FIELD_VISIT_OT_REQUEST_EXPORT'>Export
        </x-form.button>
        <x-form.button type="button" color="danger" class="w-fit" wire:click="exportFieldVisitOt('all')" :can='PermissionList::FIELD_VISIT_OT_REQUEST_EXPORT_ALL'>Export
            All</x-form.button>
    </div>
    <x-table.table-wrapper loading-target="status, date">
        <x-slot name="settings" :search="true" :pageLimit="true" placeholder="Search employee name..">
            <x-shared.default-filters />
            <div class="col-md-2">
                <x-form.list-input wire:model.live.debounce.300ms="status" :addEmptyOption="false">
                    <option value="">Select Status</option>
                    @foreach ($this->statusList as $item)
                        <option value="{{ $item }}">{{ $item }}</option>
                    @endforeach
                </x-form.list-input>
            </div>
            <div class="d-flex gap-2">
                <div class="col-md-3">
                    <x-form.nepali-date-picker-input wire:model.live.debounce.300ms="startDate"
                        placeholder="Select a date" />
                </div>
                <div class="mt-2">to</div>
                <div class="col-md-3">
                    <x-form.nepali-date-picker-input wire:model.live.debounce.300ms="endDate"
                        placeholder="Select a date" />
                </div>
            </div>
        </x-slot>
        <x-slot name="header">
            <x-table.heading :sortable="true" col="emp_name" :sortBy="$sortBy" :sortDirection="$sortDirection">Employee</x-table.heading>
            <x-table.heading>Check In Date<br>Check Out Date</x-table.heading>
            <x-table.heading>Check In Time<br />Check Out Time</x-table.heading>
            <x-table.heading>Total Hours</x-table.heading>
            <x-table.heading>Name</x-table.heading>
            <x-table.heading>Type</x-table.heading>
            <x-table.heading>Location</x-table.heading>
            <x-table.heading>Purpose</x-table.heading>
            <x-table.heading>State</x-table.heading>
            <x-table.heading>Action</x-table.heading>
        </x-slot>
        <x-slot name="body">
            @foreach ($this->list as $item)
                <tr>
                    <x-table.cell :sortable="true" col="emp_name" :sortBy="$sortBy" :sortDirection="$sortDirection">
                        {{ $item->emp_name }} <br />
                        <small>{{ $item->emp_code }}</small>
                    </x-table.cell>
                    <x-table.cell>{{ $item->check_in_date_np }} <br /> {{ $item->check_out_date_np }}</x-table.cell>
                    <x-table.cell>{{ $item->check_in_time }} <br /> {{ $item->check_out_time }}</x-table.cell>
                    <x-table.cell>{{ $item->total_hours }}</x-table.cell>
                    <x-table.cell>{{ $item->name }}</x-table.cell>
                    <x-table.cell>{{ $item->type }}</x-table.cell>
                    <x-table.cell>{{ $item->location }}</x-table.cell>
                    <x-table.cell>{{ $item->purpose }}</x-table.cell>
                    <x-table.cell><x-tickets.status :status="$item->state" /></x-table.cell>
                    <x-table.cell>
                        <x-table.action>
                            <x-table.action-option icon="eye" color="primary" label="View Details"
                                wire:click='showDetail({{ $item->id }})' modal-id="#ticketPopup" />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @endforeach
        </x-slot>
        <x-slot name="pagination">{{ $this->list->links() }}</x-slot>
    </x-table.table-wrapper>

    <livewire:self-service.my-tickets.ticket-popup :request-id="$selectedId" workflow="field_visit_workflow"
        @refresh-list="refreshList" />
</div>
