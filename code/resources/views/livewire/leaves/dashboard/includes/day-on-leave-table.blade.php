<section class="card card-body mt-3">
    <h6 class="mb-2">On Leave — {{ $selectedDate }}</h6>
    <x-table.table-wrapper :add-card="false" loadingTarget="perPageDay, employee_ids_day">
        <x-slot name="settings" :search="false" :pageLimit="false">
            <div class="col-1">
                <x-form.list-input name="perPageDay" :addEmptyOption="false" :options=[10,25,50,100]
                    wire:model.live.debounce.300ms="perPageDay" />
            </div>
            <div style="min-width: 300px">
                <x-shared.employee-dropdown font-size="13px" wire:model='employee_ids_day' multiple />
            </div>
            <div>
                <x-form.button color="info" wire:click='onLeaveExportDataTable'>Export</x-form.button>
            </div>
            <div>
                <x-form.button color="danger" wire:click='onLeaveExportAllData' value="Export All" />
            </div>
        </x-slot>
        <x-slot name="header">
            <x-table.heading>Employee</x-table.heading>
            <x-table.heading>Leave Type</x-table.heading>
            <x-table.heading>Option</x-table.heading>
            <x-table.heading>Date</x-table.heading>
            <x-table.heading>Action</x-table.heading>
        </x-slot>
        <x-slot name="body">
            @forelse ($this->onLeaveToday as $row)
                <tr>
                    <x-table.cell>{{ $row->employee_name }} <small class="text-muted">[{{ $row->emp_code }}]</small></x-table.cell>
                    <x-table.cell>{{ $row->leave_type ?? '—' }}</x-table.cell>
                    <x-table.cell>{{ $row->leave_option ?? '—' }}</x-table.cell>
                    <x-table.cell>{{ $row->date_np }}<br/><span class="text-muted">{{ $row->date_en }}</span></x-table.cell>
                    <x-table.cell>
                        <x-table.action>
                            <x-table.action-option icon="eye" color="primary" label="View Leave Request"
                                target="_blank"
                                link="{{ route('ticketPage', ['workflow' => $row->workflow, 'requestId' => $row->leave_request_id]) }}" />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @empty
                <tr>
                    <td colspan="6" class="text-center text-muted">No employees on leave for the selected
                        date.</td>
                </tr>
            @endforelse
        </x-slot>
        <x-slot name="pagination">{{ $this->onLeaveToday->links(data: ['scrollTo' => false]) }}</x-slot>
    </x-table.table-wrapper>
</section>
