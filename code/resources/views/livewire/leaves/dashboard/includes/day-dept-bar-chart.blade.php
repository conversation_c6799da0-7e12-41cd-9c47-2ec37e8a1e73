<div class="col-lg-6">
    <div class="card shadow-sm h-100">
        <div class="card-header d-flex justify-content-between">
            <span>Department-wise</span>
            <small>{{ $selectedDate }}</small>
        </div>
        <div class="card-body">
            <div x-data="leaveDeptDayBar({ initial: @js($this->chartDeptBarDay) })" x-init="$nextTick(() => init())" x-on:chart-dept-day-updated.window="update($event.detail)"
                wire:ignore wire:key="chart-dept-day">
                <div x-ref="chart"></div>
            </div>
        </div>
    </div>
</div>

@script
    <script>
        Alpine.data('leaveDeptDayBar', ({
            initial
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [{
                    name: '',
                    data: []
                }]
            },
            options() {
                return {
                    chart: {
                        type: 'bar',
                        height: 320,
                        toolbar: {
                            show: false
                        },
                    },
                    series: this.payload.series,
                    xaxis: {
                        categories: this.payload.categories
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '45%',
                            borderRadius: 6
                        }
                    },
                    dataLabels: {
                        enabled: false
                    },
                    colors: ['#10b981'],
                    states: {
                        normal: {
                            filter: {
                                type: 'none'
                            }
                        },
                        hover: {
                            filter: {
                                type: 'none'
                            }
                        },
                        active: {
                            filter: {
                                type: 'none'
                            }
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: v => Number(v).toLocaleString()
                        }
                    }
                };
            },
            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },
            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;
                this.chart.updateOptions({
                    xaxis: {
                        categories: this.payload.categories
                    }
                }, false, true);
                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
