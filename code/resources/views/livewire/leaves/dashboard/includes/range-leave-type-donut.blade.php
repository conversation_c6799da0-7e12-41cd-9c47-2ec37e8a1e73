<div class="col-lg-4">
    <div class="card shadow-sm h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span>By Leave Type</span>
            <small class="text-muted">{{ $this->kpis['window'] ?? '' }}</small>
        </div>
        <div class="card-body">
            <div x-data="leaveTypeDonutRange({ initial: @js($this->chartDonutRange) })" x-init="$nextTick(() => init())"
                x-on:chart-donut-range-updated.window="update($event.detail)" wire:ignore wire:key="chart-donut-range">
                <div x-ref="chart"></div>
            </div>
        </div>
    </div>
</div>

@script
    <script>
        Alpine.data('leaveTypeDonutRange', ({
            initial
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                labels: [],
                series: []
            },
            options() {
                return {
                    chart: {
                        type: 'donut',
                        height: 320,
                    },
                    labels: this.payload.labels,
                    series: this.payload.series,
                    colors: [
                        '#22c55e', // green
                        '#3b82f6', // blue
                        '#ef4444', // red
                        '#f59e0b', // amber
                        '#8b5cf6', // purple
                        '#10b981', // emerald
                        '#6366f1', // indigo
                        '#ec4899', // pink
                        '#14b8a6', // teal
                        '#eab308', // yellow
                        '#84cc16', // lime
                        '#0ea5e9', // sky
                        '#d946ef', // fuchsia
                        '#f97316', // orange
                        '#a855f7', // violet
                        '#06b6d4', // cyan
                        '#dc2626', // darker red
                        '#2563eb', // darker blue
                        '#16a34a', // darker green
                        '#9333ea', // deep violet
                        '#facc15', // bright yellow
                        '#fb7185', // rose
                        '#0891b2', // cyan-dark
                        '#f472b6', // pink-light
                    ],
                    legend: {
                        position: 'bottom',
                        onItemClick: {
                            toggleDataSeries: false
                        },
                        onItemHover: {
                            highlightDataSeries: false
                        }
                    },
                    states: {
                        normal: {
                            filter: {
                                type: 'none'
                            }
                        },
                        hover: {
                            filter: {
                                type: 'none'
                            }
                        },
                        active: {
                            filter: {
                                type: 'none'
                            }
                        }
                    },
                    plotOptions: {
                        pie: {
                            expandOnClick: false,
                            donut: {
                                size: '65%',
                                labels: {
                                    show: true,
                                    total: {
                                        show: true,
                                        label: 'Total',
                                        formatter: () => (this.payload.series || []).reduce((a,
                                            b) => a + Number(b || 0), 0)
                                    }
                                }
                            }
                        }
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: (val, opts) => (opts.w.config.series[opts.seriesIndex] || 0)
                            .toLocaleString()
                    },
                    tooltip: {
                        y: {
                            formatter: v => Number(v).toLocaleString()
                        }
                    }
                };
            },
            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },
            update(e) {
                if (!e || !e.payload || !this.chart) return;
                this.payload = e.payload;
                this.chart.updateOptions({
                    labels: this.payload.labels
                }, false, true);
                this.chart.updateSeries(this.payload.series, true);
            }
        }));
    </script>
@endscript
