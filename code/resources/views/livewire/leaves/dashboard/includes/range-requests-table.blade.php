<section class="card card-body mt-3" id="requests-list">
    <div class="d-flex flex-wrap align-items-center gap-2 mb-2">
        <h6 class="mb-0">Leave Requests on range</h6>
        @if ($this->requestsFilterText)
            <span class="badge bg-gray-700">{{ $this->requestsFilterText }}</span>
            <button class="btn btn-sm btn-outline-secondary ms-auto" wire:click="resetRequestsListFilters">
                Reset Filters
            </button>
        @endif
        <div>
            <x-form.button color="info" wire:click='exportRequestsInRange'>Export</x-form.button>
        </div>
        <div>
            <x-form.button color="danger" wire:click='exportAllRequestsInRange' value="Export All" />
        </div>
    </div>
    <x-table.table-wrapper :add-card="false" loading-target="employee_ids_range, state, perPageRange">
        <x-slot name="settings" :search="false" :pageLimit="false">
            <x-form.list-input name="perPageRange" :addEmptyOption="false" :options=[10,25,50,100]
                wire:model.live.debounce.300ms="perPageRange" />
            <div>
                <x-form.list-input wire:model.live.debounce.300ms="state" :addEmptyOption="true"
                    emptyOptionPlaceholder="All States" :options="$this->stateList" />
            </div>
            <div style="min-width: 300px">
                <x-shared.employee-dropdown font-size="13px" wire:model='employee_ids_range' multiple />
            </div>
        </x-slot>
        <x-slot name="header">
            <x-table.heading>Employee</x-table.heading>
            <x-table.heading>Type</x-table.heading>
            <x-table.heading>Option</x-table.heading>
            <x-table.heading>Period</x-table.heading>
            <x-table.heading>Days</x-table.heading>
            <x-table.heading>State</x-table.heading>
            <x-table.heading>Applied</x-table.heading>
            <x-table.heading>Action</x-table.heading>
        </x-slot>
        <x-slot name="body">
            @forelse ($this->requestsInRange as $r)
                <tr>
                    <x-table.cell>
                        {{ $r->employee_name }} <small class="text-muted">[{{ $r->emp_code }}]</small>
                        @if ($r->employee_deleted_at)
                            <br />
                            <span class="badge bg-danger">
                                <i class="bi bi-exclamation-circle"></i>
                                <span>Terminated</span>
                            </span>
                        @endif
                    </x-table.cell>
                    <x-table.cell>{{ $r->leave_type ?? '—' }}</x-table.cell>
                    <x-table.cell>{{ $r->leave_option ?? '—' }}</x-table.cell>
                    <x-table.cell>
                        <strong>{{ $r->nep_start_date }}</strong> to
                        <strong>{{ $r->nep_end_date }}</strong><br>
                        <span class="text-muted small">{{ $r->start_date }} → {{ $r->end_date }}</span>
                    </x-table.cell>
                    <x-table.cell>{{ $r->num_days }}</x-table.cell>
                    <x-table.cell><x-tickets.status :status="$r->state" /></x-table.cell>
                    <x-table.cell>{{ $r->created_at->diffForHumans() }}</x-table.cell>
                    <x-table.cell>
                        <x-table.action>
                            <x-table.action-option icon="eye" color="primary" label="View Details" target="_blank"
                                link="{{ route('ticketPage', ['workflow' => $r->workflow, 'requestId' => $r->id]) }}" />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="text-center text-muted">No requests overlapping the selected
                        range.</td>
                </tr>
            @endforelse
        </x-slot>
        <x-slot name="pagination">{{ $this->requestsInRange->links(data: ['scrollTo' => false]) }}</x-slot>
    </x-table.table-wrapper>
</section>
