<div class="col-lg-8">
    <div class="card shadow-sm h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span>Daily Headcount</span>
            <small class="text-muted">{{ $this->kpis['window'] ?? '' }}</small>
        </div>
        <div class="card-body">
            <div x-data="dailyHeadcountChart({ initial: @js($this->chartDaily) })" x-init="$nextTick(() => init())" x-on:chart-daily-updated.window="update($event.detail)"
                wire:ignore wire:key="chart-daily">
                <div x-ref="chart"></div>
            </div>
        </div>
    </div>
</div>

@script
    <script>
        Alpine.data('dailyHeadcountChart', ({
            initial
        }) => ({
            chart: null,
            mounted: false,
            payload: initial || {
                categories: [],
                series: [{
                    name: 'On Leave',
                    data: []
                }]
            },
            options() {
                return {
                    chart: {
                        type: 'area',
                        height: 320,
                        toolbar: {
                            show: false
                        }
                    },
                    series: this.payload.series,
                    xaxis: {
                        categories: this.payload.categories
                    },
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        curve: 'smooth',
                        width: 2
                    },
                    fill: {
                        opacity: 0.2
                    },
                    colors: ['#3b82f6'],
                    states: {
                        normal: {
                            filter: {
                                type: 'none'
                            }
                        },
                        hover: {
                            filter: {
                                type: 'none'
                            }
                        },
                        active: {
                            filter: {
                                type: 'none'
                            }
                        }
                    },
                    legend: {
                        show: false
                    },
                    tooltip: {
                        y: {
                            formatter: v => Number(v).toLocaleString()
                        }
                    }
                };
            },
            init() {
                if (this.mounted) return;
                this.mounted = true;
                this.chart = new ApexCharts(this.$refs.chart, this.options());
                this.chart.render();
            },
            update(e) {
                if (!e || !e.payload || !this.chart) return;
                const p = e.payload;
                this.chart.updateOptions({
                    xaxis: {
                        categories: p.categories
                    }
                }, false, true);
                this.chart.updateSeries(p.series, true);
            }
        }));
    </script>
@endscript
