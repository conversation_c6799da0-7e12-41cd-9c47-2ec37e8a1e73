<fieldset class="border rounded p-3">
    <legend class="px-2 text-sm font-semibold">Transition Performers</legend>

    <div class="row mb-3">
        <div class="col-md-4">
            <label class="form-label">Workflow</label>
            <x-form.list-input wire:model.live="selectedWorkflow">
                @foreach ($this->workflowList() as $workflow)
                    <option value="{{ $workflow }}">{{ $workflow }}</option>
                @endforeach
            </x-form.list-input>
        </div>

        <div class="col-md-4">
            <label class="form-label">Action</label>
            <x-form.list-input wire:model="selectedAction">
                @foreach ($this->getActionsForWorkflowProperty() as $actionKey => $actionLabel)
                    <option value="{{ $actionKey }}">{{ $actionLabel }}</option>
                @endforeach
            </x-form.list-input>
        </div>

        <div class="col-md-4">
            <label class="form-label">Employee</label>
            <x-shared.employee-dropdown font-size="13px" :list="$this->performerList" wire:model="selectedEmployee"
                :multiple="true" :required="true" placeholder="Search employee..." />
        </div>
    </div>

    <x-form.button color="success" type="button" wire:click="assignSelectedPerformers">
        {{-- :disabled="!$selectedWorkflow || !$selectedAction || empty($selectedEmployee)"> --}}
        Assign Performer
        </x-form-button>

        {{-- table display --}}
        @if (count($transitionTable))
            <div class="mt-4">
                <table class="table table-bordered table-sm">
                    <thead>
                        <tr>
                            <x-table.heading>Workflow</x-table.heading>
                            <x-table.heading>Action Level</x-table.heading>
                            <x-table.heading>Employees</x-table.heading>
                            <x-table.heading>Action</x-table.heading>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($transitionTable as $assignment)
                            <tr>
                                <x-table.cell>{{ ucfirst(str_replace('_', ' ', $assignment['workflow'])) }}</x-table.cell>
                                <x-table.cell>{{ ucfirst($assignment['action']) }}</x-table.cell>
                                <x-table.cell>
                                    @foreach ($assignment['employees'] as $emp)
                                        {{ $emp['name'] }} <small>[{{ $emp['code'] }}]</small>
                                        @if (!$loop->last)
                                            &nbsp;
                                        @endif
                                    @endforeach
                                </x-table.cell>

                                {{-- Action buttons --}}
                                <x-table.cell>
                                    <x-table.action>
                                        {{-- <x-table.action-option type="edit"
                                        wire:click="editAssignment({{ $assignment['id'] }})" modal-id="#addHoliday" /> --}}
                                        <x-table.action-option icon="trash" type="button" label=" Delete"
                                            wire:click.prevent="deleteAssignment('{{ $assignment['id'] }}')"
                                            wire:confirm="Are you sure to delete this item?" />


                                    </x-table.action>
                                </x-table.cell>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
</fieldset>
