<fieldset>
    <legend>Personal Documents</legend>

    <div class="row">
        <div class="form-group col-md-4">
            <x-form.text-input type="file" wire:model="edfRequisitionForm.profile_picture"
                accept="image/*,application/pdf" label="{{ 'Profile Picture' }}" />

            {{-- Preview --}}
            @if ($edfRequisitionForm->profile_picture)
                @php $filePath = $edfRequisitionForm->profile_picture; @endphp
                <div style="display: flex; gap: 4px;">
                    Attached file:
                    <x-bs5::image-popup src="{{ asset('storage/' . $filePath) }}" alt="Profile Picture" height="auto">
                        <x-slot:trigger>
                            <span style="color: blue; cursor:pointer;">View Pictures</span>
                        </x-slot:trigger>
                    </x-bs5::image-popup>
                    <div>
                        <button
                            style="background-color: red; padding: 2px 4px; font-size: 10px; color: white; border: none; border-radius: 10px;"
                            wire:click="removeProfilePicture">
                            Remove
                        </button>
                    </div>
                </div>
            @endif
        </div>

        @php $docs = $edfRequisitionForm->docs ?? []; @endphp

        {{-- Show all uploaded / added document fields --}}
        @foreach (\App\Http\Helpers\Constant::DOCUMENT_TYPES as $key => $label)
            @if (!empty($docs[$key]) || array_key_exists($key, $docs))
                @php
                    $filePath = $docs[$key] ?? null;
                    $isTemporary = $filePath instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
                    $isPdf = $filePath && Str::endsWith(strtolower($filePath), '.pdf');
                @endphp

                <div class="form-group col-md-4" wire:key="doc-{{ $key }}">
                    <x-form.text-input type="file" wire:model="edfRequisitionForm.docs.{{ $key }}"
                        accept="image/*,application/pdf" :label="$label" />

                    @if ($filePath && !$isTemporary)
                        <div style="display: flex; gap: 4px;">
                            Attached file:
                            @if ($isPdf)
                                <a href="{{ asset('storage/' . $filePath) }}" target="_blank" style="color: blue;">View
                                    PDF</a>
                            @else
                                @if(!empty($docs[$key]) && !empty($filePath))
                                    <x-bs5::image-popup src="{{ asset('storage/' . $filePath) }}" alt="{{ $label }}" height="auto">
                                        <x-slot:trigger>
                                            <span style="color: blue; cursor:pointer;">View Picture</span>
                                        </x-slot:trigger>
                                    </x-bs5::image-popup>
                                @endif
                            @endif

                            <button type="button"
                                style="background-color: red; padding: 2px 6px; font-size: 10px; color: white; border: none; border-radius: 10px;"
                                wire:click="removeDocument('{{ $key }}')">
                                Remove
                            </button>
                        </div>
                    @endif
                </div>
            @endif
        @endforeach

        {{-- View Missing Documents Button --}}
        <div class="form-group col-md-12">
            <div class="d-flex flex-wrap gap-2">
                <x-form.button size="sm" color="info" icon="eye" icon_color="white" type="button"
                    data-bs-toggle="modal" data-bs-target="#missingDocumentsModal" value="View Missing Documents" />
            </div>
        </div>
    </div>

    {{-- Missing Documents Modal --}}
    <x-modal :title="'Missing Documents'" id="missingDocumentsModal" size="lg">
        <x-slot name="body">
            @php
                $missingDocs = collect(\App\Http\Helpers\Constant::DOCUMENT_TYPES)->filter(
                    fn($label, $key) => !array_key_exists($key, $docs),
                );
            @endphp

            @if ($missingDocs->isEmpty())
                <p class="text-success mb-0">✅ All personal documents are uploaded.</p>
            @else
                <ul class="list-group">
                    @foreach ($missingDocs as $key => $label)
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {{ $label }}
                            <button type="button" class="btn btn-sm btn-success text-white rounded-circle "
                            style="height: 30px; width: 30px;"
                                wire:click="addDocument('{{ $key }}')">
                                +
                            </button>
                        </li>
                    @endforeach
                </ul>
            @endif
        </x-slot>
    </x-modal>
</fieldset>
