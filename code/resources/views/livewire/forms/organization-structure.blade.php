<div>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <x-title size="lg" icon="bi bi-diagram-3">Organization Tree</x-title>
        <div class="d-flex justify-content-end p-2">
            <div style="width: 280px;">
                <x-form.multi-select id="employee-dropdown" placeholder="Select Employee" name="employeeId" label="Employee"
                    wire:model.live="employeeId" :multiple="false">
                    @foreach ($this->employeeList as $key => $value)
                        <option value="{{ $value['value'] }}" @if ((string) $employeeId === (string) $value['value']) selected @endif>
                            {{ $value['label'] }}
                        </option>
                    @endforeach
                </x-form.multi-select>
            </div>
        </div>
    </div>
    
    @livewire('shared.organization-tree', ['employeeId' => $employeeId])

</div>
