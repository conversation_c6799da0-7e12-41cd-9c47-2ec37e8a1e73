<div>
    <div class="py-1 d-flex gap-4">
        <x-title icon="phone">Employee's Device List</x-title>
        <x-form.button size="sm" icon="stack" icon_color="white" type="button">
            <a href="{{ route('deviceLog') }}">Employee List</a>
        </x-form.button>
    </div>
    <section class="card card-body shadow-sm">
        <x-table.table-wrapper :addCard="false">
            <x-slot name="settings" placeholder="Search by name" :search="false" :pageLimit="true">
                <x-shared.employee-dropdown font-size="120px" :list="$this->employeeList" wire:model='employee_ids'
                    :multiple="true" placeholder="Search From Employee" />
                <x-form.list-input wire:model.live.debounce.300ms="status" name="status" :addEmptyOption="false">
                    @foreach (['active', 'inactive', 'pending', 'rejected'] as $status)
                        <option value="{{ $status }}">{{ ucfirst($status) }}</option>
                    @endforeach
                </x-form.list-input>
            </x-slot>
            <x-slot name="header">
                <x-table.heading>Employee Name</x-table.heading>
                <x-table.heading>Brand</x-table.heading>
                <x-table.heading>Platform</x-table.heading>
                <x-table.heading>App Version</x-table.heading>
                <x-table.heading>Status</x-table.heading>
                <x-table.heading>Action</x-table.heading>
            </x-slot>
            <x-slot name="body">
                @foreach ($devices as $device)
                    @php
                        $badgeColor = match ($device->status) {
                            'active' => 'success',
                            'inactive' => 'danger',
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            default => 'secondary',
                        };
                        $tableColor = match ($device->status) {
                            'active' => 'table-success',
                            'inactive' => 'table-danger',
                            'pending' => 'table-warning',
                            'rejected' => 'table-danger',
                            default => 'table-secondary',
                        };
                    @endphp
                    <tr class="{{ $tableColor }}">
                        <x-table.cell>
                            {{ $device->user->employee->name ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            {{ $device->device_brand ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            {{ $device->device_platform ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            {{ $device->app_version ?? 'N/A' }}
                        </x-table.cell>
                        <x-table.cell>
                            <span class="badge bg-{{ $badgeColor }}">
                                {{ ucfirst($device->status) }}
                            </span>
                        </x-table.cell>
                        <x-table.cell>
                            <x-table.action>
                                @if ($device->status == 'pending' && auth()->user()->can(PermissionList::APP_LOGIN_DEVICE_REJECT))
                                    <x-table.action-option icon="trash" label="Reject" color="danger"
                                        wire:confirm='Are you sure you want to reject this device?'
                                        wire:click="reject({{ $device->id }})" />
                                @endif
                                @if ($device->status == 'pending' && auth()->user()->can(PermissionList::APP_LOGIN_DEVICE_APPROVE))
                                    <x-table.action-option icon="check" label="Approve" color="success"
                                        wire:confirm='Are you sure you want to approve this device?'
                                        wire:click="approve({{ $device->id }})" />
                                @endif
                                <x-table.action-option icon="eye" label="View" color="info"
                                    wire:click="view({{ $device->id }})" />
                            </x-table.action>
                        </x-table.cell>
                    </tr>
                @endforeach
            </x-slot>
        </x-table.table-wrapper>
    </section>
</div>
