<div>
    <div class="py-1 d-flex gap-2 align-items-center">
        <x-title icon="person-fill-gear">Oauth2 Client Management</x-title>
        <x-form.button size="sm" icon="plus-circle" icon_color="white" type="button" data-bs-toggle="modal"
            data-bs-target="#addEditClient" value="Add Client" />
    </div>

    @if (session()->has('message'))
        <div class="alert alert-warning">{{ session('message') }}</div>
    @endif

    <x-table.table-wrapper loading-target="activate, revoke, delete">
        <x-slot name="settings" placeholder="Search by name" :search="true" :pageLimit="true"></x-slot>

        <x-slot name="header">
            <x-table.heading>Name</x-table.heading>
            <x-table.heading>Status</x-table.heading>
            <x-table.heading>Public (PKCE)</x-table.heading>
            <x-table.heading>Password</x-table.heading>
            <x-table.heading>Action</x-table.heading>
        </x-slot>

        <x-slot name="body">
            @foreach ($this->list as $item)
                <tr>
                    <x-table.cell>{{ $item->name }}</x-table.cell>
                    <x-table.cell>
                        @if ($item->revoked)
                            <span class="badge px-2 bg-danger"
                                style="font-size:10px; padding-top:4px; padding-bottom:5px;">Revoked</span>
                        @else
                            <span class="badge px-2 bg-success"
                                style="font-size:10px; padding-top:4px; padding-bottom:5px;">Active</span>
                        @endif
                    </x-table.cell>

                    {{-- Public --}}
                    <x-table.cell>
                        @php $isPublic = is_null($item->secret); @endphp
                        <span class="badge {{ $isPublic ? 'bg-success' : 'bg-danger' }}">
                            {{ $isPublic ? 'Yes' : 'No' }}
                        </span>
                    </x-table.cell>

                    {{-- Password client --}}
                    <x-table.cell>
                        <span class="badge {{ $item->password_client ?? false ? 'bg-success' : 'bg-danger' }}">
                            {{ $item->password_client ?? false ? 'Yes' : 'No' }}
                        </span>
                    </x-table.cell>

                    <x-table.cell>
                        <x-table.action>
                            @if ($item->revoked)
                                <x-table.action-option icon="person-fill-gear" label="Activate"
                                    wire:confirm='Are you sure to activate this client?'
                                    wire:click="activate('{{ $item->id }}')" />
                            @else
                                <x-table.action-option icon="person-fill-gear" label="Revoke"
                                    wire:confirm='Are you sure to revoke this client?'
                                    wire:click="revoke('{{ $item->id }}')" />
                            @endif

                            <x-table.action-option type="edit" data-bs-toggle="modal" data-bs-target="#addEditClient"
                                wire:click="editClient('{{ $item->id }}')" label="Edit" />

                            <x-table.action-option type="show" data-bs-toggle="modal" data-bs-target="#clientDetail"
                                wire:click="viewDetail('{{ $item->id }}')" label="View Detail" />

                            <x-table.action-option type="delete" wire:confirm='Are you sure to delete this client?'
                                wire:click="delete('{{ $item->id }}')" />
                        </x-table.action>
                    </x-table.cell>
                </tr>
            @endforeach
        </x-slot>

        <x-slot name="pagination">{{ $this->list->links() }}</x-slot>
    </x-table.table-wrapper>

    {{-- Details Modal --}}
    <x-modal title="Client Details" id="clientDetail" size="lg" :has-footer="false">
        <x-slot name="body">
            <div wire:loading wire:target='viewDetail'>
                <x-loading size="40px" border-width="3px" />
            </div>

            @if (count($details))
                @php
                    $detailRedirects = explode(',', $details['redirect']);
                @endphp

                <div class="d-flex flex-column gap-2">
                    <div class="d-flex gap-2"><b style="min-width:140px;">Name:</b> <span>{{ $details['name'] }}</span>
                    </div>
                    <div class="d-flex gap-2"><b style="min-width:140px;">Client Id:</b>
                        <span>{{ $details['id'] }}</span>
                    </div>

                    <div class="d-flex gap-2">
                        <b style="min-width:140px;">Redirects:</b>
                        <div class="d-flex flex-column">
                            @forelse ($detailRedirects as $u)
                                <code class="mb-1">{{ $u }}</code>
                            @empty
                                <span class="text-muted">None</span>
                            @endforelse
                        </div>
                    </div>

                    <div class="d-flex gap-2"><b style="min-width:140px;">Secret:</b>
                        <span>{{ $details['secret'] ?? '—' }}</span>
                    </div>
                    <div class="d-flex gap-2">
                        <b style="min-width:140px;">Public (PKCE):</b>
                        <span>{{ empty($details['secret']) ? 'Yes' : 'No' }}</span>
                    </div>
                    <div class="d-flex gap-2">
                        <b style="min-width:140px;">Password Client:</b>
                        <span>{{ $details['password_client'] ?? false ? 'Yes' : 'No' }}</span>
                    </div>

                    <div class="d-flex gap-2">
                        <b style="min-width:140px;">Scopes:</b>
                        <span>
                            @php $clientScopes = $details['scopes'] ?? []; @endphp
                            @if (count($clientScopes))
                                @foreach ($clientScopes as $s)
                                    <span class="badge bg-primary me-1">{{ $s }}</span>
                                @endforeach
                            @else
                                <span class="text-muted">None</span>
                            @endif
                        </span>
                    </div>
                </div>
            @endif
        </x-slot>
    </x-modal>

    {{-- Add/Edit Modal (FULL SCREEN) --}}
    <form wire:submit="save()">
        <x-modal title="{{ $editingId ? 'Edit Oauth2 Client' : 'Add Oauth2 Client' }}" id="addEditClient"
            size="xl">
            <x-slot name="body">
                <div wire:loading wire:target='editClient'>
                    <x-loading size="40px" border-width="3px" />
                </div>

                <div class="row g-4">
                    {{-- Left column: fields --}}
                    <div class="col-12 col-lg-5">
                        <x-form.text-input name="name" label="Name" :required="true" />

                        @if (!$editingId)
                            <div class="d-flex gap-4 mt-3">
                                <label class="d-inline-flex align-items-center gap-2">
                                    <input type="checkbox" class="form-check-input" wire:model="public">
                                    <span>Public (PKCE)</span>
                                </label>

                                <label class="d-inline-flex align-items-center gap-2">
                                    <input type="checkbox" class="form-check-input" wire:model="passwordClient"
                                        @if ($public) disabled @endif>
                                    <span>Password Client</span>
                                </label>
                            </div>
                            <small class="text-muted d-block mt-1">
                                Public clients have no secret (PKCE). Password grant is disabled for public clients.
                            </small>
                        @endif

                        {{-- Multiple Redirects --}}
                        <div class="mt-4">
                            <x-form.input-label value="Redirect URIs" />
                            @foreach ($redirects as $i => $uri)
                                <div class="mb-2">
                                    <div class="d-flex align-items-center gap-2">
                                        <input type="text"
                                            class="form-control @error('redirects.' . $i) is-invalid @enderror"
                                            wire:model.defer="redirects.{{ $i }}"
                                            placeholder="https://your-app.com/oauth/callback">
                                        <button type="button" class="btn btn-outline-danger"
                                            wire:click="removeRedirect({{ $i }})"
                                            @if (count($redirects) === 1) disabled @endif>
                                            Remove
                                        </button>
                                    </div>
                                    @error('redirects.' . $i)
                                        <span class="text-danger small">{{ $message }}</span>
                                    @enderror
                                </div>
                            @endforeach
                            <button type="button" class="btn btn-outline-primary btn-sm mt-1"
                                wire:click="addRedirect">
                                + Add More Redirect
                            </button>
                        </div>
                    </div>

                    {{-- Right column: scopes --}}
                    <div class="col-12 col-lg-7">
                        <div class="mb-2">
                            <x-form.input-label value="Scopes" />
                        </div>

                        @foreach ($this->availableScopes as $groupKey => $groupScopes)
                            <div class="card mb-3">
                                <div class="card-header py-2 fw-semibold">
                                    {{ $groupKey }}
                                </div>
                                <div class="card-body">
                                    <div class="row row-cols-1 row-cols-md-2 g-2">
                                        @foreach ($groupScopes as $id => $description)
                                            <div class="col">
                                                <label
                                                    class="w-100 p-3 border rounded-3 d-flex align-items-start gap-3"
                                                    style="cursor:pointer;">
                                                    <input type="checkbox" class="form-check-input mt-1"
                                                        wire:model="scopes" value="{{ $id }}"
                                                        aria-label="Scope {{ $id }}">
                                                    <div class="flex-grow-1">
                                                        <div class="fw-medium">{{ $id }}</div>
                                                        <div class="text-muted small">{{ $description }}</div>
                                                    </div>
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        @error('scopes')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </x-slot>

            <x-slot name="footer">
                <x-form.button type="submit" class="btn btn-success text-white" wire:target='save,editClient'>
                    {{ $editingId ? 'Update' : 'Save' }}
                </x-form.button>
            </x-slot>
        </x-modal>
    </form>
</div>
