@php
    use Carbon\Carbon;
@endphp
<div>
    <li class="nav-item dropdown ms-3">
        <a class="nav-link dropdown-toggle pt-1 px-0 position-relative" href="#" role="button"
            data-bs-toggle="dropdown" aria-expanded="false" aria-label="Work Anniversaries">
            <div class="icon-wrapper">
                <i class="bi bi-briefcase-fill anniversary-icon"></i>

                @if ($anniversaryCount > 0)
                    <span class="position-absolute top-90 right-100 badge rounded-pill bg-danger icon-badge">
                        {{ $anniversaryCount > 9 ? '9+' : $anniversaryCount }}
                    </span>
                @endif
            </div>
        </a>

        <div class="dropdown-menu dropdown-menu-end mt-2 py-1" style="min-width: 320px; transition:none;">
            <div class="anniversary-scroll">

                {{-- TODAY'S ANNIVERSARIES --}}
                @php
                    $todayLabel = \Carbon\Carbon::today()->format('M d, Y');
                @endphp

                <h6 class="dropdown-header fw-bold">
                    🎉 Work Anniversaries Today <span class="text-muted">({{ $todayLabel }})</span>
                </h6>

                @forelse($anniversariesToday as $department => $employees)
                    <div class="dropdown-item">
                        <strong class="d-block mb-1">{{ $department }}</strong>
                        <ul class="list-unstyled ms-2 mb-0">
                            @foreach ($employees as $emp)
                                {{-- Only show employees with 1+ years completed --}}
                                @if ($emp['years_completed'] >= 1)
                                    <li class="text-blue">
                                        🏆
                                        <a href="{{ route('employee-search-profile', $emp['id']) }}"
                                            class="text-decoration-none text-blue">
                                            {{ trim($emp['first_name'] . ' ' . ($emp['middle_name'] ?? '') . ' ' . $emp['last_name']) }}
                                        </a>
                                        <small class="text-muted">
                                            — {{ $emp['years_completed'] }}
                                            {{ \Illuminate\Support\Str::plural('year', $emp['years_completed']) }} 🎉
                                        </small>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @empty
                    <span class="dropdown-item text-muted">No anniversaries today 🎂</span>
                @endforelse

                <div class="dropdown-divider"></div>

                {{-- UPCOMING ANNIVERSARIES --}}
                @php
                    $upcomingCount = collect($upcomingAnniversaries)->flatten(1)->count();
                    $formatLabel = fn($date) => Carbon::parse($date)->format('M d, Y');
                @endphp

                <h6 class="dropdown-header fw-bold">
                    📅 Upcoming Anniversaries (next {{ $upcomingWindowDays }} days)
                    @if ($upcomingCount > 0)
                        <span class="badge bg-secondary ms-1">{{ $upcomingCount }}</span>
                    @endif
                </h6>

                @forelse($upcomingAnniversaries as $date => $people)
                    <div class="dropdown-item">
                        <strong>
                            {{ $formatLabel($date) }}
                            <small class="text-green">
                                @php
                                    $diff = Carbon::today()->diffInDays(Carbon::parse($date), false);
                                @endphp
                                — in {{ $diff }} {{ \Illuminate\Support\Str::plural('day', $diff) }}
                            </small>
                        </strong>
                        <ul class="list-unstyled ms-2 mb-0">
                            @foreach ($people as $p)
                                {{-- Only show employees with 1+ years completed --}}
                                @if ($p['years_completed'] >= 1)
                                    <li class="text-blue">
                                        <a href="{{ route('employee-search-profile', $p['id']) }}"
                                            class="text-decoration-none text-blue">
                                            {{ trim($p['first_name'] . ' ' . ($p['middle_name'] ?? '') . ' ' . $p['last_name']) }}
                                        </a>
                                        <small class="text-muted">
                                            ({{ $p['department'] }})
                                            — {{ $p['years_completed'] }}
                                            {{ \Illuminate\Support\Str::plural('year', $p['years_completed']) }}
                                        </small>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @empty
                    <span class="dropdown-item text-muted">No upcoming anniversaries 🎈</span>
                @endforelse
            </div>
        </div>
    </li>

    {{-- Styles --}}
    <style>
        .anniversary-icon {
            font-size: 22px;
            color: #010e24;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, .15));
            transition: transform .15s ease, filter .15s ease;
            margin-top: 4px;
            display: inline-block;
        }

        .nav-link:hover .anniversary-icon {
            transform: translateY(-1px);
            filter: drop-shadow(0 2px 2px rgba(0, 0, 0, .18));
        }

        .navbar-nav .nav-link .anniversary-icon {
            font-size: 1.5rem;
            color: #010e24;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, .15));
            transition: transform .15s ease, filter .15s ease;
        }

        .anniversary-scroll {
            max-height: 500px;
            overflow-y: auto;
        }

        .anniversary-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .anniversary-scroll::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, .2);
            border-radius: 3px;
        }
    </style>
</div>
