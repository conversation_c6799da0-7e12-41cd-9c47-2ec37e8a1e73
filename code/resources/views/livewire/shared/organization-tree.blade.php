<div class="wrapper">
    <div class="card" style="height:100vh; overflow-y: scroll">
        <div class="container-fluid org-structure">

            <x-full-page-loading loading-target="selectEmployee" />

            @if (empty($employeeId) || (count($supervisors) === 0 && count($currentEmployee) === 0 && count($subordinates) === 0))
                <div class="d-flex justify-content-center align-items-center py-5">
                    <div class="card border-0 text-center no-data-card">
                        <div class="d-flex justify-content-center align-items-center mt-3 opacity-60">
                            <p class="fs-3 fw-bold">No Data Found!!!</p>
                        </div>
                    </div>
                </div>
            @else
                @if (count($supervisors) > 0)
                    <div class="row justify-content-center mb-1 supervisor-row position-relative">
                        <div class="col-12 d-flex justify-content-center position-relative">
                            @foreach ($supervisors as $employee)
                                <div class="employee-node supervisor-node position-relative mx-2">
                                    <x-employee-card :employee="$employee" />
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div
                    class="row justify-content-center mb-1 current-employee-row position-relative {{ count($subordinates ?? []) > 0 ? '' : 'no-subs' }}">
                    <div class="col-12 d-flex justify-content-center position-relative">
                        @foreach ($currentEmployee as $employee)
                            <div class="employee-node current-node position-relative mx-2">
                                <x-employee-card :employee="$employee" />
                            </div>
                        @endforeach
                    </div>
                </div>

                @if (count($subordinates) > 0)
                    <div class="row justify-content-center mb-1 subordinates-row position-relative">
                        <div class="text-between-lines">
                            <span class="line"></span>
                            <span class="text text-report-line">People reporting to
                                {{ $currentEmployee[0]['employee_name'] ?? '' }}</span>
                            <span class="line"></span>
                        </div>

                        <div class="col-12 position-relative">
                            @if (count($subordinates) === 1)
                                <div class="d-flex justify-content-center">
                                    <div class="employee-node subordinate-node position-relative mx-2">
                                        <x-employee-card :employee="$subordinates[0]" />
                                    </div>
                                </div>
                            @else
                                <div class="row">
                                    @foreach ($subordinates as $employee)
                                        <div class="col-md-12 col-lg-6 mb-3 d-flex justify-content-center">
                                            <div class="employee-node subordinate-node position-relative mx-2">
                                                <x-employee-card :employee="$employee" />
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>

    <link rel="stylesheet" href="{{ asset('vendor/horizon/organizationTree.css') }}">
</div>
