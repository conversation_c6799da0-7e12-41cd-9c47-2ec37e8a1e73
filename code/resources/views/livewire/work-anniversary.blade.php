<div>
    @if ($showPopUp)
        <div class="popup-overlay" data-celebration="anniversary">
            <canvas id="fireworksCanvas"></canvas>
            <canvas id="balloonCanvas"></canvas>
            <canvas id="confettiCanvas"></canvas>

            <!-- Anniversary Flyer -->
            <div class="celebration-flyer anniversary-flyer">
                <button class="close-btn" onclick="closePopup()">✕</button>

                @php
                    $suffix = 'th';
                    if ($yearsCompleted == 1) {
                        $suffix = 'st';
                    } elseif ($yearsCompleted == 2) {
                        $suffix = 'nd';
                    } elseif ($yearsCompleted == 3) {
                        $suffix = 'rd';
                    }
                @endphp
                <div class="anniversary-flex">
                    <h2 class="anniversary-message">

                        Happy {{ $yearsCompleted }}{{ $suffix }} Work Anniversary,
                    </h2><br>
                    <h2 class="anniversary-msg">
                        Dear {{ $userName }}🏆
                    </h2>
                </div>

                <div class="flyer-content">
                    <div class="profile-img-container">
                        <img src="{{ auth()->user()->employee->profilePicture() }}"
                            onerror='this.src = "https://ui-avatars.com/api/?name={{ auth()->user()->employee->name }}&size=120&background=random"'
                            alt="{{ auth()->user()->employee->name }}'s profile picture">
                    </div>

                    <div class="text-container">
                        <p class="highlight-year">
                            Congratulations on completing {{ $yearsCompleted }}
                            {{ \Illuminate\Support\Str::plural('year', $yearsCompleted) }} with us!🏅
                        </p>

                        <p class="highlight-text">

                            Your dedication and hard work have been invaluable to our team.
                            Thank you for your continued commitment and excellence.<br><br>
                            Here's to many more years of success together!
                        </p>
                    </div>
                </div>

                <button class="repeat-btn" onclick="repeatCelebration()">Repeat Celebration 🎊</button>
            </div>
        </div>
    @endif
</div>
