<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HolidayResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $date = strtotime(\Carbon\Carbon::parse($this->holiday->eng_date)->format('Y-m-d'));
        $now = strtotime(\Carbon\Carbon::now()->format('Y-m-d'));
        $diff = $now - $date;
        $message = "";
        if ($diff < 0) {
            $message = 'Upcoming Holiday';
        } elseif ($diff == 0) {
            $message = 'Today';
        } elseif ($diff > 0) {
            $message = 'Past Holiday';
        }
        return [
            "name"      => $this->holiday?->name,
            "eng_date"  => $this->holiday?->eng_date,
            "nep_date"  => $this->holiday?->nep_date,
            "remarks"   => ($this->holiday?->gender == 'All') ? "For All Genders" : "For only " . $this->holiday?->gender,
            "message"   => $message,
        ];
    }
}
