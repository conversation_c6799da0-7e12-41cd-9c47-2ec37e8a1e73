<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VerifierResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $fullName = preg_replace(
            '/\s+/',
            ' ',
            trim("$this->first_name $this->middle_name $this->last_name")
        );

        return [
            'id' => $this->id,
            'name' => "$fullName ($this->employee_code)",
        ];
    }
}
