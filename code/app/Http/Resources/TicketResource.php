<?php

namespace App\Http\Resources;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketResource extends JsonResource
{
    private bool $detailRequired;

    public function __construct($resource, bool $detail = false)
    {
        parent::__construct($resource);
        $this->detailRequired = $detail;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $details =  [
            'workflow'      => $this->workflow,
            'detail'        => $this->getTicketDetails(),
            'employee'      => new EmployeeResource($this->employee),
            'current_owner' => new BasicEmployeeResource($this->currentOwner ?: $this->lastPerformer()),
            'access'        => $this->getTicketAccessList(),
            'actions'        => $this->ticketOwners(),
        ];
        if ($this->detailRequired) {
            $details['documents'] = $this->requestTicket->getDocuments();
            $details['state_history'] = [
                ...$this->stateHistory->map(fn($item) => [
                    'title'     => match ($this->workflow) {
                        WorkflowName::LEAVE_APPROVAL => "leave request",
                        WorkflowName::TIME_REQUEST_APPROVAL => "time request",
                        WorkflowName::TERMINATION_APPROVAL => "employee termination",
                        WorkflowName::REPLACEMENT_LEAVE_REQUEST => "replacement leave request"
                    },
                    'state'     => $item->to,
                    'remarks'   => $item->comment,
                    'time'      => $item->created,
                    'actor'     => $item->actor instanceof Employee ? new BasicEmployeeResource($item->actor) : [
                        'id'                => $item->actor->id ?? "", // Prevent null error if actor missing/terminated
                        'name'              => $item->actor->username ?? "",
                        'employee_code'     => '',
                        'profile_picture'   => '',
                    ],
                    'documents' => $item->documents->map(fn($item) => [
                        'document' => asset("storage/{$item['document']}"),
                        'extension' => last(explode(".", $item['document']))
                    ])
                ]),
                ...(!in_array($this->state, ArflowHelper::getFinalStates($this->workflow)) ? [
                    [
                        'state' => 'current',
                        'remarks' => '',
                        'time' => '',
                        'actor' => new BasicEmployeeResource($this->currentOwner),
                        'documents' => []
                    ]
                ] : [])
            ];
        }
        return $details;
    }

    private function getTicketDetails()
    {
        $details = [
            'ticket_id' => $this->ticket_id,
            'state'     => $this->state,
        ];

        switch ($this->workflow) {
            case WorkflowName::TIME_REQUEST_APPROVAL:
                return array_merge($details, $this->timeRequestDetail());

            case WorkflowName::LEAVE_APPROVAL:
                return array_merge($details, $this->leaveRequestDetail());
            
            case WorkflowName::TERMINATION_APPROVAL:
                return array_merge($details, $this->terminationRequestDetail());

            case WorkflowName::REPLACEMENT_LEAVE_REQUEST:
                return array_merge($details, $this->replacementRequestDetail());

            default:
                throw new \Exception("Workflow not implemented");
        }
    }

    private function getTicketAccessList()
    {
        $accesses = [
            'approve'   => $this->canApprove(),
            'cancel'    => $this->canCancel(),
            'edit'      => $this->canEdit(),
            'assign'    => $this->canAssign(),
        ];
        if ($this->detailRequired) {
            $accesses = array_merge($accesses, [
                'change_owner'      => $this->canChangeNextOwner(),
                // 'escalate'          => $this->canEscalate(),
                // 'recall'            => $this->canRecall(),
                'reject'            => $this->canReject(),
                // 'reopen'            => $this->canReopen(),
                // 'return'            => $this->canReturn(),
                'revert'            => $this->canRevert(),
                // 'review'            => $this->canReview(),
                // 'send_for_review'   => $this->canSendForReview(),
                'verify'            => $this->canVerify(),
            ]);
        }
        return $accesses;
    }

    private function timeRequestDetail()
    {
        $detail = [
            'title'         => 'Time Request',
            'applied_on'    => $this->nep_applied_date,
            'date'          => $this->nep_date,
            'device_in'     => $this->device_in ?? "N/A",
            'requested_in'  => $this->in_time ?? "N/A",
            'in_note'       => $this->in_note ?? "N/A",
            'device_out'    => $this->device_out ?? "N/A",
            'requested_out' => $this->out_time ?? "N/A",
            'out_note'      => $this->out_note ?? "N/A",
        ];

        if ($this->detailRequired) {
            $detail = array_merge($detail, [
                'time_request_count'   => $this->leaveCount,
            ]);
        }
        return $detail;
    }

    private function leaveRequestDetail()
    {
        $detail = [
            'applied_on'        => $this->nep_applied_date,
            'start_date'        => $this->nep_start_date,
            'end_date'          => $this->nep_end_date,
            'title'             => $this->leaveType->name ?? "N/A",
            'subtitle'          => $this->leaveOption->name ?? "N/A",
            'leave_type'        => $this->leaveType->name ?? "N/A",
            'leave_option'      => $this->leaveOption->name ?? "N/A",
            'num_days'          => $this->num_days,
            'applied_status'    => $this->applied_status,
            'remarks'           => $this->remarks,
        ];
        if ($this->detailRequired) {
            $detail = array_merge($detail, [
                'leave_count'   => $this->leaveCount,
                'leave_balance' => $this->leaveBalance?->map(fn($item) => [
                    "name"              => $item['name'],
                    "assigned_leave"    => $item['assigned_leave'],
                    "leave_taken"       => $item['leave_taken'],
                    "pending_leave"     => $item['pending_leave'],
                ])->values() ?? [],
            ]);
            if($this->leaveType->name === Constant::REPLACEMENT_LEAVE_NAME) {
                if(fedexHrm()) {
                    $detail['replaced_date_start'] = $this->nep_replaced_start_date ?? '';
                    $detail['replaced_date_end'] = $this->nep_replaced_end_date ?? '';
                } else {
                    $detail['replaced_dates'] = $this->replaced_dates ?? [];
                }
            }
        }
        return $detail;
    }

    private function terminationRequestDetail()
    {
        $detail = [
            'requested_on'      => $this->nep_termination_request_date,
            'termination_on'    =>$this->nep_termination_date,
            'termination_type'  =>$this->termination_type,
            'remarks'           => $this->termination_reason,
        ];
       
        return $detail;
    } 

    private function replacementRequestDetail()
    {
        $detail = [
            'request_type'  => 'Replacement Leave Request',
            'num_days'      => $this->num_days,
            'start_date'    =>$this->nep_start_date,
            'end_date'      =>$this->nep_end_date,
            'requested_on'  =>$this->nep_applied_date,
            'remarks'       =>$this->remark,
        ];

        return $detail;
    }

    private function ticketOwners()
    {
        $default = [
            'owners'    => [],
            'remarks'   => 'required'
        ];
        $toReturn = [
            'approve' => [
                'owners'    => [],
                'remarks'   => 'required'
            ],
            'cancel' => [
                'owners'    => [],
                'remarks'   => 'not-required'
            ],
            'assign' => [
                'owners'    => [],
                'remarks'   => 'not-required'
            ],
        ];
        if ($this->detailRequired) {
            $toReturn = array_merge($toReturn, [
                'change_owner' => [
                    'owners'    => BasicEmployeeResource::collection($this->ownersForChanging ?? []),
                    'remarks'   => 'not-required'
                ],
                // 'escalate' => [
                //     'owners'    => BasicEmployeeResource::collection($this->ownersForEscalation ?? []),
                //     'remarks'   => 'required'
                // ],
                // 'recall' => $default,
                'reject' => $default,
                // 'reopen' => $default,
                // 'return' => [
                //     'owners'    => BasicEmployeeResource::collection($this->previousOwners ?? []),
                //     'remarks'   => 'required'
                // ],
                'revert'            => $default,
                // 'review'            => $default,
                // 'send_for_review'   => $default,
                'verify' => [
                    'owners'    => BasicEmployeeResource::collection($this->nextOwners ?? []),
                    'remarks'   => 'required'
                ],
            ]);
        }
        return $toReturn;
    }
}
