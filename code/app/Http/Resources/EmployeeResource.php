<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeeResource extends JsonResource
{
    private bool $detailRequired;

    public function __construct($resource, bool $detail = true)
    {
        parent::__construct($resource);
        $this->detailRequired = $detail;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $result = [
            'name'              => $this->name,
            'employee_code'     => $this->employeeCode,
            'profile_picture'   => $this->profile_picture ? asset('storage/' . $this->profile_picture) : '',
        ];

        if ($this->detailRequired) {
            $result = [
                ...$result,
                'department'    => optional($this->organizationInfo?->department)->name ?? "",
                'branch'        => optional($this->organizationInfo?->branch)->name ?? "",

            ];
        }

        return $result;
    }
}
