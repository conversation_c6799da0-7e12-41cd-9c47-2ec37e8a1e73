<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class TicketResourceCollection extends ResourceCollection
{
    private bool $detailRequired;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data'          => $this->collection->map(fn($ticket) => new TicketResource($ticket, false)),
            'current_page'  => $this->resource->currentPage(),
            'per_page'      => $this->resource->perPage(),
            'total'         => $this->resource->total(),
            'last_page'     => $this->resource->lastPage()
        ];
    }
}
