<?php

namespace App\Http\Middleware;

use App\Http\Helpers\ApiResponseHelper;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BasicAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->getUser();
        $password = $request->getPassword();

        if (($user == config('app.basic_auth_user')) && ($password == config('app.basic_auth_password'))) {
            $request->attributes->set('basic_auth_user', $user);
            return $next($request);
        } else {
            $response = new \App\Http\Helpers\ApiResponseHelper;
            $return = $response->unauthorized('Authentication failed');
            return response()->json($return, $return['statusCode']);
            // return ApiResponseHelper::unauthorized('Authentication failed');
        }
    }
}
