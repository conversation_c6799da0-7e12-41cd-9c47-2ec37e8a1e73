<?php

namespace App\Http\Middleware;

use App\Http\Helpers\Constant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckPasswordChangeTime
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $route = $request->route()->uri;
        if (preg_match("/^livewire/", $route) || preg_match("/^sanctum/", $route) || preg_match("/^_ignition/", $route)) {
            return $next($request);
        }

        if (session('iops_login'))
            return $next($request);

        if (session('user_id_main')) {
            return $next($request);
        }

        if (auth()->user()?->hasRole(Constant::ROLE_SUPER_ADMIN)) {
            return $next($request);
        }

        if (!session(Constant::SESSION_HAS_PARENT_COMPANY)) {
            return $next($request);
        }

        $exceptionRoutes = [
            route('change-password')
        ];

        if (in_array($request->url(), $exceptionRoutes)) {
            return $next($request);
        }

        if (!Auth::check()) {
            return $next($request);
        }
        if (session(Constant::SESSION_PASSWORD_CHANGE_TIME)) {
            return redirect(route('change-password'));
        }

        return $next($request);
    }
}
