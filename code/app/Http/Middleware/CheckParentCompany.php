<?php

namespace App\Http\Middleware;

use App\Http\Helpers\Constant;
use App\Models\configs\Company;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckParentCompany
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $permissionExceptionRoutes = [
            "/",
            "login",
            "forgot-password",
            "confirm-password",
            "verify-email",
            "verify-email/{id}/{hash}"
        ];
        $route = $request->route()->uri;
        if (preg_match("/^livewire/", $route) || preg_match("/^sanctum/", $route) || preg_match("/^_ignition/", $route)) {
            return $next($request);
        }

        if (in_array($route, $permissionExceptionRoutes)) {
            return $next($request);
        }

        if (!Auth::check()) {
            return $next($request);
        } else if (!session(Constant::SESSION_USER_ID)) {
            // Reinitialize session data for the user who has remember_token.
            $user = Auth::user();
            session([
                Constant::SESSION_USER_ID => $user->id,
                Constant::SESSION_EMPLOYEE_ID => $user->employee?->id,
                Constant::SESSION_COMPANY => $user->employee?->company ?? Company::where("is_parent", "Y")->first(),
                Constant::SESSION_CURRENT_FISCAL_YEAR => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first(),
                Constant::SESSION_EMPLOYEE => $user->employee,
            ]);
        }

        if ($request->route()->getName() === "addCompany") {
            return $next($request);
        }

        if (session(Constant::SESSION_HAS_PARENT_COMPANY)) {
            if ($request->route()->getName() === "fiscalyear") {
                return $next($request);
            }
            $fiscalYear = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
            if (!$fiscalYear) {
                session()->flash("globalMessage", "Please add fiscal year first");
                return redirect(route('fiscalyear'));
            }
            return $next($request);
        }

        $parentCompany = Company::where("is_parent", "Y")->first();
        if (!$parentCompany) {
            session()->flash("globalMessage", "Please create company first");
            session([
                Constant::SESSION_HAS_PARENT_COMPANY => false,
                Constant::SESSION_PARENT_COMPANY_ID => null
            ]);
            return redirect(route('addCompany'));
        }

        session([
            Constant::SESSION_HAS_PARENT_COMPANY => true,
            Constant::SESSION_PARENT_COMPANY_ID => $parentCompany->id
        ]);

        return $next($request);
    }
}
