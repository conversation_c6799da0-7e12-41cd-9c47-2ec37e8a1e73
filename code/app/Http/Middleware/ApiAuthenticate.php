<?php

namespace App\Http\Middleware;

use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\DeviceLogRepository;
use App\Models\Device\DeviceDetails;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use PermissionList;
use Symfony\Component\HttpFoundation\Response;
use Tymon\JWTAuth\Facades\JWTAuth;

class ApiAuthenticate
{
    // private DeviceLogRepository $deviceLogRepo;
    // public function __construct()
    // {
    //     $this->deviceLogRepo = new DeviceLogRepository();
    // }
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();

        if (!$token)
            return ApiResponse::unauthorized("No Token Provided");


        // Check if the token is valid
        try {
            $user = JWTAuth::parseToken()->authenticate();
            $request->user = $user;
        } catch (\Exception $e) {
            logError('Api auth error details', $e);
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return ApiResponse::unauthorized("Invalid token");
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return ApiResponse::unauthorized("Expired token");
            } else {
                return ApiResponse::unauthorized("Authorization Token not found");
            }
        }

        if (!$user)
            return ApiResponse::unauthorized("Invalid or expired token");

        $checkParams = [
            "device"  => $request->header()['user-device'],
            "user_id" => $user->id,
        ];
        $deviceActive = $this->isDeviceActive($checkParams);
        if (!$deviceActive)
            return ApiResponse::unauthorized("Invalid device details.");

        // need to ask mobile developer first
        // $this->updateDeviceActivity($request);
        /*if (!$user->can(PermissionList::APP_LOGIN_ALLOW)) {
            return ApiResponse::unauthorized("Insufficient permissions");
        }*/
        return $next($request);
    }

    // private function updateDeviceActivity(Request $request)
    // {
    //     $headers = $request->header();
    //     $jwtPayload = \Tymon\JWTAuth\Facades\JWTAuth::parseToken()->getPayload();
    //     $params = [
    //         "user_id"           => $jwtPayload->get('user_id'),
    //         "username"          => $jwtPayload->get("username"),
    //         "app_login_code"    => $jwtPayload->get("app_login_code"),
    //         "token"             => $request->bearerToken(),
    //         "agent"             => $headers['user-agent'][0],
    //         "device"            => $headers['user-device'][0],
    //         "device_brand"      => $headers['user-device-brand'][0],
    //         "device_platform"   => $headers['user-device-platform'][0],
    //         "device_version"    => $headers['user-device-build'][0],
    //         "device_model"      => $headers['user-device-model'][0],
    //         "app_version"       => $headers['user-device-app-version'][0],
    //         "app_version_code"  => $headers['user-device-app-version-code'][0],
    //     ];

    //     $this->deviceLogRepo->updateDeviceActivity($params);
    // }

    private function isDeviceActive($params)
    {
        return DeviceDetails::where([
            "user_id"   => $params['user_id'],
            "device"    => $params['device'],
            "status"    => "active",
        ])->exists();
    }
}
