<?php

namespace App\Http\Services;

use App\Exceptions\FirebaseNotificationException;
use Google\Client;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;

class FirebaseNotificationService
{
    protected $messaging;

    public function __construct()
    {
        $firebase = (new Factory)->withServiceAccount(base_path('storage/firebase/yak-hrm-firebase-adminsdk-fbsvc-7e9d7f1144.json'));
        $this->messaging = $firebase->createMessaging();
    }

    public function sendToTopic($topic, $title, $body, $data = [])
    {
        try {
            $message = CloudMessage::new()
                ->withTarget('topic', $topic)
                ->withNotification([
                    'title' => $title,
                    'body'  => $body,
                ])
                ->withData($data);

            return $this->messaging->send($message);
        } catch (\Throwable $e) {
            // logError('FCM Error: ' . $e->getMessage());
            throw new FirebaseNotificationException($e->getMessage());
        }
    }

    public function sendToDevice($title, $body, $deviceToken, $data = [])
    {
        try {
            $message = CloudMessage::new()
                ->withTarget('token', $deviceToken)
                ->withNotification([
                    'title' => $title,
                    'body'  => $body,
                ])
                ->withData($data);

            return $this->messaging->send($message);
        } catch (\Throwable $e) {
            // logError('FCM Device Error: ',  $e);
            throw new FirebaseNotificationException($e->getMessage());
        }
    }
}
