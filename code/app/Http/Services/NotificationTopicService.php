<?php

namespace App\Http\Services;

use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Models\Employee\Employee;
use App\Models\User;

class NotificationTopicService
{
    /**
     * Get the platform from request headers
     *
     * @return string
     */
    public function getPlatformFromHeader()
    {
        $platform = request()->header('User-Device-Platform');

        // Default platform if not specified
        if (!$platform) {
            $platform = 'default';
        }

        return $platform;
    }

    /**
     * Generate a base topic with platform
     *
     * @return string
     */
    public function getBaseTopic($platform = null)
    {
        $projectName = config('app.name');
        $uniqueTopic = app(AppSettingRepositoryInterface::class)->getFirebaseUniqueTopic();

        if (!$platform)
            $platform = $this->getPlatformFromHeader();

        return "yakhrm-{$uniqueTopic}-{$projectName}-{$platform}";
    }

    /**
     * Generate a topic for company level notifications
     *
     * @param int $companyId
     * @return string
     */
    public function getCompanyTopic($companyId, $platform = null)
    {
        return $this->getBaseTopic($platform) . "-company_{$companyId}";
    }

    /**
     * Generate a topic for region level notifications
     *
     * @param int $regionId
     * @return string
     */
    public function getRegionTopic($regionId, $platform = null)
    {
        return $this->getBaseTopic($platform) . "-region_{$regionId}";
    }

    /**
     * Generate a topic for branch level notifications
     *
     * @param int $branchId
     * @return string
     */
    public function getBranchTopic($branchId, $platform = null)
    {
        return $this->getBaseTopic($platform) . "-branch_{$branchId}";
    }

    /**
     * Generate a topic for department level notifications
     *
     * @param int $departmentId
     * @return string
     */
    public function getDepartmentTopic($departmentId, $platform = null)
    {
        return $this->getBaseTopic($platform) . "-department_{$departmentId}";
    }

    public function getEmployeeTopic($employeeId, $platform = null)
    {
        return $this->getBaseTopic($platform) . "-employee_{$employeeId}";
    }

    /**
     * Get all applicable topics for an employee
     *
     * @param object $employee Employee model with relationships
     * @return array
     */
    public function getAllEmployeeTopics($employee, $platform = null)
    {
        $topics = [];
        $topics[] = $this->getBaseTopic($platform);

        if ($employee->company_id) {
            $topics[] = $this->getCompanyTopic($employee->company_id, $platform);
        }

        if ($employee->organizationInfo?->region_id) {
            $topics[] = $this->getRegionTopic($employee->organizationInfo?->region_id, $platform);
        }

        if ($employee->organizationInfo?->branch_id) {
            $topics[] = $this->getBranchTopic($employee->organizationInfo?->branch_id, $platform);
        }

        if ($employee->organizationInfo?->department_id) {
            $topics[] = $this->getDepartmentTopic($employee->organizationInfo?->department_id, $platform);
        }

        $topics[] = $this->getEmployeeTopic($employee->id, $platform);

        return $topics;
    }

    public function notificationTopics($targetType, $targetIds, $platform = null)
    {
        $topics = [];
        foreach ($targetIds as $targetId) {
            switch ($targetType) {
                case 'companies':
                    $topics[] = $this->getCompanyTopic($targetId, $platform);
                    break;
                case 'regions':
                    $topics[] = $this->getRegionTopic($targetId, $platform);
                    break;
                case 'branches':
                    $topics[] = $this->getBranchTopic($targetId, $platform);
                    break;
                case 'departments':
                    $topics[] = $this->getDepartmentTopic($targetId, $platform);
                    break;
                case 'employees':
                    $topics = $this->getEmployeeTopic($targetId, $platform);
                    break;
            }
        }
        return $topics;
    }

    public function getNotificationUsers($targetType, $targetIds)
    {
        $query = Employee::query()->leftJoin('employee_org', 'employees.id', '=', 'employee_org.employee_id');
        switch ($targetType) {
            case 'companies':
                $query->whereIn('company_id', $targetIds);
                break;
            case 'regions':
                $query->whereIn('employee_org.region_id', $targetIds);
                break;
            case 'branches':
                $query->whereIn('employee_org.branch_id', $targetIds);
                break;
            case 'departments':
                $query->whereIn('employee_org.department_id', $targetIds);
                break;
            case 'employees':
                $query->whereIn('employees.id', $targetIds);
                break;
        }
        $userIds =  $query->pluck('employees.user_id')->toArray();
        $users = User::whereIn('id', $userIds)->get();
        return $users;
    }
}
