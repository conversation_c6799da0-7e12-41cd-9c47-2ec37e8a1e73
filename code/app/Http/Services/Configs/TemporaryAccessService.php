<?php

namespace App\Http\Services\Configs;

use App\Http\Repositories\Configs\Interfaces\TemporaryAccessRepositoryInterface;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Config\TemporaryAccess;
use App\Models\Employee\Employee;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TemporaryAccessService
{
    public function __construct(
        protected TemporaryAccessRepositoryInterface $repository
    ) {}

    public function createTemporaryAccess(array $data): TemporaryAccess
    {
        return $this->repository->create([
            'temporary_employee_id' => $data['temporary_employee_id'],
            'from_employee_id'      => $data['from_employee_id'],
            'from'                  => $data['from'],
            'to'                    => $data['to'],
            'status'                => 'pending',
        ]);
    }

    /**
     * Activate a single temporary access:
     * - copy manager roles (only the ones not already there)
     * - copy manager transition performers (only new ones)
     */
    public function activate(TemporaryAccess $temporaryAccess): void
    {
        if (! $temporaryAccess->canBeActivate()) {
            return;
        }

        DB::transaction(function () use ($temporaryAccess) {
            /** @var Employee $manager */
            $manager = Employee::findOrFail($temporaryAccess->from_employee_id);

            /** @var Employee $tempEmp */
            $tempEmp = Employee::findOrFail($temporaryAccess->temporary_employee_id);

            $managerRoleIds  = $manager->user->roles()->pluck('id')->toArray();
            $tempEmpRoleIds = $tempEmp->user->roles()->pluck('id')->toArray();

            $rolesToAdd = array_values(array_diff($managerRoleIds, $tempEmpRoleIds));

            if (! empty($rolesToAdd)) {
                $tempEmp->user->roles()->attach($rolesToAdd);
            }

            // --- transition performers ---
            // Adjust query to your schema. Here: manager is performer_id
            $managerPerformers = TransitionPerformer::where('performer_id', $manager->id)->get();

            $addedPerformers = [];

            foreach ($managerPerformers as $tp) {
                $exists = TransitionPerformer::query()
                    ->where([
                        'workflow'      => $tp->workflow,
                        'state'         => $tp->state,
                        'performer_id'  => $tempEmp->id,
                        'recipient_id'  => $tp->recipient_id,
                        'level'         => $tp->level,
                    ])
                    ->exists();

                if ($exists) {
                    continue;
                }

                $empExists = Employee::query()->find($tp->recipient_id);
                if (! $empExists) {
                    continue;
                }

                $new = TransitionPerformer::create([
                    'performer_id' => $tempEmp->id,
                    'recipient_id' => $tp->recipient_id,
                    'workflow'     => $tp->workflow,
                    'level'        => $tp->level,
                    'state'        => $tp->state,
                ]);

                $addedPerformers[] = $new->id;
            }

            $temporaryAccess->role_ids = $rolesToAdd;
            $temporaryAccess->transition_performers = $addedPerformers;
            $temporaryAccess->status = 'active';
            $temporaryAccess->save();
            $temporaryAccess->updateActionMeta([
                'activated' => [
                    'user_id' => auth()->id() ?? null,
                    'employee_id' => currentEmployeeId() ?? null,
                    'name' => currentEmployee()?->name ?? 'System',
                    'username' => auth()->user()->username ?? 'System',
                    'employee_code' => currentEmployee()->employeeCode ?? null,
                    'at' => now()->format('Y-m-d H:i:s'),
                ]
            ]);
            Cache::flush();
        });
    }

    /**
     * Expire a single temporary access:
     * - remove roles added during activation
     * - remove transition performer entries created during activation
     */
    public function expire(TemporaryAccess $tempAccess): void
    {
        if (! $tempAccess->canBeExpire()) {
            return;
        }

        DB::transaction(function () use ($tempAccess) {
            /** @var Employee $tempEmployee */
            $tempEmployee = Employee::findOrFail($tempAccess->temporary_employee_id);

            $rolesToRemove = $tempAccess->role_ids ?? [];
            if (! empty($rolesToRemove)) {
                $tempEmployee->user->roles()->detach($rolesToRemove);
            }

            $tpIds = $tempAccess->transition_performers ?? [];
            // $idsToDelete = collect($tpIds)
            //     ->pluck('transition_performer_id')
            //     ->filter()
            //     ->all();


            if (! empty($tpIds)) {
                TransitionPerformer::whereIn('id', $tpIds)->delete();
            }

            $tempAccess->status = 'expired';
            $tempAccess->save();
            $tempAccess->updateActionMeta([
                'revoked' => [
                    'user_id' => auth()->id() ?? null,
                    'employee_id' => currentEmployeeId() ?? null,
                    'name' => currentEmployee()?->name ?? 'System',
                    'username' => auth()->user()->username ?? 'System',
                    'employee_code' => currentEmployee()->employeeCode ?? null,
                    'at' => now()->format('Y-m-d H:i:s'),
                ]
            ]);
            Cache::flush();
        });
    }

    /**
     * Called by CRON: activate pending + expire finished.
     */
    public function runCron(): void
    {
        $toActivate = $this->repository->pendingToActivate();
        foreach ($toActivate as $tempAccess) {
            $this->activate($tempAccess);
        }

        $toExpire = $this->repository->activeToExpire();
        foreach ($toExpire as $tempAccess) {
            $this->expire($tempAccess);
        }

        Artisan::call('cache:clear-all');
    }
}
