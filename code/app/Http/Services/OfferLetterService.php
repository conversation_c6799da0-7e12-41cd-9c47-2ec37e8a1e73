<?php

namespace App\Http\Services;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;

class OfferLetterService
{
    public static function offerLetterFormSchema()
    {
        return [
            [
                "title" => "Personal Information",
                "fields" => [
                    [
                        "key" => "first_name",
                        "label" => "First Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "middle_name",
                        "label" => "Middle Name",
                        "type" => "text",
                        "rules" => "max:100"
                    ],
                    [
                        "key" => "last_name",
                        "label" => "Last Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "gender",
                        "label" => "Gender",
                        "type" => "select",
                        "rules" => "required",
                        "options" => [
                            "male" => "Male",
                            "female" => "Female",
                            "other" => "Other"
                        ]
                    ],
                    [
                        "key" => "marital_status",
                        "label" => "Marital Status",
                        "type" => "select",
                        "rules" => "required",
                        "options" => [
                            "married" => "Married",
                            "unmarried" => "Unmarried"
                        ]
                    ],
                    [
                        "key" => "nationality",
                        "label" => "Nationality",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "citizenship_no",
                        "label" => "Citizenship No",
                        "type" => "text",
                        "rules" => "required|max:50"
                    ],
                    [
                        "key" => "date_of_birth",
                        "label" => "Date of Birth (AD)",
                        "type" => "date",
                        "rules" => "required"
                    ],
                    [
                        "key" => "blood_group",
                        "label" => "Blood Group",
                        "type" => "select",
                        "rules" => "max:10",
                        "options" => [
                            "A+" => "A+",
                            "A-" => "A-",
                            "B+" => "B+",
                            "B-" => "B-",
                            "AB+" => "AB+",
                            "AB-" => "AB-",
                            "O+" => "O+",
                            "O-" => "O-"
                        ]
                    ],
                    [
                        "key" => "pan_no",
                        "label" => "Pan No",
                        "type" => "text",
                        "rules" => "required"
                    ],
                    [
                        "key" => "ssf_no",
                        "label" => "SSF No",
                        "type" => "text",
                        "rules" => "nullable"
                    ]
                ]
            ],
            [
                "title" => "Contact Information",
                "fields" => [
                    [
                        "key" => "phone",
                        "label" => "Phone",
                        "type" => "text",
                        "rules" => "required|max:20|regex:/^(?:\+977[- ]?)?9\d{9}$/"
                    ],
                    [
                        "key" => "email",
                        "label" => "Email",
                        "type" => "email",
                        "rules" => "required|email|max:100"
                    ]
                ]
            ],
            [
                "title" => "Addresses",
                "fields" => [
                    [
                        "key" => "permanent_address",
                        "label" => "Permanent Address",
                        "type" => "textarea",
                        "rules" => "required|max:255"
                    ],
                    [
                        "key" => "temporary_address",
                        "label" => "Temporary Address",
                        "type" => "textarea",
                        "rules" => "max:255"
                    ]
                ]
            ],
            [
                "title" => "Family Details",
                "fields" => [
                    [
                        "key" => "father_name",
                        "label" => "Father's Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "mother_name",
                        "label" => "Mother's Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "grandfather_name",
                        "label" => "Grandfather's Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "spouse_name",
                        "label" => "Spouse Name",
                        "type" => "text",
                        "rules" => "max:100|required_if:marital_status,married",
                        "conditional" => [
                            "field" => "marital_status",
                            "value" => "married",
                        ]
                    ],
                ]
            ],
            [
                "title" => "Immediate Contact Person",
                "fields" => [
                    [
                        "key" => "contact_person_name",
                        "label" => "Name",
                        "type" => "text",
                        "rules" => "required|max:100"
                    ],
                    [
                        "key" => "contact_person_phone",
                        "label" => "Phone",
                        "type" => "text",
                        "rules" => "required|max:20|regex:/^(?:\+977[- ]?)?9\d{9}$/"
                    ],
                    [
                        "key" => "contact_person_relationship",
                        "label" => "Relationship",
                        "type" => "text",
                        "rules" => "required|max:50"
                    ]
                ]
            ],
            [
                "title" => "Documents",
                "fields" => [
                    [
                        "key" => "profile_picture",
                        "label" => "Profile Picture",
                        "type" => "file",
                        "accept" => "image/*",
                        "rules" => "required|mimes:jpeg,jpg,png|max:2048",
                        "multiple" => false,
                    ],
                    [
                        "key" => "citizenship-front",
                        "label" => "Citizenship Front",
                        "type" => "file",
                        "accept" => "image/*,.pdf",
                        "rules" => "nullable|required_without:national-identity-document|mimes:jpeg,jpg,png,pdf|max:2048",
                    ],
                    [
                        "key" => "citizenship-back",
                        "label" => "Citizenship Back",
                        "type" => "file",
                        "accept" => "image/*,.pdf",
                        "rules" => "nullable|required_without:national-identity-document|mimes:jpeg,jpg,png,pdf|max:2048",
                    ],
                    [
                        "key" => "national-identity-document",
                        "label" => "National Identity Document",
                        "type" => "file",
                        "accept" => "image/*,.pdf",
                        "rules" => "nullable|required_without_all:citizenship-front,citizenship_back|mimes:jpeg,jpg,png,pdf|max:2048",
                    ],

                    [
                        "key" => "pan-card",
                        "label" => "PAN Card",
                        "type" => "file",
                        "accept" => "image/*,.pdf",
                        "rules" => "required|mimes:jpeg,jpg,png,pdf|max:2048",
                        "multiple" => false,
                    ],
                    [
                        "key" => "marriage-certificate",
                        "label" => "Marriage Certificate",
                        "type" => "file",
                        "accept" => "image/*,.pdf",
                        "rules" => "nullable|mimes:jpeg,jpg,png,pdf|max:2048|required_if:marital_status,married",
                        "multiple" => false,
                        "conditional" => [
                            "field" => "marital_status",
                            "value" => "married",
                        ]
                    ],
                ]
            ]

        ];
    }

    public static function getFormFileKeys(): array
    {
        $schema = self::offerLetterFormSchema();
        $fileKeys = [];

        foreach ($schema as $section) {
            if (!isset($section['fields']) || !is_array($section['fields'])) {
                continue;
            }

            foreach ($section['fields'] as $field) {
                if (($field['type'] ?? null) === 'file' && isset($field['key'])) {
                    $fileKeys[] = $field['key'];
                }
            }
        }

        return $fileKeys;
    }


    public static function offerLetterFormRules(): array
    {
        $schema = self::offerLetterFormSchema();

        $rules = [];

        foreach ($schema as $section) {
            $fields = $section['fields'] ?? [];
            if (!is_array($fields))
                continue;

            foreach ($fields as $field) {
                $key = $field['key'] ?? null;
                if (!$key)
                    continue;

                $type = $field['type'] ?? 'text';
                $isMultiple = (bool) ($field['multiple'] ?? false);

                // Normalize rules to array of strings
                $rawRules = $field['rules'] ?? [];
                if (is_string($rawRules)) {
                    $rawRules = array_filter(
                        explode('|', $rawRules),
                        fn($r) => is_string($r) && trim($r) !== ''
                    );
                } elseif (!is_array($rawRules)) {
                    $rawRules = [];
                }

                // If no rules explicitly set, default nullable
                if (empty($rawRules)) {
                    $rawRules = ['nullable'];
                }

                if ($type === 'file') {
                    if ($isMultiple) {
                        $parentRules = [];
                        $childRules = [];

                        foreach ($rawRules as $r) {
                            $trim = trim($r);

                            if ($trim === 'required' || $trim === 'nullable') {
                                $parentRules[] = $trim;
                            } else {
                                // everything else (mimes, max, file, etc)
                                $childRules[] = $trim;
                            }
                        }

                        $parentRules[] = 'array';

                        if (!in_array('file', $childRules, true)) {
                            $childRules[] = 'file';
                        }

                        if (
                            in_array('required', $parentRules, true)
                            && !in_array('required', $childRules, true)
                            && !in_array('nullable', $childRules, true)
                        ) {
                            $childRules[] = 'required';
                        }

                        $rules[$key] = implode('|', $parentRules);
                        $rules[$key . '.*'] = implode('|', $childRules);
                    } else {

                        $singleRules = $rawRules;

                        if (!in_array('file', $singleRules, true)) {
                            $singleRules[] = 'file';
                        }

                        $rules[$key] = implode('|', $singleRules);
                    }
                } else {
                    $rules[$key] = implode('|', $rawRules);
                }
            }
        }

        return $rules;
    }

    public static function buildResponseSchema(array $inputValues): array
    {
        $schema = self::offerLetterFormSchema();
        $output = [];

        foreach ($schema as $section) {
            $sectionOut = [
                'title' => $section['title'] ?? '',
                'fields' => []
            ];

            foreach ($section['fields'] as $field) {
                $key = $field['key'] ?? null;
                if (!$key) {
                    continue;
                }

                $type = $field['type'] ?? 'text';

                $fieldOut = $field; // cloning to not mutate original arrays
                $fieldOut['value'] = $inputValues[$key] ?? null;

                $providedValue = Arr::get($inputValues, $key, null);

                if ($type === 'file' && $providedValue) {
                    $isMultiple = (bool) ($field['multiple'] ?? false);

                    $paths = self::normalizeFilePaths($providedValue);
                    if ($isMultiple) {
                        $filesMeta = [];
                        foreach ($paths as $path) {
                            $filesMeta[] = self::fileMetaFromPath($path);
                        }
                        $fieldOut['value'] = $filesMeta;
                    } else {
                        $fieldOut['value'] = self::fileMetaFromPath($paths[0] ?? null);
                    }
                } else {
                    $fieldOut['value'] = $providedValue;
                }

                $sectionOut['fields'][] = $fieldOut;
            }
            $output[] = $sectionOut;
        }
        return $output;
    }

    protected static function fileMetaFromPath(string $path): ?array
    {
        if (!$path) {
            return null;
        }

        if (!Storage::exists($path)) {
            return null;
            // throw new RuntimeException("File not found at path [$path]");
        }

        $mime = Storage::mimeType($path) ?? 'application/octet-stream';
        $contents = Storage::get($path);
        $base64 = base64_encode($contents);

        $label = basename($path);

        return [
            'label' => $label,
            'mime' => $mime,
            'base64' => "data:{$mime};base64,{$base64}",
        ];
    }

    protected static function normalizeFilePaths($providedValue)
    {
        if (is_null($providedValue) || $providedValue === '') {
            return [];
        }

        if (is_string($providedValue)) {
            return [$providedValue];
        }

        if (is_array($providedValue)) {
            // filter only non-empty strings
            return array_values(array_filter($providedValue, fn($p) => is_string($p) && $p !== ''));
        }

        // unexpected type
        return [];
    }
}
