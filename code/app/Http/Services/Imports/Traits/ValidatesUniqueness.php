<?php

namespace App\Http\Services\Imports\Traits;

use Exception;
use Illuminate\Database\Eloquent\Model;

trait ValidatesUniqueness
{
    /**
     * Stores values already found in the database.
     */
    protected array $existingValues = [];

    /**
     * Stores values already validated in the current batch.
     */
    protected array $seenInBatch = [];

    /**
     * Stores values already validated in the current batch for processing.
     */
    protected array $lookupCache = [];

    /**
     * Pre-load existing values from the database to memory.
     */
    protected function prepareUniquenessCache(string $modelClass, array $fields, array $rows, bool $withTrashed = true): void
    {
        $query = $withTrashed ? $modelClass::withTrashed() : $modelClass::query();

        foreach ($fields as $batchField => $databaseField) {

            $valuesInBatch = collect($rows)
                ->pluck($batchField)
                ->filter()
                ->map(fn($v) => strtolower((string)$v))
                ->unique()
                ->toArray();

            if (empty($valuesInBatch)) {
                $this->existingValues[$batchField] = [];
                continue;
            }

            $this->existingValues[$batchField] = $query->clone()
                ->whereIn($databaseField, $valuesInBatch)
                ->pluck($databaseField)
                ->map(fn($val) => strtolower((string)$val))
                ->toArray();

            $this->seenInBatch[$batchField] = [];
        }
    }

    protected function isUnique(string $batchField, string $field, mixed $value): ?string
    {
        $val = strtolower((string)$value);

        if (!$val) return null;

        // Priority 1: Check if it's already in the Database
        if (in_array($val, $this->existingValues[$batchField] ?? [])) {
            return "The $field '$value' already exists in the system.";
        }

        // Priority 2: Check if it's repeated multiple times in this file
        if (in_array($val, $this->seenInBatch[$batchField ?? $field] ?? [])) {
            return "The $field '$value' is duplicated within your upload file.";
        }

        $this->seenInBatch[$batchField][] = $val;

        return null;
    }

    /**
     * Summary of prepareLookup
     * @param string $model model class for searching
     * @param array $rows array of data
     * @param string $batchField field's name on data array
     * @param string $searchColumn The column in db to match against excel data
     * @param string|array $valueColumns the column to return, or the columns to return
     * @param bool $withTrashed
     * @return void
     */
    protected function prepareLookup(string $modelClass, array $rows,  string $batchField, string $searchColumn, string|array $valueColumns, bool $withTrashed = false)
    {
        $values = collect($rows)
            ->pluck($batchField)
            ->filter()
            ->unique()
            ->toArray();

        if (empty($values)) {
            $this->lookupCache[$batchField] = [];
            return;
        }

        $selectColumns = array_unique(array_merge($valueColumns, [$searchColumn]));

        $this->lookupCache[$batchField] = $modelClass::query()
            ->clone()
            ->when($withTrashed, fn($q) => $q->withTrashed())
            ->select($selectColumns)
            ->whereIn($searchColumn, $values)
            ->get()
            ->keyBy(fn($item) => strtolower($item->{$searchColumn}));
    }

    protected function getLookup(string $batchField, $searchVal)
    {
        if (!$searchVal) return null;
        $lookup =  $this->lookupCache[$batchField][strtolower($searchVal)] ?? null;
        if (!$lookup) {
            throw new Exception(ucfirst($batchField) . " '$searchVal' does not exist in the system.");
        }
        return $lookup;
    }
}
