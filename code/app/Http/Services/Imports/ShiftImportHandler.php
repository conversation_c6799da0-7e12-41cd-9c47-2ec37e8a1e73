<?php

namespace App\Http\Services\Imports;

use App\Models\configs\EmployeeShift;
use Carbon\Carbon;

class ShiftImportHandler extends ImportH<PERSON>lerAbstract
{
    protected $checkShiftName = [];
    protected $existingShiftName = [];
    protected $leisureMinutes, $totalMinutes;

    public function validateBatch(array $rows, array $requiredHeaders = []): array
    {
        $this->existingShiftName = EmployeeShift::withTrashed()->pluck('name')->map(fn($item) => strtolower($item))->toArray();

        $result = [];
        foreach ($rows as $row) {
            $requireResult = $this->validateRequired($row, $requiredHeaders);
            if ($requireResult['status']) {
                $result[] = $this->validateSingleRow($row);
            } else {
                $result[] = $requireResult;
            }
        }
        return $result;
    }

    protected function validateSingleRow(array $row): array
    {
        if ($error = $this->validateShiftName($row)) {
            return $error;
        }

        if ($error = $this->validateType($row)) {
            return $error;
        }

        if ($error = $this->validateTimes($row)) {
            return $error;
        }

        if ($error = $this->validateGraceTime($row)) {
            return $error;
        }

        if ($error = $this->validateDayOff($row)) {
            return $error;
        }

        return $this->successResponse("", $row);
    }

    protected function processSingleRow(array $row): array
    {
        try {
            EmployeeShift::create([
                'name'                => $row['name'],
                'type'                => $row['type'],
                'start_time'          => $row['start_time'] ?? null,
                'end_time'            => $row['end_time'] ?? null,
                'leisure_start_time'  => $row['leisure_start_time'] ?? null,
                'leisure_end_time'    => $row['leisure_end_time'] ?? null,
                'total_working_hour'  => $row['total_working_hour'],
                'actual_working_hour' => $row['actual_working_hour'],
                'grace_time'          => $row['grace_time'] ?? null,
                'day_off'             => $row['day_off'] ?? null,
                'is_default'          => false,
                'is_active'           => false,
                'company_id'          => $row['company_id'] ?? null,
            ]);
            return $this->successResponse("Shift created successfully", $row);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
    protected function validateShiftName(array $row): ?array
    {
        $name = strtolower(trim($row['name'] ?? ''));

        $allNames = array_merge(
            $this->checkShiftName,
            $this->existingShiftName
        );

        if (in_array($name, $allNames)) {
            return $this->errorResponse("Shift name already exists", $row);
        }

        return null;
    }
    protected function validateType(array $row): ?array
    {
        $type = strtolower(trim($row['type'] ?? ''));

        $allowedTypes = ['regular', 'halfday', 'overnight'];

        if (!in_array($type, $allowedTypes)) {
            return $this->errorResponse(
                "Type must be regular, halfday, or overnight",
                $row
            );
        }

        return null;
    }

    protected function validateTimes(array &$row): ?array
    {
        $startTime = $this->validateTimeFormat($row['start_time']);
        if (!$startTime) {
            return $this->errorResponse("Invalid start time format", $row);
        }

        $endTime = $this->validateTimeFormat($row['end_time']);
        if (!$endTime) {
            return $this->errorResponse("Invalid end time format", $row);
        }

        $leisureStart = $this->validateTimeFormat($row['leisure_start_time']);
        if (!$leisureStart) {
            return $this->errorResponse("Invalid leisure start time format", $row);
        }

        $leisureEnd = $this->validateTimeFormat($row['leisure_end_time']);
        if (!$leisureEnd) {
            return $this->errorResponse("Invalid leisure end time format", $row);
        }

        $totalMinutes   = $startTime->diffInMinutes($endTime);
        $leisureMinutes = $leisureStart->diffInMinutes($leisureEnd);

        if ($leisureMinutes >= $totalMinutes) {
            return $this->errorResponse(
                "Leisure time cannot be greater than or equal to total working time",
                $row
            );
        }

        $actualMinutes = $totalMinutes - $leisureMinutes;

        $row['total_working_hour']  = round($totalMinutes / 60, 2);
        $row['actual_working_hour'] = round($actualMinutes / 60, 2);

        $type = strtolower($row['type']);

        // Overnight handling
        if ($type === 'overnight' && $endTime->lte($startTime)) {
            $endTime->addDay();
        }

        if ($type !== 'overnight' && $startTime->gte($endTime)) {
            return $this->errorResponse(
                "Start Time must be earlier than End Time",
                $row
            );
        }

        if ($leisureStart->gte($leisureEnd)) {
            return $this->errorResponse(
                "Leisure Start Time must be earlier than Leisure End Time",
                $row
            );
        }

        $totalMinutes   = $startTime->diffInMinutes($endTime);
        $leisureMinutes = $leisureStart->diffInMinutes($leisureEnd);

        if ($leisureMinutes >= $totalMinutes) {
            return $this->errorResponse(
                "Leisure time cannot be greater than or equal to total working time",
                $row
            );
        }

        return null;
    }

    protected function validateGraceTime(array $row): ?array
    {
        if (!is_numeric($row['grace_time']) || (int) $row['grace_time'] < 0) {
            return $this->errorResponse(
                "Grace Time must be a positive number (minutes)",
                $row
            );
        }

        return null;
    }

    protected function validateDayOff(array &$row): ?array
    {
        $dayName = trim($row['day_off'] ?? '');

        if (!$dayName) {
            return $this->errorResponse("Day Off is required", $row);
        }

        try {
            // Convert weekday name to numeric (Sunday=0, Monday=1, etc.)
            $row['day_off'] = Carbon::parse($dayName)->dayOfWeek;
        } catch (\Exception $e) {
            return $this->errorResponse(
                "Invalid Day Off. Use weekday name like Sunday, Monday, etc.",
                $row
            );
        }

        return null;
    }

    protected function validateTimeFormat(string $time, string $format = 'H:i:s'): bool|Carbon|null
    {
        $parsed = Carbon::createFromFormat($format, $time);

        $formatted = $parsed->format($format);
        if ($parsed && $formatted === $time) {
            return $parsed;
        }
        return null;
    }
}
