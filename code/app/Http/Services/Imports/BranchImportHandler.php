<?php

namespace App\Http\Services\Imports;

use App\Models\configs\Branch;
use Illuminate\Support\Facades\DB;

class BranchImportHandler extends ImportHandlerAbstract
{
    protected $checkBranchCode = [];
    protected $existingBranchCode = [];

    public function validateBatch(array $rows, array $requiredHeaders = []): array
    {
        $this->existingBranchCode = Branch::withTrashed()->pluck('branch_code')->map(fn($item) => strtolower($item))->toArray();

        $result = [];
        foreach ($rows as $row) {
            $requireResult = $this->validateRequired($row, $requiredHeaders);
            if ($requireResult['status']) {
                $result[] = $this->validateSingleRow($row);
                $this->checkBranchCode[] = strtolower($row['branch code']);
            } else {
                $result[] = $requireResult;
            }
        }
        return $result;
    }

    protected function validateSingleRow(array $row): array
    {
        $branchCode = $row['branch code'];

        $allBranchCodes = array_merge(
            $this->checkBranchCode,
            $this->existingBranchCode
        );

        if (in_array(strtolower($branchCode), $allBranchCodes)) {
            return $this->errorResponse("Branch code already exists", $row);
        }

        return $this->successResponse("", $row);
    }

    protected function processSingleRow(array $row): array
    {
        try {
            Branch::create([
                'name'             => $row['name'],
                'address'          => $row['address'],
                'branch_code'      => $row['branch code'] ?? null,
                'phone'            => $row['phone'] ?? null,
                // 'branch_manager'   => $row['branch manager'] ?? null,
                'company_id'       => $row['company_id'] ?? null,
            ]);
            return $this->successResponse('Branch Created', $row);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $row);
        }
    }
}
