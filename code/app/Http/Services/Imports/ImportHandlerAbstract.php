<?php

namespace App\Http\Services\Imports;

use Illuminate\Support\Facades\DB;

abstract class ImportHandlerAbstract
{
    /**
     * Validate entire batch of rows.
     * $rows is array of arrays: each entry is the raw_data you stored from Excel.
     *
     * Returns array of validation results in same order: [[ 'status'=>bool, 'message'=>string|null, 'data'=>array|null ], ...]
     */
    abstract public function validateBatch(array $rows, array $requiredHeaders = []): array;

    /** Can be taken as example for future use; for now it isn't needed as we can take help of insert functionality without needing to loop the create function */
    public function processBatch(array $rows, array $requiredHeaders = []): array
    {
        // Validate all rows first before processing as the data might be changed on process
        $validationResults = $this->validateBatch($rows, $requiredHeaders);
        $validData = collect($validationResults)->filter(function ($result) {
            return $result['status'];
        })->map(fn($item) => $item['data'])->all();

        if (!\count($validData)) {
            return $this->errorResponse("No valid data to create");
        }

        if (count($validData) != count($rows)) {
            return $this->errorResponse('Some data are not validated. Validate it first.');
        }

        $this->beforeProcessing($rows);

        DB::beginTransaction();
        $result = [];
        try {
            foreach ($validData as $row) {
                $response = $this->processSingleRow($row);
                $result[] = $response;
                if (!$response['status']) {
                    DB::rollBack();
                    return $result;
                }
            }
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error processing data import batch", $e);
            throw $e;
        }
    }

    protected function beforeProcessing(array $rows) {}

    protected function errorResponse(string $message, $field = null): array
    {
        return ['status' => false, 'message' => $message, 'data' => null];
    }

    protected function successResponse(string $message = "", ?array $data = null): array
    {
        return ['status' => true, 'message' => $message, 'data' => $data];
    }

    protected function validateRequired($row, array $requiredHeaders)
    {
        foreach ($requiredHeaders as $header) {
            $header = strtolower($header);
            $item = $row[$header] ?? null;
            if ($item === null || $item === '')
                return $this->errorResponse(ucfirst($header) . " cannot be empty", $row);
        }
        return $this->successResponse("Everything validated");
    }

    protected function processSingleRow(array $row): array
    {
        return [];
    }
}
