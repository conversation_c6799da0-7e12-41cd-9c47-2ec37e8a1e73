<?php

namespace App\Http\Services\Imports;

use Exception;

class ImportHandlerFactory
{
    public function make($type)
    {
        switch ($type) {
            case 'branch':
                return new BranchImportHandler();
            case 'department':
                return new DepartmentImportHandler();
            case 'shift':
                return new ShiftImportHandler();
            case 'holiday':
                return new HolidayImportHandler();
            case 'employee':
                return new EmployeeImportHandler();
            default:
                throw new Exception("Unknown type for import: {$type}");
        }
    }
}
