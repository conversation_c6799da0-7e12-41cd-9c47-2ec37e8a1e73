<?php

namespace App\Http\Services\Imports;

use App\Http\Services\Imports\Traits\ValidatesUniqueness;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\EmployeeShift;
use App\Models\configs\EmpStatus;
use App\Models\configs\OutsourceCompany;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\EmployeeFamilyInformation;
use App\Models\User;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;

class EmployeeImportHandler extends ImportHandlerAbstract
{
    use ValidatesUniqueness;

    protected array $excelValues = [];

    protected array $existingValues = [];

    protected array $referenceMap = [];

    protected array $uniqueFields = [
        "username"           => "Username",
        'mobile'             => 'Personal contact number',
        'email'              => 'Personal email',
        'citizenship'        => 'Citizenship',
        'employee code'      => 'Employee Code',
        'organization email' => 'Organization Email',
        'bank account no'    => 'Bank Account No.',
        'cug number'         => 'CUG Number',
        'rf no'              => 'RF No.',
        'cit no'             => 'CIT No',
        'biometric id'       => 'Biometric ID',
        'pan no'             => 'PAN No.',
    ];

    protected array $checkName = [
        "first name"                  => "First Name",
        "middle name"                 => "Middle Name",
        "last name"                   => "Last Name",
        "temporary address"           => "Temporary Address",
        "spouse name"                 => "Spouse Name",
        "nationality"                 => "Nationality",
        "permanent address"           => "Permanent Address",
        "father's name"               => "Father's Name",
        "mother's name"               => "Mother's Name",
        "grandfather's name"          => "GrandFather's Name",
        "immediate contact"           => "Immediate Contact Person",
        "immediate contact relation"  => "Immediate Contact Relation",
        "branch name"                 => "Branch Name",
        "department name"             => "Department Name",
        // "shift name"                  => "Shift Name",
        "supervisor name"             => "Supervisor Name",
        "bank name"                   => "Bank Name",
    ];


    protected array $lookupFields = [
        'branch name' => [
            'label'  => 'Branch',
            'field'  => 'branch name',
            'model'  => Branch::class,
            'column' => 'name',
            'id'     => 'branch_id',
        ],
        'department name' => [
            'label'  => 'Department',
            'field'  => 'department name',
            'model'  => Department::class,
            'column' => 'name',
            'id'     => 'department_id',
        ],
        'shift name' => [
            'label'  => 'Shift',
            'field'  => 'shift name',
            'model'  => EmployeeShift::class,
            'column' => 'name',
            'id'     => 'shift_id',
        ],
        'outsource company' => [
            'label'  => 'Outsource Company',
            'field'  => 'outsource company',
            'model'  => OutsourceCompany::class,
            'column' => 'name',
            'id'     => 'outsource_company_id',
        ],
        'employee status' => [
            'label'  => 'Employee Status',
            'field'  => 'employee status',
            'model'  => EmpStatus::class,
            'column' => 'name',
            'id'     => 'employee_status_id',
        ],
    ];

    public function validateBatch(array $rows, array $requiredHeaders = []): array
    {
        $this->prepareUniquenessCache(User::class, [
            'username' => 'username'
        ], $rows);

        $this->prepareUniquenessCache(Employee::class, [
            'mobile' => 'phone',
            'email'  => 'email',
            'citizenship' => 'citizenship'
        ], $rows);

        $this->prepareUniquenessCache(EmployeeOrg::class, [
            'employee code'   => 'employee_code',
            'bank account no' => 'bank_account_no',
            'cug number'      => 'cug',
            'rf_no'           => 'rf_no',
            'cit no'          => 'cit_no',
            'biometric id'    => 'biometric_id',
            'pan no'          => 'pan_no'
        ], $rows);


        foreach ($this->lookupFields as $excelField => $data) {
            $this->prepareLookup($data['model'], $rows, $excelField, $data['column'], ['id']);
        }

        $result = [];
        foreach ($rows as $row) {
            $requireResult = $this->validateRequired($row, $requiredHeaders);
            if ($requireResult['status']) {
                $result[] = $this->validateSingleRow($row);
            } else {
                $result[] = $requireResult;
            }
        }
        return $result;
    }

    protected function validateSingleRow(array $row): array
    {
        // not unique functions just to check format
        if ($error = $this->validateGender($row)) {
            return $error;
        }
        if ($error = $this->validateDOB($row)) {
            return $error;
        }
        if ($error = $this->validateFamilyDOB($row)) {
            return $error;
        }
        if ($error = $this->validateMaritalStatus($row)) {
            return $error;
        }
        if ($error = $this->validateName($row)) {
            return $error;
        }

        //  fields that needs to have unique value
        foreach ($this->uniqueFields as $fieldKey => $label) {
            if ($error = $this->isUnique($fieldKey, $label, $row[$fieldKey])) {
                return $this->errorResponse($error);
            }
        }

        foreach ($this->lookupFields as $field => $data) {
            try {
                $this->getLookup($field, $row[$field]);
            } catch (\Exception $e) {
                return $this->errorResponse($e->getMessage());
            }
        }

        if ($error = $this->validateMobile($row)) {
            return $error;
        }
        if ($error = $this->validateImmContactMobile($row)) {
            return $error;
        }
        if ($error = $this->validateBiometricId($row)) {
            return $error;
        }
        if ($error = $this->validateCug($row)) {
            return $error;
        }

        return $this->successResponse("", $row);
    }

    protected function validateName(array $row): ?array
    {
        foreach ($this->checkName as $field => $label) {

            if ($field === 'spouse_name' && strtolower($row['marital status'] ?? '') !== 'married') {
                continue;
            }

            if ($field === 'spouse_name' && strtolower($row['marital status'] ?? '') === 'married' && empty(trim($row[$field] ?? ''))) {
                return $this->errorResponse("Spouse name is required for married employees", $row);
            }

            $value = trim((string) ($row[$field] ?? ''));

            if ($value === '') {
                continue;
            }

            if (!preg_match('/^[a-zA-Z\s]+$/', $value)) {
                return $this->errorResponse(
                    "$label cannot contain special characters or numbers",
                    $row
                );
            }
        }

        return null;
    }

    protected function validateGender(array $row): ?array
    {
        $gender = strtolower(trim($row['gender'] ?? ''));

        $allowedGender = ['male', 'female', 'other'];

        if (!in_array($gender, $allowedGender)) {
            return $this->errorResponse(
                "Gender must be either Male, Female or Other",
                $row
            );
        }
        return null;
    }

    public function validateDOB(array $row): ?array
    {
        try {
            $dob = Carbon::parse($row['dob_eng']);
        } catch (\Exception $e) {
            return $this->errorResponse(
                "Invalid Date of Birth",
                $row
            );
        }

        if ($dob->isFuture()) {
            return $this->errorResponse(
                "Date of Birth cannot be in the future",
                $row
            );
        }

        $dob_ad = Carbon::now()->subYears(18)->startOfDay();

        if ($dob->greaterThan($dob_ad)) {
            return $this->errorResponse(
                "Employee must be at least 18 years old",
                $row
            );
        }

        return null;
    }

    public function validateFamilyDOB(array $row): ?array
    {
        $familyMembers = ['father', 'mother', 'grandfather', 'spouse'];

        foreach ($familyMembers as $mem) {
            if ($mem === 'spouse' && strtolower($row['marital status'] ?? '') !== 'married') {
                continue;
            }

            if ($mem === 'spouse' && strtolower($row['marital status'] ?? '') === 'married' && empty($row[$key])) {
                return $this->errorResponse("Spouse DOB is required for married employees", $row);
            }

            $key = $mem . '_dob_eng';

            // Reuse SAME validation logic
            $error = $this->validateDOB([
                'dob_eng' => $row[$key],
            ]);

            if ($error) {
                return $this->errorResponse(
                    ucfirst($mem) . " must be at least 18 years old, DOB must not be of future date or DOB is invalid",
                    $row
                );
            }
        }

        return null;
    }


    protected function validateMaritalStatus(array $row): ?array
    {
        $gender = strtolower(trim($row['marital status'] ?? ''));

        $allowedGender = ['married', 'unmarried',];

        if (!in_array($gender, $allowedGender)) {
            return $this->errorResponse(
                "Marital status must be either Married or Unmarried",
                $row
            );
        }
        return null;
    }

    protected function validateMobile(array $row): ?array
    {
        $mobile = trim((string) ($row['mobile'] ?? ''));

        if (!preg_match('/^\+?\d+$/', $mobile)) {
            return $this->errorResponse(
                'Mobile number needs to be numeric',
                $row
            );
        }

        return null;
    }

    protected function validateImmContactMobile($row): ?array
    {
        $immMobile = $row['immediate contact mobile'];
        if (!preg_match('/^\+?[0-9]{7,15}$/', $immMobile)) {
            return $this->errorResponse(
                "Invalid immediate contact mobile number format",
                $row
            );
        }
        return null;
    }

    protected function validateBiometricId($row): ?array
    {
        $bioId = $row['biometric id'];
        if (!is_numeric($bioId)) {
            return $this->errorResponse(
                "Biometric id must be in numeric format",
                $row
            );
        }

        return null;
    }
    protected function validateCug($row): ?array
    {
        $cug = $row['cug number'];
        if (!is_numeric($cug)) {
            return $this->errorResponse(
                "CUG number must be in numeric format",
                $row
            );
        }

        return null;
    }

    protected function beforeProcessing(array $rows)
    {
        foreach ($this->lookupFields as $excelField => $data) {
            $this->prepareLookup($data['model'], $rows, $excelField, $data['column'], ['id']);
        }
    }

    protected function processSingleRow(array $row): array
    {
        $branchName = $row['branch name'];
        $branch = $this->getLookup('branch name', $branchName);

        $departmentName = $row['department name'];
        $department = $this->getLookup('department name', $departmentName);

        $shiftName = $row['shift name'];
        $shift = $this->getLookup('shift name', $shiftName);

        $outsourceCompany = $row['outsource company'];
        $outsourceCompany = $this->getLookup('outsource company', $outsourceCompany);

        $employeeStatus = $row['employee status'];
        $empStatus = $this->getLookup('employee status', $employeeStatus);

        DB::beginTransaction();
        try {
            /** Create User */
            $user = User::create([
                'username' => $row['username'],
                'password' => bcrypt($row['username'] . '@123'),
            ]);

            $dobEng = $row['dob_eng'];
            $dobNep = LaravelNepaliDate::from($dobEng)->toNepaliDate();

            /** Create Employee */
            $employee = Employee::create([
                'first_name'            => $row['first name'],
                'middle_name'           => $row['middle name'] ?? '',
                'last_name'             => $row['last name'],
                'gender'                => strtolower($row['gender']),
                'dob'                   => $dobEng,
                'dob_nepali'            => $dobNep,
                'mstat'                 => $row['marital status'],
                'phone'                 => $row['mobile'],
                'email'                 => $row['email'],
                'nationality'           => $row['nationality'],
                'citizenship'           => $row['citizenship'],
                'permanent_address'     => $row['permanent address'],
                'temporary_address'     => $row['temporary address'],
                'father'                => $row["father_name"],
                'mother'                => $row["mother_name"],
                'grandfather'           => $row["grandfather_name"],
                'spouse'                => $row["spouse_name"],
                'contact_person'        => $row['immediate contact'],
                'contact_phone'         => $row['immediate contact mobile'],
                'contact_relation'      => $row['immediate contact relation'],
                'company_id'            => $row['company_id'],
                'user_id'               => $user->id,
            ]);

            /** Create EmployeeOrg */
            EmployeeOrg::create([
                'employee_id'           => $employee->id,
                'employee_code'         => $row['employee code'],
                'email'                 => $row['organization email'] ?? $row['email'],
                'branch_id'             => $branch->id,
                'department_id'         => $department->id,
                'shift_id'              => $shift->id,
                'outsource_company_id'  => $outsourceCompany?->id,
                'employee_status_id'    => $empStatus?->id,
                'doj'                   => $row['date of join'],
                'bank_account_no'       => $row['bank account no'],
                'cug'                   => $row['cug number'],
                'rf_no'                 => $row['rf no'],
                'cit_no'                => $row['cit no'],
                'pan_no'                => $row['pan no'],
                'biometric_id'          => $row['biometric id'],
            ]);

            $familyMembers = ['father', 'mother', 'grandfather', 'spouse'];

            $familyInfo = [];

            foreach ($familyMembers as $mem) {

                if ($mem === 'spouse' && strtolower($row['marital status'] ?? '') !== 'married') {
                    continue;
                }

                if ($mem === 'spouse' && strtolower($row['marital status'] ?? '') === 'married' && empty($row['marital status'])) {
                    return $this->errorResponse("Spouse name is required for married employees", $row);
                }

                $familyInfo[] = [
                    'employee_id' => $employee->id,
                    'name' => $row[$mem . "_name"],
                    'dob_eng' => $row[$mem . '_dob_eng'],
                    'relation_type' => $mem
                ];
            }

            foreach ($familyInfo as $field => $data) {
                EmployeeFamilyInformation::create($data);
            }

            DB::commit();

            return $this->successResponse("Employee imported successfully", $row);
        } catch (\Throwable $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage(), $row);
        }
    }
}
