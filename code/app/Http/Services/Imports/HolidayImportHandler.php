<?php

namespace App\Http\Services\Imports;

use App\Models\configs\Holiday;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;

class HolidayImportHandler extends ImportHandlerAbstract
{
    protected $checkHolidayName = [];
    protected $existingHolidayName = [];
    protected $existingHolidayDate = [];
    protected $checkHolidayDate = [];

    public function validateBatch(array $rows, array $requiredHeaders = []): array
    {
        $this->checkHolidayDate = Holiday::withTrashed()->pluck('eng_date')->map(fn($item) => strtolower($item))->toArray();

        $this->checkHolidayName = Holiday::withTrashed()->pluck('name')->map(fn($item) => strtolower($item))->toArray();
        $result = [];
        foreach ($rows as $row) {
            $requireResult = $this->validateRequired($row, $requiredHeaders);
            if ($requireResult['status']) {
                $result[] = $this->validateSingleRow($row);
            } else {
                $result[] = $requireResult;
            }
        }
        return $result;
    }

    protected function validateSingleRow(array $row): array
    {
        if ($error = $this->validateGender($row)) {
            return $error;
        }
        if ($error = $this->validateFiscalYearDates($row)) {
            return $error;
        }
        if ($error = $this->validateRate($row)) {
            return $error;
        }
        if ($error = $this->validateHolidayDate( $row)) {
            return $error;
        }

        return $this->successResponse("", $row);
    }


    protected function validateGender(array $row): ?array
    {
        $gender = strtolower(trim($row['gender'] ?? ''));

        $allowedGender = ['male', 'female', 'all'];

        if (!in_array($gender, $allowedGender)) {
            return $this->errorResponse(
                "Gender must be either Male, Female or All",
                $row
            );
        }

        return null;
    }

    protected function validateRate(array $row): ?array
    {

        $rate = trim((string) $row['rate']);

        if (!preg_match('/^\d+(\.\d{1,2})?$/', $rate)) {
            return $this->errorResponse(
                "Rate must be numeric (e.g. 10 or 50.78)",
                $row
            );
        }

        if ((float) $rate < 0) {
            return $this->errorResponse(
                "Rate must be zero or greater",
                $row
            );
        }

        return null;
    }

    protected function validateHolidayDate(array $row): ?array
    {
        $holidayDate = $row['date eng'];

        $allDate = array_merge(
            $this->existingHolidayDate,
            $this->checkHolidayDate
        );

        if (in_array(($holidayDate), $allDate)) {
            return $this->errorResponse("Holiday already exists for this date.", $row);
        }

        return $this->successResponse("", $row);
    }


    protected function validateFiscalYearDates(array &$row): ?array
    {
        $date = $row['date eng'];

        $fiscalYearId = $this->getFiscalYearId($date, $date);
        if (!$fiscalYearId) {
            return $this->errorResponse("Selected date does not belong to active Fiscal Year");
        }

        $row['fiscal_year_id'] = $fiscalYearId;
        $row['date nep'] = LaravelNepaliDate::from($date)->toNepaliDate();

        return null;
    }

    protected function getFiscalYearId($startDateAd, $endDateAd)
    {
        $nepStartDate = LaravelNepaliDate::from($startDateAd)->toNepaliDate();
        $nepEndDate = LaravelNepaliDate::from($endDateAd)->toNepaliDate();

        return \App\Models\configs\FiscalYear::where([
            ['start_date', '<=', $nepStartDate],
            ['end_date', '>=', $nepEndDate]
        ])->pluck('id')->first();
    }

    protected function processSingleRow(array $row): array
    {
        try {
            Holiday::create([
                'name'                => $row['name'],
                'eng_date'            => $row['date eng'],
                'gender'              => $row['gender'] ?? null,
                'rate'                => $row['rate'] ?? null,
                'nep_date'            => $row['date nep'] ?? null,
                'remarks'             => $row['remarks'] ?? null,
                'company_id'          => $row['company_id'] ?? null,
                'fiscal_year'         => $row['fiscal_year_id'] ?? null,
            ]);
            return $this->successResponse("Shift created successfully", $row);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
