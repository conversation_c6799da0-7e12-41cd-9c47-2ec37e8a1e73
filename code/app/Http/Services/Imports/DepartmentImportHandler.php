<?php

namespace App\Http\Services\Imports;

use App\Http\Services\Imports\Traits\ValidatesUniqueness;
use App\Livewire\Config\Department\Department as ConfigDepartment;
use App\Models\configs\Department;
use Illuminate\Support\Facades\DB;

class DepartmentImportHandler extends ImportHandlerAbstract
{
    use ValidatesUniqueness;
    protected $checkedAbbreviation = [];
    protected $existingAbbreviation = [];

    public function validateBatch(array $rows, array $requiredHeaders = []): array
    {
        $this->prepareUniquenessCache(Department::class, [
            'abbreviation' => 'abbreviation', // data field => db field, e.g: data field can be date ad, but db field can be date_eng
        ], $rows);

        $result = [];
        foreach ($rows as $row) {
            $requireResult = $this->validateRequired($row, $requiredHeaders);
            if ($requireResult['status']) {
                $result[] = $this->validateSingleRow($row);
            } else {
                $result[] = $requireResult;
            }
        }
        return $result;
    }

    protected function validateSingleRow(array $row): array
    {
        if ($error = $this->isUnique('abbreviation', 'abbreviation', $row['abbreviation'])) {
            return $this->errorResponse($error, $row);
        }

        return $this->successResponse("", $row);
    }

    protected function processSingleRow(array $row): array
    {
        try {
            Department::create([
                'company_id'      => $row['company_id'] ?? null,
                'name'             => $row['name'],
                'abbreviation'     => $row['abbreviation'],
            ]);
            return $this->successResponse("Department created successfully", $row);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
