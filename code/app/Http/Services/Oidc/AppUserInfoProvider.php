<?php

namespace App\Http\Services\Oidc;

use Illuminate\Contracts\Auth\Authenticatable;
use CodeBright\OauthAnd2fa\Contracts\OidcUserInfoProvider;
use PermissionList;

class AppUserInfoProvider implements OidcUserInfoProvider
{
    public function getUserInfo(Authenticatable $user): array
    {
        /** @var \App\Models\User */
        $u = $user;

        return [
            "sub" => (string) $u->employee?->employeeCode,
            "name" => $u->employee?->fullName ?? null,
            "preferred_username" => $u->username ?? null,
            "email" => $u->employee?->organizationInfo?->email ?? null,
            "first_name" => $u->employee?->first_name ?? null,
            "last_name" => $u->employee?->last_name ?? null,
            "is_admin" => $u->hasPermissionTo(PermissionList::OPEN_PROJECT_ADMIN, 'web')
        ];
    }
}
