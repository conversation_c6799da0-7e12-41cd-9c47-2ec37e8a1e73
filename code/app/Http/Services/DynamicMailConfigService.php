<?php

namespace App\Http\Services;

use App\Models\configs\Company;
use App\Models\configs\MailConfigurationSetting;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;

class DynamicMailConfigService
{
    public function apply(?int $companyId = null): void
    {
        try {
            if (!Schema::hasTable('mail_configuration_settings')) {
                return;
            }

            $company = $companyId
                ? Company::find($companyId)
                : (currentEmployee()?->company ?? Company::where("is_parent", "Y")->first());

            if (!$company) {
                Log::error('No company found for dynamic mail config');
                return;
            }

            $settings = MailConfigurationSetting::where('company_id', $company->id)
                ->pluck('value', 'key')->toArray();

            Config::set('mail.default', $settings['mail_mailer'] ?? config('mail.default'));
            Config::set('mail.mailers.smtp.host', $settings['mail_host'] ?? config('mail.mailers.smtp.host'));
            Config::set('mail.mailers.smtp.port', $settings['mail_port'] ?? config('mail.mailers.smtp.port'));
            Config::set('mail.mailers.smtp.encryption', $settings['mail_encryption'] ?? config('mail.mailers.smtp.encryption'));
            Config::set('mail.mailers.smtp.username', $settings['mail_username'] ?? config('mail.mailers.smtp.username'));
            Config::set('mail.mailers.smtp.password', $settings['mail_password'] ?? config('mail.mailers.smtp.password'));
            Config::set('mail.from.address', $settings['mail_from_address'] ?? config('mail.from.address'));
            Config::set('mail.from.name', $settings['mail_from_name'] ?? config('mail.from.name'));

            Mail::purge('smtp');
        } catch (\Throwable $e) {
            Log::error('Dynamic mail config load failed: ' . $e->getMessage());
        }
    }
}
