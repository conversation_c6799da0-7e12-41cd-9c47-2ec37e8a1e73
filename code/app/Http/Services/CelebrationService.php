<?php

namespace App\Http\Services;

use Carbon\Carbon;

class CelebrationService
{
    public function isBirthday($employee)
    {
        $today = Carbon::today()->format('m-d');
        return $employee->dob && Carbon::parse($employee->dob)->format('m-d') === $today;
    }

    public function isWorkAnniversary($employee)
    {
        $today = Carbon::today();
        return $employee->organizationInfo?->doj
            && Carbon::parse($employee->organizationInfo->doj)->format('m-d') === $today->format('m-d')
            && Carbon::parse($employee->organizationInfo->doj)->diffInYears($today) >= 1;
    }

    public function isBirthdayOnly($employee)
    {
        return $this->isBirthday($employee) && !$this->isWorkAnniversary($employee);
    }
    public function isWorkAnniversaryOnly($employee)
    {
        return $this->isWorkAnniversary($employee) && !$this->isBirthday($employee);
    }

    public function isDoubleCelebration($employee)
    {
        return $this->isBirthday($employee) && $this->isWorkAnniversary($employee);
    }
}
