<?php

namespace App\Http\Services;

use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\Region;
use App\Models\configs\SubBranch;

class ScopeFetcher
{
    //To show the filter in the view
    public function scopeWiseView() {
        $levels = [
            'company'     => fn() => scopeAll() || scopeMultipleCompany(),
            'region'      => fn() => scopeAll() || scopeMultipleCompany() || scopeCompany() || scopeMultipleRegion(),
            'branch'      => fn() => scopeAll() || scopeMultipleCompany() || scopeCompany() || scopeMultipleRegion() || scopeRegion() || scopeMultipleBranch(),
            'sub_branch'  => fn() => scopeAll() || scopeMultipleCompany() || scopeCompany() || scopeMultipleRegion() || scopeRegion() || scopeMultipleBranch() || scopeBranch() || scopeMultipleSubBranch(),
            'department'  => fn() => scopeAll() || scopeMultipleCompany() || scopeCompany() || scopeMultipleRegion() || scopeRegion() || scopeMultipleBranch() || scopeBranch() || scopeMultipleSubBranch() || scopeSubBranch() || scopeMultipleDepartment(),
        ];

        return array_keys(array_filter($levels, fn($check) => $check()));
    }
    
    private function returnData($returnData, $pluck) {
        if($pluck !== "") {
            switch($pluck) {
                case "id":
                    return $returnData->pluck("id")->toArray();
                case "name":
                    return $returnData->pluck("name")->toArray();
                case "id_name":
                    return $returnData->pluck("name", "id")->toArray();
            }
        }
        return $returnData;
    }

    //To fetch the companies as per scope
    public function fetchCompany(?string $pluck = "")
    {
        $query = Company::orderBy('name')->select('name', 'id');
        if(isSuperAdmin() || scopeAll()) {
            $returnData = $query->get();
        } elseif (count(scopeMultipleCompany()) > 0) {
            $returnData = $query->where(function ($query) {
                                    $query->where('id', currentEmployee()?->company_id)
                                        ->orWhereIn('id', scopeMultipleCompany());
                                })->get();
        } else {
            $returnData = $query->where('id', currentEmployee()?->company_id)->get();
        }
        return $this->returnData($returnData, $pluck);
    }

    //To fetch the regions as per scope
    public function fetchRegion(int|string|null $company = null, ?string $pluck = "")
    {
        $query = Region::orderBy('name')->select('name', 'id');

        if($company != null)
            $query->where("company_id", $company);

        if(isSuperAdmin() || scopeAll()) {
            $returnData = $query->get();
        } elseif (count(scopeMultipleCompany()) > 0) {
            $returnData = $query->where(function ($query) {
                                            $query->where('company_id', currentEmployee()?->company_id)
                                                ->orWhereIn('company_id', scopeMultipleCompany());
                                        })->get();
        }elseif (scopeCompany()) {
            $returnData = $query->where("company_id", currentEmployee()?->company_id)->get();
        }elseif(count(scopeMultipleRegion()) > 0) {
            $returnData = $query->where(function ($query) {
                                            $query->where('id', currentEmployee()?->organizationInfo?->region_id)
                                                ->orWhereIn('id', scopeMultipleRegion());
                                        })->get();
        } else {
            $returnData = $query->where('id', currentEmployee()?->organizationInfo?->region_id)->get();
        }
        return $this->returnData($returnData, $pluck);
    }

    //To fetch the branches as per scope
    public function fetchBranch(int|string|null $company = null, int|string|null $region = null, ?string $pluck = "")
    {
        $query = Branch::orderBy('name')->select('name', 'id');

        if($company != null)
            $query->where("company_id", $company);

        if($region != null)
            $query->where("region_id", $region);

        if(isSuperAdmin() || scopeAll()) {
            $returnData = $query->get();
        }elseif(count(scopeMultipleCompany()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('company_id', currentEmployee()?->company_id)
                                                ->orWhereIn("company_id", scopeMultipleCompany()); 
                                        })->get();
        }elseif (scopeCompany()) {
            $returnData = $query->where('company_id', currentEmployee()?->company_id)->get();
        }elseif(count(scopeMultipleRegion()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('id', currentEmployee()?->organizationInfo?->branch_id)
                                                ->orWhereIn("region_id", scopeMultipleRegion());
                                        })->get();
        }elseif (scopeRegion()) {
            if(count(scopeMultipleBranch()) > 0) {
                $returnData = $query->where(function($query) {
                    $query->where('region_id', currentEmployee()?->organizationInfo?->region_id)
                        ->orWhereIn("id", scopeMultipleBranch());
                })->get();
            } else {
                $returnData = $query->where('region_id', currentEmployee()?->organizationInfo?->region_id)->get();
            }
        }elseif(count(scopeMultipleBranch()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('id', currentEmployee()?->organizationInfo?->branch_id)
                                                ->orWhereIn("id", scopeMultipleBranch());
                                        })->get();
        }else {
            $returnData = $query->where('id', currentEmployee()?->organizationInfo?->branch_id)->get();
        }
        return $this->returnData($returnData, $pluck);
    }

    //To fetch the sub-branches as per scope
    public function fetchSubBranch(int|string|null $company = null, int|string|null $region = null, int|string|null $branch = null, ?string $pluck = "")
    {
        $query = SubBranch::orderBy('name')->select('sub_branches.name as name', 'sub_branches.id as id')
                                            ->leftJoin("branches as b", "b.id", "sub_branches.branch_id");
        
        if($company != null)
            $query->where("b.company_id", $company);

        if($region != null)
            $query->where("b.region_id", $region);

        if($branch != null)
            $query->where("b.branch_id", $branch);

        if(isSuperAdmin() || scopeAll()) {
            $returnData = $query->get();
        }elseif(count(scopeMultipleCompany()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('b.company_id', currentEmployee()?->company_id)
                                                ->orWhereIn("b.company_id", scopeMultipleCompany());
                                        })->get();
        }elseif (scopeCompany()) {
            $returnData = $query->where('company_id', currentEmployee()?->company_id)->get();
        }elseif (count(scopeMultipleRegion()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('b.region_id', currentEmployee()?->organizationInfo?->region_id)
                                                ->orWhereIn("b.region_id", scopeMultipleRegion());
                                        })->get();
        } elseif (scopeRegion()) {
            if(count(scopeMultipleBranch()) > 0) {
                $returnData = $query->where(function($query) {
                                                $query->where('b.region_id', currentEmployee()?->organizationInfo?->region_id)
                                                    ->orWhereIn("branch_id", scopeMultipleBranch());
                                            })->get();
            } else {
                $returnData = $query->where('b.region_id', currentEmployee()?->organizationInfo?->region_id)->get();
            }
        } elseif(count(scopeMultipleBranch()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('branch_id', currentEmployee()?->organizationInfo?->branch_id)
                                                ->orWhereIn("branch_id", scopeMultipleBranch());
                                        })->get();
        } elseif(scopeBranch()) {
            $returnData = $query->where('branch_id', currentEmployee()?->organizationInfo?->branch_id)->get();
        } elseif(count(scopeMultipleSubBranch()) > 0) {
            $returnData = $query->where(function($query) {
                $query->where('id', currentEmployee()?->organizationInfo?->sub_branch_id)
                    ->orWhereIn("id", scopeMultipleSubBranch());
            })->get();
        } else {
            $returnData = $query->where('id', currentEmployee()?->organizationInfo?->sub_branch_id)->get();
        }
        return $this->returnData($returnData, $pluck);
    }

    //To fetch the departments as per scope
    public function fetchDepartment(int|string|null $company = null, ?string $pluck = "")
    {
        $query = Department::orderBy('name')->select('name', 'id');

        if ($company != null)
            $query->where("company_id", $company);

        if(isSuperAdmin() || scopeAll()) {
            $returnData = $query->get();
        } elseif (count(scopeMultipleCompany()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('company_id', currentEmployee()?->company_id)
                                                ->orWhereIn("company_id", scopeMultipleCompany());
                                        })->get();
        } elseif (scopeCompany() || scopeMultipleRegion() || scopeRegion() || scopeMultipleBranch() || scopeBranch() || scopeMultipleSubBranch() || scopeSubBranch()) {
            $returnData = $query->where('company_id', currentEmployee()?->company_id)->get();
        } elseif(count(scopeMultipleDepartment()) > 0) {
            $returnData = $query->where(function($query) {
                                            $query->where('id', currentEmployee()?->organizationInfo?->department_id)
                                                ->orWhereIn("id", scopeMultipleDepartment());
                                        })->get();
        } else {
            $returnData = $query->where('id', currentEmployee()?->organizationInfo?->department_id)->get();   
        }
        return $this->returnData($returnData, $pluck);
    }
}

