<?php
// app/Services/Analytics/ActiveWorkforceService.php

namespace App\Http\Services\Dashboard;

use App\Http\Helpers\DateRanges;
use App\Http\Repositories\Dashboard\HrAnalyticsRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Cache;

class HrAnalyticsService
{
    public function __construct(
        private HrAnalyticsRepository $repo
    ) {
    }


    public function twoWeekActiveTrend(?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        // compare up to yesterday, since no data for today, will be stale
        $end = Carbon::yesterday()->startOfDay();
        $start = (clone $end)->subDays(13);

        $cacheKey = sprintf(
            'active-trend:%s:%s:%s',
            $start->toDateString(),
            $end->toDateString(),
            md5(json_encode([$branchId, $departmentId, $companyId]))
        );

        return HrAnalyticsCache::remember($cacheKey, function () use ($start, $end, $branchId, $departmentId, $companyId) {
            $map = $this->repo->dailyActiveCounts($start, $end, $branchId, $departmentId, $companyId);

            $period = CarbonPeriod::create($start, $end);
            $dates = [];
            $values = [];

            foreach ($period as $d) {
                $key = $d->toDateString();
                $dates[] = $key;
                $values[] = (int) ($map[$key] ?? 0);
            }

            // "Mon (Oct 27)"
            $categories = array_map(function ($d) {
                return Carbon::parse($d)->format('D (M j)');
            }, $dates);

            return [
                'categories' => $categories,
                'dates' => $dates,
                'dividerIndex' => 7,
                'series' => [
                    ['name' => 'Active Employees', 'data' => $values] // single line across 14 days

                ],
            ];
        });
    }

    public function weekVsWeekHireAndFire(string $metric, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        [$thisWeekStart, $thisWeekEnd] = DateRanges::resolve();
        [$previousWeekStart, $previousWeekEnd] = DateRanges::previousWeek([$thisWeekStart, $thisWeekEnd]);

        $cacheKey = sprintf(
            'wk2wk:%s:%s:%s:%s',
            $metric,
            $branchId ?? 'all',
            $departmentId ?? 'all',
            md5(json_encode([$thisWeekStart, $thisWeekEnd, $previousWeekStart, $previousWeekEnd, $companyId]))
        );

        return HrAnalyticsCache::remember($cacheKey, function () use ($metric, $previousWeekStart, $previousWeekEnd, $thisWeekStart, $thisWeekEnd, $branchId, $departmentId, $companyId) {
            $mapPrev = $metric === 'hires'
                ? $this->repo->newJoinsByDate($previousWeekStart, $previousWeekEnd, $branchId, $departmentId, $companyId)
                : $this->repo->terminationsByDate($previousWeekStart, $previousWeekEnd, $branchId, $departmentId, $companyId);

            $mapThisWeek = $metric === 'hires'
                ? $this->repo->newJoinsByDate($thisWeekStart, $thisWeekEnd, $branchId, $departmentId, $companyId)
                : $this->repo->terminationsByDate($thisWeekStart, $thisWeekEnd, $branchId, $departmentId, $companyId);

            $prevDates = iterator_to_array(CarbonPeriod::create($previousWeekStart, $previousWeekEnd));
            $currDates = iterator_to_array(CarbonPeriod::create($thisWeekStart, $thisWeekEnd));

            $prevData = [];
            $currData = [];
            $weekdayLabels = [];
            $currMetaDates = [];

            for ($i = 0; $i < 7; $i++) {
                $pd = $prevDates[$i];
                $cd = $currDates[$i];

                $weekdayLabels[$i] = $pd->format('D'); // Mon..Sun
                $prevMetaDates[$i] = $pd->toDateString();
                $currMetaDates[$i] = $cd->toDateString();

                $prevData[$i] = (int) ($mapPrev[$pd->toDateString()] ?? 0);
                $currData[$i] = (int) ($mapThisWeek[$cd->toDateString()] ?? 0);

            }

            return [
                'categories' => $weekdayLabels,
                'series' => [
                    ['name' => 'Previous Week', 'data' => $prevData, 'metaDates' => $prevMetaDates],
                    ['name' => 'This Week', 'data' => $currData, 'metaDates' => $currMetaDates],
                ],
                'meta' => [
                    'prevRange' => [$previousWeekStart->toDateString(), $previousWeekEnd->toDateString()],
                    'currRange' => [$thisWeekStart->toDateString(), $thisWeekEnd->toDateString()],
                ],
            ];
        });
    }

    public function twoWeekAttendanceCompliance(?int $branchId = null, ?int $departmentId = null, ?int $companyId = null)
    {
        $end = Carbon::yesterday()->startOfDay();
        $start = (clone $end)->subDays(13);

        $cacheKey = sprintf(
            'att-compliance:%s:%s:%s',
            $start->toDateString(),
            $end->toDateString(),
            md5(json_encode([$branchId, $departmentId, $companyId]))
        );

        return HrAnalyticsCache::remember($cacheKey, function () use ($start, $end, $branchId, $departmentId, $companyId) {
            $maps = $this->repo->attendanceMetricsByDate($start, $end, $branchId, $departmentId, $companyId);

            $period = CarbonPeriod::create($start, $end);
            $dates = [];
            $cats = [];
            $absent = [];
            $lateIn = [];
            $earlyOut = [];
            $leave = [];
            $present = [];
            $punctual = [];

            foreach ($period as $d) {
                $key = $d->toDateString();
                $dates[] = $key;
                $cats[] = $d->format('D (M j)');

                $absent[] = (int) ($maps['absent'][$key] ?? 0);
                $lateIn[] = (int) ($maps['lateIn'][$key] ?? 0);
                $earlyOut[] = (int) ($maps['earlyOut'][$key] ?? 0);
                $leave[] = (int) ($maps['leave'][$key] ?? 0);
                $present[] = (int) ($maps['present'][$key] ?? 0);
                $punctual[] = (int) ($maps['punctual'][$key] ?? 0);
            }

            return [
                'categories' => $cats,
                'dates' => $dates,
                'dividerIndex' => 7,
                'metrics' => [
                    'absent' => $absent,
                    'lateIn' => $lateIn,
                    'earlyOut' => $earlyOut,
                    'leave' => $leave,
                    'present' => $present,
                    'punctual' => $punctual,
                ],
            ];
        });
    }

    public function twoWeekTurnOverTrend(?int $branchId = null, ?int $departmentId = null, ?int $companyId = null)
    {
        $end = Carbon::yesterday()->startOfDay();
        $start = (clone $end)->subDays(13);

        $cacheKey = sprintf(
            'turnover-trend:%s:%s:%s',
            $start->toDateString(),
            $end->toDateString(),
            md5(json_encode([$branchId, $departmentId, $companyId]))
        );

        return HrAnalyticsCache::remember($cacheKey, function () use ($start, $end, $branchId, $departmentId, $companyId) {
            $newJoin = $this->repo->newJoinsByDate($start, $end, $branchId, $departmentId, $companyId);
            $termination = $this->repo->terminationsByDate($start, $end, $branchId, $departmentId, $companyId);

            $period = CarbonPeriod::create($start, $end);
            $dates = [];
            $cats = [];
            $newJoins = [];
            $terminations = [];

            foreach ($period as $d) {
                $key = $d->toDateString();
                $dates[] = $key;
                $cats[] = $d->format('D (M j)');

                $newJoins[] = (int) ($newJoin[$key] ?? 0);
                $terminations[] = (int) ($termination[$key] ?? 0);
            }


            return [
                'categories' => $cats,
                'dates' => $dates,
                'dividerIndex' => 7,
                'series' => [
                    ['name' => 'New Joins', 'data' => $newJoins],
                    ['name' => 'Terminations', 'data' => $terminations],
                ],
            ];
        });
    }

}
