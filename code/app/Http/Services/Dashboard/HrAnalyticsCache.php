<?php

namespace App\Http\Services\Dashboard;

use Illuminate\Support\Facades\Cache;

class HrAnalyticsCache
{
    public static function remember($cacheKey, $callback, $minutes = 12 * 60)
    {
        return Cache::tags('hr-analytics')->remember($cacheKey, now()->addMinutes($minutes), $callback);
    }

    public static function flushCache()
    {
        Cache::tags('hr-analytics')->flush();
    }
}