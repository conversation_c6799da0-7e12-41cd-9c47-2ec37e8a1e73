<?php

namespace App\Http\Services\Dashboard;

use App\Http\Helpers\DateRanges;
use App\Http\Repositories\Dashboard\HrAnalyticsRepository;

class HrAnalyticsKPIService
{

    public function __construct(
        private HrAnalyticsRepository $repo,
    ) {}

    /* ----------------------------- PUBLIC KPIs ----------------------------- */

    public function absenteeismRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        $cacheKey = $this->cacheKeyGenerator('absenteeismRateKpi', $start, $end, $branchId, $departmentId, $companyId);
        return HrAnalyticsCache::remember(
            $cacheKey,
            fn() => $this->rangeKpi(
                fn($s, $e, $b, $d, $c) => $this->absenteeismRate($s, $e, $b, $d, $c),
                $start,
                $end,
                $companyId,
                $branchId,
                $departmentId,
                higherIsGood: false,
                formatPercent: true,
                decimals: 2
            )
        );
    }

    public function turnoverRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        $cacheKey = $this->cacheKeyGenerator('turnoverRateKpi', $start, $end, $branchId, $departmentId, $companyId);
        return HrAnalyticsCache::remember(
            $cacheKey,
            fn() => $this->rangeKpi(
                fn($s, $e, $b, $d, $c) => $this->turnoverRate($s, $e, $b, $d, $c),
                $start,
                $end,
                $companyId,
                $branchId,
                $departmentId,
                higherIsGood: false,
                formatPercent: true,
                decimals: 2
            )
        );
    }

    public function averageActiveWorkforceKPI(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->rangeKpi(
            fn($s, $e, $b, $d, $c) => $this->averageActiveWorkforce($s, $e, $b, $d, $c),
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: true,
            formatPercent: false,
            decimals: 2
        );
    }

    public function presentRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'present',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: true
        );
    }

    public function absentRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'absent',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false
        );
    }

    public function lateInRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'lateIn',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false
        );
    }

    public function lateOutRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'lateOut',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false
        );
    }

    public function earlyOutRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'earlyOut',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false
        );
    }

    public function punctualityRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'punctual',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: true,
        );
    }

    public function leaveRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->attendanceAvgKpi(
            'leave',
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false
        );
    }

    public function hiringRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->rangeKpi(
            fn($s, $e, $b, $d, $c) => $this->avgEmployeeCountField($s, $e, $b, $d, $c, 'new_join_employees_count'),
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: true,
            formatPercent: false,
            decimals: 2
        );
    }

    public function terminationRateKpi(?string $start = null, ?string $end = null, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        return $this->rangeKpi(
            fn($s, $e, $b, $d, $c) => $this->avgEmployeeCountField($s, $e, $b, $d, $c, 'terminated_employees_count'),
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: false,
            formatPercent: false,
            decimals: 2
        );
    }

    /* --------------------------- METRIC CALCULATORS --------------------------- */

    private function absenteeismRate($start, $end, ?int $branchId, ?int $departmentId, ?int $companyId): ?float
    {
        $cacheKey = $this->cacheKeyGenerator('absenteeismRate', $start, $end, $branchId, $departmentId, $companyId);
        return HrAnalyticsCache::remember(
            $cacheKey,
            function () use ($start, $end, $branchId, $departmentId, $companyId) {
                $absent = $this->repo->absentEmployeeDays($start, $end, $branchId, $departmentId, $companyId);
                $total = $this->repo->totalEmployeeDays($start, $end, $branchId, $departmentId, $companyId);
                if ($total <= 0)
                    return null;
                return round(($absent / $total) * 100, 2);
            }
        );
    }

    private function turnoverRate($start, $end, ?int $branchId, ?int $departmentId, ?int $companyId): ?float
    {
        $cacheKey = $this->cacheKeyGenerator('turnoverRate', $start, $end, $branchId, $departmentId, $companyId);
        return HrAnalyticsCache::remember(
            $cacheKey,
            function () use ($companyId, $start, $end, $branchId, $departmentId) {
                $rows = $this->repo->getEmployeeCounts($start, $end, $branchId, $departmentId, $companyId);
                if ($rows->isEmpty())
                    return null;

                $terminated = (int) $rows->sum('terminated_employees_count');
                $avgActive = (float) $rows->avg(fn($r) => (int) $r->active_employees_count);
                if ($avgActive <= 0.0)
                    return null;

                return round(($terminated / $avgActive) * 100, 2);
            }
        );
    }

    private function averageActiveWorkforce($start, $end, ?int $branchId, ?int $departmentId, ?int $companyId = null): ?float
    {
        $cacheKey = $this->cacheKeyGenerator('avgActive', $start, $end, $branchId, $departmentId, $companyId);
        return HrAnalyticsCache::remember(
            $cacheKey,
            function () use ($start, $end, $branchId, $departmentId, $companyId) {
                $rows = $this->repo->getEmployeeCounts($start, $end, $branchId, $departmentId, $companyId);
                if ($rows->isEmpty())
                    return null;
                return round((float) $rows->avg(fn($r) => (int) $r->active_employees_count), 2);
            }
        );
    }

    private function avgEmployeeCountField($start, $end, ?int $branchId, ?int $departmentId, ?int $companyId, string $field): ?float
    {
        $cacheKey = $this->cacheKeyGenerator('avgEmployeeCountField', $start, $end, $branchId, $departmentId, $companyId, $field);
        return HrAnalyticsCache::remember(
            $cacheKey,
            function () use ($start, $end, $branchId, $departmentId, $companyId, $field) {
                $rows = $this->repo->getEmployeeCounts($start, $end, $branchId, $departmentId, $companyId);
                if ($rows->isEmpty())
                    return null;
                return round((float) $rows->avg(fn($r) => (int) $r->{$field}), 2);
            }
        );
    }

    private function avgAttendanceMetric($start, $end, ?int $branchId, ?int $departmentId, ?int $companyId, string $key): ?float
    {
        return HrAnalyticsCache::remember(
            $this->cacheKeyGenerator('avgAttendanceMetric', $start, $end, $branchId, $departmentId, $companyId, $key),
            function () use ($start, $end, $branchId, $departmentId, $companyId, $key) {
                $rows = $this->repo->attendanceMetricsByDate($start, $end, $branchId, $departmentId, $companyId);
                if (!isset($rows[$key]))
                    return null;
                $avg = collect(array_values($rows[$key]))->avg();
                return is_null($avg) ? null : round($avg, 2);
            }
        );
    }

    private function attendanceAvgKpi(string $metricKey, ?string $start, ?string $end, ?int $companyId, ?int $branchId, ?int $departmentId, bool $higherIsGood): array
    {
        $fn = fn($s, $e, $b, $d, $c) => $this->avgAttendanceMetric($s, $e, $b, $d, $c, $metricKey);

        return $this->rangeKpi(
            $fn,
            $start,
            $end,
            $companyId,
            $branchId,
            $departmentId,
            higherIsGood: $higherIsGood,
            formatPercent: false,
            decimals: 0
        );
    }

    /* ---------------------------- KPI COMPOSER ---------------------------- */

    /**
     * if needed separate logic, create separately.
     * Shared KPI builder: computes current vs previous-week, deltas, labels, trend.
     */
    private function rangeKpi(
        callable $calc,
        ?string $start,
        ?string $end,
        ?int $companyId,
        ?int $branchId,
        ?int $departmentId,
        bool $higherIsGood,
        bool $formatPercent,
        int $decimals
    ): array {
        [$s, $e] = DateRanges::resolve($start, $end);
        [$ps, $pe] = DateRanges::previousWeek([$s, $e]);

        $curr = $calc($s, $e, $branchId, $departmentId, $companyId);
        $prev = $calc($ps, $pe, $branchId, $departmentId, $companyId);

        $rel = $this->relativeChange($curr, $prev);
        $pp = $prev === null ? null : round($curr - $prev, 0);

        // value formatting policy: only some KPIs use % text; others return numeric
        $currentOut = $formatPercent ? $this->fmtPct($curr, $decimals) : round($curr, $decimals);
        $previousOut = $formatPercent ? $this->fmtPct($prev, $decimals) : round($curr, $decimals);

        return [
            'value' => $currentOut,
            'last_week' => $previousOut,
            'delta_percent' => $rel,
            'delta_pp' => $pp,
            'trend' => $this->trend($rel),
            'label' => $this->deltaLabel($rel, 'from last week'),
            'label_pp' => $pp !== null ? $this->ppLabel($pp, 'from last week') : null,
            'type' => $this->typeFromDirection($rel, $higherIsGood),
        ];
    }

    private function typeFromDirection(?float $rel, bool $higherIsGood): string
    {
        if (is_null($rel))
            return 'flat';
        $improved = $higherIsGood ? ($rel > 0) : ($rel < 0);
        return $improved ? 'good' : 'bad';
    }

    /* ------------------------------ HELPERS ------------------------------ */

    private function cacheKeyGenerator(string $key, ...$args)
    {
        return sprintf('metric.%s:%s', $key, md5(json_encode($args)));
    }

    private function relativeChange(?float $current, ?float $previous): ?float
    {
        if ($current === null || $previous === null || $previous == 0.0)
            return null;
        return round((($current - $previous) / $previous) * 100, 1);
    }

    private function fmtPct(?float $v, int $dec = 1): string
    {
        return is_null($v) ? '--%' : number_format($v, $dec) . '%';
    }

    private function trend(?float $rel): string
    {
        if (is_null($rel))
            return 'flat';
        return $rel > 0 ? 'up' : ($rel < 0 ? 'down' : 'flat');
    }

    private function deltaLabel(?float $rel, string $suffix): ?string
    {
        if (is_null($rel))
            return null;
        $sign = $rel > 0 ? '+' : '';
        return "{$sign}{$rel}% {$suffix}";
    }

    private function ppLabel(float $pp, string $suffix): string
    {
        $sign = $pp > 0 ? '+' : '';
        return "{$sign}{$pp} {$suffix}";
    }
}
