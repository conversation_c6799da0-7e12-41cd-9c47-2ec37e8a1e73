<?php

namespace App\Http\Requests;

use <PERSON>Bright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Foundation\Http\FormRequest;

class TimeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "nep_date"                  => ["required", "date_format:Y-m-d", function ($attribute, $value, $fail) {
                try {
                    $todayNepaliDate = LaravelNepaliDate::from(\Carbon\Carbon::today())->toNepaliDate();
                    $nepaliDate = $value;
                    $comparison = compareNepaliDates($todayNepaliDate, $nepaliDate);
                    if ($comparison == -1) {
                        $fail("The date should not be greater than today.");
                    }
                } catch (\Exception $e) {
                    $fail("Invalid date format provided.");
                }
            }],
            'actual_in_time'             => 'required_without:actual_out_time|nullable|date_format:H:i',
            'actual_in_time_remarks'    => 'required_with:actual_in_time|max:255',
            'actual_out_time'           => 'required_without:actual_in_time|nullable|date_format:H:i',
            'actual_out_time_remarks'   => 'required_with:actual_out_time|max:255',
            'verifier_id'               => 'required',
            'documents'                 => 'nullable|array',
            'documents.*'               => 'mimes:pdf,doc,docx,jpeg,png,jpg|max:2048',
            'removing_document_ids'     => 'nullable|array',
            'removing_document_ids.*'   => 'exists:ticket_documents,id'
        ];
    }

    public function messages(): array
    {
        return [
            'nep_date.required'                     => 'The Nepali date is required.',
            'nep_date.date_format'                  => 'The Nepali date must be in the format YYYY-MM-DD.',
            'actual_in_time.required_without'       => 'The actual in-time is required if out-time is not provided.',
            'actual_in_time.date_format'            => 'The in-time must be in HH:MM (24-hour) format.',
            'actual_out_time.required_without'      => 'The actual out-time is required if in-time is not provided.',
            'actual_out_time.date_format'           => 'The out-time must be in HH:MM (24-hour) format.',
            'actual_in_time_remarks.required_with'  => 'Please provide a remark for the in-time.',
            'actual_in_time_remarks.max'            => 'The in-time remark must not exceed 255 characters.',
            'actual_out_time_remarks.required_with' => 'Please provide a remark for the out-time.',
            'actual_out_time_remarks.max'           => 'The out-time remark must not exceed 255 characters.',
            'verifier_id.required'                  => 'Please select a verifier.',
            'documents.array'                       => 'Uploaded documents must be in a array.',
            'documents.*.mimes'                     => 'Each document must be a PDF, Word, or image file (jpeg, png, jpg).',
            'documents.*.max'                       => 'Each document must not exceed 2MB in size.',
            'removing_document_ids.array'           => 'Invalid format for documents to remove.',
            'removing_document_ids.*.exists'        => 'One or more of the selected documents to remove do not exist.',
        ];
    }
}
