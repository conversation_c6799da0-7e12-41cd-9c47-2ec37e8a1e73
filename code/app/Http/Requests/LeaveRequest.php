<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'leave_type_id'             => 'required',
            'leave_option_id'           => 'required',
            'nep_start_date'            => 'required',
            'nep_end_date'              => 'required',
            'verifier_id'               => 'required',
            'remarks'                   => 'required',
            'documents'                 => 'nullable|array',
            'documents.*'               => 'mimes:pdf,doc,docx,jpeg,png,jpg|max:2048',
            'removing_document_ids'     => 'nullable|array',
            'removing_document_ids.*'   => 'exists:ticket_documents,id'
        ];
    }

    public function messages()
    {
        return [
            'leave_type_id.required'                => 'Please select a leave type.',
            'leave_option_id.required'              => 'Please select a leave option.',
            'nep_start_date.required'               => 'The start date is required.',
            'nep_end_date.required'                 => 'The end date is required.',
            'verifier_id.required'                  => 'Please select a verifier.',
            'remarks.required'                      => 'Please enter your remarks.',
            'documents.array'                       => 'Uploaded documents must be in a array.',
            'documents.*.mimes'                     => 'Each document must be a PDF, Word, or image file (jpeg, png, jpg).',
            'documents.*.max'                       => 'Each document must not exceed 2MB in size.',
            'removing_document_ids.array'           => 'Invalid format for documents to remove.',
            'removing_document_ids.*.exists'        => 'One or more of the selected documents to remove do not exist.',
        ];
    }
}
