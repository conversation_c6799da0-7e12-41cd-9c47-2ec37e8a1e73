<?php

namespace App\Http\Repositories;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\EmployeeOrg;
use App\Models\Tickets\EdfRequest;

class SuggestedEmployeeCode extends Repository
{
    public function get()
    {
        list($year, $month) = explode('-', LaravelNepaliDate::from(now())->toNepaliDate('Y-m'));
        $yearMonthPrefix = $year . $month;
        $maxEmployeeCodeFromOrg =  EmployeeOrg::where('employee_code', 'like', "{$yearMonthPrefix}%")
            ->orderBy('employee_code', 'desc')
            ->value('employee_code');
        $maxEmployeeCodeFromTicket = EdfRequest::where('employee_code', 'like', "{$yearMonthPrefix}%")
            ->orderBy('employee_code', 'desc')
            ->value('employee_code');

        $maxEmployeeCode = max($maxEmployeeCodeFromOrg, $maxEmployeeCodeFromTicket);
        $dateCode = (int)substr($maxEmployeeCode, 0, 6);
        $maxEmployeeId = (int)substr($maxEmployeeCode, 6);

        $maxFrom = "employee_org";
        if ($maxEmployeeCodeFromTicket > $maxEmployeeCodeFromOrg) $maxFrom = "ticket";

        // Reset the max employee id to 0 if the date code is small tha current year and month
        if ($dateCode < $yearMonthPrefix)
            $maxEmployeeId = 0;

        $newEmployeeCode = "";
        do {
            $maxEmployeeId += 1;
            $newEmployeeId = str_pad($maxEmployeeId, 4, '0', STR_PAD_LEFT);
            $newEmployeeCode = $year . $month . $newEmployeeId;

            if ($maxFrom == "employee_org") {
                $exists = EmployeeOrg::where("employee_code", $newEmployeeCode)->first();
            } elseif ($maxFrom == "ticket") {
                $exists = EdfRequest::where('employee_code', $newEmployeeCode)
                    ->whereNotIn('state', [WorkflowState::APPROVED, WorkflowState::REJECTED, WorkflowState::CANCELLED])
                    ->exists();
            }
        } while ($exists);
        return $newEmployeeCode;
    }
}
