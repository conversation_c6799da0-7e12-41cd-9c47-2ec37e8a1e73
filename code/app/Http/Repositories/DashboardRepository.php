<?php

namespace App\Http\Repositories;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use App\Http\Repositories\Snapshots\AttendanceSnapshotAnalyticsRepository;
use App\Models\configs\Department;
use App\Models\configs\EmployeeShift;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\LeaveRequest;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\Employee;
use App\Http\Repositories\Interfaces\IDashboardRepository;
use App\Models\EmployeeCategory;
use App\Http\Repositories\Snapshots\EmployeeSnapshotAnalyticsRepository;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DashboardRepository extends Repository implements IDashboardRepository
{
    protected $date_eng, $date_nep, $emp_id;

    private EmployeeSnapshotAnalyticsRepository $empSnapshotRepo;
    private AttendanceSnapshotAnalyticsRepository $attSnapshotRepo;
    public function __construct()
    {
        $this->empSnapshotRepo = new EmployeeSnapshotAnalyticsRepository();
        $this->attSnapshotRepo = new AttendanceSnapshotAnalyticsRepository();
        $this->date_eng = now()->format('Y-m-d');
        $this->date_nep = LaravelNepaliDate::from($this->date_eng)->toNepaliDate();
        $this->emp_id = currentEmployee()?->id;
    }

    private function getNepaliMonthDateRange()
    {
        $nepaliDateParts = explode('-', $this->date_nep);

        $daysInMonth = LaravelNepaliDate::daysInMonth($nepaliDateParts[1], $nepaliDateParts[0]);
        $startOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], 1);
        $endOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], $daysInMonth);

        return [
            'today' => now()->format('Y-m-d'),
            'start' => $startOfMonth->toEnglishDate(),
            'end' => $endOfMonth->toEnglishDate(),
        ];
    }

    private function getEmployeesCount(int $regionId = null, int $branchId = null)
    {
        $query = Employee::leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')->leftJoin('employee_categories as ec', 'org.employee_category_id', '=', 'ec.id')->where("ec.is_countable", "1")->whereNull('org.termination_date')->whereNotNull('org.employee_category_id');
        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

        return $query->count();
    }

    private function getNewEmployeesCount(int $regionId = null, int $branchId = null)
    {
        $dateRange = $this->getNepaliMonthDateRange();

        $query = Employee::leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->whereNull('termination_date')
            ->whereBetween('org.doj', [$dateRange['start'], $dateRange['today']]);

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId);

        return $query->count();
    }

    private function getTerminatingEmployeesCount(int $regionId = null, int $branchId = null)
    {
        $dateRange = $this->getNepaliMonthDateRange();

        $query = Employee::leftJoin('employee_terminations as termination', 'termination.employee_id', 'employees.id')
            ->leftJoin('employee_org as org', 'employees.id', 'org.employee_id')
            ->where('termination.state', WorkflowState::APPROVED)
            ->whereNull('termination.deleted_at')
            ->whereBetween('termination.termination_date', [$dateRange['today'], $dateRange['end']])
            ->select(
                'termination.termination_date',
                'termination.termination_request_date',
                'org.region_id'
            );

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

        return $query->count();
    }

    private function getTerminatedEmployeesCount(int $regionId = null, int $branchId = null)
    {
        $dateRange = $this->getNepaliMonthDateRange();

        $query = Employee::onlyTrashed()
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->whereBetween('org.termination_date', [$dateRange['start'], $dateRange['today']])
            ->select(
                'org.termination_date',
                'org.termination_reason'
            );

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

        return $query->count();
    }

    // public function getEmployeeDetails(int $regionId = null, int $branchId = null): array
    // {
    //     $cacheKey = "employee_details_{$this->date_nep}_{$this->emp_id}";

    //     return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId) {
    //         return [
    //             'total_employees' => $this->getEmployeesCount($regionId, $branchId),
    //             'new_employees' => $this->getNewEmployeesCount($regionId, $branchId),
    //             'terminating' => $this->getTerminatingEmployeesCount($regionId, $branchId),
    //             'terminated' => $this->getTerminatedEmployeesCount($regionId, $branchId),
    //         ];
    //     });
    // }
    public function getEmployeeDetails(int $regionId = null, int $branchId = null, $baseDate = null): array
    {
        $baseDate = $baseDate ?? now();
        $employeeId = currentEmployeeId();
        $cacheKey = "employee_details_{$baseDate}_{$regionId}_{$branchId}_{$employeeId}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId, $baseDate) {
            $baseDate = $baseDate instanceof Carbon ? $baseDate : Carbon::parse($baseDate);
            if ($baseDate->isToday()) {
                return [
                    'total_employees' => $this->getEmployeesCount($regionId, $branchId, $baseDate),
                    'new_employees' => $this->getNewEmployeesCount($regionId, $branchId, $baseDate),
                    'terminating' => $this->getTerminatingEmployeesCount($regionId, $branchId, $baseDate),
                    'terminated' => $this->getTerminatedEmployeesCount($regionId, $branchId, $baseDate),
                    ...$this->getEmployeeCountByTypes($regionId, $branchId, $baseDate)
                ];
            } else {

                return [
                    'total_employees' => $this->empSnapshotRepo->getActiveEmployeeCount(branchId: $branchId, dateEn: $baseDate),
                    'new_employees' => $this->empSnapshotRepo->getNewJoinEmployeeCount(branchId: $branchId, dateEn: $baseDate),
                    'terminating' => $this->empSnapshotRepo->getTerminatingEmployeeCount(branchId: $branchId, dateEn: $baseDate),
                    'terminated' => $this->empSnapshotRepo->getTerminatedEmployeeCount(branchId: $branchId, dateEn: $baseDate),
                    ...$this->empSnapshotRepo->getActiveEmployeeCountByCategory(branchId: $branchId, dateEn: $baseDate),
                ];
            }
        });
    }

    public function getEmployeeCountByTypes(int $regionId = null, int $branchId = null, $baseDate = null)
    {
        $allTypes = EmployeeCategory::all()->where('is_countable', true)->pluck('type', 'id')->toArray();

        $query = EmployeeOrg::query()
            ->select(
                'employee_categories.type',
                'employee_categories.id',
                DB::raw('COUNT(*) as count')
            )
            ->leftJoin('employee_categories', 'employee_org.employee_category_id', '=', 'employee_categories.id')
            ->whereNull('employee_org.termination_date')
            ->groupBy('employee_categories.type', 'employee_categories.id');

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'employee_org');

        $employeeCounts = $query->get()->mapWithKeys(function ($item) {
            // $key = Str::snake($item->type);
            return [$item->type => $item->count];
        })->toArray();
        $defaultCounts = collect($allTypes)
            ->mapWithKeys(function ($type) {
                $key = Str::snake($type);
                return [$key => 0];
            })
            ->toArray();
        $employeeCounts = array_merge($defaultCounts, $employeeCounts);
        return $employeeCounts;
    }
    private function getPresentCount(string $date_nep, int $regionId = null, int $branchId = null)
    {
        $query = Attendance::where('date_np', $date_nep)
            ->where(function ($q) {
                $q->whereNotNull('in_time')->orWhereNotNull('out_time');
            })
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance.employee_id');

        return $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
    }

    private function getShiftNotStartedCount(string $date_nep, int $regionId = null, int $branchId = null)
    {
        $query = Attendance::leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', 'leave_types.id')
            ->where('attendance.date_np', $date_nep)
            ->where(function ($query) {
                $query->whereNull('attendance.in_time')
                    ->whereNull('attendance.out_time')
                    ->where(function ($q) {
                        $q->whereNull('attendance.leave_request_id')
                            ->orWhere(function ($q2) {
                                $q2->where('leave_types.paid', 0)
                                    ->orWhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                            });
                    })
                    ->where('attendance.status', 'Shift Not Started');
            });

        return $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
    }

    private function getAbsentCount(string $date_nep, int $regionId = null, int $branchId = null)
    {
        $query = Attendance::leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', 'leave_types.id')
            ->where('attendance.date_np', $date_nep)
            ->where(function ($query) {
                $query->whereNull('attendance.in_time')
                    ->whereNull('attendance.out_time')
                    ->where(function ($q) {
                        $q->whereNull('attendance.leave_request_id')
                            ->orWhere(function ($q2) {
                                $q2->where('leave_types.paid', 0)
                                    ->orWhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                            });
                    })
                    ->where(function ($q) {
                        $q->whereNull('attendance.status')
                            ->orWhereNotIn('attendance.status', ['Day Off', 'On Holiday', 'Shift Not Started']);
                    });
            });

        return $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
    }
    private function getPunctualCount(string $date_nep, int $regionId = null, int $branchId = null)
    {
        $query = Attendance::where('attendance.date_np', $date_nep)
            ->whereNotNull('attendance.in_time')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance.employee_id')
            ->leftJoin('shifts', 'shifts.id', '=', 'attendance.shift_id');

        $query->whereRaw("attendance.in_time <= ADDTIME(shifts.start_time, SEC_TO_TIME(shifts.grace_time * 60))");

        $query->where('shifts.is_active', 1)
            ->where(function ($q) {
                $q->whereNull('shifts.day_off')->orWhere('shifts.day_off', 0);
            });
        return $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
    }


    private function getOnLeaveDetails(string $date_nep, int $regionId = null, int $branchId = null): array
    {
        $query = LeaveRequest::where('nep_start_date', '<=', $date_nep)
            ->where('nep_end_date', '>=', $date_nep)
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'leave_requests.employee_id');

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
        $finalStates = ArflowHelper::getFinalStates(WorkflowName::LEAVE_APPROVAL);
        $finalStatesStr = implode("','", $finalStates);
        $results = $query
            ->select(
                'leave_types.name as leave_type_name',
                // DB::raw('COUNT(*) as total'),
                DB::raw("SUM(CASE WHEN leave_requests.state IN ('Approved', 'Assigned') THEN 1 ELSE 0 END) as approved"),
                DB::raw("SUM(CASE WHEN leave_requests.state NOT IN ('$finalStatesStr') THEN 1 ELSE 0 END) as unapproved")
            )
            ->groupBy('leave_types.name')
            ->get();

        $data = [
            'types' => [],
            'total_approved_leaves' => 0,
            'total_unapproved_leaves' => 0,
            // 'total_leaves' => 0,
        ];

        foreach ($results as $row) {
            $data['types'][$row->leave_type_name] = [
                // 'total' => $row->total,
                'approved' => $row->approved,
                'unapproved' => $row->unapproved,
            ];

            // $data['total_leaves'] += $row->total;
            $data['total_approved_leaves'] += $row->approved;
            $data['total_unapproved_leaves'] += $row->unapproved;
        }

        return $data;
    }

    public function getDayOffCount(string $date_nep, int $regionId = null, int $branchId = null)
    {
        $query = Attendance::where('date_np', $date_nep)
            ->where('status', 'Day Off')
            ->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id');

        return $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');
    }

    public function getOnLeaveCount($selectedDate, $selectedBranch, $selectedDepartment = null, $selectedRegion = null)
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where(function ($query) {
                $query->whereNull('in_time')
                    ->whereNull('out_time')
                    ->where(function ($q) {
                        $q->whereNotNull('attendance.leave_request_id')
                            ->where('leave_types.paid', 1)
                            ->whereIn('leave_requests.state', ['Approved', 'Assigned']);
                    });
            });
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedRegion) {
            $query = $query->where('org.region_id', $selectedRegion);
        }
        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');

        return $query;
    }

    public function getAttendanceDetails(int $regionId = null, int $branchId = null, $filterSelectedDate = null): array
    {
        if ($filterSelectedDate == null) {
            $dateInNepali = $this->date_nep;
        } else {
            $dateInNepali = LaravelNepaliDate::from($filterSelectedDate)->toNepaliDate();
        }

        $cacheKey = "attendance_details_{$dateInNepali}_{$this->emp_id}";

        return Cache::store('dashboard')->remember($cacheKey, 15, function () use ($regionId, $branchId, $dateInNepali, $filterSelectedDate) {
            if ($dateInNepali == $this->date_nep) {
                // Breakdown counts by employee type
                $present = $this->countByEmployeeType(
                    $this->getPresentCount($dateInNepali, $regionId, $branchId)
                );

                $absent = $this->countByEmployeeType(
                    $this->getAbsentCount($dateInNepali, $regionId, $branchId)
                );

                $dayOff = $this->countByEmployeeType(
                    $this->getDayOffCount($dateInNepali, $regionId, $branchId)
                );

                $punctual = $this->countByEmployeeType(
                    $this->getPunctualCount($dateInNepali, $regionId, $branchId)
                );

                $shiftNotStarted = $this->countByEmployeeType(
                    $this->getShiftNotStartedCount($dateInNepali, $regionId, $branchId)
                );
                $onLeaveDetails = $this->getOnLeaveDetails($dateInNepali, $regionId, $branchId);

                // on leave logic comes from repo
                $repo = new AttendanceDashboardRepository();
                $onLeaveCount = $this->countByEmployeeType(
                    $this->getOnLeaveCount($dateInNepali, selectedRegion: $regionId, selectedBranch: $branchId)
                );
                $details = [
                    'present' => [
                        'total' => array_sum($present),
                        'types' => $present
                    ],
                    'absent' => [
                        'total' => array_sum($absent),
                        'types' => $absent
                    ],
                    'day_off' => [
                        'total' => ($dayOff),
                        'types' => $dayOff
                    ],
                    'punctual' => [
                        'total' => array_sum($punctual),
                        'types' => $punctual
                    ],
                    'shift_not_started' => [
                        'total' => array_sum($shiftNotStarted),
                        'types' => $shiftNotStarted
                    ],
                    'on_leave_details' => $onLeaveDetails,
                    'on_leave' => [
                        'total' => array_sum($onLeaveCount),
                        'types' => $onLeaveCount
                    ]
                ];
            } else {
                $details = $this->attSnapshotRepo->getAttendanceByCategoryType(
                    branchId: $branchId,
                    dateEn: $filterSelectedDate
                );
                $onLeaveDetails = $this->getOnLeaveDetails($dateInNepali, $regionId, $branchId);

                $details = [
                    ...$details,
                    'on_leave_details' => $onLeaveDetails,
                ];
                /*$present = $this->getPresentCount(
                    $this->getPresentCount($dateInNepali, $regionId, $branchId)
                );

                $absent = $this->countByEmployeeType(
                    $this->getAbsentCount($currentDateInNepali, $regionId, $branchId)
                );

                $dayOff = $this->countByEmployeeType(
                    $this->getDayOffCount($currentDateInNepali, $regionId, $branchId)
                );

                $punctual = $this->countByEmployeeType(
                    $this->getPunctualCount($currentDateInNepali, $regionId, $branchId)
                );

                $shiftNotStarted = $this->countByEmployeeType(
                    $this->getShiftNotStartedCount($currentDateInNepali, $regionId, $branchId)
                );
                $onLeaveDetails = $this->getOnLeaveDetails($currentDateInNepali, $regionId, $branchId);

                // on leave logic comes from repo
                $repo = new AttendanceDashboardRepository();
                $onLeaveCount = $this->countByEmployeeType(
                    $this->getOnLeaveCount($currentDateInNepali, selectedRegion: $regionId, selectedBranch: $branchId);*/
            }

            //            dd($filterSelectedDate, $details);
            return $details;
        });
    }


    private function countByEmployeeType($query)
    {
        return $query
            ->select('employee_categories.type', DB::raw('count(distinct attendance.employee_id) as total'))
            ->leftJoin('employee_categories', 'org.employee_category_id', '=', 'employee_categories.id')
            ->groupBy('org.employee_category_id', 'employee_categories.type')
            ->pluck('total', 'employee_categories.type')
            ->toArray();
    }



    public function getGenderMaritalStatusCounts(int $regionId = null, int $branchId = null): array
    {
        $cacheKey = "gender_marital_counts_{$this->date_nep}_{$this->emp_id}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId) {
            $query = Employee::select('gender', 'mstat', DB::raw('COUNT(*) as count'))
                ->whereIn('gender', ['male', 'female'])
                ->whereIn('mstat', ['married', 'unmarried'])
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id');

            $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

            $results = $query->groupBy('gender', 'mstat')->get();

            $formatted = [];

            foreach ($results as $row) {
                $formatted[$row->gender][$row->mstat] = $row->count;
            }

            return $formatted;
        });
    }
    public function getGenderAgeGroupCounts($regionId = null, $branchId = null)
    {
        $cacheKey = "gender_age_group_counts_{$this->date_nep}_{$this->emp_id}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId) {
            $query = Employee::select(
                'gender',
                DB::raw("
        CASE 
            WHEN TIMESTAMPDIFF(YEAR, dob, CURDATE()) BETWEEN 45 AND 49 THEN '45-49'
            WHEN TIMESTAMPDIFF(YEAR, dob, CURDATE()) BETWEEN 50 AND 54 THEN '50-54'
            WHEN TIMESTAMPDIFF(YEAR, dob, CURDATE()) BETWEEN 55 AND 59 THEN '55-59'
            WHEN TIMESTAMPDIFF(YEAR, dob, CURDATE()) >= 60 THEN '60 and above'
            ELSE 'Below 45'
        END AS age_group
    "),
                DB::raw('COUNT(*) as count')
            )
                ->whereIn(DB::raw('LOWER(gender)'), ['male', 'female'])
                ->whereNotNull('dob')
                ->where('dob', '<=', DB::raw('CURDATE()'))
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id');

            $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

            $results = $query->groupBy('gender', 'age_group')->get();



            $formatted = [];
            foreach ($results as $row) {
                $formatted[$row->gender][$row->age_group] = $row->count;
            }

            return $formatted;
        });
    }

    private function getActivePayslipsCount(int $regionId = null, int $branchId = null): int
    {
        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')
            ->where('status', 'Active');

        $query = $this->applyOrgFilters($query, 'payslips', $regionId, $branchId, 'org');

        return $query->count();
    }

    private function getExpiringPayslipCount(int $days, int $regionId = null, int $branchId = null): int
    {
        $today = Carbon::today();
        $expirationDate = $today->copy()->addDays($days)->format('Y-m-d');

        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')
            ->whereBetween('stop_date_eng', [$today->format('Y-m-d'), $expirationDate])
            ->where('status', 'Active');

        $query = $this->applyOrgFilters($query, 'payslips', $regionId, $branchId, 'org');

        return $query->count();
    }

    private function getExpiredPayslipsCount(int $days, int $regionId = null, int $branchId = null): int
    {
        $today = Carbon::today();
        $pastDate = $today->copy()->subDays($days)->format('Y-m-d');

        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')
            ->whereBetween('stop_date_eng', [$pastDate, $today->format('Y-m-d')])
            ->where('status', 'Expired');

        $query = $this->applyOrgFilters($query, 'payslips', $regionId, $branchId, 'org');

        return $query->count();
    }

    private function getEmployeeWithNoPayslipsCount(int $regionId = null, int $branchId = null): int
    {
        $query = Employee::leftJoin('payslips', 'employees.id', '=', 'payslips.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin('payslip_requests', 'employees.id', '=', 'payslip_requests.employee_id')
            ->where(function ($query) {
                $query->whereNull('payslip_requests.deleted_at')
                    ->whereNull('payslips.deleted_at');
            })
            ->where(function ($query) {
                $query->whereNull('payslips.employee_id')
                    ->whereNull('payslip_requests.employee_id');
            });

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId, 'org');

        return $query->distinct()->count('employees.id');
    }

    private function getPendingPayslipRequestCount(int $regionId = null, int $branchId = null): int
    {
        $query = PayslipRequest::leftJoin('employee_org as org', 'org.employee_id', 'payslip_requests.employee_id')
            ->whereNotIn('state', ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL));

        $query = $this->applyOrgFilters($query, 'payslip_requests', $regionId, $branchId, 'org');

        return $query->count();
    }

    public function getPayslipDetails(int $regionId = null, int $branchId = null): array
    {
        $cacheKey = "payslip_details_{$this->date_nep}_{$this->emp_id}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId) {
            return [
                'active' => $this->getActivePayslipsCount($regionId, $branchId),
                'expiring' => $this->getExpiringPayslipCount(30, $regionId, $branchId),
                'expired' => $this->getExpiredPayslipsCount(30, $regionId, $branchId),
                'no_payslip' => $this->getEmployeeWithNoPayslipsCount($regionId, $branchId),
                'pending_request' => $this->getPendingPayslipRequestCount($regionId, $branchId),
            ];
        });
    }

    public function getInhouseAndOutsourceCounts(int $regionId = null, int $branchId = null, int $companyId = null, string $dateEn = null)
    {
        $dateNep = $dateEn ? LaravelNepaliDate::from($dateEn)->toNepaliDate() : $this->date_nep;
        $dateEn = $dateEn ?: $this->date_eng;
        $cacheKey = "inhouse_outsource_counts_{$dateNep}_{$this->emp_id}_{$branchId}_{$companyId}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($regionId, $branchId, $dateNep, $dateEn) {
            $regularCategoryId = $this->getRegularCategoryId();

            if ($dateNep === $this->date_nep) {
                $inhouseQuery = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')->whereNull('org.termination_date')->whereNull('outsource_company_id')->where('org.employee_category_id', $regularCategoryId);
                $inhouseQuery = $this->applyOrgFilters($inhouseQuery, null, $regionId, $branchId, 'org');
                $inhouseEmpCount = $inhouseQuery->count();
                
                $outsourceQuery = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')->whereNull('org.termination_date')->whereNotNull('outsource_company_id')->where('org.employee_category_id', $regularCategoryId);
                $outsourceQuery = $this->applyOrgFilters($outsourceQuery, null, $regionId, $branchId, 'org');
                $outsourceEmpCount = $outsourceQuery->count();
    
                $inhousePresentCount = $this->getPresentCountOfInhouseAndOutSource($regionId, $branchId)['inhouse'];
                $outsourcePresentCount = $this->getPresentCountOfInhouseAndOutSource($regionId, $branchId)['outsource'];
    
                $inhouseAbsentCount = $this->getAbsentCountOfInhouseAndOutSource($regionId, $branchId)['inhouse'];
                $outsourceAbsentCount = $this->getAbsentCountOfInhouseAndOutSource($regionId, $branchId)['outsource'];
            } else {
                $employeeCountByType = $this->empSnapshotRepo->getActiveEmployeeCountByType(branchId: $branchId, dateEn: $dateEn);
                $inhouseEmpCount = $employeeCountByType['inhouse'];
                $outsourceEmpCount = $employeeCountByType['outsource'];

                $employeeAttendanceByType = $this->attSnapshotRepo->getAttendanceByEmploymentType(branchId: $branchId, dateEn: $dateEn);

                $inhousePresentCount = $employeeAttendanceByType['inhouse']['present_count'];
                $outsourcePresentCount = $employeeAttendanceByType['outsource']['present_count'];
                $inhouseAbsentCount = $employeeAttendanceByType['inhouse']['absent_count'];
                $outsourceAbsentCount = $employeeAttendanceByType['outsource']['absent_count']; 

            }


            return [
                'inhouse' => $inhouseEmpCount,
                'outsource' => $outsourceEmpCount,
                'inhouse_present' => $inhousePresentCount,
                'outsource_present' => $outsourcePresentCount,
                'inhouse_absent' => $inhouseAbsentCount,
                'outsource_absent' => $outsourceAbsentCount
            ];
        });
    }

    private function getAttendanceStatusCount(
        string $date,
        string $statusKeyword,
        int $regionId = null,
        int $branchId = null
    ): int {
        $query = Attendance::where('date_np', $date)
            ->where('status', 'like', '%' . $statusKeyword . '%')
            ->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id');

        $query = $this->applyOrgFilters($query, null, $regionId, $branchId);

        return $query->distinct()->count('attendance.employee_id');
    }

    public function getLateInEarlyOutDetails(int $regionId = null, int $branchId = null): array
    {
        $date = $this->date_nep;
        $cacheKey = "late_early_{$date}_{$this->emp_id}";

        $repo = new AttendanceDashboardRepository();

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($date, $regionId, $branchId, $repo) {
            return [
                'late_in_count' => $this->getAttendanceStatusCount($date, 'Late In', $regionId, $branchId),
                'early_out_count' => $this->getAttendanceStatusCount($date, 'Early Out', $regionId, $branchId),
                'punctual_count' => $repo->getPunctualCount($date, $regionId, $branchId),
            ];
        });
    }


    private function getPresentCountOfInhouseAndOutSource(int $regionId = null, int $branchId = null): array
    {
        $query = $this->getPresentCount($this->date_nep, $regionId, $branchId);
        $regularCategoryId = $this->getRegularCategoryId();

        $inhouseCount = (clone $query)->whereNull('org.outsource_company_id')->where('org.employee_category_id', $regularCategoryId)->distinct()->count('attendance.employee_id');
        $outsourceCount = (clone $query)->whereNotNull('org.outsource_company_id')->where('org.employee_category_id', $regularCategoryId)->distinct()->count('attendance.employee_id');

        return [
            'inhouse' => $inhouseCount,
            'outsource' => $outsourceCount,
        ];
    }

    private function getAbsentCountOfInhouseAndOutSource(int $regionId = null, int $branchId = null)
    {
        $query = $this->getAbsentCount($this->date_nep, $regionId, $branchId);
        $regularCategoryId = $this->getRegularCategoryId();

        $inhouseCount = (clone $query)->whereNull('org.outsource_company_id')->where('org.employee_category_id', $regularCategoryId)->distinct()->count('attendance.employee_id');
        $outsourceCount = (clone $query)->whereNotNull('org.outsource_company_id')->where('org.employee_category_id', $regularCategoryId)->distinct()->count('attendance.employee_id');

        return [
            'inhouse' => $inhouseCount,
            'outsource' => $outsourceCount,
        ];
    }

    /**
     * Apply scope and organization filters to the query.
     */
    private function applyOrgFilters($query, ?string $table = null, int $regionId = null, int $branchId = null, $orgInfoJoin = 'org')
    {
        if (!scopeAll()) {
            // if ($table) {
            //     $query->where("{$table}.company_id", currentEmployee()?->company_id);
            // }
            $query = filterEmployeesByScope($query, $orgInfoJoin);
        }

        if ($regionId) {
            $query->where('org.region_id', $regionId);
        }

        if ($branchId) {
            $query->where('org.branch_id', $branchId);
        }

        return $query;
    }


    public function getFilteredEmployeeCountsInJob(?int $departmentId = null): array
    {
        $cacheKey = "employee_counts_{$this->emp_id}_dept{$departmentId}";

        return Cache::store('dashboard')->remember($cacheKey, 60, function () use ($departmentId) {
            $query = DB::table('departments as d')
                ->join('job_departments as jd', 'jd.department_id', '=', 'd.id')
                ->join('jobs as j', 'j.id', '=', 'jd.job_id')
                ->leftJoin('payslips as p', function ($join) {
                    $join->on('p.job_id', '=', 'j.id')
                        ->where('p.status', 'active');
                })
                ->leftJoin('employee_org as org', function ($join) {
                    $join->on('org.employee_id', '=', 'p.employee_id')
                        ->on('org.department_id', '=', 'd.id');
                })
                ->when($departmentId, fn($q) => $q->where('d.id', $departmentId))
                ->groupBy('d.id', 'd.name', 'j.id', 'j.name')
                ->select([
                    'd.name as dept',
                    'j.name as job',
                    DB::raw('COUNT(DISTINCT org.id) as cnt'),
                ])
                ->orderBy('d.name')
                ->orderBy('cnt', 'desc');

            $query = filterEmployeesByScope($query, 'org', 'e');

            $rows = $query->get();

            $result = [];
            foreach ($rows->groupBy('dept') as $dept => $jobs) {
                $topJobs = $jobs->take(5);
                foreach ($topJobs as $r) {
                    $result[$dept][$r->job] = (int) $r->cnt;
                }
            }

            if ($departmentId) {
                $deptName = Department::whereKey($departmentId)->value('name');
                if ($deptName && !array_key_exists($deptName, $result)) {
                    $result[$deptName] = []; // department exists but has no linked jobs
                }
            } else {
                foreach (Department::pluck('name') as $name) {
                    $result[$name] = $result[$name] ?? []; // departments without jobs → empty
                }
            }

            return $result;
        });
    }
    public function getRegularCategoryId()
    {
        return EmployeeCategory::where('type', 'Regular')->value('id');
    }
}
