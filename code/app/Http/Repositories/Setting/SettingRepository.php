<?php

namespace App\Http\Repositories\Setting;

use App\Http\Repositories\Setting\Interfaces\SettingRepositoryInterface;
use App\Models\configs\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class SettingRepository implements SettingRepositoryInterface
{
    protected function cacheKey(string $namespace, string $key): string
    {
        return "settings:{$namespace}:{$key}";
    }

    protected function cacheStoreSupportsTags(): bool
    {
        // File cache doesn't support tags; redis/memcached do
        return method_exists(Cache::getStore(), 'tags');
    }

    protected function remember(string $cacheKey, \Closure $callback, ?int $seconds = null)
    {
        if ($this->cacheStoreSupportsTags()) {
            return Cache::tags(['settings'])->remember($cacheKey, $seconds, $callback);
        }
        return Cache::remember($cacheKey, $seconds, $callback);
    }

    protected function forgetCache(string $namespace, string $key): void
    {
        $ck = $this->cacheKey($namespace, $key);
        if ($this->cacheStoreSupportsTags()) {
            Cache::tags(['settings'])->forget($ck);
        } else {
            Cache::forget($ck);
        }
    }

    public function createOrUpdate(string $namespace, string $key, mixed $value, string $types): Setting
    {
        // Trim to DB limits just in case
        $namespace = Str::limit($namespace, 50, '');
        $key       = Str::limit($key, 50, '');

        if (!in_array($types, ['json', 'text', 'integer', 'boolean'])) {
            throw new \InvalidArgumentException("Invalid type: {$types}, Supported types are: json, text, integer, boolean");
        }

        // Serialize for storage
        [$storedValue, $castedForCache] = $this->serializeForStorage($value, $types);

        $setting = Setting::updateOrCreate(
            ['namespace' => $namespace, 'key' => $key],
            ['value' => $storedValue, 'types' => $types]
        );

        // Bust & warm cache with already-casted value
        $this->forgetCache($namespace, $key);
        $ck = $this->cacheKey($namespace, $key);
        if ($this->cacheStoreSupportsTags()) {
            Cache::tags(['settings'])->forever($ck, $castedForCache);
        } else {
            Cache::forever($ck, $castedForCache);
        }

        return $setting;
    }

    public function getValue(string $namespace, string $key, mixed $default = null): mixed
    {
        $ck = $this->cacheKey($namespace, $key);

        return $this->remember($ck, function () use ($namespace, $key, $default) {
            $row = Setting::query()
                ->where('namespace', $namespace)
                ->where('key', $key)
                ->first();

            if (!$row) {
                return $default;
            }

            return $this->castFromStorage($row->value, $row->types);
        }, null) ?? $default;
    }

    public function forget(string $namespace, string $key): void
    {
        $this->forgetCache($namespace, $key);
    }

    public function getRaw(string $namespace, string $key): ?Setting
    {
        return Setting::query()
            ->where('namespace', $namespace)
            ->where('key', $key)
            ->first();
    }

    protected function inferType(mixed $value): string
    {
        if (is_bool($value)) return 'boolean';
        if (is_int($value) || (is_string($value) && ctype_digit($value))) return 'integer';
        if (is_array($value) || is_object($value)) return 'json';
        return 'text';
    }

    /**
     * @return array{0:string,1:mixed} [storedValue, castedForCache]
     */
    protected function serializeForStorage(mixed $value, string $types): array
    {
        switch ($types) {
            case 'json':
                $stored = json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                $cached = json_decode($stored, true);
                return [$stored, $cached];

            case 'boolean':
                $stored = $value ? '1' : '0';
                return [$stored, (bool) ((int) $stored)];

            case 'integer':
                $stored = (string) intval($value);
                return [$stored, (int) $stored];

            case 'text':
            default:
                $stored = (string) $value;
                return [$stored, $stored];
        }
    }

    protected function castFromStorage(string $value, string $types): mixed
    {
        switch ($types) {
            case 'json':
                $decoded = json_decode($value, true);
                return $decoded === null ? [] : $decoded;

            case 'boolean':
                $v = strtolower(trim($value));
                return in_array($v, ['1', 'true', 'yes', 'on'], true);

            case 'integer':
                return (int) $value;

            case 'text':
            default:
                return $value;
        }
    }
}
