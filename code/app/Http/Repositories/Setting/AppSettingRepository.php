<?php

namespace App\Http\Repositories\Setting;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Http\Repositories\Setting\Interfaces\SettingRepositoryInterface;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class AppSettingRepository implements AppSettingRepositoryInterface
{
    public function __construct(
        protected SettingRepositoryInterface $settingRepo
    ) {}

    /**
     * Define the workflows you expose as switches in the UI.
     */
    public function knownWorkflows(): array
    {
        return [
            WorkflowName::LEAVE_APPROVAL,
            WorkflowName::TIME_REQUEST_APPROVAL,
            WorkflowName::TERMINATION_APPROVAL,
            WorkflowName::REPLACEMENT_LEAVE_REQUEST,
        ];
    }

    protected function keyFor(string $workflow): string
    {
        // DB key max 50; keep it compact and predictable
        $slug = Str::slug($workflow, '_');
        return "ticketToShow.{$slug}";
    }

    public function initializeDefaults(): void
    {
        foreach ($this->knownWorkflows() as $wf) {
            $key = $this->keyFor($wf);
            // Don't override existing choices; only create if missing
            $existing = $this->settingRepo->getValue('app', $key, null);
            if ($existing === null) {
                $this->settingRepo->createOrUpdate('app', $key, false, 'boolean');
            }
        }
    }

    public function all(): array
    {
        $out = [];
        foreach ($this->knownWorkflows() as $wf) {
            $out[$wf] = $this->isEnabled($wf);
        }
        return $out;
    }

    public function getEnabled(): array
    {
        $enabled = [];
        foreach ($this->knownWorkflows() as $wf) {
            if ($this->isEnabled($wf)) {
                $enabled[] = $wf;
            }
        }
        return $enabled;
    }

    public function isEnabled(string $workflow): bool
    {
        $key = $this->keyFor($workflow);
        return (bool) $this->settingRepo->getValue('app', $key, false);
    }

    public function setEnabled(string $workflow, bool $enabled): void
    {
        $key = $this->keyFor($workflow);
        $this->settingRepo->createOrUpdate('app', $key, $enabled, 'boolean');
    }

    public function setMany(array $workflows): void
    {
        // $workflows = ['leave_approval' => true, 'time_request_approval' => false, ...]
        foreach ($workflows as $wf => $on) {
            $this->setEnabled($wf, (bool) $on);
        }
    }


    /* ----------- Feature flags ----------- */
    public function knownFeatures(): array
    {
        // keys used in UI/request
        return ['otp', 'mpin', 'sms', 'biometric'];
    }

    protected function featureKey(string $feature): string
    {
        $slug = Str::slug($feature, '_');
        return "feature.{$slug}";
    }

    public function getFeatures(): array
    {
        $out = [];
        foreach ($this->knownFeatures() as $f) {
            $out[$f] = $this->isFeatureEnabled($f);
        }
        return $out;
    }

    public function setFeatures(array $features): void
    {
        foreach ($features as $f => $enabled) {
            if (in_array($f, $this->knownFeatures(), true)) {
                $this->setFeature($f, (bool) $enabled);
            }
        }
    }

    public function isFeatureEnabled(string $feature): bool
    {
        if (!in_array($feature, $this->knownFeatures(), true)) {
            throw new \InvalidArgumentException("Unknown feature: {$feature}. Known features: " . implode(', ', $this->knownFeatures()));
        }
        return (bool) $this->settingRepo->getValue('app', $this->featureKey($feature), false);
    }

    public function setFeature(string $feature, bool $enabled): void
    {
        $this->settingRepo->createOrUpdate('app', $this->featureKey($feature), $enabled, 'boolean');
    }

    /* ---------------- Firebase ---------------- */

    protected function firebaseEnabledKey(): string
    {
        return 'firebase.notification_enabled';
    }

    protected function firebaseTopicKey(): string
    {
        return 'firebase.unique_topic';
    }

    public function getFirebaseNotificationEnabled(): bool
    {
        return (bool) $this->settingRepo->getValue('app', $this->firebaseEnabledKey(), false);
    }

    public function setFirebaseNotificationEnabled(bool $enabled): void
    {
        $this->settingRepo->createOrUpdate('app', $this->firebaseEnabledKey(), $enabled, 'boolean');
    }

    public function getFirebaseUniqueTopic(?string $default = null): ?string
    {
        return $this->settingRepo->getValue('app', $this->firebaseTopicKey(), $default);
    }

    public function setFirebaseUniqueCode(string $code): void
    {
        $this->settingRepo->createOrUpdate('app', $this->firebaseTopicKey(), trim($code), 'text');
    }

    /* ---------------- Initializer (Only for one time use; to sync from env to database) ---------------- */

    public function initializeFromEnv(): void
    {
        // Only set if missing (don’t overwrite existing DB choices)
        $this->seedIfMissing($this->firebaseEnabledKey(), (bool) env('FIREBASE_NOTIFICATION', false), 'boolean');
        $this->seedIfMissing($this->firebaseTopicKey(), (string) env('FIREBASE_UNIQUE_TOPIC', ''), 'text');

        $this->seedIfMissing($this->featureKey('otp'),       (bool) env('APP_OTP_FEATURE', false), 'boolean');
        $this->seedIfMissing($this->featureKey('mpin'),      (bool) env('APP_MPIN_FEATURE', false), 'boolean');
        $this->seedIfMissing($this->featureKey('sms'),       (bool) env('APP_SMS_FEATURE', false), 'boolean');
        $this->seedIfMissing($this->featureKey('biometric'), (bool) env('APP_BIOMETRIC_FEATURE', false), 'boolean');
    }

    protected function seedIfMissing(string $key, mixed $value, string $type): void
    {
        $existing = $this->settingRepo->getValue('app', $key, null);
        if ($existing === null) {
            $this->settingRepo->createOrUpdate('app', $key, $value, $type);
        }
    }

    /* ---------------- Base URL for App ---------------- */

    public function setBaseUrl(string $baseUrl): void   
    {
        $this->settingRepo->createOrUpdate('app', 'base_url', $baseUrl, 'text');
        Cache::tags('app')->flush();
    }

    public function getBaseUrl(): string
    {
        return $this->settingRepo->getValue('app', 'base_url', null) ?? config('app.url');
    }
}
