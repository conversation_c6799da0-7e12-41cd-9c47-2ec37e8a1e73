<?php

namespace App\Http\Repositories\Setting\Interfaces;

interface AppSettingRepositoryInterface
{
    /** Return map: ['workflow_key' => bool, ...] */
    public function all(): array;

    /** Return enabled workflow keys only */
    public function getEnabled(): array;

    /** Check if a workflow is enabled */
    public function isEnabled(string $workflow): bool;

    /** Enable/disable a workflow */
    public function setEnabled(string $workflow, bool $enabled): void;

    /** Set many at once: ['workflow_key' => bool, ...] */
    public function setMany(array $workflows): void;

    /** Ensure all known workflows exist with defaults (off by default) */
    public function initializeDefaults(): void;

    /** Return the list of known workflow keys */
    public function knownWorkflows(): array;

    // feature flags
    public function knownFeatures(): array;
    public function getFeatures(): array; // ['otp'=>bool, 'mpin'=>bool, 'sms'=>bool, 'biometric'=>bool]
    public function setFeatures(array $features): void; // accepts same shape
    public function isFeatureEnabled(string $feature): bool;
    public function setFeature(string $feature, bool $enabled): void;

    // Firebase config
    public function getFirebaseNotificationEnabled(): bool;
    public function setFirebaseNotificationEnabled(bool $enabled): void;

    public function getFirebaseUniqueTopic(?string $default = null): ?string;
    public function setFirebaseUniqueCode(string $code): void;

    public function initializeFromEnv(): void;
    public function getBaseUrl(): string;
    public function setBaseUrl(string $baseUrl): void;
}
