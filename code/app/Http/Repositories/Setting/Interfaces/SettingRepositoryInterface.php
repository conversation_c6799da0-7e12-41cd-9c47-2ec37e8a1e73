<?php

namespace App\Http\Repositories\Setting\Interfaces;

use App\Models\configs\Setting;

interface SettingRepositoryInterface
{
    public function createOrUpdate(string $namespace, string $key, mixed $value, string $types): Setting;

    public function getValue(string $namespace, string $key, mixed $default = null): mixed;

    public function forget(string $namespace, string $key): void;

    public function getRaw(string $namespace, string $key): ?Setting;
}
