<?php

namespace App\Http\Repositories\Employees;

use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\Employee;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EmployeeRepository
{
    /**
     * @param array $filters [
     *   'search' => string|null,
     *   'department_id' => int|null,
     *   'company_id' => int|null,
     *   'branch_id' => int|null,
     *   'show_terminated' => bool|null,   // default false
     *   'require_search' => bool|null,    // default true
     *   'limit' => int|null            // default 15
     * ]
     */
    public function searchEmployee(array $filters = [])
    {
        $search         = $filters['search'] ?? null;
        $departmentId   = $filters['department_id'] ?? null;
        $companyId      = $filters['company_id'] ?? null;
        $branchId       = $filters['branch_id'] ?? null;
        $showTerminated = (bool)($filters['show_terminated'] ?? false);
        $limit        = max(1, (int)($filters['limit'] ?? 15));

        if ((!is_string($search) || trim($search) === '')) {
            return collect([]);
        }

        $q = Employee::query()
            ->withTrashed()
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->leftJoin('attendance as a', function($join) {
                $join->on('employees.id', '=', 'a.employee_id')
                    ->where('a.date_en', date('Y-m-d'));
            })
            ->with([
                'department:departments.id,name',
                'branch:branches.id,name',
                'supervisor:employees.id,first_name,middle_name,last_name',
                'designation:designations.id,title',
                'company:companies.id,name,code',
                'unit:units.id,name',
                'organizationInfo',
                'activePayslipList:employee_id,job_id',
                'activePayslipList.job:jobs.id,name',
                'organizationInfo.shift',
            ]);

        if (!$showTerminated) {
            $q->whereNull('org.termination_date');
        }

        $q->when($departmentId, fn($qb, $v) => $qb->where('org.department_id', $v))
            ->when($companyId,    fn($qb, $v) => $qb->where('employees.company_id', $v))
            ->when($branchId,     fn($qb, $v) => $qb->where('org.branch_id', $v));

        if (is_string($search) && ($search = trim($search)) !== '') {
            $q->where(function ($qb) use ($search) {
                $qb->where('employees.id', 'like', "%{$search}%")
                    ->orWhere('org.employee_code', 'like', "%{$search}%")
                    ->orWhere('employees.email', 'like', "%{$search}%")
                    ->orWhereRaw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) LIKE ?", ["%{$search}%"])
                    ->orWhereRaw("CONCAT_WS(' ', employees.first_name, employees.last_name) LIKE ?", ["%{$search}%"]);
            });
        }


        $q->where(function ($qb) {
            $canSeeHidden = Auth::user()?->can(\PermissionList::EMPLOYEE_HIDDEN_PROFILE_VIEW) ?? false;
            $impersonated = isImpersonated();
            if (!$canSeeHidden || $impersonated) {
                $qb->where('employees.hide_profile', 0);
            }
        });


        $latestApproved = DB::table('employee_terminations as et1')
            ->selectRaw('MAX(et1.id) AS id, et1.employee_id')
            ->where('et1.state', WorkflowState::APPROVED)
            ->whereNull('et1.deleted_at')
            ->groupBy('et1.employee_id');

        $q->leftJoinSub(
            $latestApproved,
            'et_max',
            fn($join) =>
            $join->on('et_max.employee_id', '=', 'employees.id')
        )->leftJoin('employee_terminations as et', 'et.id', '=', 'et_max.id');


        // for keeping model hydration
        $q->select(
            'employees.*',
            DB::raw('CASE WHEN et.id IS NULL THEN 0 ELSE 1 END AS on_notice_period'),
            DB::raw('et.termination_request_date AS notice_request_date'),
            DB::raw('et.termination_date AS ticket_termination_date'),
            'a.in_time',
            'a.out_time'
        );
        
        $data = $q->orderBy('employees.first_name')
            ->limit($limit)->get();

        return $data->map(function (Employee $e) {
            return [
                'id'                    => $e->id,
                'name'                  => $e->name,
                'employee_code'         => $e->employeeCode,
                'company'               => $e->company?->name ?? '',
                'department'            => $e->department?->name ?? '',
                'branch'                => $e->branch?->name ?? '',
                'supervisor'            => $e->supervisor->name ?? '',
                'profile_picture'       => $e->profile_picture
                    ? asset('storage/' . $e->profile_picture)
                    : asset('build/img/team/' . ($e->gender ?? 'other') . '.png'),
                'job'                   => $e->activePayslipList[0]->job->name ?? '',
                'unit'                  => $e->unit?->name ?? '',
                'permanent_address'     => $e->permanent_address,
                'phone'                 => $e->phone ?? '',
                'cug'                   => $e->organizationInfo?->cug ?? '',
                'org_email'             => $e->organizationInfo?->email ?? '',
                'is_terminated'         => ($e->deleted_at || $e->organizationInfo?->termination_date),
                'on_notice_period'      => !!$e->on_notice_period,
                'notice_request_date'   => $e->notice_request_date ?? '',
                'ticket_termination_date' => $e->ticket_termination_date ?? '',

                'is_present' => $e->in_time || $e->out_time,

                'shift' => [
                    'start_time' => $e->organizationInfo?->shift?->formatted_start_time ?? '',
                    'end_time' => $e->organizationInfo?->shift?->formatted_end_time ?? '',
                    'off_day' => $e->organizationInfo?->shift?->offday ?? ''
                ]
            ];
        });
    }
}
