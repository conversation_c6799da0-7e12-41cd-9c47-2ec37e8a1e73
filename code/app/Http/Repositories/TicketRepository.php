<?php

namespace App\Http\Repositories;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Leaves\LeaveRequest;
use App\Models\OtRequests;
use App\Models\Payroll\PayslipRequest;
use App\Models\RequestTicket;
use App\Models\Termination\EmployeeTermination;
use App\Models\Tickets\ManpowerRequisition;
use App\Models\Tickets\TicketDocument;
use App\Models\TimeRequest;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Exceptions\TransitionActionException;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PermissionList;

class TicketRepository extends Repository
{

  public const TICKET_TYPES = [
    'leave_request' => LeaveRequest::class,
    'time_request' => TimeRequest::class,
    'manpower_requisition' => ManpowerRequisition::class,
    'employee_termination' => EmployeeTermination::class,
    'employee_ticket' => EmployeeTicket::class,
    'payslip_request' => PayslipRequest::class,
  ];
  private \App\Http\Repositories\TicketDetailRepository $ticketApproveRepo;

  public function __construct()
  {
    $this->ticketApproveRepo = new TicketDetailRepository;
  }
  /**
   * Get the tickets to be reviewed based on the filter type and filters.
   *
   * @param string $ownershipMethod  Type of filter ('leave_request', 'time_request', or 'all').
   * @param array{perPage: int, search: string, workflow: string} $filters      Additional filters for the query.
   *
   * @return \Illuminate\Pagination\Paginator
   */
  public function getTickets(string $ownershipMethod, array $filters, array $workflows = [])
  {
    // Extracting variables from the $filters array
    $perPage = $filters['perPage'] ?? 10;
    $page = $filters['page'] ?? null;
    $search = $filters['search'] ?? null;
    $workflow = $filters['workflow'] ? $filters['workflow'] : "all";
    $state = $filters['state'] ?? 'all';
    // $workflows = $filters['workflows'] ?? [];
    $requestId = $filters['requestId'] ?? null;
    $employeeIds = $filters['employee_ids'] ?? [];

    $morphClasses = config('arflow-config.getTickets');

    // Fetching request tickets with relationships
    $requestTickets = RequestTicket::with([
      'employee:id,first_name,middle_name,last_name',
      'model' => function (MorphTo $morphTo) use ($morphClasses) {
        $morphTo->morphWith($morphClasses);
      }
    ])
      ->when($workflow !== "all", function ($query) use ($workflow) {
        $query->where('workflow', $workflow);
      })

      ->when($state !== 'all', function ($query) use ($state) {
        $query->where('state', $state);
      })

      ->when(count($workflows), fn($query) => $query->whereIn('workflow', $workflows))
      ->when(count($employeeIds), function ($query) use ($employeeIds) {
        $query->whereIn('employee_id', $employeeIds);
      })
      // ->when($workflow && $requestId, function ($query) use ($workflow, $requestId) {
      //   $query->where([['workflow', $workflow], ['model_id', $requestId]]);
      // })
      ->whereHas('employee', function ($query) use ($search) {
        $query->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $search . '%')
          ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $search . '%');
      })
      ->{$ownershipMethod}()
      ->withoutIgnoredWorkflows()
      ->orderBy('created_at', 'desc')
      ->paginate($perPage, page: $page);

    // Transforming request tickets data
    $requestTickets->transform(function (RequestTicket $ticket) {
      $request = $ticket->model;
      if (!$request) throw new \Exception("Request not found for ticket_id: $ticket->id,  model id: $ticket->model_id and model type: $ticket->model_type");
      $request->ticket_id = $ticket->id;

      $this->setTicketDetail($ticket, $request, "card");

      $request->uuid = Str::uuid()->toString();
      $request->state = $request->currentState();
      $request->performerHistory = $request->performerHistory();

      return $request;
    });

    return $requestTickets;
  }

  public function getTicket(int $requestId = 0, string $workflow = "", int $ticketId = 0)
  {
    $morphClasses = config('arflow-config.getTicket');
    $query = RequestTicket::with([
      'employee:id,first_name,middle_name,last_name',
      'documents',
      'documents.owner:id,first_name,middle_name,last_name',
      'documents.arflow:id,to',
      'model' => function (MorphTo $morphTo) use ($morphClasses) {
        $morphTo->morphWith($morphClasses);
      }
    ]);

    if ($ticketId) {
      $query = $query->where([
        ["id", $ticketId]
      ]);
    } else {
      if (!$requestId || !$workflow) {
        throw new \Exception("Please provide request id and workflow");
      }
      $query = $query->where([
        ["model_id", $requestId],
        ["workflow", $workflow]
      ]);
    }

    $ticket = $query->first();
    if (!$ticket) {
      return $this->responseHelper(false, "Ticket Not Found");
    }
    $request = $ticket->model;
    if (!$request instanceof ArflowModelInterface) return;

    if (!$request->canBeViewed()) {
      // abort("403", "You don't have permission to view this ticket");
      return $this->responseHelper(false, "You don't have permission to view this ticket");
    }

    $request->ticket_id = $ticket->id;
    $this->setTicketDetail($ticket, $request, "all");

    $request->stateName = $request->stateName ?? implode(' ', explode('_', $request->workflow));
    $request->uuid = Str::uuid()->toString();
    $request->employee = $request->employee;

    $request->stateHistory = $request->stateHistory->each(function ($item) {
      $item->created = $this->getNepaliDate($item->created_at, addTime: true);
    });
    $request->performerHistory = $request->performerHistory();

    if ($request->canVerify()) {
      $request->nextOwners = $request->getNextOwners($request->employee_id);
    }

    if ($request->canSendForReview()) {
      $request->reviewers = $request->getReviewers($request->employee_id);
    }

    if ($request->canChangeNextOwner()) {
      $request->ownersForChanging = $request->getNextOwnersForChanging($request->employee_id);
      $request->current_owner_id = $ticket->current_owner_id;
    }
    if ($request->canEscalate()) {
      $request->ownersForEscalation = $request->getNextOwnersForChanging($request->employee_id, true);
    }
    if ($request->canReturn()) {
      $request->previousOwners = $request->getPreviousOwners();
    }

    if ($request->hasNavigation() && $request->requestTicket->current_owner_id == currentEmployeeId()) {
      $request->nextPageUrl = $this->getNextTicketUrl($request->requestTicket->id);
      $request->prevPageUrl = $this->getPreviousTicketUrl($request->requestTicket->id);
    }

    $request->canEdit = $request->canCancel();
    return $this->responseHelper(true, data: $request);
  }

  /**
   * Retrieve tickets for the dashboard of the current employee.
   *
   * This method retrieves the request tickets for the current employee,
   * including the associated models and their relationships.
   * It filters the tickets based on the current employee's submitted tickets,
   * and the final state of the tickets (approved, cancelled, rejected) within the last week.
   * The method returns a collection of the transformed request models,
   * with additional properties like the ticket ID, link, and state.
   *
   * @return \Illuminate\Database\Eloquent\Collection
   */
  public function getMyTicketsForDashboard()
  {
    $requestTickets = RequestTicket::with(['model' => function (MorphTo $morphTo) {
      $morphTo->morphWith([
        LeaveRequest::class => ['leaveType:id,name', 'leaveOption:id,name'],
        TimeRequest::class => ['dateAttendance:date_en,employee_id,in_time,out_time'],
        ManpowerRequisition::class => [],
        EmployeeTermination::class => [],
        EmployeeTicket::class => [],
        PayslipRequest::class => [],
        OtRequests::class => ['attendance:date_np,employee_id,in_time,out_time'],
      ]);

      $morphTo->constrain([
        LeaveRequest::class => function ($query) {
          $query->select('id', 'start_date', 'end_date', 'leave_type_id', 'workflow');
        },
        TimeRequest::class => function ($query) {
          $query->select('id', 'date', 'in_time', 'out_time', 'workflow');
        },
      ]);
    }])
      ->where('submitted_by', currentEmployee()?->id)
      ->where(function ($q) {
        $finalStates = [WorkflowState::APPROVED, WorkflowState::REJECTED, WorkflowState::CANCELLED, WorkflowState::REVERTED, WorkflowState::ASSIGNED];
        $q->whereNotIn('state', $finalStates);
      })
      ->orderBy('created_at', 'desc')->take(12)->get();

    $requestTickets->transform(function ($ticket) {
      $request = $ticket->model;
      $request->ticket_id = $ticket->id;

      $this->setTicketDetail($ticket, $request);

      // link for the ticket on my tickets page
      $request->link = route(
        'ticketPage',
        ['requestId' => $request->id, 'workflow' => $request->workflow]
      );

      $request->state = $ticket->state;
      $request->uuid = Str::uuid()->toString();

      return $request;
    });

    return $requestTickets;
  }

  /**
   * Sets the ticket detail for a given request ticket and request object.
   *
   * @param RequestTicket $ticket The request ticket object.
   * @param ArflowModelInterface $request The request object.
   * @param string $type The type of detail to set, can be "card" or "all".
   */
  private function setTicketDetail(RequestTicket $ticket, ArflowModelInterface $request, string $type = "")
  {
    $workflowsConfig = config('arflow-config.setTicketDetails');

    if (!isset($workflowsConfig[$ticket->workflow])) {
      return $this->responseHelper(false, "Workflow not found");
    }

    $workflow = $workflowsConfig[$ticket->workflow];
    $repository = app($workflow['repository']);
    $method = $workflow['method'];

    if ($method == "setManpowerRequisitionDetail") {
      $repository->$method($ticket, $request, $type);
    } else {
      $repository->$method($request, $type);
    }

    /* switch ($ticket->workflow) {
      case WorkflowName::LEAVE_APPROVAL:
        $this->ticketApproveRepo->setLeaveApprovalDetails($request, $type);
        break;

      case WorkflowName::TIME_REQUEST_APPROVAL:
        $this->ticketApproveRepo->setTimeRequestApproval($request, $type);
        break;

      case WorkflowName::MANPOWER_REQUISITION:
          $this->ticketApproveRepo->setManpowerRequisitionDetail($ticket, $request, $type);
        break;

      case WorkflowName::TERMINATION_APPROVAL:
          $this->ticketApproveRepo->setTerminationApproval($request, $type);

        break;

      case WorkflowName::EMPLOYEE_TRANSFER:
          $this->ticketApproveRepo->setEmployeeTransfer($request, $type);
        break;

      case WorkflowName::PAYSLIP_APPROVAL:
          $this->ticketApproveRepo->setPaySlipApproval($request, $type);
        break;

      case WorkflowName::PAYSLIP_APPROVAL:
        switch ($type) {
          case "all":
            $request->stateName = "payslip approval";
            $payslip = \App\Models\Payroll\Payslip::with(['approvedBy:id,first_name,middle_name,last_name'])
              ->leftJoin('payslip_requests', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
              ->where('payslip_requests.id', $request->id)
              ->select('payslips.approved_by', 'payslips.approved_date')
              ->first();
            $request->approved_by = $payslip?->approvedBy?->name;
            $request->approved_date = $payslip?->approved_date;
          case "card":


          default:
            $request->type = 'payslip_approval';
            $request->title = "payslip approval";
            $request->employeeName = $request->employee?->name;
        }
        break;

      case WorkflowName::REPLACEMENT_LEAVE_REQUEST:
          $this->ticketApproveRepo->setReplacementLeaveRequest($request, $type);
       break;

       case WorkflowName::DUTY_CHANGE_REQUEST:
        switch ($type) {
          case "all":
          case "card":
            $request->applied_date = $this->getNepaliDate($request->created_at);
            $request->nep_date = $this->getNepaliDate($request->date_en);

            $existingShift = $request->existingShift;
            if ($existingShift)
              $request->existingShiftName = "$existingShift->name ({$existingShift->start_time} - {$existingShift->end_time})";
            else
              $request->existingShiftName = "Day Off";

            $updatedShift = $request->shift;
            if ($updatedShift)
              $request->updatedShiftName = "$updatedShift->name ({$updatedShift->start_time} - {$updatedShift->end_time})";
            else
              $request->updatedShiftName = "Day Off";
          default:
        }

        case "petty_cash_vendor":
            $this->ticketApproveRepo->vendorApprovalRequest($request, $type);
        break;
    }*/
  }

  public function getNepaliDate(string | Carbon $date, string $format = "F j, Y D", $addTime = false)
  {
    $nepaliDate = LaravelNepaliDate::from($date)->toNepaliDate($format);
    if ($addTime) {
      if (!$date instanceof Carbon) {
        $date = Carbon::parse($date);
      }
      $time = $date->format('h:i A');
      $nepaliDate .= " $time";
    }
    return $nepaliDate;
  }

  /**
   * Changes the state of a ticket.
   *
   * @param int $ticket_id The ID of the ticket to change the state for.
   * @param string $state The new state to set for the ticket.
   * @param string $comment An optional comment to include with the state change.
   * @param string|int|null $nextOwnerId The ID of the next owner of the ticket, if applicable.
   * @return array An array with the status (true/false) and a message.
   */
  public function changeState(
    int $ticket_id,
    string $state,
    string $comment = "",
    string | int | null $nextOwnerId = null
  ) {
    $message = '';
    $status = true;

    $ticket = RequestTicket::find($ticket_id);
    if (!$ticket) {
      return $this->responseHelper(false, "Request Ticket not found");
    }

    $model = app(ucfirst($ticket->model_type))::find($ticket->model_id);
    if (!$model) {
      return $this->responseHelper(false, "Requested Model Not Found");
    }

    if (!($model instanceof ArflowModelInterface || $model instanceof Model)) {
      return $this->responseHelper(false, "Requested Model Not Found");
    }

    DB::beginTransaction();

    try {
      \logInfo(
        "Params for performing '$state' action on " . $model::class . " : ",
        ['requestId' => $model->id, 'employee_id' => $model->employee_id, "next_owner_id" => $nextOwnerId]
      );

      switch ($state) {

        case 'cancel':
          if (!$model->canCancel())
            return $this->errorResponse("Request cannot be cancelled");

          if (
            $ticket->workflow === WorkflowName::TERMINATION_APPROVAL
            && $ticket->state === WorkflowState::APPROVED
            && $model->employee->trashed()
          ) {
            return $this->errorResponse("You can't cancel the approved termination request");
          }

          $this->ticketTransition($model, $ticket, WorkflowState::CANCELLED, $comment);
          $message = 'This Request has been Cancelled!';
          break;

        case 'verify':
          if (!$model->canVerify())
            return $this->errorResponse("Request cannot be verified");

          $nextOwners = $model->getNextOwners($model?->employee_id);
          $nextOwnersIds = $model->getNextOwners($model?->employee_id)
            ->map(fn($owner) => $owner->id)
            ->toArray();
          if (!in_array($nextOwnerId, $nextOwnersIds)) {
            return $this->responseHelper(false, "Invalid next owner");
          }

          $this->ticketTransition($model, $ticket, WorkflowState::VERIFIED, $comment, $nextOwnerId, $model->nextOwnerRole());
          $ticket->verification_level += 1;
          $message = 'This Request has been Verified Successfully!';
          break;

        case 'approve':
          if (!$model->canApprove())
            return $this->errorResponse("Request cannot be approved"); // Need to check in EDF request => Endorse

          if ($ticket->workflow === WorkflowName::EMPLOYEE_TRANSFER) {
            $metaData = $model->metaData->pluck('meta_value', 'meta_key');
            if ($metaData['payslip_change']) {
              $payslipDetails = $metaData['new_payslip_details'];
              $verifierId = $payslipDetails['verifier_id'];
              $this->ticketTransition($model, $ticket, WorkflowState::ENDORSED, $comment, $verifierId, "Payroll");
              $message = 'This Request has been endorsed Successfully!';
              break;
            }
          }

          if ($ticket->workflow === WorkflowName::TRAVEL_TICKET) {
            $approverId = $nextOwnerId;
            $model->finance_ticket_approver = $approverId;
            $this->ticketTransition($model, $ticket, WorkflowState::ENDORSED, $comment, $approverId, "Finance");
            $message = 'This request has been endorsed successfully!';
            break;
          }

          if ($ticket->workflow === WorkflowName::FINANCE_TICKET) {
            $this->ticketTransition($model, $ticket, WorkflowState::ENDORSED, $comment, null, "Settlement");
            $message = 'This request has been endorsed successfully!';
            break;
          }

          $this->ticketTransition($model, $ticket, WorkflowState::APPROVED, $comment);
          $response = $this->approveRequest($model);
          if (!$response['status']) return $this->responseHelper(false, $response['message']);
          $message = 'This Request has been Approved Successfully!';
          break;

        case 'assign':
          $this->ticketTransition($model, $ticket, WorkflowState::ASSIGNED, $comment);
          $message = 'This request has been assigned successfully!';
          $response = $this->approveRequest($model);
          if (!$response['status']) return $this->responseHelper(false, $response['message']);
          break;

        case 'reject':
          if (!$model->canReject())
            return $this->errorResponse("Request cannot be rejected");

          if ($model->currentState() === WorkflowState::SENT_FOR_REVIEW) {
            $this->ticketTransition($model, $ticket, WorkflowState::REVIEW_REJECTED, $comment, $model->initiatorId(WorkflowState::SENT_FOR_REVIEW), $model->nextOwnerRole());
          } else {
            $this->ticketTransition($model, $ticket, WorkflowState::REJECTED, $comment);
          }
          $message = 'This request has been Rejected!';
          break;

        case 'send_for_review':
          if (!$model->canSendForReview())
            return $this->errorResponse("Request cannot be sent for review");

          $reviewers = $model->getReviewers($model?->employee_id)
            ->map(fn($owner) => $owner->id)
            ->toArray();
          if (!in_array($nextOwnerId, $reviewers)) {
            return $this->responseHelper(false, "Invalid reviewer");
          }

          $this->ticketTransition($model, $ticket, WorkflowState::SENT_FOR_REVIEW, $comment, $nextOwnerId, $model->nextOwnerRole());
          $message = 'This request has been sent for review!';
          break;

        case 'review':
          if (!$model->canReview())
            return $this->errorResponse("Request cannot be reviewed");

          $this->ticketTransition($model, $ticket, WorkflowState::REVIEWED, $comment, $model->initiatorId(WorkflowState::SENT_FOR_REVIEW), $model->nextOwnerRole());
          $message = 'This request has been reviewed and sent back to initiator!';
          break;

        case 'return':
          if (!$model->canReturn())
            return $this->errorResponse("Request cannot be returned");

          $reviewers = $model->getPreviousOwners($model?->employee_id)
            ->map(fn($owner) => $owner->id)
            ->toArray();
          if (!in_array($nextOwnerId, $reviewers)) {
            return $this->responseHelper(false, "Invalid previous owner");
          }

          $this->ticketTransition($model, $ticket, WorkflowState::RETURNED, $comment, $nextOwnerId, "Recaller");
          $message = 'This request has been returned!';
          break;

        case 'recall':
          if (!$model->canRecall())
            return $this->errorResponse("Request cannot be recalled");

          $this->ticketTransition($model, $ticket, WorkflowState::RECALLED, $comment, $model->initiatorId(WorkflowState::RETURNED), "Reverifier");
          $message = 'This request has been recalled!';
          break;

        case 'escalate';
          if (!$model->canEscalate())
            return $this->errorResponse("Request cannot be escalated");

          $owners = $model->getNextOwnersForChanging($model?->employee_id, true)
            ->map(fn($owner) => $owner->id)
            ->toArray();
          if (!in_array($nextOwnerId, $owners)) {
            return $this->responseHelper(false, "Invalid reviewer");
          }

          $this->ticketTransition($model, $ticket, WorkflowState::ESCALATED, $comment, $nextOwnerId, $ticket->current_owner_role);
          $message = 'This request has been escalated!';
          break;

        case 'reopen':
          if (!$model->canReopen())
            return $this->errorResponse("Request cannot be reopened");

          $this->ticketTransition($model, $ticket, WorkflowState::REOPENED, $comment, \currentEmployee()->id, WorkflowPerformer::APPROVER);
          $message = 'This request has been reopened!';
          break;

        case 'revert':
          $response = $this->revertRequest($model);
          if (!$response['status']) return $this->responseHelper(false, $response['message']);
          $this->ticketTransition($model, $ticket, WorkflowState::REVERTED, $comment);
          $message = 'The request has been reverted!';
          break;
      }

      if (in_array($model->state, ArflowHelper::getFinalStates($model->currentWorkflow()))) {
        \logInfo("Ticket reached on final state", ['model_id' => $model->id, 'ticket_id' => $ticket->id, 'workflow' => $model->workflow]);
        $ticket->current_owner_id = null;
        $ticket->current_owner_role = null;
      }

      $ticket->save();

      $latestArflowId = $model->stateHistory->last()->id;

      // Get IDs of documents where arflow_id is null
      $documentIdsToUpdate = $ticket->documents()->whereNull('arflow_id')->pluck('id');

      // Update all documents with the latest arflow_id
      $ticket->documents()->whereIn('id', $documentIdsToUpdate)->update(['arflow_id' => $latestArflowId]);

      DB::commit();

      \logInfo("$state action performed on " . $model::class . " : ", [
        "model_id"   => $model->id,
        "workflow"   => $model->workflow,
        "ticket_id"  => $ticket->id,
      ]);
    } catch (TransitionActionException $e) {
      DB::rollback();
      \logError("Transition Action error performing '$state' action on " . $model::class . "",  $e);
      $message = $e->getMessage() ?? "User cannot perform '$state' action";
      $status = false;
    } catch (\Exception $e) {
      DB::rollback();
      \logError("Error while performing '$state' action on " . $model::class, $e);
      $message = "Error while performing '$state' action";
      $status = false;
    }
    return $this->responseHelper($status, $message);
  }

  /**
   * Perform transition of the model and update ticket state and owner information.
   *
   * @param Model $model            The associated model instance.
   * @param RequestTicket $ticket   The request ticket instance.
   * @param string $state           The new state to transition to.
   * @param string $comment         Comment for the transition.
   * @param mixed $nextOwnerId      Optional next owner ID for the transition.
   * @param mixed $nextOwnerRole    Optional next owner role for the transition.
   *
   * @return void
   */
  public function ticketTransition(
    Model $model,
    RequestTicket $ticket,
    string $state,
    string $comment,
    $nextOwnerId = null,
    $nextOwnerRole = null,
  ) {

    // In termination cancellation, initiator can be admin as well so we check for null.
    if ($model->workflow === WorkflowName::TERMINATION_APPROVAL && $state === WorkflowState::CANCELLED)
      $initiator_id = $ticket->current_owner_id ?? currentEmployee()?->id;
    else
      $initiator_id = $ticket->current_owner_id ?? currentEmployee()->id;

    // Transition the model to the specified state with metadata
    $model->transitionTo(
      $state,
      $comment,
      metadata: [
        'initiator_id'    => $initiator_id,
        'initiator_role'  => $ticket->current_owner_role,
        'next_owner_id'   => $nextOwnerId,
        'next_owner_role' => $nextOwnerRole,
      ]
    );

    $ticket->update([
      'state' => $state,
      'current_owner_id' => $nextOwnerId,
      'current_owner_role' => $nextOwnerRole
    ]);
  }

  private function additionalChangeState(RequestTicket $ticket, $model)
  {
    // Additional logic for handling additional states or transitions
    $workflowsConfig = config('arflow-config.additionalChangeState');

    if (!isset($workflowsConfig[$model->workflow])) {
      return $this->errorResponse("Can't find additional change state for workflow ");
    }

    $workflow = $workflowsConfig[$model->workflow];

    if (!isset($workflow['repository'])) {
      return $this->responseHelper(false, "No Further Action Required.");
    }

    $repository = app($workflow['repository']);
    $method = $workflow['method'];
    return $repository->$method($ticket, $model);
  }


  /**
   * Dispatch a job to send a notification regarding a state transition.
   *
   * @param StateableModelContract & Model $request   The request instance.
   * @param string $from              The sender of the notification.
   * @param string $to                The recipient of the notification.
   * @param string|int $initiatorId   The ID of the initiator.
   * @param string $initiatorRole     The role of the initiator.
   * @param string|int $nextOwnerId   The ID of the next owner.
   * @param string $nextOwnerRole     The role of the next owner.
   *
   * @return void
   */
  public static function sendNotification(
    StateableModelContract & Model $request,
    string $from,
    string $to,
    string | int $initiatorId,
    string $initiatorRole,
    string | int $nextOwnerId,
    string $nextOwnerRole,
  ) {
    dispatch(new \App\Jobs\SendTicketNotificationJob($request, $from, $to, [
      'metadata' => json_encode([
        'initiator_id'    => $initiatorId,
        'initiator_role'  => $initiatorRole,
        'next_owner_id'   => $nextOwnerId,
        'next_owner_role' => $nextOwnerRole
      ]),
    ]));
  }

  public function changeTicketOwner(
    int $ticketId,
    int $changingOwnerId,
  ) {
    try {
      $ticket = RequestTicket::find($ticketId);
      if (!$ticket) {
        return $this->responseHelper(false, "Request Ticket Not Found");
      }
      $model = app(ucfirst($ticket->model_type))::findOrFail($ticket->model_id);
      if (
        !($model instanceof ArflowModelInterface &&
          $model instanceof StateableModelContract &&
          $model instanceof Model)
      ) {
        return $this->responseHelper(false, "Invalid request ticket");
      }
      if (!$model->canChangeNextOwner()) {
        return $this->responseHelper(false, "You can't change the next owner");
      }

      if (!in_array($changingOwnerId, $model->getNextOwnersForChanging($model->employee_id)->pluck('id')->toArray())) {
        return $this->errorResponse("The selected owner is not available for changing");
      }

      $ticket->current_owner_id = $changingOwnerId;
      $ticket->save();

      TicketRepository::sendNotification(
        $model,
        "",
        $ticket->state,
        currentEmployee()?->id,
        "nextOwnerChanger",
        $ticket->current_owner_id,
        $ticket->current_owner_role,
      );

      return $this->responseHelper(true, "Owner Changed Successfully");
    } catch (\Exception $e) {
      Log::error("Error while changing ticket owner: " . \errorContext($e));
      return $this->responseHelper(false, "Error while changing ticket owner");
    }
  }

  public function approveRequest(Model $model)
  {
    $workflowsConfig = config('arflow-config.approveTicketDetails');

    if (!isset($workflowsConfig[$model->workflow])) {
      return $this->successResponse('Workflow not registered, approved');
      // return $this->responseHelper(false, "Workflow not found");
    }

    $workflow = $workflowsConfig[$model->workflow];

    if (!isset($workflow['repository']) && !isset($workflow['method'])) {
      return $this->responseHelper(false, "No Further Action Required.");
    }

    $repository = app($workflow['repository']);
    $method = $workflow['method'];
    return $repository->$method($model);

    /*switch ($model->workflow) {
      case WorkflowName::LEAVE_APPROVAL:
        $leaveRepo = new LeaveRepository;
        return $leaveRepo->approveLeaveRequest($model);
      case WorkflowName::TIME_REQUEST_APPROVAL:
        $timeRequestRepo = new TimeRequestRepository;
        return $timeRequestRepo->approveTimeRequest($model);
      case WorkflowName::MANPOWER_REQUISITION:
        $manpowerRequisitionRepo = new ManpowerRequisitionRepository;
        return $manpowerRequisitionRepo->approveManpowerRequisition($model);
      case WorkflowName::EMPLOYEE_TRANSFER:
        $employeeTicketRepo = new EmployeeTicketRepository;
        return $employeeTicketRepo->approveEmployeeTransfer($model);
      case WorkflowName::REPLACEMENT_LEAVE_REQUEST:
        $leaveRepo = new LeaveRepository;
        return $leaveRepo->applyReplacementLeave($model);
      case WorkflowName::TERMINATION_APPROVAL:
        return $this->responseHelper(true);
      case WorkflowName::PAYSLIP_APPROVAL:
        $payslipApprovalRepo = new PayslipApprovalRepository;
        return $payslipApprovalRepo->approvePayslip($model);
      case WorkflowName::DUTY_CHANGE_REQUEST:
        $dutyChangeRepo = new \App\Http\Repositories\DutyRosterRepository;
        return $dutyChangeRepo->approveDutyRosterTicket($model);
      default:
        return $this->responseHelper(false, "Workflow not found");
    }*/
  }

  public function revertRequest(Model $model)
  {
    $workflowsConfig = config('arflow-config.revertRequestTicket');

    if (!isset($workflowsConfig[$model->workflow])) {
      return $this->responseHelper(false, "Workflow not found");
    }

    $workflow = $workflowsConfig[$model->workflow];

    if (!isset($workflow['repository']) && !isset($workflow['method'])) {
      return $this->responseHelper(false, "No Further Action Required.");
    }

    $repository = app($workflow['repository']);
    $method = $workflow['method'];
    return $repository->$method($model);

    // switch ($model->workflow) {
    //   case WorkflowName::LEAVE_APPROVAL:
    //     $leaveRepo = new LeaveRepository;
    //     return $leaveRepo->revertLeaveRequest($model);
    //   case WorkflowName::TIME_REQUEST_APPROVAL:
    //     //
    //   case WorkflowName::MANPOWER_REQUISITION:
    //     //
    //   case WorkflowName::EMPLOYEE_TRANSFER:
    //     //
    //   case WorkflowName::REPLACEMENT_LEAVE_REQUEST:
    //     //
    //   case WorkflowName::TERMINATION_APPROVAL:
    //     //
    //   case WorkflowName::PAYSLIP_APPROVAL:
    //     //
    //   default:
    //     return $this->responseHelper(false, "Workflow not found");
    // }
  }

  /**
   * Creates a new request ticket for the given model.
   *
   * @param Model|StateableModelContract|ArflowModelInterface $request The model for which the request ticket is being created.
   * @param array $data An array of data for the request ticket, including the employee ID, current owner ID, and documents.
   * @param bool $sendNotification Whether to send a notification for the created request ticket.
   * @return array The response data, including the created request ticket.
   */
  public function createRequestTicket(
    Model|StateableModelContract|ArflowModelInterface $request,
    $data,
    bool $sendNotification = true
  ) {
    $nextOwnerRole = $request->nextOwnerRole(isSubmitting: true);
    $requestTicket = RequestTicket::create([
      "model_type" => \get_class($request),
      "workflow" => $request->workflow,
      "model_id" => $request->id,
      "employee_id" => $data['employee_id'],
      "current_owner_id" => $data['current_owner_id'],
      "state" => $request->state,
      'current_owner_role' => $nextOwnerRole,
    ]);

    \logInfo("Request Ticket Created: ", $requestTicket->toArray());
    $latestArflowId = $request->stateHistory->last()->id;
    if ($data['documents'] ?? null)
      $this->addAndUpdateDocuments($requestTicket, $data['documents'], arflowId: $latestArflowId);
    if ($sendNotification)
      TicketRepository::sendNotification(
        $request,
        "",
        $request->state,
        currentEmployee()?->id,
        "Submitter",
        $requestTicket->current_owner_id,
        $nextOwnerRole
      );
    return $this->responseHelper(true, data: $requestTicket);
  }

  public function updateRequestTicket(
    Model|StateableModelContract|ArflowModelInterface $request,
    array $data
  ) {
    $requestTicket = RequestTicket::where([
      ['model_id', $request->id],
      ['workflow', $request->workflow]
    ])->first();

    $oldVerifierId = $requestTicket->current_owner_id;
    $requestTicket->update($data);
    \logInfo("Request Ticket of " . \get_class($request) . " updated", $requestTicket->toArray());
    $latestArflowId = $request->stateHistory->last()->id;
    $this->addAndUpdateDocuments($requestTicket, $data['documents'] ?? [], $data['removing_document_ids'] ?? [], arflowId: $latestArflowId);

    if (isset($data['current_owner_id']) && $data['current_owner_id'] !== $oldVerifierId) {
      TicketRepository::sendNotification(
        $request,
        "",
        $request->state,
        $request->employee_id,
        "Submitter",
        $requestTicket->current_owner_id,
        $request->nextOwnerRole(isSubmitting: true),
      );
    }
    return $this->responseHelper(true);
  }

  public function addAndUpdateDocuments(
    RequestTicket $requestTicket,
    array $documents,
    array $removingDocumentIds = [],
    int | string $arflowId = null,
  ) {
    if (count($removingDocumentIds)) {
      $requestTicket->removeDocuments($removingDocumentIds);
    }
    $fileNames = $requestTicket->addDocuments($documents, $arflowId);
    \logInfo("Documents added", $fileNames);
  }

  public function deleteRequestTicket(int|string $ticketId)
  {
    logInfo("Deleting request ticket" . $ticketId);
    $requestTicket = RequestTicket::find($ticketId);
    if (!$requestTicket) {
      logError("Request ticket not found");
      return $this->responseHelper(false, "Request Ticket Not Found");
    }

    $model = app(ucfirst($requestTicket->model_type))::find($requestTicket->model_id);
    if (!$model) {
      logError("Model for the request ticket not found");
      return $this->errorResponse("Model for the request ticket not found");
    }

    try {
      DB::beginTransaction();
      logInfo("Deleting model for the request ticket", ['model_id' => $model->id]);

      // Additional things to do after the deletion, that is set from the config, dynamic methods ;)
      $workflowsConfig = config('arflow-config.deleteRequestTicket');

      if (!isset($workflowsConfig[$requestTicket->workflow])) {
        logError("Workflow for the request ticket not found on config");
        return $this->responseHelper(false, "Workflow not found on config");
      }

      $workflow = $workflowsConfig[$requestTicket->workflow];
      if (isset($workflow['repository']) && isset($workflow['method'])) {
        $repository = app($workflow['repository']);
        $method = $workflow['method'];
        $response = $repository->$method($model);
        if (!$response['status']) {
          return $this->responseHelper(false, "Failed to delete model. Please check workflow implementation.");
        }
      }

      $model->stateHistory()->delete();
      logInfo("State history for the request ticket deleted");
      $model->delete();
      logInfo("Model of request ticket deleted");

      $requestTicket->removeDocuments($requestTicket->documents->pluck('id')->toArray());
      logInfo("Documents of request ticket deleted and its files are also removed");
      $requestTicket->delete();
      logInfo("Request ticket deleted successfully");
      DB::commit();
    } catch (\Exception $e) {
      DB::rollBack();
      \logError("Error while deleting request ticket", $e);
      return $this->errorResponse("Error while deleting request ticket");
    }

    \logInfo("Request Ticket deleted", $requestTicket->toArray());
    return $this->responseHelper(true);
  }

  public function getMyTickets($employeeId, $workflows = [])
  {
    return RequestTicket::where('submitted_by', $employeeId)
      ->withoutIgnoredWorkflows()
      ->where(function ($q) {
        $finalStates = config('arflow-config.globalFinalStates', []);
        $q->whereNotIn('state', $finalStates);
      })
      ->when(count($workflows), fn($query) => $query->whereIn('workflow', $workflows))
      ->select('workflow', DB::raw('count(*) as count'))
      ->groupBy('workflow')
      ->get();
  }

  public function ticketWorkflows($user)
  {
    $workflows = [
      "all" => "Everything",
    ];

    $permissions = [
      WorkflowName::LEAVE_APPROVAL => true,
      WorkflowName::TIME_REQUEST_APPROVAL => true,
      WorkflowName::MANPOWER_REQUISITION => true,
      WorkflowName::TERMINATION_APPROVAL => true,
      WorkflowName::EMPLOYEE_TRANSFER => true,
      WorkflowName::REPLACEMENT_LEAVE_REQUEST => fedexHrm(),
      WorkflowName::PAYSLIP_APPROVAL => true,
      WorkflowName::DUTY_CHANGE_REQUEST => true,
      WorkflowName::IRREGULARITY_TICKET => true,
      WorkflowName::EDF_REQUEST => $user->can(PermissionList::EMPLOYEE_CREATE),
    ];

    foreach (array_values(WorkflowName::getConstants()) as $type) {
      if (in_array($type, RequestTicket::IGNORE_WORKFLOWS)) {
        continue;
      }

      if ($permissions[$type] ?? true) {
        $workflows[$type] = str_replace('_', ' ', $type); // or use ucwords if needed
      }
    }
    return $workflows;
  }

  public function getTicketInfo(int $ticketId, string | array $workflow)
  {
    if (is_string($workflow))
      return RequestTicket::where('id', $ticketId)->where('workflow', $workflow)->first();

    if (is_array($workflow))
      return RequestTicket::where('id', $ticketId)->whereIn('workflow', $workflow)->exists();

    return false;
  }

  public function getPendingTicket($employeeId = null, $workflows = [])
  {
    if (isset($employeeId) && count($workflows) > 0) {
      return RequestTicket::where('current_owner_id', $employeeId)
        ->withoutIgnoredWorkflows()
        ->select('id', 'workflow', 'state', 'current_owner_role', 'model_id')
        ->select('workflow', DB::raw('count(*) as count'))
        ->whereIn('workflow', $workflows)
        ->groupBy('workflow')
        ->get();
    }
    return RequestTicket::where('current_owner_id', currentEmployee()?->id)
      ->select('id', 'workflow', 'state', 'current_owner_role', 'model_id')
      ->select('workflow', DB::raw('count(*) as count'))
      ->groupBy('workflow')
      ->get();
  }

  public function getRejectedTicket($employeeId = null, $workflows = [])
  {
    $query = RequestTicket::query()
      ->withoutIgnoredWorkflows();

    if ($employeeId) {
      $query->where('submitted_by', $employeeId);
    }

    $query->where('state', WorkflowState::REJECTED);

    if (!empty($workflows)) {
      $query->whereIn('workflow', $workflows);
    }

    $query->where('updated_at', '>=', Carbon::now()->subDays(3));

    return $query->select('workflow', DB::raw('count(*) as count'))
      ->groupBy('workflow')
      ->get();
  }

  //Made static to be called from volt component
  public static function removeDocument(int $ticketId, array $documentIds)
  {
    $ticket = RequestTicket::find($ticketId);
    $documents = TicketDocument::whereIn('id', $documentIds)->get();
    foreach ($documents as $document) {
      if (!$document->canRemove) {
        return false;
      }
    }
    return $ticket->removeDocuments($documentIds);
  }

  public function getNextTicketUrl(int $currentTicketId)
  {
    $currentTicket = RequestTicket::findOrFail($currentTicketId);

    $nextTicket = RequestTicket::where('current_owner_id', currentEmployeeId())
      ->where('created_at', '>', $currentTicket->created_at)
      ->where('workflow', $currentTicket->workflow)
      ->orderBy('created_at', 'asc')
      ->first();

    if (!$nextTicket) {
      return "";
    }

    return route('ticketPage', ['workflow' => $nextTicket->workflow, 'requestId' => $nextTicket->model_id]);
  }

  public function getPreviousTicketUrl(int $currentTicketId)
  {
    $currentTicket = RequestTicket::findOrFail($currentTicketId);

    $prevTicket = RequestTicket::where('current_owner_id', currentEmployeeId())
      ->where('created_at', '<', $currentTicket->created_at)
      ->where('workflow', $currentTicket->workflow)
      ->orderBy('created_at', 'desc')
      ->first();

    if (!$prevTicket) {
      return "";
    }

    return route('ticketPage', ['workflow' => $prevTicket->workflow, 'requestId' => $prevTicket->model_id]);
  }

  public function createAndSendTicketNotification(\App\Models\User $user, string $title, string $message, RequestTicket $ticket)
  {
    $notificationRepo = app(\App\Http\Repositories\Configs\NotificationRepository::class);
    $appSettingRepo = app(\App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface::class);
    $icons = config('arflow-config.icons');
    $notificationRepo->createAndSendDeviceNotification(
      $user,
      $title,
      $message,
      route('ticketPage', ['workflow' => $ticket->workflow, 'requestId' => $ticket->model_id]),
      "View",
      [
        'type'      => "detail",
        'workflow'  => $ticket->workflow,
        'ticket_id' => $ticket->id,
        'icon'      => $icons[$ticket->workflow] ?? null,
      ],
      in_array($ticket->workflow,  $appSettingRepo->getEnabled())
    );
  }
}
