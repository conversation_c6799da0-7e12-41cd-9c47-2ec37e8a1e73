<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Constant;
use App\Jobs\SyncAttendanceFromDeviceJob;
use App\Models\configs\AttDevice;
use App\Models\configs\AttLog;
use Exception;
use Illuminate\Support\Facades\DB;

class SyncDeviceAttendanceRepository
{
    protected $hrm;

    public function syncDeviceAttendanceToDB($date = null, $device_ip = null)
    {
        logInfo("App name: " . config('app.name'));
        // This condition is for development only. Need to remove later.
        if (config('app.name') === "vianet_hrm") {
            $this->syncFromHRIS($date);
            return true;
        }

        if (config('app.name') === "konnect_hrm") {
            $this->syncFromHRIS($date);
        }

        if ($device_ip) {
            $attDevice = AttDevice::where("device_ip", $device_ip)->first();
            if ($attDevice) {
                $attDevice->syncAttendanceToDb(true);
                return true;
            } else return false;
        } else if ($date) {
            $attDevices = AttDevice::where("is_active", true)->get();
            if (count($attDevices)) {
                foreach ($attDevices as $attDevice) {
                    SyncAttendanceFromDeviceJob::dispatch($attDevice)->onQueue('attendance');
                }
                return true;
            } else return false;
        }

        $attDevices = AttDevice::where("is_active", true)->get();
        foreach ($attDevices as $attDevice) {
            SyncAttendanceFromDeviceJob::dispatch($attDevice)->onQueue('attendance');
        }
    }

    private function syncFromHRIS($date = null)
    {
        logCronInfo("connecting db");
        $this->hrm = DB::connection('mysql1');
        logCronInfo($this->hrm ? 'Connection variable set.' : 'Failed to connect hrm db');
        $timestamp = $date ? date("Y-m-d", strtotime($date)) : date("Y-m-d");
        // $records = collect([]);

        // if (!config('app.sync_attendance_from_hris') && config('app.sync_attendance_from_hris') !== null) {
        //     logCronInfo("Fetching data from API.");
        //     $iopsRepo = new IopsRepository();
        //     $response = $iopsRepo->getAttendanceFromHRIS($timestamp);
        //     if ($response['status'] == 1) {
        //         $records = json_decode(json_encode($response['data']['response'], false));
        //     }
        // } else {
        logCronInfo("Fetching data from HRIS.");
        $records = $this->hrm->table('att_logs')->whereDate("log_date", $timestamp)->get();
        // }

        $dataToStore = [];

        foreach ($records as $row) {
            $device_id = 1;
            if ($row?->device_id) {
                if ($row->device_id == 92) {
                    $device_id = Constant::EMP_APP_ID;
                } else if ($row->device_id == 93) {
                    $device_id = Constant::WEB_APP_ID;
                }
            }

            $attributes = [
                'log_id' => $row->log_id,
                'device_id' => $device_id,
                'enrollment_no' => $row->enrollment_no,
                'verify_mode' => $row->verify_mode,
                'inout_mode' => $row->inout_mode,
                'device_ip' => $row->device_ip,
                'fetch_date' => $row->fetch_date,
                'created_at' => $row->created_at ?? null,
                'updated_at' => $row->updated_at ?? null,
                'log_date' => $row->log_date,
            ];
            $dataToStore[] = $attributes;
        }

        // Check for existing records
        $existingRecords = AttLog::whereIn('log_date', array_column($dataToStore, 'log_date'))
            ->whereIn('enrollment_no', array_column($dataToStore, 'enrollment_no'))
            ->get()
            ->keyBy(function ($record) {
                return $record['log_date'] . '-' . $record['enrollment_no'];
            });

        // Filter out new records
        $newRecords = array_filter($dataToStore, function ($data) use ($existingRecords) {
            $key = $data['log_date'] . '-' . $data['enrollment_no'];
            return !$existingRecords->has($key);
        });

        logCronInfo("Number of data to store: " . count($dataToStore));
        logCronInfo("New Record: " . count($newRecords));
        if (!empty($newRecords)) {
            $chunks = array_chunk($newRecords, 500); // Split into chunks of 500 records

            DB::beginTransaction();
            try {
                foreach ($chunks as $chunk) {
                    DB::table('att_logs')->insert($chunk);
                }
                DB::commit();
                logCronInfo("Attendance synced from HRIS.");
            } catch (Exception $e) {
                DB::rollBack();
                logCronError("Failed to sync attendance from HRIS.", $e);
            }
        }
    }
}
