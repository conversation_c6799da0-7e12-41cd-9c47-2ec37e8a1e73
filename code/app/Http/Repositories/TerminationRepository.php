<?php

namespace App\Http\Repositories;

use App\Models\Tickets\ManpowerRequisition;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\Repository;
use App\Models\configs\JobDepartment;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Termination\EmployeeTermination;
use App\Models\User;
use carbon\Carbon;
use App\Http\Helpers\Enums\WorkflowName;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TerminationRepository extends Repository
{
    public function getNepaliMonthDateRange()
    {
        $today = now()->format('Y-m-d'); // Current date
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate(); // Convert to Nepali date
        $nepaliDateParts = explode('-', $nepaliDate);

        // Calculate start and end dates of the Nepali month
        $daysInMonth = LaravelNepaliDate::daysInMonth($nepaliDateParts[1], $nepaliDateParts[0]);
        $startOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], 1);
        $endOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], $daysInMonth);

        return [
            'today' => $today,
            'start' => $startOfMonth->toEnglishDate(),
            'end' => $endOfMonth->toEnglishDate(),
        ];
    }

    public function applyScope($query)
    {
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;

            $query->where('company_id', $currentEmployee?->company_id);

            if (scopeCompany())
                return $query;
            $query->where('region_id', $orgInfo->region_id);

            if (scopeRegion())
                return $query;
            $query->where('branch_id', $orgInfo->branch_id);

            if (scopeBranch())
                return $query;
            $query->where('department_id', $orgInfo->department_id);
        }

        return $query;
    }
    public function terminatedEmployeesQuery()
    {

        $dateRange = $this->getNepaliMonthDateRange();

        $query = Employee::onlyTrashed()
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->whereBetween('org.termination_date', [$dateRange['start'], $dateRange['today']])
            ->select(
                'org.termination_date',
                'org.termination_reason'
            );
        $query = $this->applyScope($query);
        return $query;
    }

    public function terminatingEmployeesQuery()
    {
        $dateRange = $this->getNepaliMonthDateRange();

        $query = Employee::leftJoin('employee_terminations as termination', 'termination.employee_id', 'employees.id')
            ->leftjoin('employee_org as org', 'employees.id', 'org.employee_id')
            ->where('termination.state', WorkflowState::APPROVED)
            ->whereNull('termination.deleted_at')
            ->whereBetween('termination.termination_date', [$dateRange['today'], $dateRange['end']])
            ->select(
                'termination.termination_date',
                'termination.termination_request_date',
                'employees.region_id'
            );
        $query = $this->applyScope($query);
        return $query;
    }

    public function jobSeatAfterTermination(EmployeeTermination $request)
    {
        logInfo("Calculation for job seat after termination approved start");
        $employee = Employee::find($request->employee_id);
        $orgInfo = $employee->organizationInfo;
        $jobId = $employee->getJobId();
        try {
            $jobSeatRepo = app(\App\Http\Repositories\Configs\JobSeatRepository::class);
            $jobSeatRepo->increaseOnNotice([
                'company_id' => $employee->company_id,
                'branch_id' => $orgInfo->branch_id,
                'department_id' => $orgInfo->department_id,
                'job_id' => $jobId
            ]);
        } catch (\Exception $e) {
            // continue the process even if job seat update fails
        }
        return $this->successResponse("Termination approved successfully.");
    }

    public function getTerminatedEmployeesCount()
    {
        return $this->terminatedEmployeesQuery()->count();
    }

    public function getTerminatingEmployeesCount()
    {
        return $this->terminatingEmployeesQuery()->count();
    }
    public function cancelTermination(int $id)
    {
        DB::beginTransaction();
        try {

            $terminationTicket = EmployeeTermination::where('id', $id)->where('state', WorkflowState::APPROVED)->first();

            if (!$terminationTicket) {
                throw new \Exception("Termination Ticket does not exists.");
            }

            $terminationTicket->transitionTo(WorkflowState::CANCELLED);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while requesting to canceling termination: ", $e);
            throw $e;
        }
    }

    public function deleteTerminationTicket(EmployeeTermination $request)
    {
        $employee = $request->employee;
        $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
        $orgInfo = $employee->organizationInfo;
        $jobId = count($orgInfo->activePayslips) ? $orgInfo->activePayslips[0]->job_id : null;
        try {
            $jobSeatRepo->revertIncreaseOnNotice([
                'company_id' => $employee->company_id,
                'branch_id' => $orgInfo->branch_id,
                'department_id' => $orgInfo->department_id,
                'job_id' => $jobId
            ]);
        } catch (\Exception $e) {
            logInfo("Revert Increase On Notice Failed");
            // don't halt the process
        }

        return $this->successResponse("Job not found.");
    }

    /**
     * Restore an employee either permanently or for a day
     */
    public function restoreEmployee($employeeId, $type)
    {
        if (isImpersonated()) {
            $userId = session('user_id_main');
        } else {
            $userId = session('user_id');
        }
        try {
            DB::beginTransaction();
            logInfo("Restoring employee of $employeeId");

            $restoredBy = null;
            if ($userId && $type == 'permanent' || $type == 'iops_permanent') {
                $restoredBy = Employee::leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
                    ->leftJoin("companies as comp", "comp.id", "=", "employees.company_id")
                    ->where('employees.user_id', '=', $userId)
                    ->select(
                        Employee::selectNameRawQuery('employees', 'restored_by_name'),
                        DB::raw("CONCAT(comp.code, '-', employee_org.employee_code) as emp_code"),
                    )
                    ->first();
            }

            $employee = Employee::onlyTrashed()->where('id', $employeeId)->first();
            if (!$employee)
                return $this->errorResponse("Can't find terminated employee");

            $orgInfo = EmployeeOrg::onlyTrashed()->where('employee_id', $employee->id)->first();
            if (!$orgInfo)
                return $this->errorResponse("Can't find terminated employee organization");

            $userInfo = User::onlyTrashed()->where('id', $employee->user_id)->first();
            if (!$userInfo)
                return $this->errorResponse("Can't find terminated employee user");

            if ($type === 'permanent' || $type == 'iops_permanent') {
                $terminationTicket = EmployeeTermination::where('employee_id', $employee->id)->where('workflow', WorkflowName::TERMINATION_APPROVAL)->where('state', WorkflowState::APPROVED)->first();
                if (!$terminationTicket) {
                    return $this->errorResponse("Can't find terminated employee ticket");
                }
            }

            $employee->restore();
            $orgInfo->restore();
            $userInfo->restore();
            $orgInfo->update([
                'termination_request_date' => null,
                'termination_date' => null,
                'termination_reason' => null,
            ]);
            if (($type === 'permanent' || $type == 'iops_permanent') && isset($terminationTicket)) {
                $terminationReason = isset($restoredBy)
                    ? '(Termination Cancelled by ' . ($restoredBy->restored_by_name . ' [' . $restoredBy->emp_code . ']') . ')'
                    : '(Termination Cancelled by Admin)';
                $terminationTicket->update([
                    'termination_reason' => $terminationTicket->termination_reason . ' ' . $terminationReason,
                ]);

                $terminationTicket->setAttribute('isRestoring', true);
                $terminationTicket->transitionTo(WorkflowState::CANCELLED);
            }
            if (($type == 'iops_permanent' || $type == 'iops_temporary') && (vianetHrm() || konnectHrm())) {
                $iposUnterminate = new IopsRepository;
                $response = $iposUnterminate->unTerminateEmployee($employeeId);
            }

            logInfo("Restored employee and organization information");

            $jobId = ($orgInfo->activePayslips[0] ?? null)?->job_id;
            $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);

            try {
                if ($jobId)
                    $jobSeatRepo->restoreEmployee([
                        'company_id' => $employee->company_id,
                        'branch_id' => $orgInfo->branch_id,
                        'department_id' => $orgInfo->department_id,
                        'job_id' => $jobId
                    ], $type === 'permanent');
            } catch (\Exception $e) {
                logInfo("Restore Employee Failed");
                return $this->errorResponse("Restore Employee Failed: " . $e->getMessage());
            }

            Cache::store('arflow')->flush();
            Cache::store('employee')->flush();
            DB::commit();
            return $this->successResponse("Employee restored successfully");
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error restoring employee", $e);
            return $this->errorResponse("Error restoring employee: " . $e->getMessage());
        }
    }
}
