<?php

namespace App\Http\Repositories\Interfaces;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

interface ILeaveDashboardRepository
{
    /** Build base attendance query (range) with filters. */
    public function baseAttendanceRangeQuery(array $filters): Builder;

    /** KPIs for range (person-days, distinct employees, avg/day, pending requests). */
    public function kpisRange(array $filters): array;

    /** On-leave paginated list for selected (English) date. */
    public function onLeaveForDate(array $filters, string $dateEn, int $page = 1, int $perPage = 10, bool $returnQuery = false): LengthAwarePaginator|Builder;

    /** Requests overlapping range (paginated). */
    public function requestsOverlappingRange(array $filters, int $page = 1, int $perPage = 10, bool $returnQuery = false): LengthAwarePaginator|Builder;

    /** Charts (range): daily headcount + donut by leave type + dept bar. */
    public function chartDailyRange(array $filters): array;
    public function chartTypeDonutRange(array $filters): array;
    public function chartDeptBarRange(array $filters): array;

    /** Charts (single day): donut by type + bar by department. */
    public function chartTypeDonutOnDate(array $filters, string $dateEn): array;
    public function chartDeptBarOnDate(array $filters, string $dateEn): array;

    public function clearCache(): void;
}
