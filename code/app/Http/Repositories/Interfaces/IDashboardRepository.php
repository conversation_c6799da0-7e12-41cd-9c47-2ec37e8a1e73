<?php

namespace App\Http\Repositories\Interfaces;

/**
 * Dashboard Repository Interface
 * 
 * This interface defines the contract for dashboard-related data operations.
 * It provides methods to retrieve various employee statistics, attendance data,
 * payslip information, and demographic breakdowns for dashboard display.
 * 
 */
interface IDashboardRepository
{

    /**
     * Get comprehensive employee statistics including total, new, terminating, and terminated employees.
     * 
     * Returns an array containing:
     * - total_employees: Current active employee count
     * - new_employees: Employees hired within the current Nepali month
     * - terminating: Employees with approved termination requests for current month
     * - terminated: Employees terminated within the current Nepali month
     * 
     * @param int|null $regionId Optional region filter to scope results
     * @param int|null $branchId Optional branch filter to scope results
     * @return array Associative array with employee statistics
     */
    public function getEmployeeDetails(int $regionId = null, int $branchId = null): array;

    /**
     * Get daily attendance breakdown for the current Nepali date.
     * 
     * Returns comprehensive attendance statistics including:
     * - present: Employees who have checked in/out today
     * - absent: Employees without attendance and not on approved leave/day off
     * - day_off: Employees marked as having a day off
     * - on_leave: Employees on approved paid leave
     * - leave_type_counts: Breakdown of leave counts by leave type
     * 
     * @param int|null $regionId Optional region filter to scope results
     * @param int|null $branchId Optional branch filter to scope results
     * @return array Associative array with attendance statistics
     * 
     */
    public function getAttendanceDetails(int $regionId = null, int $branchId = null): array;

    /**
     * Get comprehensive payslip statistics and status breakdown.
     * 
     * Returns detailed payslip information including:
     * - active: Currently active payslips
     * - expiring: Payslips expiring within 30 days
     * - expired: Payslips that expired within the last 30 days
     * - no_payslip: Employees without any payslip or payslip request
     * - pending_request: Payslip requests awaiting approval
     * 
     * @param int|null $regionId Optional region filter to scope results
     * @param int|null $branchId Optional branch filter to scope results
     * @return array Associative array with payslip statistics
     * 
     */
    public function getPayslipDetails(int $regionId = null, int $branchId = null): array;

    /**
     * Get employee breakdown by employment type (in-house vs outsourced).
     * 
     * Returns comprehensive statistics for both in-house and outsourced employees,
     * including total counts and current day attendance status.
     * 
     * @param int|null $regionId Optional region filter to scope results
     * @param int|null $branchId Optional branch filter to scope results
     * @return array Associative array with employment type statistics
     * 
     * @example
     * ```php
     * $employmentStats = $repo->getInhouseAndOutsourceCounts();
     */
    public function getInhouseAndOutsourceCounts(int $regionId = null, int $branchId = null);

    public function getDayOffCount(string $date_nep, int $regionId = null, int $branchId = null);

    /**
     * Get attendance irregularity statistics for the current day.
     * 
     * Returns counts of employees with attendance issues:
     * - late_in_count: Employees who arrived late
     * - early_out_count: Employees who left early
     * 
     * @param int|null $regionId Optional region filter to scope results
     * @param int|null $branchId Optional branch filter to scope results
     * @return array Associative array with late/early attendance counts
     * 
     */
    public function getLateInEarlyOutDetails(int $regionId = null, int $branchId = null): array;

    public function getFilteredEmployeeCountsInJob(?int $departmentId = null): array;

    public function getGenderMaritalStatusCounts(int $regionId = null, int $branchId = null): array;
}
