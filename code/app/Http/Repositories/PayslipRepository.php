<?php

namespace App\Http\Repositories;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class PayslipRepository extends Repository
{
    /**
     * get expiring payslip count from the payslips table, which are going to expire within 30 days from now and that are active
     */
    public function getExpiringPayslipCount($days = 30)
    {
        $today = Carbon::today();
        $daysFromNow = $today->copy()->addDays($days)->format('Y-m-d');

        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')->where([
            ['stop_date_eng', '>=', $today->format('Y-m-d')],
            ['stop_date_eng', '<=', $daysFromNow],
            ['status', 'Active']
        ]);

        if (!scopeAll()) {
            $query = $query->where('payslips.company_id', currentEmployee()?->company_id);
            $query = filterEmployeesByScope($query, 'org');
        }

        $count = $query->count();

        return $count;
    }

    /**
     * get pending payslip requests that are yet to be approved or not in final states(REJECTED, UPLOADED) from payslip_requests table
     */
    public function getPendingPayslipRequestCount()
    {
        $query = PayslipRequest::leftJoin('employee_org as org', 'org.employee_id', 'payslip_requests.employee_id')
            ->whereNotIn('state', ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL));
        if (!scopeAll()) {
            $query = $query->where('payslip_requests.company_id', currentEmployee()?->company_id);
            $query = filterEmployeesByScope($query, 'org');
        }
        return $query->count();
    }

    /**
     * get active payslips count from the payslips table
     */
    public function getActivePayslipsCount()
    {
        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')->where('status', 'Active');
        if (!scopeAll()) {
            $query = $query->where('payslips.company_id', currentEmployee()?->company_id);
            $query = filterEmployeesByScope($query, 'org');
        }
        return $query->count();
    }

    /**
     * get employees who doesn't have payslip request or payslips in any states.
     */
    public function getEmployeeWithNoPayslipsCount()
    {
        $query = Employee::leftJoin('payslips', 'employees.id', '=', 'payslips.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin('payslip_requests', 'employees.id', '=', 'payslip_requests.employee_id')
            ->orWhere(
                fn($query) =>
                $query->whereNull('payslips.employee_id')
                    ->whereNull('payslip_requests.employee_id')
            )
            ->whereNull('payslip_requests.deleted_at')
            ->whereNull('payslips.deleted_at');

        if (!scopeAll()) {
            $query = filterEmployeesByScope($query, 'org');
        }

        return $query->distinct()->count('employees.id');
    }

    /**
     * Get the count of terminated employees with payslip
     */
    public function getTerminatedEmployeePayslipCount()
    {
        $query = Employee::leftJoin('payslips', 'employees.id', '=', 'payslips.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->where(
                fn($query) =>
                $query->whereNotNull('payslips.employee_id')
            )
            ->whereNotNull('org.termination_date')
            ->whereNotNull('org.deleted_at')
            ->withTrashed();

        if (!scopeAll()) {
            $query = filterEmployeesByScope($query, 'org');
        }

        return $query->distinct()->count('employees.id');
    }

    /**
     * Get the count of expired payslip
     */
    public function getExpiredPayslipsCount($days = 30)
    {
        $today = Carbon::today();
        $daysFromNow = $today->copy()->subDays($days)->format('Y-m-d');

        $query = Payslip::leftJoin('employee_org as org', 'org.employee_id', 'payslips.employee_id')->where([
            ['stop_date_eng', '<=', $today->format('Y-m-d')],
            ['stop_date_eng', '>=', $daysFromNow],
            ['status', 'Expired']
        ]);

        if (!scopeAll()) {
            $query = $query->where('payslips.company_id', currentEmployee()?->company_id);
            $query = filterEmployeesByScope($query, 'org');
        }
        return $query->count();
    }

    public function needDojInhouse($employeeId, $status)
    {
        // return Cache::remember('needDojInhouse' . $employeeId . '' . $status, 10, function () use ($employeeId, $status) {
            if (!$status || $status == "Outsource") return false;
            $payslip = Payslip::where('employee_id', $employeeId)->where('status', 'Active')->first();
            if ($payslip?->enroll_status === "Inhouse") return false;
            return true;
        // });
    }
}
