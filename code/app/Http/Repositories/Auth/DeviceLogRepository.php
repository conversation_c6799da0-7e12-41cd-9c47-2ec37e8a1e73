<?php

namespace App\Http\Repositories\Auth;

use App\Http\Helpers\QRCodeHelper;
use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\Repository;
use App\Http\Repositories\UserRepository;
use App\Models\Device\DeviceDetails;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Cache;
use PermissionList;
use Illuminate\Support\Facades\Hash;

class DeviceLogRepository extends Repository implements IDeviceLogRepository
{

    public function listDeviceDetails(int $userId)
    {
        return DeviceDetails::where('user_id', $userId)->get();
    }

    private function storeDeviceDetails($params, $additionalParams = [])
    {
        $checkParams = [
            'agent'           => $params['agent'],
            'device'          => $params['device'],
            'device_brand'    => $params['device_brand'],
            'device_platform' => $params['device_platform'],
            'user_id'         => $params['user_id'],
            'device_model'    => $params['device_model'],
        ];


        $updateParams = [
            'device_version'   => $params['device_version'],
            'app_version'      => $params['app_version'],
            'app_version_code' => $params['app_version_code'],
            'last_activity'    => date('Y-m-d H:i:s'),
            'app_login_code'   => $params['app_login_code'],
            ...$additionalParams,
        ];
        if (isset($params['biometric_enabled'])) {
            $updateParams['biometric_enabled'] = $params['biometric_enabled'];
        }

        if (isset($params['fcm_token']) && $params['fcm_token'] != null) {
            $updateParams['fcm_token'] = $params['fcm_token'];
        }

        return DeviceDetails::updateOrInsert(
            $checkParams,
            $updateParams,
        );
    }

    public function storeLoginTimeDevice($params)
    {
        $this->storeDeviceDetails($params, [
            'login_time' => $params['login_time'] ?? now(),
            'status' => 'active'
        ]);
    }

    public function storeMpinDevice($params)
    {
        $this->storeDeviceDetails($params, [
            'mpin' => $params['mpin'],
        ]);
    }

    public function updateDeviceActivity($params)
    {
        $this->storeDeviceDetails($params);
        logInfo("Device Activity Updated");
    }

    public function checkMpinExistsForDevice($params)
    {
        return DeviceDetails::where([
            'agent'           => $params['agent'],
            'device'          => $params['device'],
            'device_brand'    => $params['device_brand'],
            'device_platform' => $params['device_platform'],
            'device_model'    => $params['device_model'],
            'user_id'         => $params['user_id'],
            'app_login_code'  => $params['app_login_code'],
        ])->whereNotNull("mpin")
            ->exists();
    }

    public function checkBiometricExistsForDevice($params)
    {
        return DeviceDetails::where([
            'agent'           => $params['agent'],
            'device'          => $params['device'],
            'device_brand'    => $params['device_brand'],
            'device_platform' => $params['device_platform'],
            'device_model'    => $params['device_model'],
            'user_id'         => $params['user_id'],
            'app_login_code'  => $params['app_login_code'],
            'biometric_enabled' => true,
        ])->exists();
    }

    public function checkOtpUsedForDevice($params)
    {
        //
    }

    public function verifyMpin($params): array
    {
        $deviceDetails =  DeviceDetails::where(
            [
                'agent'           => $params['agent'],
                'device'          => $params['device'],
                'device_brand'    => $params['device_brand'],
                'device_platform' => $params['device_platform'],
                'device_model'    => $params['device_model'],
                'user_id'         => $params['user_id'],
                'mpin'            => $params['mpin'],
                'app_login_code'  => $params['app_login_code'],
            ]
        )->exists();
        if (! $deviceDetails) {
            return ['status' => false];
        }
        $accessToken = Hash::make($params['app_login_code']);
        return [
            'status' => true,
            'token' => $accessToken,
        ];
    }

    public function verifyBiometric($params): bool
    {
        $deviceDetails =  DeviceDetails::where(
            [
                'agent'           => $params['agent'],
                'device'          => $params['device'],
                'device_brand'    => $params['device_brand'],
                'device_platform' => $params['device_platform'],
                'device_model'    => $params['device_model'],
                'user_id'         => $params['user_id'],
                'app_login_code'  => $params['app_login_code'],
                'biometric_enabled' => $params['biometric_enabled'],
            ]
        )->exists();
        if ($deviceDetails)
            return true;

        return false;
    }
    public function deactivateDevice($params): bool
    {
        $deviceDetail = DeviceDetails::where($params)->first();
        if (!$deviceDetail) {
            throw new Exception("Device not found");
        }

        $user = currentEmployee()?->user;
        if (!$user) {
            throw new Exception("User not authenticated");
        }

        $userId = $deviceDetail->user_id;
        if (!$user->can(PermissionList::REVOKE_DEVICE_ALL) && $userId != $user?->id) {
            logError("Unauthorized attempt to deactivate device for user ID: {$userId}");
            throw new Exception("Unauthorized action");
        }

        return DeviceDetails::where($params)
            ->update(['status' => 'inactive']);
    }

    public function resetMpin(array $params): bool
    {
        $deviceDetail = DeviceDetails::where($params)->first();
        if (!$deviceDetail) {
            return false;
        }

        $userId = $deviceDetail->user_id;
        if (!auth()->user()->can(PermissionList::RESET_MPIN_ALL) && $userId != auth()->user()?->id) {
            logError("Unauthorized attempt to reset MPIN for user ID: {$userId}");
            return false;
        }

        return DeviceDetails::where($params)
            ->update(['mpin' => null]);
    }

    public function deactivateDevices(\Carbon\Carbon $timeFrame): bool
    {
        // Prevent accidental future deactivation
        if ($timeFrame->isFuture()) {
            throw new \InvalidArgumentException('Time frame cannot be in the future.');
        }

        return DeviceDetails::where([
            ['last_activity', '<=', $timeFrame],
            ['status', 'active'],
        ])
            ->update(['status' => 'inactive']);
    }

    public function getFcmTokens(int $userId, bool $active = true): array
    {
        return DeviceDetails::where('user_id', $userId)
            ->where('status', $active ? 'active' : 'inactive')
            ->pluck('fcm_token')
            ->toArray();
    }

    public function generateQrCode($params): array
    {
        $appLoginCode = \Illuminate\Support\Facades\Hash::make($params['username'] . "" . now());
        $appSettingRepo = app(\App\Http\Repositories\Setting\AppSettingRepository::class);
        $profileArray = [
            'name' => $params['name'],
            'code' => $params['employee_code'],
            'department' => $params['department'],
            'branch' => $params['branch'],
            'contact' => $params['contact'],
            'username' => $params['username'],
            'base_url' => $appSettingRepo->getBaseUrl(),
            'user_code' => $appLoginCode,
        ];

        $jsonProfile = json_encode($profileArray, JSON_PRETTY_PRINT);
        $qrCode = QRCodeHelper::generateQRCode($jsonProfile);

        $cacheKey = $params['username'] . $params['employee_id'] . "qrCode";
        Cache::tags('app')->put($cacheKey, $qrCode, now()->addHours(2));

        $updateLoginCode = app(UserRepository::class)->updateLoginCode($params['user_id'], $appLoginCode);

        return [
            'qrCode' => $qrCode,
            'updateLoginCode' => $updateLoginCode,
        ];
    }

    public function getCachedQrCode(string $username, int|string $employeeId): string
    {
        $code = $username . $employeeId . "qrCode";
        $dataUri = Cache::tags('app')->get(key: $code) ?? "";
        return $dataUri;
    }

    public function rejectDevice(array $params): bool
    {
        return DeviceDetails::where($params)
            ->update(['status' => 'rejected']);
    }

    public function approveDevice(array $params): bool
    {
        return DeviceDetails::where($params)
            ->update(['status' => 'active']);
    }
}
