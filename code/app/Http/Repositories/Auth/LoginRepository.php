<?php

namespace App\Http\Repositories\Auth;

use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\Repository;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Laravel\Fortify\Fortify;

class LoginRepository extends Repository
{
    public $apiResponseHelper;

    public function __construct()
    {
        $this->apiResponseHelper = new ApiResponseHelper();
    }
    /**
     * @throws ValidationException
     */
    public function fortifyCustomLogin(Request $request)
    {
        $user = User::where('username', $request->username)->first();

        // Attempt local authentication
        $authenticated = $user && Hash::check($request->password, $user->password);

        // If local auth fails, try external auth (e.g., iOPS)
        if (!$authenticated) {
            if (vianetHrm()) {
                $user = $this->iopsLogin($request->username, $request->password);
                if (!$user) return false;
            } else {
                return false;
            }
        }

        // Check if the account is suspended
        if ($user->suspend_account) {
            throw ValidationException::withMessages([
                Fortify::username() => ['Your account has been suspended.'],
            ]);
        }

        // Handle "remember me" token
        $user->setRememberToken($request->boolean('remember') ? Str::random(60) : null);
        $user->save();

        return $user;
    }

    /**
     * Authenticate user via IOPS API
     *
     * @param string $username User's username
     * @param string $password User's password
     * @return User|false Returns User object if authentication successful, false otherwise
     */
    private function iopsLogin($username, $password)
    {
        $apiUsername = config('app.empAppUsername');
        $apiPassword = config('app.empAppPassword');

        $baseUrl = "https://empapp.vianet.com.np";

        try {
            $client = new \GuzzleHttp\Client([
                // 'verify' => false,
                'base_uri' => $baseUrl
            ]);

            $tokenResponse = $client->post("/api/authentication/user", [
                'auth' => [$apiUsername, $apiPassword],
            ]);

            $tokenData = json_decode($tokenResponse->getBody(), true);

            if ($tokenData['status'] !== "1") {
                return false;
            }

            $token = $tokenData['response']['token'];

            // Step 2: Perform IOPS login
            $loginResponse = $client->post('/api/iops_login', [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => compact('username', 'password')
            ]);

            $loginData = json_decode($loginResponse->getBody(), true);

            if ($loginData['status'] !== "1") {
                return false;
            }

            $user = User::where('username', $username)->first();
            if (!$user) {
                \logError("User not found for username '$username', but found on iops");
                return false;
            }

            $user->password = Hash::make($password);
            $user->password_change_date = now();
            $user->save();

            session(['iops_login' => true]);

            return $user;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \logError("Error while iops login", $e);
            return false;
        }
    }
}
