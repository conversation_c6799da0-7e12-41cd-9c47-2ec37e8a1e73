<?php

namespace App\Http\Repositories\Auth\Interfaces;

interface ILoginAppRepository
{
    public function checkUser($params): array;

    public function checkFeatures($params);

    public function verifyOTP($params): array;

    public function setMpin($params): bool;

    public function fetchToken($params);

    public function fetchUserDetail($params);
    public function firebaseTopics($userId);
}
