<?php

namespace App\Http\Repositories\Auth\Interfaces;

interface IDeviceLogRepository
{
    public function listDeviceDetails(int $userId);

    public function storeLoginTimeDevice($params);

    public function storeMpinDevice($params);

    public function updateDeviceActivity($params);

    public function checkMpinExistsForDevice($params);

    public function checkBiometricExistsForDevice($params);

    public function checkOtpUsedForDevice($params);

    public function verifyMpin($params): array;

    public function deactivateDevice(array $params);

    public function deactivateDevices(\Carbon\Carbon $timeFrame): bool;

    public function getFcmTokens(int $userId, bool $active = true): array;

    public function resetMpin(array $params): bool;

    public function verifyBiometric($params): bool;
    public function generateQrCode(array $params): array;

    public function getCachedQrCode(string $username, int|string $employeeId): string;

    public function rejectDevice(array $params): bool;

    public function approveDevice(array $params): bool;
}
