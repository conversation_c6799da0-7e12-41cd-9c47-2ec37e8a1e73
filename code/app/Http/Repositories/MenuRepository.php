<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Constant;
use App\Models\Admin\Menu;
use App\Models\Admin\MenuItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class MenuRepository
{
  private $menu;

  public function __construct($menuName = null)
  {
    $menuName = $menuName ?? env("DEFAULT_MENU", "side_menu");
    $this->menu = Menu::where("name", $menuName)->select("id")->first();
  }

  public function permittedMenuItems($menuItems, $all = false)
  {
    $permitted = [];
    if($all || Auth::user()->hasRole(Constant::ROLE_SUPER_ADMIN)) {
      return $menuItems;
    }
    foreach ($menuItems as $item) {
      if ($item->route_name) {
        if (Auth::user()->hasAnyRole($item->roles->pluck("name")->toArray())) {
          $permitted[] = $item;
        }
      } else {
        $permitted[] = $item;
      }
    }
    return $permitted;
  }

  public function buildTree($menuItems, $parent = null)
  {
    $menu = [];
    foreach ($menuItems as $item) {
      if ($item->parent_id == $parent) {
        $children = $this->buildTree($menuItems, $item->id);
        $menu[] = [
          "id" => $item->id,
          "label" => $item->label,
          // "uri" => $item->url,
          "route_name" => $item->route_name,
          "position" => $item->position,
          "icon" => $item->icon,
          "wire_navigate" => $item->wire_navigate,
          "children" => $children
        ];
      }
    }
    return $menu;
  }

  public function menuJsonForConfig()
  {
    $menuItems = $this->fetchMenuItems();
    return $menuItems->map(function ($item) {
      $menu = [
        "id" => $item->id,
        "label" => $item->label,
        "order" => $item->position,
      ];
      if ($item->parent_id) {
        $menu['parent'] = $item->parent_id;
      }
      return $menu;
    });
  }

  public function getMenuList()
  {
    if (!$this->menu) return [];
    $userId = session(Constant::SESSION_USER_ID);
    $cacheName = "{$userId}menu{$this->menu->id}";
    $cachedMenuList = Cache::get($cacheName);
    if ($cachedMenuList) {
      return $cachedMenuList;
    }
    $menuItems = $this->fetchMenuItems();

    $permitted = $this->permittedMenuItems($menuItems);
    $menuList = $this->buildTree($permitted);

    foreach ($menuList as $key => $item) {
      if (empty($item["children"]) && $item["route_name"] == "")
        unset($menuList[$key]);
    }

    Cache::put($cacheName, $menuList);
    return $menuList;
  }

  public function getAllMenuTree() {
    if (!$this->menu) return [];
    $menuItems = $this->fetchMenuItems();
    $permitted = $this->permittedMenuItems($menuItems, all:\true);
    $menuList = $this->buildTree($permitted);

    foreach ($menuList as $key => $item) {
      if (empty($item["children"]) && $item["route_name"] == "")
        unset($menuList[$key]);
    }

    return $menuList;
  }

  private function fetchMenuItems() {
    return  MenuItem::where("menu_id", $this->menu->id)
    ->select("id", "label", "route_name", "target", "icon", "parent_id", "position", "wire_navigate")
    ->orderBy("position")
    ->get();
  }
}
