<?php

namespace App\Http\Repositories;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\EmployeeTicketProcess as EnumsEmployeeTicketProcess;
use App\Http\Helpers\Enums\EmployeeTicketTransferKey;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Livewire\Payroll\Band;
use App\Livewire\Payroll\Designation;
use App\Models\configs\Branch;
use App\Models\configs\Job;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Employee\EmployeeTransferDetails;
use App\Models\RequestTicket;
use App\Models\EmployeeTicket\EmployeeTicketMeta;
use App\Models\EmployeeTicket\EmployeeTicketProcess;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\PayslipRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

use function PHPUnit\Framework\returnSelf;

class EmployeeTicketRepository extends Repository
{
    /**
     * Get the detail of a employee ticket depending upon the process type.
     *
     * @param int $ticket_id
     * @return array
     */
    public function getEmployeeTicketDetail($ticket_id)
    {
        $employeeTicket = EmployeeTicket::findOrFail($ticket_id);
        $process = EmployeeTicketProcess::findOrFail($employeeTicket->process_id);
        $data = ['process' => $process->name];
        switch ($process->name) {
            case EnumsEmployeeTicketProcess::Transfer:
                $data = [...$data, ...$this->getTransferTicketDetail($ticket_id)];
                break;
        }
        return $this->responseHelper(true, data: $data);
    }

    /**
     * Get the details of a transfer ticket.
     *
     * @param int $ticket_id
     * @return array
     */
    private function getTransferTicketDetail($ticket_id)
    {
        $metaData = EmployeeTicketMeta::where("employee_ticket_id", $ticket_id)->get();
        $newPayslipId = null;
        $newPayslipDetail = [];
        $newSalaryDetail = [];
        $oldPayslipDetail = [];
        $oldSalaryDetail = [];
        $details = [];
        $payslipTicketLink = "";
        $payslipChange = false;
        $type = "";
        foreach ($metaData as $meta) {
            $key = $meta->meta_key;
            if (
                in_array($key, [
                    'payslip_change',
                    'new_payslip_details',
                    'new_payslip_detail_extended',
                    'old_payslip_detail_extended',
                    'payslip_id',
                    'type'
                ])
            ) {
                switch ($key) {
                    case 'new_payslip_detail_extended':
                        $newPayslipDetail = $meta->meta_value['payslipInformation'] ?? [];
                        $newSalaryDetail = $meta->meta_value['salaryStructure'] ?? [];
                        break;

                    case 'old_payslip_detail_extended':
                        $oldPayslipDetail = $meta->meta_value['payslipInformation'] ?? [];
                        $oldSalaryDetail = $meta->meta_value['salaryStructure'] ?? [];
                        break;

                    case 'payslip_id':
                        $payslipId = $meta->meta_value;
                        $payslipRequest = PayslipRequest::withTrashed()->find($payslipId);
                        if (!$payslipRequest) {
                            throw new \Exception("Payslip not found");
                        }
                        $payslipTicketLink = route('ticketPage', ['requestId' => $payslipRequest->id, 'workflow' => $payslipRequest->workflow]);
                        break;

                    case 'type':
                        $type = $meta->meta_value;
                        break;

                    case 'payslip_change':
                        $payslipChange = (bool) $meta->meta_value;
                }
            } else {
                $value = is_array($meta->meta_value) ? $meta->meta_value : [];
                $name = $value['name'] ?? "";

                // Map meta keys to model names and corresponding column names
                $details[$key] = $name;
            }
        }

        $separatedData = [];
        foreach ($details as $key => $value) {
            // Separate the key by "_"
            $parts = explode('_', $key, 2);

            // Check if the key has "from" or "to" prefix
            if (count($parts) > 1 && ($parts[0] == 'from' || $parts[0] == 'to')) {
                $direction = $parts[0]; // "from" or "to"
                $field = $parts[1]; // "company", "branch", etc.

                // Initialize the sub-array if it doesn't exist
                if (!isset($separatedData[$direction])) {
                    $separatedData[$direction] = [];
                }

                // Assign the value to the corresponding field
                $separatedData[$direction][$field] = $value;
            }
        }
        return [
            ...$separatedData,
            "old_payslip_information"   => $oldPayslipDetail,
            "old_salary_details"        => $oldSalaryDetail,
            "new_payslip_information"   => $newPayslipDetail,
            "new_salary_details"        => $newSalaryDetail,
            'payslipTicketLink'         => $payslipTicketLink,
            'payslip_change'            => $payslipChange,
            'type'                      => $type
        ];
    }

    /**
     * Get default data for a specific process (used for filling the form of employee ticket initially).
     *
     * @param int $employee_id
     * @param string $process
     * @return mixed
     */
    public function getDefaultData(int $employee_id, string $process)
    {
        $process = EmployeeTicketProcess::where("name", $process)->first();
        if (!$process) return $this->responseHelper(false, "Can't find process");
        switch ($process->name) {
            case EnumsEmployeeTicketProcess::Transfer:
                return $this->getDefaultDataForTransferTicket($employee_id);
        }
    }

    /**
     * Get default data for a transfer ticket.
     *
     * @param int $employee_id
     * @return mixed
     */
    private function getDefaultDataForTransferTicket($employee_id)
    {
        $employee = Employee::with('organizationInfo:id,employee_id,department_id,branch_id,sub_branch_id,unit_id')
            ->where('id', $employee_id)
            ->select('id', 'company_id')
            ->first();

        if (!$employee) return $this->responseHelper(false, "Can't find employee detail");

        return $this->responseHelper(true, data: [
            "company_id"    => $employee->company_id ?? "",
            "department_id" => $employee->organizationInfo->department_id ?? "",
            "branch_id"     => $employee->organizationInfo->branch_id ?? "",
            "sub_branch_id" => $employee->organizationInfo->sub_branch_id ?? "",
            "unit_id"       => $employee->organizationInfo->unit_id ?? "",
        ]);
    }

    /**
     * Create a transfer ticket.
     *
     * @param int $employeeId The ID of the employee for whom the ticket is being created.
     * @param array $data The data for creating the transfer ticket.
     * @param array $validationAttributes Additional validation attributes.
     * @return mixed The result of creating the transfer ticket.
     */
    public function createTransferTicket(int $employeeId, array $data, array $validationAttributes = [])
    {
        // Retrieve employee and organization information
        $employee = Employee::with([
            'company:id,name',
            'organizationInfo:id,employee_id,branch_id,sub_branch_id,department_id,unit_id,employee_status_id',
            'organizationInfo.employee_status:id,name',
            'organizationInfo.branch:id,name',
            'organizationInfo.subBranch:id,name',
            'organizationInfo.department:id,name',
            'organizationInfo.unit:id,name'
        ])
            ->where('id', $employeeId)->select('id', 'company_id')->first();
        if (!$employee) {
            return $this->responseHelper(false, "Employee not found");
        }
        $empOrg = $employee->organizationInfo;

        // Check the process and old tickets
        $processResponse = $this->checkProcess(EnumsEmployeeTicketProcess::Transfer);
        if (!$processResponse['status']) return $processResponse;

        $process = $processResponse['data'];

        $checkOldTicketResponse = $this->checkOldTicket($employee, $process);
        if (!$checkOldTicketResponse['status']) return $checkOldTicketResponse;

        // ensure that details doesn't match for transfer, can't transfer on the same data
        if ($data['type'] == 'transfer') {
            if (
                $employee->company_id == $data['company_id'] &&
                $empOrg->branch_id == $data['branch_id'] &&
                $empOrg->sub_branch_id == $data['sub_branch_id'] &&
                $empOrg->department_id == $data['department_id'] &&
                $empOrg->unit_id == $data['unit_id']
            ) {
                return $this->responseHelper(false, "Please change at least one detail");
            }
        }

        $toCompany = \App\Models\configs\Company::find($data['company_id']);
        $toBranch = \App\Models\configs\Branch::find($data['branch_id']);
        $toSubBranch = \App\Models\configs\SubBranch::find($data['sub_branch_id']);
        $toDepartment = \App\Models\configs\Department::find($data['department_id']);
        $toUnit = \App\Models\configs\Unit::find($data['unit_id']);
        $empOrg = $employee->organizationInfo;

        $jobId = ($empOrg->activePayslips[0] ?? null)?->job_id;

        if ($jobId) {
            if (
                $data['branch_id'] != $empOrg->branch_id
                || $data['department_id'] != $empOrg->department_id
                || ($data['job_id'] ?? false ? $data['job_id'] != $jobId : false)
            ) {
                try {
                    $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
                    $jobSeatRepo->checkCapacity([
                        'company_id'    => $data['company_id'],
                        'branch_id'     => $data['branch_id'],
                        'department_id' => $data['department_id'],
                        'job_id'        => $data['job_id'] ?? $jobId
                    ]);
                } catch (\Exception $e) {
                    return $this->errorResponse($e->getMessage());
                }
            }
        }

        // prepare meta data for the transfer ticket
        $metaData = [
            EmployeeTicketTransferKey::FromCompany      => ["id" => $employee->company?->id,    "name" => $employee->company?->name],
            EmployeeTicketTransferKey::ToCompany        => ["id" => $toCompany->id,             "name" => $toCompany->name],
            EmployeeTicketTransferKey::FromBranch       => ["id" => $empOrg->branch?->id,       "name" => $empOrg->branch?->name],
            EmployeeTicketTransferKey::ToBranch         => ["id" => $toBranch->id,              "name" => $toBranch->name],
            EmployeeTicketTransferKey::FromSubBranch    => ["id" => $empOrg->subBranch?->id,    "name" => $empOrg->subBranch?->name],
            EmployeeTicketTransferKey::ToSubBranch      => ["id" => $toSubBranch?->id,          "name" => $toSubBranch?->name],
            EmployeeTicketTransferKey::FromDepartment   => ["id" => $empOrg->department?->id,   "name" => $empOrg->department?->name],
            EmployeeTicketTransferKey::ToDepartment     => ["id" => $toDepartment->id,          "name" => $toDepartment->name],
            EmployeeTicketTransferKey::FromUnit         => ["id" => $empOrg->unit?->id,         "name" => $empOrg->unit?->name],
            EmployeeTicketTransferKey::ToUnit           => ["id" => $toUnit?->id,               "name" => $toUnit?->name],
        ];

        $metaData['type'] = $data['type'];
        $metaData['payslip_change'] = $data['payslip_change'];
        if ($data['payslip_change']) {
            $metaData['new_payslip_details'] = [
                'employee_id'           => $employeeId,
                'verifier_id'           => $data['verifier_id'],
                'job_id'                => $data['job_id'],
                'designation_id'        => $data['designation_id'],
                'band_id'               => $data['band_id'],
                'pgrade_id'             => $data['level_id'],
                'allowance_metadata'    => $data['additionalAllowance'] ?? [],
                'enroll_status'         => $data['enroll_status'],
                'employee_status_id'    => $data['employee_status'],
                'doj_inhouse'           => $data['doj_inhouse'] ?? null,
                'stipend'               => $data['stipend'],
                'cit'                   => $data['cit'],
                'insurance'             => $data['insurance'],
                'insurance_expiry_date' => $data['insurance_expiry_date'],
                'allow_ot'              => $data['allow_ot'],
                'payslip_for'           => $data['payslip_for'],
                'payment_frequency'     => $data['payment_frequency'],
                'payment_type'          => $data['payment_type'],
                'payslip_type'          => $data['payslip_type'],
                'remarks'               => $data['payslip_remarks'],
                'start_date_eng'        => (LaravelNepaliDate::from($data['start_date_nep']))->toEnglishDate(),
                'stop_date_eng'         => (LaravelNepaliDate::from($data['stop_date_nep']))->toEnglishDate(),
                'start_date_nep'        => $data['start_date_nep'],
                'stop_date_nep'         => $data['stop_date_nep'],
            ];
            $metaData['new_payslip_detail_extended'] = $this->setPayslipDetail($metaData['new_payslip_details'], $data['salary_details']);
            $payslipRepo = new PayslipApprovalRepository;
            $metaData['old_payslip_detail_extended'] = $payslipRepo->getTransferPayslipData($employeeId);;
        }
        return $this->createEmployeeTicket($employee, $process, $data, $metaData);
    }

    public function setPayslipDetail($data, $salaryDetails)
    {
        $job = Job::find($data['job_id']);
        $designation = \App\Models\Payroll\Designation::find($data['designation_id']);
        $band = \App\Models\Payroll\EmployeeBand::find($data['band_id']);
        $pgrade = \App\Models\Payroll\EmployeePgrade::find($data['pgrade_id']);
        $employee_status = \App\Models\configs\EmpStatus::find($data['employee_status_id']);
        $additionalAllowance = json_decode($data['allowance_metadata'] ? $data['allowance_metadata'] : "") ?? [];
        $additionalAllowanceMetaData = [];
        $totalCtc = $salaryDetails['ctc'] ?? 0;
        foreach ($additionalAllowance ?? [] as $id => $item) {
            foreach ($item as $key => $value)
                $additionalAllowanceMetaData[$key] = $value ? $value : 0;
            $totalCtc += (float)$value;
        }
        return [
            'payslipInformation' => [
                'Job Profile'           => $job->name,
                'Designation'           => $designation?->title,
                'Grade'                 => "$band?->name($pgrade?->name)",
                "Enroll Status"         => $data['enroll_status'],
                ...($data['doj_inhouse'] ? ["DOJ Inhouse" => $data['doj_inhouse']] : []),
                "Employee Status"       => $employee_status?->name,
                "Stipend"               => $data['stipend'],
                'OT Allowed'            => $data['allow_ot'] ? 'Yes' : 'No',
                'Payment Type'          => $data['payment_type'],
                'Payslip Type'          => $data['payslip_type'],
                'Payslip For'           => ($data['payslip_for'] ?? null) == 'Full_time' ? 'Regular' : ($data['payslip_for'] ?? null),
                'Payslip Frequency'     => $data['payment_frequency'] ?? null,
                'Insurance Expiry'      => $data['insurance_expiry_date'],
                'Remarks'               => $data['remarks'] ? $data['remarks'] :  'N/A',
                'Payslip Start Date'    => $data['start_date_nep'] . '(' . (LaravelNepaliDate::from($data['start_date_nep']))->toEnglishDate() . ')',
                'Payslip End Date'      => $data['stop_date_nep'] . '(' . (LaravelNepaliDate::from($data['stop_date_nep']))->toEnglishDate() . ')',
            ],
            'salaryStructure' => [
                'basic_salary'          => $salaryDetails['basic_salary'] ?? 0,
                'pf'                    => $salaryDetails['pf'] ?? 0,
                'ssf'                   => $salaryDetails['ssf'] ?? 0,
                'allowance'             => $salaryDetails['allowance'] ?? 0,
                'gross_salary'          => $salaryDetails['gross_salary'] ?? 0,
                'gratuity'              => $salaryDetails['gratuity'] ?? 0,
                'ctc'                   => $salaryDetails['ctc'] ?? 0,
                'additional_allowance'  => $additionalAllowanceMetaData,
                'total_ctc'             => $totalCtc,
                'insurance'             => $data['insurance'],
                'cit'                   => $data['cit'],
            ]
        ];
    }

    /**
     * Create a employee ticket for the specified employee with the given process and metadata.
     *
     * @param Employee $employee The employee for whom the ticket is being created.
     * @param EmployeeTicketProcess $process The process for the employee ticket.
     * @param mixed $data The data for creating the ticket.
     * @param array $metaData The metadata associated with the ticket.
     * @return mixed The result of creating the employee ticket.
     */
    private function createEmployeeTicket(Employee $employee, EmployeeTicketProcess $process, $data, $metaData)
    {
        \logInfo("Params for creating $process->name ticket", [...$data, "employee_id" => $employee->id]);
        DB::beginTransaction();
        try {
            // Create the employee ticket
            $employeeTicket = EmployeeTicket::create([
                "process_id"    => $process->id,
                "employee_id"   => $employee->id
            ]);

            $employeeTicket->applyWorkflow(WorkflowName::EMPLOYEE_TRANSFER);
            \logInfo("Employee Ticket Created", $employeeTicket->toArray());

            // inserting meta data for the transfer
            $bulkMetaData = [];
            foreach ($metaData as $metaKey => $metaValue) {
                $bulkMetaData[] = [
                    'employee_ticket_id'    => $employeeTicket->id,
                    'meta_key'              => $metaKey,
                    'meta_value'            => $metaValue
                ];
            }
            foreach ($bulkMetaData as $item) {
                \App\Models\EmployeeTicket\EmployeeTicketMeta::create($item);
            }
            \logInfo("Meta data inserted for the $process->name for employee ticket $employeeTicket->id", $bulkMetaData);

            // validate the next owner id
            $nextOwnerId = $data["next_owner_id"];
            $nextOwners = $employeeTicket->getNextOwners(employeeId: $employee->id, isSubmitting: true, workflow: WorkflowName::EMPLOYEE_TRANSFER);

            if (!\in_array($nextOwnerId, $nextOwners->pluck('id')->toArray())) {
                return $this->responseHelper(false, "Invalid Next Owner");
            }

            // Create the request ticket for the employee ticket
            $ticketRepo = new \App\Http\Repositories\TicketRepository;
            $ticketRepo->createRequestTicket($employeeTicket, [
                'employee_id'       => $employeeTicket->employee_id,
                'current_owner_id'  => $data['next_owner_id'],
                'documents'         => $data['documents'] ?? []
            ]);

            DB::commit();
            \logInfo("Employee Ticket ($process->name) created successfully");
            return $this->responseHelper(true, "$process->name Ticket created successfully");
        } catch (\Exception $e) {
            DB::rollBack();
            \logError("Error while creating $process->name ticket", $e);
            return $this->responseHelper(false, "Error while creating $process->name Ticket");
        }
    }

    /**
     * Check the validity of the specified employee ticket process.
     *
     * @param string $processName The name of the employee ticket process.
     * @return mixed The result of the process validation.
     */
    private function checkProcess(string $processName)
    {
        $process = EmployeeTicketProcess::where("name", $processName)->first();
        if (!$process) return $this->responseHelper(false, "Can't find process");
        if (!auth()->user()->hasPermissionTo($process->permission_id))
            return $this->responseHelper(false, "You don't have permission to create this ticket");

        return $this->responseHelper(true, data: $process);
    }

    /**
     * Check if there is an existing employee ticket for the specified employee and process.
     *
     * @param Employee $employee The employee to check for.
     * @param EmployeeTicketProcess $process The process to check against.
     * @return mixed The result of the ticket check.
     */
    private function checkOldTicket(Employee $employee, EmployeeTicketProcess $process)
    {
        $oldTicket = EmployeeTicket::where([
            ["employee_id", $employee->id],
            ["process_id", $process->id]
        ])
            ->whereNotIn('state', ArflowHelper::getFinalStates($process->workflow))
            ->first();

        if ($oldTicket)
            return $this->responseHelper(false, "Employee Ticket for $process->name for this employee already exists");

        return $this->responseHelper(true);
    }

    /**
     * Approve an employee transfer specified by the employee ticket.
     *
     * @param EmployeeTicket $ticket The employee ticket to approve.
     * @return mixed The result of the approval process.
     */
    public function approveEmployeeTransfer(EmployeeTicket $ticket)
    {
        // Retrieve employee details including organization information
        $emp = Employee::with('organizationInfo:id,employee_id,branch_id,sub_branch_id,department_id,unit_id')
            ->where('id', $ticket->employee_id)
            ->select('id', 'company_id')
            ->first();

        if (!$emp) return $this->responseHelper(false, "Employee detail not found");

        // Extract metadata from the ticket
        $metaData = $ticket->metaData->pluck('meta_value', 'meta_key');

        \logInfo("Approving employee transfer ticket of {$emp->id}", $metaData->toArray());

        $fromCompanyId = $metaData[EmployeeTicketTransferKey::FromCompany]['id'];
        $fromBranchId = $metaData[EmployeeTicketTransferKey::FromBranch]['id'];
        $fromDepartmentId = $metaData[EmployeeTicketTransferKey::FromDepartment]['id'];

        $toCompanyId = $metaData[EmployeeTicketTransferKey::ToCompany]['id'];
        $toBranchId = $metaData[EmployeeTicketTransferKey::ToBranch]['id'];
        $toDepartmentId = $metaData[EmployeeTicketTransferKey::ToDepartment]['id'];

        // Check if employee information matches the transfer data
        if (
            !(
                $fromCompanyId == $emp->company_id &&
                $fromBranchId == $emp->organizationInfo->branch_id &&
                $metaData[EmployeeTicketTransferKey::FromSubBranch]['id'] == $emp->organizationInfo->sub_branch_id &&
                $fromDepartmentId == $emp->organizationInfo->department_id &&
                $metaData[EmployeeTicketTransferKey::FromUnit]['id'] == $emp->organizationInfo->unit_id
            )
        ) {
            Log::error("Employee information not matched");
            return $this->responseHelper(false, "Employee Information not matched.");
        }

        // checking if the "to" data of transfer exists or not
        $toCompany = \App\Models\configs\Company::find($toCompanyId);
        if (!$toCompany) return $this->responseHelper(false, "To Company not found");

        $toBranch = \App\Models\configs\Branch::find($toBranchId);
        if (!$toBranch) return $this->responseHelper(false, "To Branch not found");

        $toSubBranch = null;
        $toSubBranchId = $metaData[EmployeeTicketTransferKey::ToSubBranch]['id'];
        if ($toSubBranchId) {
            $toSubBranch = \App\Models\configs\SubBranch::find($toSubBranchId);
            if (!$toSubBranch) return $this->responseHelper(false, "To Sub Branch not found");
        }

        $toDepartment = \App\Models\configs\Department::find($toDepartmentId);
        if (!$toDepartment) return $this->responseHelper(false, "To Department not found");

        $toUnit = null;
        $toUnitId = $metaData[EmployeeTicketTransferKey::ToUnit]['id'];
        if ($toUnitId) {
            $toUnit = \App\Models\configs\Unit::find($toUnitId);
            if (!$toUnit) return $this->responseHelper(false, "To Unit not found");
        }

        $oldJobId = ($emp->organizationInfo->activePayslips[0] ?? null)?->job_id;
        $newPayslipDetail = $metaData['new_payslip_details'] ?? null;
        $destinationJobId = $newPayslipDetail ? $newPayslipDetail['job_id'] : $oldJobId;

        $fromBranch = Branch::find($fromBranchId);

        try {
            if ($destinationJobId) {
                $jobSeatRepo = app(\App\Http\Repositories\Configs\JobSeatRepository::class);
                $fromKey = [
                    'company_id'    => $fromCompanyId,
                    'branch_id'     => $fromBranch->id,
                    'department_id' => $fromDepartmentId,
                    'job_id'        => $oldJobId,
                ];
    
                $toKey = [
                    'company_id'    => $toCompanyId,
                    'branch_id'     => $toBranch->id,
                    'department_id' => $toDepartment->id,
                    'job_id'        => $destinationJobId,
                ];
                
                $jobSeatRepo->moveOnRoleChange($fromKey, $toKey);
            }
        } catch (\Exception $e) {
            return $this->responseHelper(false, $e->getMessage());
        }


        if (isset($newPayslipDetail)) {
            $employeeStatusId = (int) $newPayslipDetail['employee_status_id'];
        } else {
            $employeeStatusId = $emp->organizationInfo?->employee_status_id;
        }
        // Update employee information
        $emp->update([
            "company_id" => $toCompany->id,
        ]);
        \logInfo("Employee updated", ["company_id" => $emp->company_id]);

        $emp->organizationInfo()->update([
            "region_id"     => $toBranch->region_id ?? null,
            "branch_id"     => $toBranch->id,
            "sub_branch_id" => $toSubBranch?->id,
            "region_id"     => $toBranch->region_id,
            "department_id" => $toDepartment->id,
            "unit_id"       => $toUnit?->id,
            "employee_status_id" => $employeeStatusId,
        ]);
        \logInfo("Organization Info of Employee updated", [
            "region_id"     => $toBranch->region_id ?? null,
            "branch_id"     => $toBranch->id,
            "sub_branch_id" => $toSubBranch?->id,
            "department_id" => $toDepartment->id,
            "unit_id"       => $toUnit?->id,
            "employee_status_id" => $employeeStatusId,
        ]);

        $insertData = [
            "employee_id"   => $ticket->employee_id,
            "company_id"    => $metaData[EmployeeTicketTransferKey::FromCompany]['id'] ?? "",
            "branch_id"     => $metaData[EmployeeTicketTransferKey::FromBranch]['id'] ?? "",
            "department_id" => $metaData[EmployeeTicketTransferKey::FromDepartment]['id'],
        ];
        $this->updateEmpTransferDetail($insertData);

        $cugManagementClass = '\Codebright\CugManagement\CugManagement';
        if (class_exists($cugManagementClass)) {
            $params = [
                'employee_id'   => $ticket->employee_id,
                'branch_id'     => $toBranch->id,
                'department_id' => $toDepartment->id
            ];
            \logInfo("Params to update in cug for branch transfer", ["params" => $params]);
            $response = $cugManagementClass::branchTransfer($params);
            \logInfo("Response of update in cug for branch transfer", ["response" => $response]);
        }

        return $this->responseHelper(true);
    }

    public function revertEmployeeTicket(EmployeeTicket $employeeTicket)
    {
        $employee = Employee::find($employeeTicket->employee_id);
        if (!$employee) {
            return $this->responseHelper(false, 'Employee not found');
        }

        $metaData = $employeeTicket->metaData->pluck('meta_value', 'meta_key');

        // check if the detail match the current status or not
        if (
            !($metaData['to_company']['id'] == $employee->company_id
                && $metaData['to_branch']['id'] == $employee->organizationInfo->branch_id
                && $metaData['to_sub_branch']['id'] == $employee->organizationInfo->sub_branch_id
                && $metaData['to_department']['id'] == $employee->organizationInfo->department_id
                && $metaData['to_unit']['id'] == $employee->organizationInfo->unit_id)
        ) {
            return $this->errorResponse("Details not match with current status of employee");
        }

        // validating the from details
        $fromCompany = \App\Models\configs\Company::find($metaData['from_company']['id']);
        if (!$fromCompany) {
            return $this->errorResponse("From Company not found");
        }

        $fromBranch = \App\Models\configs\Branch::find($metaData['from_branch']['id']);
        if (!$fromBranch) {
            return $this->errorResponse("From Branch not found");
        }

        $fromSubBranch = null;
        $fromSubBranchId = $metaData['from_sub_branch']['id'];
        if ($fromSubBranchId) {
            $fromSubBranch = \App\Models\configs\SubBranch::find($fromSubBranchId);
            if (!$fromSubBranch) {
                return $this->errorResponse("From Sub Branch not found");
            }
        }

        $fromDepartment = \App\Models\configs\Department::find($metaData['from_department']['id']);
        if (!$fromDepartment) {
            return $this->errorResponse("From Department not found");
        }
        $fromUnit = null;
        $fromUnitId = $metaData['from_unit']['id'];
        if ($fromUnitId) {
            $fromUnit = \App\Models\configs\Unit::find($fromUnitId);
            if (!$fromUnit) {
                return $this->errorResponse("From Unit not found");
            }
        }
        // update employee information
        $employee->update([
            "company_id" => $fromCompany->id,
        ]);

        $employee->organizationInfo()->update([
            "branch_id"     => $fromBranch->id,
            "sub_branch_id" => $fromSubBranch?->id,
            "department_id" => $fromDepartment->id,
            "unit_id"       => $fromUnit?->id,
        ]);

        return $this->successResponse("Employee ticket has been successfully reverted");
    }

    public function employeeTransferDetail()
    {
        $approvedEmpTransfer = EmployeeTicket::where("state", "Approved")->get();

        $insertData = [];
        foreach ($approvedEmpTransfer as $empTickets) {
            $metaData = $empTickets->metaData->pluck('meta_value', 'meta_key');
            $fromCompany = $metaData['from_company']['id'] ?? "";
            $toCompany = $metaData['to_company']['id'] ?? "";
            $fromBranch = $metaData['from_branch']['id'] ?? "";
            $toBranch = $metaData['to_branch']['id'] ?? "";
            $fromDepartment = $metaData['from_department']['id'];
            $toDepartment = $metaData['to_department']['id'];
            if ($fromCompany != $toCompany || $fromBranch != $toBranch || $fromDepartment != $toDepartment) {
                $employeeId = EmployeeTicket::where("id", $empTickets->id)
                    ->pluck("employee_id")
                    ->first();

                $checkEmp = EmployeeTransferDetails::where([
                    "employee_id"   => $employeeId,
                    "company_id"    => $fromCompany,
                    "branch_id"     => $fromBranch,
                    "department_id" => $fromDepartment,
                    "replaced"      => "N",
                ])->exists();
                if (!$checkEmp) {
                    $insertData[] = [
                        "employee_id"   => $employeeId,
                        "company_id"    => $fromCompany,
                        "branch_id"     => $fromBranch,
                        "department_id" => $fromDepartment,
                        "created_at"    => date("Y-m-d H:i:s"),
                        "updated_at"    => date("Y-m-d H:i:s"),
                    ];
                }
            }
        }

        EmployeeTransferDetails::insert($insertData);
        return $this->responseHelper(true, "Employee Transfer Details Inserted Successfully");
    }

    private function updateEmpTransferDetail($params)
    {
        Log::info("Update EMP Transfer Detail. Params =>" . print_r($params, true));
        $empTransferDetails =  EmployeeTransferDetails::create(
            [
                "employee_id" => $params['employee_id'],
                "company_id" => $params['company_id'],
                "branch_id" => $params['branch_id'],
                "department_id" => $params['department_id']
            ]
        );
        Log::info("Update EMP Transfer Detail. Response =>" . print_r($empTransferDetails, true));
        return $empTransferDetails;
    }
}
