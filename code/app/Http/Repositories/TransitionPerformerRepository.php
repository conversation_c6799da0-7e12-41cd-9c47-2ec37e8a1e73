<?php

namespace App\Http\Repositories;

use App\Models\Employee;
use App\Models\Employee\Employee as ModelsEmployee;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;

class TransitionPerformerRepository extends Repository
{
    public function assignPerformers(
        string $workflow,
        string $state, // state must be in format 'verifier-1'
        array $performerIds,
        array $recipientIds,
    ) {

        Log::info("Assigning performers: ", ['performers' => $performerIds, 'recipients' => $recipientIds]);

        $performers = ModelsEmployee::with('recipients')->findOrFail($performerIds);

        $states = explode('-', $state);
        $performerName = $states[0];
        $level = $states[1] ?? 1;
        $attachedRows = [];
        foreach ($performers as $performer) {
            foreach ($recipientIds as $recipientId) {
                if (!$performer->recipients->contains(function ($recipient) use ($recipientId, $performerName, $level, $workflow) {
                    return $recipient->id == $recipientId
                        && $recipient->pivot->workflow === $workflow
                        && $recipient->pivot->state === $performerName
                        && $recipient->pivot->level === $level;
                })) {
                    $pivotData = [
                        'workflow' => $workflow,
                        'state' => ucfirst($performerName),
                    ];
                    $pivotData['level'] = $level;
                    array_push($attachedRows, [
                        "recipient" => $recipientId,
                        "performer" => $performer->id,
                        "pivotData" => $pivotData
                    ]);
                    $performer->recipients()->attach($recipientId, $pivotData);
                }
                Cache::store('arflow')->flush();
            }
        }
        Log::info("Assigned Performers: ", $attachedRows);;
    }
}
