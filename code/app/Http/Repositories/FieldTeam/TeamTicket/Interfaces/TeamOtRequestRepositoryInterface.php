<?php

namespace App\Http\Repositories\FieldTeam\TeamTicket\Interfaces;

interface TeamOtRequestRepositoryInterface
{
    public function confirmTeamTickets(array $teamIds, string $date, string $start, string $end): array;

    public function getAssignableVerifiers(): array;

    public function submitOtTickets(array $teamAttendance, string $startTime, string $endTime, string $currentDate, int $verifierId, string $remarks): array;
}
