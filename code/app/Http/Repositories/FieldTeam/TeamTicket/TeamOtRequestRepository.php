<?php

namespace App\Http\Repositories\FieldTeam\TeamTicket;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface;
use App\Models\FieldTeam\TeamOtRequest;
use App\Http\Repositories\FieldTeam\TeamTicket\Interfaces\TeamOtRequestRepositoryInterface;
use App\Http\Repositories\OtRequestRepository;
use App\Http\Repositories\TicketRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\FieldTeam\TeamList;
use App\Models\Leaves\Attendance;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TeamOtRequestRepository implements TeamOtRequestRepositoryInterface
{
    public function __construct(
        protected TeamListRepositoryInterface $teamRepo,
        protected TicketRepository $ticketRepo
    ) {}

    public function confirmTeamTickets(array $teamIds, string $date, string $start, string $end): array
    {
        $result = [];

        foreach ($teamIds as $teamId) {
            $membersArray = collect($this->teamRepo->getAllTeamMembers($teamId, $date))->flatten(1);
            $teamLeaderAttendance = $this->teamRepo->getTeamLeaderAttendance($teamId, $date);
            $teamLeader = $this->teamRepo->getTeamLeader($teamId);
            $teamName = TeamList::find($teamId)?->name ?? 'Unknown';

            $leader = $teamLeader ? [
                'id' => $teamLeader->employee->id,
                'name' => $teamLeader->employee->name,
                'employee_code' => $teamLeader->employee->employee_code,
            ] : ['name' => null, 'employee_code' => null];

            $rangeStart = Carbon::parse($start);
            $rangeEnd = Carbon::parse($end);

            // Leader Attendance
            if ($teamLeaderAttendance instanceof Attendance) {
                $logIn = Carbon::parse($teamLeaderAttendance->in_time);
                $logOut = Carbon::parse($teamLeaderAttendance->out_time);
                $status = $teamLeaderAttendance->status;

                $timeCalculation = $this->calculateTimeWithinRange($logIn, $logOut, $rangeStart, $rangeEnd, $status);

                $leaderAttendance = (object) array_merge($leader, [
                    'status' => $status,
                    'in_time' => $logIn->format('h:i A'),
                    'out_time' => $logOut->format('h:i A'),
                    'total_attendance_time' => $teamLeaderAttendance->total_hours,
                    'total_within_range' => $timeCalculation['total_within_range'],
                    'total_ot' => $timeCalculation['total_ot'],
                ]);
            } else {
                // Absent leader
                $leaderAttendance = (object) array_merge($leader, [
                    'status' => 'Absent',
                    'in_time' => null,
                    'out_time' => null,
                    'total_attendance_time' => null,
                    'total_within_range' => '00:00',
                    'total_ot' => '00:00',
                ]);
            }

            // Members Attendance
            $membersAttendance = $membersArray->map(function ($member) use ($date, $rangeStart, $rangeEnd) {
                $attendance = Attendance::where('employee_id', $member->id)
                    ->where('date_np', $date)
                    ->first();

                if ($attendance && $attendance->in_time && $attendance->out_time) {
                    $logIn = Carbon::parse($attendance->in_time);
                    $logOut = Carbon::parse($attendance->out_time);
                    $status = $attendance->status;

                    $timeCalculation = $this->calculateTimeWithinRange($logIn, $logOut, $rangeStart, $rangeEnd, $status);

                    $attendanceObj = (object)[
                        'status' => $status,
                        'in_time' => $logIn->format('h:i A'),
                        'out_time' => $logOut->format('h:i A'),
                        'total_attendance_time' => $attendance->total_hours,
                        'total_within_range' => $timeCalculation['total_within_range'],
                        'total_ot' => $timeCalculation['total_ot'],
                    ];
                } elseif ($attendance && (!$attendance->in_time || !$attendance->out_time)) {
                    $attendanceObj = (object)[
                        'status' => $attendance->status,
                        'in_time' => $attendance->in_time ? Carbon::parse($attendance->in_time)->format('h:i A') : '-',
                        'out_time' => $attendance->out_time ? Carbon::parse($attendance->out_time)->format('h:i A') : '-',
                        'total_attendance_time' => null,
                        'total_within_range' => '00:00',
                        'total_ot' => '00:00',
                    ];
                } else {
                    $attendanceObj = (object)[
                        'status' => 'Absent',
                        'in_time' => null,
                        'out_time' => null,
                        'total_attendance_time' => null,
                        'total_within_range' => '00:00',
                        'total_ot' => '00:00',
                    ];
                }

                return (object)[
                    'id' => $member->id,
                    'name' => $member->name,
                    'employee_code' => $member->employee_code,
                    'member_type' => $member->member_type,
                    'attendance' => $attendanceObj,
                ];
            });

            $result[$teamId] = [
                'team' => ['id' => $teamId, 'name' => $teamName],
                'leader' => $leaderAttendance,
                'members' => $membersAttendance,
            ];
        }

        return $result;
    }

    /**
     * Calculate time within range and overtime with comprehensive edge case handling
     */
    private function calculateTimeWithinRange(Carbon $logIn, Carbon $logOut, Carbon $rangeStart, Carbon $rangeEnd, string $status): array
    {
        // Case 1: No overlap between attendance and range
        if ($logOut <= $rangeStart || $logIn >= $rangeEnd) {
            return [
                'total_within_range' => '00:00',
                'total_ot' => '00:00',
            ];
        }

        // Case 2: Calculate actual time within range
        $actualStart = $logIn->greaterThan($rangeStart) ? $logIn : $rangeStart;
        $actualEnd = $logOut->lessThan($rangeEnd) ? $logOut : $rangeEnd;

        $totalWithinMinutes = $actualEnd->greaterThan($actualStart)
            ? $actualEnd->diffInMinutes($actualStart)
            : 0;

        // Case 3: Calculate overtime with proper boundary handling
        $mainWorkEnd = $logIn->copy()->addHours(8);
        $mainWorkEndMinutes = $logIn->diffInMinutes($mainWorkEnd);
        $overtimeMinutes = max(0, $totalWithinMinutes - $mainWorkEndMinutes);

        // Apply minimum OT threshold
        $minOTMinutes = (new OtRequestRepository)->minDifferenceForOt($status);
        $totalOTMinutes = max(0, $overtimeMinutes - $minOTMinutes);

        return [
            'total_within_range' => gmdate('H:i', $totalWithinMinutes * 60),
            'total_ot' => gmdate('H:i', $totalOTMinutes * 60),
        ];
    }

    public function getAssignableVerifiers(): array
    {
        $transitionList = TransitionPerformer::where([
            ['workflow', WorkflowName::TEAM_OT_TICKET],
            ['recipient_id', currentEmployee()?->id],
        ])->get();

        $verifier_ids = $transitionList
            ->filter(fn($transition) => $transition->state == WorkflowPerformer::VERIFIER)
            ->pluck('performer_id')
            ->toArray();

        return Employee::whereIn('id', $verifier_ids)
            ->get()
            ->mapWithKeys(function ($emp) {
                return [
                    $emp->id => [
                        'name' => $emp->name,
                        'employee_code' => $emp->employee_code,
                    ]
                ];
            })
            ->toArray();
    }

    public function submitOtTickets(array $teamAttendance, string $startTime, string $endTime, string $currentDate, int $verifierId, string $remarks): array
    {
        DB::beginTransaction();

        try {
            foreach ($teamAttendance as $teamId => $teamData) {
                $leader = $teamData['leader'];
                $validMembers = [];
                foreach ($teamData['members'] as $member) {
                    $inTime = $member->attendance->in_time ?? null;
                    $outTime = $member->attendance->out_time ?? null;

                    if (empty($inTime) || empty($outTime)) {
                        return [
                            'message' => "Incomplete attendance for {$member->name} in team ID: {$teamId}. IN-OUT time required.",
                            'type' => 'error',
                        ];
                    }

                    $validMembers[] = [
                        'employee_id' => $member->id,
                        'employee_code' => $member->employee_code,
                        'name' => $member->name,
                        'log_in' => $inTime,
                        'log_out' => $outTime,
                        'total_attendance_time' => $member->attendance->total_attendance_time,
                        'status' => $member->attendance->status,
                        'total_within_range' => $member->attendance->total_within_range,
                        'total_ot' => $member->attendance->total_ot,
                    ];
                }

                $totalWorking = Carbon::parse($startTime)->diff(Carbon::parse($endTime));
                $totalWorkingStr = sprintf('%02d:%02d', $totalWorking->h, $totalWorking->i);

                $teamOtRequest = TeamOtRequest::create([
                    'team_id' => $teamId,
                    'employee_id' => currentEmployee()?->id,
                    'nep_date' => $currentDate,
                    'start_time' => Carbon::parse($startTime)->format('H:i:s'),
                    'end_time' => Carbon::parse($endTime)->format('H:i:s'),
                    'leader_attendance' => (array) $leader,
                    'members_attendance' => $validMembers,
                    'total_working_hours' => $totalWorkingStr,
                    'total_ot_hours' => null,
                    'remarks' => $remarks,
                ]);

                $this->ticketRepo->createRequestTicket($teamOtRequest, [
                    'current_owner_id' => $verifierId,
                    'employee_id' => currentEmployee()?->id,
                    'documents' => [],
                ]);
            }
            DB::commit();

            return [
                'message' => 'OT Ticket(s) submitted successfully.',
                'type' => 'success',
                'ticketIds' => $teamOtRequest->id
            ];
        } catch (\Throwable $th) {
            DB::rollBack();

            Log::error("Failed to submit OT tickets: " . $th->getMessage(), [
                'trace' => $th->getTraceAsString(),
            ]);

            if ($th instanceof ValidationException) {
                throw $th;
            }

            throw ValidationException::withMessages([
                'submit_error' => 'An unexpected error occurred while submitting OT tickets.',
            ]);
        }
    }
}
