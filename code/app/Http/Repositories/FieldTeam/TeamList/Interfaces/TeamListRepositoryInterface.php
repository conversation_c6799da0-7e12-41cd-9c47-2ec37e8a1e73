<?php

namespace App\Http\Repositories\FieldTeam\TeamList\Interfaces;

use App\Models\Employee\Employee;
use App\Models\FieldTeam\TeamMembers;
use App\Models\Leaves\Attendance;
use Illuminate\Support\Collection;

interface TeamListRepositoryInterface
{
    public function teamList($search, $team_type, $transport_type, $branch_id, $operation_center, $date);
    public function toggleStatus($id);
    public function teamTypes();
    public function transportTypes();
    public function branches($company, $region);
    public function operationCenters($branchId);
    public function delete($id);

    /**
     * Finds the team leader for a given team ID.
     * @param int $teamId
     * @return TeamMembers|null
     */
    public function getTeamLeader(int $teamId): ?TeamMembers;

    /**
     * Gets permanent team members (excluding the leader) for a given team ID.
     * @param int $teamId
     * @return Collection
     */
    public function getPermanentMembers(int $teamId): Collection;

    /**
     * Gets temporary team members for a given team ID and date.
     * @param int $teamId
     * @param string $date
     * @return Collection
     */
    public function getTemporaryMembers(int $teamId, string $date): Collection;

    /**
     * Gets the team leader's attendance record for a given team ID and date.
     * @param int $teamId
     * @param string $date
     * @return Attendance|null
     */
    public function getTeamLeaderAttendance(int $teamId, string $date): Attendance|Employee|null;
    public function getAttendanceForMembers(Collection $members, string $date): Collection;

    /**
     * Gets a paginated list of team members for a given team ID and date.
     * @param int $teamId
     * @param string $date
     * @return Collection
     */
    public function getAllTeamMembers(int $teamId, string $date): Collection;
}
