<?php

namespace App\Http\Repositories\FieldTeam\TeamList;

use App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface;
use App\Http\Services\ScopeFetcher;
use App\Models\Employee\Employee;
use App\Models\FieldTeam\TeamList;
use App\Models\FieldTeam\TeamMembers;
use App\Models\FieldTeam\TemporaryTeamMembers;
use App\Models\Leaves\Attendance;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Collection;

class TeamListRepository implements TeamListRepositoryInterface
{
    public function teamList($search, $team_type, $transport_type, $branch_id, $operation_center, $date)
    {
        return TeamList::with(['teamMembers', 'teamType', 'transportType', 'branch', 'createdBy', 'otRequests'])
            ->when($search, function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->when($team_type, function ($query) use ($team_type) {
                $query->where('team_type', operator: $team_type);
            })
            ->when($transport_type, function ($query) use ($transport_type) {
                $query->where('transport_type', $transport_type);
            })
            ->when($branch_id, function ($query) use ($branch_id) {
                if (is_array($branch_id)) {
                    $query->whereIn('branch_id', $branch_id);
                } else {
                    $query->where('branch_id', $branch_id);
                }
            })
            ->when($operation_center, function ($query) use ($operation_center) {
                $query->where('operation_center', $operation_center);
            })
            ->when($date, function ($query) use ($date) {
                $query->where(function ($q) use ($date) {
                    $q->whereNull('end_date')
                        ->orWhere('end_date', '>=', $date);
                });
            });
    }

    public function toggleStatus($id)
    {
        $row = TeamList::findOrFail($id);

        if ($row->is_active) {
            $row->teamMembers()->update(['is_active' => false]);
            $row->is_active = !$row->is_active;
            $row->end_date = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
            $row->save();
            return true;
        } else {
            return false;
        }
    }

    public function teamTypes()
    {
        return \App\Models\FieldTeam\TeamType::all();
    }

    public function transportTypes()
    {
        return \App\Models\FieldTeam\TransportType::all();
    }

    public function branches($company, $region)
    {
        $region = $region == "" ? null : $region;
        return (new ScopeFetcher())->fetchBranch($company, $region);
    }

    public function operationCenters($branchId)
    {
        return \App\Models\configs\SubBranch::where('branch_id', $branchId)->get();
    }

    public function delete($id)
    {
        TeamList::findOrFail($id)->delete();
    }

    public function getTeamLeader(int $teamId): ?TeamMembers
    {
        return TeamMembers::where('team_id', $teamId)
            ->where('is_team_leader', true)
            ->with('employee')
            ->first();
    }

    public function getPermanentMembers(int $teamId): Collection
    {
        return TeamMembers::where('team_id', $teamId)
            ->where('is_team_leader', false)
            ->with('employee')
            ->get()
            ->pluck('employee');
    }

    public function getTemporaryMembers(int $teamId, string $date): Collection
    {
        $tempRecord = TemporaryTeamMembers::where('team_id', $teamId)
            ->where('date', $date)
            ->first();

        $memberIds = $tempRecord ? json_decode($tempRecord->member_details, true) : [];

        $members = Employee::whereIn('id', $memberIds)->get();

        return $members;
    }


    public function getTeamLeaderAttendance(int $teamId, string $date): Attendance|Employee|null
    {
        $teamLeader = $this->getTeamLeader($teamId);
        if (!$teamLeader) {
            return null;
        }

        $attendance = Attendance::with('employee')
            ->where('employee_id', $teamLeader->employee_id)
            ->whereDate('date_np', $date)
            ->first();

        if ($attendance) {
            return $attendance;
        }

        return Employee::find($teamLeader?->employee_id);
    }

    public function getAttendanceForMembers(Collection $members, string $date): Collection
    {
        return Attendance::whereIn('employee_id', $members->pluck('id'))
            ->whereDate('date_np', $date)
            ->get();
    }


    public function getAllTeamMembers(int $teamId, string $date): Collection
    {
        $permanent = $this->getPermanentMembers($teamId)->map(function ($m) {
            $m->member_type = 'permanent';
            return $m;
        });

        $temporary = $this->getTemporaryMembers($teamId, $date)->map(function ($m) {
            $m->member_type = 'temporary';
            return $m;
        });

        return $permanent->merge($temporary)->groupBy('member_type');
    }
}
