<?php

namespace App\Http\Repositories\FieldTeam\TransportationTypes;

use App\Http\Repositories\FieldTeam\TransportationTypes\Interfaces\TransportationTypeRepositoryInterface;

class TransportationTypesRepository implements TransportationTypeRepositoryInterface
{
    public function all($search)
    {
        return \App\Models\FieldTeam\TransportType::search($search);
    }

    public function create($data)
    {
        return \App\Models\FieldTeam\TransportType::create($data);
    }

    public function update($id, $data)
    {
        return \App\Models\FieldTeam\TransportType::findOrFail($id)->fill($data)->save();
    }

    public function find($id)
    {
        return \App\Models\FieldTeam\TransportType::findOrFail($id);
    }

    public function delete($id)
    {
        $transportExists = \App\Models\FieldTeam\TeamList::where('transport_type', $id)->exists();

        if ($transportExists) {
            throw new \Exception("Cannot delete transport type assigned for teams.");
        }

        \App\Models\FieldTeam\TransportType::findOrFail($id)->delete();
        return "Transportation type deleted successfully.";
    }
}
