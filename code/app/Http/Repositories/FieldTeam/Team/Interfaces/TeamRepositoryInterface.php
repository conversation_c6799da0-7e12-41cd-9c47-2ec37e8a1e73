<?php

namespace App\Http\Repositories\FieldTeam\Team\Interfaces;

use Illuminate\Support\Collection;

interface TeamRepositoryInterface
{
    public function teamTypes();
    public function transportTypes();
    public function branches($company, $region);
    public function operationCenters($branchId);
    public function create(array $data);
    public function update($id, array $data);
    public function updateTemporary($id, array $data);
    public function getEmployeeList(): Collection;
    public function getTransportTypeById(int $id);
    public function validateOtDataForExcel(array $data): array;
    public function createFieldTeamFromExcel(array $data): array;
}
