<?php

namespace App\Http\Repositories\FieldTeam\Team;

use App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface;
use App\Http\Services\ScopeFetcher;
use App\Models\Employee\Employee;
use App\Models\FieldTeam\TemporaryTeamMembers;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TeamRepository implements TeamRepositoryInterface
{
    public function teamTypes()
    {
        return \App\Models\FieldTeam\TeamType::where('is_active', true)->get();
    }

    public function transportTypes()
    {
        return \App\Models\FieldTeam\TransportType::where('is_active', true)->get();
    }

    public function branches($company, $region)
    {
        $region = $region == "" ? null : $region;
        return (new ScopeFetcher())->fetchBranch($company, $region);
    }

    public function operationCenters($branchId)
    {
        $a = \App\Models\configs\SubBranch::where('branch_id', (int)$branchId)->pluck('name', 'id')->toArray();
        return $a;
    }

    public function create(array $data)
    {
        return DB::transaction(function () use ($data) {
            // 1. Create the main team
            $team = \App\Models\FieldTeam\TeamList::create([
                'name' => $data['name'],
                'team_type' => $data['team_type'],
                'transport_type' => $data['transport_type'],
                'branch_id' => $data['branch_id'],
                'operation_center' => $data['operation_center'],
                'created_by' => $data['created_by'],
                'is_active' => $data['is_active'],
                'start_date' => $data['start_date'],
            ]);

            // 2. Add team leader
            if (!empty($data['team_leader'])) {
                $team->teamMembers()->create([
                    'employee_id' => $data['team_leader'],
                    'is_team_leader' => true,
                ]);
            }

            // 3. Add team members
            if (!empty($data['team_members']) && is_array($data['team_members'])) {
                foreach ($data['team_members'] as $memberId) {
                    // Skip if member is the leader
                    if ($memberId == $data['team_leader']) continue;

                    $team->teamMembers()->create([
                        'employee_id' => $memberId,
                        'is_team_leader' => false,
                    ]);
                }
            }

            return $team;
        });
    }

    public function update($id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $team = \App\Models\FieldTeam\TeamList::findOrFail($id);
            $formattedDate = Carbon::parse($data['date'])->format('Y-m-d');

            // 1. Get current members (including soft deleted)
            $currentMembers = \App\Models\FieldTeam\TeamMembers::withTrashed()
                ->where('team_id', $id)
                ->get()
                ->keyBy('employee_id');

            $oldMemberIds = $currentMembers->keys()->toArray();

            // 2. Update main team details
            $team->update([
                'team_type' => $data['team_type'],
                'transport_type' => $data['transport_type'],
                'branch_id' => $data['branch_id'],
                'operation_center' => $data['operation_center'],
            ]);

            // 3. Prepare new member list
            $newMemberIds = [];
            if (!empty($data['team_leader'])) {
                $newMemberIds[] = $data['team_leader'];
            }

            if (!empty($data['team_members']) && is_array($data['team_members'])) {
                foreach ($data['team_members'] as $memberId) {
                    if ($memberId == ($data['team_leader'] ?? null)) {
                        continue;
                    }
                    $newMemberIds[] = $memberId;
                }
            }

            // 4. Handle removed members
            $removedMemberIds = array_diff($oldMemberIds, $newMemberIds);

            foreach ($removedMemberIds as $memberId) {
                if ($currentMember = $currentMembers->get($memberId)) {
                    $alreadyLogged = \App\Models\FieldTeam\TeamAssignmentHistory::where('team_id', $id)
                        ->where('employee_id', $memberId)
                        ->where('removed_at', $formattedDate)
                        ->exists();
                    // Only log if member is currently active
                    if (!$alreadyLogged && is_null($currentMember->deleted_at)) {
                        \App\Models\FieldTeam\TeamAssignmentHistory::create([
                            'team_id' => $id,
                            'employee_id' => $memberId,
                            'removed_at' => $formattedDate,
                            'assigned_at' => $currentMember->assigned_date
                        ]);
                    }

                    // Soft delete the member
                    $currentMember->delete();
                }
            }

            // 5. Handle team leader with restoration if needed
            if (!empty($data['team_leader'])) {
                \App\Models\FieldTeam\TeamMembers::where('team_id', $id)
                    ->update(['is_team_leader' => false]);
                // First check if this member exists (even if soft deleted)
                $existingMember = \App\Models\FieldTeam\TeamMembers::withTrashed()
                    ->where('team_id', $id)
                    ->where('employee_id', $data['team_leader'])
                    ->first();

                if ($existingMember) {
                    // Restore if soft deleted
                    if ($existingMember->trashed()) {
                        $existingMember->restore();
                    }

                    // Update the member
                    $existingMember->update([
                        'is_team_leader' => true,
                    ]);
                } else {
                    // Create new member
                    $team->teamMembers()->create([
                        'employee_id' => $data['team_leader'],
                        'is_team_leader' => true,
                        'assigned_date' => $formattedDate
                    ]);
                }
            }

            // 6. Handle team members
            if (!empty($data['team_members']) && is_array($data['team_members'])) {
                foreach ($data['team_members'] as $memberId) {
                    if ($memberId == ($data['team_leader'] ?? null)) {
                        continue;
                    }

                    // Check if this member exists (even if soft deleted)
                    $existingMember = \App\Models\FieldTeam\TeamMembers::withTrashed()
                        ->where('team_id', $id)
                        ->where('employee_id', $memberId)
                        ->first();

                    if ($existingMember) {
                        // Restore if soft deleted
                        if ($existingMember->trashed()) {
                            $existingMember->restore();
                        }

                        // Update the member
                        $existingMember->update([
                            'is_team_leader' => false,
                        ]);
                    } else {
                        // Create new member
                        $team->teamMembers()->create([
                            'employee_id' => $memberId,
                            'is_team_leader' => false,
                            'assigned_date' => $formattedDate
                        ]);
                    }
                }
            }

            return $team;
        });
    }

    public function updateTemporary($id, array $data)
    {
        return DB::transaction(callback: function () use ($id, $data) {
            $team = \App\Models\FieldTeam\TeamList::findOrFail($id);
            $formattedDate = Carbon::parse($data['date'])->format('Y-m-d');

            // 1. Get the list of all employees currently in the main team
            $permanentMemberIds = \App\Models\FieldTeam\TeamMembers::where('team_id', $id)
                ->pluck('employee_id')
                ->toArray();

            // 2. Filter the submitted members to find only the new, temporary ones
            $temporaryMemberIds = collect(array_merge([$data['team_leader']], $data['team_members']))
                ->filter(function ($memberId) use ($permanentMemberIds) {
                    return !in_array($memberId, $permanentMemberIds);
                })
                ->unique()
                ->values()
                ->toArray();

            // 4. Update the temporary team table with the current snapshot of members
            TemporaryTeamMembers::updateOrCreate(
                [
                    'team_id' => $id,
                    'date' => $formattedDate,
                ],
                [
                    'member_details' => json_encode($temporaryMemberIds),
                ]
            );

            // 5. Update the main team details
            $team->update([
                'team_type' => $data['team_type'],
                'transport_type' => $data['transport_type'],
                'branch_id' => $data['branch_id'],
                'operation_center' => $data['operation_center'],
            ]);
        });
    }
    public function getEmployeeList(): Collection
    {
        return DB::table('employees as e')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'e.id')
            ->leftJoin('companies as c', 'c.id', '=', 'e.company_id')
            ->select([
                'e.id as value',
                DB::raw("
                CONCAT(
                    TRIM(CONCAT_WS(' ', e.first_name, e.middle_name, e.last_name)),
                    ' (',
                    c.code, '-', org.employee_code,
                    ')'
                ) as label
            "),
            ])
            ->whereNull('e.deleted_at')
            ->orderBy('e.first_name')
            ->get()
            ->map(fn($row) => [
                'value' => $row->value,
                'label' => $row->label,
            ]);;
    }


    public function getTransportTypeById(int $id)
    {
        return $this->transportTypes()->firstWhere('id', $id);
    }

    public function validateOtDataForExcel(array $data): array
    {
        $results = [];

        // 1. Collect all unique employee codes
        $leaderEmpCodes = collect($data)->pluck('team leader code')->map('trim')->unique();
        $memberEmpCodes = collect($data)->pluck('member codes')
            ->flatMap(fn($codes) => explode(',', $codes))
            ->map('trim')
            ->unique();

        $allEmpCodes = $leaderEmpCodes->merge($memberEmpCodes)->unique()->values();

        // 2. Fetch employee records in bulk
        $employees = $this->getEmployeeDetails($allEmpCodes->toArray());

        // 3. Preload all reference data
        $existingTeamNames = \App\Models\FieldTeam\TeamList::pluck('name')
            ->map(fn($name) => strtolower($name))
            ->flip()
            ->toArray();

        $teamTypes = \App\Models\FieldTeam\TeamType::pluck('id', 'name')
            ->mapWithKeys(fn($id, $name) => [strtolower($name) => $id])
            ->toArray();

        $transportTypes = \App\Models\FieldTeam\TransportType::pluck('total_number_of_seats', 'name')
            ->mapWithKeys(fn($seats, $name) => [strtolower($name) => $seats])
            ->toArray();

        $branches = \App\Models\configs\Branch::pluck('id', 'name')
            ->mapWithKeys(fn($id, $name) => [strtolower($name) => $id])
            ->toArray();

        // 4. Track already used employee codes
        $assignedEmployeeCodes = [];

        // 5. Start row-wise validation
        foreach ($data as $index => $row) {
            $status = true;
            $errors = [];

            $teamName = trim($row['team name'] ?? '');
            $teamType = strtolower(trim($row['team type'] ?? ''));
            $transportType = strtolower(trim($row['transport type'] ?? ''));
            $branch = strtolower(trim($row['branch'] ?? ''));
            $leaderCode = trim($row['team leader code'] ?? '');
            $memberCodes = collect(explode(',', $row['member codes'] ?? ''))
                ->map('trim')
                ->filter()
                ->values();

            // 5.1 Required fields
            foreach (['team leader code', 'member codes', 'branch'] as $field) {
                if (empty($row[$field])) {
                    $status = false;
                    $errors[] = "Missing required field: {$field}";
                }
            }

            // 5.2 Validate team name uniqueness
            if (isset($existingTeamNames[strtolower($teamName)])) {
                $status = false;
                $errors[] = "Team name already exists: {$teamName}";
            }

            // 5.3 Validate team type
            if (!isset($teamTypes[$teamType])) {
                $status = false;
                $errors[] = "Invalid team type: {$row['team type']}";
            }

            // 5.4 Validate transport type
            if (!isset($transportTypes[$transportType])) {
                $status = false;
                $errors[] = "Invalid transport type: {$row['transport type']}";
            }

            // 5.5 Validate branch
            if (!isset($branches[$branch])) {
                $status = false;
                $errors[] = "Invalid branch: {$row['branch']}";
            }

            // 5.6 Validate leader code
            if (!$employees->has($leaderCode)) {
                $status = false;
                $errors[] = "Invalid team leader code: {$leaderCode}";
            } elseif (isset($assignedEmployeeCodes[$leaderCode])) {
                $status = false;
                $errors[] = "Leader code {$leaderCode} already assigned to team '{$assignedEmployeeCodes[$leaderCode]}'";
            } else {
                $assignedEmployeeCodes[$leaderCode] = $teamName;
            }

            // 5.7 Validate member codes
            foreach ($memberCodes as $memberCode) {
                if (!$employees->has($memberCode)) {
                    $status = false;
                    $errors[] = "Invalid member code: {$memberCode}";
                    continue;
                }
                if (isset($assignedEmployeeCodes[$memberCode])) {
                    $status = false;
                    $errors[] = "Member code {$memberCode} already assigned to team '{$assignedEmployeeCodes[$memberCode]}'";
                } else {
                    $assignedEmployeeCodes[$memberCode] = $teamName;
                }
            }

            // 5.8 Validate transport seat count
            if (isset($transportTypes[$transportType])) {
                $maxSeats = $transportTypes[$transportType];
                $totalEmployees = 1 + $memberCodes->count(); // 1 for leader
                if ($totalEmployees > $maxSeats) {
                    $status = false;
                    $errors[] = "Transport type '{$row['transport type']}' allows maximum {$maxSeats} members (got {$totalEmployees})";
                }
            }

            // Finalize result
            $results[] = [
                'status' => $status,
                'message' => implode('; ', $errors),
            ];
        }

        return $results;
    }

    public function getEmployeeDetails(array $empCodes): Collection
    {
        $employees = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereIn(DB::raw("CONCAT(comp.code, '-', org.employee_code)"), $empCodes)
            ->select('employees.id', 'employees.company_id', 'employees.first_name', 'employees.middle_name', 'employees.last_name', 'org.employee_status_id', DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"))
            ->get()
            ->keyBy('emp_code');
        return $employees;
    }

    public function createFieldTeamFromExcel(array $data): array
    {
        DB::beginTransaction();
        try {
            // 1. Collect all leader and member codes
            $leaderEmpCodes = collect($data)->pluck('team leader code')->unique();
            $memberEmpCodes = collect($data)->pluck('member codes')->flatMap(
                fn($codes) =>
                collect(explode(',', $codes))->map(fn($code) => trim($code))
            )->unique();

            $allEmpCodes = $leaderEmpCodes->merge($memberEmpCodes)->unique();

            // 2. Fetch all employees at once
            $employees = $this->getEmployeeDetails($allEmpCodes->toArray());

            // 3. Fetch all branches, operation centers, team types, transport types for mapping
            $branches = \App\Models\configs\Branch::pluck('id', 'name')->mapWithKeys(fn($id, $name) => [strtolower($name) => $id]);
            $operationCenters = \App\Models\configs\SubBranch::pluck('id', 'name')->mapWithKeys(fn($id, $name) => [strtolower($name) => $id]);
            $teamTypes = \App\Models\FieldTeam\TeamType::pluck('id', 'name')->mapWithKeys(fn($id, $name) => [strtolower($name) => $id]);
            $transportTypes = \App\Models\FieldTeam\TransportType::pluck('id', 'name')->mapWithKeys(fn($id, $name) => [strtolower($name) => $id]);

            // 4. Loop through each row
            foreach ($data as $row) {
                $leaderCode = trim($row['team leader code']);
                $leader = $employees[$leaderCode] ?? null;
                if (!$leader) continue;

                $memberCodes = collect(explode(',', $row['member codes']))
                    ->map(fn($code) => trim($code))
                    ->filter();

                $memberIds = $memberCodes->map(fn($code) => $employees[$code]->id ?? null)->filter()->values();

                // Create team
                $team = \App\Models\FieldTeam\TeamList::create([
                    'name' => $row['team name'] ?? null,
                    'team_type' => $teamTypes[strtolower($row['team type'] ?? '')] ?? null,
                    'transport_type' => $transportTypes[strtolower($row['transport type'] ?? '')] ?? null,
                    'branch_id' => $branches[strtolower($row['branch'] ?? '')] ?? null,
                    'operation_center' => $operationCenters[strtolower($row['operation center'] ?? '')] ?? null,
                    'created_by' => currentEmployee()->id,
                    'is_active' => true,
                    'start_date' => Carbon::parse($row['start date'])->format('Y-m-d'),
                ]);

                // Add team leader
                $team->teamMembers()->create([
                    'employee_id' => $leader->id,
                    'is_team_leader' => true,
                ]);

                // Add team members (skip leader)
                foreach ($memberIds as $memberId) {
                    if ($memberId == $leader->id) continue;

                    $team->teamMembers()->create([
                        'employee_id' => $memberId,
                        'is_team_leader' => false,
                    ]);
                }

                // $team->employees()->attachWithPivot($memberIds, ['is_team_leader' => false]);
            }

            DB::commit();

            return [
                'status' => true,
                'message' => 'Field Team created successfully',
                'data' => []
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            logError('OT Upload Error', $e);

            return [
                'status' => false,
                'message' => 'Failed to create Field Team : ' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
