<?php

namespace App\Http\Repositories\FieldTeam\TeamTypes;

use App\Http\Repositories\FieldTeam\TeamTypes\Interfaces\TeamTypeRepositoryInterface;

class TeamTypeRepository implements TeamTypeRepositoryInterface
{
    public function all($search)
    {
        return \App\Models\FieldTeam\TeamType::search($search);
    }

    public function create($data)
    {
        return \App\Models\FieldTeam\TeamType::create($data);
    }

    public function update($id, $data)
    {
        return \App\Models\FieldTeam\TeamType::findOrFail($id)->fill($data)->save();
    }

    public function find($id)
    {
        return \App\Models\FieldTeam\TeamType::findOrFail($id);
    }

    public function delete($id)
    {
        return \App\Models\FieldTeam\TeamType::findOrFail($id)->delete();
    }
}
