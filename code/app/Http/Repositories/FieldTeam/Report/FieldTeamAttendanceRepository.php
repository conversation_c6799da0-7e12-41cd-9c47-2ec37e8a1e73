<?php

namespace App\Http\Repositories\FieldTeam\Report;

use App\Http\Repositories\FieldTeam\Report\Interfaces\FieldTeamAttendanceRepositoryInterface;
use App\Models\FieldTeam\TeamOtRequest;
use App\Models\FieldTeam\TeamType;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;

class FieldTeamAttendanceRepository implements FieldTeamAttendanceRepositoryInterface
{
    public function getDetailedAttendance(array $filters): Collection
    {
        $query = TeamOtRequest::whereBetween('nep_date', [$filters['start_date'], $filters['end_date']])
            ->where('state', 'Approved');

        if (!empty($filters['team_id'])) {
            $query->where('team_id', $filters['team_id']);
        }

        $records = $query->get()->flatMap(function ($item) {
            $data = collect();

            // Leader
            $leader = $item->leader_attendance;
            if ($leader) {
                $data->push([
                    'employee_id' => $leader['id'],
                    'employee_name' => $leader['name'],
                    'employee_code' => $leader['employee_code'],
                    'in_time' => $leader['in_time'],
                    'out_time' => $leader['out_time'],
                    'status' => $leader['status'],
                    'date_np' => $item->nep_date,
                    'total_within_range' => $leader['total_within_range'],
                    'total_hours' => $leader['total_attendance_time'],
                    'member_type' => 'Team Leader',
                ]);
            }

            // Members
            $members = $item->members_attendance;
            foreach ($members ?? [] as $member) {
                $data->push([
                    'employee_id' => $member['employee_id'],
                    'employee_name' => $member['name'],
                    'employee_code' => $member['employee_code'],
                    'in_time' => $member['log_in'],
                    'out_time' => $member['log_out'],
                    'status' => $member['status'],
                    'date_np' => $item->nep_date,
                    'total_within_range' => $member['total_within_range'],
                    'total_hours' => $member['total_attendance_time'],
                    'member_type' => 'Team Member',
                ]);
            }

            return $data;
        });

        // Apply employee filtering
        if (!empty($filters['employee_id'])) {
            $records = $records->filter(function ($record) use ($filters) {
                return $record['employee_id'] == $filters['employee_id'];
            });
        }

        return $records->values();
    }

    public function getSummaryAttendance(array $filters): Collection
    {
        $query = TeamOtRequest::whereBetween('nep_date', [$filters['start_date'], $filters['end_date']])
            ->where('state', 'Approved');

        if (!empty($filters['team_id'])) {
            $query->where('team_id', $filters['team_id']);
        }

        $flattened = $query->get()->flatMap(function ($item) {
            $rows = collect();

            $leader = $item->leader_attendance;
            if ($leader) {
                $rows->push([
                    'employee_name' => $leader['name'],
                    'employee_code' => $leader['employee_code'],
                    'total_within_range' => $leader['total_within_range'],
                    'total_hours' => $leader['total_attendance_time'] ?? '00:00:00',
                    'member_type' => 'Team Leader',
                ]);
            }

            $members = $item->members_attendance;
            foreach ($members ?? [] as $member) {
                $rows->push([
                    'employee_name' => $member['name'],
                    'employee_code' => $member['employee_code'],
                    'total_within_range' => $member['total_within_range'],
                    'total_hours' => $member['total_attendance_time'] ?? '00:00:00',
                    'member_type' => 'Team Member',
                ]);
            }

            return $rows;
        });

        return $flattened
            ->groupBy('employee_code')
            ->map(function ($rows, $employeeCode) {
                $firstRow = $rows->first();
                $totalWithinRange = $rows->sum(function ($row) {
                    return $this->timeToSeconds($row['total_within_range']);
                });
                $totalHours = $rows->sum(function ($row) {
                    return $this->timeToSeconds($row['total_hours']);
                });
                $daysCount = $rows->count();

                return [
                    'employee_name' => $firstRow['employee_name'],
                    'employee_code' => $employeeCode,
                    'member_type' => $firstRow['member_type'],
                    'total_within_range' => $this->secondsToTime($totalWithinRange),
                    'total_hours' => $this->secondsToTime($totalHours),
                    'days_count' => $daysCount,
                ];
            })
            ->values();
    }

    public function getPaginatedDetailedAttendance(array $filters, int $perPage = 25): LengthAwarePaginator
    {
        $data = $this->getDetailedAttendance($filters);

        $page = Paginator::resolveCurrentPage() ?: 1;
        $items = $data->slice(($page - 1) * $perPage, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $data->count(),
            $perPage,
            $page,
            ['path' => Paginator::resolveCurrentPath()]
        );
    }

    public function getPaginatedSummaryAttendance(array $filters, int $perPage = 25): LengthAwarePaginator
    {
        $data = $this->getSummaryAttendance($filters);

        $page = Paginator::resolveCurrentPage() ?: 1;
        $items = $data->slice(($page - 1) * $perPage, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $data->count(),
            $perPage,
            $page,
            ['path' => Paginator::resolveCurrentPath()]
        );
    }

    public function getExportData(array $filters): array
    {
        $exportType = $filters['export_type'] ?? 'all';
        $isDetailed = $filters['is_detailed'] ?? false;

        if ($exportType === 'paginated') {
            return $this->getPaginatedExportData($filters, $isDetailed);
        } else {
            return $this->getAllExportData($filters, $isDetailed);
        }
    }

    protected function getPaginatedExportData(array $filters, bool $isDetailed): array
    {
        $perPage = $filters['per_page'] ?? 25;
        $currentPage = $filters['current_page'] ?? 1;

        if ($isDetailed) {
            $paginator = $this->getPaginatedDetailedAttendance($filters, $perPage);
            $data = collect($paginator->items());
            return $this->formatDetailedData($data);
        } else {
            $paginator = $this->getPaginatedSummaryAttendance($filters, $perPage);
            $data = collect($paginator->items());
            return $this->formatSummaryData($data);
        }
    }

    protected function getAllExportData(array $filters, bool $isDetailed): array
    {
        if ($isDetailed) {
            $data = $this->getDetailedAttendance($filters);
            return $this->formatDetailedData($data);
        } else {
            $data = $this->getSummaryAttendance($filters);
            return $this->formatSummaryData($data);
        }
    }

    protected function formatDetailedData(Collection $data): array
    {
        $heading = [
            'Employee Name',
            'Employee Code',
            'Date',
            'In Time',
            'Out Time',
            'Status',
            'Total Hours',
            'Total Within Range',
            'Member Type',
        ];

        $formattedData = [];

        foreach ($data as $record) {
            $formattedData[] = [
                $record['employee_name'] ?? '',
                $record['employee_code'] ?? '',
                $record['date_np'] ?? '',
                $record['in_time'] ?? '',
                $record['out_time'] ?? '',
                $record['status'] ?? '',
                $record['total_hours'] ?? '00:00:00',
                $record['total_within_range'] ?? '00:00:00',
                $record['member_type'] ?? '',
            ];
        }

        return [$heading, $formattedData];
    }

    protected function formatSummaryData(Collection $data): array
    {
        $heading = [
            'Employee Name',
            'Employee Code',
            'Member Type',
            'Total Hours',
            'Total Within Range',
            'Number of Days',
        ];

        $formattedData = [];

        foreach ($data as $record) {
            $formattedData[] = [
                $record['employee_name'] ?? '',
                $record['employee_code'] ?? '',
                $record['member_type'] ?? '',
                $record['total_hours'] ?? '00:00:00',
                $record['total_within_range'] ?? '00:00:00',
                $record['days_count'] ?? 0,
            ];
        }

        return [$heading, $formattedData];
    }

    public function getTeamTypes(): Collection
    {
        return TeamType::all();
    }

    protected function timeToSeconds($time): int
    {
        if (empty($time)) return 0;

        [$h, $m, $s] = array_pad(explode(':', $time), 3, 0);
        return ($h * 3600) + ($m * 60) + $s;
    }

    protected function secondsToTime($seconds): string
    {
        $h = floor($seconds / 3600);
        $m = floor(($seconds % 3600) / 60);
        $s = $seconds % 60;
        return sprintf('%02d:%02d:%02d', $h, $m, $s);
    }
}
