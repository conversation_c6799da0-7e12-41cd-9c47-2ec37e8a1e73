<?php

namespace App\Http\Repositories\FieldTeam\Report\Interfaces;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface FieldTeamAttendanceRepositoryInterface
{
    /**
     * Get detailed field team attendance data
     */
    public function getDetailedAttendance(array $filters): Collection;

    /**
     * Get summary field team attendance data
     */
    public function getSummaryAttendance(array $filters): Collection;

    /**
     * Get paginated detailed attendance data
     */
    public function getPaginatedDetailedAttendance(array $filters, int $perPage = 25): LengthAwarePaginator;

    /**
     * Get paginated summary attendance data
     */
    public function getPaginatedSummaryAttendance(array $filters, int $perPage = 25): LengthAwarePaginator;

    /**
     * Get data for export based on type (paginated or all)
     */
    public function getExportData(array $filters): array;

    /**
     * Get field team types
     */
    public function getTeamTypes(): Collection;
}
