<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Constant;
use App\Http\Repositories\Repository;
use App\Models\Employee\Employee;
use App\Models\SmsLog;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class IopsRepository extends Repository
{
    private Client $guzzleClient;

    public function __construct()
    {
        if (strtolower(config('app.environment') ?? "") == 'production') {
            $baseUrl = "https://empapp.vianet.com.np";
        } else {
            $baseUrl = "https://empdev.vianet.com.np";
        }
        $this->guzzleClient = new \GuzzleHttp\Client([
            'http_errors' => false,
            'verify' => false,
            'base_uri' => $baseUrl
        ]);
    }
    public function changePassword($oldPassword, $newPassword, $employee_iops_id = null)
    {
        // dd(session(Constant::SESSION_IOPS_ID));
        try {
            $response = $this->authenticateEmpApp();
            if (!(isset($response['status'])) || !($response['status'] ?? null)) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];
            $iopsId = $employee_iops_id ? $employee_iops_id : session(Constant::SESSION_IOPS_ID);

            if (!$iopsId) {
                return $this->errorResponse("IOPS Id doesn't exists");
            }

            // Step 2: Perform IOPS login
            try {
                $apiResponse = $this->guzzleClient->post('/api/iops/change_password', [
                    'headers' => ['Authorization' => 'Bearer ' . $token],
                    'form_params' => compact('iopsId', 'oldPassword', 'newPassword'),
                ]);
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                logError("Error changing iops password", $e);
                if ($e->getResponse()->getStatusCode() == 403) {
                    $errorBody = $e->getResponse()->getBody()->getContents();
                    $errorJson = json_decode($errorBody, true);
                    if (isset($errorJson['message']))
                        return $this->errorResponse($errorJson['message'][0]);
                }
                return $this->errorResponse("Error changing iops password");
            }

            $responseData = json_decode($apiResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                return $this->errorResponse($responseData['message'] ?? "Error from iops while changing password.");
            }

            return $this->successResponse("Password changed successfully");
        } catch (\Exception $e) {
            \logError("Error while changing password", $e);
            return $this->errorResponse("Error while changing password");
        }
    }

    public function authenticateEmpApp()
    {
        $apiUsername = config('app.empAppUsername');
        $apiPassword = config('app.empAppPassword');
        try {
            $tokenResponse = $this->guzzleClient->post("/api/authentication/user", [
                'auth' => [$apiUsername, $apiPassword],
            ]);
            $tokenData = json_decode($tokenResponse->getBody(), true);
            if ($tokenData['status'] !== "1") {
                return false;
            }

            $token = $tokenData['response']['token'];

            return $this->successResponse("Token generated", ['token' => $token]);
        } catch (Exception $e) {
            \logError("Error while empApp authentication", $e);
            return $this->errorResponse("Error while empApp authentication");
        }
    }

    public function iopsInfo()
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];

            $apiResponse = $this->guzzleClient->get("/api/iops/getInfo", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
            ]);

            $apiData = json_decode($apiResponse->getBody(), true);

            if (($apiData['status'] ?? '0') !== "1") {
                return false;
            }

            $apiResponse = $apiData['response'];

            return $this->successResponse("IOPS info fetched", ['data' => $apiResponse]);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \logError("Error while fetching iops information", $e);
            return $this->errorResponse("Error while fetching iops information");
        }
    }

    public function createEmployee($params)
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/employee_create", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $params
            ]);

            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                return false;
            }

            $response = $responseData['response'];

            return $this->successResponse("Iops employee created successfully", ['response' => $response]);
        } catch (\Exception $e) {
            \logError("Error while creating iops employee", $e);
            return $this->errorResponse("Error while creating iops employee");
        }
    }

    /**
     * Sens SMS to phone number of selected employee with provided content
     * *ONLY FOR VIANET
     */
    public function sendSMS($employeeId, string $content, $type = "")
    {
        if (!vianetHrm() && !konnectHrm())
            return $this->errorResponse("THIS SERVICE IS ONLY FOR VIANET.");

        $employee = Employee::find($employeeId);
        if (!$employee) return $this->errorResponse("No such employee found");
        $phone = $employee->phone;

        $smsLog = SmsLog::create([
            'type'          => $type,
            'employee_id'   => $employee?->id,
            'phone'         => $phone,
            'status'        => "pending",
            'description'   => $content,
        ]);

        $client = new \GuzzleHttp\Client([
            // 'verify' => false,
        ]);

        DB::beginTransaction();
        try {
            $formParams = [
                'phone' => $phone,
                'service_type' => 'default',
                'sms_message' => $content,
            ];

            if (vianetHrm()) {
                $formParams['activity_type'] = "Vianet OTP";
            } else if (konnectHrm()) {
                $formParams['activity_type'] = "ViaSoft Konnect-HRIS";
            }

            $response = $client->post(
                'https://mqueue.vianet.com.np/api/user/sendSms',
                [
                    'headers' => [
                        'Authorization' => 'Bearer RnQzUklpaUR1ZWkwZUp2NWwzd2JDdUVJNFpWZFp0akpOd0tNMnBWTjNCVDhCbFBTbnlzZDR5b3Y1akxxSVJCN01vTElQaGlBSDlPOWV0cWdiSnphU2RSbWFLWFA5Z0lSSlRoWUlvTGZQZ1lDTnlOd3dqUUpma3VlRnV0c3RjbTkwZmVWdTVhOTFKOEhOTXp5SWptMmFWMENnWVR6R2tUcUFCTEpBaE5PZGNNZGhjVzJDZFRIYWFCZEd1ZmYzdDhoRlhFTEJxbkU2aWNFQ1Z4NFZUbXRobkczWlZ5cFZCaU5uM0FoSkozV2pIZHJLT1ZQUXpwWlVtNzkzNmduTElV619e1988ec102'
                    ],
                    'form_params' => $formParams
                ]
            );

            $responseData = json_decode($response->getBody(), true);
            if ($responseData['status'] !== "1") {
                $msg = "";
                if (is_array($responseData['message'])) {
                    foreach ($responseData['message'] as $message) {
                        $msg .= $message . "\r\n";
                    }
                }
                throw new \Exception($msg);
            }
            $smsLog->status = 'delivered';
            $smsLog->sent_on = now();
            return $this->successResponse("SMS sent successfully");
        } catch (\Exception $e) {
            \logError("Error while sending SMS", $e);
            $smsLog->status = 'failed';
            return $this->errorResponse("Error while sending SMS " . $e->getMessage());
        } finally {
            $smsLog->save();
            DB::commit();
        }
    }

    /**
     * Fetch employee details
     * @param array $params can be:
     * 'employeeId'      => 'required|Integer',
     */
    public function fetchEmployeeDetails($employeeId)
    {
        try {
            $employee = Employee::find($employeeId);
            if (!$employee) return $this->errorResponse("No such employee found");
            $iopsId = $employee->organizationInfo->iops_id;

            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $type = "others";
            if (vianetHrm()) {
                $type = "vianet";
            } elseif (konnectHrm()) {
                $type = "konnect";
            }
            if ($type == "others") {
                \logError("Employee details could not be fetched.");
                return $this->errorResponse("Employee details could not be fetched.");
            }
            $params = [
                "iopsId" => $iopsId,
                "type"   => $type,
            ];
            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/get-employee-details", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $params
            ]);
            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] === "1") {
                return $this->successResponse(data: $responseData['response'] ?? []);
            }
            logError($responseData['message'][0] ?? "Error fetching employee.");
            return $this->errorResponse($responseData['message'][0] ?? "Error fetching employee.");
        } catch (\Exception $e) {
            \logError("Error fetching employee.", $e);
            return $this->errorResponse("Error fetching employee.");
        }
    }

    public function terminateEmployee($employeeId, $terminationDate = "")
    {
        $employee = Employee::find($employeeId);
        if (!$employee) return $this->errorResponse("No such employee found");
        $iopsId = $employee->organizationInfo->iops_id;
        logCronInfo("Terminating employee from iops", ['employee_id' => $employeeId, 'iops_id' => $iopsId, 'termination_date' => $terminationDate]);
        $activeStatus = 'N';
        return $this->changeIopsUserStatus($iopsId, $activeStatus, $terminationDate);
    }

    public function unTerminateEmployee($employeeId)
    {
        $employee = Employee::withTrashed()->where('id', $employeeId)->first();
        if (!$employee) return $this->errorResponse("No such employee found");
        $iopsId = $employee->organizationInfo->iops_id;
        Log::info("unTerminating employee from iops", ['employee_id' => $employeeId, 'iops_id' => $iopsId]);
        $activeStatus = 'Y';
        return $this->changeIopsUserStatus($iopsId, $activeStatus);
    }

    /**
     * change iops user status
     * @param string $iopsId iops id of the employee
     * @param string $activeStatus 'Y' or 'N' for employee status; 'Y' if terminating, 'N' for non terminating
     */
    private function changeIopsUserStatus($iopsId, $activeStatus, $terminationDate = "")
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $formParams = [
                'iopsId' => $iopsId,
                'activeStatus' => $activeStatus,
            ];

            if ($terminationDate) {
                $formParams['dor'] = $terminationDate;
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/userStatus/change", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $formParams,
            ]);

            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                return false;
            }

            // $response = $responseData['response'];

            return $this->successResponse("Iops employee status changed successfully");
        } catch (\Exception $e) {
            \logError("Error while changing iops status", $e);
            return $this->errorResponse("Error while changing iops status");
        }
    }

    public function checkIopsHasUsername($username)
    {
        $response = $this->authenticateEmpApp();
        if (!$response['status']) {
            return $this->errorResponse($response['message']);
        }

        $token = $response['data']['token'];

        $response = $this->guzzleClient->post("/api/iops/check_iops_user", [
            'headers' => ['Authorization' => 'Bearer ' . $token],
            'form_params' => [
                'username' => $username,
            ]
        ]);

        $responseData = json_decode($response->getBody(), true);

        if ($responseData['status'] !== "1") {
            return false;
        }
        return true;
    }

    public function getAttendanceFromHRIS($timestamp = null)
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/hrm/getAttlogsData", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => [
                    'date' => $timestamp,
                ],
            ]);
            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                return $this->errorResponse("Unable to fetch attendance from HRIS");
            }
            return $this->successResponse("Employee attendance fetched successfully", $responseData);
        } catch (\Exception $e) {
            \logError("Error while fetching attendance from HRIS", $e);
            return $this->errorResponse("Error while fetching attendance from HRIS");
        }
    }

    /**
     * Update employee details in IOPS
     * @param array $params can be: 
     * 'iopsId'          => 'required|Integer',
     * 'roleId'            => 'exists:mysql4.org_roles,id',
     * 'designation'       => 'String',
     * 'dutyHoursId'       => 'exists:mysql4.employee_duty_hours,id',
     * 'hrAgencyId'        => 'exists:mysql4.hr_agency,id',
     * 'department'        => 'String',
     * 'taskDepartmentId'  => 'exists:mysql4.departments,id',
     * 'isManager'         => 'in:Y,N',
     * 'accessProfile'     => 'String',
     * 'contactHome'       => 'integer',
     * 'firstName'         => 'String',
     * 'middleName'        => 'String',
     * 'lastName'          => 'String',
     * 'address'           => 'String',
     * 'contactMobile'     => 'Integer',
     * 'branchId'          => 'exists:mysql4.branches,id',
     * 'operationCenter'   => 'exists:mysql4.collection_centers,id',
     * 'employeeType'      => 'in:Internal,External',
     * 'email'             => 'email',
     * 'hrisDesignation'   => 'String',
     * 'hrisJobDescription'=> 'String',
     * @param array $authResponse
     * @return array
     */
    public function updateEmployeeDetails($params, $authResponse = null)
    {
        try {
            $response = $authResponse;
            if (!$authResponse) {
                $response = $this->authenticateEmpApp();
                if (!$response['status']) {
                    return $this->errorResponse($response['message']);
                }
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/employee_update", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $params
            ]);

            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] != "1") {
                return $this->errorResponse($responseData['message'][0] ?? "Error from iops while updating employee details.");
            }
            return $this->successResponse($responseData['message'][0] ?? "Iops employee details updated successfully");
        } catch (\Exception $e) {
            \logError("Error while updating iops employee details", $e);
            return $this->errorResponse("Error while updating iops employee details");
        }
    }

    /**
     * Get staff tag from IOPS
     * @param array $params can be:
     * 'iopsId'          => 'required|Integer',
     */
    public function getStaffTag($params)
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/get-staff-tag", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $params
            ]);

            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                logError($responseData['message'][0] ?? "Error while getting staff tag.");
                return $this->errorResponse($responseData['message'][0] ?? "Error while getting staff tag.");
            }

            return $this->successResponse(data: $responseData['response'] ?? []);
        } catch (\Exception $e) {
            \logError("Error while getting staff tag.", $e);
            return $this->errorResponse("Error while getting staff tag.");
        }
    }

    /**
     * Set staff tag in IOPS
     * @param array $params can be:
     * 'iopsId'          => 'required|Integer',
     * 'customerId'      => 'String',
     */
    public function setStaffTagging($params)
    {
        try {
            $response = $this->authenticateEmpApp();
            if (!$response['status']) {
                return $this->errorResponse($response['message']);
            }

            $token = $response['data']['token'];
            $responseResponse = $this->guzzleClient->post("/api/iops/staffTagging", [
                'headers' => ['Authorization' => 'Bearer ' . $token],
                'form_params' => $params
            ]);

            $responseData = json_decode($responseResponse->getBody(), true);

            if ($responseData['status'] !== "1") {
                logError($responseData['message'][0] ?? "Error while tagging staff.");
                return $this->errorResponse($responseData['message'][0] ?? "Error while tagging staff.");
            }

            return $this->successResponse(data: $responseData['response'] ?? []);
        } catch (\Exception $e) {
            \logError("Error while tagging staff.", $e);
            return $this->errorResponse("Error while tagging staff.");
        }
    }
}
