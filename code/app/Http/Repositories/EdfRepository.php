<?php

namespace App\Http\Repositories;

use App\Models\Employee\EmployeeDocument;
use App\Models\EmployeeFamilyDocument;
use App\Models\EmployeeFamilyInformation;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface;
use App\Http\Repositories\Repository;
use App\Jobs\SendSMSJob;
use App\Models\Employee\Employee;
use App\Models\Payroll\GradeStructure;
use App\Models\Tickets\EdfRequest;
use App\Models\Tickets\TicketDocument;
use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;


class EdfRepository extends Repository
{
    use WithNotify;
    public static $optionalFieldList = ["blood_group", "fedex_id", "private_vehicle_identity", "office_vehicle_identity"];

    public function createEdfTicket($data)
    {
        try {
            logInfo("Creating Edf request", $data);
            $data['employee_id'] = currentEmployee()?->id;

            $jobSeatRepo = app(\App\Http\Repositories\Configs\JobSeatRepository::class);
            $jobId = $data['payslip_meta']['data']['job_id'];

            try {
                $jobSeatRepo->checkCapacity([
                    'company_id'    => $data['company_id'],
                    'branch_id'     => $data['branch_id'],
                    'department_id' => $data['department_id'],
                    'job_id'        => $jobId
                ]);
            } catch (Exception $e) {
                return $this->errorResponse($e->getMessage());
            }

            $edfRequest = EdfRequest::create($data);

            $familyDetail = $data['family_data_meta'];
            unset($data['family_data_meta']);
           
            foreach ($familyDetail as $idx => $detail) {

                $relation = $detail['relation_type']; 

                foreach ($detail['documents'] as $docIndex => $doc) {

                    $file = $doc['file'];
                    
                    if (!$file instanceof \Illuminate\Http\UploadedFile) {
                        continue;
                    }
                    
                    $fileName = "{$edfRequest->id}-{$relation}-{$doc['type']}-" . time() . "." . $file->getClientOriginalExtension();

                    $path = $file->storeAs(
                        "uploads/edf/$relation", 
                        $fileName,
                        "public"                   
                    );

                    $familyDetail[$idx]['documents'][$docIndex]['file'] = $path;
                }
            }

            $edfRequest->update(['family_data_meta' => $familyDetail]);

            $edfRequest->applyWorkflow(WorkflowName::EDF_REQUEST);
            logInfo("Edf request created id:" . $edfRequest->id);
            $ticketRepo = new TicketRepository;
            $ticketResponse = $ticketRepo->createRequestTicket($edfRequest, [
                'documents' => $data['documents'],
                'employee_id' => currentEmployee()?->id,
                'current_owner_id' => $data['verifier_id']
            ]);
            $ticket = $ticketResponse['data'];
            if (!$ticketResponse['status'])
                return $this->errorResponse($ticketResponse['message']);
            logInfo("Ticket for edf request created");
            return $this->successResponse("Edf request created successfully", ['ticket_id' => $ticket?->id, 'request_id' => $edfRequest->id]);
        } catch (\Exception $e) {
            logError("Error creating Edf request: ", $e);
            return $this->errorResponse("Failed to create Edf request");
        }
    }

    public function setEmployeeFamilyDetails(array $familyRelations, array $documentMappings, $employeeId, $mstat)
    {
        foreach ($familyRelations as $relation) {
            $record = EmployeeFamilyInformation::create([
                "employee_id" => $employeeId,
                "relation_type" => $relation['relation_type'],
                "name" => $relation['name'],
                "dob_eng" => $relation['dob'],
                "remarks" => null,
            ]);

            $familyInfo[$relation['relation_type']] = $record->id;
        }

        Log::info("Family information saved", $familyInfo);
        foreach ($documentMappings as $field => $map) {

            if (!$this->$field)
                continue;

            if ($map['relation'] === 'spouse' && $mstat !== 'married')
                continue;

            $familyId = $familyInfo[$map['relation']];

            $filePath = $this->$field->storeAs(
                "uploads/employee/family_docs/{$employeeId}",
                $field . '-' . time() . '.' . $this->$field->extension(),
                "public"
            );

            EmployeeFamilyDocument::create([
                "family_id" => $familyId,
                "document_path" => $filePath,
                "type" => $map['type'],
                "remarks" => null,
                "status" => "Submitted",
                "actioned_by" => auth()->id(),
            ]);

            Log::info("Family document saved: $field ($filePath)");
            return;
        }
    }

    public function setEdfRequestDetail($request, $type)
    {
        switch ($type) {
            case "all":
                $request->payslipInformation = $request->payslip_meta['detail']['payslipInformation'] ?? [];
                $request->salaryStructure = $request->payslip_meta['detail']['salaryStructure'] ?? [];
                $request->employee_code = $request->company->code . '-' . $request->employee_code;
                if ($request->payslip_request_id)
                    $request->payslipLink = route('ticketPage', ['workflow' => WorkflowName::PAYSLIP_APPROVAL, 'requestId' => $request->payslip_request_id]);
                $payslipApproverId = $request->payslip_meta['data']['verifier_id'] ?? "";
                $request->payslipApprover = $payslipApproverId ? Employee::find($payslipApproverId)?->name : null;
                $request->canEdit = !in_array($request->state, [WorkflowState::APPROVED, WorkflowState::REJECTED]) && (
                    $request->employee_id == currentEmployee()?->id ||
                    $request->requestTicket->current_owner_id == currentEmployee()?->id
                );
        }
    }

    public function approveEdfRequest(EdfRequest $request)
    {
        logInfo("Creating employee for edf request: " . $request->id);

        $password = $request->username . '@' . ($request->phone ?? '123');
        if (vianetHrm()) {
            $password = Str::random(10);
        }

        $user = \App\Models\User::create([
            'username' => $request->username,
            'password' => Hash::make($password),
        ]);

        logInfo("User created: ", $user->toArray());
        $user->assignRole(Constant::ROLE_GENERAL);
        logInfo("General role assigned to the created user");

        $employee = \App\Models\Employee\Employee::create([
            "first_name"        => $request->first_name,
            "middle_name"      => $request->middle_name,
            "last_name"        => $request->last_name,
            "gender"            => $request->gender,
            "dob"               => $request->dob,
            "dob_nepali"        => $request->dob_nepali,
            "mstat"             => $request->mstat,
            "phone"             => $request->phone,
            "email"             => $request->email,
            "nationality"       => $request->nationality,
            "citizenship"       => $request->citizenship,
            "permanent_address" => $request->permanent_address,
            "temporary_address" => $request->temporary_address,
            "father"            => $request->father,
            "mother"            => $request->mother,
            "grandfather"       => $request->grandfather,
            "spouse"            => $request->spouse,
            "contact_person"    => $request->contact_person,
            "contact_phone"     => $request->contact_phone,
            "contact_relation"  => $request->contact_relation,
            "company_id"        => $request->company_id,
            "user_id"           => $user->id,
        ]);

        $request->added_employee_id = $employee->id;
        $request->save();

        logInfo("Employee created: ", $employee->toArray());
        $branch = \App\Models\configs\Branch::find($request->branch_id);
        if (!$branch) {
            return $this->errorResponse("Branch not found");
        }
        $region = \App\Models\configs\Region::find($branch->region_id);


        $org = \App\Models\Employee\EmployeeOrg::create([
            "employee_id"           => $employee->id,
            "employee_code"         => $request->employee_code,
            "email"                 => $request->company_email,
            "region_id"             => $region?->id,
            "branch_id"             => $request->branch_id,
            "sub_branch_id"         => $request->sub_branch_id,
            "department_id"         => $request->department_id,
            "unit_id"               => $request->unit_id,
            "shift_id"              => $request->shift_id,
            "outsource_company_id"  => $request->outsource_company_id,
            "employee_status_id"    => $request->employee_status_id,
            "doj"                   => $request->doj,
            "supervisor_id"         => $request->supervisor_id,
            "bank"                  => $request->bank,
            "bank_account_no"       => $request->bank_account_no,
            "cug"                   => $request->cug,
            "rf_no"                 => $request->rf_no,
            "cit_no"                => $request->cit_no,
            "biometric_id"          => $request->biometric_id,
            "pan_no"                => $request->pan_no,
            "designation_id"        => $request->designation_id,
            "employee_category_id" =>  $request->employee_category_id,

        ]);

        logInfo("Employee organization created: ", $org->toArray());

        if ($request->optional_field_meta) {
            foreach ($request->optional_field_meta as $key => $value) {
                \App\Models\OptionalFields\OptionalField::create([
                    "model_type" => get_class($employee),
                    "model_id" => $employee->id,
                    "field_key" => $key,
                    "field_value" => $value
                ]);
            }
            logInfo("Optional field filled for the employee");
        }

        logInfo("Updating requisition fulfilled value");
        $requisitionData = \App\Models\Tickets\ManpowerRequisition::find($request?->manpower_requisition_id);
        $requisitionData->number_fulfilled += 1;
        $requisitionData->save();
        logInfo("Updating requisition fulfilled value completed");

        logInfo("Updating replaced employee id value");
        $employeeTransferDetail = \App\Models\Employee\EmployeeTransferDetails::where([
            ['employee_id', $requisitionData->incumbent_id],
            ['replaced', 'Y'],
            ['replaced_emp_id', null]
        ])->orderBy('created_at', 'desc')->first();
        if ($employeeTransferDetail) {
            $employeeTransferDetail->replaced_emp_id = $employee->id;
            $employeeTransferDetail->save();
        }
        logInfo("Updating replaced employee id value completed", ['incumbent_id' => $requisitionData->incumbent_id]);

        if ($request->payslip_meta) {
            $payslipData = $request->payslip_meta['data'];

            logInfo("Assigning transition performer");
            \App\Models\Arflow\TransitionPerformer::create([
                "workflow" => WorkflowName::PAYSLIP_APPROVAL,
                "state" => WorkflowPerformer::APPROVER,
                "performer_id" => $payslipData['verifier_id'],
                "recipient_id" => $employee->id,
                "level" => 1
            ]);
            logInfo("Transition performer assigned successfully.");

            logInfo("Creating payslip information");
            $grade_structure = GradeStructure::where([
                ['grade_id', $payslipData['designation_id']],
                ['band_id', $payslipData['band_id']],
                ['pgrade_id', $payslipData['pgrade_id']]
            ]);
            if (!$grade_structure) throw new Exception("Grade structure not found for:" . json_encode([
                    'grade_id' => $payslipData['designation_id'],
                    'band_id' => $payslipData['band_id'],
                    'pgrade_id' => $payslipData['pgrade_id']
                ]));
            $ssf_employer = $grade_structure->first()->basic_salary * Constant::SSF_EMPLOYER;
            $ssf_employee = $grade_structure->first()->basic_salary * Constant::SSF_EMPLOYEE;
            $grade_structure_id = $grade_structure->pluck('id')->first();
            $data = [
                'company_id'            => $request->company_id,
                'job_id'                => $payslipData['job_id'],
                'designation_id'        => $payslipData['designation_id'],
                'band_id'               => $payslipData['band_id'],
                'pgrade_id'             => $payslipData['pgrade_id'],
                'grade_structure_id'    => $grade_structure_id,
                'verifier_id'           => $payslipData['verifier_id'],
                'employee_id'           => $employee->id, // Id of new created employee
                'allowance_metadata'    => $payslipData['allowance_metadata'],
                'enroll_status'         => $payslipData['enroll_status'],
                'doj_inhouse'           => $payslipData['doj_inhouse'] ?? null,
                'employee_status_id'    => $payslipData['employee_status_id'],
                'stipend'               => $payslipData['stipend'] ? $payslipData['stipend'] : 0,
                'cit'                   => $payslipData['cit'] ? $payslipData['cit'] : 0,
                'ssf_employer'          => $ssf_employer,
                'ssf_employee'          => $ssf_employee,
                'insurance'             => $payslipData['insurance'] ?  $payslipData['insurance'] : 0,
                'insurance_expiry_date' => $payslipData['insurance_expiry_date'],
                'allow_ot'              => $payslipData['allow_ot'],
                'payment_type'          => $payslipData['payment_type'],
                'payslip_type'          => $payslipData['payslip_type'],
                'remarks'               => $payslipData['remarks'],
                'start_date_eng'        => $payslipData['start_date_eng'],
                'stop_date_eng'         => $payslipData['stop_date_eng'],
                'start_date_nep'        => $payslipData['start_date_nep'],
                'stop_date_nep'         => $payslipData['stop_date_nep'],
                'documents'             => [],
            ];
            if ($payslipData['payslip_for'] ?? null) {
                $data['payslip_for'] = $payslipData['payslip_for'];
            }
            if ($payslipData['payment_frequency'] ?? null) {
                $data['payment_frequency'] = $payslipData['payment_frequency'];
            }
            $payslipApprovalRepo = new PayslipApprovalRepository();
            $response = $payslipApprovalRepo->create($data);
            if (!$response['status']) {
                throw $response['message'];
            }
            $payslipRequest = $response['data']['request'];
            $payslipTicket = $response['data']['ticket'];
            $request->payslip_request_id = $payslipRequest->id;
            foreach ($request->requestTicket->documents as $document) {
                TicketDocument::create([
                    'ticket_id' => $payslipTicket->id,
                    'document' => $document->document,
                    'owner_id' => $document->owner_id,
                ]);
            }
            $request->save();
        }

        // Need to create the user in iops only after all the transaction is successfully completed as the employee created in IOPS is not reverted by te DB::rollBack().
        if (vianetHrm()) {
            logInfo("Creating iops employee");
            $iopsRepo = new \App\Http\Repositories\IopsRepository;
            $response = $iopsRepo->createEmployee([...$request->iops_meta, 'password' => $password]);
            if (!$response['status']) return $this->errorResponse($response['message']);

            logInfo("Employee created on iops");

            $iopsId = $response['data']['response']['iopsEmployeeId'];
            $org->update(['iops_id' => $iopsId]);
            logInfo("Updated the iops_id for the user. IOPS ID: " . $iopsId);
        }

        if (vianetHrm()) {
            $smsContent = "Use following credential for HRIS login \r\nUsername:" . $request->username . "\r\nPassword:" . $password;
            SendSMSJob::dispatch($employee->id, $smsContent)->onQueue('sms');
        }

        logInfo("Creating payslip information completed");

        $this->storeDocumentsAfterApproval($request->refresh());

        $this->storeFamilyDetailsAfterApproval($request->refresh());


        logInfo("Employee requisition completed.");

        Cache::flush();
        Cache::store('employee')->flush();
        Cache::store('arflow')->flush();

        return $this->successResponse("Employee requisition approved");
    }

    // for setting payslip details
    public function payslipDetail($payslipData)
    {
        $job = \App\Models\configs\Job::find($payslipData['job_id']);
        $designation = \App\Models\Payroll\Designation::find($payslipData['designation_id']);
        $band = \App\Models\Payroll\EmployeeBand::find($payslipData['band_id']);
        $pgrade = \App\Models\Payroll\EmployeePgrade::find($payslipData['pgrade_id']);
        $employee_status = \App\Models\configs\EmpStatus::find($payslipData['employee_status_id']);
        $additionalAllowance = json_decode($payslipData['allowance_metadata'] ? $payslipData['allowance_metadata'] : "") ?? [];
        $additionalAllowanceMetaData = [];

        $gradeStructure = GradeStructure::where([
            ['grade_id', $payslipData['designation_id']],
            ['band_id', $payslipData['band_id']],
            ['pgrade_id', $payslipData['pgrade_id']],
        ])->first();

        $salaryDetails = [
            "basic_salary"  => $gradeStructure->basic_salary,
            "pf"            => $gradeStructure->pf,
            "ssf"           => $gradeStructure->ssf,
            "allowance"     => $gradeStructure->allowance,
            "gross_salary"  => $gradeStructure->gross_salary,
            "gratuity"      => $gradeStructure->gratuity,
            "ctc"           => $gradeStructure->ctc,
        ];
        $totalCtc = $salaryDetails['ctc'] ?? 0;

        foreach ($additionalAllowance ?? [] as $id => $item) {
            foreach ($item as $key => $value)
                $additionalAllowanceMetaData[$key] = $value ? $value : 0;
            $totalCtc += (float)$value;
        }

        return [
            'payslipInformation' => [
                'Job Profile'           => $job->name,
                'Designation'           => $designation?->title,
                'Grade'                 => "$band?->name($pgrade?->name)",
                "Enroll Status"         => $payslipData['enroll_status'],
                ...($payslipData['doj_inhouse'] ? ['DOJ Inhouse' => $payslipData['doj_inhouse']] : []),
                "Employee Status"       => $employee_status?->name,
                "Stipend"               => $payslipData['stipend'],
                'OT Allowed'            => $payslipData['allow_ot'] ? 'Yes' : 'No',
                'Payment Type'          => $payslipData['payment_type'],
                'Payslip Type'          => $payslipData['payslip_type'],
                'Payslip For'           => $payslipData['payslip_for'] == 'Full_time' ? 'Regular' : $payslipData['payslip_for'],
                'Payslip Frequency'     => $payslipData['payment_frequency'],
                'Insurance Expiry'      => $payslipData['insurance_expiry_date'],
                'Remarks'               => $payslipData['remarks'] ? $payslipData['remarks'] :  'N/A',
                'Payslip Start Date'    => $payslipData['start_date_nep'] . '(' . (LaravelNepaliDate::from($payslipData['start_date_nep']))->toEnglishDate() . ')',
                'Payslip End Date'      => $payslipData['stop_date_nep'] . '(' . (LaravelNepaliDate::from($payslipData['stop_date_nep']))->toEnglishDate() . ')',
            ],
            'salaryStructure' => [
                'basic_salary'          => $salaryDetails['basic_salary'] ?? 0,
                'pf'                    => $salaryDetails['pf'] ?? 0,
                'ssf'                   => $salaryDetails['ssf'] ?? 0,
                'allowance'             => $salaryDetails['allowance'] ?? 0,
                'gross_salary'          => $salaryDetails['gross_salary'] ?? 0,
                'gratuity'              => $salaryDetails['gratuity'] ?? 0,
                'ctc'                   => $salaryDetails['ctc'] ?? 0,
                'additional_allowance'  => $additionalAllowanceMetaData,
                'total_ctc'             => $totalCtc,
                'insurance'             => $payslipData['insurance'],
                'cit'                   => $payslipData['cit'],
            ]
        ];
    }

    public function storeDocumentsAfterApproval(EdfRequest $edfRequest)
    {
        $documents = $edfRequest->personal_documents ?? [];

        logInfo("Storing Employee Documents after approval", $documents);
        $employee = $edfRequest->addedEmployee;
        if ($documents['profile_picture'] ?? false) {
            logInfo("Storing Profile Picture");
            $profilePicture = $documents['profile_picture'];
            unset($documents['profile_picture']);
            $employee->update([
                'profile_picture' => $profilePicture
            ]);
            logInfo("Storing Profile Picture Completed");
        }

        foreach($documents as $key => $value) {
            if (!$value) continue;
            EmployeeDocument::create([
                'name' => $value,
                'type' => $key,
                'employee_id' => $employee->id,
                'status' => 'Approved',
                'actioned_by' => currentEmployeeId(),
                'remarks' => 'Added from edf request id: ' . $edfRequest->id
            ]);
        }
        logInfo("Storing completed");
    }
    public function storeFamilyDetailsAfterApproval(EdfRequest $request)
    {
        $employee = $request->addedEmployee;
        $familyMeta = $request->family_data_meta ?? [];

        foreach ($familyMeta as $relationData) {

            $family = EmployeeFamilyInformation::create([
                "employee_id"   => $employee->id,
                "relation_type" => $relationData['relation_type'],
                "name"          => $relationData['name'],
                "dob_eng"       => $relationData['dob'] ?? null,
                "remarks"       => null
            ]);

            foreach ($relationData['documents'] as $doc) {

                EmployeeFamilyDocument::create([
                    "family_id"      => $family->id,
                    "document_path"  => $doc['file'],      
                    "type"           => $doc['type'],       
                    "status"         => "Submitted",
                    "actioned_by"    => currentEmployeeId(),
                    "remarks"        => "From EDF request {$request->id}"
                ]);
            }
        }

        logInfo("Family details saved for employee " . $employee->id);
    }
}
