<?php

namespace App\Http\Repositories\Snapshots;

abstract class ComplianceSnapshotAbstractRepository
{
    public function __construct() {}

    protected function employmentTypeCase($joinInfo = 'employee_org'): string
    {
        return "
            CASE
                WHEN {$joinInfo}.outsource_company_id IS NULL
                    THEN 'inhouse'
                ELSE 'outsource'
            END
        ";
    }
}
