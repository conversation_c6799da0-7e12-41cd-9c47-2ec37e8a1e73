<?php

namespace App\Http\Repositories\Snapshots;

use App\Http\Helpers\DateRanges;
use App\Models\Snapshot\EmployeeCountSnapshot;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EmployeeSnapshotAnalyticsRepository
{
    /**
     * Base query with joins and filters.
     */
    protected function baseQuery(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $date_en = null
    ): Builder {
        if (!scopeAll()) {
            if (scopeCompany()) {
                $companyId = currentEmployee()->company_id;
            } else if (scopeBranch()) {
                $companyId = currentEmployee()->company_id;
                $branchId = currentEmployee()->organizationInfo->branch_id;
            } else if (scopeDepartment()) {
                $companyId = currentEmployee()->company_id;
                $branchId = currentEmployee()->organizationInfo->branch_id;
                $departmentId = currentEmployee()->organizationInfo->department_id;
            }
        }
        return EmployeeCountSnapshot::query()
            ->join('employee_categories as ec', 'ec.id', '=', 'employee_category_id')
            ->where('ec.is_countable', 1)
            ->when($companyId, fn($q) => $q->where('employee_count_snapshots.company_id', $companyId))
            ->when($branchId, fn($q) => $q->where('employee_count_snapshots.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('employee_count_snapshots.department_id', $departmentId))
            ->when($date_en, fn($q) => $q->where('employee_count_snapshots.date_en', $date_en));
    }

    /**
     * Base scoped query used by summary & daily endpoints.
     */
    protected function employeeCountBaseQuery(string $fromDate, string $toDate, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        return EmployeeCountSnapshot::query()
            ->whereBetween('date_en', [$fromDate, $toDate])
            ->when($companyId, fn($q) => $q->where('company_id', $companyId))
            ->when($branchId, fn($q) => $q->where('branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('department_id', $departmentId));
    }

    /**
     * Summarize employee counts for a date range (totals across matching rows).
     *
     * @return array|null
     */
    public function getEmployeeCountSummary(
        ?Carbon $date = null,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?array $sumColumns = ['active_employees_count', 'new_join_employees_count', 'terminated_employees_count', 'terminating_employees_count']
    ) {
        [$fromDate, $toDate] = DateRanges::normalizeDateRange($date, $from, $to);

        // 1) Whitelist allowed SUM fields
        $allowed = [
            'active_employees_count',
            'new_join_employees_count',
            'terminated_employees_count',
            'terminating_employees_count',
        ];
        $sumColumns = array_values(array_intersect($sumColumns ?? [], $allowed));

        // 2) Build select parts
        $selectParts = [
            'MIN(date_en)  AS start_date_en',
            'MAX(date_en)  AS end_date_en',
        ];

        foreach ($sumColumns as $col) {
            $selectParts[] = "SUM(COALESCE($col, 0)) AS $col";
        }

        $selectParts[] = 'MIN(date_np)  AS start_date_np';
        $selectParts[] = 'MAX(date_np)  AS end_date_np';

        $row = $this->employeeCountBaseQuery($fromDate, $toDate, $branchId, $departmentId, $companyId)
            ->selectRaw(implode(', ', $selectParts))
            ->first();

        return $row;
    }

    /**
     * Get per-day employee counts within a date range (one row per date).
     * Sums are grouped by date, respecting optional branch/department filters.
     *
     * @return array An array of rows ordered by date_en.
     */
    public function dailyEmployeeCounts(
        ?Carbon $date = null,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?array $sumColumns = [
            'active_employees_count',
            'new_join_employees_count',
            'terminated_employees_count',
            'terminating_employees_count',
        ]
    ): Collection {
        [$fromDate, $toDate] = DateRanges::normalizeDateRange($date, $from, $to);

        $allowed = [
            'active_employees_count',
            'new_join_employees_count',
            'terminated_employees_count',
            'terminating_employees_count',
        ];
        $sumColumns = array_values(array_intersect($sumColumns ?? [], $allowed));

        $selectParts = [
            'date_en',
            'MIN(date_np) AS date_np',
        ];

        foreach ($sumColumns as $col) {
            $selectParts[] = "SUM(COALESCE($col, 0)) AS $col";
        }

        return $this->employeeCountBaseQuery($fromDate, $toDate, $branchId, $departmentId, $companyId)
            ->selectRaw(implode(', ', $selectParts))
            ->groupBy('date_en')
            ->orderBy('date_en')
            ->get();
    }

    /**
     * Generic aggregator by employee category type.
     *
     * @param  string      $column      Column to sum (e.g. 'active_employees_count')
     * @return array                   ['total' => int, 'categorywise' => ['type' => int, ...]]
     */
    protected function aggregateByCategoryType(
        string $column,
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null
    ): array {
        $rows = $this->baseQuery($companyId, $branchId, $departmentId)
            ->selectRaw('ec.type as category_type, SUM(' . $column . ') as total_count')
            ->groupBy('ec.type')
            ->get();

        $categoryWise = $rows->mapWithKeys(function ($row) {
            return [
                (string) $row->category_type => (int) $row->total_count,
            ];
        });

        return $categoryWise->toArray();
    }

    public function getActiveEmployeeCountByCategory(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null,
    ): array {

        $rows = $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->select('ec.type as category_type', DB::raw('SUM(active_employees_count) as total_count'))
            ->groupBy('ec.type')
            ->get();

        $categoryWise = $rows->mapWithKeys(function ($row) {
            return [
                (string) $row->category_type => (int) $row->total_count,
            ];
        });

        return $categoryWise->toArray();
    }

    /**
     * Category-wise ACTIVE employees count.
     */
    public function getActiveEmployeeCount(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null
    ): int {
        return (int) $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->sum('active_employees_count');
    }

    public function getActiveEmployeeCountByType(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null
    )
    {
        return $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->select('employment_type', DB::raw('SUM(active_employees_count) as total_count'))
            ->groupBy('employment_type')
            ->pluck('total_count', 'employment_type');
    }

    /**
     * Category-wise TERMINATED employees count.
     */
    public function getTerminatedEmployeeCount(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null
    ): int {
        return (int) $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->sum('terminated_employees_count');
    }

    /**
     * Category-wise TERMINATING employees count.
     */
    public function getTerminatingEmployeeCount(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null
    ): int {
        return (int) $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->sum('terminating_employees_count');
    }

    public function getNewJoinEmployeeCount(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null
    ): int {
        return (int) $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->sum('new_join_employees_count');
    }
}
