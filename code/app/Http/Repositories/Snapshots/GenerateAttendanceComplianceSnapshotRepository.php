<?php

namespace App\Http\Repositories\Snapshots;

use App\Http\Helpers\DateRanges;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\Employee\Employee;
use App\Models\EmployeeCategory;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\EmployeeLeaveDetail;
use App\Models\Snapshot\AttendanceComplianceSnapshot;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class GenerateAttendanceComplianceSnapshotRepository extends ComplianceSnapshotAbstractRepository
{
    public function handle(
        string $date,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null,
    ) {
        $date = Carbon::parse($date);

        $companyIds = $companyId ? [$companyId] : Company::pluck('id')->toArray();
        logInfo("Generating Attendance Compliance Snapshot for Date: $date", $companyIds);

        $dateEn = $date->format('Y-m-d');
        $dateNp = LaravelNepaliDate::from($dateEn)->toNepaliDate();

        $this->clearExistingSnapshot($dateEn, $branchId, $departmentId, $companyId, $categoryId);   

        $activeMap      = $this->getActiveEmployeeCountsMap($date, $companyIds, $branchId, $departmentId, $categoryId);
        $attendanceMap  = $this->getAttendanceAggMap($date, $companyIds, $branchId, $departmentId, $categoryId);
        $paidLeaveMap   = $this->getLeaveCountsMap($dateEn, $companyIds, $branchId, $departmentId, $categoryId, 'paid');
        $unpaidLeaveMap = $this->getLeaveCountsMap($dateEn, $companyIds, $branchId, $departmentId, $categoryId, 'unpaid');

        $keys = array_merge(
            array_keys($activeMap),
            array_keys($attendanceMap),
            array_keys($paidLeaveMap),
            array_keys($unpaidLeaveMap)
        );

        foreach ($keys as $key) {
            [$companyId, $branchId, $departmentId, $categoryId, $employmentType] = $this->parseKey($key);
            $activeCount = $activeMap[$key] ?? 0;

            $agg = $attendanceMap[$key] ?? [
                'absent_count'    => 0,
                'late_in_count'   => 0,
                'early_out_count' => 0,
                'present_count'   => 0,
                'punctual_count'  => 0,
                'day_off_count'   => 0,
            ];

            $paidLeave   = $paidLeaveMap[$key]   ?? 0;
            $unpaidLeave = $unpaidLeaveMap[$key] ?? 0;

            $snapshot = AttendanceComplianceSnapshot::updateOrCreate(
                [
                    'date_en'               => $dateEn,
                    'company_id'            => $companyId,
                    'branch_id'             => $branchId,
                    'department_id'         => $departmentId,
                    'employee_category_id'  => $categoryId,
                    'employment_type'       => $employmentType,
                ],
                [
                    'date_np'               => $dateNp,
                    'active_employee_count' => $activeCount,
                    'present_count'         => $agg['present_count'],
                    'absent_count'          => $agg['absent_count'] + $unpaidLeave,
                    'late_in_count'         => $agg['late_in_count'],
                    'early_out_count'       => $agg['early_out_count'],
                    'leave_count'           => $paidLeave,
                    'punctual_count'        => $agg['punctual_count'],
                    'day_off_count'         => $agg['day_off_count'],
                ]
            );
        }
    }

    /**
     * Build a unique key for maps (now includes category).
     */
    private function makeKey(?int $companyId, ?int $branchId, ?int $departmentId, ?int $categoryId, ?string $employmentType): string
    {
        return implode(':', [
            $companyId    ?? 'null',
            $branchId     ?? 'null',
            $departmentId ?? 'null',
            $categoryId   ?? 'null',
            $employmentType ?? 'null'
        ]);
    }

    protected function parseKey(string $key): array
    {
        [$companyId, $branchId, $departmentId, $categoryId, $employmentType] = explode(':', $key);

        $toInt = fn($v) => $v === 'null' ? null : (int) $v;
        $toStr = fn($v) => $v === 'null' ? null : $v;

        return [
            $toInt($companyId),
            $toInt($branchId),
            $toInt($departmentId),
            $toInt($categoryId),
            $toStr($employmentType),
        ];
    }

    /**
     * Active employees per (company, branch, department, category) on the date.
     */
    private function getActiveEmployeeCountsMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId,
    ): array {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay   = $date->copy()->endOfDay();

        $typeCase  = $this->employmentTypeCase('org');

        return Employee::withTrashed()
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->whereIn('employees.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('org.employee_category_id', $categoryId))
            ->where(function ($q) use ($startOfDay, $endOfDay) {
                $q->whereNull('employees.deleted_at')
                    ->orWhereBetween('employees.deleted_at', [$startOfDay, $endOfDay]);
            })
            ->groupByRaw("
                employees.company_id, 
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase}
            ")
            ->selectRaw("
                employees.company_id,
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase} as employment_type,
                COUNT(DISTINCT employees.id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->employment_type,
                );

                return [$key => (int) $row->total];
            })
            ->all();
    }

    /**
     * Attendance aggregates per (company, branch, department, category).
     */
    private function getAttendanceAggMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId,
    ): array {
        $typeCase  = $this->employmentTypeCase('org');

        return Attendance::query()
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance.employee_id')
            ->leftJoin('employees', 'employees.id', '=', 'attendance.employee_id')
            ->whereDate('attendance.date_en', $date->format('Y-m-d'))
            ->whereIn('employees.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('org.employee_category_id', $categoryId))
            ->groupByRaw("
                employees.company_id, 
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase}
            ")
            ->selectRaw("
                employees.company_id,
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase} as employment_type,
                SUM(CASE WHEN (attendance.status LIKE '%Absent%') THEN 1 ELSE 0 END)            as absent_count,
                SUM(CASE WHEN attendance.in_remarks  LIKE '%Late In%'   THEN 1 ELSE 0 END)     as late_in_count,
                SUM(CASE WHEN attendance.out_remarks LIKE '%Early Out%' THEN 1 ELSE 0 END)     as early_out_count,
                SUM(CASE WHEN (attendance.in_time IS NOT NULL OR attendance.out_time IS NOT NULL)
                        THEN 1 ELSE 0 END)                                                    as present_count,
                SUM(CASE WHEN (attendance.status = 'Present') THEN 1 ELSE 0 END)               as punctual_count,
                SUM(CASE WHEN (attendance.status = 'Day Off') THEN 1 ELSE 0 END)               as day_off_count
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->employment_type
                );

                return [
                    $key => [
                        'absent_count'    => (int) $row->absent_count,
                        'late_in_count'   => (int) $row->late_in_count,
                        'early_out_count' => (int) $row->early_out_count,
                        'present_count'   => (int) $row->present_count,
                        'punctual_count'  => (int) $row->punctual_count,
                        'day_off_count'   => (int) $row->day_off_count
                    ],
                ];
            })
            ->all();
    }

    /**
     * Leave counts per (company, branch, department, category).
     */
    private function getLeaveCountsMap(
        string $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId,
        string $type = 'paid'
    ): array {
        $isPaid = $type === 'paid';
        $typeCase  = $this->employmentTypeCase('org');

        return EmployeeLeaveDetail::query()
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employee_leave_details.employee_id')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'employee_leave_details.leave_type_id')
            ->leftJoin('employees as e', 'e.id', '=', 'employee_leave_details.employee_id')
            ->whereIn('e.company_id', $companyIds)
            ->where('employee_leave_details.date', $date)
            ->when($branchId, fn($q) => $q->where('org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('org.employee_category_id', $categoryId))
            ->when($isPaid,  fn($q) => $q->where('lt.paid', true))
            ->when(!$isPaid, fn($q) => $q->where('lt.paid', false))
            ->groupByRaw("
                e.company_id, 
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase}
            ")
            ->selectRaw("
                e.company_id,
                org.branch_id,
                org.department_id,
                org.employee_category_id,
                {$typeCase} as employment_type,
                COUNT(DISTINCT employee_leave_details.employee_id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->employment_type
                );

                return [$key => (int) $row->total];
            })
            ->all();
    }

    /**
     * Base query for reporting (now category-aware).
     */
    protected function attendanceComplianceBaseQuery(
        string $fromDate,
        string $toDate,
        ?int $branchId,
        ?int $departmentId,
        ?int $companyId = null,
        ?int $categoryId = null,
    ) {
        return AttendanceComplianceSnapshot::query()
            ->whereBetween('date_en', [$fromDate, $toDate])
            ->when($companyId, fn($q) => $q->where('company_id', $companyId))
            ->when($branchId, fn($q) => $q->where('branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('employee_category_id', $categoryId));
    }

    public function summarizeAttendanceCompliance(
        ?Carbon $date = null,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null,
    ): ?AttendanceComplianceSnapshot {
        [$fromDate, $toDate] = DateRanges::normalizeDateRange($date, $from, $to);

        return $this->attendanceComplianceBaseQuery($fromDate, $toDate, $branchId, $departmentId, $companyId, $categoryId)
            ->selectRaw('
                MIN(date_en) AS start_date_en,
                MAX(date_en) AS end_date_en,
                MIN(date_np) AS start_date_np,
                MAX(date_np) AS end_date_np,
                SUM(active_employee_count) AS active_employee_count,
                SUM(present_count) AS present_count,
                SUM(absent_count) AS absent_count,
                SUM(late_in_count) AS late_in_count,
                SUM(early_out_count) AS early_out_count,
                SUM(leave_count) AS leave_count,
                SUM(punctual_count) AS punctual_count
            ')
            ->first();
    }

    public function dailyAttendanceCompliance(
        ?Carbon $date = null,
        ?Carbon $from = null,
        ?Carbon $to = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null,
    ): Collection {
        [$fromDate, $toDate] = DateRanges::normalizeDateRange($date, $from, $to);

        return $this->attendanceComplianceBaseQuery($fromDate, $toDate, $branchId, $departmentId, $companyId, $categoryId)
            ->selectRaw('
                date_en,
                MIN(date_np) AS date_np,
                SUM(active_employee_count) AS active_employee_count,
                SUM(present_count) AS present_count,
                SUM(absent_count) AS absent_count,
                SUM(late_in_count) AS late_in_count,
                SUM(early_out_count) AS early_out_count,
                SUM(leave_count) AS leave_count,
                SUM(punctual_count) AS punctual_count
            ')
            ->groupBy('date_en')
            ->orderBy('date_en')
            ->get();
    }

    protected function clearExistingSnapshot( string $date,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null) {
            AttendanceComplianceSnapshot::where('date_en', $date)
                ->when($branchId, fn($q) => $q->where('branch_id', $branchId))
                ->when($departmentId, fn($q) => $q->where('department_id', $departmentId))
                ->when($companyId, fn($q) => $q->where('company_id', $companyId))
                ->when($categoryId, fn($q) => $q->where('category_id', $categoryId))
                ->delete();
        }

    public function clearOldSnapshots(int $days)
    {
        $cutOffDate = Carbon::now()->subDays($days)->format('Y-m-d');

        $deleted = 0;
        do {
            $batch = AttendanceComplianceSnapshot::whereDate('date_en', '<', $cutOffDate)
                ->limit(1000)
                ->delete();
            $deleted += $batch;
        } while ($batch > 0);

        return ['deleted' => $deleted, 'cut_off_date' => $cutOffDate];
    }
}
