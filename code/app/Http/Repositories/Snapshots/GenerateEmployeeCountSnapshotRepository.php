<?php

namespace App\Http\Repositories\Snapshots;

use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\Company;
use App\Models\Employee\Employee;
use App\Models\Snapshot\EmployeeCountSnapshot;
use App\Models\Termination\EmployeeTermination;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Carbon\Carbon;

class GenerateEmployeeCountSnapshotRepository extends ComplianceSnapshotAbstractRepository
{
    public function handle(
        string $date,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null
    ) {
        $date   = Carbon::parse($date);
        $dateEn = $date->format('Y-m-d');
        $dateNp = LaravelNepaliDate::from($dateEn)->toNepaliDate();

        $this->clearExistingSnapshot($dateEn, $branchId, $departmentId, $companyId, $categoryId);

        $companyIds = $companyId
            ? [$companyId]
            : Company::pluck('id')->toArray();

        // Pre-fetch all counts (grouped by all dimensions)
        $activeCounts      = $this->getActiveCountsMap($date, $companyIds, $branchId, $departmentId, $categoryId);
        $newJoinCounts     = $this->getNewJoinCountsMap($date, $companyIds, $branchId, $departmentId, $categoryId);
        $terminatedCounts  = $this->getTerminatedCountsMap($date, $companyIds, $branchId, $departmentId, $categoryId);
        $terminatingCounts = $this->getTerminatingCountsMap($date, $companyIds, $branchId, $departmentId, $categoryId);

        // Union of all keys
        $allKeys = array_unique(array_merge(
            array_keys($activeCounts),
            array_keys($newJoinCounts),
            array_keys($terminatedCounts),
            array_keys($terminatingCounts),
        ));

        foreach ($allKeys as $key) {
            [
                $companyIdLoop,
                $branchIdLoop,
                $departmentIdLoop,
                $categoryIdLoop,
                $gender,
                $maritalStatus,
                $employmentType,
                $ageGroup,
            ] = $this->parseKey($key);

            $activeCount      = $activeCounts[$key]      ?? 0;
            $newJoinCount     = $newJoinCounts[$key]     ?? 0;
            $terminatedCount  = $terminatedCounts[$key]  ?? 0;
            $terminatingCount = $terminatingCounts[$key] ?? 0;

            logCronInfo("Generating Employee Count Snapshot", [
                'date_en'         => $dateEn,
                'company_id'      => $companyIdLoop,
                'branch_id'       => $branchIdLoop,
                'department_id'   => $departmentIdLoop,
                'category_id'     => $categoryIdLoop,
                'gender'          => $gender,
                'marital_status'  => $maritalStatus,
                'employment_type' => $employmentType,
                'age_group'       => $ageGroup,
                'active'          => $activeCount,
                'new_join'        => $newJoinCount,
                'terminated'      => $terminatedCount,
                'terminating'     => $terminatingCount,
            ]);

            EmployeeCountSnapshot::updateOrCreate(
                [
                    'date_en'              => $dateEn,
                    'company_id'           => $companyIdLoop,
                    'branch_id'            => $branchIdLoop,
                    'department_id'        => $departmentIdLoop,
                    'employee_category_id' => $categoryIdLoop,
                    'gender'               => $gender,
                    'marital_status'       => $maritalStatus,
                    'employment_type'      => $employmentType,
                    'age_group'            => $ageGroup,
                ],
                [
                    'date_np'                     => $dateNp,
                    'active_employees_count'      => $activeCount,
                    'new_join_employees_count'    => $newJoinCount,
                    'terminated_employees_count'  => $terminatedCount,
                    'terminating_employees_count' => $terminatingCount,
                ]
            );
        }
    }

    /**
     * Key: company, branch, department, category, gender, marital_status, employment_type, age_group
     */
    protected function makeKey(
        ?int $companyId,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId,
        ?string $gender,
        ?string $maritalStatus,
        ?string $employmentType,
        ?string $ageGroup
    ): string {
        return implode(':', [
            $companyId        ?? 'null',
            $branchId         ?? 'null',
            $departmentId     ?? 'null',
            $categoryId       ?? 'null',
            $gender           ?? 'null',
            $maritalStatus    ?? 'null',
            $employmentType   ?? 'null',
            $ageGroup         ?? 'null',
        ]);
    }

    protected function parseKey(string $key): array
    {
        [$companyId, $branchId, $departmentId, $categoryId, $gender, $marital, $employment, $ageGroup] = explode(':', $key);

        $toInt = fn($v) => $v === 'null' ? null : (int) $v;
        $toStr = fn($v) => $v === 'null' ? null : $v;

        return [
            $toInt($companyId),
            $toInt($branchId),
            $toInt($departmentId),
            $toInt($categoryId),
            $toStr($gender),
            $toStr($marital),
            $toStr($employment),
            $toStr($ageGroup),
        ];
    }

    /**
     * Helper: age_group CASE expression for Employee (employees.*)
     */
    protected function ageGroupCaseForEmployee(string $dateEn): string
    {
        return "
            CASE
                WHEN employees.dob IS NULL THEN NULL
                WHEN TIMESTAMPDIFF(YEAR, employees.dob, '{$dateEn}') < 20
                    THEN 'age_less_than_20'
                WHEN TIMESTAMPDIFF(YEAR, employees.dob, '{$dateEn}') BETWEEN 20 AND 29
                    THEN 'age_20_to_30'
                WHEN TIMESTAMPDIFF(YEAR, employees.dob, '{$dateEn}') BETWEEN 30 AND 39
                    THEN 'age_30_to_40'
                WHEN TIMESTAMPDIFF(YEAR, employees.dob, '{$dateEn}') BETWEEN 40 AND 49
                    THEN 'age_40_to_50'
                WHEN TIMESTAMPDIFF(YEAR, employees.dob, '{$dateEn}') BETWEEN 50 AND 59
                    THEN 'age_50_to_60'
                ELSE 'age_60_above'
            END
        ";
    }

    /**
     * Helper: age_group CASE expression for Employee alias e (employees as e)
     */
    protected function ageGroupCaseForAliasE(string $dateEn): string
    {
        return "
            CASE
                WHEN e.dob IS NULL THEN NULL
                WHEN TIMESTAMPDIFF(YEAR, e.dob, '{$dateEn}') < 20
                    THEN 'age_less_than_20'
                WHEN TIMESTAMPDIFF(YEAR, e.dob, '{$dateEn}') BETWEEN 20 AND 29
                    THEN 'age_20_to_30'
                WHEN TIMESTAMPDIFF(YEAR, e.dob, '{$dateEn}') BETWEEN 30 AND 39
                    THEN 'age_30_to_40'
                WHEN TIMESTAMPDIFF(YEAR, e.dob, '{$dateEn}') BETWEEN 40 AND 49
                    THEN 'age_40_to_50'
                WHEN TIMESTAMPDIFF(YEAR, e.dob, '{$dateEn}') BETWEEN 50 AND 59
                    THEN 'age_50_to_60'
                ELSE 'age_60_above'
            END
        ";
    }

    /**
     * ACTIVE employees on the given date.
     */
    protected function getActiveCountsMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId
    ): array {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay   = $date->copy()->endOfDay();
        $dateEn     = $date->format('Y-m-d');

        $ageCase   = $this->ageGroupCaseForEmployee($dateEn);
        $typeCase  = $this->employmentTypeCase();

        return Employee::withTrashed()
            ->leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereIn('employees.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('employee_org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('employee_org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('employee_org.employee_category_id', $categoryId))
            ->where(function ($q) use ($startOfDay, $endOfDay) {
                $q->whereNull('employees.deleted_at');
                    // don't count deleted at at all. bihana active xa diuso terminate vayo vaney pani that will be counted as terminated not active
                    // ->orWhereBetween('employees.deleted_at', [$startOfDay, $endOfDay]);
            })
            ->groupByRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat,
                {$typeCase},
                {$ageCase}
            ")
            ->selectRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat as marital_status,
                {$typeCase} as employment_type,
                {$ageCase} as age_group,
                COUNT(DISTINCT employees.id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->gender,
                    $row->marital_status,
                    $row->employment_type,
                    $row->age_group,
                );

                return [$key => (int) $row->total];
            })
            ->all();
    }

    /**
     * NEW JOINS on the given date.
     */
    protected function getNewJoinCountsMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId
    ): array {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay   = $date->copy()->endOfDay();
        $dateEn     = $date->format('Y-m-d');

        $ageCase   = $this->ageGroupCaseForEmployee($dateEn);
        $typeCase  = $this->employmentTypeCase();

        return Employee::leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereIn('employees.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('employee_org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('employee_org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('employee_org.employee_category_id', $categoryId))
            ->where('employee_org.doj', $dateEn)
            ->groupByRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat,
                {$typeCase},
                {$ageCase}
            ")
            ->selectRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat as marital_status,
                {$typeCase} as employment_type,
                {$ageCase} as age_group,
                COUNT(DISTINCT employees.id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->gender,
                    $row->marital_status,
                    $row->employment_type,
                    $row->age_group,
                );
                return [$key => (int) $row->total];
            })
            ->all();
    }

    /**
     * TERMINATED (deleted) on the given date.
     */
    protected function getTerminatedCountsMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId
    ): array {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay   = $date->copy()->endOfDay();
        $dateEn     = $date->format('Y-m-d');

        $ageCase   = $this->ageGroupCaseForEmployee($dateEn);
        $typeCase  = $this->employmentTypeCase();

        return Employee::onlyTrashed()
            ->leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereIn('employees.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('employee_org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('employee_org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('employee_org.employee_category_id', $categoryId))
            ->whereBetween('employees.deleted_at', [$startOfDay, $endOfDay])
            ->groupByRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat,
                {$typeCase},
                {$ageCase}
            ")
            ->selectRaw("
                employees.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                employees.gender,
                employees.mstat as marital_status,
                {$typeCase} as employment_type,
                {$ageCase} as age_group,
                COUNT(DISTINCT employees.id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->gender,
                    $row->marital_status,
                    $row->employment_type,
                    $row->age_group,
                );
                return [$key => (int) $row->total];
            })
            ->all();
    }

    /**
     * TERMINATING (approved termination records) on the given date.
     */
    protected function getTerminatingCountsMap(
        Carbon $date,
        array $companyIds,
        ?int $branchId,
        ?int $departmentId,
        ?int $categoryId
    ): array {
        $startOfDay = $date->copy()->startOfDay();
        $endOfDay   = $date->copy()->endOfDay();
        $dateEn     = $date->format('Y-m-d');

        $ageCase = $this->ageGroupCaseForAliasE($dateEn);
        $typeCase = $this->employmentTypeCase(); // still uses employee_org.*

        return EmployeeTermination::leftJoin('employees as e', 'e.id', '=', 'employee_terminations.employee_id')
            ->leftJoin('employee_org', 'employee_org.employee_id', '=', 'e.id')
            ->whereIn('e.company_id', $companyIds)
            ->when($branchId, fn($q) => $q->where('employee_org.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('employee_org.department_id', $departmentId))
            ->when($categoryId, fn($q) => $q->where('employee_org.employee_category_id', $categoryId))
            ->where('employee_terminations.state', WorkflowState::APPROVED)
            ->where('employee_terminations.termination_date', $dateEn)
            ->whereNull('e.deleted_at')
            ->groupByRaw("
                e.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                e.gender,
                e.mstat,
                {$typeCase},
                {$ageCase}
            ")
            ->selectRaw("
                e.company_id,
                employee_org.branch_id,
                employee_org.department_id,
                employee_org.employee_category_id,
                e.gender,
                e.mstat as marital_status,
                {$typeCase} as employment_type,
                {$ageCase} as age_group,
                COUNT(DISTINCT e.id) as total
            ")
            ->get()
            ->mapWithKeys(function ($row) {
                $key = $this->makeKey(
                    $row->company_id,
                    $row->branch_id,
                    $row->department_id,
                    $row->employee_category_id,
                    $row->gender,
                    $row->marital_status,
                    $row->employment_type,
                    $row->age_group,
                );
                return [$key => (int) $row->total];
            })
            ->all();
    }

    protected function clearExistingSnapshot( string $date,
        ?int $branchId = null,
        ?int $departmentId = null,
        ?int $companyId = null,
        ?int $categoryId = null) {
            EmployeeCountSnapshot::where('date_en', $date)
                ->when($branchId, fn($q) => $q->where('branch_id', $branchId))
                ->when($departmentId, fn($q) => $q->where('department_id', $departmentId))
                ->when($companyId, fn($q) => $q->where('company_id', $companyId))
                ->when($categoryId, fn($q) => $q->where('category_id', $categoryId))
                ->delete();
        }

    public function clearOldSnapshots(int $days)
    {
        $cutOffDate = Carbon::now()->subDays($days)->format('Y-m-d');

        $deleted = 0;
        do {
            $batch = EmployeeCountSnapshot::query()
                ->whereDate('date_en', '<', $cutOffDate)
                ->limit(1000)
                ->delete();
            $deleted += $batch;
        } while ($batch > 0);

        return ['deleted' => $deleted, 'cut_off_date' => $cutOffDate];
    }
}
