<?php

namespace App\Http\Repositories\Snapshots;

use App\Models\Snapshot\AttendanceComplianceSnapshot;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class AttendanceSnapshotAnalyticsRepository
{
    /**
     * Base query with joins and filters.
     */
    protected function baseQuery(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $date_en = null
    ): Builder {
        if (!scopeAll()) {
            if (scopeCompany()) {
                $companyId = currentEmployee()->company_id;
            } else if (scopeBranch()) {
                $companyId = currentEmployee()->company_id;
                $branchId = currentEmployee()->organizationInfo->branch_id;
            } else if (scopeDepartment()) {
                $companyId = currentEmployee()->company_id;
                $branchId = currentEmployee()->organizationInfo->branch_id;
                $departmentId = currentEmployee()->organizationInfo->department_id;
            }
        }

        return AttendanceComplianceSnapshot::query()
            ->join('employee_categories as ec', 'ec.id', '=', 'employee_category_id')
            ->where('ec.is_countable', 1)
            ->when($companyId, fn($q) => $q->where('attendance_compliance_snapshots.company_id', $companyId))
            ->when($branchId, fn($q) => $q->where('attendance_compliance_snapshots.branch_id', $branchId))
            ->when($departmentId, fn($q) => $q->where('attendance_compliance_snapshots.department_id', $departmentId))
            ->when($date_en, fn($q) => $q->where('attendance_compliance_snapshots.date_en', $date_en));
    }

    public function getPresentCount()
    {
        return $this->baseQuery()
            ->sum('present_count');
    }

    public function getAttendanceByCategoryType(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null,
    ): array {
        $rows = $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->selectRaw('
            ec.type as category_type, 
            SUM(present_count) as present_count,
            SUM(absent_count) as absent_count,
            SUM(punctual_count) as punctual_count,
            SUM(leave_count) as leave_count,
            SUM(day_off_count) as day_off_count
        ')
            ->groupBy('ec.type')
            ->get();

        // Prepare arrays grouped by category type
        $present = [];
        $absent = [];
        $dayOff = [];
        $punctual = [];
        $onLeave = [];

        foreach ($rows as $row) {
            $type = (string)$row->category_type;

            $present[$type]  = (int)$row->present_count;
            $absent[$type]   = (int)$row->absent_count;
            $punctual[$type] = (int)$row->punctual_count;
            $dayOff[$type]   = (int)$row->day_off_count;
            $onLeave[$type]  = (int)$row->leave_count;
        }

        return [
            'present' => [
                'total' => array_sum($present),
                'types' => $present
            ],
            'absent' => [
                'total' => array_sum($absent),
                'types' => $absent
            ],
            'day_off' => [
                'total' => array_sum($dayOff),
                'types' => $dayOff
            ],
            'punctual' => [
                'total' => array_sum($punctual),
                'types' => $punctual
            ],
            'on_leave' => [
                'total' => array_sum($onLeave),
                'types' => $onLeave
            ]
        ];
    }

    public function getAttendanceByEmploymentType(
        ?int $companyId = null,
        ?int $branchId = null,
        ?int $departmentId = null,
        string|Carbon|null $dateEn = null,
    ) {
        return $this->baseQuery($companyId, $branchId, $departmentId, $dateEn)
            ->selectRaw('
           employment_type, 
            SUM(present_count) as present_count,
            SUM(absent_count) as absent_count
        ')
            ->groupBy('employment_type')
            ->get()
            ->keyBy('employment_type');
    }
}
