<?php

namespace App\Http\Repositories;

use App\Models\BiometricDeviceMapping;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Education;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeDocument;
use App\Models\Employee\EmployeeTraining;
use App\Models\Employee\Experience;
use App\Models\Payroll\Payment;
use App\Models\Payroll\Payslip;

class ProfileRepository extends Repository{
    
    public function profileInfo($employeeId) {
        return Employee::withTrashed()->where('id', $employeeId)
                        ->firstOrFail();
    }
    
    public function experienceList($employeeId) {
        return Experience::where('employee_id', $employeeId)
                        ->select('id', 'name', 'location', 'position', 'period_from', 'period_to')
                        ->get();
    }
    
    public function educationList($employeeId) {
        return Education::leftJoin('education_levels as el', 'el.id', '=', 'employee_education.level_id')
                        ->where('employee_id', $employeeId)
                        ->select('employee_education.id', 'el.name as level_name', 'employee_education.level_id', 'employee_education.institute_name', 'employee_education.course', 'employee_education.joined_date as joined_date', 'employee_education.completed_date as completed_date', 'employee_education.score')
                        ->get();
    }
    
    public function documentList($employeeId) {
        return EmployeeDocument::where('employee_id', $employeeId);
    }
    
    public function trainingList($employeeId) {
        return EmployeeTraining::where('employee_id', $employeeId)
                        ->select('id', 'name', 'course', 'period_from', 'period_to')
                        ->get();
    }
    
    public function biometricList($biometricId) {
        return BiometricDeviceMapping::with([
                                "attDevice" => function ($query) {
                                    $query->withTrashed()->select("id", "name", "branch_id", "deleted_at");
                                },
                                "attDevice.branch:id,name"
                            ])
                            ->where("biometric_id", $biometricId)
                            ->get();
    }
    
    public function activeFiscalYear() {
        return FiscalYear::where('is_active', 1)->first();
    }
    
    public function pastFiscalYear($startDate) {
        return FiscalYear::where([['is_active', 0], ['end_date', '<', $startDate]])
                            ->whereNull('deleted_at')
                            ->orderBy('start_date', 'desc')
                            ->get();
    }
    
    public function payments($params) {
        return Payment::select('id', 'year', 'month')
                    ->where([
                                ['employee_id', $params['employeeId']], 
                                ['fiscal_year', $params['activeFiscalYear']], 
                                ['is_locked', true], 
                                ['actual_payment_meta', '!=', null], 
                                ['adjusted_payment_meta', '!=', null]
                    ])
                    ->orderBy('year', 'desc')
                    ->orderBy('month', 'desc');
    }

    public function payslipList($employeeId)
    {
        $payslips = Payslip::with(['job', 'designation', 'band', 'level'])
            ->where('employee_id', $employeeId)
            ->select('id', 'employee_id', 'job_id', 'designation_id', 'band_id', 'pgrade_id', 'start_date_eng', 'stop_date_eng', 'status', 'created_at')
            ->orderBy('start_date_eng', 'desc')
            ->get();

        return $payslips;
    }
    
    public function performerList($employee, $desiredWorkflow) {
        $performers = $employee->performers($desiredWorkflow)
            ->with('organizationInfo:id,employee_id,employee_code')
            ->select('employees.id', 'first_name', 'middle_name', 'last_name')
            ->get();

        $result = [];

        foreach ($performers as $item) {
            $workflow = ucwords(str_replace("_", " ", $item->pivot->workflow));
            $state = $item->pivot->state;
            $name_employee_code = $item->name . ' (' . $item->employeeCode . ')';
            $result[$workflow][$state] = [
                "id" => $item['id'],
                "name_employee_code" => isset($result[$workflow][$state]['name_employee_code']) ? $result[$workflow][$state]['name_employee_code'] . ', ' . $name_employee_code : $name_employee_code,
            ];
        }
        
        return $result;
    }
}
