<?php

namespace App\Http\Repositories\FieldVisit;

use App\Http\Helpers\Enums\WorkflowName;
use App\Models\FieldVisit;
use App\Models\Image;
use Exception;
use App\Http\Repositories\FieldVisit\FieldVisitTicketRepository;
use App\Http\Repositories\TicketRepository;
use App\Models\FieldVisitTicket;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;

class FieldVisitRepository
{
    private FieldVisitTicketRepository $fieldVisitTicketRepo;
    private TicketRepository $ticketRepository;

    public function __construct()
    {
        $this->fieldVisitTicketRepo = new FieldVisitTicketRepository;
        $this->ticketRepository = new TicketRepository;
    }

    public function checkIn($employeeId, $data)
    {
        // dd($data);
        $activeVisit = FieldVisit::where('employee_id', $employeeId)->whereNull('check_out_time')->first();

        $checkInDateEn = $data['check_in_date_en'] ?? date('Y-m-d');
        $checkInDateNp = LaravelNepaliDate::from($checkInDateEn)->toNepaliDate();

        if ($activeVisit) {
            throw new \Exception("You have active field visit. Please checkout first");
        }
        $fieldVisit = FieldVisit::create([
            'employee_id' => $employeeId,
            'name' => $data['name'],
            'location' => $data['location'],
            'type' => $data['type'],
            'purpose' => $data['purpose'],
            'message' => $data['message'],
            'check_in_time' => $data['check_in_time'] ?? now()->format('H:i:s'),
            'check_in_lat' => $data['check_in_lat'],
            'check_in_lon' => $data['check_in_lon'],
            'check_in_date_en' => $checkInDateEn,
            'check_in_date_np' => $checkInDateNp,
            'check_in_ip' => $data['check_in_ip'] ?? $this->getIp()['address'],
        ]);

        return  [
            'name' => $fieldVisit->name,
            'location' => $fieldVisit->location,
            'purpose' => $fieldVisit->purpose,
            'message' => $fieldVisit->message,
            'check_in_ip' => $fieldVisit->check_in_ip,
            'type' => $fieldVisit->type,
            'check_in_lat' => $fieldVisit->check_in_lat,
            'check_in_lon' => $fieldVisit->check_in_lat,
            'check_in_time' => $fieldVisit->check_in_time,
            'check_in_date_en' => $fieldVisit->check_in_date_en,
            'check_in_date_np' => $fieldVisit->check_in_date_np,

        ];
    }

    public function checkOut($employeeId, $data)
    {

        $fieldVisit = FieldVisit::where('employee_id', $employeeId)->whereNull('check_out_time')->first();
        $checkOutDateEn = $data['check_out_date_en'] ?? date('Y-m-d');
        $checkOutDateNp = LaravelNepaliDate::from($checkOutDateEn)->toNepaliDate();


        if (!$fieldVisit) {
            throw new Exception("No active field visit is found to check out");
        }

        $fieldVisit->update([
            'check_out_lat' => $data['check_out_lat'],
            'check_out_lon' => $data['check_out_lon'],
            'description' => $data['description'],
            'check_out_time' => $data['check_out_time'] ?? now()->format('H:i:s'),
            'check_out_date_en' => $checkOutDateEn,
            'check_out_date_np' => $checkOutDateNp,
            'check_out_ip' => $data['check_out_ip'] ?? $this->getIp()['address'],
        ]);

        $imageData = [];
        if (!empty($data['images'])) {
            foreach ($data['images'] as $image) {
                $path = $image->store('field_visit_images', 'public');

                $imageInserted = Image::create([
                    'field_visit_id' => $fieldVisit->id,
                    'image_path' => $path,
                    'alt' => $data['alt'] ?? '',
                ]);

                $imageData[] = [
                    'id' => $imageInserted->id,
                    'path' => $imageInserted->image_path,
                    'alt' => $imageInserted->alt,
                ];
            }
        }
        try {
            $this->fieldVisitTicketRepo->createFromFieldVisit($fieldVisit, $data['verifier_id'], $data['images']);
        } catch (Exception $e) {
            logError($e);
        }

        return [
            'description' => $data['description'],
            'check_out_time' => $data['check_out_time'] ?? now()->format('H:i:s'),
            'check_out_ip' => $data['check_out_ip'] ?? $this->getIp()['address'],
            'images' => $imageData,
            'check_out_lat' => $data['check_out_lat'],
            'check_out_lon' => $data['check_out_lon'],
            'check_out_date_en' => $checkOutDateEn,
            'check_out_date_np' => $checkOutDateNp,
        ];
    }
    function getIp()
    {
        $ipType = 'X-Real-Ip';
        $ipAddress = request()->header('X-Real-Ip') ?? null;
        if ($ipAddress === null) {
            $ipType = 'X-Forwarded-For';
            $ipAddress = request()->header('X-Forwarded-For') ?? null;
        }

        if (filter_var($ipAddress, FILTER_VALIDATE_IP))
            return ['type' => $ipType, 'address' => $ipAddress];
        else {
            return ['type' => 'Request-Ip', 'address' => request()->ip()];
        }
    }
    public function getFieldVisitDetail($fieldVisitId)
    {
        $fieldVisitDetail = FieldVisitTicket::with('requestTicket')->select('id', 'employee_id', 'field_visit_id')->Where('id', $fieldVisitId)->first();
        return $fieldVisitDetail;
    }
    public function getActiveVisit($employeeId)
    {
        $fieldVisit = FieldVisit::where('employee_id', $employeeId)->whereNull('check_out_time')->first();

        if ($fieldVisit) {
            return [
                'name' => $fieldVisit->name,
                'location' => $fieldVisit->location,
                'purpose' => $fieldVisit->purpose
            ];
        }
    }
}
