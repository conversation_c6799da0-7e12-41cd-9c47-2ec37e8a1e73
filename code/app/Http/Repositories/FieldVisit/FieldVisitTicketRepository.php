<?php

namespace App\Http\Repositories\FieldVisit;

use App\Http\Helpers\Constant;
use App\Http\Repositories\Attendance\SyncRepository;
use App\Http\Repositories\Repository;
use App\Http\Repositories\TicketRepository;
use App\Models\configs\AttLog;
use App\Models\configs\FiscalYear;
use App\Models\FieldVisit;
use App\Models\FieldVisitTicket;
use App\Models\Leaves\Attendance;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class FieldVisitTicketRepository extends Repository
{
    private TicketRepository $ticketRepo;

    public function __construct()
    {
        $this->ticketRepo = new TicketRepository;
    }

    public function createFromFieldVisit($fieldVisit, $verifier_id, $images)
    {
        $currentDate = LaravelNepaliDate::getToday();
        $fiscalYear = FiscalYear::where('start_date', '<=', $currentDate)
            ->where('end_date', '>=', $currentDate)
            ->where('is_active', true)->first();
        try {
            if ($fieldVisit->check_in_time && $fieldVisit->check_out_time) {

                $t1 = Carbon::parse($fieldVisit->check_in_date_en . ' ' . $fieldVisit->check_in_time);
                $t2 = Carbon::parse($fieldVisit->check_out_date_en . ' ' . $fieldVisit->check_out_time);

                // difference in total minutes
                $diffInMinutes = $t2->diffInMinutes($t1);

                // hours and minutes
                $hours = floor($diffInMinutes / 60);
                $minutes = $diffInMinutes % 60;

                if ($hours > 0 && $minutes > 0) {
                    $totalTime = "{$hours}h {$minutes}min";
                } elseif ($hours > 0) {
                    $totalTime = "{$hours}h";
                } else {
                    $totalTime = "{$minutes}min";
                }

                $ticket = FieldVisitTicket::create([
                    'employee_id'    => $fieldVisit->employee_id,
                    'field_visit_id' => $fieldVisit->id,
                    'fiscal_year_id' => $fiscalYear?->id,
                    'total_hours'    => $totalTime, // e.g. "1h 20min"
                ]);
            }

            $this->ticketRepo->createRequestTicket($ticket, [
                'current_owner_id'  => $verifier_id,
                'employee_id'       => $ticket['employee_id'],
                'documents'         => $images ?? []
            ]);
        } catch (Exception $e) {
            Log::error($e);
        }
    }
    public function verifiers($params)
    {
        return (new FieldVisitTicket)->getNextOwners(employeeId: $params['employee_id'], isSubmitting: $params['is_submitting']);
    }

    public function approveFieldOtRequest(FieldVisitTicket $fieldVisitTicket)
    {
        try {
            DB::beginTransaction();

            if (!$fieldVisitTicket->employee?->organizationInfo?->biometric_id) {
                return $this->responseHelper(false, 'Employee biometric ID not found');
            }
            $fieldVisit = $fieldVisitTicket->fieldVisit;

            $check_in_fetch_date = Carbon::parse($fieldVisitTicket->fieldVisit->check_in_date_en)->format('Y-m-d');
            $check_out_fetch_date = Carbon::parse($fieldVisitTicket->fieldVisit->check_out_date_en)->format('Y-m-d');

            $employeeId = $fieldVisitTicket->employee_id;

            $locationData = [
                'clock_in' => [
                    'lat' => (float)$fieldVisitTicket->fieldVisit->check_in_lat,
                    'lng' => (float)$fieldVisitTicket->fieldVisit->check_in_lon,
                ],
                'clock_out' => [
                    'lat' => (float)$fieldVisitTicket->fieldVisit->check_out_lat,
                    'lng' => (float)$fieldVisitTicket->fieldVisit->check_out_lon,
                ]
            ];

            $commanData = [
                'device_id' => Constant::HRM_APP_ID,
                'enrollment_no' => $fieldVisitTicket->employee->organizationInfo?->biometric_id,
                'verify_mode' => 1,
            ];

            $logs = [
                array_merge($commanData, [
                    'inout_mode' => 0,
                    'device_ip' => $fieldVisitTicket->fieldVisit->check_in_ip,
                    'log_date' => "{$fieldVisit->check_in_date_en} {$fieldVisit->check_in_time}",
                    'fetch_date' => $check_in_fetch_date,
                ]),
                array_merge($commanData, [
                    'inout_mode' => 1,
                    'device_ip' => $fieldVisitTicket->fieldVisit->check_out_ip,
                    'log_date' => "{$fieldVisit->check_out_date_en} {$fieldVisit->check_out_time}",
                    'fetch_date' => $check_out_fetch_date,
                ]),
            ];

            AttLog::insert($logs);
            logInfo("Att Log Created");

            $syncRepo = new SyncRepository;

            $syncRepo->syncAttendanceFromDeviceToDb($check_in_fetch_date, $employeeId);
            $syncRepo->syncAttendanceFromDeviceToDb($check_out_fetch_date, $employeeId);

            logInfo("Attendance Sync");

            $attendanceDate = $check_out_fetch_date ?? $check_in_fetch_date;

            $attendance = Attendance::where('employee_id', $employeeId)
                ->where('date_en', $attendanceDate)
                ->latest('id')
                ->first();

            if ($attendance) {
                $attendance->location_data = $locationData;
                $attendance->save();
            }

            DB::commit();
            return $this->responseHelper(true);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("Failed to approve Field OT Requests: " . $e->getMessage());
            return $this->responseHelper(false, 'Failed to approve field OT request');
        }
    }
}
