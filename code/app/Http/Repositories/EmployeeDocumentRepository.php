<?php

namespace App\Http\Repositories;

use App\Models\Employee\EmployeeDocument;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

class EmployeeDocumentRepository extends Repository
{

    public function documentAction($params)
    {
        return EmployeeDocument::where("id", $params['id'])
            ->update([
                "status" => $params['status'],
                "actioned_by" => $params['actioned_by'],
            ]);
    }

    public function removeRejectedDocuments()
    {
        $employeeDocuments = EmployeeDocument::where([
            ["status", "Rejected"],
            ['created_at', '<=', Carbon::now()->subDays(config('app.removeRejectedDocuments'))]
        ]);

        $filePaths = $employeeDocuments->pluck('name')->toArray();
        foreach ($filePaths as $filePath) {
            if (Storage::exists("public/" . $filePath)) {
                Storage::delete("public/" . $filePath);
                logCronInfo("Remove Rejected Documents. Removed storage files.");
            }
        }

        if ($employeeDocuments->delete()) {
            logCronInfo("Remove Rejected Documents. Removed database records.");
            return $this->successResponse("Rejected documents deleted successfully");
        }
        logCronError("Error removing Rejected Documents.");
        return $this->errorResponse("Error deleting rejected documents");
    }

    public function approveDocuments($date)
    {
        $validator = \Illuminate\Support\Facades\Validator::make(['date' => $date], [
            'date' => 'required|date_format:Y-m-d',
        ]);

        if ($validator->fails())
            return $this->errorResponse($validator->errors()->first());

        $approveDocs = EmployeeDocument::where('created_at', '<=', $date . ' 23:59:59')
            ->where('status', 'Submitted')
            ->update(['status' => 'Approved']);
        if ($approveDocs) {
            logCronInfo("Approved Employee Documents of upto $date");
            return $this->successResponse("Approved documents successfully.");
        }
        logCronError("Error approving Employee Documents of upto $date");
        return $this->errorResponse("Error approving documents.");
    }
}
