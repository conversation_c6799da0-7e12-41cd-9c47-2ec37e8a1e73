<?php

namespace App\Http\Repositories;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\Interfaces\ILeaveDashboardRepository;
use App\Models\configs\Department;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\LeaveRequest;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class LeaveDashboardRepository implements ILeaveDashboardRepository
{
    /** Common exclusion list for non-leave statuses stored in attendance.status */
    protected array $excludeStatuses = ['day off', 'dayoff', 'holiday', 'weekend'];
    protected string $cacheTag = 'leave_dashboard';

    private const BASE_FILTERS_RANGE = [
        'start_en',
        'end_en',
        'leave_type_id',
        'leave_option_id',
        'branch_id',
        'department_id',
        'region_id',
    ];

    private const BASE_FILTERS_DAY = [
        'branch_id',
        'department_id',
        'region_id',
        'leave_type_id',
        'leave_option_id',
    ];

    private function whitelisted(array $filters, array $allowed): array
    {
        return array_intersect_key($filters, array_fill_keys($allowed ?: $allowed, true));
    }

    private function cacheKey(string $prefix, array $filters, array $extras = []): string
    {
        $payload = [
            'filters' => $filters,
            'extras'  => $extras,
            'emp'     => function_exists('currentEmployeeId') ? currentEmployeeId() : null,
        ];
        return $prefix . '_' . md5(json_encode($payload));
    }

    private function remember(string $key, \Closure $callback)
    {
        return Cache::tags($this->cacheTag)->remember($key, 3600, $callback);
    }

    protected function applyOrgFilters(Builder $query, array $filters): Builder
    {
        return $query
            ->when($filters['branch_id'] ?? null, fn($q, $value) => $q->where('org.branch_id', $value))
            ->when($filters['department_id'] ?? null, fn($q, $value) => $q->where('org.department_id', $value))
            ->when($filters['region_id'] ?? null, fn($q, $value) => $q->where('org.region_id', $value));
    }

    public function baseAttendanceRangeQuery(array $filters): Builder
    {
        $start = $filters['start_en'];
        $end   = $filters['end_en'];

        $query = Attendance::query()
            ->join('employees as e', 'e.id', '=', 'attendance.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'e.id')
            ->leftJoin('leave_requests as lr', 'lr.id', '=', 'attendance.leave_request_id')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'lr.leave_type_id')
            ->leftJoin('leave_options as lo', 'lo.id', '=', 'lr.leave_option_id')
            ->whereBetween('attendance.date_en', [$start, $end])
            ->where('attendance.leave_status', 1)
            ->whereNotIn(DB::raw('LOWER(attendance.status)'), $this->excludeStatuses)
            // ->when($filters['employee_ids'] ?? [], fn($q, $v) => $q->whereIn('attendance.employee_id', $v))
            ->when($filters['leave_type_id'] ?? null, fn($q, $v) => $q->where('lt.id', $v))
            ->when($filters['leave_option_id'] ?? null, fn($q, $v) => $q->where('lo.id', $v));

        $query = filterEmployeesByScope($query, 'org', 'e');

        $query = $this->applyOrgFilters($query, $filters);

        return $query;
    }

    public function kpisRange(array $filters): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_RANGE]);
        $key = $this->cacheKey('kpisRange', $filters);

        return $this->remember($key, function () use ($filters) {

            $base = $this->baseAttendanceRangeQuery($filters);
            $personDays = (clone $base)->count();
            $distinctEmployees = (clone $base)->distinct('attendance.employee_id')->count('attendance.employee_id');

            $days = Carbon::parse($filters['start_en'])->diffInDays(Carbon::parse($filters['end_en'])) + 1;
            $avgPerDay = $days > 0 ? round($personDays / $days, 2) : 0;

            $pending = LeaveRequest::query()
                ->leftJoin('employees as e', 'e.id', '=', 'leave_requests.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'leave_requests.employee_id')
                ->when($filters['leave_type_id'] ?? null, fn($q, $v) => $q->where('leave_requests.leave_type_id', $v))
                ->when($filters['leave_option_id'] ?? null, fn($q, $v) => $q->where('leave_requests.leave_option_id', $v))
                ->whereNotIn('leave_requests.state', ArflowHelper::getFinalStates(WorkflowName::LEAVE_APPROVAL))
                ->where(function ($q) use ($filters) {
                    $start = $filters['start_en'];
                    $end = $filters['end_en'];
                    $q->whereBetween('leave_requests.start_date', [$start, $end])
                        ->orWhereBetween('leave_requests.end_date', [$start, $end])
                        ->orWhere(fn($qq) => $qq->where('leave_requests.start_date', '<=', $start)
                            ->where('leave_requests.end_date', '>=', $end));
                });

            if (function_exists('filterEmployeesByScope')) {
                $pending = tap($pending, fn($qq) => filterEmployeesByScope($qq, 'org', 'e'));
            }
            $pending = $this->applyOrgFilters($pending, $filters)->count();

            return [
                'person_days'        => $personDays,
                'distinct_employees' => $distinctEmployees,
                'avg_per_day'        => $avgPerDay,
                'pending_requests'   => $pending,
                'days'               => $days,
            ];
        });
    }

    public function onLeaveForDate(array $filters, string $dateEn, int $page = 1, int $perPage = 10, bool $returnQuery = false): LengthAwarePaginator|Builder
    {
        $filters = $this->whitelisted($filters, [
            ...self::BASE_FILTERS_RANGE,
            'employee_ids',
        ]);
        $cacheKey = $this->cacheKey('onLeaveForDate', $filters, compact('dateEn', 'perPage', 'page'));

        $query = Attendance::query()
            ->join('employees as e', 'e.id', '=', 'attendance.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'e.id')
            ->leftJoin('companies as c', 'c.id', '=', 'e.company_id')
            ->leftJoin('leave_requests as lr', 'lr.id', '=', 'attendance.leave_request_id')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'lr.leave_type_id')
            ->leftJoin('leave_options as lo', 'lo.id', '=', 'lr.leave_option_id')
            ->where('attendance.date_en', $dateEn)
            ->where('attendance.leave_status', 1)
            ->whereNotIn(DB::raw('LOWER(attendance.status)'), $this->excludeStatuses)
            ->when($filters['employee_ids'] ?? null, fn($q, $v) => $q->whereIn('attendance.employee_id', $v))
            ->when($filters['leave_type_id'] ?? null, fn($q, $v) => $q->where('lt.id', $v))
            ->when($filters['leave_option_id'] ?? null, fn($q, $v) => $q->where('lo.id', $v))
            ->select([
                DB::raw("CONCAT_WS(' ', e.first_name, e.middle_name, e.last_name) as employee_name"),
                DB::raw("CONCAT(c.code,'-',org.employee_code) as emp_code"),
                'e.deleted_at as employee_deleted_at',
                'e.id as employee_id',
                'lt.name as leave_type',
                'lo.name as leave_option',
                'lr.id as leave_request_id',
                'attendance.date_en as date_en',
                'attendance.date_np as date_np',
                'lr.workflow as workflow',
            ])
            ->orderBy('employee_name');

        $query = filterEmployeesByScope($query, 'org', 'e');
        $query = $this->applyOrgFilters($query, $filters);

        if ($returnQuery) return $query;

        return $this->remember($cacheKey, function () use ($query, $perPage, $page) {
            return $query->paginate($perPage, ['*'], 'onLeavePage', $page);
        });
    }



    public function chartDailyRange(array $filters): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_RANGE]);
        $key = $this->cacheKey('chartDailyRange', $filters);


        return $this->remember($key, function () use ($filters) {
            $rows = (clone $this->baseAttendanceRangeQuery($filters))
                ->select('attendance.date_np', DB::raw('COUNT(*) as c'))
                ->groupBy('attendance.date_np')->orderBy('attendance.date_np')->get();

            return [
                'categories' => $rows->pluck('date_np')->values(),
                'series' => [['name' => 'On Leave', 'data' => $rows->pluck('c')->values()]],
            ];
        });
    }

    public function chartTypeDonutRange(array $filters): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_RANGE]);
        $key = $this->cacheKey('chartTypeDonutRange', $filters);

        return $this->remember($key, function () use ($filters) {
            $rows = (clone $this->baseAttendanceRangeQuery($filters))
                ->select('lt.id as type_id', 'lt.name as type', DB::raw('COUNT(*) as c'))
                ->groupBy('lt.id', 'lt.name')->orderByDesc('c')->get();

            return [
                'labels' => $rows->pluck('type')->map(fn($v) => $v ?: 'Unknown')->values(),
                'ids'    => $rows->pluck('type_id')->values(),
                'series' => $rows->pluck('c')->values(),
            ];
        });
    }

    public function chartDeptBarRange(array $filters): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_RANGE]);
        $key = $this->cacheKey('chartDeptBarRange', $filters);

        return $this->remember($key, function () use ($filters) {
            $rows = (clone $this->baseAttendanceRangeQuery($filters))
                ->select('org.department_id as dept_id', DB::raw('COUNT(*) as c'))
                ->groupBy('org.department_id')->orderByDesc('c')->get();

            $names = Department::pluck('name', 'id');

            return [
                'categories' => $rows->pluck('dept_id')->map(fn($id) => $names[$id] ?? 'Unknown')->values(),
                'ids'        => $rows->pluck('dept_id')->values(),
                'series'     => [['name' => 'Leave Count', 'data' => $rows->pluck('c')->values()]],
            ];
        });
    }

    public function chartTypeDonutOnDate(array $filters, string $dateEn): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_RANGE]);
        $key = $this->cacheKey('chartTypeDonutOnDate', $filters, compact('dateEn'));

        return $this->remember($key, function () use ($filters, $dateEn) {
            $rows = Attendance::query()
                ->leftJoin('leave_requests as lr', 'lr.id', '=', 'attendance.leave_request_id')
                ->leftJoin('leave_types as lt', 'lt.id', '=', 'lr.leave_type_id')
                ->leftJoin('employees as e', 'e.id', '=', 'attendance.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'e.id')
                ->where('attendance.date_en', $dateEn)
                ->where('attendance.leave_status', 1)
                ->whereNotIn(DB::raw('LOWER(attendance.status)'), $this->excludeStatuses)
                ->when($filters['leave_type_id'] ?? null, fn($q, $v) => $q->where('lt.id', $v))
                ->when($filters['leave_option_id'] ?? null, fn($q, $v) => $q->where('lr.leave_option_id', $v));
            // ->when($filters['employee_ids'] ?? null, fn($q, $v) => $q->whereIn('attendance.employee_id', $v));

            if (function_exists('filterEmployeesByScope')) $rows = tap($rows, fn($qq) => filterEmployeesByScope($qq, 'org', 'e'));
            $rows = $this->applyOrgFilters($rows, $filters)
                ->select('lt.id as type_id', 'lt.name as type', DB::raw('COUNT(*) as c'))
                ->groupBy('lt.id', 'lt.name')->orderByDesc('c')->get();

            return [
                'labels' => $rows->pluck('type')->map(fn($v) => $v ?: 'Unknown')->values(),
                'ids'    => $rows->pluck('type_id')->values(),
                'series' => $rows->pluck('c')->values(),
            ];
        });
    }

    public function chartDeptBarOnDate(array $filters, string $dateEn): array
    {
        $filters = $this->whitelisted($filters, [...self::BASE_FILTERS_DAY]);
        $key = $this->cacheKey('chartDeptBarOnDate', $filters, compact('dateEn'));

        return $this->remember($key, function () use ($filters, $dateEn) {
            $rows = Attendance::query()
                ->leftJoin('leave_requests as lr', 'lr.id', '=', 'attendance.leave_request_id')
                ->join('employees as e', 'e.id', '=', 'attendance.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'e.id')
                ->where('attendance.date_en', $dateEn)
                ->where('attendance.leave_status', 1)
                ->when($filters['leave_type_id'] ?? null, fn($q, $v) => $q->where('lr.leave_type_id', $v))
                ->when($filters['leave_option_id'] ?? null, fn($q, $v) => $q->where('lr.leave_option_id', $v))
                ->whereNotIn(DB::raw('LOWER(attendance.status)'), $this->excludeStatuses);

            if (function_exists('filterEmployeesByScope')) $rows = tap($rows, fn($qq) => filterEmployeesByScope($qq, 'org', 'e'));
            $rows = $this->applyOrgFilters($rows, $filters)
                ->select('org.department_id as dept_id', DB::raw('COUNT(*) as c'))
                ->groupBy('org.department_id')->orderByDesc('c')->get();

            $names = Department::pluck('name', 'id');

            return [
                'categories' => $rows->pluck('dept_id')->map(fn($id) => $names[$id] ?? 'Unknown')->values(),
                'ids'        => $rows->pluck('dept_id')->values(),
                'series'     => [['name' => 'Headcount', 'data' => $rows->pluck('c')->values()]],
            ];
        });
    }
    public function requestsOverlappingRange(array $filters, int $page = 1, int $perPage = 10, bool $returnQuery = false): LengthAwarePaginator|Builder
    {
        $filters = $this->whitelisted($filters, [
            ...self::BASE_FILTERS_RANGE,
            'pending_only',
            'state_in',
            'employee_ids',
            'state',
        ]);
        $cacheKey = $this->cacheKey('requestsOverlappingRange', $filters, compact('perPage', 'page'));

        $query = LeaveRequest::query()
            ->leftJoin('employees as e', 'e.id', '=', 'leave_requests.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'leave_requests.employee_id')
            ->leftJoin('companies as c', 'c.id', '=', 'e.company_id')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'leave_requests.leave_type_id')
            ->leftJoin('leave_options as lo', 'lo.id', '=', 'leave_requests.leave_option_id')
            ->where(function ($q) use ($filters) {
                $start = $filters['start_en'];
                $end = $filters['end_en'];
                $q->whereBetween('leave_requests.start_date', [$start, $end])
                    ->orWhereBetween('leave_requests.end_date', [$start, $end])
                    ->orWhere(fn($qq) => $qq->where('leave_requests.start_date', '<=', $start)
                        ->where('leave_requests.end_date', '>=', $end));
            })

            // dropdown filter
            ->when($filters['state'], function ($query) use($filters) {
                if ($filters['state'] === 'Unapproved') {
                    $query->unapproved();
                } else {
                    $query->where('leave_requests.state', $filters['state']);
                }
            })

            // quick filters, that is triggered through clicking the graphs
            ->when(
                ($filters['pending_only'] ?? false) === true,
                fn($q) => $q->whereNotIn('leave_requests.state', ArflowHelper::getFinalStates(WorkflowName::LEAVE_APPROVAL))
            )
            ->when(
                !empty($filters['state_in']),
                fn($query) => $query->whereIn('leave_requests.state', $filters['state_in'])
            )

            // global filters
            ->when($filters['leave_type_id'] ?? null, fn($query, $v) => $query->where('leave_requests.leave_type_id', $v))
            ->when($filters['leave_option_id'] ?? null, fn($query, $v) => $query->where('leave_requests.leave_option_id', $v))
            ->when($filters['employee_ids'] ?? null, fn($query, $v) => $query->whereIn('leave_requests.employee_id', $v))

            ->select([
                DB::raw("CONCAT_WS(' ', e.first_name, e.middle_name, e.last_name) as employee_name"),
                DB::raw("CONCAT(c.code,'-',org.employee_code) as emp_code"),
                'e.id as employee_id',
                'leave_requests.id',
                'leave_requests.start_date',
                'leave_requests.end_date',
                'leave_requests.nep_start_date',
                'leave_requests.nep_end_date',
                'leave_requests.num_days',
                'leave_requests.state',
                'leave_requests.workflow',
                'lt.name as leave_type',
                'lo.name as leave_option',
                'leave_requests.created_at',
                'e.deleted_at as employee_deleted_at',
            ])
            ->orderByDesc('leave_requests.created_at');

        $query = filterEmployeesByScope($query, 'org', 'e');
        $query = $this->applyOrgFilters($query, $filters);

        if ($returnQuery)
            return $query;

        return $this->remember($cacheKey, function () use ($query, $perPage, $page) {
            return $query->paginate($perPage, ['*'], 'requestsPage', $page);
        });
    }


    public function clearCache(): void
    {
        Cache::tags($this->cacheTag)->flush();
    }

    public function getLeaveCount($startDate, $endDate, $selectedBranch = null, $selectedDepartment = null)
    {
        $query = LeaveRequest::join('leave_types as lt', 'lt.id', '=', 'leave_requests.leave_type_id')
            ->leftJoin('employee_org as org', 'leave_requests.employee_id', '=', 'org.employee_id')
            ->whereIn('leave_requests.state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
            ->where(function ($query) use ($startDate, $endDate) {
                $query->where('leave_requests.nep_start_date', '>=', $startDate)
                    ->where('leave_requests.nep_end_date', '<=', $endDate);
            });

        if ($selectedBranch) {
            $query->where('org.branch_id', $selectedBranch);
        }

        if ($selectedDepartment) {
            $query->where('org.department_id', $selectedDepartment);
        }

        $query = filterEmployeesByScope($query, 'org');

        return $query->select('lt.id', 'lt.name', DB::raw('COUNT(leave_requests.employee_id) as count'))
            ->groupBy('lt.id', 'lt.name')
            ->having('count', '>', 0)
            ->orderBy('lt.name')
            ->get();
    }
}
