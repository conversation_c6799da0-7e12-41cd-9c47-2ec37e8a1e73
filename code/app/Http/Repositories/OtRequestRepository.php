<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\Setting;
use App\Models\FieldTeam\TeamList;
use App\Models\FieldTeam\TeamOtRequest;
use App\Models\OtRequests;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\ReplacementLeaveRequest;
use App\Models\Payroll\Payslip;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\Log;
use Termwind\Components\Dd;

/**
 * Repository for handling Overtime (OT) requests
 * 
 * This class manages the creation, updating, and processing of overtime requests,
 * including calculations for OT eligibility and hours.
 */
class OtRequestRepository extends Repository
{
    /**
     * Ticket repository for handling request tickets
     */
    private TicketRepository $ticketRepo;

    /**
     * Constructor - initializes the ticket repository
     */
    public function __construct()
    {
        $this->ticketRepo = new TicketRepository;
    }

    /**
     * Checks if the the ot is allowed for the employee in the payslip
     * 
     * @param int $employeeId The employee ID
     * @return bool Whether OT is allowed
     */
    public function isOtAllowed($employeeId)
    {
        if (konnectHrm()) {
            return Payslip::where('employee_id', $employeeId)
                ->where('status', 'Active')
                ->where('allow_ot', 1)
                ->exists();
        } else {
            return true;
        }
    }
    /**
     * Retrieves attendance data for a specific date and employee
     * 
     * @param string $nepDate The date in Nepali format
     * @param int $employeeId The employee ID
     * @param bool $isAssigned Whether this is for an assigned request
     * @return object|null The attendance record or null if not found/error
     */
    public function getAttendanceData(string $nepDate, $employeeId, bool $isAssigned = false)
    {
        try {
            // Convert Nepali date to English date
            $date = LaravelNepaliDate::from($nepDate)->toEnglishDate();

            if (!$date) {
                return null;
            }

            // Query attendance record for the given date and employee
            return Attendance::where('date_en', $date)
                ->where('employee_id', $employeeId)
                ->select('in_time', 'out_time', 'duty_start', 'duty_end', 'status', 'total_hours', 'date_en', 'date_np')
                ->first();
        } catch (\Exception $e) {
            Log::error("Error fetching attendance data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Calculates working hours and overtime hours from attendance data
     * 
     * @param array $attendanceData The attendance data
     * @return array Calculation results including total working hours, OT hours, and eligibility
     */
    public function calculateWorkingAndOtHours(array $attendanceData)
    {
        try {
            // Parse time values from attendance data
            $inTime = Carbon::parse($attendanceData['in_time']);
            $outTime = Carbon::parse($attendanceData['out_time']);
            $dutyStartTime = Carbon::parse($attendanceData['duty_start']);
            $dutyEndTime = Carbon::parse($attendanceData['duty_end']);

            // Calculate total hours worked from attendance record
            $totalHours = Carbon::createFromFormat('H:i', $attendanceData['total_hours']);
            $totalMinutes = $totalHours->hour * 60 + $totalHours->minute;

            // Calculate actual duty hours (from duty_start to duty_end)
            $actualTimeInMinutes = $this->calculateTimeDifferenceInMinutes($dutyStartTime, $dutyEndTime);

            // Calculate total working hours (from in_time to out_time)
            $workingTimeInMinutes = $this->calculateTimeDifferenceInMinutes($inTime, $outTime);

            $totalWorkingHours = floor($workingTimeInMinutes / 60);
            $totalWorkingMinutes = $workingTimeInMinutes % 60;

            $result = [
                'total_working_hours' => "{$totalWorkingHours} hours {$totalWorkingMinutes} minutes",
                'actual_working_hours' => floor($actualTimeInMinutes / 60)
            ];

            // Get OT eligibility and required minimum OT minutes from calculateOtTime
            [$isOtTimeEligible, $minMinutesForOt, $actualWorkingMinutes, $status] =
                $this->calculateOtTime(
                    $attendanceData,
                    $actualTimeInMinutes,
                    $workingTimeInMinutes,
                    $dutyEndTime,
                    $outTime
                );


            // Determine if OT is eligible based on time difference
            $isEligible = $isOtTimeEligible && $this->isOtEligible($totalMinutes, $actualWorkingMinutes, $minMinutesForOt, $status);

            // Calculate OT time in hours and minutes
            if ($status == 'Regular') {
                $otTime = max(0, $workingTimeInMinutes - $actualWorkingMinutes - $attendanceData['extra_time']);
            } else {
                $otTime = $totalMinutes;
            }

            $otHours = floor($otTime / 60);
            $otMinutes = $otTime % 60;

            $result['total_ot_hours'] = "{$otHours} hours {$otMinutes} minutes";
            $result['is_ot_eligible'] = $isEligible;
            $result['status'] = $status;

            if ($otHours == 0 && $otMinutes == 0) {
                $result['is_ot_eligible'] = false;
                $result['message'] = 'OT time must be greater than ' . $minMinutesForOt . ' minutes';
                return $result;
            }

            if (!$isEligible) {
                $result['message'] = 'OT time must be at least ' . $minMinutesForOt . ' minutes';
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Error calculating working and OT hours: " . $e->getMessage(), [
                'attendanceData' => $attendanceData,
                'exception' => $e
            ]);
            return [
                'total_working_hours' => "0 hours 0 minutes",
                'total_ot_hours' => "0 hours 0 minutes",
                'is_ot_eligible' => false,
                'message' => 'Error calculating hours'
            ];
        }
    }

    /**
     * Helper method to calculate time difference in minutes
     * 
     * @param Carbon $startTime Start time
     * @param Carbon $endTime End time
     * @return int Time difference in minutes
     */
    private function calculateTimeDifferenceInMinutes(Carbon $startTime, Carbon $endTime): int
    {
        $hours = $endTime->diffInHours($startTime);
        $minutes = $endTime->diffInMinutes($startTime) % 60;
        return ($hours * 60) + $minutes;
    }

    /**
     * Determines if overtime is eligible based on time calculations
     * 
     * @param int $totalMinutes Total minutes worked
     * @param int $actualDutyMinutes Standard duty minutes
     * @param int $minMinutesForOt Minimum minutes required for OT eligibility
     * @param string $status Attendance status (present, holiday, etc.)
     * @return bool Whether OT is eligible
     */
    protected function isOtEligible(int $totalMinutes, int $actualDutyMinutes, int $minMinutesForOt, string $status): bool
    {
        // Calculate OT time differently based on status
        if ($status == 'Regular') {
            // For present status, OT is the difference between total and actual duty time
            $otTime = $totalMinutes - $actualDutyMinutes;
        } else {
            // For other statuses (holiday, weekend), the actual duty minutes are the OT time
            $otTime = $actualDutyMinutes;
        }
        // OT is eligible if it meets or exceeds the minimum required minutes
        return $otTime >= $minMinutesForOt;
    }

    /**
     * Calculates overtime eligibility based on attendance status and settings
     * 
     * @param array $attendanceData Attendance data
     * @param float $actualWorkingMinutes Standard working hours in minutes
     * @param float $totalWorkingMinutes Total working hours in minutes
     * @param Carbon $dutyEndTime Duty end time
     * @param Carbon $outTime Actual out time
     * @return array [isEligible, minMinutesForOt, actualWorkingHours, status, diffBetweenDutyAndOut]
     */
    public function calculateOtTime(
        array $attendanceData,
        float $actualWorkingMinutes,
        float $totalWorkingMinutes,
        Carbon $dutyEndTime,
        Carbon $outTime
    ) {
        // Extract the base status (removing any text in brackets or parentheses)
        $status = trim(preg_split('/[\[\(]/', strtolower($attendanceData['status']))[0]);
        $mapData = [
            'present' => 'Regular',
            'work on holiday' => 'Work On Holiday',
            'work on day off' => 'Work On Day Off'
        ];

        // Get OT settings for this status from the database
        $otSetting = $this->getOtSetting($status, $mapData);

        // Initialize default values
        $isEligible = false;
        $minMinutesForOt = 0;
        // $diffBetweenDutyAndOut = $outTime->diffInMinutes($dutyEndTime);

        if ($otSetting) {
            $minMinutesForOt = (int)($otSetting->value ?? 0);

            // if ($diffBetweenDutyAndOut < $minMinutesForOt) {
            //     return [
            //         false,
            //         $minMinutesForOt,
            //         $actualWorkingMinutes,
            //         $status,
            //         $diffBetweenDutyAndOut,
            //     ];
            // }

            // For non-present statuses (like holidays), use the configured hours
            if ($status !== 'present') {
                $actualWorkingMinutes = $minMinutesForOt;
                $isEligible = ($totalWorkingMinutes >= $actualWorkingMinutes);
            } elseif (($totalWorkingMinutes - $actualWorkingMinutes) >= $minMinutesForOt) {

                $isEligible = true;
            }
        }

        return [
            $isEligible,
            $minMinutesForOt,
            $actualWorkingMinutes,
            $mapData[$status] ?? $status
            // $diffBetweenDutyAndOut
        ];
    }

    /**
     * Get OT setting from database
     * 
     * @param string $status Attendance status
     * @param array $mapData Status mapping data
     * @return object|null OT setting
     */
    private function getOtSetting(string $status, array $mapData)
    {
        return Setting::where('namespace', 'ot')
            ->whereNull('deleted_at')
            ->where('key', $mapData[$status] ?? null)
            ->first();
    }

    /**
     * Summary of getOtSettingMaxDays
     * @param mixed $otType
     */
    public function getOtSettingMaxDays($otType)
    {
        $status = trim(preg_split('/[\[\(]/', strtolower($otType))[0]);
        $mapData = [
            'present' => 'Regular_max_claim_days',
            'work on holiday' => 'Work On Holiday_max_claim_days',
            'work on day off' => 'Work On Day Off_max_claim_days'
        ];
        $otSetting = $this->getOtSetting($status, $mapData);
        return $otSetting->value ?? 30;
    }

    /**
     * Checks if a leave or OT request has already been submitted for a date
     * 
     * @param string $date_en Date in English format
     * @param int $employee Employee ID
     * @return bool Whether a request exists
     */
    public function checkLeaveOrOTSubmitted($date_en, $employee)
    {
        // Convert English date to Nepali
        $date_np = $date_en ? LaravelNepaliDate::from($date_en)->toNepaliDate() : null;

        // Check if an OT request exists for this date and employee
        $otRequestExists = OtRequests::where('employee_id', $employee)
            ->where('nep_date', $date_np)
            ->when(konnectHrm(), function ($query) {
                $query->whereNotIn('state', ['Cancelled', 'Reverted', 'Rejected']);
            }, function ($query) {
                $query->whereNotIn('state', ['Cancelled', 'Reverted']);
            })
            ->exists();

        // Check if a replacement leave request exists for this date and employee
        $replacementLeaveExists = ReplacementLeaveRequest::where('employee_id', $employee)
            ->where('start_date', $date_en)
            ->when(konnectHrm(), function ($query) {
                $query->whereNotIn('state', ['Cancelled', 'Reverted', 'Rejected']);
            }, function ($query) {
                $query->whereNotIn('state', ['Cancelled', 'Reverted']);
            })
            ->exists();

        $teamOtExists = TeamList::whereDate('start_date', '<=', $date_en)
            ->where(function ($query) use ($date_en) {
                $query->whereNull('end_date')
                    ->orWhereDate('end_date', '>=', $date_en);
            })
            ->where(function ($query) use ($employee) {
                $query
                    // Team Leader
                    ->whereHas('teamMembers', function ($q) use ($employee) {
                        $q->where('employee_id', $employee)
                            ->where('is_team_leader', true);
                    })
                    // Regular Members
                    ->orWhereHas('teamMembers', function ($q) use ($employee) {
                        $q->where('employee_id', $employee);
                    })
                    // Temporary Members
                    ->orWhereHas('temporaryTeamMembers', function ($q) use ($employee) {
                        $q->whereJsonContains('member_details', (string) $employee);
                    });
            })
            ->whereHas('otRequests', function ($q) {
                $q->when(konnectHrm(), function ($q) {
                    $q->whereNotIn('state', ['Cancelled', 'Reverted', 'Rejected']);
                }, function ($q) {
                    $q->whereNotIn('state', ['Cancelled', 'Reverted']);
                });
            })
            ->exists();

        // Return true if either request exists
        return $otRequestExists || $replacementLeaveExists || $teamOtExists;
    }

    /**
     * Gets the minimum minutes required for OT eligibility for a given status
     * 
     * @param string $status Attendance status
     * @return int|null Minimum minutes required for OT
     */
    public function minDifferenceForOt($status)
    {
        // Extract the base status (removing any text in brackets or parentheses)
        $status = trim(preg_split('/[\[\(]/', strtolower($status))[0]);
        $mapData = [
            'present' => 'Regular',
            'work on holiday' => 'Work On Holiday',
            'work on day off' => 'Work On Day Off'
        ];

        // Get OT settings for this status from the database
        $otSetting = $this->getOtSetting($status, $mapData);
        $minMinutesForOt = (int)($otSetting->value ?? 0);

        return $minMinutesForOt;
    }
    /**
     * Creates a new overtime request
     * 
     * @param array $data OT request data
     * @return array Response with status and message
     */
    public function createOtRequest(array $data)
    {
        Log::info('Parameters for creating OT request: ', $data);

        // Create the OT request record
        $otRequest = OtRequests::create($data);

        // Create a request ticket for workflow processing
        $this->ticketRepo->createRequestTicket($otRequest, [
            'current_owner_id' => $data['verifier_id'],
            'employee_id' => $data['employee_id'],
            'documents' => $data['documents'] ?? []
        ]);

        return $this->responseHelper(true);
    }

    /**
     * Checks if a date is within the allowed range for OT requests
     * 
     * @param string $date Date to check
     * @param bool $assign Whether this is for an assigned request
     * @param string|null $ot_type OT type key (e.g., '0')
     * @return bool Whether the date is within the allowed range
     */
    public function checkWeekDate($date, $assign = false, $otType = null)
    {
        $status = trim(preg_split('/[\[\(]/', strtolower($otType))[0]);
        $mapData = [
            'present' => 'Regular_max_claim_days',
            'work on holiday' => 'Work On Holiday_max_claim_days',
            'work on day off' => 'Work On Day Off_max_claim_days'
        ];

        $otSetting = $this->getOtSetting($status, $mapData);
        $days = (int)($otSetting->value ?? 30);

        $today = Carbon::now()->startOfDay();

        // Convert Nepali date if assigned
        if ($assign) {
            $date = LaravelNepaliDate::from($date)->toEnglishDate();
        }
        $givenDate = Carbon::parse($date)->startOfDay();
        $dateBefore = Carbon::now()->subDays((int)$days)->startOfDay();

        // Check if the given date is between the allowed range and today
        return $givenDate->between($dateBefore, $today, true);
    }

    /**
     * Updates an existing overtime request
     * 
     * @param OtRequests $request The OT request to update
     * @param array $data Updated data
     * @return array Response with status and message
     */
    public function updateOtRequest(OtRequests $request, array $data)
    {
        Log::info("Parameters for updating OT request of id: ", $data);

        // Update the OT request record
        $request->update($data);
        Log::info("OT Request Updated", ['ot_request_id' => $request?->id]);

        // Update the associated request ticket
        $this->ticketRepo->updateRequestTicket($request, [
            'current_owner_id' => $data['verifier_id'],
            'documents' => $data['documents'] ?? []
        ]);

        return $this->responseHelper(true, "OT Request Updated");
    }

    /**
     * Assigns an OT request to an approver
     * 
     * @param array $data Assignment data
     * @return array Response with status and message
     */
    public function assignOtRequest(array $data)
    {
        Log::info('Parameters for assigning OT request: ', $data);

        // Get the OT request
        $otRequest = OtRequests::findOrFail($data['id']);

        // Create a request ticket for the assignment
        $this->ticketRepo->createRequestTicket($otRequest, [
            'current_owner_id' => $data['verifier_id'],
            'employee_id' => $otRequest->employee_id,
            'documents' => $data['documents'] ?? []
        ]);

        // Transition the request to the assigned state
        $otRequest->transitionTo(WorkflowState::ASSIGNED, "Assigned OT Request", metadata: [
            'initiator_id' => currentEmployee()?->id,
            'initiator_role' => WorkflowPerformer::VERIFIER,
            'next_owner_id' => $data['verifier_id'],
            'next_owner_role' => WorkflowPerformer::APPROVER,
        ]);

        Log::info("OT Request transitioned to assigned", ['ot_request_id' => $otRequest->id]);

        return $this->responseHelper(true, "OT Request Assigned Successfully");
    }

    /**
     * Approves an OT request and updates attendance records
     * 
     * @param OtRequests $model The OT request to approve
     * @return array Response with status and message
     */
    public function approveOtRequest(OtRequests $model)
    {
        Log::info("Approving OT request for employee id: {$model->employee_id}", ['ot_request_id' => $model->id]);

        // Find the attendance record for this date and employee
        $attendance = Attendance::where('employee_id', $model->employee_id)
            ->where('date_np', $model->nep_date)
            ->first();

        if ($attendance) {
            // Add OT remark to attendance record
            if (!empty($attendance->remarks)) {
                $attendance->remarks .= "[OT Applied]";
            } else {
                $attendance->remarks = "OT Applied";
            }

            $attendance->save();
            Log::info("OT Request Updated", $attendance->toArray());
        }

        return $this->responseHelper(true);
    }

    public function approveTeamOtRequest(TeamOtRequest $model)
    {
        Log::info("Approving Team OT request for team id: {$model->team_id}", ['team_ot_request_id' => $model->id]);

        $employeeIds = [];

        // Get team with members
        $team = TeamList::with(['teamMembers', 'temporaryTeamMembers'])->find($model->team_id);

        if (!$team) {
            return $this->responseHelper(false, "Team not found.");
        }

        // Collect all employee IDs
        $teamMembers = $team->teamMembers ?? collect();
        $temporaryMembers = $team->temporaryTeamMembers ?? collect();
        $memberIds = $teamMembers->pluck('employee_id')
            ->merge($temporaryMembers->pluck('employee_id'))
            ->unique()
            ->filter();
        $employeeIds = array_merge($employeeIds, $memberIds->toArray());

        // Parse OT range
        $otStart = Carbon::parse($model->start_time);
        $otEnd = Carbon::parse($model->end_time);
        $totalWorkingMinutes = $otEnd->diffInMinutes($otStart);

        // Fetch attendance records
        $attendances = Attendance::whereIn('employee_id', $employeeIds)
            ->where('date_np', $model->nep_date)
            ->whereNotNull('in_time')
            ->whereNotNull('out_time')
            ->get();

        $eligibleEmployees = 0;
        foreach ($attendances as $attendance) {
            try {
                $logIn = Carbon::parse($attendance->in_time);
                $logOut = Carbon::parse($attendance->out_time);
                $actualMinutes = $logOut->diffInMinutes($logIn);

                // Calculate OT minutes
                $minMinutesForOt = $this->minDifferenceForOt($attendance->status);

                $otMinutes = $actualMinutes - $totalWorkingMinutes;
                if ($otMinutes >= $minMinutesForOt) {
                    // Only update if not already marked
                    $existingRemarks = $attendance->remarks ?? '';
                    if (!str_contains($existingRemarks, 'OT Applied')) {
                        $attendance->remarks = trim($existingRemarks . ' [OT Applied]');
                        $attendance->save();
                        Log::info("OT Remark Updated", $attendance->toArray());
                    }

                    $eligibleEmployees++;
                } else {
                    Log::info("Employee not eligible for OT (less than required OT minutes)", [
                        'employee_id' => $attendance->employee_id,
                        'ot_minutes' => $otMinutes,
                        'actual_minutes' => $actualMinutes,
                        'required_working_minutes' => $totalWorkingMinutes,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Error processing attendance for employee_id: {$attendance->employee_id}", [
                    'error' => $e->getMessage(),
                ]);
                return $this->responseHelper(
                    false,
                    "An error occurred while processing OT for employee ID: {$attendance->employee_id}. Check logs for details."
                );
            }
        }

        return $this->responseHelper(
            true,
            "$eligibleEmployees employee(s) marked with OT Applied remarks."
        );
    }


    public function isEligibleForOt(string $attendanceTime, string $totalWorkingHours): bool
    {
        $attendanceMinutes = Carbon::parse($attendanceTime)->diffInMinutes(Carbon::parse('00:00'));
        $workingMinutes = Carbon::parse($totalWorkingHours)->diffInMinutes(Carbon::parse('00:00'));

        $otMinutes = $attendanceMinutes - $workingMinutes;

        return $otMinutes > 20;
    }
}
