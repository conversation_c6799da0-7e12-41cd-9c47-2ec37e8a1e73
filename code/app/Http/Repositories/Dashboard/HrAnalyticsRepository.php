<?php
namespace App\Http\Repositories\Dashboard;

use App\Http\Repositories\Snapshots\GenerateAttendanceComplianceSnapshotRepository;
use App\Http\Repositories\Snapshots\EmployeeSnapshotAnalyticsRepository;
use App\Http\Services\Dashboard\HrAnalyticsCache;
use Carbon\Carbon;


class HrAnalyticsRepository
{
    /**
     *  Returns date → active_count for a date range.
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @param int|null $branchId
     * @param int|null $departmentId
     */
    public function dailyActiveCounts(Carbon $startDate, Carbon $endDate, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        $result = $this->getEmployeeCounts($startDate, $endDate, $branchId, $departmentId, $companyId);
        return $result->reduce(function ($carry, $item) {
            $carry[$item->date_en->format('Y-m-d')] = $item->active_employees_count;
            return $carry;
        }, []);
    }

    public function newJoinsByDate(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        $result = $this->getEmployeeCounts($start, $end, $branchId, $departmentId, $companyId);

        return $result->reduce(function ($carry, $item) {
            $carry[$item->date_en->format('Y-m-d')] = $item->new_join_employees_count;
            return $carry;
        }, []);
    }

    public function terminationsByDate(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        $result = $this->getEmployeeCounts($start, $end, $branchId, $departmentId, $companyId);

        return $result->reduce(function ($carry, $item) {
            $carry[$item->date_en->format('Y-m-d')] = $item->terminated_employees_count;
            return $carry;
        }, []);
    }

    public function absentEmployeeDays(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        $result = $this->getTotalAttendanceComplianceCount($start, $end, $branchId, $departmentId, $companyId);
        return $result->absent_count;
    }

    public function totalEmployeeDays(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        $result = $this->getTotalAttendanceComplianceCount($start, $end, $branchId, $departmentId, $companyId);
        return $result->active_employee_count;
    }

    /**
     * Returns keyed arrays by date for absent / late_in / early_out.
     * date_en is assumed to be the attendance date column.
     * Recommended index: (date_en, region_id, branch_id)
     */
    public function attendanceMetricsByDate(Carbon $start, Carbon $end, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): array
    {
        $result = $this->getAttendanceComplianceCounts($start, $end, $branchId, $departmentId, $companyId);

        $absent = [];
        $lateIn = [];
        $earlyOut = [];
        $leave = [];
        $present = [];
        $punctual = [];

        foreach ($result as $item) {
            $absent[$item->date_en->format('Y-m-d')] = (int) $item->absent_count;
            $lateIn[$item->date_en->format('Y-m-d')] = (int) $item->late_in_count;
            $earlyOut[$item->date_en->format('Y-m-d')] = (int) $item->early_out_count;
            $leave[$item->date_en->format('Y-m-d')] = (int) $item->leave_count;
            $present[$item->date_en->format('Y-m-d')] = (int) $item->present_count;
            $punctual[$item->date_en->format('Y-m-d')] = (int) $item->punctual_count;
        }

        return compact('absent', 'lateIn', 'earlyOut', 'leave', 'present', 'punctual');
    }

    public function getEmployeeCounts(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        return HrAnalyticsCache::remember(
            "emp-counts:{$start->toDateString()}:{$end->toDateString()}:{$branchId}:{$departmentId}:{$companyId}",
            function () use ($start, $end, $branchId, $departmentId, $companyId) {
                $employeeCountRepo = new EmployeeSnapshotAnalyticsRepository();
                return $employeeCountRepo->dailyEmployeeCounts(from: $start, to: $end, branchId: $branchId, departmentId: $departmentId, companyId: $companyId);
            }
        );
    }

    private function getAttendanceComplianceCounts(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        return HrAnalyticsCache::remember(
            "attendance-counts:{$start->toDateString()}:{$end->toDateString()}:{$branchId}:{$departmentId}:{$companyId}",
            function () use ($start, $end, $branchId, $departmentId, $companyId) {
                $attendanceCountRepo = new GenerateAttendanceComplianceSnapshotRepository();
                $result = $attendanceCountRepo->dailyAttendanceCompliance(from: $start, to: $end, branchId: $branchId, departmentId: $departmentId, companyId: $companyId);

                return $result;
            }
        );
    }

    private function getTotalAttendanceComplianceCount(Carbon $start, Carbon $end, ?int $branchId, ?int $departmentId, ?int $companyId = null)
    {
        return HrAnalyticsCache::remember(
            "total-attendance-counts:{$start->toDateString()}:{$end->toDateString()}:{$branchId}:{$departmentId}:{$companyId}",
            function () use ($start, $end, $branchId, $departmentId, $companyId) {
                $attendanceCountRepo = new GenerateAttendanceComplianceSnapshotRepository();
                $result = $attendanceCountRepo->summarizeAttendanceCompliance(from: $start, to: $end, branchId: $branchId, departmentId: $departmentId, companyId: $companyId);

                return $result;
            }
        );
    }
}
