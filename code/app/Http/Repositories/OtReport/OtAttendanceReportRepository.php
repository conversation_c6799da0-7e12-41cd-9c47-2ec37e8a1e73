<?php

namespace App\Http\Repositories\OtReport;

use App\Http\Repositories\OtReport\Interfaces\OtAttendanceReportRepositoryInterface;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use Illuminate\Support\Collection;
use App\Models\OtRequests;
use Illuminate\Support\Facades\DB;

class OtAttendanceReportRepository implements OtAttendanceReportRepositoryInterface
{
    public function baseOtAttendanceQuery(array $filters): \Illuminate\Database\Query\Builder
    {
        $query = DB::table('ot_requests as ot')
            ->leftJoin('employees as emp', 'emp.id', 'ot.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->leftJoin('branches as br', 'br.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->where('ot.nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('ot.state', 'Approved')
            ->when(!empty($filters['branch']), fn($q) => $q->where('org.branch_id', $filters['branch']))
            ->when(!empty($filters['department']), fn($q) => $q->where('org.department_id', $filters['department']))
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('ot.employee_id', $filters['employee_id']))
            ->select(
                'ot.employee_id as id',
                'comp.name as company',
                'comp.pan_no as pan_no',
                'ot.duty_start',
                'ot.duty_end',
                DB::raw("
                    SUM(
                        TIME_TO_SEC(
                            MAKETIME(
                                SUBSTRING_INDEX(ot.total_ot_hours, ' hours', 1),
                                SUBSTRING_INDEX(SUBSTRING_INDEX(ot.total_ot_hours, 'minutes', 1), ' ', -2),
                                0
                            )
                        )
                    )  as total_ot_hours
                "),
                DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as employee_name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
                DB::raw("COUNT(ot.id) as ot_count"),
                'br.name as branch',
                'dept.name as department',
            )->groupBy(
                'ot.employee_id',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'comp.code',
                'org.employee_code',
                'br.name',
                'dept.name',
                'ot.duty_start',
                'ot.duty_end',
                'comp.name',
            );

        return filterEmployeesByScope($query, 'org', 'emp');
    }

    public function getVarianceReport(array $filters)
    {
        $currentMonth = $this->baseOtAttendanceQuery([
            'year' => $filters['year'],
            'month' => $filters['month'],
            'branch' => $filters['branch'] ?? null,
            'department' => $filters['department'] ?? null,
            'employee_id' => $filters['employee_id'] ?? null,
        ])->get()->keyBy('id');

        $previousMonth = $this->baseOtAttendanceQuery([
            'year' => $filters['variance_year'],
            'month' => $filters['variance_month'],
            'branch' => $filters['branch'] ?? null,
            'department' => $filters['department'] ?? null,
            'employee_id' => $filters['employee_id'] ?? null,
        ])->get()->keyBy('id');

        return $currentMonth->map(function ($current) use ($previousMonth) {
            $previous = $previousMonth[$current->id] ?? null;
            $variance = $current->total_ot_hours - ($previous?->total_ot_hours ?? 0);

            return [
                'id' => $current->id,
                'employee_name' => $current->employee_name,
                'emp_code' => $current->emp_code,
                'branch' => $current->branch,
                'department' => $current->department,
                'ot_count' => $previous?->ot_count ?? 'N/A',
                'current_month_ot' => $current->total_ot_hours,
                'previous_month_ot' => $previous?->total_ot_hours ?? 0,
                'variance_hours' => $variance,
                'variance_status' => match (true) {
                    $variance > 0 => 'Increased',
                    $variance < 0 => 'Decreased',
                    default => 'Same'
                },
            ];
        });
    }

    public function getBranchList($company = null, $region = null)
    {
        $query = Branch::query();
        if ($company) $query->where('company_id', $company);
        if ($region) $query->where('region_id', $region);
        return $query->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function getDepartmentList($company)
    {
        return Department::where('company_id', $company)->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function getOtDetails(array $filters)
    {
        return OtRequests::with([
            'employee',
            'employee.branch',
            'employee.department',
            'employee.activeJob',
            'attendance',
        ])
            ->where('nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('state', 'Approved')
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('employee_id', $filters['employee_id']))
            ->get()
            ->groupBy('employee_id')
            ->map(function ($requests) {
                return $requests->map(function ($item) {
                    $finalOtType = $item->type === 'Regular'
                        ? 'Regular'
                        : ($item->attendance->first()?->status ?? $item->type);

                    return [
                        ...$item->toArray(),
                        'type' => $finalOtType,
                        'initiator_name' => $item->initiator_name,
                        'approved_date' => $item->approved_date,
                        'name' => $item->employee?->name,
                        'emp_code' => $item->employee?->company_emp_code,
                        'branch' => $item->employee?->branch?->name,
                        'department' => $item->employee?->department?->name,
                        'job_name' => $item->employee?->activeJob?->name,
                        'company' => $item->employee?->company?->name,
                        'pan_no' => $item->employee?->company?->pan_no,
                    ];
                })->values();
            });
    }

    public function getOtAttendanceReport(array $filters): Collection
    {
        return $this->baseOtAttendanceQuery($filters)->get();
    }

    /**
     * Optimized method for both list display and export
     * Returns detailed OT attendance data in a single query
     */
    public function getOtAttendanceDetailedReport(array $filters, bool $forExport = false)
    {
        $query = DB::table('ot_requests as ot')
            ->leftJoin('employees as emp', 'emp.id', 'ot.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->leftJoin('branches as br', 'br.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->leftJoin('attendance as att', function ($join) {
                $join->on('att.employee_id', '=', 'ot.employee_id')
                    ->on('att.date_np', '=', 'ot.nep_date');
            })
            ->leftJoin('employees as initiator_emp', function ($join) {
                $join->whereRaw("
                    initiator_emp.id = JSON_UNQUOTE(
                        JSON_EXTRACT(
                            ot.state_metadata,
                            '$.latest_actions[1][1].actor_model_id'
                        )
                    )
                ");
            })
            ->leftJoin('employee_org as initiator_org', 'initiator_org.employee_id', 'initiator_emp.id')
            ->leftJoin('companies as initiator_comp', 'initiator_comp.id', 'initiator_emp.company_id')
            ->leftJoin('jobs as job', 'job.id', 'org.job_id')
            ->where('ot.nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('ot.state', 'Approved')
            ->when(!empty($filters['branch']), fn($q) => $q->where('org.branch_id', $filters['branch']))
            ->when(!empty($filters['department']), fn($q) => $q->where('org.department_id', $filters['department']))
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('ot.employee_id', $filters['employee_id']))
            ->select([
                'ot.id as ot_request_id',
                'ot.employee_id',
                'ot.nep_date',
                'ot.duty_start',
                'ot.duty_end',
                'ot.in_time',
                'ot.out_time',
                'ot.remarks',
                'ot.type as ot_type',
                'ot.created_at',
                'ot.updated_at',
                'comp.name as company',
                'comp.pan_no as pan_no',
                DB::raw("TRIM(CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name)) as employee_name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
                'br.name as branch',
                'dept.name as department',
                // 'job.name as job_name',
                DB::raw("
                    CASE
                        WHEN ot.type = 'Regular' THEN 'Regular'
                        ELSE COALESCE(att.status, ot.type)
                    END as type
                "),
                DB::raw("
                    TIME_TO_SEC(
                        MAKETIME(
                            SUBSTRING_INDEX(ot.total_working_hours, ' hours', 1),
                            SUBSTRING_INDEX(SUBSTRING_INDEX(ot.total_working_hours, 'minutes', 1), ' ', -2),
                            0
                        )
                    ) as total_working_hours_seconds
                "),
                DB::raw("
                    TIME_TO_SEC(
                        MAKETIME(
                            SUBSTRING_INDEX(ot.total_ot_hours, ' hours', 1),
                            SUBSTRING_INDEX(SUBSTRING_INDEX(ot.total_ot_hours, 'minutes', 1), ' ', -2),
                            0
                        )
                    ) as total_ot_hours_seconds
                "),
                DB::raw("
                    CASE
                        WHEN initiator_emp.id IS NOT NULL THEN
                            CONCAT(
                                TRIM(CONCAT_WS(' ',
                                    initiator_emp.first_name,
                                    NULLIF(TRIM(initiator_emp.middle_name), ''),
                                    initiator_emp.last_name
                                )),
                                ' [',
                                CONCAT(initiator_comp.code, '-', initiator_org.employee_code),
                                ']'
                            )
                        ELSE 'N/A'
                    END as approver_name
                "),
                DB::raw("DATE_FORMAT(ot.updated_at, '%d-%m-%Y') as approved_date"),
            ])
            ->orderBy('emp.first_name')
            ->orderBy('emp.last_name')
            ->orderBy('ot.nep_date');

        $query = filterEmployeesByScope($query, 'org', 'emp');

        if ($forExport) {
            return $query->get();
        }

        return $query;
    }

    /**
     * Get export data with proper formatting for OT Attendance Report
     */
    public function getOtAttendanceExportData(array $filters, bool $isVariance = false): array
    {
        if ($isVariance) {
            return $this->getVarianceExportData($filters);
        }

        $heading = [
            'Employee Name',
            'Employee Code',
            'Branch',
            'Job Roles',
            'Department',
            'Duty Start',
            'Duty End',
            'In Time',
            'Out Time',
            'Total Working Hours',
            'Total OT Hours',
            'OT Type',
            'Remarks',
            'Approved By',
            'Applied At',
            'Approved At',
            'Submitted At'
        ];

        $otDetails = $this->getOtAttendanceDetailedReport($filters, true);

        $rows = [];
        foreach ($otDetails as $ot) {
            $rows[] = [
                $ot->employee_name ?? 'N/A',
                $ot->emp_code ?? 'N/A',
                $ot->branch ?? 'N/A',
                $ot->job_name ?? 'N/A',
                $ot->department ?? 'N/A',
                $ot->duty_start ?? '',
                $ot->duty_end ?? '',
                $ot->in_time ?? '',
                $ot->out_time ?? '',
                $this->formatSecondsToTime($ot->total_working_hours_seconds ?? 0),
                $this->formatSecondsToTime($ot->total_ot_hours_seconds ?? 0),
                $ot->type ?? '',
                $ot->remarks ?? '',
                $ot->approver_name ?? 'N/A',
                $ot->nep_date ?? '',
                $ot->approved_date ?? '',
                $ot->nep_date ?? '',
            ];
        }

        return [$heading, $rows];
    }

    /**
     * Get variance export data
     */
    private function getVarianceExportData(array $filters): array
    {
        $heading = [
            'Employee Name',
            'Employee Code',
            'Branch',
            'Job Roles',
            'Department',
            'Duty Start',
            'Duty End',
            'Vendor',
            'Vendor PAN',
            'Current Month OT',
            'Previous Month OT',
            'Variance Hours',
            'Variance Status',
        ];

        $rows = [];

        $currentMonthData = $this->getOtAttendanceReport($filters)->keyBy('id');
        $varianceData = collect($this->getVarianceReport($filters))
            ->mapWithKeys(fn($item) => [$item['id'] => $item])
            ->toArray();

        foreach ($currentMonthData as $employeeId => $currentItem) {
            $variance = $varianceData[$employeeId] ?? null;

            $rows[] = [
                $currentItem->employee_name ?? 'N/A',
                $currentItem->emp_code ?? 'N/A',
                $currentItem->branch ?? 'N/A',
                'N/A', // job_name not available in summary query
                $currentItem->department ?? 'N/A',
                $currentItem->duty_start ?? '',
                $currentItem->duty_end ?? '',
                $currentItem->company ?? 'N/A',
                $currentItem->pan_no ?? 'N/A',
                $this->formatSecondsToTime($variance['current_month_ot'] ?? 0),
                $this->formatSecondsToTime($variance['previous_month_ot'] ?? 0),
                $this->formatSecondsToTime($variance['variance_hours'] ?? 0),
                $variance['variance_status'] ?? 'Same',
            ];
        }

        // Add employees that exist only in previous month
        foreach ($varianceData as $employeeId => $varianceItem) {
            if (!isset($currentMonthData[$employeeId])) {
                $rows[] = [
                    $varianceItem['employee_name'] ?? 'N/A',
                    $varianceItem['emp_code'] ?? 'N/A',
                    $varianceItem['branch'] ?? 'N/A',
                    'N/A',
                    $varianceItem['department'] ?? 'N/A',
                    '',
                    '',
                    $varianceItem['company'] ?? 'N/A',
                    $varianceItem['pan_no'] ?? 'N/A',
                    $this->formatSecondsToTime(0),
                    $this->formatSecondsToTime($varianceItem['previous_month_ot'] ?? 0),
                    $this->formatSecondsToTime($varianceItem['variance_hours'] ?? 0),
                    $varianceItem['variance_status'] ?? 'Same',
                ];
            }
        }

        return [$heading, $rows];
    }

    /**
     * Format seconds to time string
     */
    private function formatSecondsToTime($seconds): string
    {
        if (empty($seconds) || !is_numeric($seconds)) {
            return '0 hours 00 minutes';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return sprintf('%d hours %02d minutes', $hours, $minutes);
    }
}
