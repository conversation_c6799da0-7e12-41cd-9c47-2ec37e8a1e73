<?php

namespace App\Http\Repositories\OtReport;

use App\Http\Repositories\OtReport\Interfaces\OtAttendanceReportRepositoryInterface;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use Illuminate\Support\Collection;
use App\Models\OtRequests;
use Illuminate\Support\Facades\DB;

class OtAttendanceReportRepository implements OtAttendanceReportRepositoryInterface
{
    public function baseOtAttendanceQuery(array $filters): \Illuminate\Database\Query\Builder
    {
        $query = DB::table('ot_requests as ot')
            ->leftJoin('employees as emp', 'emp.id', 'ot.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->leftJoin('branches as br', 'br.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->where('ot.nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('ot.state', 'Approved')
            ->when(!empty($filters['branch']), fn($q) => $q->where('org.branch_id', $filters['branch']))
            ->when(!empty($filters['department']), fn($q) => $q->where('org.department_id', $filters['department']))
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('ot.employee_id', $filters['employee_id']))
            ->select(
                'ot.employee_id as id',
                'comp.name as company',
                'comp.pan_no as pan_no',
                'ot.duty_start',
                'ot.duty_end',
                DB::raw("
                    SUM(
                        TIME_TO_SEC(
                            MAKETIME(
                                SUBSTRING_INDEX(ot.total_ot_hours, ' hours', 1),
                                SUBSTRING_INDEX(SUBSTRING_INDEX(ot.total_ot_hours, 'minutes', 1), ' ', -2),
                                0
                            )
                        )
                    )  as total_ot_hours
                "),
                DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as employee_name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
                DB::raw("COUNT(ot.id) as ot_count"),
                'br.name as branch',
                'dept.name as department',
            )->groupBy(
                'ot.employee_id',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'comp.code',
                'org.employee_code',
                'br.name',
                'dept.name',
                'ot.duty_start',
                'ot.duty_end',
                'comp.name',
            );

        return filterEmployeesByScope($query, 'org', 'emp');
    }

    public function getVarianceReport(array $filters)
    {
        $currentMonth = $this->baseOtAttendanceQuery([
            'year' => $filters['year'],
            'month' => $filters['month'],
            'branch' => $filters['branch'] ?? null,
            'department' => $filters['department'] ?? null,
            'employee_id' => $filters['employee_id'] ?? null,
        ])->get()->keyBy('id');

        $previousMonth = $this->baseOtAttendanceQuery([
            'year' => $filters['variance_year'],
            'month' => $filters['variance_month'],
            'branch' => $filters['branch'] ?? null,
            'department' => $filters['department'] ?? null,
            'employee_id' => $filters['employee_id'] ?? null,
        ])->get()->keyBy('id');

        return $currentMonth->map(function ($current) use ($previousMonth) {
            $previous = $previousMonth[$current->id] ?? null;
            $variance = $current->total_ot_hours - ($previous?->total_ot_hours ?? 0);

            return [
                'id' => $current->id,
                'employee_name' => $current->employee_name,
                'emp_code' => $current->emp_code,
                'branch' => $current->branch,
                'department' => $current->department,
                'ot_count' => $previous?->ot_count ?? 'N/A',
                'current_month_ot' => $current->total_ot_hours,
                'previous_month_ot' => $previous?->total_ot_hours ?? 0,
                'variance_hours' => $variance,
                'variance_status' => match (true) {
                    $variance > 0 => 'Increased',
                    $variance < 0 => 'Decreased',
                    default => 'Same'
                },
            ];
        });
    }

    public function getBranchList($company = null, $region = null)
    {
        $query = Branch::query();
        if ($company) $query->where('company_id', $company);
        if ($region) $query->where('region_id', $region);
        return $query->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function getDepartmentList($company)
    {
        return Department::where('company_id', $company)->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function getOtDetails(array $filters)
    {
        return OtRequests::with([
            'employee',
            'employee.branch',
            'employee.department',
            'employee.activeJob',
            'attendance',
        ])
            ->where('nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('state', 'Approved')
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('employee_id', $filters['employee_id']))
            ->get()
            ->groupBy('employee_id')
            ->map(function ($requests) {
                return $requests->map(function ($item) {
                    $finalOtType = $item->type === 'Regular'
                        ? 'Regular'
                        : ($item->attendance->first()?->status ?? $item->type);

                    return [
                        ...$item->toArray(),
                        'type' => $finalOtType,
                        'initiator_name' => $item->initiator_name,
                        'approved_date' => $item->approved_date,
                        'name' => $item->employee?->name,
                        'emp_code' => $item->employee?->company_emp_code,
                        'branch' => $item->employee?->branch?->name,
                        'department' => $item->employee?->department?->name,
                        'job_name' => $item->employee?->activeJob?->name,
                        'company' => $item->employee?->company?->name,
                        'pan_no' => $item->employee?->company?->pan_no,
                    ];
                })->values();
            });
    }

    public function getOtAttendanceReport(array $filters): Collection
    {
        return $this->baseOtAttendanceQuery($filters)->get();
    }
}
