<?php

namespace App\Http\Repositories\OtReport;

use App\Http\Repositories\OtReport\Interfaces\OtEmployeeReportRepositoryInterface;
use App\Models\OtRequests;
use Illuminate\Support\Facades\DB;

class OtEmployeeReportRepository implements OtEmployeeReportRepositoryInterface
{
    public function getDepartmentList($company)
    {
        return \App\Models\configs\Department::orderBy('name', 'asc')->where('company_id', $company)->pluck("name", "id")->toArray();
    }

    public function getOtEmployeeReport(array $filters)
    {
        $query = OtRequests::query()
            ->leftJoin('employees as emp', 'emp.id', 'ot_requests.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->leftJoin('branches as br', 'br.id', 'org.branch_id')
            ->leftJoin('attendance as att', function ($join) {
                $join->on('att.employee_id', '=', 'ot_requests.employee_id')
                    ->on('att.date_np', '=', 'ot_requests.nep_date');
            })
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->leftJoin('employees as initiator_emp', function ($join) {
                $join->whereRaw("
                    initiator_emp.id = JSON_UNQUOTE(
                        JSON_EXTRACT(
                            ot_requests.state_metadata,
                            '$.latest_actions[1][1].actor_model_id'
                        )
                    )
                ");
            })
            ->leftJoin('employee_org as initiator_org', 'initiator_org.employee_id', 'initiator_emp.id')
            ->leftJoin('companies as initiator_comp', 'initiator_comp.id', 'initiator_emp.company_id')
            ->where('ot_requests.nep_date', 'like', "{$filters['year']}-{$filters['month']}-%")
            ->where('ot_requests.state', 'Approved')
            ->when(!empty($filters['branch']), fn($q) => $q->where('org.branch_id', $filters['branch']))
            ->when(!empty($filters['department']), fn($q) => $q->where('org.department_id', $filters['department']))
            ->when(!empty($filters['employee_id']), fn($q) => $q->where('ot_requests.employee_id', $filters['employee_id']))
            ->select([
                'emp.id as employee_id',
                'initiator_emp.id as approver_id',
                'ot_requests.id',
                'ot_requests.nep_date',
                'ot_requests.duty_start',
                'ot_requests.duty_end',
                'ot_requests.in_time',
                'ot_requests.out_time',
                'ot_requests.remarks',
                'ot_requests.state',
                'ot_requests.created_at',
                'comp.name as company',
                'comp.pan_no as pan_no',
                DB::raw("TRIM(CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name)) as employee_name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
                DB::raw("
                CASE
                    WHEN ot_requests.type = 'Regular' THEN 'Regular'
                    ELSE att.status
                END as type
            "),
                'br.name as branch',
                'dept.name as department',
                DB::raw("
                CASE 
                    WHEN initiator_emp.id IS NOT NULL THEN 
                        CONCAT(
                            CONCAT_WS(' ', 
                                initiator_emp.first_name, 
                                NULLIF(TRIM(initiator_emp.middle_name), ''), 
                                initiator_emp.last_name
                            ),
                            '[',
                            CONCAT_WS(initiator_comp.code, '-', initiator_org.employee_code),
                            ']'
                        )
                    ELSE 'N/A'
                END as approver_name
            "),
                DB::raw("
                SUM(
                    TIME_TO_SEC(
                        MAKETIME(
                            SUBSTRING_INDEX(ot_requests.total_working_hours, ' hours', 1),
                            SUBSTRING_INDEX(SUBSTRING_INDEX(ot_requests.total_working_hours, 'minutes', 1), ' ', -2),
                            0
                        )
                    )
                ) as total_working_hours
            "),
                DB::raw("
                SUM(
                    TIME_TO_SEC(
                        MAKETIME(
                            SUBSTRING_INDEX(ot_requests.total_ot_hours, ' hours', 1),
                            SUBSTRING_INDEX(SUBSTRING_INDEX(ot_requests.total_ot_hours, 'minutes', 1), ' ', -2),
                            0
                        )
                    )
                ) as total_ot_hours
            "),
                DB::raw("DATE_FORMAT(ot_requests.updated_at, '%d-%m-%Y') as approved_date"),
            ])
            ->groupBy([
                'ot_requests.id',
                'ot_requests.nep_date',
                'ot_requests.duty_start',
                'ot_requests.duty_end',
                'ot_requests.in_time',
                'ot_requests.out_time',
                'ot_requests.type',
                'ot_requests.remarks',
                'ot_requests.state',
                'ot_requests.created_at',
                'comp.name',
                'comp.pan_no',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'comp.code',
                'org.employee_code',
                'br.name',
                'dept.name',
                'att.status',
                'initiator_emp.id',
                'initiator_emp.first_name',
                'initiator_emp.middle_name',
                'initiator_emp.last_name',
                'initiator_comp.code',
                'initiator_org.employee_code',
            ]);

        $query = filterEmployeesByScope($query, 'org', 'emp');

        return $query;
    }
}
