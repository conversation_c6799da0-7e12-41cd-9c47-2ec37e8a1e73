<?php

namespace App\Http\Repositories\OtReport\Interfaces;

use Illuminate\Database\Query\Builder;

interface OtAttendanceReportRepositoryInterface
{
    public function getOtAttendanceReport(array $filters);
    public function getVarianceReport(array $filters);
    public function getOtDetails(array $filters);
    public function getBranchList($company, $region);
    public function getDepartmentList($company);
    public function baseOtAttendanceQuery(array $filters): Builder;
    public function getOtAttendanceDetailedReport(array $filters, bool $forExport = false);
    public function getOtAttendanceExportData(array $filters, bool $isVariance = false): array;
}
