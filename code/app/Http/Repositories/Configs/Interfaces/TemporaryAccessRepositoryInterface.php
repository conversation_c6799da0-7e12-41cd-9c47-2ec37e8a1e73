<?php

namespace App\Http\Repositories\Configs\Interfaces;

use App\Models\Config\TemporaryAccess;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface TemporaryAccessRepositoryInterface
{
    public function paginate(int $perPage, int $page, array $filters = []): LengthAwarePaginator;

    public function create(array $data): TemporaryAccess;

    public function find(int $id): ?TemporaryAccess;

    public function pendingToActivate(): Collection;

    public function activeToExpire(): Collection;

    public function forEmployee(int $employeeId): Collection;

    public function update(TemporaryAccess $tempAccess, array $data): TemporaryAccess;

    public function delete(TemporaryAccess $tempAccess): void;
}
