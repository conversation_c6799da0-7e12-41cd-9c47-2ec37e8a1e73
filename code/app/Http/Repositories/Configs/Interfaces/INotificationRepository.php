<?php

namespace App\Http\Repositories\Configs\Interfaces;

interface INotificationRepository
{
    public function notificationList($params);
    public function markAsRead($params);
    public function markAllAsRead($params);
    public function sendFcmFirebaseNotification(int $userId, string $title, string $body, array $data);
    public function createNotification($user, string $title, string $message, string $link, string $actionText, array $meta): bool;
    public function createAndSendDeviceNotification($user, string $title, string $message, string $link, string $actionText, array $meta, bool $push = true): bool;
}
