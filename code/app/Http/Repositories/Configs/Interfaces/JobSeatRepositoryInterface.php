<?php

namespace App\Http\Repositories\Configs\Interfaces;

use App\Models\configs\JobDepartment;
use Exception;

interface JobSeatRepositoryInterface
{
    /*key example;
    $key = [
        'company_id' => 1,
        'region_id' => 1,
        'branch_id' => 1,
        'department_id' => 1,
        'job_id' => 1
    ]*/

    /**
     * Increase the seat plan (mainly used in manpower requisition). Create the record if missing (seat += $delta)
     */
    public function increaseSeatPlan(array $key, int $delta): JobDepartment;

    /**
     * Check destination capacity (used for creation of edf or role change);
     * @throws Exception
     */
    public function checkCapacity(array $key): JobDepartment;

    /**
     * On EDF approval: allocate one seat (occupancy += 1) with company allowed or not.
     */
    public function allocateOnEdfApproval(array $key): JobDepartment;

    /**
     * On payslip approval when job changed: move occupancy from source to destination.
     * If same job, no operation is performed. Validates destination capacity if company has allowed.
     */
    public function moveOnPayslipChange(?array $fromKey, ?array $toKey): void;

    /**
     * Used mainly on termination ticket approval; occupancy -= 1, on_notice_period += 1
     */
    public function increaseOnNotice(array $key);

    /**
     * After employee is terminated, on_notice_period -= 1 
     */
    public function decreaseOnNotice(array $key);

    /**
     * Used mainly on deletion of termination ticket approval; occupancy += 1, on_notice_period -= 1
     */
    public function revertIncreaseOnNotice(array $key): JobDepartment;
    public function restoreEmployee(array $key, bool $permanent = false): ?JobDepartment;

    /**
     * On role change approval: move occupancy from source to destination (like payslip change).
     */
    public function moveOnRoleChange(array $fromKey, array $toKey);

    /**
     * Update occupancy and seat from snapshot using in sync
     */
    public function upsertFromSnapshot(array $key, int $occupancy, bool $seatEqualsOccupancy = false);
    public function deleteJobSeat(array $key);
}
