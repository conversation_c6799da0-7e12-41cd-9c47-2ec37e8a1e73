<?php

namespace App\Http\Repositories\Configs\Interfaces;

use App\Models\CustomNotification;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

interface ICustomNotificationRepository
{
    public function createCustomNotification(array $data, bool $draft = false): CustomNotification;
    public function findCustomNotification(int $id): ?CustomNotification;
    public function getScheduledNotifications(Carbon $date);
    public function getNotificationsList(array $filters = [], array $sort = []): LengthAwarePaginator;
}
