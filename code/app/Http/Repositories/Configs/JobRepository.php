<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Configs\Interfaces\JobRepositoryInterface;
use App\Models\configs\JobDepartment;

class JobRepository implements JobRepositoryInterface {
    public function getJobs($branchId, $departmentId): array {
        if (!$branchId || !$departmentId) {
            return [];
        }
        $jobs = JobDepartment::with('job')->where('branch_id', $branchId)->
            where('department_id', $departmentId)->get()->reduce(function ($carry, $item) {
                $carry[$item->job->id] = $item->job->name;
                return $carry;
            }, []);

        return $jobs;
    }
}