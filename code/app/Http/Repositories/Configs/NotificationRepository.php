<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\Configs\Interfaces\INotificationRepository;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Http\Services\FirebaseNotificationService;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Notifications\DatabaseNotification;

class NotificationRepository implements INotificationRepository
{
    public function __construct(
        private FirebaseNotificationService $firebaseService,
        private IDeviceLogRepository $deviceLogRepo,
        private AppSettingRepositoryInterface $appSettingRepo
    ) {}

    public function notificationList($params)
    {
        $notifications =  DatabaseNotification::select("id", "data", "read_at", "created_at")
            ->where("notifiable_id", $params['user_id'])
            ->orderBy('created_at', 'desc')
            ->get();

        $unreadNotifications = [];
        $previousNotifications = [];
        foreach ($notifications as $key => $notification) {
            $data = $notification->data;
            unset($data['link']);
            unset($data['linkText']);
            if (!($data['meta'] ?? false))
                $data['meta'] = (object)[];
            else {
                if ($data['meta']['date'] ?? null) {
                    $data['meta']['date'] = LaravelNepaliDate::from($data['meta']['date'])->toNepaliDate();
                }
            }

            $item = [
                'id' => $notification->id,
                ...$data,
                'time' => $notification->created_at->diffForHumans(),
                'day' => $notification->created_at->format('d'),
                'month' => $notification->created_at->format('M')
            ];

            if ($notification->read()) {
                $previousNotifications[] = $item;
            } else {
                $unreadNotifications[] = $item;
            }
        }

        return [
            'unread_notifications' => $unreadNotifications,
            'previous_notifications' => $previousNotifications
        ];
    }

    public function markAsRead($params)
    {
        return DatabaseNotification::where("id", $params['id'])
            ->where("notifiable_id", $params['user_id'])
            ->update(["read_at" => now()]);
    }

    public function markAllAsRead($params)
    {
        return DatabaseNotification::where("notifiable_id", $params['user_id'])
            ->update(["read_at" => now()]);
    }

    public function createNotification($user, string $title, string $message, string $link, string $actionText, array $meta): bool
    {
        if (!$user) return false;
        $notification = new \App\Notifications\TaskTicket($title, $message, $link, $actionText, $meta);
        $user->notify($notification);
        return true;
    }

    public function createAndSendDeviceNotification($user, string $title, string $message, string $link, string $actionText, array $meta, bool $push = true): bool
    {
        if (!$user) return false;
        $this->createNotification($user, $title, $message, $link, $actionText, $meta);
        if ($push && $this->appSettingRepo->getFirebaseNotificationEnabled())
            dispatch(new \App\Jobs\FirebaseFcmNotificationJob($user->id, $title, $message, $meta));
        return true;
    }

    public function sendFcmFirebaseNotification(int $userId, string $title, string $body, array $data)
    {
        $fcmTokens = $this->deviceLogRepo->getFcmTokens($userId);
        logInfo("Sending FCM NOtification for user id::$userId", $fcmTokens);
        // $success = [];
        // $errors = [];
        foreach ($fcmTokens as $fcmToken) {
            try {
                if ($fcmToken)
                    $this->firebaseService->sendToDevice($title, $body, $fcmToken, $data);
                logInfo("Notification sent to $fcmToken");
                // $success[] = $fcmToken;
            } catch (\Throwable $e) {
                logError('FCM Error: ', $e, ['token' => $fcmToken]);
                // $errors[] = ['token' => $fcmToken, 'error' => $e->getMessage()];
            }
        }
        return true;
    }

    public function sendTopicNotification(string $topic, string $title, string $body, array $data)
    {
        try {
            $this->firebaseService->sendToTopic($topic, $title, $body, $data);
            return true;
        } catch (\Throwable $e) {
            logCronError("FCM Error while sending notification on topic $topic: ", $e);
            return false;
        }
    }
}
