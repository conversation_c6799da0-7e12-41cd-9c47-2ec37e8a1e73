<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Configs\Interfaces\TemporaryAccessRepositoryInterface;
use App\Models\Config\TemporaryAccess;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Carbon;

class TemporaryAccessRepository implements TemporaryAccessRepositoryInterface
{
    public function paginate(int $perPage, int $page, array $filters = []): LengthAwarePaginator
    {
        $fromEmployeeIds = $filters['from_employee_ids'] ?? [];
        $temporaryEmployeeIds = $filters['temporary_employee_ids'] ?? [];
        return TemporaryAccess::query()
            ->with([
                'temporaryEmployee',
                'temporaryEmployee.organizationInfo',
                'temporaryEmployee.company',
                'fromEmployee',
                'fromEmployee.organizationInfo',
                'fromEmployee.company'
            ])
            ->when($fromEmployeeIds, fn($q) => $q->whereIn('from_employee_id', $fromEmployeeIds))
            ->when($temporaryEmployeeIds, fn($q) => $q->whereIn('temporary_employee_id', $temporaryEmployeeIds))
            ->select('temporary_access.*')
            ->paginate($perPage, ['*'], 'page', $page);
    }

    public function create(array $data): TemporaryAccess
    {
        $data['status'] ??= 'pending';
        return TemporaryAccess::create($data);
    }

    public function find(int $id): ?TemporaryAccess
    {
        return TemporaryAccess::find($id);
    }

    public function pendingToActivate(): Collection
    {
        return TemporaryAccess::where('status', 'pending')
            ->where('from', '<=', Carbon::now()->format('Y-m-d'))
            ->get();
    }

    public function activeToExpire(): Collection
    {
        return TemporaryAccess::query()
            // ->where('status', 'active')
            ->whereNot('status', 'Expired')
            ->where('to', '<', Carbon::now()->format('Y-m-d'))
            ->get();
    }

    public function forEmployee(int $employeeId): Collection
    {
        return TemporaryAccess::where('temporary_employee_id', $employeeId)
            ->orderByDesc('starts_at')
            ->get();
    }

    public function update(TemporaryAccess $tempAccess, array $data): TemporaryAccess
    {
        $tempAccess->fill($data);
        $tempAccess->save();

        return $tempAccess;
    }

    public function delete(TemporaryAccess $tempAccess): void
    {
        $tempAccess->delete();
    }
    public function updateActionMeta(TemporaryAccess $tempAccess, array $data)
    {
        $tempAccess->action_meta = array_merge($this->action_meta ?? [], $data);
        $tempAccess->save();
    }
}
