<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Configs\Interfaces\ICustomNotificationRepository;
use App\Models\CustomNotification;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomNotificationRepository implements ICustomNotificationRepository
{
    public function createCustomNotification(array $data, bool $draft = false): CustomNotification
    {
        if ($draft)
            $data['status'] = 'draft';
        else if ($data['scheduled_at'])
            $data['status'] = 'scheduled';
        else
            $data['status'] = 'pending';

        $data['created_by'] = auth()->user()?->id;
        $notification = CustomNotification::create($data);
        return $notification;
    }

    public function findCustomNotification(int $id): ?CustomNotification
    {
        $notification = CustomNotification::where('id', $id)->first();
        return $notification;
    }

    public function getScheduledNotifications(Carbon $date)
    {
        $notifications = CustomNotification::where('status', 'scheduled')
            ->where('scheduled_at', '<=', "{$date->format('Y-m-d H:i')}")
            ->get();
        return $notifications;
    }

    public function getNotificationsList(array $filters = [], array $sort = []): LengthAwarePaginator
    {
        $perPage = $filters['perPage'] ?? 10;
        $page = $filters['page'] ?? 1;
        $sortBy = $sort['sortBy'] ?? 'created_at';
        $sortDirection = $sort['sortDirection'] ?? 'desc';

        $query = CustomNotification::query()->with('creator');

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($query) use ($filters) {
                $query->where('title', 'like', "%{$filters['search']}%")
                    ->orWhere('message', 'like', "%{$filters['search']}%");
            });
        }

        if (isset($filters['target'])) {
            $query->where('target', $filters['target']);
        }

        // Apply date range filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy($sortBy, $sortDirection)->paginate($perPage, page: $page);
    }
}
