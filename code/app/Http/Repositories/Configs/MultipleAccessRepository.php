<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Configs\Interfaces\IMultipleAccessRepository;
use App\Models\Config\MultipleAccess;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MultipleAccessRepository implements IMultipleAccessRepository
{
    public function create(array $data): bool
    {
        try {
            DB::beginTransaction();
            Log::info("Create Multiple Access. Data:".print_r($data, true));
            $insertData = [
                "employee_id" => $data['employee_id'],
                "type"        => $data['access_type'],
                "model_type"  => $data['model_type'],
                "model_id"    => $data['model_id'],
                "from"        => $data['from'],
                "to"          => $data['to'],
                "expired"     => $data['expired']
            ];
            $insertMultipleAccess = MultipleAccess::updateOrCreate(
                [
                    "employee_id" => $data['employee_id'],
                    "type"        => $data['access_type'],
                    "model_type"  => $data['model_type'],
                    "model_id"    => $data['model_id'],
                ],
                $insertData
            );
            Log::info("Create Multiple Access. Response:".print_r($insertMultipleAccess, true));
            if($insertMultipleAccess) {
                Cache::tags(['multiple-access'])->forget("multiple-access-".$data['employee_id']."-".$data['access_type']);
                DB::commit();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Create Multiple Access. Exception:".print_r($e->getMessage(), true));
            return false;
        }
    }
    
    public function edit($id): MultipleAccess {
        return MultipleAccess::find($id);
    }

    public function update(int $id, array $data): bool
    {
        try {
            DB::beginTransaction();
            Log::info("Update Multiple Access. Id:".print_r($id, true));
            
            $multipleAccess = MultipleAccess::findOrFail($id);

            $updateMultipleAccess = $multipleAccess->update($data);

            Cache::tags(['multiple-access'])
                ->forget("multiple-access-{$multipleAccess->employee_id}-{$multipleAccess->type}");
            
            Log::info("Update Multiple Access. Response:".print_r($updateMultipleAccess, true));
            DB::commit();
            return $updateMultipleAccess;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Update Multiple Access. Exception:".print_r($e->getMessage(), true));
            return false;
        }
    }

    public function delete(int $id): bool
    {
        try {
            DB::beginTransaction();
            Log::info("Delete Multiple Access. Id:".print_r($id, true));
            $multipleAccess = MultipleAccess::findOrFail($id);

            $deleteMultipleAccess = $multipleAccess->delete();

            Cache::tags(['multiple-access'])
                ->forget("multiple-access-{$multipleAccess->employee_id}-{$multipleAccess->type}");
            
            Log::info("Delete Multiple Access. Response:".print_r($deleteMultipleAccess, true));
            DB::commit();
            return $deleteMultipleAccess;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Delete Multiple Access. Exception:".print_r($e->getMessage(), true));
            return false;
        }
    }
}
