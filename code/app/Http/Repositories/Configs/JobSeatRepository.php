<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface;
use App\Models\configs\JobDepartment;
use Exception;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class JobSeatRepository implements JobSeatRepositoryInterface
{
    /**
     * for the validation of the key and normalizing it to integer
     */
    private function normalizeKey(array $key): array
    {
        $required = ['company_id', 'branch_id', 'department_id', 'job_id'];
        foreach ($required as $r) {
            if (!array_key_exists($r, $key)) {
                throw new InvalidArgumentException("Missing key for job seat: $r");
            }
            $key[$r] = (int) $key[$r];
        }
        return $key;
    }

    private function allowJobSeatCompany(array $key)
    {
        return allowJobSeat($key['company_id']);
    }

    private function getRowsForUpdate(array $key, bool $createIfMissing = false): ?JobDepartment
    {
        $row = JobDepartment::where($key)->lockForUpdate()->first();
        if ($createIfMissing && !$row) {
            $row = JobDepartment::create($key + [
                'seat' => 0,
                'occupancy' => 0,
                'on_notice_period' => 0
            ]);

            $row = JobDepartment::where($key)->lockForUpdate()->first();
        }
        return $row;
    }

    public function deleteJobSeat(array $key) {
        JobDepartment::where($key)->delete();
    }

    /**
     * notZero; if less than 0 return 0
     */
    private function nz(int $v): int
    {
        return $v < 0 ? 0 : $v;
    }

    public function increaseSeatPlan(array $key, int $delta): JobDepartment
    {
        $key = $this->normalizeKey($key);
        if ($delta <= 0) {
            throw new Exception("Delta must be greater than 0 for increasing job seat");
        }

        return DB::transaction(function () use ($key, $delta) {
            $row = $this->getRowsForUpdate($key, true);
            $row->seat = $this->nz($row->seat + $delta);
            $row->save();

            logInfo("[JobSeatRepository] Seat Plan Increased", [
                'key' => $key,
                'delta' => $delta,
                'seat' => $row->seat
            ]);

            return $row;
        });
    }

    public function checkCapacity(array $key): JobDepartment
    {
        $key = $this->normalizeKey($key);
        $allowed = $this->allowJobSeatCompany($key);

        return DB::transaction(function () use ($key, $allowed) {

            // here, creating job seat if the company is not allowed, if the company is allowed, then the job seat must be create manually
            // this is done for those company whose job seat is not allowed to maintain the record
            $row = $this->getRowsForUpdate($key, !$allowed);

            if (!$row) {
                logWarning("[JobSeatRepository] Check Capacity Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            if ($allowed && $row->isSeatFull) {
                logWarning("[JobSeatRepository] Check Capacity Failed: seat full", context: $key);
                throw new Exception("Job seat is full");
            }

            return $row;
        });
    }

    public function allocateOnEdfApproval(array $key): JobDepartment
    {
        $key = $this->normalizeKey($key);
        $allowed = $this->allowJobSeatCompany($key);

        return DB::transaction(function () use ($key, $allowed) {
            $row = $this->getRowsForUpdate($key, !$allowed);

            if (!$row) {
                logWarning("[JobSeatRepository] Allocate On EDF Approval Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            if ($allowed && $row->isSeatFull) {
                logWarning("[JobSeatRepository] Allocate On EDF Approval Failed: seat full", context: $key);
                throw new Exception("Job seat is full");
            }

            $row->occupancy = $row->occupancy + 1;
            $row->save();

            logInfo("[JobSeatRepository] Allocate On EDF Approval", [
                'key' => $key,
                'seat' => $row->seat,
                'occupancy' => $row->occupancy
            ]);

            return $row;
        });
    }

    public function moveOnPayslipChange(?array $fromKey, ?array $toKey): void
    {
        if (!$fromKey && !$toKey) {
            logInfo("[JobSeatRepository] Move On Payslip Change: no change null keys");
        }

        $fromKey = $fromKey ? $this->normalizeKey($fromKey) : null;
        $toKey = $fromKey ? $this->normalizeKey($toKey) : null;

        if ($fromKey && $toKey && $fromKey == $toKey) {
            logInfo("[JobSeatRepository] Move On Payslip Change: no change (same keys)");
            return;
        }

        $fromAllowed = $fromKey ? $this->allowJobSeatCompany($fromKey) : true;
        $toAllowed = $toKey ? $this->allowJobSeatCompany($toKey) : true;

        DB::transaction(function () use ($fromKey, $toKey, $fromAllowed, $toAllowed) {
            $fromRow = $fromKey ? $this->getRowsForUpdate($fromKey) : null;
            $toRow = $toKey ? $this->getRowsForUpdate($toKey, !$toAllowed) : null;

            if ($toKey) {
                if (!$toRow) {
                    logWarning("[JobSeatRepository] Move On Payslip Change Failed: to job seat not found", context: $toKey);
                    throw new Exception("Job seat not found");
                }
                if ($toAllowed && $toRow->isSeatFull) {
                    logWarning("[JobSeatRepository] Move On Payslip Change Failed: to job seat seat full", context: $toKey);
                    throw new Exception("Job seat is full");
                }
            }

            if ($fromKey && $fromRow) {
                $fromRow->occupancy = $this->nz($fromRow->occupancy - 1);
                $fromRow->save();
                logInfo("[JobSeatRepository] Move On Payslip Change: decremented source occupancy", [
                    'from' => $fromKey,
                    'occupancy' => $fromRow->occupancy
                ]);
            }

            if ($toKey) {
                $toRow->occupancy = $this->nz($toRow->occupancy + 1);
                $toRow->save();
                logInfo("[JobSeatRepository] Move On Payslip Change: incremented destination occupancy", [
                    'to' => $toKey,
                    'occupancy' => $toRow->occupancy
                ]);
            }
        });
    }

    public function increaseOnNotice(array $key): JobDepartment
    {
        $key = $this->normalizeKey($key);

        return DB::transaction(function () use ($key) {
            $row = $this->getRowsForUpdate($key);

            if (!$row) {
                logWarning("[JobSeatRepository] Increase On Notice Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            $row->on_notice_period += 1;
            $row->occupancy = $this->nz($row->occupancy - 1);
            $row->save();

            logInfo("[JobSeatRepository] Increase On Notice", [
                'key' => $key,
                'on_notice_period' => $row->on_notice_period,
                'occupancy' => $row->occupancy
            ]);

            return $row;
        });
    }

    public function revertIncreaseOnNotice(array $key): JobDepartment
    {
        $key = $this->normalizeKey($key);
        $allowed = $this->allowJobSeatCompany($key);

        return DB::transaction(function () use ($key, $allowed) {
            $row = $this->getRowsForUpdate($key);

            if (!$row) {
                logWarning("[JobSeatRepository] Revert Increase On Notice Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            if ($allowed && $row->isSeatFull) {
                logWarning("[JobSeatRepository] Revert Increase On Notice Failed: seat full", context: $key);
                throw new Exception("Job seat is full");
            }

            $row->on_notice_period = $this->nz($row->on_notice_period - 1);
            $row->occupancy = $row->occupancy + 1;
            $row->save();

            logInfo("[JobSeatRepository] Revert Increase On Notice", [
                'key' => $key,
                'on_notice_period' => $row->on_notice_period,
                'occupancy' => $row->occupancy
            ]);
            return $row;
        });
    }

    public function decreaseOnNotice(array $key): JobDepartment
    {
        $key = $this->normalizeKey($key);

        return DB::transaction(function () use ($key) {
            $row = $this->getRowsForUpdate($key);

            if (!$row) {
                logWarning("[JobSeatRepository] Decrease On Notice Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            $row->on_notice_period = $this->nz($row->on_notice_period - 1);
            $row->save();

            logInfo("[JobSeatRepository] Decrease On Notice", [
                'key' => $key,
                'on_notice_period' => $row->on_notice_period
            ]);

            return $row;
        });
    }

    public function restoreEmployee(array $key, bool $permanent = false): ?JobDepartment
    {
        $key = $this->normalizeKey($key);
        $allowed = $this->allowJobSeatCompany($key);

        return DB::transaction(function () use ($key, $allowed, $permanent) {
            $row = $this->getRowsForUpdate($key, !$allowed);

            if (!$row) {
                logWarning("[JobSeatRepository] Restore Employee Failed: not found", context: $key);
                throw new Exception("Job seat not found");
            }

            if ($permanent && $allowed && $row->isSeatFull) {
                logWarning("[JobSeatRepository] Restore Employee Failed: seat full", context: $key);
                throw new Exception("Job seat is full");
            }

            if ($permanent) $row->occupancy = $this->nz($row->occupancy + 1);
            else $row->on_notice_period = $row->on_notice_period + 1;
            $row->save();

            logInfo("[JobSeatRepository] Restore Employee", [
                'key' => $key,
                'occupancy' => $row->occupancy
            ]);

            return $row;
        });
    }

    public function moveOnRoleChange(array $fromKey, array $toKey)
    {
        $fromKey = $this->normalizeKey($fromKey);
        $toKey = $this->normalizeKey($toKey);

        if ($fromKey == $toKey) {
            logInfo("[JobSeatRepository] Move On Role Change: no change (same keys)");
            return;
        }

        $this->moveOnPayslipChange($fromKey, $toKey);
    }


    public function upsertFromSnapshot(array $key, int $occupancy, bool $seatEqualsOccupancy = false)
    {
        $key = $this->normalizeKey($key);

        return DB::transaction(function () use ($key, $occupancy, $seatEqualsOccupancy) {
            $row = $this->getRowsForUpdate($key, true);
            $row->occupancy = $this->nz($occupancy);
            if ($seatEqualsOccupancy) {
                $row->seat = $this->nz($occupancy);
            }

            $row->save();

            logInfo("[JobSeatRepository] Upsert From Snapshot", [
                'key' => $key,
                'occupancy' => $row->occupancy,
                'seat' => $row->seat
            ]);
        });
    }
}
