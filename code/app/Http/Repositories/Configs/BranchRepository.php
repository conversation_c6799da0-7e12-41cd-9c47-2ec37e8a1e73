<?php

namespace App\Http\Repositories\Configs;

use App\Http\Repositories\Repository;
use App\Models\configs\Branch;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;

class BranchRepository extends Repository {
    /**
     * change region_id of employee_org when region_id of branch is changed
     */
    public function changeRegionOfEmployee(Branch $branch = null, string | int $branchId = "") {
        if (!$branch && !$branchId) {
            throw new \InvalidArgumentException("Either branchId or branch must be specified");
        }
        if (!$branch && $branchId) {
            $branch = Branch::find($branchId);
        }

        $branchEmployees = EmployeeOrg::where('branch_id', $branch->id)->get();
        // dump($branchEmployees);
        foreach ($branchEmployees as $org) {
            $org->region_id = $branch->region_id;
            $org->save();
            // dump($org);
        }
        logInfo("Region changed for employees of branch " . $branch->name);
    }
}