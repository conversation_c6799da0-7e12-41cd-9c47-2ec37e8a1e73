<?php

namespace App\Http\Repositories;

use App\Jobs\LeaveBalanceJob;
use <PERSON>Bright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\EmployeeLeave;
use App\Models\Leaves\EmployeeLeaveDetail;
use App\Models\Leaves\LeaveOption;
use App\Models\Leaves\LeaveRequest;
use App\Models\Leaves\ReplacementLeaveDate;
use App\Models\Leaves\ReplacementLeaveRequest;
use App\Models\RequestTicket;
// use App\Models\Tickets\TicketTempOldDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use PermissionList;

class LeaveRepository extends Repository
{
    private int $fiscalYearId;
    private TicketRepository $ticketRepo;
    public function __construct($fiscalYearId = null)
    {
        $this->fiscalYearId = $fiscalYearId ?? currentFiscalYearId();
        $this->ticketRepo = new TicketRepository;
    }

    /**
     * Creates a new leave request and associated request ticket.
     *
     * This method performs the following tasks:
     * - Creates a new `LeaveRequest` model instance with the provided data.
     * - If the leave type is a replacement leave, it checks and updates the replacement leave dates.
     * - Creates a new `RequestTicket` model instance associated with the leave request.
     * - Sends a notification about the submitted leave request.
     *
     * @param array $data The data for the new leave request.
     * @return array A response array with a status and message.
     */
    public function createLeaveRequest(array $data, bool $sendNotification = true, $bypassFiscalYear = false)
    {
        \logInfo('Parameters for creating leave request: ', $data);

        if (!$bypassFiscalYear && !isWithinActiveFiscalYear($data['nep_start_date'], $data['nep_end_date'])) {
            \logError('Leave request is not within active fiscal year');
            return $this->errorResponse("Leave request is not within active fiscal year");
        }

        $leaveType = LeaveType::find($data['leave_type_id']);
        if (
            ($leaveType->name !== Constant::REPLACEMENT_LEAVE_NAME || !fedexHrm())
            && $leaveType->name !== Constant::UNPAID_LEAVE
            && $leaveType->name !== Constant::TRAVEL_LEAVE
        ) {
            $response = $this->checkRemainingLeave($data);
            if (!$response['status']) return $response;
            $employeeLeave = $response['data'];
        }

        if ($leaveType->name === Constant::TRAVEL_LEAVE) {
            $travelLeave = LeaveType::where('name', Constant::TRAVEL_LEAVE)->first();
            $employeeLeave = EmployeeLeave::where([
                ['fiscal_year_id', $this->fiscalYearId],
                ['employee_id', $data['employee_id']],
                ['leave_type_id', $travelLeave->id]
            ])->first();
            if (!$employeeLeave) {
                return $this->errorResponse("Travel Leave not assigned to employee");
            }
        }

        $leaveRequest = LeaveRequest::create($data);
        if ($leaveRequest->leaveType->name == Constant::REPLACEMENT_LEAVE_NAME && !fedexHrm()) {
            $response = $this->checkAndUpdateReplacementLeaveDate($leaveRequest);
            if (!$response['status']) return $response;
        }
        \logInfo("Leave Request Created: ", ['leave_request_id' => $leaveRequest?->id]);

        if ($leaveType->name === Constant::REPLACEMENT_LEAVE_NAME && fedexHrm()) {
            $this->assignLeave($leaveType, [$leaveRequest->employee_id], $leaveRequest->num_days);
            $employeeLeave = EmployeeLeave::where([
                'leave_type_id'     => $data['leave_type_id'],
                'fiscal_year_id'    => $data['fiscal_year_id'],
                'employee_id'       => $data['employee_id']
            ])->first();
            if (!$employeeLeave) return $this->errorResponse("Employee Leave not found for replacement leave");
        }

        if ($leaveType->name === Constant::UNPAID_LEAVE) {
            $this->assignLeave($leaveType, [$leaveRequest->employee_id], $leaveRequest->num_days);
            $employeeLeave = EmployeeLeave::where([
                'leave_type_id'     => $data['leave_type_id'],
                'fiscal_year_id'    => $data['fiscal_year_id'],
                'employee_id'       => $data['employee_id']
            ])->first();
            if (!$employeeLeave) return $this->errorResponse("Employee Leave not found for unpaid leave");
        }

        $employeeLeave->update(['pending_leave' => $employeeLeave->pending_leave + $leaveRequest->num_days]);

        $this->ticketRepo->createRequestTicket($leaveRequest, [
            'employee_id'       => $data['employee_id'],
            'current_owner_id'  => $data['verifier_id'],
            'documents'         => $data['documents']
        ], $sendNotification);

        return $this->responseHelper(true, "Leave Request Created Successfully", $leaveRequest);
    }

    /**
     * Assigns a new leave request and creates an associated request ticket.
     *
     * - Checks the remaining leave balance for the employee.
     * - Creates a new `LeaveRequest` model instance with the provided data.
     * - If the leave type is a replacement leave, it checks and updates the replacement leave dates.
     * - Transitions the leave request to the "Assigned" state.
     * - Creates a new `RequestTicket` model instance associated with the leave request.
     * - Approves the leave request.
     *
     * @param array $data The data for the new leave request.
     * @return array A response array with a status and message.
     */
    public function assignLeaveRequest(array $data, $bypassFiscalYear = false)
    {
        \logInfo('Parameters for assigning leave request: ', $data);
        if (!$bypassFiscalYear && !isWithinActiveFiscalYear($data['nep_start_date'], $data['nep_end_date'])) {
            \logError('Leave request is not within active fiscal year');
            return $this->errorResponse("Leave request is not within active fiscal year");
        }
        $leaveType = LeaveType::find($data['leave_type_id']);
        if (
            ($leaveType->name !== Constant::REPLACEMENT_LEAVE_NAME || !fedexHrm())
            && $leaveType->name !== Constant::UNPAID_LEAVE
            && $leaveType->name !== Constant::TRAVEL_LEAVE
        ) {
            $response = $this->checkRemainingLeave($data);
            if (!$response['status']) return $response;
            $employeeLeave = $response['data'];
        }

        if ($leaveType->name === Constant::TRAVEL_LEAVE) {
            $travelLeave = LeaveType::where('name', Constant::TRAVEL_LEAVE)->first();
            $employeeLeave = EmployeeLeave::where([
                ['fiscal_year_id', $this->fiscalYearId],
                ['employee_id', $data['employee_id']],
                ['leave_type_id', $travelLeave->id]
            ])->first();
            if (!$employeeLeave) {
                return $this->errorResponse("Travel Leave not assigned to employee");
            }
        }

        $leaveRequest = LeaveRequest::create($data);
        if ($leaveRequest->leaveType->name == Constant::REPLACEMENT_LEAVE_NAME && !fedexHrm()) {
            $response = $this->checkAndUpdateReplacementLeaveDate($leaveRequest);
            if (!$response['status']) return $response;
        }
        \logInfo("Leave Request Created: ", ['leave_request_id' => $leaveRequest?->id]);

        if ($leaveType->name === Constant::REPLACEMENT_LEAVE_NAME && fedexHrm()) {
            $this->assignLeave($leaveType, [$leaveRequest->employee_id], $leaveRequest->num_days);
            $employeeLeave = EmployeeLeave::where([
                'leave_type_id'     => $data['leave_type_id'],
                'fiscal_year_id'    => $data['fiscal_year_id'],
                'employee_id'       => $data['employee_id']
            ])->first();
            if (!$employeeLeave) return $this->errorResponse("Employee Leave not found for replacement leave");
        }

        if ($leaveType->name === Constant::UNPAID_LEAVE) {
            $this->assignLeave($leaveType, [$leaveRequest->employee_id], $leaveRequest->num_days);
            $employeeLeave = EmployeeLeave::where([
                'leave_type_id'     => $data['leave_type_id'],
                'fiscal_year_id'    => $data['fiscal_year_id'],
                'employee_id'       => $data['employee_id']
            ])->first();
            if (!$employeeLeave) return $this->errorResponse("Employee Leave not found for unpaid leave");
        }

        $employeeLeave->update(['pending_leave' => $employeeLeave->pending_leave + $leaveRequest->num_days]);

        $response = $this->ticketRepo->createRequestTicket($leaveRequest, [
            'employee_id'       => $data['employee_id'],
            'current_owner_id'  => null,
            'documents'         => $data['documents']
        ], false);
        $requestTicket = $response['data'];

        $transitionsList = $leaveRequest->transitionPerformers()->where([
            ['recipient_id', $leaveRequest->employee_id],
            ['performer_id', currentEmployee()->id]
        ])->get();

        $isApprover = count($transitionsList->filter(fn($transition) => $transition->state === WorkflowPerformer::APPROVER));
        $canAssignScope = auth()->user()->can(PermissionList::LEAVE_REQUEST_ASSIGN_SCOPE);

        if ($isApprover || $canAssignScope) {
            $leaveRequest->transitionTo(
                WorkflowState::ASSIGNED,
                "",
                metadata: [
                    'initiator_id'    => '',
                    'initiator_role'  => '',
                    'next_owner_id'   => '',
                    'next_owner_role' => '',
                ]
            );
            $requestTicket->state = WorkflowState::ASSIGNED;
            $requestTicket->current_owner_role = null;
            $requestTicket->save();

            $response = $this->approveLeaveRequest($leaveRequest, $requestTicket);
            if (!$response['status']) {
                return $response;
            }
            \logInfo("Leave Request Transitioned to assigned");
        } else if (count($transitionsList)) {
            if (!$data['verifier_id']) return $this->errorResponse("Verifier not found");
            $requestTicket->state = WorkflowState::ALLOCATED;
            $requestTicket->current_owner_id = $data['verifier_id'];
            $requestTicket->current_owner_role = 'Approver';
            $requestTicket->verification_level += 1;
            $requestTicket->save();
            $leaveRequest->transitionTo(
                WorkflowState::ALLOCATED,
                "Assigned Leave Request",
                metadata: [
                    'initiator_id'    => currentEmployee()?->id,
                    'initiator_role'  => WorkflowPerformer::VERIFIER,
                    'verifier_id'   => $data['verifier_id'],
                    'next_owner_role' => WorkflowPerformer::APPROVER,
                ]
            );

            \logInfo("Leave Request Transitioned to verified and sent to next owner");
        } else {
            return $this->errorResponse("You don't have permission to assign leave to this user");
        }

        return $this->responseHelper(
            true,
            "Leave Request Assigned Successfully",
            ['model_id' => $leaveRequest->id, 'workflow' => $leaveRequest->workflow]
        );
    }

    /**
     * Updates an existing leave request and associated request ticket.
     *
     * - Updates the `LeaveRequest` model instance with the provided data.
     * - If the leave type is a replacement leave, it checks and updates the replacement leave dates.
     * - Updates the associated `RequestTicket` model instance with the new verifier ID.
     * - Sends a notification about the updated leave request if the verifier has changed.
     *
     * @param LeaveRequest $request The leave request to be updated.
     * @param array $data The updated data for the leave request.
     * @return array A response array with a status and message.
     */
    public function updateLeaveRequest(LeaveRequest $request, array $data)
    {
        \logInfo('Updating leave request with ID: ' . $request->id);

        if (!isWithinActiveFiscalYear($data['nep_start_date'], $data['nep_end_date'])) {
            \logError('Leave request is not within active fiscal year');
            return $this->errorResponse("Leave request is not within active fiscal year");
        }

        $leaveType = LeaveType::find($data['leave_type_id']);
        if (
            ($leaveType->name !== Constant::REPLACEMENT_LEAVE_NAME || !fedexHrm())
            && $leaveType->name !== Constant::UNPAID_LEAVE
            && $leaveType->name !== Constant::TRAVEL_LEAVE
        ) {
            $response = $this->checkRemainingLeave($data, $request->num_days);
            if (!$response['status']) return $response;

            $employeeLeave = $response['data'];
            // for leave balance of pending leave, first decrease the pending leave from initial num days
            $pendingLeave = $employeeLeave->pending_leave - $request->num_days;
            // add the updated number of days to pending leave
            $employeeLeave->update(['pending_leave' => $pendingLeave + $request->num_days]);
        }

        if ($request->leaveType->name == Constant::REPLACEMENT_LEAVE_NAME && !fedexHrm()) {
            $response = $this->checkAndUpdateReplacementLeaveDate($request, "Pending", "Not Used");
            if (!$response['status']) return $response;
        }

        \logInfo('Parameters for updating leave request: ', $data);

        // num day might be updated
        $request->update($data);
        if ($request->leaveType->name == Constant::REPLACEMENT_LEAVE_NAME && !fedexHrm()) {
            $response = $this->checkAndUpdateReplacementLeaveDate($request);
            if (!$response['status']) return $response;
        }
        \logInfo("Leave Request Updated", ['leave_request_id' => $request?->id]);


        $this->ticketRepo->updateRequestTicket($request, [
            'current_owner_id'      => $data['verifier_id'],
            'documents'             => $data['documents'],
            'removing_document_ids' => $data['removing_document_ids'] ?? []
        ]);
        return $this->responseHelper(true, "Leave Request Updated Successfully");
    }

    /**
     * Approves a leave request and updates the associated employee leave details.
     *
     * - Fetches the employee leave record for the given leave request.
     * - Updates the attendance records within the leave period to reflect the approved leave.
     * - Updates the employee leave record with the new leave taken, excess leave, and remaining days.
     * - Adds the employee leave detail for the approved leave request.
     *
     * @param LeaveRequest $request The leave request to be approved.
     * @return array A response array with a status and message.
     */
    public function approveLeaveRequest(LeaveRequest $request, RequestTicket $ticket = null)
    {
        $oldTempData = [];
        \logInfo("Updating leave detail of employee of id: {$request->employee_id}");

        $employeeLeave = EmployeeLeave::where('employee_id', $request->employee_id)
            ->where('leave_type_id', $request->leave_type_id)
            ->where('fiscal_year_id', $request->fiscal_year_id)
            ->first();

        if (!$employeeLeave) return $this->errorResponse("Assigned leave has been removed");
        $oldTempData['employeeLeave'] = $employeeLeave->toArray();

        // Fetch all attendance records within the leave period
        $attendanceRecords = Attendance::where('employee_id', $request->employee_id)
            ->whereBetween('date_en', [$request->start_date, $request->end_date])
            ->get();

        $oldTempData['attendanceRecords'] = $attendanceRecords->each(fn($record) => $record->toArray())->toArray();

        // Update attendance records and leave details
        foreach ($attendanceRecords as $attendance) {
            $attendance->update([
                'remarks'           => $request->remarks,
                'status'            => "{$request->leaveType?->name} [{$request->leaveOption?->name}]",
                "leave_status"      => $request->leaveType?->paid ? true : false,
                "leave_request_id"  => $request->id
            ]);
        }

        $employeeLeave->leave_taken += $request->num_days;
        $employeeLeave->excess_leave = max(0.0, $employeeLeave->leave_taken - $employeeLeave->assigned_leave);
        $employeeLeave->remaining_days = max(0.0, $employeeLeave->assigned_leave - $employeeLeave->leave_taken);
        $employeeLeave->save();

        \logInfo("Leave Detail updated", $employeeLeave->toArray());
        $response = $this->addEmployeeLeaveDetail($request);
        if (!$response['status']) return $response;
        return $this->responseHelper(true);
    }

    /**
     * Applies a replacement leave request for an employee.
     *
     * - Checks if a replacement leave type exists, and creates it if not.
     * - Creates replacement leave date records for each day in the requested leave period.
     * - Assigns the requested number of replacement leave days to the employee.
     *
     * @param ReplacementLeaveRequest $request The replacement leave request to be processed.
     * @return array A response array with a status and message.
     */
    public function applyReplacementLeave(ReplacementLeaveRequest $request)
    {
        $leaveType = LeaveType::where('name', Constant::REPLACEMENT_LEAVE_NAME)->first();
        if (!$leaveType) {
            $leaveType = LeaveType::create([
                "name"          => Constant::REPLACEMENT_LEAVE_NAME,
                "paid"          => true,
                "default_days"  => 0,
                "is_active"     => true,
            ]);
            \logInfo("Replacement Leave not found, replacement leave created");
        }

        $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
        $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);
        for ($date = $start_date; $date->lte($end_date); $date->addDay()) {
            ReplacementLeaveDate::create([
                "fiscal_year_id"                => $this->fiscalYearId,
                "employee_id"                   => $request->employee_id,
                "leave_option_id"               => $request->leave_option_id,
                "date"                          => $date,
                "replacement_leave_request_id"  => $request->id,
            ]);
        }
        \logInfo("Replacement leave days added from $request->start_date to $request->end_date");

        $attendance = Attendance::where('employee_id', $request->employee_id)
            ->where('date_en', $request->start_date)
            ->first();
        if ($attendance) {
            $attendance->remarks = "Replacement Leave Claimed";
            $attendance->save();
        }
        $this->assignLeave($leaveType, [$request->employee_id], $request->num_days);

        return $this->responseHelper(true, "Replacement Leave Assigned Successfully");
    }

    /**
     * Applies a replacement leave request for an employee.
     * directly create leave request for employee
     *
     * @param ReplacementLeaveRequest $request The replacement leave request to be processed.
     * @return array A response array with a status and message.
     */
    public function applyDirectReplacementLeave(ReplacementLeaveRequest $request)
    {
        $leaveType = LeaveType::where('name', Constant::REPLACEMENT_LEAVE_NAME)->first();
        if (!$leaveType) {
            $leaveType = LeaveType::create([
                "name"          => Constant::REPLACEMENT_LEAVE_NAME,
                "paid"          => true,
                "default_days"  => 0,
                "is_active"     => true,
            ]);
            \logInfo("Replacement Leave not found, replacement leave created");
        }

        $this->addEmployeeLeaveDetail($request);

        return $this->responseHelper(true, "Replacement Leave Assigned Successfully");
    }

    /**
     * Assigns a specified leave type to a list of employees.
     *
     * This method checks if the leave type already exists for the given employees and fiscal year.
     * If it does, it updates the existing pivot entry with the new leave details.
     * If not, it attaches a new pivot entry for the employee and leave type.
     *
     * @param LeaveType $leaveType The leave type to be assigned.
     * @param array $employeeIds The IDs of the employees to assign the leave to.
     * @param int $dayCount The number of leave days to assign (optional, defaults to 0).
     * @return array A response array with a status and message.
     */
    public function assignLeave(LeaveType $leaveType, array $employeeIds, float $dayCount = 0)
    {
        \logInfo("Assigning leave $leaveType->name to employees", $employeeIds, true);
        $isReplacementLeave = $leaveType->name == Constant::REPLACEMENT_LEAVE_NAME;
        // $assignedLeaves = $isReplacementLeave ?  $dayCount : $leaveType->default_days;
        $expiryDate = $leaveType->expiry_date_count ?
            Carbon::now()->addDays($leaveType->expiry_day_count)->toDateString()
            : null;

        $existingPivots = EmployeeLeave::whereIn('employee_id', $employeeIds)
            ->where('fiscal_year_id', $this->fiscalYearId)
            ->where('leave_type_id', $leaveType->id)
            ->get()
            ->keyBy('employee_id');
        $employeeDoj = EmployeeOrg::select('employee_id', 'doj')->get()->keyBy('employee_id');
        $fiscalYear = FiscalYear::where('id', $this->fiscalYearId)->first();
        $start_date = LaravelNepaliDate::from($fiscalYear->start_date)->toEnglishDate("Y-m-d");
        $end_date = LaravelNepaliDate::from($fiscalYear->end_date)->toEnglishDate("Y-m-d");

        $leaveDays = $isReplacementLeave ?  $dayCount : $leaveType->default_days;
        $assigned = [];
        $alreadyAssigned = [];
        $firstEmployee = null;

        foreach ($employeeIds as $employeeId) {
            $employee = Employee::find($employeeId);
            $assignedLeaves = $leaveDays;
            if (!$isReplacementLeave) {
                if ($employeeDoj[$employeeId]->doj >= $end_date) {
                    continue;
                }

                if (!$leaveType->ignore_joining_date) {
                    // Calculate leave for employee joining in the middle of fiscal year.
                    if ($employeeDoj[$employeeId]->doj >= $start_date && $employeeDoj[$employeeId]->doj <= $end_date) {
                        $month = LaravelNepaliDate::from($employeeDoj[$employeeId]->doj)->toNepaliDate("m");
                        $remaining_month = ((15 - (int)$month) % 12) + 1; // Balance the leave by adding 1 day.
                        $assignedLeaves *= $remaining_month / 12;
                    }
                }
            }

            $existingPivot = $existingPivots->get($employeeId);
            $isProRataOrMonthly = $leaveType->pro_rata_basis || $leaveType->monthly;

            if (!$existingPivot) {
                $pivotValues = [
                    'assigned_leave'    => $assignedLeaves,
                    'leave_taken'       => 0,
                    'remaining_days'    => $assignedLeaves,
                    'excess_leave'      => 0,
                    'pending_leave'     => 0,
                    'fiscal_year_id'    => $this->fiscalYearId,
                    'expiry_date'       => $expiryDate
                ];
                if ($isProRataOrMonthly) {
                    $pivotValues['assigned_leave'] = $leaveType->default_days;
                    $pivotValues['remaining_days'] = $leaveType->default_days;
                }
                $leaveType->employees()
                    ->attach($employeeId, $pivotValues);
                if ($firstEmployee === null) {
                    $firstEmployee = $employee->name;
                }
                $assigned[] = "Leave type '{$leaveType->name}' assigned to employee {$employee->name}.";
            } else {
                $pivotValues = [
                    'assigned_leave'    => $assignedLeaves,
                    'remaining_days'    => $assignedLeaves,
                    'expiry_date'       => $expiryDate
                ];
                if ($isReplacementLeave) {
                    $pivotValues['assigned_leave'] += $existingPivot->assigned_leave ?? 0;
                    $pivotValues['remaining_days'] += $existingPivot->remaining_days ?? 0;
                    if ($firstEmployee === null) {
                        $firstEmployee = $employee->name;
                    }
                    $alreadyAssigned[] = "Leave type '{$leaveType->name}' is already assigned to employee {$employee->name}.";
                } elseif ($isProRataOrMonthly) {
                    $pivotValues['assigned_leave'] = $existingPivot->assigned_leave;
                    $pivotValues['remaining_days'] = $existingPivot->remaining_days;
                    if ($firstEmployee === null) {
                        $firstEmployee = $employee->name;
                    }
                    $alreadyAssigned[] = "Leave type '{$leaveType->name}' is already assigned to employee {$employee->name}.";
                } else {
                    $alreadyAssigned[] = "Leave type '{$leaveType->name}' is already assigned to employee {$employee->name}.";
                    continue;
                }
                $existingPivot->update($pivotValues);
                $assigned[] = "Leave type '{$leaveType->name}' is already assigned to this Employee {$employee->name}.";
            }
        }
        dispatch(new LeaveBalanceJob(
            $employeeIds,
            $this->fiscalYearId
        ));

        \logInfo("Assigned leave $leaveType->name to employees", [$employeeIds]);

        if (!empty($alreadyAssigned)) {
            $firstAssignedMessage = $firstEmployee ? "Leave type '{$leaveType->name}' is already assigned to employee {$firstEmployee} and others." : "";
            $message = count($alreadyAssigned) > 1 ? $firstAssignedMessage : implode(' ', $alreadyAssigned);
            return $this->responseHelper(false, $message);
        } elseif (!empty($assigned)) {
            $firstAssignedMessage = $firstEmployee ? "Leave type '{$leaveType->name}' is assigned to employee {$firstEmployee} and others." : "";
            $message = count($assigned) > 1 ? $firstAssignedMessage : implode(' ', $assigned);
            return $this->responseHelper(true, $message);
        } else {
            return $this->responseHelper(true, "No leaves were assigned.");
        }
    }

    public function unassignLeaves(int | string $fiscalYearId, array $leaveTypeIds, array $employeeIds)
    {
        $leaveTypes = LeaveType::whereIn('id', $leaveTypeIds)->get();
        foreach ($leaveTypes as $leaveType) {
            $leaveType->employees()->where('fiscal_year_id', $fiscalYearId)->detach($employeeIds);
        }
        return $this->successResponse("Leave type unassigned successfully");
    }

    /**
     * Adds the leave details for an employee based on the provided leave request.
     *
     * This method creates an EmployeeLeaveDetail record for each day between the start and end dates of the leave request.
     * If the leave type is a Replacement Leave, it also updates the status of the corresponding ReplacementLeaveDate record.
     *
     * @param LeaveRequest|ReplacementLeaveRequest $request The leave request containing the details to be added.
     * @return array A response array with a status and message.
     */
    private function addEmployeeLeaveDetail(LeaveRequest|ReplacementLeaveRequest $request)
    {
        \logInfo("Adding leave detail of the employee of leaveRequest: ");
        $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
        $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);

        $replacedDate = null;
        if (fedexHrm() && $request->replaced_start_date) {
            $replacedDate = Carbon::createFromFormat('Y-m-d', $request->replaced_start_date);
        }
        for ($date = $start_date; $date->lte($end_date); $date->addDay()) {
            $replacedDateItem = null;
            if (!fedexHrm()) {
                if ($request->leaveType->name == Constant::REPLACEMENT_LEAVE_NAME) {
                    $replacedDateItem = ReplacementLeaveDate::where([
                        ['fiscal_year_id', $request->fiscal_year_id],
                        ['employee_id', $request->employee_id],
                        ['leave_option_id', $request->leaveOption->id],
                        ['status', "Pending"]
                    ])->first();
                    if (!$replacedDateItem) {
                        return $this->responseHelper(false, "Replacement Leave not available for {$request->leaveOption->name}");
                    }
                }
            }

            $nepaliDate = LaravelNepaliDate::from($date)->toNepaliDate();

            EmployeeLeaveDetail::create([
                "employee_id"       => $request->employee_id,
                "date"              => $date,
                "nep_date"              => $nepaliDate,
                "fiscal_year_id"    => $this->fiscalYearId,
                "leave_type_id"     => $request->leaveType->id,
                "leave_option_id"   => $request->leave_option_id,
                "num_days"          => $request->leaveOption->num_days,
                "remarks"           => $request->remarks,
                "replaced_date"     => fedexHrm() ? $replacedDate : $replacedDateItem?->date,
                "leave_request_id"  => $request->id,
                "performers"        => [
                    "verified_by" => $request->getPerformer(WorkflowState::VERIFIED)?->name,
                    "approved_by" => $request->getPerformer(WorkflowState::APPROVED)?->name ?? $request->getPerformer(WorkflowState::ASSIGNED)?->name,
                ]
            ]);

            if ($replacedDateItem) {
                $replacedDateItem->status = "Used";
                $replacedDateItem->save();
            }
            $replacedDate?->addDay();
        }

        \logInfo("Employee Leave Detail Added");
        return $this->responseHelper(true, "Employee Leave Detail Added");
    }

    /**
     * This method checks and updates the status of the corresponding ReplacementLeaveDate record
     * for each day between the start and end dates of the leave request.
     *
     * @param LeaveRequest $request The leave request containing the details to be updated.
     * @param string $checkStatus The status to check for the ReplacementLeaveDate record (default is "Not Used").
     * @param string $status The new status to set for the ReplacementLeaveDate record (default is "Pending").
     * @return array A response array with a status and message.
     */
    private function checkAndUpdateReplacementLeaveDate(
        LeaveRequest $request,
        string $checkStatus = "Not Used",
        string $status = "Pending"
    ) {
        $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date);
        $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date);
        for ($date = $start_date; $date->lte($end_date); $date->addDay()) {
            $replacedDateItem = null;
            $replacedDateItem = ReplacementLeaveDate::where([
                ['fiscal_year_id', $request->fiscal_year_id],
                ['employee_id', $request->employee_id],
                ['leave_option_id', $request->leaveOption->id],
                ['status', $checkStatus]
            ])->first();
            if (!$replacedDateItem) {
                return $this->responseHelper(false, "Replacement Leave not available for {$request->leaveOption->name}");
            }

            $replacedDateItem->status = $status;
            $replacedDateItem->leave_request_id = $request->id;
            $replacedDateItem->save();
        }
        return $this->responseHelper(true, "Replacement Dates updated successfully");
    }

    /**
     * Checks the remaining leave days for an employee based on the provided data.
     *
     * @param array $data An associative array containing the following keys:
     *                   - 'leave_type_id': The ID of the leave type.
     *                   - 'fiscal_year_id': The ID of the fiscal year.
     *                   - 'employee_id': The ID of the employee.
     * @param int $existingDays The number of existing leave days to subtract from the total.
     * @return array A response array with a boolean 'success' key and an optional 'message' key.
     */
    public function checkRemainingLeave(array $data, int $existingDays = 0)
    {
        $employeeLeave = EmployeeLeave::where([
            'leave_type_id'     => $data['leave_type_id'],
            'fiscal_year_id'    => $data['fiscal_year_id'],
            'employee_id'       => $data['employee_id']
        ])->first();
        if (!$employeeLeave) return $this->responseHelper(false, "Employee Leave not found");

        $numDays = $data['num_days'] - $existingDays;
        logInfo("Employee Leave Details", $employeeLeave->toArray());
        if ($numDays > $employeeLeave->remaining_days - $employeeLeave->pending_leave) {
            logError("Employee don't have enough remaining days to take leave", context: [
                'employeeLeave' => $employeeLeave
            ]);
            return $this->responseHelper(
                false,
                "You don't have enough remaining days to take leave",
                data: $employeeLeave
            );
        }

        return $this->responseHelper(true, data: $employeeLeave);
    }

    /**
     * Adds monthly leave entitlements to employee leave records.
     *
     * This method retrieves the current active fiscal year, and then finds all leave types that are either
     * pro-rata or monthly. It then updates the assigned leave and remaining days for each employee leave
     * record that matches those leave types and the current fiscal year.
     */
    public function addMonthlyLeave()
    {
        try {
            \logCronInfo("Adding monthly leave");
            $currentFiscalYearId = FiscalYear::where('is_active', true)->first()?->id;
            if (!$currentFiscalYearId) {
                \logCronError("Active Fiscal Year not found");
                return;
            }

            $monthlyLeaveTypes = LeaveType::where(function ($query) {
                $query->where('pro_rata_basis', true)
                    ->orWhere('monthly', true);
            })->get();
            \logCronInfo(count($monthlyLeaveTypes) . " pro rata leave found");

            echo "Pro rata or monthly leaves:\n";
            foreach ($monthlyLeaveTypes as $monthlyLeave) {
                echo "$monthlyLeave->id: $monthlyLeave->name ($monthlyLeave->default_days)\n";
            }
            echo "\n\n";

            foreach ($monthlyLeaveTypes as $monthlyLeave) {
                $employeeLeaves = EmployeeLeave::where('fiscal_year_id', $currentFiscalYearId)
                    ->where('leave_type_id', $monthlyLeave->id)->get();
                foreach ($employeeLeaves as $employeeLeave) {
                    echo "Updating employee leave details for employee_id: $employeeLeave->employee_id with leave type: $monthlyLeave->name\n";
                    $employeeLeave->update([
                        'assigned_leave'    => $employeeLeave->assigned_leave + $monthlyLeave->default_days,
                        'remaining_days'    => $employeeLeave->remaining_days + $monthlyLeave->default_days,
                    ]);
                }
            }
            \logCronInfo("Pro rata leave added to assigned employee successfully\n\n");
        } catch (\Exception $e) {
            logCronError("Error while adding monthly leave", $e);
        }
    }

    /**
     * Reverts a leave request for an employee.
     *
     * This method updates the attendance records and employee leave details for the specified leave request.
     * It updates the remarks and status of the attendance records, decrements the leave taken by the employee,
     * and updates the remaining leave days. It also updates the status of any replacement leave dates associated
     * with the leave request.
     *
     * @param LeaveRequest $request The leave request to be reverted.
     * @return array The response data.
     */
    public function revertLeaveRequest(LeaveRequest $request)
    {
        \logInfo("Reverting leave detail of employee of id: {$request->employee_id} and request id $request->id");

        $employeeLeave = EmployeeLeave::where('employee_id', $request->employee_id)
            ->where('leave_type_id', $request->leave_type_id)
            ->where('fiscal_year_id', $request->fiscal_year_id)
            ->first();

        $attendanceRecords = Attendance::where('employee_id', $request->employee_id)
            ->whereBetween('date_en', [$request->start_date, $request->end_date])
            ->get();

        // Update attendance records and leave details
        foreach ($attendanceRecords as $attendance) {
            $attendance->update([
                'remarks'       => "$attendance->remarks [Reverted]",
                'status'        => "$attendance->status [Reverted]",
                "leave_status"  => false,
            ]);
        }

        if ($employeeLeave) {
            $employeeLeave->leave_taken -= $request->num_days;
            $employeeLeave->remaining_days = max(0.0, $employeeLeave->assigned_leave - $employeeLeave->leave_taken);
            $employeeLeave->save();
            \logInfo("Leave Detail Reverted", $employeeLeave->toArray());
        } else {
            \logInfo("Leave Detail Not found while reverting");
        }

        if (!fedexHrm()) {
            $replacedDates = EmployeeLeaveDetail::where('leave_request_id', $request->id)
                ->whereNotNull('replaced_date')
                ->pluck('replaced_date')
                ->toArray();
            if (count($replacedDates)) {
                ReplacementLeaveDate::where('employee_id', $request->employee_id)
                    ->whereIn('date', $replacedDates)->update([
                        "status" => "Not Used",
                        "leave_request_id" => null
                    ]);
                \logInfo("Replacement Dates updated to not used");
            }
        }

        EmployeeLeaveDetail::where('leave_request_id', $request->id)->delete();

        $syncRepo = new SyncRepository();
        foreach ($attendanceRecords as $attendance) {
            $syncRepo->syncAttendanceFromDeviceToDb($attendance->date_en, $request->employee_id);
        }

        return $this->responseHelper(true);
    }

    public function myLeaveDetails($params)
    {
        $this->fiscalYearId = \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first();
        $employee = Employee::find($params['employeeId']);
        return $this->getLeaveBalance($employee);
    }

    public function getLeaveBalance(Employee|null $employee, $fiscalYearId = null)
    {
        $fiscalYearId ??= $this->fiscalYearId;
        return $employee?->assignedLeaves
            ->when(fedexHrm(), fn($query) => $query->where('name', '!=', Constant::REPLACEMENT_LEAVE_NAME))
            ->whereNotIn('name', [Constant::UNPAID_LEAVE, Constant::TRAVEL_LEAVE])
            ->where("pivot.fiscal_year_id", $fiscalYearId)
            ->map(fn($leave) => [
                'id' => $leave->id,
                'name' => $leave->name,
                'assigned_leave' => $leave->pivot->assigned_leave,
                'leave_taken' => $leave->pivot->leave_taken,
                'pending_leave' => $leave->pivot->pending_leave,
                'remaining_leave' => ($leave->pivot->assigned_leave - $leave->pivot->pending_leave - $leave->pivot->leave_taken)
            ]);
    }

    public function getLeaveCount(Employee| null $employee)
    {
        if (!$employee) {
            return 0;
        }

        $currentDate = now();
        $dateFrom30DaysAgo = now()->subDays(30);
        return LeaveRequest::query()
            ->leftJoin('leave_options as leaveOption', 'leaveOption.id', 'leave_requests.leave_option_id')
            ->where('employee_id', $employee->id)
            ->where('state', 'Approved')
            ->where('end_date', '>=', $dateFrom30DaysAgo)
            ->where('start_date', '<=', $currentDate)
            ->select('leave_requests.*', 'leaveOption.num_days as leave_num_days')
            ->get()
            ->sum(function ($leaveRequest) use ($currentDate, $dateFrom30DaysAgo) {
                // Calculate the overlap between the leave period and the last 30 days
                $countStartDate = Carbon::parse($leaveRequest->start_date)->max($dateFrom30DaysAgo);
                $countEndDate = Carbon::parse($leaveRequest->end_date)->min($currentDate);

                // Check if there's any overlap
                if ($countStartDate->lte($countEndDate)) {
                    // Calculate the number of days in the overlapping period
                    $countDays = $countStartDate->diffInDays($countEndDate) + 1;

                    // Calculate total leave days by multiplying the count with the leave option's number of days
                    return $leaveRequest->leave_num_days * $countDays;
                }

                return 0;
            });
    }

    /**
     * Recalculates the leave balance for the given employee id and fiscal year id.
     * It fetches all leave requests for the given employee and fiscal year,
     * then sums up the approved and pending leave days and updates the employee leave record.
     * If there's an error, it rolls back and returns an error response.
     * @param int $employeeId The employee id.
     * @param int $fiscalYearId The fiscal year id.
     * @return array A response array with a boolean 'success' key and an optional 'message' key.
     */
    public function recalculateLeaveBalance($employeeId, $fiscalYearId)
    {
        $employeeLeaves = EmployeeLeave::where([
            'employee_id' => $employeeId,
            'fiscal_year_id' => $fiscalYearId
        ])->get();
        try {
            // dd($employeeLeaves, $employeeId, $fiscalYearId);
            DB::beginTransaction();
            foreach ($employeeLeaves as $employeeLeave) {
                $leaveRequests = LeaveRequest::where([
                    'employee_id' => $employeeId,
                    'leave_type_id' => $employeeLeave->leave_type_id,
                    'fiscal_year_id' => $fiscalYearId,
                ])->get();

                $pendingLeaveRequest = $leaveRequests->filter(function ($request) {
                    return !in_array($request->state, ArflowHelper::getFinalStates(WorkflowName::LEAVE_APPROVAL));
                });

                $approvedLeaveRequest = $leaveRequests->filter(function ($request) {
                    return in_array($request->state, ArflowHelper::getSuccessFinalStates(WorkflowName::LEAVE_APPROVAL));
                });

                $employeeLeave->leave_taken = $approvedLeaveRequest->sum('num_days');
                $employeeLeave->pending_leave = $pendingLeaveRequest->sum('num_days');
                $employeeLeave->remaining_days = $employeeLeave->assigned_leave - $employeeLeave->leave_taken;
                $employeeLeave->save();
            }
            DB::commit();
            return $this->successResponse("Leave Balance recalculated");
        } catch (\Exception $e) {
            // dd($e);
            \logError("Error while recalculating leave balance", $e);
            DB::rollBack();
            return $this->errorResponse("Error while recalculating leave balance");
        }
        // dd('test');
    }

    public function fiscalYearList()
    {
        return FiscalYear::pluck('name', 'id')->toArray();
    }

    public function leaveTypeList()
    {
        return LeaveType::where('is_active', 1)->get();
    }

    public function stateList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::LEAVE_APPROVAL);
    }

    public function leaveOptions($employeeId, $leaveTypeName = null)
    {
        if ($leaveTypeName == Constant::REPLACEMENT_LEAVE_NAME) {
            if (fedexHrm()) {
                return LeaveOption::list(true);
            } else {
                $leaveOptionIds = ReplacementLeaveDate::where([
                    ['employee_id', $employeeId],
                    ['status', 'Not Used']
                ])->select('leave_option_id')->distinct()->pluck('leave_option_id')->toArray();
                return LeaveOption::whereIn('id', $leaveOptionIds)
                    ->where('is_active', true)->pluck('name', 'id')->toArray();
            }
        }
        return LeaveOption::list();
    }

    public function verifiers($params)
    {
        return (new LeaveRequest)->getNextOwners(employeeId: $params['employee_id'], isSubmitting: $params['is_submitting']);
    }

    public function assignedLeaves($params)
    {
        if ($params['employee_id']) {
            $employeeLeaves = DB::table("employee_leaves")
                ->leftJoin("leave_types as leave", "leave.id", "=", "employee_leaves.leave_type_id")
                ->where([
                    ["employee_id", $params['employee_id']],
                    ["employee_leaves.fiscal_year_id", $params['fiscal_year_id']],
                    ["leave.is_active", true],
                    ["employee_leaves.remaining_days", ">", 0]
                ])
                ->whereColumn('employee_leaves.remaining_days', "!=", 'employee_leaves.pending_leave')
                ->where(function ($query) {
                    $query->whereNull("employee_leaves.expiry_date")
                        ->orWhere("employee_leaves.expiry_date", ">=", date("Y-m-d"));
                })
                ->whereNull('leave.deleted_at')
                ->select(
                    "leave.id",
                    "leave.name",
                    "employee_leaves.assigned_leave as assigned_leave",
                    "employee_leaves.leave_taken",
                )->get();
            if (fedexHrm()) {
                $hasReplacementLeave = $employeeLeaves->filter(fn($employeeLeave) => $employeeLeave->name === Constant::REPLACEMENT_LEAVE_NAME)->first();
                $employeeLeaves->toArray();
                if (!$hasReplacementLeave) {
                    $replacementLeaveType = LeaveType::where('name', Constant::REPLACEMENT_LEAVE_NAME)->first();
                    if ($replacementLeaveType) {
                        $employeeLeaves->push((object)([
                            'id' => $replacementLeaveType->id,
                            'name' => $replacementLeaveType->name,
                            'assigned_leave' => 0,
                            'leave_taken' => 0,
                        ]));
                    }
                }
            } else {
                $employeeLeaves->toArray();
            }

            $unpaidLeave = LeaveType::where('name', Constant::UNPAID_LEAVE)->first();
            if ($unpaidLeave) {
                $employeeLeaves->push((object)([
                    'id' => $unpaidLeave->id,
                    'name' => $unpaidLeave->name,
                    'assigned_leave' => 0,
                    'leave_taken' => 0,
                ]));
            }

            $travelLeave = LeaveType::where('name', Constant::TRAVEL_LEAVE)->first();
            if ($travelLeave) {
                $travelLeaveAssigned = EmployeeLeave::where([
                    ['fiscal_year_id', $params['fiscal_year_id']],
                    ['employee_id', $params['employee_id']],
                    ['leave_type_id', $travelLeave->id]
                ])->exists();
                if ($travelLeaveAssigned) {
                    $employeeLeaves->push((object)([
                        'id' => $travelLeave->id,
                        'name' => $travelLeave->name,
                        'assigned_leave' => 0,
                        'leave_taken' => 0,
                    ]));
                }
            }
            return $employeeLeaves;
        }
        return [];
    }

    public function activeFiscalYear()
    {
        return FiscalYear::where("is_active", "1")
            ->pluck("id")
            ->first();
    }

    public function getRemainingLeaveCount($employeeId, $leaveTypeId, $fiscalYearId, $assign = false)
    {
        $leaveType = LeaveType::find($leaveTypeId);
        if (!$leaveType) {
            return $this->errorResponse("Leave type not found");
        }
        $remainingLeaveMessage = "";
        $leaveName = $leaveType->name;
        if (
            (!fedexHrm() || $leaveName !== Constant::REPLACEMENT_LEAVE_NAME)
            && $leaveName !== Constant::UNPAID_LEAVE
            && $leaveName !== Constant::TRAVEL_LEAVE
        ) {
            $employeeLeave = EmployeeLeave::where('employee_id', $employeeId)
                ->where('leave_type_id', $leaveType->id)
                ->where('fiscal_year_id', $fiscalYearId)
                ->select('remaining_days', 'pending_leave')
                ->first();
            if (!$employeeLeave) {
                $remainingLeaveMessage = '';
                if (!$assign)
                    return $this->errorResponse("Leave type not found");
                return;
            }
            $remainingLeave = $employeeLeave->remaining_days - $employeeLeave->pending_leave;
            $remainingLeaveMessage = "$remainingLeave days remaining";
        } else {
            $remainingLeaveMessage = '';
        }
        return $this->successResponse($remainingLeaveMessage);
    }
}
