<?php

namespace App\Http\Repositories;

use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use App\Models\configs\Setting;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Models\TimeRequest;
use Carbon\Carbon;
use DB;
use Exception;
use PermissionList;

class TimeRequestRepository extends Repository
{
    protected AttendanceDashboardRepository $attendanceDashboardRepository;
    private TicketRepository $ticketRepo;
    public function __construct()
    {
        $this->ticketRepo = new TicketRepository;
        $this->attendanceDashboardRepository = new AttendanceDashboardRepository;
    }

    public function createTimeRequest(array $data, $employeeId = null)
    {
        \logInfo('Parameters for creating time request: ', $data);
        $timeRequest = TimeRequest::create($data, $employeeId);
        $this->ticketRepo->createRequestTicket($timeRequest, [
            'current_owner_id'  => $data['verifier_id'],
            'employee_id'       => $data['employee_id'],
            'documents'         => $data['documents'] ?? []
        ]);
        return $this->responseHelper(true);
    }

    public function updateTimeRequest(TimeRequest $request, array $data)
    {
        $verifierIds = (new TimeRequest())
                        ->getNextOwners(employeeId: $data['employee_id'], isSubmitting: true)
                        ->map(fn($verifier) => $verifier->id)
                        ->toArray();

        if (!in_array($data['verifier_id'], $verifierIds))
            return $this->errorResponse("Invalid next owner", field: "verifier_id");

        \logInfo("Parameters for updating time request of id : ", $data);
        $request->update($data);
        \logInfo("Time Request Updated", ['time_request_id' => $request?->id]);
        $this->ticketRepo->updateRequestTicket($request, [
            'current_owner_id'      => $data['verifier_id'],
            'documents'             => $data['documents'] ?? [],
            'removing_document_ids' => $data['removing_document_ids'] ?? []
        ]);
        return $this->responseHelper(true, "Time Request Updated");
    }

    public function assignLeaveRequest(array $data)
    {
        \logInfo('Parameters for assigning time request: ', $data);

        $timeRequest = TimeRequest::create($data);
        \logInfo("Leave Request Created: ", ['time_request_id' => $timeRequest->id]);

        $response = $this->ticketRepo->createRequestTicket($timeRequest, [
            'employee_id'       => $data['employee_id'],
            'current_owner_id'  => null,
            'documents'         => $data['documents'] ?? []
        ], false);
        $requestTicket = $response['data'];

        $transitionsList = $timeRequest->transitionPerformers()->where([
            ['recipient_id', $timeRequest->employee_id],
            ['performer_id', currentEmployee()->id]
        ])->get();

        $isApprover = count($transitionsList->filter(fn($transition) => $transition->state === WorkflowPerformer::APPROVER));
        $canAssignScope = auth()->user()->can(PermissionList::TIME_REQUEST_ASSIGN_SCOPE);

        if ($isApprover || $canAssignScope) {
            $timeRequest->transitionTo(
                WorkflowState::ASSIGNED,
                "",
                metadata: [
                    'initiator_id'    => '',
                    'initiator_role'  => '',
                    'next_owner_id'   => '',
                    'next_owner_role' => '',
                ]
            );
            $requestTicket->state = WorkflowState::ASSIGNED;
            $requestTicket->current_owner_role = null;
            $requestTicket->save();

            $response = $this->approveTimeRequest($timeRequest);
            if (!$response['status']) {
                return $response;
            }

            \logInfo("Time Request Transitioned to assigned");
        } else if (count($transitionsList)) {
            if (!$data['verifier_id']) return $this->errorResponse("Verifier not found");
            $requestTicket->state = WorkflowState::ALLOCATED;
            $requestTicket->current_owner_id = $data['verifier_id'];
            $requestTicket->current_owner_role = 'Approver';
            $requestTicket->verification_level += 1;
            $requestTicket->save();

            $timeRequest->transitionTo(
                WorkflowState::ALLOCATED,
                "Assigned Time Request",
                metadata: [
                    'initiator_id'      => currentEmployee()?->id,
                    'initiator_role'    => WorkflowPerformer::VERIFIER,
                    'verifier_id'       => $data['verifier_id'],
                    'next_owner_role'   => WorkflowPerformer::APPROVER,
                ]
            );

            \logInfo("Time Request Transitioned to verified and sent to next owner");
        } else {
            return $this->errorResponse("You don't have permission to assign time request to this user");
        }

        return $this->responseHelper(
            true,
            "Time Request Assigned Successfully",
            ['model_id' => $timeRequest->id, 'workflow' => $timeRequest->workflow]
        );
    }

    /**
     * Update attendance records according to the time of the approved time request.
     *
     * @param TimeRequest $model The time request model instance.
     * @return array
     */
    public function approveTimeRequest(TimeRequest $model)
    {
        $employeeId = $model->employee_id;
        \logInfo("Updating attendance of employee of id: $employeeId", ['time_request_id' => $model->id]);

        $attendance = Attendance::where([['employee_id', $employeeId], ['date_en', $model->date]])->first();

        $syncRepo = new SyncRepository;

        if ($attendance) {
            if ($model->in_time) $attendance->in_time = Carbon::parse($model->in_time)->format('g:i:s A');
            if ($model->out_time) $attendance->out_time = Carbon::parse($model->out_time)->format('g:i:s A');

            $status = $syncRepo->checkStatus($model->employee->organizationInfo, $model->date, $attendance->in_time, $attendance->out_time);

            $attendance->in_remarks = $status['in_remarks'] ?? null;
            $attendance->out_remarks = $status['out_remarks'] ?? null;
            $attendance->total_hours = $status['total_hours'] ?? null;
            $attendance->remarks = $attendance->remarks ? "{$attendance->remarks} {$status['remarks']}" : $status['remarks'];
            $attendance->status = $status['status'] ?? null;
            $attendance->source = 'Time Request';
            $attendance->missed_punch_status = true;

            $attendance->save();
            \logInfo("Attendance Updated", $attendance->toArray());
        } else {
            \logInfo("Creating new attendance for employee of id: $employeeId for date: $model->date");

            $employee = Employee::with('organizationInfo')->findOrFail($employeeId);

            $attendance = new Attendance();
            $attendance->employee_id = $employee->id;
            $attendance->employee_code = $employee->employeeCode;
            $attendance->date_en = $model->date;
            $attendance->date_np = LaravelNepaliDate::from($model->date)->toNepaliDate();
            $attendance->in_time = $model->in_time;
            $attendance->out_time = $model->out_time;

            $status = $syncRepo->checkStatus($employee->organizationInfo, $model->date, $attendance->in_time, $attendance->out_time);

            $attendance->in_remarks = $status['in_remarks'];
            $attendance->out_remarks = $status['out_remarks'];
            $attendance->total_hours = $status['total_hours'];
            $attendance->remarks = $status['remarks'] ? $status['remarks'] : "{$model->in_note}" . ($model->out_note ? "; {$model->out_note}" : "");
            $attendance->status = $status['status'];
            $attendance->source = 'Time Request';
            $attendance->missed_punch_status = true;
            $attendance->shift_id = $employee->organizationInfo->shift->id;

            $attendance->save();
            \logInfo("Attendance Created", $attendance->toArray());
        }
        return $this->responseHelper(status: true);
    }

    public function getTimeRequestCount(Employee| null $employee)
    {
        if (!$employee) {
            return 0;
        }

        $currentDate = now()->format('Y-m-d');
        $dateFrom30DaysAgo = now()->subDays(30)->format('Y-m-d');
        return TimeRequest::where('employee_id', $employee->id)
            ->where('state', 'Approved')
            ->whereBetween('date', [$dateFrom30DaysAgo, $currentDate])
            ->count();
    }

    public function verifiers($params)
    {
        return (new TimeRequest)->getNextOwners(employeeId: $params['employee_id'], isSubmitting: $params['is_submitting']);
    }

    public function recordedTime($params) {
        $dateEn = LaravelNepaliDate::from($params['nep_date'])->toEnglishDate();
        return \App\Models\Leaves\Attendance::where('date_en', $dateEn)
                                            ->where('employee_id', $params['employee_id'])
                                            ->select('in_time', 'out_time')
                                            ->first();
    }

    public function checkTimeRequestOverlap($employeeId, $date, $ignoreId = null)
    {
        return TimeRequest::where('employee_id', $employeeId)
            ->where('date', $date)
            ->whereNotIn('state', ArflowHelper::getFinalStates(WorkflowName::TIME_REQUEST_APPROVAL))
            ->when($ignoreId, fn($query) => $query->whereNot('id', $ignoreId))
            ->exists();
    }

    public function createOrUpdateTimeRequest(array $params, $editingId = null, ?bool $assign = false): array
    {
        DB::beginTransaction();
        try {
            logInfo("Parameters for time request: ", $params);

            $date = LaravelNepaliDate::from($params['nep_date'])->toEnglishDate();
            $employeeId = $params['employee_id'];

            logInfo("Converted Nepali date to English: $date");


            if ($date > date("Y-m-d")) {
                throw new Exception("Future date cannot be selected.");
            }

            if (empty($params['in_time']) && empty($params['out_time'])) {
                throw new Exception("Either in time or out time is required.");
            }

            if ($assign && isset($params['employee_id']) && $params['employee_id'] == currentEmployeeId()) {
                throw new Exception('Self assign cannot be done.');
            }

            if ($this->checkTimeRequestOverlap($params['employee_id'], $date, $editingId)) {
                throw new Exception('Time request already exists for this date.');
            }

            logInfo("No time request overlap for $date");

            if(!$this->isTimeDateValid($date, $employeeId)){
                throw new Exception("You cannot select date older than allowed date.");
            }

            logInfo("Time date is valid for: $date");

            if (!$assign) {
                $verifierIds = (new TimeRequest)->getNextOwners($params['employee_id'], true)
                    ->map(fn($verifier) => $verifier->id)->toArray();
                if (!in_array($params['verifier_id'], $verifierIds)) {
                    throw new Exception('Invalid Verifier.');
                }
                logInfo("Verifier is valid: {$params['verifier_id']}");
            }

            $data = [
                'date'                  => $date,
                'nep_date'              => $params['nep_date'],
                'in_time'               => $params['in_time'],
                'in_note'               => $params['in_note'],
                'out_time'              => $params['out_time'],
                'out_note'              => $params['out_note'],
                'verifier_id'           => $params['verifier_id'],
                'employee_id'           => $params['employee_id'],
                'documents'             => $params['documents'] ?? [],
                'removing_document_ids' => $params['removing_document_ids'] ?? [],
                'fiscal_year_id'        => currentFiscalYearId()
            ];

            logInfo("Prepared time request data", $data);

            $response = $editingId
                ? $this->updateTimeRequest(TimeRequest::findOrFail($editingId), $data)
                : ($assign
                    ? $this->assignLeaveRequest($data)
                    : $this->createTimeRequest($data));

            if (!$response['status']) {
                throw new Exception($response['message']);
            }

            DB::commit();
            logInfo("Time request " . ($editingId ? "updated" : ($assign ? "assigned" : "created")) . " successfully.");
            logInfo("Response: " . $response['message'] ?? 'Success');
            return $response['data'];
        } catch (Exception $e) {
            DB::rollBack();
            logError("Error while creating/updating time request: ", $e);
            throw $e;
        }
    }

    public function getTimeRequest($id) {
        return TimeRequest::with('requestTicket')
                            ->select('id', 'date', 'in_time', 'in_note', 'out_time', 'out_note', 'nep_date')
                            ->where('id', $id)
                            ->firstOrFail();
    }
    public function isTimeDateValid(Carbon|string $timeDate, int $employeeId): bool
    {
        $minRequiredDays = Setting::where('key', 'time.max_days_to_apply')->value('value');
        if(!$minRequiredDays) return true;

        return $this->attendanceDashboardRepository->isDateValidBasedOnRecentAttendance($timeDate, $employeeId, $minRequiredDays);
    }
}
