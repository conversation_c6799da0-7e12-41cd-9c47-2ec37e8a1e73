<?php

namespace App\Http\Repositories\Reports;

use App\Http\Repositories\Repository;
use App\Models\Payroll\Payment;
use App\Models\Payroll\SalaryVariance;
use App\Traits\WithDataTable;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;

class SalaryVarianceRepository extends Repository
{
    use WithDataTable; 
    public function listQuery($filters, $store = false)
    {
        $currentAdditionalAllowance = $this->additionalAllowanceQuery("current");
        $actualAdditionalAllowance = $this->additionalAllowanceQuery("actual");
        
        $previousMonth = $filters['selectedMonth'] != 1 ? $filters['selectedMonth'] - 1 : 12;
        $previousYear = $filters['selectedMonth'] != 1 ? $filters['selectedYear'] : $filters['selectedYear'] - 1;

        $daysInSelectedMonth = LaravelNepaliDate::daysInMonth($filters['selectedMonth'], $filters['selectedYear']);
        $daysInPreviousMonth = LaravelNepaliDate::daysInMonth($previousMonth, $previousYear);

        $selectedPayment = Payment::query()
            ->leftJoin('employees as e', 'e.id', 'payments.employee_id')
            ->leftJoin('employee_org as eo', 'eo.employee_id', 'e.id')
            ->leftJoin('outsource_companies as oc', 'eo.outsource_company_id', 'oc.id')
            ->leftJoin('payslips as p', 'p.id', 'payments.payslip_id')
            ->leftJoin('companies as c', 'e.company_id', 'c.id')
            ->select(
                "payments.id as payment_id",
                "e.id",
                "eo.doj",
                "p.remarks",
                "e.company_id",
                "eo.branch_id",
                "eo.department_id",
                "eo.outsource_company_id",
                "e.first_name as first_name",
                "e.middle_name as middle_name",
                "e.last_name as last_name",
                "c.code as company_code",
                "eo.employee_code",
                "p.payslip_type",
                "c.name as company_name",
                "payments.payslip_id",
                "p.remarks as payslip_remarks",
                DB::raw("CONCAT(e.first_name, ' ', e.middle_name, ' ', e.last_name) as employee_name"),
                DB::raw("
                            CASE 
                                WHEN eo.termination_request_date IS NOT NULL OR eo.termination_date IS NOT NULL THEN 'Resigned'
                                ELSE 'Active'
                            END AS status
                        "),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.branch.name')) as branch"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.department.name')) as department"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.outsource_company.name')) as vendor"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.enroll_status')) as enroll_status"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.total_salary_days')) as attendance"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.service_charge')) as oc_service_charge"),
                DB::raw(
                    "
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.basic_salary.current')) AS DECIMAL(10,2)), 0) + 
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.allowance.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.stipend.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cit.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.ssf_contribution.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.total_current_month_additional_income')) AS DECIMAL(10,2)), 0)"
                    . ($currentAdditionalAllowance ? " + {$currentAdditionalAllowance}" : "") .
                    " AS gross_salary"
                ),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cash_in_hand')) as net"),
                DB::raw(
                    "
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.basic_salary.actual')) AS DECIMAL(10,2)), 0) + 
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.allowance.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.stipend.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cit.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.ssf_contribution.actual')) AS DECIMAL(10,2)), 0)"
                    . ($actualAdditionalAllowance ? " + {$actualAdditionalAllowance}" : "") .
                    " AS before_attn"
                ),
                DB::raw("
                                CONCAT(
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.company.id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.outsource_company.outsource_company_id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.branch.branch_id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.department.department_id'))
                                ) as checkInfo
                            ")
            )
            ->where("payments.year", $filters['selectedYear'])
            ->where("payments.month", $filters['selectedMonth'])
            ->when($filters['companyId'] ?? null, function ($query) use ($filters) {
                $query->where("e.company_id", $filters['companyId']);
            })->when($filters['branchId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.branch_id", $filters['branchId']);
            })->when($filters['departmentId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.department_id", $filters['departmentId']);
            })->when($filters['vendorId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.outsource_company_id", $filters['vendorId']);
            })->when($filters['status'] ?? null, function ($query) use ($filters) {
                $query->where("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.enroll_status'))", $filters['status']);
            })->get()
            ->keyBy("id");

        $previousPayment = Payment::query()
            ->leftJoin('employees as e', 'e.id', 'payments.employee_id')
            ->leftJoin('employee_org as eo', 'eo.employee_id', 'e.id')
            ->leftJoin('outsource_companies as oc', 'eo.outsource_company_id', 'oc.id')
            ->leftJoin('companies as c', 'e.company_id', 'c.id')
            ->leftJoin('payslips as p', 'p.id', 'payments.payslip_id')
            ->select(
                "payments.id as payment_id",
                "e.id",
                "eo.termination_date",
                "e.company_id",
                "eo.branch_id",
                "eo.department_id",
                "eo.outsource_company_id as vendor_id",
                "e.first_name as first_name",
                "e.middle_name as middle_name",
                "e.last_name as last_name",
                "c.code as company_code",
                "eo.employee_code",
                "p.payslip_type",
                "c.name as company_name",
                "payments.payslip_id",
                "p.remarks as payslip_remarks",
                DB::raw("CONCAT(e.first_name, ' ', e.middle_name, ' ', e.last_name) as employee_name"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.branch.name')) as branch"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.department.name')) as department"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.outsource_company.name')) as vendor"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.enroll_status')) as enroll_status"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.total_salary_days')) as attendance"),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.service_charge')) as oc_service_charge"),
                DB::raw(
                    "
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.basic_salary.current')) AS DECIMAL(10,2)), 0) + 
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.allowance.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.stipend.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cit.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.ssf_contribution.current')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.total_current_month_additional_income')) AS DECIMAL(10,2)), 0)"
                    . ($currentAdditionalAllowance ? " + {$currentAdditionalAllowance}" : "") .
                    " AS gross_salary"
                ),
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cash_in_hand')) as net"),
                DB::raw(
                    "
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.basic_salary.actual')) AS DECIMAL(10,2)), 0) + 
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.allowance.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.stipend.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.cit.actual')) AS DECIMAL(10,2)), 0) +
                            COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.ssf_contribution.actual')) AS DECIMAL(10,2)), 0)"
                    . ($actualAdditionalAllowance ? " + {$actualAdditionalAllowance}" : "") .
                    " AS before_attn"
                ),
                DB::raw("
                                CONCAT(
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.company.id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.outsource_company.outsource_company_id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.branch.branch_id')),
                                    '-',
                                    JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.employee.department.department_id'))
                                ) as checkInfo
                            ")
            )->where("payments.year", $previousYear)
            ->where("payments.month", $previousMonth)
            ->when($filters['companyId'] ?? null, function ($query) use ($filters) {
                $query->where("e.company_id", $filters['companyId']);
            })->when($filters['branchId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.branch_id", $filters['branchId']);
            })->when($filters['departmentId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.department_id", $filters['departmentId']);
            })->when($filters['vendorId'] ?? null, function ($query) use ($filters) {
                $query->where("eo.outsource_company_id", $filters['vendorId']);
            })->when($filters['status'] ?? null, function ($query) use ($filters) {
                $query->where("JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.enroll_status'))", $filters['status']);
            })->get()
            ->keyBy("id");

        $allEmployeeIds = $selectedPayment->keys()
            ->merge($previousPayment->keys())
            ->unique();

        $varianceData = $allEmployeeIds->map(function ($id) use ($filters, $selectedPayment, $previousPayment, $daysInSelectedMonth, $daysInPreviousMonth) {
            $selected = $selectedPayment->get($id);
            $previous = $previousPayment->get($id);

            $status = "Active";
            $dateRange = LaravelNepaliDate::getDateRangeForNepaliMonth($filters['selectedMonth'], $filters['selectedYear']);
            if (isset($selected) && !isset($previous)) {
                $status = "Attrited";
                if ($selected->doj >= $dateRange["startDate"] && $selected->doj <= $dateRange["endDate"]) {
                    $status = "New Joinee";
                }
            }

            if (!isset($selected) && isset($previous)) {
                $status = "Attrited";
                if ($previous->termination_date >= $dateRange["startDate"] && $previous->doj <= $dateRange["endDate"]) {
                    $status = "Resigned";
                }
            }
            
            $payslipRemarks = "";
            if ($status == "Resigned") {
                $remarks = $status;
            } elseif(isset($selected->checkInfo) && isset($previous->checkInfo) && $selected->checkInfo != $previous->checkInfo) {
                $remarks = "Employee Transfer";
                if($selected->payslip_id != $previous->payslip_id) {
                    $remarks = "Role Change";
                }
                $payslipRemarks = $selected->payslip_remarks;
            } elseif(isset($selected->payslip_id) && isset($previous->payslip_id) &&  $selected->payslip_id != $previous->payslip_id) {
                $remarks = $selected->payslip_type ?? $previous->payslip_type;
                $payslipRemarks = $selected->payslip_remarks ?? $previous->payslip_remarks;
            } elseif((isset($selected->attendance) && $daysInSelectedMonth != $selected->attendance) || (isset($previous->attendance) && $daysInPreviousMonth != $previous->attendance)) {
                $remarks = "Impact due to attendance";
            } elseif(isset($selected->payslip_id) && !isset($previous->payslip_id)) {
                $remarks = $selected->payslip_type;
                $payslipRemarks = $selected->payslip_remarks;
            } else {
                $remarks = "OK";
            }

            $employeeName = $employeeCode = "";
            if(isset($selected->first_name)) {
                $employeeName = $selected->first_name." ".$selected->last_name;
                if(isset($selected->middle_name) && $selected->middle_name != "") {
                    $employeeName = $selected->first_name." ".$selected->middle_name." ".$selected->last_name;
                }
                $employeeCode = $selected->company_code."-".$selected->employee_code;
            } else {
                if(isset($previous->first_name)) {
                    $employeeName = $previous->first_name." ".$previous->last_name;
                    if(isset($previous->middle_name) && $previous->middle_name != "") {
                        $employeeName = $previous->first_name." ".$previous->middle_name." ".$previous->last_name;
                    }
                }
                $employeeCode = $previous->company_code."-".$previous->employee_code;
            }      
            
            $ctcCurrent = sprintf("%.2f",(($selected?->gross_salary ?? 0) + (($selected?->oc_service_charge ?? 0) / $daysInSelectedMonth) * $selected?->attendance));
            $ctcPrevious = sprintf("%.2f",(($previous?->gross_salary ?? 0) + (($previous?->oc_service_charge ?? 0) / $daysInPreviousMonth) * $previous?->attendance));
            $ctcDiff = sprintf("%.2f",$ctcCurrent - $ctcPrevious);

            return [
                'id' => $id,
                'payment_id' => $selected->payment_id ?? $previous?->payment_id,
                'employee_name' => $employeeName,
                'employee_code' => $employeeCode,
                'status' => $status,
                'company_id' => $selected->company_id ?? $previous?->company_id,
                'branch_id' => $selected->branch_id ?? $previous?->branch_id,
                'department_id' => $selected->department_id ?? $previous?->department_id,
                'vendor_id' => $selected?->vendor_id ?? $previous?->vendor_id,
                'branch_current' => $selected?->branch ?? 'N/A',
                'branch_previous' => $previous?->branch ?? 'N/A',

                'department_current' => $selected?->department ?? 'N/A',
                'department_previous' => $previous?->department ?? 'N/A',

                'vendor_current' => $selected?->vendor ?? ($selected->company_name ?? "N/A"),
                'vendor_previous' => $previous?->vendor ?? ($previous->company_name ?? "N/A"),

                'enroll_status' => $selected?->enroll_status ?? 'N/A',

                'attendance_current' => $selected?->attendance ?? 0,
                'attendance_previous' => $previous?->attendance ?? 0,
                'attendance_diff' => ($selected?->attendance ?? 0) - ($previous?->attendance ?? 0),

                'gross_current' => sprintf("%.2f",$selected?->gross_salary ?? 0),
                'gross_previous' => sprintf("%.2f",$previous?->gross_salary ?? 0),
                'gross_diff' => sprintf("%.2f",($selected?->gross_salary ?? 0) - ($previous?->gross_salary ?? 0)),

                'ctc_current' => $ctcCurrent,
                'ctc_previous' => $ctcPrevious,
                'ctc_diff' => $ctcDiff,

                'net_current' => sprintf("%.2f",$selected?->net ?? 0),
                'net_previous' => sprintf("%.2f",$previous?->net ?? 0),
                'net_diff' => sprintf("%.2f",($selected?->net ?? 0) - ($previous?->net ?? 0)),

                'before_attn_current' => sprintf("%.2f",$selected?->before_attn ?? 0),
                'before_attn_previous' => sprintf("%.2f",$previous?->before_attn ?? 0),
                'before_attn_diff' => sprintf("%.2f",($selected?->before_attn ?? 0) - ($previous?->before_attn ?? 0)),

                'remarks' => $remarks,
                'payslip_remarks' => $payslipRemarks,
            ];
        });
        if ($store)
            $this->storeSalaryVariance($varianceData, $filters['selectedYear'], $filters['selectedMonth']);

        return $varianceData;
    }

    public function storeSalaryVariance($data, $year, $month)
    {
        try {
            DB::beginTransaction();
            $deleteExistingSalaryVariance = SalaryVariance::where("year", $year)
                ->where("month", $month)
                ->delete();
            $dataToStore = [];
            foreach ($data as $item) {
                $dataToStore[] = [
                    "year" => $year,
                    "month" => $month,
                    "employee_id" => $item['id'],
                    "payment_id" => $item['payment_id'],
                    "variance_meta" => json_encode($item),
                    "created_at" => date("Y-m-d H:i:s"),
                    "updated_at" => date("Y-m-d H:i:s")
                ];
            }

            $store = SalaryVariance::insert($dataToStore);
            if ($store) {
                DB::commit();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }
    
    private function jsonUnquote($variable): \Illuminate\Contracts\Database\Query\Expression
    {
        return DB::raw("JSON_UNQUOTE(JSON_EXTRACT(variance_meta, '$.$variable'))");
    }

    public function getVarianceData($filters, $array = false)
    {
        $data = SalaryVariance::select("variance_meta")
            ->where("year", $filters['selectedYear'])
            ->where("month", $filters['selectedMonth'])
            ->when($filters['companyId'] ?? null, function ($query) use ($filters) {
                $query->where($this->jsonUnquote('company_id'), $filters['companyId']);
             })->when($filters['branchId'] ?? null, function ($query) use ($filters) {
                $query->where($this->jsonUnquote('branch_id'), $filters['branchId']);
            })->when($filters['departmentId'] ?? null, function ($query) use ($filters) {
                $query->where($this->jsonUnquote('department_id'), $filters['departmentId']);
            })->when($filters['vendorId'] ?? null, function ($query) use ($filters) {
                $query->where($this->jsonUnquote('vendor_id'), $filters['vendorId']);
            })->when($filters['statusId'] ?? null, function ($query) use ($filters) {
                $query->where($this->jsonUnquote('enroll_status'), $filters['statusId']);
            });

        if(isSuperAdmin() || scopeAll()) {
            //since for admin and scope all users all data are to be shown
        } elseif(count(scopeMultipleCompany()) > 0) {
            $data = $data->where(function ($query) {
                                    $query->where($this->jsonUnquote('company_id'), currentEmployee()->company_id)
                                        ->orWhereIn($this->jsonUnquote('company_id'), scopeMultipleCompany());
                                });
        } elseif (scopeCompany()) {
            $data = $data->where($this->jsonUnquote('company_id'), currentEmployee()->company_id);
        } elseif(count(scopeMultipleBranch()) > 0) {
            $data = $data->where(function($query) {
                                    $query->where($this->jsonUnquote('branch_id'), currentEmployee()?->organizationInfo?->branch_id)
                                        ->orWhereIn($this->jsonUnquote('branch_id'), scopeMultipleBranch());
                                });
        } elseif(scopeBranch() || scopeRegion() || scopeMultipleRegion()) {
            $data = $data->where($this->jsonUnquote('branch_id'), currentEmployee()?->organizationInfo?->branch_id);
        } elseif(count(scopeMultipleDepartment()) > 0) {
            $data = $data->where(function($query) {
                                    $query->where($this->jsonUnquote('department_id'), currentEmployee()?->organizationInfo?->department_id)
                                        ->orWhereIn($this->jsonUnquote('department_id'), scopeMultipleDepartment());
                                });
        } elseif(scopeDepartment()) {
            $data = $data->where($this->jsonUnquote('department_id'), currentEmployee()?->organizationInfo?->department_id);
        } else {
            $data = $data->where($this->jsonUnquote('employee_code'), currentEmployee()->getCompanyEmpCodeAttribute());
        }

        if($array) {
            $data = $data->get()
                ->map(fn($item) => json_decode($item->variance_meta, true))
                ->toArray();
        }
        return $data;
    }

    private function additionalAllowanceQuery($type)
    {
        $perkKeys = DB::table('perks')->pluck('name');

        $allowanceSumParts = $perkKeys->map(function ($key) use ($type) {
            return "COALESCE(CAST(JSON_UNQUOTE(JSON_EXTRACT(payments.actual_payment_meta, '$.additional_allowance.{$type}.\"{$key}\"')) AS DECIMAL(10,2)), 0)";
        });
        return $allowanceSumParts->implode(' + ');
    }
}
