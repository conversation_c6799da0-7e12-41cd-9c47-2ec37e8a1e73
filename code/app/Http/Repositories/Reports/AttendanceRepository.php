<?php

namespace App\Http\Repositories\Reports;

use App\Facades\LaravelNepaliDate;
use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use App\Http\Helpers\Constant;
use App\Http\Repositories\Attendance\SyncRepository;
use App\Http\Repositories\OtRequestRepository;
use App\Http\Repositories\Repository;
use App\Livewire\Config\AttDevice;
use App\Livewire\SelfService\Attendance\AttendancePage;
use App\Models\configs\AttDevice as ConfigsAttDevice;
use App\Models\configs\AttLog;
use App\Models\configs\Department;
use App\Models\configs\FiscalYear;
use App\Models\configs\Setting;
use App\Models\configs\Unit;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\EmployeeAttendanceMonthlyDetail;
use App\Models\Leaves\EmployeeLeaveDetail;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AttendanceRepository extends Repository
{
    private $timeValidDate, $leaveValidDate, $minInOutTimeDifferenceForOt, $otGraceDay;

    public function generateEmployeeMonthlyLeaveSummary(int $year, int $month, bool $fromCommand = false)
    {
        \logCronInfo("Generating employee monthly leave summary for year: $year month $month");
        try {
            $laravelNepaliDate = new LaravelNepaliDate;
            $dateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth($month, $year);

            $startDate = Carbon::createFromFormat('Y-m-d', $dateRange['startdate']);
            $endDate = Carbon::createFromFormat('Y-m-d', $dateRange['enddate']);
            if ($endDate->gte(Carbon::today())) $endDate = Carbon::today();

            $attendances = Attendance::where([
                ['date_en', '>=', $startDate->format('Y-m-d')],
                ['date_en', '<=', $endDate->format('Y-m-d')]
            ])->whereNull('deleted_at')->get();

            $employees = Employee::all();

            $fiscalYear = FiscalYear::getFiscalYearFromYearAndMonth($year, $month);
            if (!$fiscalYear) {
                throw new Exception("Fiscal year not found");
            }

            foreach ($employees as $employee) {
                $record = [
                    'employee_id'       => $employee->id,
                    'year'              => $year,
                    'month'             => $month,
                    'total_days'        => 0,
                    'duty_days'         => 0,
                    'present_days'      => 0,
                    'absent_days'       => 0,
                    'approved_leave'    => 0,
                    'leave_details'     => [],
                    'missed_punch'      => 0,
                    'holiday'           => 0,
                    'work_on_holiday'   => 0,
                    'fiscal_year_id'    => $fiscalYear->id,
                ];

                foreach ($startDate->daysUntil($endDate) as $date) {
                    $record['total_days']++;

                    $dayAttendance = $attendances->filter(function ($att) use ($date, $employee) {
                        return $att->employee_id == $employee->id
                            && $att->date_en == $date->format('Y-m-d');
                    })->first();
                    if ($dayAttendance) {
                        if (strpos($dayAttendance?->status, 'On Holiday') === 0) {
                            $record['holiday']++;
                        } else if (strpos($dayAttendance?->status, 'Work On Holiday') === 0) {
                            $record['work_on_holiday']++;
                        } else if (strpos($dayAttendance?->status, 'Absent') === 0) {
                            $record['absent_days']++;
                        } else if (strpos($dayAttendance?->status, 'Day Off') !== false) {
                            continue;
                        } else
                            $record['duty_days']++;


                        if ($dayAttendance->in_time || $dayAttendance->out_time)
                            $record['present_days']++;

                        if (
                            (!$dayAttendance->in_time && $dayAttendance->out_time) ||
                            ($dayAttendance->in_time && !$dayAttendance->out_time) ||
                            $dayAttendance->missed_punch
                        )
                            $record['missed_punch']++;

                        if ($dayAttendance->leave_status) {
                            $leaveDetail = EmployeeLeaveDetail::where('date', $date->format('Y-m-d'))
                                ->where('employee_id', $employee->id)
                                ->first();
                            $leaveDay = $leaveDetail?->num_days ?? 0;
                            $record['approved_leave'] += $leaveDay;
                            $record['leave_details'][$leaveDetail?->leaveType?->name] = ($record['leave_details'][$leaveDetail?->leaveType?->name] ?? 0) +  $leaveDay;
                        }
                    }
                }

                EmployeeAttendanceMonthlyDetail::updateOrCreate(
                    ['employee_id' => $employee?->id, 'year' => $year, 'month' => $month],
                    $record,
                );
                echo "$employee?->id: $employee?->name completed\n";
                \logCronInfo("Summary of $employee?->id: $employee?->name completed");
            }
            echo "All Completed $year $month";
            \logCronInfo("Employee monthly leave summary calculation completed successfully\n\n");

            return $this->successResponse(true, "Employee Attendance Report synced successfully");
        } catch (\Exception $e) {
            if ($fromCommand) {
                echo "Error: " . $e->getMessage();
                \logCronError("Error while generating employee monthly leave summary report", $e);
            } else {
                logError("Error while generating employee monthly leave summary report", $e);
            }
            return $this->errorResponse(false, "Error while generating employee monthly leave summary report");
        }
    }

    public function deleteAttendanceBeforeDOJ($employee_id, $doj)
    {
        if ($doj)
            Attendance::where('employee_id', $employee_id)->where('date_en', '<', $doj)->forceDelete();
    }

    public function deleteFutureDateAttendance($employee_id, $termination_date)
    {
        $attendanceQuery = Attendance::where('employee_id', $employee_id);

        if (fedexHrm()) {
            $attendanceQuery->where('date_en', '>=', $termination_date);
        } else {
            $attendanceQuery->where('date_en', '>', $termination_date);
        }

        $attendanceQuery->forceDelete();
    }

    public function attendanceList($params)
    {
        $this->initializeValidDate();

        $query = Attendance::where('employee_id', $params['employeeId'])
            ->where('date_np', 'like', $params['date'] . "%")
            ->select(
                'id',
                'date_en',
                'date_np',
                'in_time',
                'out_time',
                'duty_start',
                'duty_end',
                'total_hours',
                'status',
                'remarks',
                'source'
            );

        $sortBy = $params['sortBy'] ?? 'date_en';
        $sortDirection = $params['sortDirection'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        $items = $query->get();

        $items->map(function ($item) {
            $item->time_request = $this->showApplyMissedPunch($item);
            $item->leave_request = $this->showApplyLeave($item);
            // $item->ot_request = !konnectHrm() && $this->isEligibleForOvertime($item);
            // $item->ot_request_konnect = konnectHrm() && $this->isEligibleForOvertime($item);
            return $item;
        });

        return $items;
    }

    public function teamMemberAttendanceList($params)
    {
        $params['search'] = $params['search'] ?? "";
        return Attendance::select(
            DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
            'attendance.id as id',
            'attendance.employee_id as employee_id',
            'attendance.date_en as date_en',
            'attendance.date_np as date_np',
            'attendance.in_time as in_time',
            'attendance.out_time as out_time',
            'attendance.status as status',
            'attendance.source as source',
            'branch.name as branch_name',
            'departments.name as department_name',
        )
            ->join('employees', 'attendance.employee_id', '=', 'employees.id')
            ->join('employee_org', function ($join) use ($params) {
                $join->on('employees.id', '=', 'employee_org.employee_id');
                if (isset($params['selectedUnit']) && $params['selectedUnit'] !== null && $params['selectedUnit'] != "-1") {
                    $join->where('employee_org.unit_id', $params['selectedUnit']);
                }
                if (isset($params['selectedDepartment']) && $params['selectedDepartment'] && $params['selectedDepartment'] != '-1') {
                    $join->where('employee_org.department_id', $params['selectedDepartment']);
                }
                if (isset($params['branchId']) && $params['branchId'])
                    $join->where('employee_org.branch_id', $params['branchId']);

                if (isset($params['regionId']) && $params['regionId'])
                    $join->where('employee_org.region_id', $params['regionId']);
            })
            ->join('departments', 'employee_org.department_id', '=', 'departments.id')
            ->leftJoin('branches as branch', 'employee_org.branch_id', '=', 'branch.id')
            ->where('attendance.date_np', "{$params['selectedDate']}")
            ->where(function ($query) use ($params) {
                $query->where('employees.id', 'LIKE', "%{$params['search']}%")
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $params['search'] . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $params['search'] . '%')
                    ->orWhere('employee_org.employee_code', 'like', "%{$params['search']}%");
            })->orderBy('full_name');
    }

    public function departmentList()
    {
        return Department::pluck('name', 'id')->toArray();
    }

    public function unitList($departmentId)
    {
        return Unit::where('department_id', $departmentId)
            ->select('id', 'name')
            ->get();
    }

    public function saveAttendanceLogs(array $data)
    {
        $deviceIps = array_column($data, 'device_ip');
        $deviceData = ConfigsAttDevice::query()
            ->whereIn('device_ip', $deviceIps)
            ->pluck('id', 'device_ip')
            ->toArray();
        $data = array_map(function ($item) use ($deviceData) {
            return [
                ...$item,
                'device_id' => $deviceData[$item['device_ip']],
                'fetch_date' => now()->format('Y-m-d'),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, $data);
        AttLog::insert($data);
        return true;
    }

    public function initializeValidDate()
    {
        $this->minInOutTimeDifferenceForOt = 30;

        $leaveMinDays = Setting::where('key', 'leave.max_days_to_apply')->value('value');
        $timeMinDays = Setting::where('key', 'time.max_days_to_apply')->value('value');

        $this->leaveValidDate = $leaveMinDays ? app(AttendanceDashboardRepository::class)->getEarliestValidDate(currentEmployeeId(), $leaveMinDays) : null;
        $this->timeValidDate = $timeMinDays ? app(AttendanceDashboardRepository::class)->getEarliestValidDate(currentEmployeeId(), $timeMinDays) : null;
    }

    public function showApplyMissedPunch($item)
    {
        $status = strtolower($item->status);
        $isValidStatus = str_contains($status, 'missed punch') || str_contains($status, 'absent');

        return (!$item->in_time || !$item->out_time)
            && $isValidStatus
            && $this->checkValidDate($this->timeValidDate, $item->date_en);
    }


    public function showApplyLeave($item)
    {
        $isValidStatus = str_contains(strtolower($item['status']), 'absent');
        return $isValidStatus && $this->checkValidDate($this->leaveValidDate, $item->date_en);
    }

    private function isCurrentMonthYear($item)
    {
        Log::info('Checking isCurrentMonthYear for date:', ['date_np' => $item->date_np]);
        $currentNepaliDay = \CodeBright\LaravelNepaliDate\LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'd', locale: 'en');
        // $currentNepaliMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        // $currentNepaliYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');

        $laravelNepaliDate = new LaravelNepaliDate;
        $dateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth();

        $date = $item->date_en;
        $start = $dateRange['startdate'] ?? null;
        $end = $dateRange['enddate'] ?? null;

        return ($date >= $start && $date <= $end) || ($currentNepaliDay <= $this->otGraceDay);
    }

    public function getTodayAttendance($employeeId)
    {
        return Attendance::where('employee_id', $employeeId)
            ->where('date_en', Carbon::now()->format('Y-m-d'))
            ->first();
    }

    public function canClockInOut($employeeId, $coordinate)
    {
        $employeeOrg = EmployeeOrg::where('employee_id', $employeeId)->first();

        if (!$employeeOrg) {
            throw new Exception("EmployeeOrg not found");
        }

        if (!$employeeOrg->biometric_id) {
            throw new Exception("Biometric id not found");
        }

        if ($employeeOrg->bypass_geolocation_check) {
            logInfo("Bypassed geolocation check");
            return true;
        }

        $branch = $employeeOrg->branch;
        $subBranch = $employeeOrg->subBranch;
        if ($branch?->need_sub_branch && $subBranch) {
            if (!$subBranch?->allow_attendance)
                throw new Exception("This feature is not allowed in your sub branch.");
            if (!$subBranch?->latitude || !$subBranch?->longitude)
                throw new Exception("Sub branch location not found");
        } else if (!$branch?->allow_attendance) {
            throw new Exception("This feature is not allowed in your branch.");
        }

        if (!$branch?->latitude || !$branch?->longitude) {
            throw new Exception("Branch location not found");
        }
        $distance = distanceMeasurement($coordinate, [
            'latitude' => $branch->latitude,
            'longitude' => $branch->longitude
        ]);

        logInfo("Distance for clocking in and out", [
            'coordinate' => $coordinate,
            'distance' => $distance
        ]);

        $minDistance = $branch->max_distance ?? config('app.attendance_min_checkin_distance_meters');
        return $distance <= $minDistance;
    }

    /**
     * Clock in or out an employee and sync attendance.
     *
     * @param int $employeeId
     * @param string $type 'in' or 'out'
     * @param int $deviceTypeId
     * @param array $coordinates ['latitude' => float, 'longitude' => float]
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function clockInOut($employeeId, $type, $deviceTypeId = Constant::HRM_APP_ID, $coordinates = [])
    {
        logInfo("ClockInOut process started", [
            'employee_id' => $employeeId,
            'type' => $type,
            'device_type_id' => $deviceTypeId,
            'coordinates' => $coordinates
        ]);
        if (!in_array($type, ['in', 'out'])) {
            logError("Invalid type provided for clockInOut");
            throw new Exception("Invalid type");
        }

        $employeeOrg = EmployeeOrg::where('employee_id', $employeeId)->first();
        if (!$employeeOrg) {
            logError("EmployeeOrg not found");
            throw new Exception("EmployeeOrg not found");
        }

        if (!$employeeOrg->biometric_id) {
            logError("Biometric id not found");
            throw new Exception("Biometric id not found");
        }

        $lat = $coordinates['latitude'];
        $lng = $coordinates['longitude'];

        $fetchDate = Carbon::now()->format('Y-m-d');

        DB::beginTransaction();
        try {
            AttLog::create([
                'device_id'         => $deviceTypeId,
                'enrollment_no'     => $employeeOrg->biometric_id,
                'verify_mode'       => 1,
                'inout_mode'        => $type == 'in' ? 0 : 1,
                'log_date'          => Carbon::now()->format('Y-m-d H:i:s'),
                'fetch_date'        => $fetchDate,
                'device_ip'         => $this->getIp()['address'] ?? '',
            ]);
            logInfo("AttLog created");

            $syncRepo = new SyncRepository;
            $syncRepo->syncAttendanceFromDeviceToDb($fetchDate, $employeeId);
            logInfo("Attendance synced");

            $attendance = Attendance::where('employee_id', $employeeId)
                ->whereDate('date_en', $fetchDate)
                ->latest('id') // just in case multiple
                ->first();

            if ($attendance && $lat && $lng) {
                $locationData = $attendance->location_data ?? [];

                if (is_string($locationData)) {
                    $locationData = json_decode($locationData, true) ?? [];
                }

                $locationData[$type === 'in' ? 'clock_in' : 'clock_out'] = [
                    'lat' => (float) $lat,
                    'lng' => (float) $lng,
                ];

                $attendance->location_data = $locationData;
                $attendance->save();

                logInfo("Location data updated", [
                    'employee_id' => $employeeId,
                    'type' => $type,
                    'location' => $locationData
                ]);
            }
            DB::commit();
        } catch (Exception $e) {
            logError("Error while clocking in/out", $e);
            throw $e;
        }


        return true;
    }
    private function checkValidDate($validDate, $date)
    {
        if (!$validDate)
            return true;
        if (!$date)
            return false;

        $validDate = $validDate instanceof Carbon ? $validDate : Carbon::parse($validDate);
        $date = $date instanceof Carbon ? $date : Carbon::parse($date);

        return $validDate->lte($date);
    }

    function getIp()
    {
        $ipType = 'X-Real-Ip';
        $ipAddress = request()->header('X-Real-Ip') ?? null;
        if ($ipAddress === null) {
            $ipType = 'X-Forwarded-For';
            $ipAddress = request()->header('X-Forwarded-For') ?? null;
        }

        if (filter_var($ipAddress, FILTER_VALIDATE_IP))
            return ['type' => $ipType, 'address' => $ipAddress];
        else {
            return ['type' => 'Request-Ip', 'address' => request()->ip()];
        }
    }
}
