<?php

namespace App\Http\Repositories;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Arflow\TransitionPerformer;
use App\Models\configs\Job;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\EmpStatus;
use App\Models\configs\JobDepartment;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Payroll\BandedGradeStructure;
use App\Models\Payroll\Designation;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipPerksMapping;
use App\Models\Payroll\PayslipRequest as PayslipRequestModel;
use App\Models\Payroll\Perk;
use App\Models\RequestTicket;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Traits\WithNotify;


class PayslipApprovalRepository extends Repository
{

    use withNotify;
    private TicketRepository $ticketRepo;
    public $showStipendFor = Constant::STIPEND_FOR;

    public function __construct()
    {
        $this->ticketRepo = new TicketRepository;
    }

    public function create(array $data)
    {
        \logInfo('Parameters for creating payslip request: ', $data);
        $payslipApproval = PayslipRequestModel::create($data);
        $response = $this->ticketRepo->createRequestTicket($payslipApproval, [
            'current_owner_id' => $data['verifier_id'],
            'employee_id' => $data['employee_id'],
            'documents' => $data['documents'],
        ]);
        return $this->responseHelper(true, "Payslip request created successfully", [
            'request' => $payslipApproval,
            'ticket' => $response['data']
        ]);
    }

    public function approvePayslip(PayslipRequestModel $request)
    {
        //after payslip approved by approver
        $approver_id = $request->stateHistory()->getPerformer("Approved")->get()->last()?->actor_model_id;
        logInfo("Payslip approval transaction begin");

        DB::beginTransaction();
        try {
            $payslip = new Payslip;
            $payslip->payslip_request_id = $request->id;
            $payslip->company_id = $request->company_id;
            $payslip->employee_id = $request->employee_id;
            $payslip->enroll_status = $request->enroll_status;
            $payslip->employee_status_id = $request->employee_status_id;
            $payslip->job_id = $request->job_id;
            $payslip->designation_id = $request->designation_id;
            $payslip->band_id = $request->band_id;
            $payslip->pgrade_id = $request->pgrade_id;
            $payslip->grade_structure_id = $request->grade_structure_id;
            $payslip->banded_grade_structure_id = $request->banded_grade_structure_id ?? null;
            $payslip->added_basic_salary = $request->grade_structure?->added_basic_salary ?? 0;
            $payslip->basic_salary = $request->grade_structure?->basic_salary ?? 0;
            $payslip->allowance = $request->grade_structure?->allowance ?? 0;
            $payslip->gratuity = $request->grade_structure?->gratuity ?? 0;
            $payslip->stipend = $request->stipend ?? 0;
            $payslip->pf = $request->grade_structure?->pf ?? 0;
            $payslip->cit = $request->cit ?? 0;
            $payslip->ssf_employer = $request->ssf_employer ?? 0;
            $payslip->ssf_employee = $request->ssf_employee ?? 0;
            $payslip->insurance = $request->insurance ?? 0;
            $payslip->insurance_expiry_date = $request->insurance_expiry_date;
            $payslip->medical_insurance = $request->medical_insurance ?? 0;
            $payslip->medical_insurance_expiry_date = $request->medical_insurance_expiry_date;
            $payslip->house_insurance = $request->house_insurance ?? 0;
            $payslip->house_insurance_expiry_date = $request->house_insurance_expiry_date;
            $payslip->allow_ot = $request->allow_ot;
            $payslip->status = "Active";
            $payslip->payment_type = $request->payment_type;
            $payslip->payslip_type = $request->payslip_type;
            $payslip->payslip_for = $request->payslip_for ?? "";
            $payslip->payment_frequency = $request->payment_frequency ?? null;
            $payslip->non_cash_deduction = $request->non_cash_deduction ?? 0;
            $payslip->approved_by = $approver_id;
            $payslip->approved_date = date('Y-m-d');
            $payslip->remarks = $request->remarks;
            $payslip->start_date_eng = $request->start_date_eng;
            $payslip->stop_date_eng = $request->stop_date_eng;
            $payslip->start_date_nep = $request->start_date_nep;
            $payslip->stop_date_nep = $request->stop_date_nep;
            $payslip->position_meta = $request->position_meta;
            logInfo("Payslip now allow or not");

            $employeeData = EmployeeOrg::select('region_id', 'branch_id', 'department_id', 'iops_id')->where('employee_id', $request->employee_id)->first();
            $result = EmployeeOrg::where('employee_id', $payslip->employee_id)->update([
                'designation_id' => $payslip->designation_id,
                'employee_status_id' => $payslip->employee_status_id,
                ...($request->doj_inhouse ? ['doj_inhouse' => $request->doj_inhouse] : [])
            ]);
            if ($result)
                logInfo('Designation of employee updated');
            else
                logError('Unable to change designation of employee');
            if (!$request->employee_ticket_id) {
                $emp = Employee::find($request->employee_id);
                if (!$emp) {
                    return $this->responseHelper(false, "Unable to create payslip of a terminated employee.");
                }
                $currentJobId = $emp->getJobId();
                $destinationJobId = $request->job_id;

                $fromKey = [
                    'company_id' => $emp->company_id,
                    'branch_id' => $employeeData->branch_id,
                    'department_id' => $employeeData->department_id,
                    'job_id' => $currentJobId
                ];

                $toKey = [
                    'company_id' => $emp->company_id,
                    'branch_id' => $employeeData->branch_id,
                    'department_id' => $employeeData->department_id,
                    'job_id' => $destinationJobId
                ];

                $repo = $repo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
                $repo->moveOnPayslipChange($fromKey, $toKey);

                // if ($currentJobId != $destinationJobId) {
                //     $currentJobSeat = JobDepartment::where([
                //         'job_id' => $currentJobId,
                //         'region_id' => $employeeData->region_id,
                //         'branch_id' => $employeeData->branch_id,
                //         'department_id' => $employeeData->department_id,
                //     ])
                //         ->first();
                //     if ($currentJobSeat) {
                //         $currentJobSeat->occupancy -= 1;
                //         $currentJobSeat->save();
                //     }

                //     $destinationJobSeat = JobDepartment::where([
                //         'job_id' => $destinationJobId,
                //         'region_id' => $employeeData->region_id,
                //         'branch_id' => $employeeData->branch_id,
                //         'department_id' => $employeeData->department_id,
                //     ])
                //         ->first();
                //     if (allowJobSeat($emp->company_id)) {
                //         if (!$destinationJobSeat) {
                //             logError("Could not find job for branch_id: $employeeData->branch_id, region_id: $employeeData->region_id, job_id: $request->job_id");
                //             return $this->errorResponse("Could not find job");
                //         }
                //         if ($destinationJobSeat->seat <= $destinationJobSeat->occupancy) {
                //             logError("Error here, destination job seat is full for branch_id: $employeeData->branch_id, region_id: $employeeData->region_id, job_id: $request->job_id");
                //             return $this->errorResponse("Error here, destination job seat is full");
                //         }
                //     }

                //     if ($destinationJobSeat) {
                //         $destinationJobSeat->occupancy += 1;
                //         $destinationJobSeat->save();
                //     }
                // }
                logInfo("Job seat occupancy updated");
            }

            $payslip->save();
            logInfo("Payslip created successfully");
            logInfo("Payslip perks mapping begin");
            // DB::raw('UNLOCK TABLES payslip_perks_mapping');
            $perks_metadata = [];
            foreach (json_decode($request->allowance_metadata ?? "[]") as $perks_id => $value) {
                $perks_metadata[] = [
                    'payslip_id' => $payslip->id,
                    'perks_id' => $perks_id,
                    'amount' => last($value) ? last($value) : 0,
                ];
            }

            if (count($perks_metadata)) {
                PayslipPerksMapping::insert($perks_metadata);
            }
            // DB::raw('LOCK TABLES payslip_perks_mapping WRITE');
            logInfo("Payslip perks mapping completed");

            if ($employeeData->iops_id) {
                $iopsRepo = new IopsRepository();
                $result = $iopsRepo->updateEmployeeDetails(
                    [
                        'iopsId' => $employeeData->iops_id,
                        'hrisDesignation' => $payslip->designation->title ?? '',
                        'hrisJobDescription' => $payslip->job->name ?? '',
                    ]
                );

                if (!($result['status'] ?? null))
                    logError("Payslip approved but unable to update employee details in IOPS. " . $result['message']);
                else
                    logInfo("Payslip approved successfully and employee details updated in IOPS. " . $result['message']);
            }
            DB::commit();
            return $this->responseHelper(true);
        } catch (Exception $e) {
            DB::rollBack();
            logError("Fail to approve payslip", $e);
            return $this->responseHelper(false, message: $e->getMessage() ?? "Failed to approve payslip");
        }
    }

    /**
     * for creating payslip from payslip list (new payslip)
     * @param mixed $data
     * @return array
     */
    public function createPayslip($data)
    {
        logInfo("Parameters for creating payslip: ", $data);
        DB::beginTransaction();
        try {
            $result = TransitionPerformer::where([
                ["workflow", WorkflowName::PAYSLIP_APPROVAL],
                ["state", WorkflowPerformer::APPROVER],
                ["performer_id", $data['verifier_id']],
                ["recipient_id", $data['employee_id']],
                ["level", 1]
            ])->first();

            if (!$result) {
                logInfo("Assign transition performer");
                $result = TransitionPerformer::create([
                    "workflow" => WorkflowName::PAYSLIP_APPROVAL,
                    "state" => WorkflowPerformer::APPROVER,
                    "performer_id" => $data['verifier_id'],
                    "recipient_id" => $data['employee_id'],
                    "level" => 1
                ]);
                Cache::store('arflow')->flush();
                logInfo("Transition performer assigned successfully.");
            }

            $employeeData = EmployeeOrg::select('employee_id', 'region_id', 'branch_id', 'department_id')->where('employee_id', $result->recipient_id)->first();

            $currentJobId = $employeeData->employee->getJobId();
            if ($currentJobId != $data['job_id']) {
                $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
                try {
                    $jobSeatRepo->checkCapacity([
                        'company_id' => $data['company_id'],
                        'branch_id' => $employeeData->branch_id,
                        'department_id' => $employeeData->department_id,
                        'job_id' => $data['job_id']
                    ]);
                } catch (Exception $e) {
                    return $this->errorResponse($e->getMessage());
                }
            }

            $payslipRequestResponse = $this->createPayslipRequest($data);

            if ($payslipRequestResponse['status']) {
                logInfo("Creating payslip information completed");

                DB::commit();
                return $this->successResponse('Payslip created successfully');
            } else {
                DB::rollBack();
                logError("Error while creating payslip from createPayslipRequest function. " . $payslipRequestResponse['message']);
                return $this->errorResponse("Error while creating payslip");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while creating payslip", $e);
            return $this->errorResponse("Error while creating payslip");
        }
    }

    /**
     * for creating of payslip after the approval of transfer tickets
     *
     * @param mixed $data
     * @param mixed $transferId
     * @return array
     */
    public function createPayslipFormTransfer($data, $transferId = null)
    {
        logInfo("Parameters for creating payslip: ", $data);
        DB::beginTransaction();
        try {
            $employee = Employee::find($data['employee_id']);
            if (!$employee)
                return $this->errorResponse("Employee not found");
            if (!$transferId) {
                $result = TransitionPerformer::where([
                    ["workflow", WorkflowName::PAYSLIP_APPROVAL],
                    ["state", WorkflowPerformer::APPROVER],
                    ["performer_id", $data["verifier_id"]],
                    ["recipient_id", $data["employee_id"]],
                    ["level", 1]
                ])->first();

                if (!$result) {
                    logInfo("Assign transition performer");
                    TransitionPerformer::create([
                        "workflow" => WorkflowName::PAYSLIP_APPROVAL,
                        "state" => WorkflowPerformer::APPROVER,
                        "performer_id" => $data["verifier_id"],
                        "recipient_id" => $data["employee_id"],
                        "level" => 1
                    ]);
                    Cache::store('arflow')->flush();
                    logInfo("Transition performer assigned successfully.");
                }
            }

            $response = $this->createPayslipRequest([
                ...$data,
                'payslip_remarks' => $data['remarks'],
                'level_id' => $data['pgrade_id'],
                'company_id' => $employee->company_id,
                'employee_status_id' => $data['employee_status_id'],
                'employee_ticket_id' => $transferId,
            ]);
            if (!$response['status']) {
                throw $response['message'];
            }
            logInfo("Creating payslip information completed");

            DB::commit();
            return ["status" => true, 'data' => $response['data']];
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while creating payslip", $e);
            return $this->errorResponse("Error while creating payslip");
        }
    }

    public function getTransferPayslipData($employeeId)
    {
        $payslip = Payslip::with(['job:id,name', 'designation:id,title,level', 'band:id,name', 'level:id,name', 'employee_status:id,name', 'grade_structure', 'payslip_perks', 'payslip_perks.perks:id,name', 'approvedBy:id,first_name,middle_name,last_name'])->where([['employee_id', $employeeId], ['status', 'Active']])
            ->first();

        if (!$payslip)
            return [];

        $total = 0;
        $perks = [];
        foreach ($payslip->payslip_perks ?? [] as $value) {
            if ($value->is_required || $value->is_global) {
                $perks['required'][$value->perks_id] = [$value->perks->name => $value->amount];
            } else {
                $perks['optional'][$value->perks_id] = [$value->perks->name => $value->amount];
            }
            $total += $value->amount;
        }
        $total = $total + $payslip->grade_structure?->ctc ?? 0;

        $additionalAllowance = [
            ...($perks['required'] ?? []),
            ...($perks['optional'] ?? []),
        ];
        $additionalAllowanceMetaData = [];
        $totalCtc = $payslip->grade_structure?->ctc ?? 0;
        foreach ($additionalAllowance as $id => $item) {
            foreach ($item as $key => $value)
                $additionalAllowanceMetaData[$key] = $value;
            $totalCtc += (float) $value;
        }

        return [
            'payslipInformation' => [
                'Job Profile' => $payslip->job?->name,
                'Designation' => $payslip->designation?->title,
                'Grade' => $payslip->band?->name . "(" . $payslip->level?->name . ")",
                'Enroll Status' => $payslip->enroll_status,
                "Employee Status" => $payslip->employee_status?->name,
                "Stipend" => $payslip->stipend,
                'OT Allowed' => $payslip->allow_ot ? "Yes" : "No",
                'Payment Type' => $payslip->payment_type,
                'Payslip Type' => $payslip->payslip_type,
                'Payslip For' => $payslip->payslip_for == 'Full_time' ? 'Regular' : $payslip->payslip_for,
                'Payslip Frequency' => $payslip->payment_frequency,
                'Insurance Expiry ' => $payslip->insurance_expiry_date,
                'Medical Insurance Expiry ' => $payslip->medical_insurance_expiry_date,
                'House Insurance Expiry ' => $payslip->house_insurance_expiry_date,
                'Remarks' => $payslip->remarks,
                'Payslip Start Date' => $payslip->start_date_nep . ' (' . (LaravelNepaliDate::from($payslip->start_date_nep))->toEnglishDate() . ')',
                'Payslip End Date' => $payslip->stop_date_nep . ' (' . (LaravelNepaliDate::from($payslip->stop_date_nep))->toEnglishDate() . ')',
                'Approved By' => $payslip->approvedBy?->first_name . " " . $payslip->approvedBy?->middle_name . " " . $payslip->approvedBy?->last_name,
            ],
            'salaryStructure' => [
                'basic_salary' => $payslip->grade_structure?->basic_salary ?? 0,
                'pf' => $payslip->grade_structure?->pf ?? 0,
                'ssf' => $payslip->grade_structure?->ssf ?? 0,
                'allowance' => $payslip->grade_structure?->allowance ?? 0,
                // 'gross_salary'          => $payslip->grade_structure->gross_salary, // This is not required
                'gratuity' => $payslip->grade_structure?->gratuity ?? 0,
                'ctc' => $payslip->grade_structure?->ctc ?? 0,
                'additional_allowance' => $additionalAllowanceMetaData,
                'total_ctc' => $payslip->grade_structure?->basic_salary ?? 0,
            ],
            'designationLevel' => $payslip?->designation?->level ?? 0
        ];
    }

    // public function validatePayslipDataForExcel($data)
    // {
    //     $empCode = $data['employee code'];

    //     // $code = explode('-', $empCode);
    //     // if (count($code) != 2) return $this->errorResponse("Invalid Employee Code");

    //     $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
    //         ->leftJoin("companies as comp", "comp.id", "employees.company_id")
    //         ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$empCode'")
    //         ->select(
    //             'employees.id as id',
    //             'employees.company_id',
    //             'org.employee_status_id',
    //         )
    //         ->first();
    //     if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($empCode ? $empCode : '(NULL)'));

    //     $job = isset($data['job']) ? Job::where('name', $data['job'])->first() : null;
    //     if (isset($data['job']) && !$job) return $this->errorResponse("Job not found with name " . $data['job']);

    //     $stipend = $data['stipend'] ?? null;
    //     $designation = null;
    //     $band = null;
    //     $level = null;
    //     $gradeStructure = null;

    //     if (!$stipend) {
    //         $designation = Designation::where('title', $data['designation'])->withTrashed()->first();
    //         if (!$designation) return $this->errorResponse("Designation not found with name " . $data['designation']);

    //         $bandLevelArr = explode('-', $data['band-level']);
    //         if (count($bandLevelArr) != 2) return $this->errorResponse("Invalid band level");

    //         $band = EmployeeBand::where('name', $bandLevelArr[0])->withTrashed()->first();
    //         if (!$band) return $this->errorResponse("Band not found with name " . $bandLevelArr[0]);

    //         $level = EmployeePgrade::where('name', $bandLevelArr[1])->first();
    //         if (!$level) return $this->errorResponse("Level not found with name " . $bandLevelArr[1]);

    //         $gradeStructure = GradeStructure::where([
    //             ['grade_id', $designation->id],
    //             ['band_id', $band->id],
    //             ['pgrade_id', $level->id]
    //         ])->withTrashed()->first();
    //         if (!$gradeStructure) return $this->errorResponse("Grade structure not found for {$designation->title}({$band->name}-{$level->name})");
    //     }

    //     $enrollStatus = $data['enroll status'];
    //     $enrollStatuses = field_enums('payslip_requests', 'enroll_status', false);
    //     if (!in_array($enrollStatus, $enrollStatuses)) return $this->errorResponse("Invalid enroll status.");

    //     $paymentType = $data['payment type'];
    //     $paymentTypes = field_enums('payslip_requests', 'payment_type', false);
    //     if (!in_array($paymentType, $paymentTypes)) return $this->errorResponse("Invalid payment type.");

    //     $payslipType = $data['payslip type'];
    //     $payslipTypes = field_enums('payslip_requests', 'payslip_type', false);
    //     if (!in_array($payslipType, $payslipTypes)) return $this->errorResponse("Invalid payslip type");

    //     $payslipFor = $data['payslip for'];
    //     $payslipForEnums = field_enums('payslip_requests', 'payslip_for', false);
    //     if (!in_array($payslipFor, $payslipForEnums)) return $this->errorResponse("Invalid payslip for");

    //     $paymentFrequency = null;
    //     if ($stipend) {
    //         $paymentFrequency = $data['payment frequency'];
    //         $paymentFrequencies = field_enums('payslip_requests', 'payment_frequency', false);
    //         if (!in_array($paymentFrequency, $paymentFrequencies)) return $this->errorResponse("Invalid payment frequency");
    //     }

    //     $allowOt = strtolower($data['allow ot'] ?? 'N');
    //     if (!in_array($allowOt, ['y', 'n'])) return $this->errorResponse("Invalid allow ot");
    //     $allowOt = $allowOt == 'y';


    //     $startDate = $data['start date'];
    //     $endDate = $data['end date'];
    //     if (!$startDate || !$endDate) return $this->errorResponse("Please provide start date and end date");

    //     // $engStartDate = LaravelNepaliDate::from($startDate)->toEnglishDate();
    //     // $engEndDate = LaravelNepaliDate::from($endDate)->toEnglishDate();
    //     $engStartDate = $startDate;
    //     $engEndDate = $endDate;
    //     try {
    //         $nepStartDate = LaravelNepaliDate::from($engStartDate)->toNepaliDate();
    //         $nepEndDate = LaravelNepaliDate::from($engEndDate)->toNepaliDate();
    //     } catch (\Exception $e) {
    //         \logError("Error while converting date", $e);
    //         $nepDate = LaravelNepaliDate::from("2090-12-30")->toEnglishDate();
    //         return $this->errorResponse("We currently do not support dates more than $nepDate A.D. (2090-12-30 B.S)");
    //     }
    //     if (Carbon::parse($engStartDate)->gt(Carbon::parse($engEndDate)))
    //         return $this->errorResponse("start date can't be greater than end date");

    //     $insuranceExpiryDate = $data['insurance expiry date'] ?? null;
    //     $insurance = $data['insurance'] ?? null;
    //     if ($insurance && !$insuranceExpiryDate)
    //         return $this->errorResponse("Insurance expiry date is required when insurance is provided");

    //     // Allowance Metadata
    //     $addedAllowance = "";
    //     $allowanceMetadata = [];
    //     foreach (array_keys($data) as $header) {
    //         $perk = Perk::where('name', $header)->first();
    //         if ($perk) {
    //             $value = $data[$header];
    //             if (!is_numeric($value))
    //                 return $this->errorResponse("Invalid numeric value for allowance $perk->name");
    //             if ($value) {
    //                 if ($perk->is_global) {
    //                     if ($value != $perk->default_value)
    //                         return $this->errorResponse("$perk->name is global. Invalid value. (Valid global value: $perk->default_value)");
    //                 }
    //                 $addedAllowance .= $perk->name . ", ";
    //                 $allowanceMetadata[$perk->id] = [$perk->name => $value];
    //             }
    //         }
    //     }
    //     return $this->successResponse("", [
    //         'job'                   => $job,
    //         'employee'              => $employee,
    //         'designation'           => $designation,
    //         'band'                  => $band,
    //         'level'                 => $level,
    //         'startDate'             => $nepStartDate,
    //         'endDate'               => $nepEndDate,
    //         'insuranceExpiryDate'   => $insuranceExpiryDate,
    //         'addedAllowance'        => $addedAllowance,
    //         'allowanceMetadata'     => $allowanceMetadata,
    //         'enrollStatus'          => $enrollStatus,
    //         'paymentType'           => $paymentType,
    //         'payslipType'           => $payslipType,
    //         'payslipFor'            => $payslipFor,
    //         'paymentFrequency'      => $paymentFrequency,
    //         'allowOt'                => $allowOt,
    //     ]);
    // }

    // public function createPayslipFromExcel(array $data, $comment = "")
    // {
    //     // default values

    //     $response = $this->validatePayslipDataForExcel($data);
    //     if (!$response['status']) return $this->errorResponse($response['message']);
    //     $validatedData = $response['data'];
    //     $employee = $validatedData['employee'];
    //     $designation = $validatedData['designation'];
    //     $job = $validatedData['job'];
    //     $band = $validatedData['band'];
    //     $level = $validatedData['level'];
    //     $startDate = $validatedData['startDate'];
    //     $endDate = $validatedData['endDate'];
    //     $insuranceExpiryDate = $validatedData['insuranceExpiryDate'];
    //     $addedAllowance = $validatedData['addedAllowance'];
    //     $allowanceMetadata = $validatedData['allowanceMetadata'];
    //     $enrollStatus = $validatedData['enrollStatus'];
    //     $allowOt = $validatedData['allowOt'];

    //     $paymentType = $validatedData['paymentType'];
    //     $payslipType = $validatedData['payslipType'];
    //     $paymentFrequency = $validatedData['paymentFrequency'];
    //     $payslipFor = $validatedData['payslipFor'];

    //     $payslipRequestResponse = $this->createPayslipRequest([
    //         'company_id'            => $employee->company_id,
    //         'job_id'                => $job?->id,
    //         'designation_id'        => $designation?->id,
    //         'band_id'               => $band?->id,
    //         'level_id'              => $level?->id,
    //         'employee_id'           => $employee->id,
    //         'allowance_metadata'    => $allowanceMetadata,
    //         'enroll_status'         => $enrollStatus,
    //         'employee_status_id'    => $employee->employee_status_id,
    //         'stipend'               => $data['stipend'] ?? null,
    //         'cit'                   => $data['cit'] ?? null,
    //         'insurance'             => $data['insurance'] ?? null,
    //         'insurance_expiry_date' => $insuranceExpiryDate,
    //         'allow_ot'              => $allowOt,
    //         'payment_type'          => $paymentType,
    //         'payslip_type'          => $payslipType,
    //         'payslip_for'           => $payslipFor,
    //         'payment_frequency'     => $paymentFrequency,
    //         'payslip_remarks'       => $data['remarks'] ?? 'Uploaded',
    //         'start_date_nep'        => $startDate,
    //         'stop_date_nep'         => $endDate,
    //         'verifier_id'           => null,
    //     ], sendNotification: false);
    //     if (!$payslipRequestResponse['status']) {
    //         return $this->errorResponse($payslipRequestResponse['message']);
    //     }
    //     logInfo("Payslip created");

    //     $payslipApprovalTicket = $payslipRequestResponse['data'];
    //     $payslipApprovalTicket->transitionTo(
    //         WorkflowState::UPLOADED,
    //         $comment ?? 'Uploaded from excel',
    //         metadata: [
    //             'initiator_id'    => currentEmployee()->id,
    //             'initiator_role'  => "Uploader",
    //             'next_owner_id'   => null,
    //             'next_owner_role' => null,
    //         ]
    //     );
    //     logInfo("Payslip transition to uploaded");

    //     $this->approvePayslip($payslipApprovalTicket);
    //     logInfo("Payslip approval flow completed");

    //     $requestTicket = RequestTicket::where([
    //         ['model_type', get_class($payslipApprovalTicket)],
    //         ['model_id', $payslipApprovalTicket->id]
    //     ])->first();
    //     if (!$requestTicket) $this->errorResponse("Request ticket not found for the payslip. Contact service provider");
    //     $requestTicket->state = WorkflowState::UPLOADED;
    //     $requestTicket->current_owner_id = null;
    //     $requestTicket->current_owner_role = null;
    //     $requestTicket->save();
    //     logInfo("Request Ticket updated: state, current_owner_id and current_owner_role updated");

    //     DB::commit();
    //     return $this->successResponse("Allowance added: " . $addedAllowance, $payslipApprovalTicket);
    // }

    /**
     * validate the payslip data that are from excel file
     * @param $data data from excel file in the format of [[key: value], [key: value]]
     * @return array array of the success and error response, if success returns validated data also
     */
    public function validatePayslipDataForExcel(array $data): array
    {
        // Collect all unique values for batch queries
        $empCodes = collect($data)->pluck('employee code')->unique();
        $jobNames = collect($data)->pluck('job')->unique()->filter();
        $designationTitles = collect($data)->pluck('designation')->unique()->filter();
        $bandLevels = collect($data)->pluck('band-level')->unique()->filter();
        $empStatus = collect($data)->pluck('employee status')->unique()->filter();

        // Batch queries
        $employees = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereIn(DB::raw("CONCAT(comp.code, '-', org.employee_code)"), $empCodes)
            ->select('employees.id', 'employees.company_id', 'org.employee_status_id', DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"))
            ->get()
            ->keyBy('emp_code');

        $jobs = Job::whereIn('name', $jobNames)->get()->keyBy('name');
        $employeeStatus = EmpStatus::whereIn('name', $empStatus)->get()->keyBy('name');
        $designations = Designation::whereIn('title', $designationTitles)->get()->keyBy('title');

        $bandNames = $bandLevels->map(function ($item) {
            return explode('-', $item)[0];
        })->unique();
        $bands = EmployeeBand::whereIn('name', $bandNames)->withTrashed()->get()->keyBy('name');

        $levelNames = $bandLevels->map(function ($item) {
            return explode('-', $item)[1];
        })->unique();
        $levels = EmployeePgrade::whereIn('name', $levelNames)->get()->keyBy('name');

        $gradeStructures = GradeStructure::whereIn('grade_id', $designations->pluck('id'))
            ->whereIn('band_id', $bands->pluck('id'))
            ->whereIn('pgrade_id', $levels->pluck('id'))
            ->get()
            ->keyBy(function ($item) {
                return "{$item->grade_id}-{$item->band_id}-{$item->pgrade_id}";
            });

        $perks = Perk::all()->keyBy('name');
        $perks = collect(array_change_key_case($perks->toArray()));

        $enrollStatuses = field_enums('payslip_requests', 'enroll_status', false);
        $paymentTypes = field_enums('payslip_requests', 'payment_type', false);
        $payslipTypes = field_enums('payslip_requests', 'payslip_type', false);
        $payslipForEnums = field_enums('payslip_requests', 'payslip_for', false);
        $paymentFrequencies = field_enums('payslip_requests', 'payment_frequency', false);

        $results = [];
        foreach ($data as $item) {
            $result = $this->validateSinglePayslip(
                $item,
                $employees,
                $jobs,
                $designations,
                $employeeStatus,
                $bands,
                $levels,
                $gradeStructures,
                $enrollStatuses,
                $paymentTypes,
                $payslipTypes,
                $payslipForEnums,
                $paymentFrequencies,
                $perks
            );
            $results[] = $result;
        }

        return $results;
    }

    /**
     * validate the single payslip for excel upload
     * @param array $item single item from the excel file (single row)
     * @param Collection $employees collection of employees from which the item is to be validated
     * @param Collection $jobs
     * @param Collection $designations
     * ....
     * @return array success response with validated data or error response
     */
    private function validateSinglePayslip(
        array $item,
        Collection $employees,
        Collection $jobs,
        Collection $designations,
        Collection $employeeStatus,
        Collection $bands,
        Collection $levels,
        Collection $gradeStructures,
        array $enrollStatuses,
        array $paymentTypes,
        array $payslipTypes,
        array $payslipForEnums,
        array $paymentFrequencies,
        SupportCollection $perks
    ): array {
        $empCode = $item['employee code'];
        $employee = $employees->get($empCode);

        if (!$employee) {
            return $this->errorResponse("Employee not found with employee code: " . ($empCode ?: '(NULL)'));
        }

        // Validate job, designation, band, level, and grade structure
        $job = isset($item['job']) ? $jobs->get($item['job']) : null;
        if (isset($item['job']) && !$job) {
            return $this->errorResponse("Job not found with name " . $item['job']);
        }

        $stipend = $item['stipend'] ?? null;
        $designation = null;
        $band = null;
        $level = null;
        $gradeStructure = null;

        if (!$stipend) {
            $designation = $designations->get($item['designation']);
            if (!$designation) {
                return $this->errorResponse("Designation not found with name " . $item['designation']);
            }

            $bandLevelArr = explode('-', $item['band-level']);
            if (count($bandLevelArr) != 2) {
                return $this->errorResponse("Invalid band level");
            }

            $band = $bands->get($bandLevelArr[0]);
            if (!$band) {
                return $this->errorResponse("Band not found with name " . $bandLevelArr[0]);
            }

            $level = $levels->get($bandLevelArr[1]);
            if (!$level) {
                return $this->errorResponse("Level not found with name " . $bandLevelArr[1]);
            }

            $empStatus = $employeeStatus->get($item['employee status']);
            if (!$empStatus) {
                return $this->errorResponse("Employee Status not found with name " . $item['employee status']);
            }

            $gradeStructureKey = "{$designation->id}-{$band->id}-{$level->id}";
            $gradeStructure = $gradeStructures->get($gradeStructureKey);
            if (!$gradeStructure) {
                return $this->errorResponse("Grade structure not found for {$designation->title}({$band->name}-{$level->name})");
            }

            $addedBasicSalary = $item['added basic salary'] ?? 0;
            if (!is_numeric($addedBasicSalary)) {
                return $this->errorResponse("Invalid numeric value for added basic salary");
            }

            $nonCashDeduction = $item['non cash deduction'] ?? 0;
            if (!is_numeric($nonCashDeduction)) {
                return $this->errorResponse("Invalid numeric value for non cash deduction");
            }
        }

        $enrollStatus = $item['enroll status'];
        if (!in_array($enrollStatus, $enrollStatuses))
            return $this->errorResponse("Invalid enroll status.");

        $paymentType = $item['payment type'];
        if (!in_array($paymentType, $paymentTypes))
            return $this->errorResponse("Invalid payment type.");

        $payslipType = $item['payslip type'];
        if (!in_array($payslipType, $payslipTypes))
            return $this->errorResponse("Invalid payslip type");

        $payslipFor = $item['payslip for'];
        if (!in_array($payslipFor, $payslipForEnums))
            return $this->errorResponse("Invalid payslip for");

        $paymentFrequency = null;
        if ($stipend) {
            $paymentFrequency = $item['payment frequency'];
            if (!in_array($paymentFrequency, $paymentFrequencies))
                return $this->errorResponse("Invalid payment frequency");
        }

        $allowOt = strtolower($item['allow ot'] ?? 'N');
        if (!in_array($allowOt, ['y', 'n']))
            return $this->errorResponse("Invalid allow ot");
        $allowOt = $allowOt == 'y';

        $startDate = $item['start date'];
        $endDate = $item['end date'];
        if (!$startDate || !$endDate)
            return $this->errorResponse("Please provide start date and end date");

        // $engStartDate = LaravelNepaliDate::from($startDate)->toEnglishDate();
        // $engEndDate = LaravelNepaliDate::from($endDate)->toEnglishDate();
        $engStartDate = $startDate;
        $engEndDate = $endDate;
        try {
            $nepStartDate = LaravelNepaliDate::from($engStartDate)->toNepaliDate();
            $nepEndDate = LaravelNepaliDate::from($engEndDate)->toNepaliDate();
        } catch (\Exception $e) {
            \logError("Error while converting date", $e);
            $nepDate = LaravelNepaliDate::from("2090-12-30")->toEnglishDate();
            return $this->errorResponse("We currently do not support dates more than $nepDate A.D. (2090-12-30 B.S)");
        }
        if (Carbon::parse($engStartDate)->gt(Carbon::parse($engEndDate)))
            return $this->errorResponse("start date can't be greater than end date");

        $insuranceExpiryDate = $item['insurance expiry date'] ?? null;
        $insurance = $item['insurance'] ?? null;
        $medicalInsuranceExpiryDate = $item['medical insurance expiry date'] ?? null;
        $medicalInsurance = $item['medical insurance'] ?? null;
        $houseInsuranceExpiryDate = $item['house insurance expiry date'] ?? null;
        $houseInsurance = $item['house insurance'] ?? null;
        if ($insurance && !$insuranceExpiryDate)
            return $this->errorResponse("Insurance expiry date is required when insurance is provided");
        if ($medicalInsurance && !$medicalInsuranceExpiryDate)
            return $this->errorResponse("Medical insurance expiry date is required when medical insurance is provided");
        if ($houseInsurance && !$houseInsuranceExpiryDate)
            return $this->errorResponse("House insurance expiry date is required when house insurance is provided");


        // Validate allowances
        $addedAllowance = "";
        $allowanceMetadata = [];
        foreach ($item as $header => $value) {
            $perk = $perks->get($header);
            if (!$perk)
                continue;
            if ($perk) {
                $perk = (object) $perk;
                if (!is_numeric($value)) {
                    return $this->errorResponse("Invalid numeric value for allowance $perk->name");
                }
                if ($value) {
                    if ($perk->is_global && $value != $perk->default_value) {
                        return $this->errorResponse("$perk->name is global. Invalid value. (Valid global value: $perk->default_value)");
                    }
                    $addedAllowance .= $perk->name . ", ";
                    $allowanceMetadata[$perk->id] = [$perk->name => $value];
                }
            }
        }

        // Return success response with validated data
        return $this->successResponse("", [
            'job' => $job,
            'employee' => $employee,
            'designation' => $designation,
            'band' => $band,
            'level' => $level,
            'employeeStatus' => $employeeStatus,
            'addedBasicSalary' => $addedBasicSalary,
            'startDate' => $nepStartDate,
            'endDate' => $nepEndDate,
            'insuranceExpiryDate' => $insuranceExpiryDate,
            'medicalInsuranceExpiryDate' => $medicalInsuranceExpiryDate,
            'houseInsuranceExpiryDate' => $houseInsuranceExpiryDate,
            'addedAllowance' => $addedAllowance,
            'allowanceMetadata' => $allowanceMetadata,
            'enrollStatus' => $enrollStatus,
            'paymentType' => $paymentType,
            'payslipType' => $payslipType,
            'payslipFor' => $payslipFor,
            'paymentFrequency' => $paymentFrequency,
            'allowOt' => $allowOt,
            'nonCashDeduction' => $nonCashDeduction,
        ]);
    }

    /**
     * for creating payslip from excel
     * @param array $data data in the form of [[key: value],[key: value]]
     * @param string $comment comment for uploading payslip
     * @return array if success ['status' => true, 'data' => ['payslips' => '', 'logs' => []]] and if error ['status' => false, 'logs' => []]
     */
    public function createPayslipsFromExcel(array $data, $comment = "")
    {
        $validationResults = $this->validatePayslipDataForExcel($data);
        $validPayslips = collect($validationResults)->filter(function ($result) {
            return $result['status'];
        });

        if ($validPayslips->isEmpty()) {
            return $this->errorResponse("No valid payslips to create");
        }

        $validationMessage = "";
        $hasError = false;

        DB::beginTransaction();

        try {
            foreach ($validPayslips as $index => $validatedData) {
                $payslipData = $data[$index];
                $empCode = $payslipData['employee code'];
                $empName = $payslipData['name'];

                $validationMessage .= "Creating payslip of {$empCode}, {$empName}.<br />";

                $response = $this->createSinglePayslip($validatedData['data'], $payslipData, $comment);

                if ($response['status']) {
                    $validationMessage .= "Payslip of {$empCode} created successfully.<br />";
                    $validationMessage .= $response['message'] . "<br />";

                    $ticket = $response['data'];
                    $validationMessage .=
                        "<a href='" .
                        route('ticketPage', ['workflow' => $ticket->workflow, 'requestId' => $ticket->id]) .
                        "' style='color: blue; text-decoration: underline' target='_blank'>View Payslip</a><br /><br />";
                } else {
                    $validationMessage .= "<span class='text-danger fw-bold'>Error: " . $response['message'] . "</span><br/>";
                    $hasError = true;
                    break;
                }
            }

            if ($hasError) {
                $validationMessage .= "<span class='text-danger fw-bold'>An error occurred. All created payslips are being reverted.</span>";
                DB::rollBack();
                return $this->errorResponse("Error occurred during payslip creation. All changes reverted. " . $response['message']);
            }

            DB::commit();
            return $this->successResponse("Payslips created successfully", ['validation' => $validationMessage]);
        } catch (\Exception $e) {
            DB::rollBack();
            $validationMessage .= "<span class='text-danger fw-bold'>An unexpected error occurred. All created payslips are being reverted.</span><br />";
            logError("Error while creating payslips", $e);
            return $this->errorResponse("Error creating payslips: " . $e->getMessage() . "All changes are reverted", ['validation' => $validationMessage]);
        }
    }

    /**
     * Create single payslip for excel upload
     * @param array $validatedData
     * @param array $rawData data that are not validated and fetched directly from excel
     * @param string $comment
     */
    private function createSinglePayslip(array $validatedData, array $rawData, $comment)
    {
        $payslipRequestResponse = $this->createPayslipRequest([
            'company_id' => $validatedData['employee']->company_id,
            'job_id' => $validatedData['job']?->id,
            'designation_id' => $validatedData['designation']?->id,
            'band_id' => $validatedData['band']?->id,
            'level_id' => $validatedData['level']?->id,
            'added_basic_salary' => $validatedData['addedBasicSalary'] ?? 0,
            'employee_id' => $validatedData['employee']->id,
            'allowance_metadata' => $validatedData['allowanceMetadata'],
            'enroll_status' => $validatedData['enrollStatus'],
            'employee_status_id' => $validatedData['employee']->employee_status_id,
            'stipend' => $rawData['stipend'] ?? null,
            'cit' => $rawData['cit'] ?? null,
            'insurance' => $rawData['insurance'] ?? null,
            'insurance_expiry_date' => $validatedData['insuranceExpiryDate'],
            'medical_insurance' => $rawData['medical insurance'] ?? null,
            'medical_insurance_expiry_date' => $validatedData['medicalInsuranceExpiryDate'],
            'house_insurance' => $rawData['house insurance'] ?? null,
            'house_insurance_expiry_date' => $validatedData['houseInsuranceExpiryDate'],
            'allow_ot' => $validatedData['allowOt'],
            'payment_type' => $validatedData['paymentType'],
            'payslip_type' => $validatedData['payslipType'],
            'payslip_for' => $validatedData['payslipFor'],
            'payment_frequency' => $validatedData['paymentFrequency'],
            'non_cash_deduction' => $validatedData['nonCashDeduction'] ?? 0,
            'payslip_remarks' => $rawData['remarks'] ?? 'Uploaded',
            'start_date_nep' => $validatedData['startDate'],
            'stop_date_nep' => $validatedData['endDate'],
            'verifier_id' => null,
        ], sendNotification: false);

        if (!$payslipRequestResponse['status']) {
            return $this->errorResponse($payslipRequestResponse['message']);
        }

        $payslipApprovalTicket = $payslipRequestResponse['data'];
        $payslipApprovalTicket->transitionTo(
            WorkflowState::UPLOADED,
            $comment ?? 'Uploaded from excel',
            metadata: [
                'initiator_id' => currentEmployee()->id,
                'initiator_role' => "Uploader",
                'next_owner_id' => null,
                'next_owner_role' => null,
            ]
        );

        $this->approvePayslip($payslipApprovalTicket);

        $requestTicket = RequestTicket::where([
            ['model_type', get_class($payslipApprovalTicket)],
            ['model_id', $payslipApprovalTicket->id]
        ])->first();

        if (!$requestTicket) {
            return $this->errorResponse("Request ticket not found for the payslip. Contact service provider");
        }

        $requestTicket->update([
            'state' => WorkflowState::UPLOADED,
            'current_owner_id' => null,
            'current_owner_role' => null,
        ]);

        return $this->successResponse("Allowance added: " . $validatedData['addedAllowance'], $payslipApprovalTicket);
    }

    private function createPayslipRequest($data, $sendNotification = true)
    {
        $grade_structure = GradeStructure::where([
            ['grade_id', $data['designation_id']],
            ['band_id', $data['band_id']],
            ['pgrade_id', $data['level_id']]
        ])->first();
        // if (!$grade_structure) return $this->errorResponse("Grade structure  not found for the selected designation, band and level");
        // $ssf_employer = $grade_structure->basic_salary * Constant::SSF_EMPLOYER;
        // $ssf_employee = $grade_structure->basic_salary * Constant::SSF_EMPLOYEE;
        $ssf_employer = in_array($data['payslip_for'], $this->showStipendFor) ? 0 : $grade_structure?->first()->basic_salary * Constant::SSF_EMPLOYER;
        $ssf_employee = in_array($data['payslip_for'], $this->showStipendFor) ? 0 : $grade_structure?->first()->basic_salary * Constant::SSF_EMPLOYEE;

        if (is_string($data['allowance_metadata'])) {
            $data['allowance_metadata'] = json_decode($data['allowance_metadata']);
        }
        // allowance_metadata form:
        // {id: {name: amount}, id: {name: amount}}
        foreach ($data['allowance_metadata'] ?? [] as $id => $perks) {
            foreach ($perks as $name => $_amount) {
                $perk = Perk::where([['name', $name], ['id', $id]])->first();
                if (!$perk) {
                    return $this->errorResponse("Perk with id $id and name $name not found");
                }
            }
        }

        $employee = Employee::with('company:id,name', 'organizationInfo.outsource_company:id,name', 'organizationInfo.region:id,name', 'organizationInfo.branch:id,name', 'organizationInfo.subBranch:id,name', 'organizationInfo.department:id,name', 'organizationInfo.unit:id,name')->where('employees.id', $data['employee_id'])->first();
        $activePayslip = $employee->activePaySlipList->first();
        $designation = Designation::where('id', $activePayslip->designation_id ?? "")->first();
        $band = EmployeeBand::where('id', $activePayslip->band_id ?? "")->first();
        $job = Job::where('id', $activePayslip->job_id ?? "")->first();
        $level = EmployeePgrade::where('id', $activePayslip->level_id ?? "")->first();

        $grade_structure_id = $grade_structure?->id ?? null;
        $params = [
            'company_id' => $data['company_id'],
            'job_id' => $data['job_id'],
            'designation_id' => $data['designation_id'] != '' ? $data['designation_id'] : null,
            'band_id' => $data['band_id'],
            'pgrade_id' => $data['level_id'],
            'grade_structure_id' => $grade_structure_id,
            'verifier_id' => $data['verifier_id'],
            'employee_id' => $data['employee_id'],
            'allowance_metadata' => json_encode($data['allowance_metadata']),
            'non_cash_deduction' => $data['non_cash_deduction'] ?? 0,
            'enroll_status' => $data['enroll_status'],
            'employee_status_id' => $data['employee_status_id'],
            'doj_inhouse' => $data['doj_inhouse'] ?? null,
            'stipend' => $data['stipend'] ? $data['stipend'] : 0,
            'cit' => $data['cit'] ? $data['cit'] : 0,
            'ssf_employer' => $ssf_employer,
            'ssf_employee' => $ssf_employee,
            'insurance' => $data['insurance'] ? $data['insurance'] : 0,
            'insurance_expiry_date' => $data['insurance_expiry_date'],
            'medical_insurance' => $data['medical_insurance'] ?? false ? $data['medical_insurance'] : 0,
            'medical_insurance_expiry_date' => $data['medical_insurance_expiry_date'] ?? null,
            'house_insurance' => $data['house_insurance'] ?? false ? $data['house_insurance'] : 0,
            'house_insurance_expiry_date' => $data['house_insurance_expiry_date'] ?? null,
            'allow_ot' => $data['allow_ot'],
            'payment_type' => $data['payment_type'],
            'payslip_type' => $data['payslip_type'],
            'payslip_for' => $data['payslip_for'],
            'payment_frequency' => $data['payment_frequency'],
            'remarks' => $data['payslip_remarks'] ?? "",
            'start_date_eng' => (LaravelNepaliDate::from($data['start_date_nep']))->toEnglishDate(),
            'stop_date_eng' => (LaravelNepaliDate::from($data['stop_date_nep']))->toEnglishDate(),
            'start_date_nep' => $data['start_date_nep'],
            'stop_date_nep' => $data['stop_date_nep'],
            'documents' => $data['documents'] ?? [],
            'employee_ticket_id' => $data['employee_ticket_id'] ?? null,
            'position_meta' => json_encode([
                "company" => [
                    "id" => $employee->company->id ?? "",
                    "name" => $employee->company->name ?? "",
                ],
                "outsourceCompany" => [
                    "id" => $employee->organizationInfo->outsource_company->id ?? "",
                    "name" => $employee->organizationInfo->outsource_company->name ?? "",
                ],
                "region" => [
                    "id" => $employee->organizationInfo->region->id ?? "",
                    "name" => $employee->organizationInfo->region->name ?? "",
                ],
                "branch" => [
                    "id" => $employee->organizationInfo->branch->id ?? "",
                    "name" => $employee->organizationInfo->branch->name ?? "",
                ],
                "sub_branch" => [
                    "id" => $employee->organizationInfo->subBranch->id ?? "",
                    "name" => $employee->organizationInfo->subBranch->name ?? "",
                ],
                "department" => [
                    "id" => $employee->organizationInfo->department->id ?? "",
                    "name" => $employee->organizationInfo->department->name ?? "",
                ],
                "unit" => [
                    "id" => $employee->organizationInfo->unit->id ?? "",
                    "name" => $employee->organizationInfo->unit->name ?? "",
                ],
                "job" => [
                    "id" => $job->id ?? "",
                    "name" => $job->name ?? "",
                ],
                "designation" => [
                    "id" => $designation->id ?? "",
                    "title" => $designation->title ?? "",
                ],
                "band" => [
                    "id" => $band->id ?? "",
                    "name" => $band->name ?? "",
                ],
                "level" => [
                    "id" => $level->id ?? "",
                    "name" => $level->name ?? "",
                ],
            ])
        ];

        DB::beginTransaction();
        try {
            if (($data['added_basic_salary'] ?? 0) > 0) {
                $salaryDetails = [
                    "basic_salary"  => $grade_structure->basic_salary,
                    "allowance"     => $grade_structure->allowance,
                ];
                $salaryDetails["basic_salary"] += $data['added_basic_salary'] ?? 0;
                $salaryDetails["ssf"] = $salaryDetails["basic_salary"] * Constant::SSF_167;
                $salaryDetails["gratuity"] = $salaryDetails["basic_salary"] * Constant::SSF_833;
                $salaryDetails["pf"] = $salaryDetails["basic_salary"] * Constant::SSF_10;
                $salaryDetails["gross_salary"] = $salaryDetails["basic_salary"] + $salaryDetails["ssf"] + $salaryDetails["gratuity"] + $salaryDetails["pf"] + $salaryDetails['allowance'];
                $salaryDetails["ctc"] = $salaryDetails["gross_salary"];
                $bandGradeData = [
                    'grade_id' => $data['designation_id'],
                    'band_id' => $data['band_id'],
                    'pgrade_id' => $data['level_id'],
                    'original_basic_salary' => $grade_structure->basic_salary,
                    'added_basic_salary' => $data['added_basic_salary'] ?? 0,
                    'basic_salary' => $salaryDetails["basic_salary"],
                    'original_pf' => $grade_structure->pf,
                    'pf' => $salaryDetails["pf"],
                    'original_ssf' => $grade_structure->ssf,
                    'ssf' => $salaryDetails["ssf"],
                    'allowance' => $salaryDetails['allowance'],
                    'original_gross_salary' => $grade_structure->gross_salary,
                    'gross_salary' => $salaryDetails["gross_salary"],
                    'original_gratuity' => $grade_structure->gratuity,
                    'gratuity' => $salaryDetails['gratuity'],
                    'original_ctc' => $grade_structure->ctc,
                    'ctc' => $salaryDetails["ctc"],
                    'company_id' => $data['company_id'],

                ];
                $bandGradeStructure = BandedGradeStructure::create($bandGradeData);
                $params['banded_grade_structure_id'] = $bandGradeStructure->id;
            }
            $payslipApprovalTicket = PayslipRequestModel::create($params);
            $this->ticketRepo->createRequestTicket($payslipApprovalTicket, [
                'current_owner_id' => $data['verifier_id'],
                'employee_id' => $data['employee_id'],
                'documents' => $data['documents'] ?? []
            ], $sendNotification);
            DB::commit();
            return $this->responseHelper(true, "Payslip request created successfully", $payslipApprovalTicket);
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while creating payslip request", $e);
            return $this->responseHelper(false, "Error while creating payslip request. " . print_r($e->getMessage(), true));
        }
    }
}
