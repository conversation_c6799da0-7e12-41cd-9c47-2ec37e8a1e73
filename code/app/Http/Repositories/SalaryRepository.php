<?php

namespace App\Http\Repositories;

use App\Models\configs\OutsourceCompany;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\AdvanceSalaryType;
use App\Http\Helpers\Enums\BypassType;
use App\Http\Helpers\Enums\DesignationRoles;
use App\Http\Helpers\Enums\MarriedStatus;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Models\Payroll\AdvanceSalary;
use App\Models\Payroll\AttendanceCount;
use App\Models\Payroll\BypassSSFCalculation;
use App\Models\Payroll\FemaleTaxRebate;
use App\Models\Payroll\Payment;
use App\Models\Payroll\PaymentDeductionMapping;
use App\Models\Payroll\PaymentPerksMapping;
use App\Models\Payroll\PayrollBypassEmployee;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\Perk;
use App\Models\Payroll\SalaryAdjustment;
use App\Models\Payroll\SalaryCalculationStopDate;
use App\Traits\NepaliCalendarTrait;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Support\Facades\DB;
use PermissionList;

class SalaryRepository extends Repository
{
    use NepaliCalendarTrait;

    public $nep_year, $nep_month;

    public $deduction_limit = Constant::ANNUAL_TAXABLE_INCOME_DEDUCTION_LIMIT;
    public $insurance_limit = Constant::INSURANCE_DEDUCTION_LIMIT;
    public $medical_insurance_limit = Constant::MEDICAL_INSURANCE_DEDUCTION_LIMIT;
    public $house_insurance_limit = Constant::HOUSE_INSURANCE_DEDUCTION_LIMIT;
    public $ssf_contribution_multiplier = Constant::SSF_EMPLOYEE;
    public $ssf_deduction_multiplier = Constant::SSF_DEDUCTION_MULTIPLIER;
    public $ssf_deduction_multiplier_for_tax = Constant::SSF_DEDUCTION_MULTIPLIER;
    public $min_ssf_amount = Constant::MIN_SSF_AMOUNT;

    public $emergency_fund_deduction = [
        'new_employee' => 500,
        'new_manager_and_above' => 1000,
    ];

    public $stipendPayslip = Constant::STIPEND_FOR;

    public $digitsAfterDecimal = 2;

    public $fiscalYearStartWithDay, $fiscalYearEndWithDay;

    public function generateSalary($nep_month, $fiscalYearId = null, array $employeeIds = [], $filter = "all")
    {
        if (vianetHrm()) $this->ssf_deduction_multiplier_for_tax = Constant::SSF_DEDUCTION_MULTIPLIER_VIANET;
        $fiscalYear = $fiscalYearId ? FiscalYear::where('id', $fiscalYearId)->first() : FiscalYear::where('is_active', true)->first();

        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        $fiscalYearStart = "{$start_date_year}-{$start_date_month}";
        $fiscalYearStartWithDay = "{$start_date_year}-{$start_date_month}-{$start_date_day}";
        $this->fiscalYearStartWithDay = $fiscalYearStartWithDay;

        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);
        $fiscalYearEnd = "{$end_date_year}-{$end_date_month}";
        $fiscalYearEndWithDay = "{$end_date_year}-{$end_date_month}-{$end_date_day}";
        $this->fiscalYearEndWithDay = $fiscalYearEndWithDay;

        $this->nep_year = $start_date_year;
        $this->nep_month = $nep_month;
        // Initialize year according to selected month
        if ($this->nep_month < $start_date_month) {
            $this->nep_year = $end_date_year;
        }

        $yearAndMonth = new DateTime("{$this->nep_year}-{$this->nep_month}");
        $currentYearAndMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate("Y-m");

        // Check if the selected year and month is valid. Past and present year and month are valid.
        if ($yearAndMonth->format('Y-m') > $currentYearAndMonth) {
            // return ['status' => 0, 'message' => 'Unable to generate salary sheet of future date.', 'data' => []]; // return empty array if the selected year and month is greater than current year and month
        }

        $remaining_month = abs(((((int)$end_date_year - (int)$this->nep_year) * 12) + ((int)$end_date_month - (int)$this->nep_month)));

        $dateAfterMonthDecreased = $yearAndMonth->modify('-1 month');
        $newDate = $dateAfterMonthDecreased->format('Y-m');
        $previous_month = "";
        $prev_salary_model = [];

        // Check if previous month exist and get the previous month payment data.
        if ($newDate >= $fiscalYearStart && $newDate <= $fiscalYearEnd) {
            $year = $dateAfterMonthDecreased->format('Y');
            $previous_month = $dateAfterMonthDecreased->format('m');
            $prev_salary_model = Payment::with(["payment_perks", "payment_perks.perks:id,name", "payment_deduction"])
                ->where('year', $year)
                ->where('month', $previous_month)
                ->get()
                ->keyBy('employee_id')
                ->toArray();
        }

        $total_month_days = $this->get_days_of_month($this->nep_year, (int)$this->nep_month); //LaravelNepaliDate::daysInMonth($this->nep_month, $this->nep_year);
        $last_day_of_selected_month = "$this->nep_year-$this->nep_month-$total_month_days";

        $attendance_query = AttendanceCount::where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month]]);
        $payslips_query = Payslip::with([
            'payslip_perks',
            'additional_income_mapping' => function ($query) use ($fiscalYearStartWithDay, $fiscalYearEndWithDay, $last_day_of_selected_month) {
                $query->where([['date_nep', '>=', "{$fiscalYearStartWithDay}"], ['date_nep', '<=', "{$fiscalYearEndWithDay}"], ['date_nep', '<=', "{$last_day_of_selected_month}"]]);
            },
            'additional_income_mapping.additional_income',
            'payslip_perks',
            'additional_deduction_mapping' => function ($query) use ($fiscalYearStartWithDay, $fiscalYearEndWithDay, $last_day_of_selected_month) {
                $query->where([['date_nep', '>=', "{$fiscalYearStartWithDay}"], ['date_nep', '<=', "{$fiscalYearEndWithDay}"], ['date_nep', '<=', "{$last_day_of_selected_month}"]]);
            },
            'additional_deduction_mapping.additional_deduction',
            'payslip_perks.perks:id,name',
            'employee:id,first_name,middle_name,last_name,company_id,gender,mstat',
            'employee.organizationInfo:employee_id,employee_code,bank,bank_account_no,branch_id,department_id,doj,pan_no,designation_id,termination_date',
            'employee.company:id,name,code',
            'employee.outsource_company:outsource_company_id,name',
            'employee.branch:branch_id,name',
            'employee.department:department_id,name',
            'employee.designation:designation_id,title,role',
            'band:id,name',
            'level:id,name',
            'job:id,name'
        ])
            ->where('status', 'Active');
        // ->orWhere([['start_date_nep', '>=', "{$last_day_of_selected_month}"], ['stop_date_nep', '<=', "{$last_day_of_selected_month}"]])
        // ->where(function ($query) {
        //     $query->whereNull('payment_frequency')
        //         ->orWhere('payment_frequency', 'Day');
        // });

        // Company wise salary
        if (!permissionViewAll() && permissionViewCompany()) {
            $payslips_query->where('company_id', currentEmployee()?->company_id);
        }

        $salaryAdjustments = SalaryAdjustment::where([
            ['year', $this->nep_year],
            ['month', $this->nep_month]
        ])->get()  // Fetch the results as a collection
            ->groupBy('employee_id')  // Group the collection by employee_id
            ->map(function ($items) {
                return $items->groupBy(function ($item) {
                    return is_null($item->adjustment_id) ? $item->adjustment_heading : $item->adjustment_id;
                })->map(function ($itemsByGroup, $key) {
                    if (is_string($key)) {
                        return $itemsByGroup->first()->toArray();
                    } else {
                        return $itemsByGroup->first()->toArray();
                    }
                });
            })->toArray();

        $salaryAdjustmentsEmployeeIds = array_keys($salaryAdjustments);

        // Filter the employee ids if it is passed in the function.
        if (count($employeeIds)) {
            $payslips_query = $payslips_query->whereIn('employee_id', $employeeIds);
            $attendance_query = $attendance_query->whereIn('employee_id', $employeeIds);
            if ($filter == "adjustmentOnly") {
                $payslips_query = $payslips_query->whereIn('employee_id', $salaryAdjustmentsEmployeeIds);
            }
        }

        $payslips = $payslips_query->select('id', 'company_id', 'enroll_status', 'employee_id', 'band_id', 'pgrade_id', 'job_id', 'added_basic_salary', 'basic_salary', 'allowance', 'gratuity', 'stipend', 'pf', 'cit', 'ssf_employer', 'ssf_employee', 'insurance', 'medical_insurance', 'house_insurance', 'allowance_metadata', 'allow_ot', 'status', 'payment_type', 'payslip_for', 'payment_frequency', 'non_cash_deduction')
            ->get();

        $attendances = $attendance_query->get()
            ->keyBy('employee_id')
            ->toArray();

        if (!count($attendances ?? [])) {
            return ['status' => 0, 'message' => 'Generate the attendance data and try again.', 'data' => []];
        }

        $marriedStatusBypassEmployeeIds = PayrollBypassEmployee::with(['bypassType'])->whereHas('bypassType', function ($query) {
            $query->where('name', BypassType::DISABLE_MARITAL_STATUS);
        })->pluck('employee_id')->toArray();

        $stoppageEmployeeIds = SalaryCalculationStopDate::where('nep_start_date', '<=', "$this->nep_year-$this->nep_month-01")
            ->where('nep_stop_date', '>=', $last_day_of_selected_month)
            ->where('is_active', 1)
            ->pluck('employee_id')
            ->toArray();

        $marriedWomanFemaleTaxAllowedEmployeeIds = FemaleTaxRebate::where('deleted_at', null)->pluck('employee_id')->toArray();
        $bypassSSFCalculationEmployeeIds = BypassSSFCalculation::where('deleted_at', null)->pluck('employee_id')->toArray();

        $advanceSalaries = AdvanceSalary::where([
            ['is_settled', false],
            ['nep_year', '<=', $this->nep_year],
            ['nep_month', '<=', $this->nep_month]
        ])->get()->keyBy('employee_id')->toArray();
        
        $serviceCharge = OutsourceCompany::all()
                        ->pluck("service_charge","id")
                        ->toArray();

        $salary_structure = [];
        foreach ($payslips as $payslip) {
            if (!isset($attendances[$payslip->employee_id]) || in_array($payslip->employee_id, $stoppageEmployeeIds)) continue; // Check if employee has attendance data or employee salary is freezed.
            $employee_attendance = $attendances[$payslip->employee_id];
            $total_salary_days = $employee_attendance['salary_days'] - $employee_attendance['deduction_days'];
            $total_absent_days = $employee_attendance['absent_days'];
            $total_attendance_days = $employee_attendance['attendance_days'];
            $total_present_days = $employee_attendance['present_days'];
            $total_day_off = $employee_attendance['day_off'];
            $total_log_hour = $employee_attendance['log_hour'];
            $_total_month_days = $total_month_days;

            if (fedexHrm()) {
                $total_salary_days = (30 / $_total_month_days) * ($employee_attendance['salary_days'] - $employee_attendance['deduction_days']);
                $_total_month_days = 30;
            }

            $prev_salary = $prev_salary_model[$payslip->employee_id] ?? [];
            $hasPrevSalary = count($prev_salary) ? true : false;
            $isEmployeeTerminated = $payslip?->employee?->organizationInfo?->termination_date ? true : false;
            $employeeType = LaravelNepaliDate::from($payslip->employee->organizationInfo->doj)->toNepaliDate('Y-m') == "{$this->nep_year}-{$this->nep_month}" ? 'new' : 'old';

            $isNewEmployee = $employeeType == 'new' ? true : false;
            $adjustment = in_array($filter, ['withAdjustment', 'adjustmentOnly', 'all']) ? $salaryAdjustments[$payslip->employee_id] ?? [] : [];
            $calculatedTaxableIncome = $this->calculateTaxableIncome($prev_salary, $payslip, $_total_month_days, $total_salary_days, $remaining_month, $total_present_days, $total_log_hour, $hasPrevSalary, $isNewEmployee, $isEmployeeTerminated, $adjustment, $bypassSSFCalculationEmployeeIds);
            $calculatedTaxableAmount = $this->calculateTaxableAmount($prev_salary, $payslip, $_total_month_days, $total_salary_days, $remaining_month, $calculatedTaxableIncome['annual_taxable_income'], $total_present_days, $total_log_hour, $hasPrevSalary, $isNewEmployee, $isEmployeeTerminated, $adjustment, $bypassSSFCalculationEmployeeIds);

            // Bypass married status
            $mstat = $payslip->employee->mstat ?? MarriedStatus::UNMARRIED;
            if (in_array($payslip->employee_id, $marriedStatusBypassEmployeeIds)) {
                $mstat = MarriedStatus::UNMARRIED;
            }

            $calValue = [
                'mstat'                     => $mstat,
                'gender'                    => $payslip->employee->gender,
                'bypassSSFCalculation'      => in_array($payslip->employee_id, $bypassSSFCalculationEmployeeIds),
                'marriedWomanFemaleTaxRebate' => in_array($payslip->employee_id, $marriedWomanFemaleTaxAllowedEmployeeIds),
                'calculatedTaxableIncome'   => $calculatedTaxableIncome,
                'calculatedTaxableAmount'   => $calculatedTaxableAmount,
                'remainingMonths'           => $remaining_month,
            ];

            $remaining_month_for_tax = $remaining_month;
            if (in_array($payslip->payslip_for, $this->stipendPayslip)) $remaining_month_for_tax = 11; //For stipend employee always calculate tax with remaining month 11 as tax calculated for a year is paid in same month.

            $calculatedTax = $this->taxCalculation($this->nep_year, $this->nep_month, $calValue, $prev_salary, $payslip, $remaining_month_for_tax, $_total_month_days, $advanceSalaries);
            $salary_structure[] = [
                ...$payslip->toArray(),
                ...$calculatedTaxableAmount,
                ...$calculatedTaxableIncome,
                'year'                  => $this->nep_year,
                'month'                 => $this->nep_month,
                'fiscal_year'           => $fiscalYear->id,
                'remaining_month'       => $remaining_month_for_tax,
                'total_month_days'      => $_total_month_days,
                'total_salary_days'     => $total_salary_days,
                'total_absent_days'     => $total_absent_days,
                'total_attendance_days' => $total_present_days,
                'total_present_days'    => $total_attendance_days,
                'total_day_off'         => $total_day_off,
                'yearly_tax'            => $calculatedTax['yearly_tax'],
                'tax'                   => $calculatedTax['tax_for_this_month'],
                'current_sst_paid'      => $calculatedTax['current_sst_paid'],
                'remaining_tax'         => $calculatedTax['remaining_tax'],
                'paid_tds'              => $calculatedTax['paid_tds'],
                'paid_sst'              => $calculatedTax['paid_sst'] ?? 0,
                'advance_salary_deduct' => $calculatedTax['advance_salary_deduct'] ?? 0,
                'emergency_fund'        => $calculatedTax['calculatedEmergencyFund'],
                'cash_in_hand'          => $calculatedTax['cashInhandPerMonth'],
                'total_per_month'       => $calculatedTax['totalPerMonth'],
                'service_charge'        => $serviceCharge[$payslip?->employee?->outsource_company?->outsource_company_id] ?? 0
            ];
        }

        return ['status' => 1, 'message' => 'Salary calculated successfully.', 'data' => $salary_structure];
    }

    public function calculateTaxableIncome($prev_salary, $payslip, $total_month_days, $total_salary_days, $remaining_month, $total_present_days, $total_log_hour, $hasPrevSalary, $isNewEmployee, $isEmployeeTerminated, $adjustment, $bypassSSFCalculationEmployeeIds)
    {
        $checked_total_salary_days = $total_salary_days >= $total_month_days ? $total_month_days : $total_salary_days;
        $present_day_ratio = $checked_total_salary_days / $total_month_days;

        $prev_earned_basic_salary = ($prev_salary['earned_basic_salary'] ?? 0) + ($prev_salary['current_basic_salary'] ?? 0);
        $basic_salary_adjustment_amount = isset($adjustment['current_basic_salary']) ? $adjustment['current_basic_salary']['adjustment_amount'] : 0;
        $basic_salary = $this->pastPresentFutureCalculation($prev_earned_basic_salary, $payslip->basic_salary * $present_day_ratio + $basic_salary_adjustment_amount, 1, $payslip->basic_salary, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
        $prev_earned_ssf_contribution = ($prev_salary['earned_ssf'] ?? 0) + ($prev_salary['current_ssf'] ?? 0);
        $basic_salary_for_current_ssf = $payslip->basic_salary * $present_day_ratio + $basic_salary_adjustment_amount;
        $basic_salary_for_future_ssf = $this->ssf_contribution_multiplier * $payslip->basic_salary;
        if (in_array($payslip->employee_id, $bypassSSFCalculationEmployeeIds)) {
            $basic_salary_for_current_ssf = 0;
            $basic_salary_for_future_ssf = 0;
        }
        $ssf_contribution = $this->pastPresentFutureCalculation($prev_earned_ssf_contribution, $basic_salary_for_current_ssf, $this->ssf_contribution_multiplier, $basic_salary_for_future_ssf, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
        $prev_earned_allowance = ($prev_salary['earned_allowance'] ?? 0) + ($prev_salary['current_allowance'] ?? 0);
        $allowance_adjustment_amount = isset($adjustment['current_allowance']) ? $adjustment['current_allowance']['adjustment_amount'] : 0;
        $allowance = $this->pastPresentFutureCalculation($prev_earned_allowance, $payslip->allowance * $present_day_ratio + $allowance_adjustment_amount, 1, $payslip->allowance, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);

        $total_additional_allowance = 0.00;
        $total_additional_allowance_array = [];
        $earned_additional_allowance = [];
        foreach ($prev_salary['payment_perks'] ?? [] as $payment_perks) {
            if ($isNewEmployee) {
                break;
            }
            $total_prev_earning = round($payment_perks['earned_amount'] + $payment_perks['current_amount'], $this->digitsAfterDecimal);
            $total_additional_allowance += $total_prev_earning;
            $total_additional_allowance_array[$payment_perks['perks']['name']] = $total_prev_earning;
            $earned_additional_allowance[$payment_perks['perks']['name']] = $total_prev_earning;
        }

        $current_additional_allowance = [];
        $actual_additional_allowance = [];
        $future_additional_allowance = [];
        $earned_additional_allowance_sample = [];
        $total_current_month_additional_allowance = 0;
        foreach ($payslip->payslip_perks->toArray() ?? [] as $payslip_perks) {
            // For current month.
            $perks_adjustment_amount = $adjustment[$payslip_perks['perks_id']]['adjustment_amount'] ?? 0;
            $value = round($payslip_perks['amount'] * $present_day_ratio + $perks_adjustment_amount, $this->digitsAfterDecimal);
            $total_additional_allowance += $value;
            $total_current_month_additional_allowance += $value;
            $total_additional_allowance_array[$payslip_perks['perks']['name']] = ($total_additional_allowance_array[$payslip_perks['perks']['name']] ?? 0) + $value;
            $current_additional_allowance[$payslip_perks['perks']['name']] = $value;

            // For future month. And only add to total if the employee is not terminated.
            if (!$isEmployeeTerminated) {
                $total_additional_allowance += round($payslip_perks['amount'] * $remaining_month, $this->digitsAfterDecimal);
                $total_additional_allowance_array[$payslip_perks['perks']['name']] = round(($total_additional_allowance_array[$payslip_perks['perks']['name']] ?? 0)  + $payslip_perks['amount'] * $remaining_month, $this->digitsAfterDecimal);
            }

            $future_additional_allowance[$payslip_perks['perks']['name']] = round($payslip_perks['amount'] * $remaining_month, $this->digitsAfterDecimal);
            if ($isEmployeeTerminated) {
                $future_additional_allowance[$payslip_perks['perks']['name']] = 0;
            }
            $actual_additional_allowance[$payslip_perks['perks']['name']] = round($payslip_perks['amount'], $this->digitsAfterDecimal);


            // if (isset($earned_additional_allowance) && count($earned_additional_allowance) == 0 && !$isNewEmployee) {
            //     $payslip_perks_amount = round($payslip_perks['amount'] * (11 - $remaining_month), $this->digitsAfterDecimal);
            //     $earned_additional_allowance_sample[$payslip_perks['perks']['name']] = $payslip_perks_amount;
            //     $total_additional_allowance += $payslip_perks_amount;
            //     $total_additional_allowance_array[$payslip_perks['perks']['name']] = ($total_additional_allowance_array[$payslip_perks['perks']['name']] ?? 0)  + $payslip_perks_amount;
            // }
        }
        $earned_additional_allowance = count($earned_additional_allowance_sample) ? $earned_additional_allowance_sample : $earned_additional_allowance;

        $additional_income = [];
        $current_month_additional_income = [];
        $total_additional_income = 0.00;
        $total_current_month_additional_income = 0.00;
        foreach ($payslip->additional_income_mapping as $add_income_mapping) {
            // if($payslip->employee_id=5916) dd($payslip->additional_income_mapping);
            if ($add_income_mapping->date_nep >= $this->fiscalYearStartWithDay && $add_income_mapping->date_nep <= $this->fiscalYearEndWithDay) {
                $total_additional_income += $add_income_mapping->gross_amount;
                $additional_income[] = [
                    'name'      => $add_income_mapping->additional_income->name,
                    'amount'    => round($add_income_mapping->gross_amount, $this->digitsAfterDecimal),
                    'date_nep'  => $add_income_mapping->date_nep,
                    'date_eng'  => $add_income_mapping->date_eng
                ];

                if (LaravelNepaliDate::from($add_income_mapping->date_eng)->toNepaliDate('m') == $this->nep_month) {
                    $total_current_month_additional_income += round($add_income_mapping->gross_amount, $this->digitsAfterDecimal);
                    $current_month_additional_income[] = [
                        'name'      => $add_income_mapping->additional_income->name,
                        'amount'    => round($add_income_mapping->gross_amount, $this->digitsAfterDecimal),
                        'date_nep'  => $add_income_mapping->date_nep,
                        'date_eng'  => $add_income_mapping->date_eng
                    ];
                }
            }
        }

        $stipend = [
            'actual'    => 0,
            'earned'    => 0,
            'current'   => 0,
            'future'    => 0,
            'total'     => 0,
        ];
        $current_month_taxable_income = round($basic_salary['current'] + $ssf_contribution['current'] + $allowance['current'] + $total_current_month_additional_allowance + $total_current_month_additional_income, $this->digitsAfterDecimal);
        $annual_taxable_income = round($basic_salary['total'] + $ssf_contribution['total'] + $allowance['total'] + $total_additional_allowance + $total_additional_income, $this->digitsAfterDecimal);
        if (in_array($payslip->payslip_for, $this->stipendPayslip)) {
            $earned_stipend = $prev_salary['earned_stipend'] ?? 0;
            if (strtolower($payslip->payment_frequency) == 'day') {
                $current_stipend = $payslip->stipend * $total_present_days;

                $current_month_taxable_income += round($current_stipend, $this->digitsAfterDecimal);
                $total_stipend = $current_stipend * 12;
                $annual_taxable_income += round($total_stipend, $this->digitsAfterDecimal);

                $stipend = [
                    'actual'    => $payslip->stipend,
                    'earned'    => $earned_stipend,
                    'current'   => $current_stipend,
                    'total'     => $total_stipend,
                ];
            }
            if (strtolower($payslip->payment_frequency) == 'hour') {
                $current_stipend = $payslip->stipend * $total_log_hour;

                $current_month_taxable_income += round($current_stipend, $this->digitsAfterDecimal);
                $total_stipend = $current_stipend * 12;
                $annual_taxable_income += round($total_stipend, $this->digitsAfterDecimal);

                $stipend = [
                    'actual'    => $payslip->stipend,
                    'earned'    => $earned_stipend,
                    'current'   => $current_stipend,
                    'total'     => $total_stipend,
                ];
            }
            if (strtolower($payslip->payment_frequency) == 'month') {
                $current_stipend = $payslip->stipend * $present_day_ratio;

                $current_month_taxable_income += round($current_stipend, $this->digitsAfterDecimal);
                $total_stipend = $current_stipend * 12;
                $annual_taxable_income += round($total_stipend, $this->digitsAfterDecimal);

                $stipend = [
                    'actual'    => $payslip->stipend,
                    'earned'    => $earned_stipend,
                    'current'   => $current_stipend,
                    'total'     => $total_stipend,
                ];
            }
        }

        return [
            'basic_salary'      => $basic_salary,
            'ssf_contribution'  => $ssf_contribution,
            'allowance'         => $allowance,
            'additional_allowance' => [
                'actual'    => $actual_additional_allowance,
                'earned'    => $earned_additional_allowance,
                'current'   => $current_additional_allowance,
                'future'    => $future_additional_allowance,
                'total'     => $total_additional_allowance_array,
            ],
            'additional_income' => $additional_income,
            'stipend' => $stipend,
            'current_month_additional_income' => $current_month_additional_income,
            'total_current_month_additional_income' => $total_current_month_additional_income,
            'current_month_taxable_income' => $current_month_taxable_income,
            'annual_taxable_income' => $annual_taxable_income
        ];
    }

    public function calculateTaxableAmount($prev_salary, $payslip, $total_month_days, $total_salary_days, $remaining_month, $annual_taxable_income, $total_present_days, $total_log_hour, $hasPrevSalary, $isNewEmployee, $isEmployeeTerminated, $adjustment, $bypassSSFCalculationEmployeeIds)
    {
        $checked_total_salary_days = $total_salary_days >= $total_month_days ? $total_month_days : $total_salary_days;
        $present_day_ratio = $checked_total_salary_days / $total_month_days;

        $cit_adjustment_amount = isset($adjustment['current_cit_deduction']) ? $adjustment['current_cit_deduction']['adjustment_amount'] : 0;
        $prev_deducted_cit = ($prev_salary['past_cit_deduction'] ?? 0) + ($prev_salary['current_cit_deduction'] ?? 0);
        $cit = $this->pastPresentFutureCalculation($prev_deducted_cit, $payslip->cit + $cit_adjustment_amount, 1, $payslip->cit, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
        $prev_deducted_ssf = ($prev_salary['past_ssf_deduction'] ?? 0) + ($prev_salary['current_ssf_deduction'] ?? 0);
        $prev_deducted_ssf_for_tax = ($prev_salary['past_ssf_deduction_for_tax'] ?? 0) + ($prev_salary['current_ssf_deduction_for_tax'] ?? 0);

        $basic_salary_adjustment_amount = 0; //isset($adjustment['current_basic_salary']) ? $adjustment['current_basic_salary']['adjustment_amount'] : 0; //need retest
        $current_value = ($payslip->basic_salary * $present_day_ratio) + $basic_salary_adjustment_amount;
        if (vianetHrm()) {
            $current_value = $payslip->basic_salary + $basic_salary_adjustment_amount;
        }
        $future_ssf_value = $payslip->basic_salary * $this->ssf_deduction_multiplier;
        $future_ssf_value_for_tax = $payslip->basic_salary * $this->ssf_deduction_multiplier_for_tax;

        if (in_array($payslip->employee_id, $bypassSSFCalculationEmployeeIds)) {
            $current_value = 0;
            $future_ssf_value = 0;
            $future_ssf_value_for_tax = 0;
        }
        $ssf_deduction = $this->pastPresentFutureCalculation($prev_deducted_ssf, $current_value, $this->ssf_deduction_multiplier, $future_ssf_value, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
        $ssf_deduction_for_tax = $this->pastPresentFutureCalculation($prev_deducted_ssf_for_tax, $current_value, $this->ssf_deduction_multiplier_for_tax, $future_ssf_value_for_tax, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);

        if ($isEmployeeTerminated || $isNewEmployee) {
            $current_value = $payslip->basic_salary * $present_day_ratio + $basic_salary_adjustment_amount;
            $future_ssf_value = $payslip->basic_salary * $this->ssf_deduction_multiplier;
            $future_ssf_value_for_tax = $payslip->basic_salary * $this->ssf_deduction_multiplier_for_tax;

            if (in_array($payslip->employee_id, $bypassSSFCalculationEmployeeIds)) {
                $current_value = 0;
                $future_ssf_value = 0;
                $future_ssf_value_for_tax = 0;
            }
            $ssf_deduction = $this->pastPresentFutureCalculation($prev_deducted_ssf, $current_value, $this->ssf_deduction_multiplier, $future_ssf_value, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
            $ssf_deduction_for_tax = $this->pastPresentFutureCalculation($prev_deducted_ssf_for_tax, $current_value, $this->ssf_deduction_multiplier_for_tax, $future_ssf_value_for_tax, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
        }

        if (!in_array($payslip->payslip_for, $this->stipendPayslip) && !vianetHrm()) {
            $ssf_deduction['current'] = max($this->min_ssf_amount, $ssf_deduction['current']);
            $ssf_deduction_for_tax['current'] = max($this->min_ssf_amount, $ssf_deduction['current']);
        }

        if (in_array($payslip->employee_id, $bypassSSFCalculationEmployeeIds)) {
            $ssf_deduction['current'] = 0;
            $ssf_deduction_for_tax['current'] = 0;
        }

        $deductionAmount = 0;
        if (fedexHrm()) {
            $prev_non_cash_deduction = 0;
            foreach ($prev_salary['payment_deduction'] ?? [] as $prev_salary_deduction) {
                if ($prev_salary_deduction['name'] == 'Non Cash Deduction') {
                    $prev_non_cash_deduction = $prev_salary_deduction['deducted_amount'] + $prev_salary_deduction['current_amount'];
                }
            }
            $non_cash_deduction = $this->pastPresentFutureCalculation($prev_non_cash_deduction, $payslip->non_cash_deduction, 1, $payslip->non_cash_deduction, $remaining_month, $remaining_month, $hasPrevSalary, $isEmployeeTerminated);
            $deductionAmount = $non_cash_deduction['total'];
            $deduction = [
                [
                    'name'      => 'Non Cash Deduction',
                    ...$non_cash_deduction
                ]
            ];
        }

        $sum_of_cit_and_ssf = $cit['total'] + $ssf_deduction_for_tax['total'];
        $one_third_of_taxable_income = $annual_taxable_income / 3;
        $min_deductible = min($sum_of_cit_and_ssf, $this->deduction_limit, $one_third_of_taxable_income);

        $insurance_adjustment_amount = isset($adjustment['insurance']) ? $adjustment['insurance']['adjustment_amount'] : 0;
        $insurance = $payslip->insurance + $insurance_adjustment_amount;

        $medical_insurance_adjustment_amount = isset($adjustment['medical_insurance']) ? $adjustment['medical_insurance']['adjustment_amount'] : 0;
        $medical_insurance = $payslip->medical_insurance + $medical_insurance_adjustment_amount;

        $house_insurance_adjustment_amount = isset($adjustment['house_insurance']) ? $adjustment['house_insurance']['adjustment_amount'] : 0;
        $house_insurance = $payslip->house_insurance + $house_insurance_adjustment_amount;

        $deductible_life_insurance = min($this->insurance_limit, $insurance);
        $deductible_medical_insurance = min($this->medical_insurance_limit, $medical_insurance);
        $deductible_house_insurance = min($this->house_insurance_limit, $house_insurance);
        $deductible_insurance = $deductible_life_insurance + $deductible_medical_insurance + $deductible_house_insurance;

        $additional_deduction = [];
        $current_month_additional_deduction = [];
        $total_additional_deduction = 0.00;
        $total_current_month_additional_deduction = 0.00;
        foreach ($payslip->additional_deduction_mapping as $add_deduction_mapping) {
            if ($add_deduction_mapping->date_nep >= $this->fiscalYearStartWithDay && $add_deduction_mapping->date_nep <= $this->fiscalYearEndWithDay) {
                $total_additional_deduction += $add_deduction_mapping->amount;
                $additional_deduction[] = [
                    'name'      => $add_deduction_mapping->additional_deduction->name,
                    'amount'    => round($add_deduction_mapping->amount, $this->digitsAfterDecimal),
                    'date_nep'  => $add_deduction_mapping->date_nep,
                    'date_eng'  => $add_deduction_mapping->date_eng
                ];

                if (LaravelNepaliDate::from($add_deduction_mapping->date_eng)->toNepaliDate('m') == $this->nep_month) {
                    $total_current_month_additional_deduction += round($add_deduction_mapping->amount, $this->digitsAfterDecimal);
                    $current_month_additional_deduction[] = [
                        'name'      => $add_deduction_mapping->additional_deduction->name,
                        'amount'    => round($add_deduction_mapping->amount, $this->digitsAfterDecimal),
                        'date_nep'  => $add_deduction_mapping->date_nep,
                        'date_eng'  => $add_deduction_mapping->date_eng
                    ];
                }
            }
        }

        $annual_taxable_amount = $annual_taxable_income - $min_deductible - $deductible_insurance - $total_additional_deduction - $deductionAmount;

        return [
            'cit'                   => $cit,
            'ssf_deduction'         => $ssf_deduction,
            'ssf_deduction_for_tax' => $ssf_deduction_for_tax,
            'cit_ssf_sum'           =>  round($sum_of_cit_and_ssf, $this->digitsAfterDecimal),
            'one_third_of_taxable_income' => round($one_third_of_taxable_income, $this->digitsAfterDecimal),
            'deduction_limit'       => round($this->deduction_limit, $this->digitsAfterDecimal),
            'min_deductible'        => round($min_deductible, $this->digitsAfterDecimal),
            'insurance'             => round($insurance, $this->digitsAfterDecimal),
            'deductible_life_insurance'  => round($deductible_life_insurance, $this->digitsAfterDecimal),
            'medical_insurance'     => round($medical_insurance, $this->digitsAfterDecimal),
            'deductible_medical_insurance'  => round($deductible_medical_insurance, $this->digitsAfterDecimal),
            'house_insurance'     => round($house_insurance, $this->digitsAfterDecimal),
            'deductible_house_insurance'  => round($deductible_house_insurance, $this->digitsAfterDecimal),
            'deductible_insurance'  => round($deductible_insurance, $this->digitsAfterDecimal),
            'additional_deduction' => $additional_deduction,
            'current_month_additional_deduction' => $current_month_additional_deduction,
            'total_current_month_additional_deduction' => $total_current_month_additional_deduction,
            'deduction' => isset($deduction) ? $deduction : [],
            'annual_taxable_amount' => round($annual_taxable_amount, $this->digitsAfterDecimal)
        ];
    }

    public function pastPresentFutureCalculation($prev_earned_value, $current_value, $present_day_ratio, $future_value, $future_multiplier, $remaining_month, $hasPrevSalary = false, $isEmployeeTerminated = false)
    {
        $earned     = $prev_earned_value; //max(0, $prev_earned_value);
        $current    = $current_value * $present_day_ratio; //max(0, $current_value * $present_day_ratio);
        if (isset($prev_earned_value) && $prev_earned_value > 0) {
            $earned = $prev_earned_value; //max(0, $prev_earned_value);
        }
        // elseif ($remaining_month < 11) {
        //     if (!$hasPrevSalary) $earned = $current * (11 - $remaining_month);
        // }
        $future     = $future_value * $future_multiplier; //max(0, $future_value * $future_multiplier);
        if ($isEmployeeTerminated) $future = 0; // For terminated employee the future calculation will be 0;
        $total      = $earned + $current + $future;

        return [
            'actual'    => round($future_value, $this->digitsAfterDecimal),
            'earned'    => round($earned, $this->digitsAfterDecimal),
            'current'   => round($current, $this->digitsAfterDecimal),
            'future'    => round($future, $this->digitsAfterDecimal),
            'total'     => round($total, $this->digitsAfterDecimal),
        ];
    }

    public function getSlabStructure(): array
    {
        return array(
            'unmarried' => array(
                'first' => 500000,
                'second' => 700000,
                'third' => 1000000,
                'fourth' => 2000000,
                'fifth' => 5000000,
            ),
            'married' => array(
                'first' => 600000,
                'second' => 800000,
                'third' => 1100000,
                'fourth' => 2000000,
                'fifth' => 5000000,
            )
        );
    }

    public function calculateEmergencyFund($payslip, $total_month_days, $type = 'new')
    {
        $employeeDesignationRole = $payslip->employee->designation?->role;
        $rolesToCheck = [DesignationRoles::HOD, DesignationRoles::BranchManager];

        // Emergency fund calculation for newly joined employee.
        if ($type == 'new') {
            $deduction = $this->emergency_fund_deduction['new_employee'];
            if (in_array($employeeDesignationRole, $rolesToCheck) && !konnectHrm()) $deduction = $this->emergency_fund_deduction['new_manager_and_above'];
            return $deduction;
        }

        // Emergency fund calculation for existing employee.
        $one_day_salary = $payslip->basic_salary / $total_month_days;
        return $one_day_salary;
    }

    public function calculateTax($params, $paidTax = '', $duration = 'm')
    {
        $slabRange = $this->getSlabStructure();
        $return = 0;
        /* PF Calculation */
        // if (isset($params['type']) && $params['type'] == "pf") {
        //     if ($params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['first']) {
        //         $return = $params['calculatedTaxableAmount']['annual_taxable_amount'] * 0.01;
        //     } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['first'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['second']) {
        //         $preTaxAmount = $slabRange[$params['mstat']]['first'] * 0.01;
        //         $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['first']) * 0.1;
        //     } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['second'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['third']) {
        //         $preTaxAmount = ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1 + $slabRange[$params['mstat']]['first'] * 0.01;
        //         $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['second']) * 0.2;
        //     } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['third'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['fourth']) {
        //         $preTaxAmount = ($slabRange[$params['mstat']]['third'] -  $slabRange[$params['mstat']]['second']) * 0.2 + ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1 + $slabRange[$params['mstat']]['first'] * 0.01;
        //         $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['third']) * 0.3;
        //     } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['fourth']) {
        //         $preTaxAmount = ($slabRange[$params['mstat']]['fourth'] -  $slabRange[$params['mstat']]['third']) * 0.3
        //             + ($slabRange[$params['mstat']]['third'] -  $slabRange[$params['mstat']]['second']) * 0.2
        //             + ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1
        //             + $slabRange[$params['mstat']]['first'] * 0.01;
        //         $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['fourth']) * 0.36;
        //     }
        // }
        // /* SSF Calculation */ else {
        $calculateSST = ($params['calculatedTaxableAmount']['ssf_deduction_for_tax']['current'] == 0) && $params['bypassSSFCalculation'] && fedexHrm();
        $sst = $calculateSST ? ($slabRange[$params['mstat']]['first'] * 0.01) : 0;
        if ($params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['first']) {
            if ($duration == 'm' && $params['calculatedTaxableAmount']['ssf_deduction_for_tax']['current'] > 1) {
                $return = 0;
            } else {
                if ($calculateSST) {
                    // sst is a part of tax.
                    $sst = $params['calculatedTaxableAmount']['annual_taxable_amount'] * 0.01;
                    $return = 0;
                } else {
                    $sst = 0;
                    $return = $params['calculatedTaxableAmount']['annual_taxable_amount'] * 0.01;
                }
            }
        } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['first'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['second']) {
            $return = ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['first']) * 0.1;
        } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['second'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['third']) {
            $preTaxAmount = ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1;
            $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['second']) * 0.2;
        } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['third'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['fourth']) {
            $preTaxAmount = ($slabRange[$params['mstat']]['third'] -  $slabRange[$params['mstat']]['second']) * 0.2 + ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1;
            $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['third']) * 0.3;
        } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['fourth'] && $params['calculatedTaxableAmount']['annual_taxable_amount'] <= $slabRange[$params['mstat']]['fifth']) {
            $preTaxAmount = ($slabRange[$params['mstat']]['fourth'] -  $slabRange[$params['mstat']]['third']) * 0.3
                + ($slabRange[$params['mstat']]['third'] -  $slabRange[$params['mstat']]['second']) * 0.2
                + ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1;
            $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['fourth']) * 0.36;
        } elseif ($params['calculatedTaxableAmount']['annual_taxable_amount'] > $slabRange[$params['mstat']]['fifth']) {
            $preTaxAmount = ($slabRange[$params['mstat']]['fifth'] -  $slabRange[$params['mstat']]['fourth']) * 0.36
                + ($slabRange[$params['mstat']]['fourth'] -  $slabRange[$params['mstat']]['third']) * 0.3
                + ($slabRange[$params['mstat']]['third'] -  $slabRange[$params['mstat']]['second']) * 0.2
                + ($slabRange[$params['mstat']]['second'] -  $slabRange[$params['mstat']]['first']) * 0.1;
            $return = $preTaxAmount + ($params['calculatedTaxableAmount']['annual_taxable_amount'] - $slabRange[$params['mstat']]['fifth']) * 0.39;
        }

        if (strtolower($params['gender']) == 'female' && $params['mstat'] == 'unmarried') {
            $return = $return - $return * 0.1; //or $return*0.9
        } else if (strtolower($params['gender']) == 'female' && $params['mstat'] == 'married' && $params['marriedWomanFemaleTaxRebate']) {
            $return = $return - $return * 0.1;
        }

        // Adding SST in the tax calculation after female tax rebate is provided.
        $return += $sst;

        /* Monthly */
        if ($duration == 'm') {
            if (is_array($paidTax) && isset($paidTax['tax'])) {
                $return = $return - $paidTax['tax'];

                $return = $return / $params['remainingMonths'];
                $sst = $sst / $params['remainingMonths'];
            } else {
                $return = $return / 12;
                $sst = $sst / 12;
            }
        }
        if (isset($params['payment_type']) && strtolower($params['payment_type']) == 'daily') {
            if (strtolower($params['gender']) == 'male') {
                $return = $params['monthly_gross'] * 0.01;
                $sst = $return;
            } else {
                $val = $params['monthly_gross'] * 0.01;
                $return = $val - ($val * 0.1);
                $sst = $return;
            }
        }

        $tds = round($return, $this->digitsAfterDecimal);
        return [
            "sst" => $sst,
            "tds" => $tds,
        ];
    }

    function incomeMonth($salary, $type = 'cashInHand')
    {
        $total = 0;
        $total += $salary['calculatedTaxableIncome']['basic_salary']['current'];
        $total += $salary['calculatedTaxableIncome']['allowance']['current'];
        $total += $salary['calculatedTaxableIncome']['ssf_contribution']['current'];
        $total += $salary['calculatedTaxableIncome']['stipend']['current'];

        foreach ($salary['calculatedTaxableIncome']['additional_allowance']['current'] as $value) {
            $total += $value;
        }
        foreach ($salary['calculatedTaxableIncome']['current_month_additional_income'] as $value) {
            $total += (float)$value['amount'];
        }

        if ($type == "cashInHand") {
            $total -= $salary['calculatedTaxableAmount']['ssf_deduction']['current'] ?? 0;
            $total -= $salary['calculatedTaxableAmount']['cit']['current'] ?? 0;
            foreach ($salary['calculatedTaxableAmount']['current_month_additional_deduction'] as $value) {
                $total -= (float)$value['amount'];
            }
            foreach ($salary['calculatedTaxableAmount']['deduction'] as $value) {
                $total -= (float)$value['current'];
            }
        }
        //else{
        //     $total += $salary['calculatedTaxableAmount']['ssf_deduction']['current'] - $salary['calculatedTaxableIncome']['ssf_contribution']['current'];
        // }
        return round($total, $this->digitsAfterDecimal);
    }

    public function taxCalculation($year, $month, $calculatingValue, $prev_salary, $payslip, $remaining_month, $total_month_days, $advanceSalariesArray)
    {
        $emergencyFundType = LaravelNepaliDate::from($payslip->employee->organizationInfo->doj)->toNepaliDate('Y-m') == "{$year}-{$month}" ? 'new' : 'old';
        $calculatedEmergencyFund = 0;
        if (($emergencyFundType == 'new') && vianetHrm()) {
            $calculatedEmergencyFund = round($this->calculateEmergencyFund($payslip, $total_month_days, $emergencyFundType), $this->digitsAfterDecimal);
        } else if (($emergencyFundType == 'new') && konnectHrm()) {
            $calculatedEmergencyFund = round($this->calculateEmergencyFund($payslip, $total_month_days, $emergencyFundType), $this->digitsAfterDecimal);
        }

        $tax  = $this->calculateTax($calculatingValue);
        $calculatedTax = $tax['tds'] ?? 0;
        if ((vianetHrm() && ($payslip['basic_salary'] == 0 && $payslip['stipend'] == 0))) {
            $calculatedTax  = 0;
        }
        $yearly_tax = $prev_salary['yearly_tax'] ?? 0 + ($calculatedTax - isset($prev_salary['tax']) ?? 0) * 12;

        $remaining_tax = $yearly_tax;
        if (isset($prev_salary['paid_tds'])) {
            $remaining_tax -= $prev_salary['paid_tds'];
        }

        $additionalIncomeTDS = 0;
        foreach ($payslip->additional_income_mapping ?? [] as $additionalIncome) {
            $additionalIncomeTDS += $additionalIncome['tds'];
        }

        $remaining_tax -= $additionalIncomeTDS;
        $tax_for_this_month = ($remaining_tax / ($remaining_month + 1)) + $additionalIncomeTDS;
        $tax_for_this_month = $tax_for_this_month <= 0 ? 0 : $tax_for_this_month;
        $paid_tds = $tax_for_this_month;
        if (isset($prev_salary['paid_tds'])) {
            $paid_tds += $prev_salary['paid_tds'];
        }
        $paid_sst = $tax['sst'] ?? 0;
        if (isset($prev_salary['paid_sst'])) {
            $paid_sst += $prev_salary['paid_sst'];
        }

        // $advanceSalaries = AdvanceSalary::where([
        //     ['employee_id', $payslip->employee_id],
        //     ['is_settled', false],
        //     ['nep_year', '<=', $year],
        //     ['nep_month', '<=', $month]
        // ])->sum('amount') ?? 0;
        $advanceSalaries = $advanceSalariesArray[$payslip->employee_id]['amount'] ?? 0;

        $cashInhandPerMonthWithoutTax   = $this->incomeMonth($calculatingValue);
        $totalPerMonth                  = $this->incomeMonth($calculatingValue, "total");
        $cashInhandPerMonth             = $cashInhandPerMonthWithoutTax - $tax_for_this_month - $calculatedEmergencyFund ?? 0;
        if ($cashInhandPerMonth >= $advanceSalaries) {
            $cashInhandPerMonth -= $advanceSalaries;
        } else {
            $advanceSalaries = 0;
        }

        return [
            'calculatedEmergencyFund' => round($calculatedEmergencyFund, $this->digitsAfterDecimal),
            'yearly_tax' => round($yearly_tax, $this->digitsAfterDecimal),
            'remaining_tax' => round($remaining_tax, $this->digitsAfterDecimal),
            'tax_for_this_month' => round($tax_for_this_month, $this->digitsAfterDecimal),
            'current_sst_paid' => round($tax['sst'] ?? 0, $this->digitsAfterDecimal),
            'paid_tds' => round($paid_tds, $this->digitsAfterDecimal),
            'paid_sst' => round($paid_sst ?? 0, $this->digitsAfterDecimal),
            'advance_salary_deduct' => round($advanceSalaries, $this->digitsAfterDecimal),
            'cashInhandPerMonthWithoutTax' => round($cashInhandPerMonthWithoutTax, $this->digitsAfterDecimal),
            'totalPerMonth' => round($totalPerMonth, $this->digitsAfterDecimal),
            'cashInhandPerMonth' => round($cashInhandPerMonth, $this->digitsAfterDecimal)
        ];
    }

    public function validateSalarySheetForExcel($data)
    {
        $empCode = $data['employee_code'];

        $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$empCode'")
            ->select(
                'employees.id as id',
                'employees.company_id',
            )
            ->withTrashed()
            ->first();
        if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($empCode ? $empCode : '(NULL)'));

        $headers = [
            "earned_basic_salary",
            "earned_ssf",
            "earned_allowance",
            "annual_taxable_income",
            "past_cit_deduction",
            "past_ssf_deduction",
            "past_ssf_deduction_for_tax",
            "annual_taxable_amount",
            "paid_tds",
        ];

        foreach ($headers as $header) {
            if (!(is_float($data[$header]) || is_numeric($data[$header]))) {
                return $this->errorResponse("Only numbers are allowed in {$header}.");
            }
        }

        $addedAllowance = "";
        $allowanceMetadata = [];
        foreach (array_keys($data) as $header) {
            $perk = Perk::where('name', $header)->first();
            if ($perk) {
                $value = $data[$header];
                if (!is_numeric($value))
                    return $this->errorResponse("Invalid numeric value for allowance $perk->name");
                if ($value) {
                    if ($perk->is_global) {
                        if ($value != $perk->default_value)
                            return $this->errorResponse("$perk->name is global. Invalid value. (Valid global value: $perk->default_value)");
                    }
                    $addedAllowance .= $perk->name . ", ";
                    $allowanceMetadata[$perk->id] = [$perk->name => $value];
                }
            }
        }

        $deductionMetadata = [];
        foreach (array_keys($data) as $header) {
            if ($header == 'non_cash_deduction') {
                $value = $data[$header];
                if (!is_numeric($value))
                    return $this->errorResponse("Invalid numeric value for allowance non cash deduction");

                if ($value) {
                    $deductionMetadata['Non Cash Deduction'] = $value;
                }
            }
        }

        return $this->successResponse("", [
            'employee'              => $employee,
            'addedAllowance'        => $addedAllowance,
            'allowanceMetadata'     => $allowanceMetadata,
            'deductionMetadata'     => $deductionMetadata,
        ]);
    }

    public function createSalarySheetFromExcel(array $data, $comment = "")
    {
        $response = $this->validateSalarySheetForExcel($data);
        if (!$response['status']) return $this->errorResponse($response['message']);

        $responseData = $response['data'];
        $data['month'] -= 1;
        if ($data['month'] <= 0) {
            $data['month'] = 12;
            $data['year'] -= 1;
        }
        $data['month'] = $data['month'] < 10 ? '0' . $data['month'] : $data['month'];

        $payslip_id = $responseData['employee']?->activePaySlipList[0]?->id ?? 0;
        $employee_id = $responseData['employee']->id;
        $uuid = "{$payslip_id}-{$employee_id}-{$data['year']}-{$data['month']}";
        $fiscalYear = FiscalYear::getFiscalYearFromYearAndMonth($data['year'], $data['month']);
        $dataToSave = [
            'uuid' => $uuid,
            'payslip_id' => $payslip_id,
            'employee_id' => $employee_id,
            'year' => $data['year'],
            'month' => $data['month'],
            'fiscal_year' => $fiscalYear->id,
            'total_month_days' => 0,
            'total_salary_days' => 0,
            'current_basic_salary' => 0,
            'earned_basic_salary' => $data['earned_basic_salary'] ?? 0,
            'current_ssf' => 0,
            'earned_ssf' => $data['earned_ssf'] ?? 0,
            'current_allowance' => 0,
            'earned_allowance' => $data['earned_allowance'] ?? 0,
            'annual_taxable_income' => $data['annual_taxable_income'] ?? 0,
            'deduction_limit' => 500000,
            'insurance' => 0,
            'current_cit_deduction' => 0,
            'past_cit_deduction' => $data['past_cit_deduction'] ?? 0,
            'current_ssf_deduction' => 0,
            'past_ssf_deduction' => $data['past_ssf_deduction'] ?? 0,
            'past_ssf_deduction_for_tax' => $data['past_ssf_deduction_for_tax'] ?? 0,
            'annual_taxable_amount' => $data['annual_taxable_amount'] ?? 0,
            'paid_tds' => ($data['paid_tds'] ?? 0) + ($data['paid_sst'] ?? 0),
            'paid_sst' => $data['paid_sst'] ?? 0,
            'is_locked' => true,
            'remarks' => 'Uploaded salary sheet.',
        ];

        $perksDataToSave = [];
        foreach ($responseData['allowanceMetadata'] as $key => $perk) {
            $earned_amount = 0;
            foreach ($perk as $value) {
                $earned_amount = $value;
            }
            $perksDataToSave[] = [
                'payment_uuid'      => $uuid,
                "perks_id"          => $key,
                "actual_amount"     => 0,
                "earned_amount"     => $earned_amount,
                "current_amount"    => 0,
            ];
        }

        $deductionDataToSave = [];
        foreach ($responseData['deductionMetadata'] as $name => $value) {
            $deductionDataToSave[] = [
                'payment_uuid'      => $uuid,
                "name"              => $name,
                "actual_amount"     => 0,
                "deducted_amount"   => $value,
                "current_amount"    => 0,
            ];
        }

        // Delete the existing data replace it with the uploaded one.
        Payment::where([['employee_id', $employee_id], ['year', $data['year']], ['month', $data['month']]])->delete();

        try {
            DB::beginTransaction();
            if (count($dataToSave)) {
                Payment::insert($dataToSave);
            }

            if (count($perksDataToSave)) {
                PaymentPerksMapping::insert($perksDataToSave);
            }

            if (count($deductionDataToSave)) {
                PaymentDeductionMapping::insert($deductionDataToSave);
            }
            DB::commit();
            return $this->responseHelper(true, "Salary Sheet Uploaded");
        } catch (Exception $e) {
            DB::rollBack();
            logError('Ubable to upload salary sheet.', $e);
            return $this->responseHelper(true, "Unable to Uploaded Salary Sheet");
        }
    }
}
