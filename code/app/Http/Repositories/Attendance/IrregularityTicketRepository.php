<?php

namespace App\Http\Repositories\Attendance;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\Repository;
use App\Http\Repositories\TicketRepository;
use App\Livewire\SelfService\MyTickets\Components\IrregularityRequest;
use App\Models\Attendance\IrregularityTicket;
use App\Models\Leaves\Attendance;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use PermissionList;

class IrregularityTicketRepository extends Repository
{

    public function createIrregularityTicket($data)
    {
        try {
            DB::beginTransaction();
            logInfo("Creating Irregularity Request and Ticket", $data);
            $existingIrregularityTicket = IrregularityTicket::where([
                ['date_en', $data['date_en']],
                ['employee_id', $data['employee_id']],
                ['type', $data['type']],
            ])->whereIn('state', [WorkflowState::APPROVED, WorkflowState::SUBMITTED])->first();
            if ($existingIrregularityTicket) {
                return $this->errorResponse("Irregularity request for this date already exists");
            }

            $irregularityTicket = IrregularityTicket::create($data);

            logInfo("Irregularity Request Created", $irregularityTicket->toArray());

            $ticketRepo = new TicketRepository;
            $response = $ticketRepo->createRequestTicket($irregularityTicket, [
                'employee_id'       => $data['employee_id'],
                'current_owner_id'  => $data['verifier_id'],
                'documents'         => $data['documents'] ?? [],
            ]);
            if (!$response['status']) {
                logError("Error from ticket repo while creating irregularity ticket: " . $response['message']);
                return $this->errorResponse($response['message']);
            }
            logInfo("Ticket for the irregularity request created");

            DB::commit();
            return $this->successResponse("Irregularity Ticket created successfully", $irregularityTicket);
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error creating Irregularity Ticket: ", $e);
            return $this->errorResponse("Could not create Irregularity Ticket");
        }
    }

    public function validateDate($date_np, $employee_id)
    {
        $date_en = LaravelNepaliDate::from($date_np)->toEnglishDate();
        if (Carbon::parse($date_en)->gt(Carbon::today())) {
            return $this->errorResponse('Please select date less than or equal to today');
        }
        $attendance = Attendance::where([['date_en', $date_en], ['employee_id', $employee_id]])->first();
        if (!$attendance) {
            return $this->errorResponse("Attendance not found for the selected date");
        }
        // dd($attendance);
        return $this->successResponse("Attendance data found", $attendance);
    }

    public function updateIrregularityTicket($requestId, $data)
    {
        try {
            DB::beginTransaction();
            logInfo("Updating Irregularity Request with $requestId", $data);

            $irregularityTicket = IrregularityTicket::find($requestId);
            if (!$irregularityTicket) {
                return $this->errorResponse("Could not update Irregularity Ticket");
            }

            $existingIrregularityTicket = IrregularityTicket::where([
                ['date_en', $data['date_en']],
                ['employee_id', $data['employee_id']],
                ['id', '!=', $requestId],
            ])->first();
            if ($existingIrregularityTicket) {
                return $this->errorResponse("Irregularity request for this date already exists");
            }

            $irregularityTicket->update($data);
            logInfo("Irregularity Request updated", $irregularityTicket->toArray());

            $ticketRepo = new TicketRepository;
            $response = $ticketRepo->updateRequestTicket($irregularityTicket, [
                'current_owner_id'  => $data['verifier_id'],
                'documents'         => $data['documents'] ?? [],
            ]);
            if (!$response['status']) {
                logError("Error from ticket repo while updating irregularity ticket: " . $response['message']);
                return $this->errorResponse($response['message']);
            }
            logInfo("Ticket for the irregularity request updated");

            DB::commit();
            return $this->successResponse("Irregularity Ticket updated successfully", $irregularityTicket);
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error finding Irregularity Ticket: ", $e);
            return $this->errorResponse("Could not update Irregularity Ticket");
        }
    }

    public function getIrregularityTicket(string|int $id)
    {
        $irregularityTicket = IrregularityTicket::with(['requestTicket'])->find($id);
        if (!$irregularityTicket) {
            return $this->errorResponse("Irregularity Ticket not found");
        }
        return $this->successResponse("Irregularity Ticket found", $irregularityTicket);
    }

    public function approveIrregularityTicket(IrregularityTicket $model)
    {
        $attendance = Attendance::where([
            ['date_en', $model->date_en],
            ['employee_id', $model->employee_id]
        ])->first();
        if (!$attendance) {
            return $this->errorResponse("Could not find attendance for given date");
        }
        $finalRemarks = $attendance->remarks . '(' . $model->type . ' Request)';
        $attendance->update(['remarks' => $finalRemarks]);
        return $this->successResponse("Remarks updated successfully");
    }

    public function setIrregularityTicketDetail($request, $type): void
    {
        switch ($type) {
            case "all":
                $attendance = Attendance::where([
                    ['date_en', $request->date_en],
                    ['employee_id', $request->employee_id]
                ])->first();
                $request->in_time = $attendance->in_time;
                $request->out_time = $attendance->out_time;
                $request->status = $attendance->status;
            case "card":
                $request->nep_applied_on = convertToNepDate($request->created_at, 'F j, Y D');
                $request->nep_date = convertToNepDate($request->date_en, 'F j, Y D');
                //
            default:
                //
        }
    }

    public function assignIrregularityTicket($data)
    {
        try {
            DB::beginTransaction();
            logInfo("Parameters for assigning attendance irregularity request:", $data);
            $existingIrregularityTicket = IrregularityTicket::where([
                ['date_en', $data['date_en']],
                ['employee_id', $data['employee_id']],
                ['type', $data['type']],
            ])->whereIn('state', [WorkflowState::ASSIGNED])->first();

            if ($existingIrregularityTicket) {
                return $this->errorResponse("Irregularity request for this date already exists");
            }

            $irregularityTicket = IrregularityTicket::create($data);
            logInfo("Irregularity Request Created", $irregularityTicket->toArray());

            $ticketRepo = new TicketRepository;
            $response = $ticketRepo->createRequestTicket($irregularityTicket, [
                'employee_id'       => $data['employee_id'],
                'current_owner_id'  => null,
                'documents'         => $data['documents'] ?? [],
            ], false);
            $requestTicket = $response['data'];

            $transitionsList = $irregularityTicket->transitionPerformers()->where([
                ['recipient_id', $irregularityTicket->employee_id],
                ['performer_id', currentEmployee()->id]
            ])->get();
            $isApprover = count($transitionsList->filter(fn($transition) => $transition->state === WorkflowPerformer::APPROVER));
            $canAssignScope = auth()->user()->can(PermissionList::IRREGULARITY_REQUEST_ASSIGN_SCOPE);

            if ($isApprover || $canAssignScope) {
                $irregularityTicket->transitionTo(
                    WorkflowState::ASSIGNED,
                    "",
                    metadata: [
                        'initiator_id'    => '',
                        'initiator_role'  => '',
                        'next_owner_id'   => '',
                        'next_owner_role' => '',
                    ]
                );
                $requestTicket->state = WorkflowState::ASSIGNED;
                $requestTicket->current_owner_role = null;
                $requestTicket->save();

                $response = $this->approveIrregularityTicket($irregularityTicket);
                if (!$response['status']) {
                    logError("Error from ticket repo while creating irregularity ticket: " . $response['message']);
                    return $this->errorResponse($response['message']);
                }

                logInfo("Ticket for the irregularity request assigned");

                DB::commit();
                return $this->responseHelper(
                    true,
                    "Irregularity Request Assigned Successfully",
                    ['model_id' => $irregularityTicket->id, 'workflow' => $irregularityTicket->workflow]
                );
                // return $this->successResponse("Irregularity Ticket assigned successfully", $irregularityTicket);
            } else {
                return $this->errorResponse("You don't have permission to assign irregularity ticket to this user");
            }
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error creating Irregularity Ticket: ", $e);
            return $this->errorResponse("Could not create Irregularity Ticket");
        }
    }

    public function revertIrregularityTicket(IrregularityTicket $request)
    {
        \logInfo("Reverting irregularity ticket detail of employee of id: {$request->employee_id} and request id $request->id");

        $attendance = Attendance::where('employee_id', $request->employee_id)
            ->where('date_en', $request->date_en)
            ->first();

        if ($attendance) {
            $attendance->update([
                'remarks'       => "$attendance->remarks [Reverted]",
                'status'        => "$attendance->status",
            ]);
        }

        return $this->responseHelper(true);
    }
}
