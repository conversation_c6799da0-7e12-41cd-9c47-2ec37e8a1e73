<?php

namespace App\Http\Repositories\Attendance;

use App\Http\Helpers\ArflowHelper;
use App\Http\Repositories\DashboardRepository;
use App\Http\Repositories\Repository;
use App\Models\Attendance\IrregularityTicket;
use App\Models\Leaves\Attendance;
use App\Http\Helpers\Enums\WorkflowName;
use Carbon\Carbon;

class AttendanceDashboardRepository extends Repository
{
    private DashboardRepository $dashboardRepo;

    public function __construct()
    {
        $this->dashboardRepo = new DashboardRepository();
    }

    public function getPresentCount($selectedDate, $selectedBranch, $selectedDepartment)
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where(function ($query) {
                $query->whereNotNull('in_time')
                    ->orWhereNotNull('out_time');
            });
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }
    public function getAbsentCount($selectedDate, $selectedBranch, $selectedDepartment)
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where(function ($query) {
                $query->whereNull('in_time')
                    ->whereNull('out_time')
                    ->where(function ($q) {
                        $q->whereNull('attendance.leave_request_id')
                            ->orWhere(function ($q2) {
                                $q2->where('leave_types.paid', 0)
                                    ->orWhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                            });
                    })
                    ->where(function ($q) {
                        $q->whereNull('attendance.status')
                            ->orWhereNotIn('attendance.status', ['Day Off', 'On Holiday', 'Shift Not Started']);
                    });
            });
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }

    public function getShiftNotStartedCount($selectedDate, $selectedBranch, $selectedDepartment): int
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where(function ($query) {
                $query->whereNull('in_time')
                    ->whereNull('out_time')
                    ->where(function ($q) {
                        $q->whereNull('attendance.leave_request_id')
                            ->orWhere(function ($q2) {
                                $q2->where('leave_types.paid', 0)
                                    ->orWhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                            });
                    })
                    ->where('attendance.status', 'Shift Not Started');
            });
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }

    public function getOnLeaveCount($selectedDate, $selectedBranch, $selectedDepartment = null, $selectedRegion = null)
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where(function ($query) {
                $query->whereNull('in_time')
                    ->whereNull('out_time')
                    ->where(function ($q) {
                        $q->whereNotNull('attendance.leave_request_id')
                            ->where('leave_types.paid', 1)
                            ->whereIn('leave_requests.state', ['Approved', 'Assigned']);
                    });
            });
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedRegion) {
            $query = $query->where('org.region_id', $selectedRegion);
        }
        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }

    public function getOnDayOffCount($selectedDate, $selectedBranch, $selectedDepartment = null, $selectedRegion = null)
    {
        $query = Attendance::where('date_np', $selectedDate)
            ->where('status', 'Day Off')
            ->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedRegion) {
            $query->where('org.region_id', $selectedRegion);
        }

        if ($selectedBranch) {
            $query->where('org.branch_id', $selectedBranch);
        }

        if ($selectedDepartment) {
            $query->where('org.department_id', $selectedDepartment);
        }

        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }


    public function getLateInCount($selectedDate, $selectedBranch, $selectedDepartment)
    {
        $query = Attendance::where('date_np', [$selectedDate])
            ->where('status', 'like', '%Late%');
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->whereNotNull('org.employee_category_id');
        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');
        return $query->distinct()->count('attendance.employee_id');
    }

    public function getEarlyOutCount($selectedDate, $selectedBranch, $selectedDepartment)
    {
        $query = Attendance::where('date_np', [$selectedDate])
            ->where('status', 'like', '%Early Out%');
        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'attendance.employee_id')
            ->whereNotNull('org.employee_category_id');
        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');
        return $query->distinct()->count('attendance.employee_id');
    }

    public function getPunctualCount($selectedDate, $selectedBranch, $selectedDepartment)
    {
        $query = Attendance::where('attendance.date_np', $selectedDate)
            ->where('attendance.status', 'Present')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($selectedBranch) {
            $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query->where('org.department_id', $selectedDepartment);
        }

        $query = filterEmployeesByScope($query, 'org');

        return $query->distinct()->count('attendance.employee_id');
    }


    public function irregularityEarlyOutCount($selectedBranch, $selectedDepartment, $startDate = null, $endDate = null)
    {
        $query = IrregularityTicket::query()
            ->where('type', 'like', '%Early Out%')
            ->leftJoin('employee_org as org', 'org.employee_id', 'irregularity_tickets.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($startDate && $endDate) {
            $query->where('date_np', '>=', $startDate)
                ->where('date_np', '<=', $endDate);
        }

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');
        return $query->distinct()->count('irregularity_tickets.employee_id');
    }

    public function irregularityLateInCount($selectedBranch, $selectedDepartment, $startDate = null, $endDate = null)
    {
        $query = IrregularityTicket::query()
            ->where('type', 'like', '%Late In%');

        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'irregularity_tickets.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($startDate && $endDate) {
            $query->where('date_np', '>=', $startDate)
                ->where('date_np', '<=', $endDate);
        }

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');
        return $query->distinct()->count('irregularity_tickets.employee_id');
    }

    public function irregularityTicketCount($selectedBranch, $selectedDepartment, $startDate = null, $endDate = null)
    {
        $query = IrregularityTicket::query()
            ->whereNotIn('state', ArflowHelper::getFinalStates(WorkflowName::IRREGULARITY_TICKET));

        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'irregularity_tickets.employee_id')
            ->whereNotNull('org.employee_category_id');

        if ($startDate && $endDate) {
            $query->where('date_np', '>=', $startDate)
                ->where('date_np', '<=', $endDate);
        }

        if ($selectedBranch) {
            $query = $query->where('org.branch_id', $selectedBranch);
        }
        if ($selectedDepartment) {
            $query = $query->where('org.department_id', $selectedDepartment);
        }
        $query = filterEmployeesByScope($query, 'org');
        return $query->distinct()->count('irregularity_tickets.employee_id');
    }

    public function getEarliestValidDate(int $employeeId, int $minRequiredDays): ?Carbon
    {
        $latestDates = Attendance::where('employee_id', $employeeId)
            ->where(function ($query) {
                $query->whereNotNull('in_time')
                    ->orWhereNotNull('out_time');
            })
            ->whereDate('date_en', '<', now())
            ->orderByDesc('date_en')
            ->limit($minRequiredDays)
            ->pluck('date_en')
            ->map(fn($d) => Carbon::parse($d)->toDateString())
            ->toArray();

        if (count($latestDates) < $minRequiredDays) {
            return null;
        }

        return Carbon::parse(end($latestDates))->startOfDay();
    }

    public function isDateValidBasedOnRecentAttendance(Carbon|string $date, int $employeeId, int $minRequiredDays): bool
    {
        $date = $date instanceof Carbon ? $date : Carbon::parse($date);
        $selectedDate = $date->copy()->startOfDay();

        $earliestValidDate = $this->getEarliestValidDate($employeeId, $minRequiredDays);

        if (is_null($earliestValidDate)) {
            return true;
        }

        return $selectedDate->gte($earliestValidDate);
    }
}
