<?php

namespace App\Http\Repositories\Attendance\Interfaces;

/**
 * Interface SyncRepositoryInterface
 *
 * Provides methods for synchronizing attendance data between devices and database
 *
 * @package App\Http\Repositories\Interfaces
 */
interface ISyncRepository
{
    /**
     * Synchronize attendance data from device to database
     *
     * This method processes attendance logs from devices and updates the database
     * with attendance records for each employee.
     *
     * @param string|null $date The date to sync (format: Y-m-d). Defaults to current date
     * @param int|array|null $empId Employee id(s) to sync. Sync all active employees if null
     * @return bool Returns true on successful synchronization
     */
    public function syncAttendanceFromDeviceToDb(?string $date = null, $empId = null): bool;

    /**
     * Check the attendance status of an employee
     *
     * @param object $empOrg Employee organization model
     * @param string|null $date Date to check status for
     * @param string|null $inTime Employee's check-in time
     * @param string|null $outTime Employee's check-out time
     * @param object|null $existingAttendance Existing attendance record if available
     * @return array|int Status information or 0 if invalid
     */
    public function checkStatus($empOrg, ?string $date = null, ?string $inTime = null, ?string $outTime = null, $existingAttendance = null);

    /**
     * Get employee break-in and break-out details
     *
     * @param int|null $department Department ID (optional)
     * @param string|null $date Date to get break details for (defaults to current date)
     * @return \Illuminate\Support\Collection Collection of break details
     */
    public function getBreakInOut(?int $department = null, ?string $date = null);

    /**
     * Check if employee time meets specified condition
     *
     * @param string $time Reference time
     * @param string $condition Comparison condition (>, >=, =, <, <=)
     * @param string $employeeTime Employee time to compare
     * @return bool Whether condition is met
     */
    public function getTimeStatus(string $time, string $condition, string $employeeTime): bool;
}
