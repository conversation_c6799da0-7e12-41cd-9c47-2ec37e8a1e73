<?php

namespace App\Http\Repositories\Attendance;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\BypassType;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\Attendance\Interfaces\ISyncRepository;
use App\Models\configs\EmployeeShift;
use App\Models\configs\LateInCategoryTime;
use App\Models\Employee\Employee as EmployeeModel;
use App\Models\Employee\EmployeeOrg as OrgModel;
use App\Models\Leaves\Attendance as AttendanceModel;
use App\Models\configs\Holiday as HolidayModel;
use App\Models\Payroll\PayrollBypassEmployee;
use Carbon\Carbon;
use App\Models\configs\FiscalYear as FiscalYearModel;
use App\Models\configs\HolidayBranch as HolidayBranchModel;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\EmployeeLeaveDetail;
use App\Models\Leaves\LeaveRequest;
use App\Models\TimeRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class SyncRepository implements ISyncRepository
{
    /**
     * @var array Holiday data indexed by date
     */
    private array $holidayByDate;

    /**
     * @var array Holiday data indexed by branch
     */
    private array $holidayByBranch;

    public function __construct()
    {
        $this->initializeHolidayData();
    }

    /**
     * Initialize holiday data from database
     *
     * Loads holiday data for the current fiscal year and organizes it by date and branch
     * for efficient lookups during attendance processing.
     *
     * @return void
     */
    private function initializeHolidayData(): void
    {
        $fiscalYear = FiscalYearModel::current()->first();
        $this->holidayByDate = HolidayModel::where('fiscal_year', $fiscalYear->id)
            ->get()
            ->keyBy('eng_date')
            ->toArray();

        $holidayIds = array_column($this->holidayByDate, 'id');
        $holidayByBranch = HolidayBranchModel::whereIn('holiday_id', $holidayIds)->get();

        $this->holidayByBranch = [];
        foreach ($holidayByBranch as $value) {
            if (!isset($this->holidayByBranch[$value['holiday_id']])) {
                $this->holidayByBranch[$value['holiday_id']] = [];
            }
            $this->holidayByBranch[$value['holiday_id']][$value['branch_id']] = $value;
        }
    }


    public function syncAttendanceFromDeviceToDb(?string $date = null, $empId = null): bool
    {
        logCronInfo("New Attendance Repository In Action");
        set_time_limit(0);

        $date ??= date('Y-m-d');
        $dateBs = LaravelNepaliDate::from($date)->toNepaliDate();

        $this->cleanupOldDeviceLogs($date);

        $empOrgs = $this->getEmployeeData($empId);
        $biometricIds = array_column($empOrgs->toArray(), 'biometric_id');

        $attendanceBypassEmployees = $this->getAttendanceBypassEmployees();

        $attendanceToday = AttendanceModel::where('date_en', $date)
            ->get()
            ->keyBy('employee_id');

        $attLogs = $this->getAttendanceLogs($date);

        // Process attendance for each employee
        foreach ($biometricIds as $biometricId) {
            if (!isset($empOrgs[$biometricId])) {
                continue;
            }

            $empDetails = $empOrgs[$biometricId];
            $enrollmentNo = $this->normalizeEnrollmentNo($biometricId);
            $attLog = $attLogs[$enrollmentNo] ?? [];
            $source = $this->determineSource($attLog);

            // Remove duplicate attendance records
            $this->cleanupDuplicateAttendance($date, $empDetails->employee_id);

            // Handle attendance bypass employees
            if (in_array($empDetails->employee_id, $attendanceBypassEmployees)) {
                $canProcessAttendanceByLogCount = $this->handleBypassEmployeeAttendance($empDetails, $date, $dateBs, $source);
                if (!$canProcessAttendanceByLogCount)
                    continue;
            }

            // Process attendance based on log count
            $this->processAttendanceByLogCount(
                $attLog,
                $empDetails,
                $date,
                $dateBs,
                $attendanceToday[$empDetails->employee_id] ?? null,
                $source
            );
        }

        return true;
    }

    /**
     * Clean up old device logs from previous fiscal years
     *
     * @param string $date Current date in Y-m-d format
     * @return void
     */
    private function cleanupOldDeviceLogs(string $date): void
    {
        $dateParts = explode('-', $date);
        $activeYear = (int)$dateParts[0];
        $activeMonth = (int)$dateParts[1];

        if ($activeMonth > 8) {
            $prevYear = $activeYear - 1;
            $beforePrevYear = $prevYear - 1;
            // $beforePrevYear = $beforePrevYear . '-07' . '-17 00:00:00';
            $beforePrevYear = "$beforePrevYear-07-17 00:00:00"; // year before previous year
            // $prevYear = $prevYear . '-07' . '-16 23:59:59';
            $prevYear = "$prevYear-07-16 23:59:59"; // previous year

            DB::table('att_logs')
                ->whereBetween('log_date', [$beforePrevYear, $prevYear])
                ->delete();
        }
    }

    /**
     * Get employee data for attendance processing
     *
     * @param int|array|null $empId Employee id(s) to filter by
     * @return \Illuminate\Support\Collection Employee data collection
     */
    private function getEmployeeData($empId): Collection
    {
        if (is_null($empId)) {
            $activeEmployees = EmployeeModel::whereHas('organizationInfo', function ($query) {
                $query->whereNull('termination_date');
            })
                ->get()
                ->pluck('id');

            return OrgModel::with('employee')
                ->whereIn('employee_id', $activeEmployees)
                ->get(['employee_id', 'biometric_id', 'shift_id', 'branch_id', 'employee_code'])
                ->keyBy('biometric_id');
        } elseif (!is_array($empId)) {
            return OrgModel::with('employee')
                ->where('employee_id', $empId)
                ->withTrashed()
                ->get()
                ->keyBy('biometric_id');
        } else {
            return OrgModel::with('employee')
                ->whereIn('employee_id', $empId)
                ->withTrashed()
                ->get()
                ->keyBy('biometric_id');
        }
    }

    /**
     * Get list of employees with attendance bypass
     *
     * @return array Array of employee IDs
     */
    private function getAttendanceBypassEmployees(): array
    {
        return PayrollBypassEmployee::query()
            ->where('bypass_type', function ($query) {
                $query->select('id')
                    ->from('payroll_bypass_types')
                    ->where('name', BypassType::ATTENDANCE);
            })->pluck('employee_id')->toArray();
    }

    /**
     * Get attendance logs for a specific date
     *
     * @param string $date Date in Y-m-d format
     * @return array Attendance logs grouped by enrollment number
     */
    private function getAttendanceLogs(string $date): array
    {
        $attLogRaw = DB::table('att_logs')
            ->whereRaw("log_date like '$date%'")
            ->orderBy('log_date', 'asc')
            ->get();

        $attLogs = [];
        foreach ($attLogRaw as $value) {
            if (!isset($attLogs[$value->enrollment_no])) {
                $attLogs[$value->enrollment_no] = [];
            }
            $attLogs[$value->enrollment_no][] = $value;
        }

        return $attLogs;
    }

    /**
     * Normalize enrollment number by removing non-numeric characters and leading zeros
     *
     * @param string|null $biometricId Biometric ID
     * @return string Normalized enrollment number
     */
    private function normalizeEnrollmentNo(?string $biometricId): string
    {
        $enrollmentNo = preg_replace('/[^0-9]/', '', $biometricId);
        return ltrim($enrollmentNo, '0');
    }

    /**
     * Determine the source of attendance (App, Web, or Biometric Device)
     *
     * @param array $attLog Attendance log data
     * @return string Source description
     */
    private function determineSource(array $attLog): string
    {
        if (empty($attLog)) {
            return "";
        }

        return match ($attLog[0]->device_id) {
            Constant::EMP_APP_ID => "App",
            Constant::WEB_APP_ID => "Web",
            Constant::HRM_APP_ID => "HRM App",
            default => "Biometric Device",
        };
    }

    /**
     * Clean up duplicate attendance records for an employee on a specific date
     *
     * @param string $date Date in Y-m-d format
     * @param int $employeeId Employee ID
     * @return void
     */
    private function cleanupDuplicateAttendance(string $date, int $employeeId): void
    {
        $existing_ids = AttendanceModel::where([
            ['date_en', $date],
            ['employee_id', $employeeId]
        ])->get()->pluck('id')->toArray();

        if (count($existing_ids) > 1) {
            $attendanceDaily = AttendanceModel::where([
                ['date_en', $date],
                ['employee_id', $employeeId]
            ])->first();

            if ($attendanceDaily) {
                $index = array_search($attendanceDaily->id, $existing_ids);
                if ($index !== false) {
                    // unset the index of one attendance record
                    unset($existing_ids[$index]);
                }

                // remove the remaining attendance except that one attendance.
                $existing_ids = array_values($existing_ids);
                if (!empty($existing_ids)) {
                    AttendanceModel::whereIn('id', $existing_ids)->forceDelete();
                }
            }
        }
    }

    /**
     * Handle attendance for employees with attendance bypass
     *
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $todayBs Date in Nepali format
     * @param string $source Attendance source
     * @return bool
     */
    private function handleBypassEmployeeAttendance(EmployeeOrg $empDetails, string $date, string $todayBs, string $source): bool
    {
        $shift = EmployeeShift::find($empDetails->shift_id);
        $dayOff = $shift ? date('l', strtotime("Sunday +{$shift->day_off} days")) : null;

        if ($dayOff == date('l', strtotime($date))) {
            return true; // Skip attendance for employee on their off day
        }

        $holidayList = $this->holidayByDate;
        if (array_key_exists($date, $holidayList)) {
            $holiday_id = $holidayList[$date]['id'];
            $branch_id = $empDetails->branch_id;
            $gender = $empDetails->employee->gender;
            $holiday_branch = $this->holidayByBranch[$holiday_id][$branch_id] ?? null;
            if ($holiday_branch) {
                if (strtolower($holidayList[$date]['gender'] ?? '') == strtolower("All") || strtolower($holidayList[$date]['gender'] ?? '') == strtolower($gender)) {
                    return true;
                }
            }
        }

        $existingAttendance = AttendanceModel::where('employee_id', $empDetails->employee_id)
            ->where('date_en', $date)
            ->get();

        if ($existingAttendance->count()) {
            $existingAttendance->each->forceDelete();
        }

        // Create new attendance record
        $attendance = new AttendanceModel();
        $attendance->employee_id = $empDetails->employee_id;
        $attendance->employee_code = $empDetails->employee_code;
        $attendance->date_en = $date;
        $attendance->date_np = $todayBs;
        $attendance->in_time = Carbon::parse($shift->start_time, 'UTC')->format('h:i:s A');
        $attendance->out_time = Carbon::parse($shift->end_time, 'UTC')->format('h:i:s A');
        $attendance->status = 'Present';
        $attendance->remarks = '';
        $attendance->source = 'Biometric Device';
        $attendance->shift_id = $empDetails->shift_id;
        $attendance->duty_start = $shift->start_time;
        $attendance->duty_end = $shift->end_time;
        $attendance->total_hours = gmdate('H:i', $shift->total_working_hour * 3600);
        $attendance->save();
        return false;
    }

    /**
     * Process attendance based on number of log entries
     *
     * @param array $attLog Attendance log entries
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $dateBs Date in Nepali format
     * @param AttendanceModel|null $attendanceDaily Existing attendance record
     * @param string $source Attendance source
     * @return void
     */
    private function processAttendanceByLogCount(
        array $attLog,
        EmployeeOrg $empDetails,
        string $date,
        string $dateBs,
        ?AttendanceModel $attendanceDaily,
        string $source
    ): void {
        $countData = count($attLog);

        switch ($countData) {
            case 0:
                $this->processZeroLogAttendance($empDetails, $date, $dateBs, $attendanceDaily, $source);
                break;

            case 1:
                $this->processSingleLogAttendance($attLog[0], $empDetails, $date, $dateBs, $attendanceDaily, $source);
                break;

            default:
                $this->processMultipleLogAttendance($attLog, $empDetails, $date, $dateBs, $attendanceDaily, $source);
                break;
        }
    }
    /**
     * Process attendance when no log entries are found
     *
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $dateBs Date in Nepali format
     * @param AttendanceModel|null $attendanceDaily Existing attendance record
     * @param string $source Attendance source
     * @return void
     */
    private function processZeroLogAttendance(
        EmployeeOrg $empDetails,
        string $date,
        string $dateBs,
        ?AttendanceModel $attendanceDaily,
        string $source
    ): void {
        if ($attendanceDaily) {
            if ($attendanceDaily->leave_status) {
                return;
            }

            $timeRequests = $this->getTimeRequests($date, $empDetails->employee_id);

            if ($attendanceDaily->missed_punch_status || $timeRequests->count()) {
                if ($timeRequests->isEmpty()) {
                    return;
                }

                [$inTime, $outTime, $inNote, $outNote] = $this->processTimeRequestData(
                    $date,
                    $empDetails->employee_id,
                    $attendanceDaily->in_time,
                    $attendanceDaily->out_time,
                    $timeRequests
                );

                $status = $this->checkStatus($empDetails, $date, $inTime, $outTime);

                $status['in_time'] = $inTime;
                $status['out_time'] = $outTime;
                $status['in_remarks'] = $inNote;
                $status['out_remarks'] = $outNote;
                $status['missed_punch_status'] = true;
                $status['source'] = 'Time Request';

                $attendanceDaily->update($status);
                return;
            }

            // Delete existing attendance as there are no time requests
            $attendanceDaily->forceDelete();
        }

        // Create new attendance record
        $attendance = $this->createBaseAttendanceRecord($empDetails, $date, $dateBs, null, null, $source);

        if (!$attendance) {
            return;
        }

        $this->applyLeaveDetailsToAttendance($attendance, $date, $empDetails->employee_id);
        $this->applyTimeRequestsToAttendance($attendance, $date, $empDetails->employee_id);

        $attendance->save();
    }

    /**
     * Process attendance when a single log entry is found
     *
     * @param object $log Log entry
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $dateBs Date in Nepali format
     * @param AttendanceModel|null $attendanceDaily Existing attendance record
     * @param string $source Attendance source
     * @return void
     */
    private function processSingleLogAttendance(
        object $log,
        EmployeeOrg $empDetails,
        string $date,
        string $dateBs,
        ?AttendanceModel $attendanceDaily,
        string $source
    ): void {
        $punchTime = Carbon::parse($log->log_date, 'UTC');
        $inTime = $outTime = null;

        // if attendance already exists, update the attendance according to the single log time and also check the status like time request
        if ($attendanceDaily) {
            // Determine in and out times based on existing record
            if ($attendanceDaily->in_time === null) {
                $inTime = $punchTime->format('h:i:s A');
            } else {
                $inTime = Carbon::parse("$date {$attendanceDaily->in_time}", 'UTC');

                if ($inTime->format('h:i:s A') == $punchTime->format('h:i:s A')) {
                    $inTime = $punchTime;
                } else if ($inTime->lt($punchTime)) {
                    $outTime = $punchTime->format('h:i:s A');
                }

                $inTime = $inTime->format('h:i:s A');
            }

            // Handle time requests if needed
            if ($attendanceDaily->missed_punch_status) {
                [$inTime, $outTime, $inNote, $outNote] = $this->processTimeRequestData(
                    $date,
                    $empDetails->employee_id,
                    $inTime,
                    $outTime
                );
            } else {
                $inNote = $outNote = "";
            }

            // Update existing attendance
            $this->updateAttendanceRecord(
                $attendanceDaily,
                $empDetails,
                $date,
                $inTime,
                $outTime,
                $inNote,
                $outNote,
                $attendanceDaily->missed_punch_status ? 'Time Request' : $source
            );
        } else {
            // Create new attendance record
            $inTime = $punchTime->format('h:i:s A');
            $this->createAndSaveAttendanceRecord(
                $empDetails,
                $date,
                $dateBs,
                $inTime,
                $outTime,
                $source
            );
        }
    }

    /**
     * Process attendance when multiple log entries are found
     *
     * @param array $attLog Log entries
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $today Date in Y-m-d format
     * @param string $todayBs Date in Nepali format
     * @param AttendanceModel|null $attendanceDaily Existing attendance record
     * @param string $source Attendance source
     * @return void
     */
    private function processMultipleLogAttendance(
        array $attLog,
        EmployeeOrg $empDetails,
        string $today,
        string $todayBs,
        ?AttendanceModel $attendanceDaily,
        string $source
    ): void {
        $inTime = Carbon::parse($attLog[0]->log_date, 'UTC')->format('h:i:s A');
        $outTime = Carbon::parse(last($attLog)->log_date, 'UTC')->format('h:i:s A');

        if ($attendanceDaily) {
            // Use existing in time if available
            if ($attendanceDaily->in_time === null) {
                $inTime = $attendanceDaily->in_time;
            }

            // Handle time requests if needed
            if ($attendanceDaily->missed_punch_status) {
                [$inTime, $outTime, $inNote, $outNote] = $this->processTimeRequestData(
                    $today,
                    $empDetails->employee_id,
                    $inTime,
                    $outTime
                );
            } else {
                $inNote = $outNote = "";
            }

            $this->updateAttendanceRecord(
                $attendanceDaily,
                $empDetails,
                $today,
                $inTime,
                $outTime,
                $inNote,
                $outNote,
                $attendanceDaily->missed_punch_status ? 'Time Request' : $source
            );
        } else {
            $this->createAndSaveAttendanceRecord(
                $empDetails,
                $today,
                $todayBs,
                $inTime,
                $outTime,
                $source
            );
        }
    }

    /**
     * Apply leave details to attendance record
     *
     * @param AttendanceModel $attendance Attendance record
     * @param string $date Date in Y-m-d format
     * @param int $employeeId Employee ID
     * @return void
     */
    private function applyLeaveDetailsToAttendance(AttendanceModel $attendance, string $date, int $employeeId): void
    {
        $employeeLeaveDetails = EmployeeLeaveDetail::with("leaveType:id,name")
            ->where([
                ['date', $date],
                ['employee_id', $employeeId]
            ])
            ->first();

        if ($employeeLeaveDetails) {
            $attendance->remarks = $employeeLeaveDetails->remarks ?? "";
            $attendance->status = $employeeLeaveDetails->leaveType?->name ?? "On Leave";
            $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
            $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
        }
    }

    /**
     * Apply time requests to attendance record
     *
     * @param AttendanceModel $attendance Attendance record
     * @param string $date Date in Y-m-d format
     * @param int $employeeId Employee ID
     * @return void
     */
    private function applyTimeRequestsToAttendance(AttendanceModel $attendance, string $date, int $employeeId): void
    {
        $timeRequests = $this->getTimeRequests($date, $employeeId);

        if ($timeRequests->isEmpty()) {
            return;
        }

        [$inTime, $outTime, $inNote, $outNote] = $this->processTimeRequestData(
            $date,
            $employeeId,
            $attendance->in_time,
            $attendance->out_time,
            $timeRequests
        );

        $status = $this->checkStatus($attendance->empDetails, $date, $inTime, $outTime);

        $attendance->in_time = $inTime;
        $attendance->out_time = $outTime;
        $attendance->in_remarks = $inNote;
        $attendance->out_remarks = $outNote;
        $attendance->total_hours = $status['total_hours'];
        $attendance->remarks = $status['remarks'];
        $attendance->status = $status['status'];
        $attendance->missed_punch_status = true;
        $attendance->source = 'Time Request';
    }

    /**
     * Create and save a new attendance record
     *
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $dateBs Date in Nepali format
     * @param string|null $inTime In time
     * @param string|null $outTime Out time
     * @param string $source Attendance source
     * @return void
     */
    private function createAndSaveAttendanceRecord(
        EmployeeOrg $empDetails,
        string $date,
        string $dateBs,
        ?string $inTime,
        ?string $outTime,
        string $source
    ): void {
        $attendance = $this->createBaseAttendanceRecord($empDetails, $date, $dateBs, $inTime, $outTime, $source);
        if ($attendance) {
            $attendance->save();
        }
    }

    /**
     * Create a base attendance record without saving
     *
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string $dateBs Date in Nepali format
     * @param string|null $inTime In time
     * @param string|null $outTime Out time
     * @param string $source Attendance source
     * @param AttendanceModel|null $existingAttendance Existing attendance record
     * @return AttendanceModel|null New attendance record or null if status check fails
     */
    private function createBaseAttendanceRecord(
        EmployeeOrg $empDetails,
        string $date,
        string $dateBs,
        ?string $inTime,
        ?string $outTime,
        string $source,
        ?AttendanceModel $existingAttendance = null
    ): ?AttendanceModel {
        $status = $this->checkStatus($empDetails, $date, $inTime, $outTime, $existingAttendance);

        if ($status === 0) {
            return null;
        }

        $attendance = new AttendanceModel();
        $attendance->employee_id = $empDetails->employee_id;
        $attendance->employee_code = $empDetails->employee_code;
        $attendance->date_en = $date;
        $attendance->date_np = $dateBs;
        $attendance->in_time = $inTime;
        $attendance->out_time = $outTime;
        $attendance->in_remarks = $status['in_remarks'];
        $attendance->out_remarks = $status['out_remarks'];
        $attendance->total_hours = $status['total_hours'];
        $attendance->remarks = $status['remarks'];
        $attendance->status = $status['status'];
        $attendance->source = $source;
        $attendance->shift_id = $status['shift_id'] ?? null;

        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);
        $attendance->duty_coverage = $this->calculateDutyCoverage($inTime, $outTime, $attendance->duty_start, $attendance->duty_end);

        return $attendance;
    }

    /**
     * Update an existing attendance record
     *
     * @param AttendanceModel $attendance Attendance record
     * @param EmployeeOrg $empDetails Employee organization details
     * @param string $date Date in Y-m-d format
     * @param string|null $inTime In time
     * @param string|null $outTime Out time
     * @param string $inNote In note
     * @param string $outNote Out note
     * @param string $source Attendance source
     * @return void
     */
    private function updateAttendanceRecord(
        AttendanceModel $attendance,
        EmployeeOrg $empDetails,
        string $date,
        ?string $inTime,
        ?string $outTime,
        string $inNote = "",
        string $outNote = "",
        string $source = ""
    ): void {
        $status = $this->checkStatus($empDetails, $date, $inTime, $outTime, $attendance);
        if ($status === 0) {
            return;
        }

        if ($inNote) {
            $status['in_remarks'] .= " [$inNote]";
        }
        if ($outNote) {
            $status['out_remarks'] .= " [$outNote]";
        }

        $status['in_time'] = $inTime;
        $status['out_time'] = $outTime;
        $status['source'] = $source;
        $status['remarks'] = $attendance->remarks ? "{$attendance->remarks}; {$status['remarks']}" : $status['remarks'];

        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);
        $status['duty_coverage'] = $this->calculateDutyCoverage($status['in_time'], $status['out_time'], $attendance->duty_start, $attendance->duty_end);

        $attendance->update($status);
    }

    /**
     * Process time request data and return processed values
     *
     * @param string $date Date in Y-m-d format
     * @param int $employeeId Employee ID
     * @param string|null $currentInTime Current in time
     * @param string|null $currentOutTime Current out time
     * @param Collection|null $timeRequests Optional pre-fetched time requests
     * @return array Array containing [inTime, outTime, inNote, outNote]
     */
    private function processTimeRequestData(
        string $date,
        int $employeeId,
        ?string $currentInTime,
        ?string $currentOutTime,
        ?Collection $timeRequests = null
    ): array {
        $inTime = $currentInTime;
        $outTime = $currentOutTime;
        $inNote = "";
        $outNote = "";

        $requests = $timeRequests ?? $this->getTimeRequests($date, $employeeId);

        foreach ($requests as $timeRequest) {
            $inTime = $timeRequest?->in_time
                ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A')
                : $inTime;

            $outTime = $timeRequest?->out_time
                ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A')
                : $outTime;

            $inNote = $timeRequest?->in_note ?? $inNote;
            $outNote = $timeRequest?->out_note ?? $outNote;
        }

        return [$inTime, $outTime, $inNote, $outNote];
    }



    private function getTimeRequests($date, $employeeId)
    {
        return  TimeRequest::where([['date', $date], ['employee_id', $employeeId]])
            ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
            ->get();
    }

    private function setDutyStartEndTimeForSaving(AttendanceModel $attendance, EmployeeOrg $empOrg)
    {
        if ($attendance->shift_id) {
            $dutyTime = $empOrg->employee?->dutyTime($attendance->date_en, $attendance);
        } else {
            $dutyTime = $empOrg->employee?->dutyTime($attendance->date_en);
        }
        $shift = $dutyTime['shift'];
        $attendance->duty_start =  $shift?->start_time;
        $attendance->duty_end =  $shift?->end_time;
        $attendance->shift_id = $shift?->id;
    }

    private function calculateDutyCoverage($dutyStartTime, $dutyEndTime, $startTime, $endTime)
    {
        if (!$dutyStartTime || !$dutyEndTime || !$startTime || !$endTime) {
            return 0;
        }


        $dutyStart = Carbon::parse($dutyStartTime);
        $dutyEnd = Carbon::parse($dutyEndTime);
        $logStart = Carbon::parse($startTime);
        $logEnd = Carbon::parse($endTime);

        // Total duty duration in minutes
        $totalDutyMinutes = $dutyStart->diffInMinutes($dutyEnd);
        if ($totalDutyMinutes === 0) {
            return 0;
        }

        $attendedMinutes = $logStart->diffInMinutes($logEnd);
        return min(1, round($attendedMinutes / $totalDutyMinutes, 2));
    }

    public function checkStatus($empOrg, ?string $date = null, ?string $inTime = null, ?string $outTime = null, $existingAttendance = null)
    {
        $dutyTime = $empOrg->employee?->dutyTime($date, $existingAttendance);
        $shiftId = $dutyTime['shift']?->id;
        $dayOff = $dutyTime['dayOff'];

        if ($shiftId === null) {
            return 0;
        }

        // Initialize status array with default values
        $status = [
            'in_remarks' => null,
            'out_remarks' => null,
            'total_hours' => null,
            'remarks' => null,
            'shift_id' => $shiftId,
            'status' => null
        ];

        $today = $date ?? date('Y-m-d');

        $shifts = EmployeeShift::all()->keyBy('id');
        if (!$shifts->has($shiftId)) {
            return 0;
        }

        $shift = $shifts[$shiftId];
        $graceTime = (int)($shift->grace_time ?? 0);

        $startTime = Carbon::parse($shift->start_time)->addMinutes($graceTime);
        $endTime = Carbon::parse($shift->end_time)->subMinutes($graceTime);

        $lateInCategoryTimes = LateInCategoryTime::leftJoin(
            "late_in_categories",
            'late_in_category_id',
            'late_in_categories.id'
        )->where("shift_id", $shiftId)->get();

        $holidayList = $this->holidayByDate;
        $todayDay = $date ? date('N', strtotime($date)) : date('N', strtotime('Y-m-d'));

        $this->determineBaseStatus(
            $status,
            $empOrg->employee_id,
            $today,
            $inTime,
            $outTime,
            $startTime,
            $endTime,
            $lateInCategoryTimes
        );

        $this->checkHoliday(
            $status,
            $today,
            $holidayList,
            $empOrg,
            $inTime,
            $outTime
        );

        $this->checkDayOff($status, $dayOff, $inTime, $outTime);

        return $status;
    }

    /**
     * Determine the base attendance status (present, absent, missed punch)
     *
     * @param array &$status Status array to be updated
     * @param int $employeeId Employee ID
     * @param string $today Date in Y-m-d format
     * @param string|null $inTime Clock-in time
     * @param string|null $outTime Clock-out time
     * @param Carbon $startTime Shift start time with grace period
     * @param Carbon $endTime Shift end time with grace period
     * @param Collection $lateInCategoryTimes Late-in category times
     * @return void
     */
    private function determineBaseStatus(
        array &$status,
        int $employeeId,
        string $today,
        ?string $inTime,
        ?string $outTime,
        Carbon $startTime,
        Carbon $endTime,
        Collection $lateInCategoryTimes
    ): void {
        // Case 1: No in or out time - check for leave or mark as absent
        if ($inTime === null && $outTime === null) {
            $leaveDetail = EmployeeLeaveDetail::with("leaveType:id,name")
                ->where([['employee_id', $employeeId], ['date', $today]])
                ->first();

            if ($leaveDetail) {
                $status['status'] = $leaveDetail->leaveType->name ?? "On Leave";
                $status['remarks'] = $leaveDetail->remarks ?? "";
            } else {
                $status['status'] = "Absent";
            }
            return;
        }

        // Case 2: Missing in time, but has out time
        if ($inTime === null) {
            $status['status'] = 'Missed Punch';
            $outTime = Carbon::parse($outTime);

            if ($outTime->lt($endTime)) {
                $status['status'] .= '[Early Out]';
                $status['out_remarks'] = 'Early Out';
            }
            return;
        }

        // Case 3: Has in time, but missing out time
        if ($outTime === null) {
            $status['status'] = 'Missed Punch';
            $this->checkLateIn($status, $inTime, $startTime, $lateInCategoryTimes);
            return;
        }

        // Case 4: Has both in and out time
        $status['status'] = 'Present';
        $outTime = Carbon::parse($outTime);
        $_inTime = Carbon::parse($inTime);

        // Check for late in
        $this->checkLateIn($status, $inTime, $startTime, $lateInCategoryTimes);

        // Check for early out
        if ($outTime->lt($endTime)) {
            $status['status'] .= '[Early Out]';
            $status['out_remarks'] = 'Early Out';
        }

        // Calculate total hours worked
        $status['total_hours'] = $outTime->diff($_inTime)->format('%H:%I');
    }

    /**
     * Check if employee was late in and update status accordingly
     *
     * @param array &$status Status array to be updated
     * @param string $inTime Clock-in time
     * @param Carbon $startTime Shift start time with grace period
     * @param Collection $lateInCategoryTimes Late-in category times
     * @return void
     */
    private function checkLateIn(
        array &$status,
        string $inTime,
        Carbon $startTime,
        Collection $lateInCategoryTimes
    ): void {
        $_inTime = Carbon::parse($inTime);

        if (!$_inTime->gt($startTime)) {
            return; // Not late, exit early
        }

        if (!$lateInCategoryTimes->count()) {
            if ($_inTime->gt($startTime)) {
                $status['status'] .= '[Late In]';
                $status['in_remarks'] = 'Late In';
            }
            return;
        }

        $storedLateTime = "";
        foreach ($lateInCategoryTimes as $lateInTime) {
            $flag = $this->getTimeStatus($lateInTime->late_time, $lateInTime->condition, $inTime);

            if ($flag) {
                $currentLateTime = strtotime(Carbon::parse($lateInTime->late_time, 'UTC')->format('h:i:s A'));

                if (!$storedLateTime || $currentLateTime > $storedLateTime) {
                    $status['status'] .= "[{$lateInTime->name}]";
                    $status['in_remarks'] = $lateInTime->name;
                    $storedLateTime = $currentLateTime;
                }
            }
        }
    }

    /**
     * Check if the date is a holiday and update status accordingly
     *
     * @param array &$status Status array to be updated
     * @param string $date Date in Y-m-d format
     * @param array $holidayList Holiday list
     * @param EmployeeOrg $empOrg Employee organization details
     * @param string|null $inTime Clock-in time
     * @param string|null $outTime Clock-out time
     * @return bool Whether status was changed due to holiday
     */
    private function checkHoliday(
        array &$status,
        string $date,
        array $holidayList,
        $empOrg,
        ?string $inTime,
        ?string $outTime
    ): bool {
        if (!array_key_exists($date, $holidayList)) {
            return false;
        }

        $holiday = $holidayList[$date];
        $holiday_id = $holiday['id'];
        $branch_id = $empOrg->branch_id;
        $gender = $empOrg->employee->gender;

        $holiday_branch = $this->holidayByBranch[$holiday_id][$branch_id] ?? null;
        if (!$holiday_branch) {
            return false;
        }

        $holidayGender = strtolower($holiday['gender'] ?? '');
        $employeeGender = strtolower($gender);

        if ($holidayGender === 'all' || $holidayGender === $employeeGender) {
            $holidayName = $holiday['name'];

            $status['status'] = $inTime === null && $outTime === null
                ? "On Holiday($holidayName)"
                : "Work On Holiday($holidayName)";

            return true;
        }

        return false;
    }

    /**
     * Check if the date is a day off and update status accordingly
     *
     * @param array &$status Status array to be updated
     * @param bool $dayOff Whether it's a day off
     * @param string|null $inTime Clock-in time
     * @param string|null $outTime Clock-out time
     * @return void
     */
    private function checkDayOff(
        array &$status,
        bool $dayOff,
        ?string $inTime,
        ?string $outTime
    ): void {
        if (!$dayOff) {
            return;
        }

        // If status already changed by holiday check, don't override
        if (strpos($status['status'] ?? '', 'Holiday') !== false) {
            return;
        }

        $status['status'] = $inTime === null && $outTime === null
            ? "Day Off"
            : "Work On Day Off";
    }
    /**
     * Get employee break in/out data for a specific date and optional department
     *
     * @param int|null $department Department ID (optional)
     * @param string|null $date Date in Y-m-d format (defaults to today)
     * @return \Illuminate\Support\Collection Collection of employee break data
     */
    public function getBreakInOut($department = null, $date = null)
    {
        $date ??= date('Y-m-d');

        // $departments = \App\Models\configs\Department::pluck('id')->toArray();  can use if particular department is needed.


        // Get active employees with their IDs indexed by employee code
        $employees = OrgModel::selectRaw('SUBSTRING(employee_code,5) as employee_code, employee_id')
            ->whereNull('termination_date')
            // ->whereIn('department_id', $departments)
            ->pluck('employee_id', 'employee_code')
            ->toArray();
        if (empty($employees)) {
            return collect([]);
        }

        $employeeIds = array_values($employees);
        $enrollmentNumbers = array_map('intval', array_keys($employees));

        $employeeNames = EmployeeModel::whereIn('id', $employeeIds)
            ->get(['first_name', 'middle_name', 'last_name', 'id'])
            ->keyBy('id')
            ->toArray();;

        // Get break in/out logs for the date
        $breakLogs = DB::table('att_logs')
            ->where('log_date', 'like', $date . '%')
            ->whereIn('enrollment_no', $enrollmentNumbers)
            ->whereIn('inout_mode', [2, 3])  // Break in/out modes
            ->orderBy('log_date')
            ->get();

        $breakData = [];

        // process break logs into structured break data
        foreach ($breakLogs as $log) {
            $enrollmentNo = $log->enrollment_no;

            // Initialize employee data if not already present
            if (!isset($breakData[$enrollmentNo])) {
                // Get employee ID, trying different formats if needed
                $employeeId = $employees[$enrollmentNo] ?? $employees["0{$enrollmentNo}"] ?? null;

                // Skip if employee not found
                if (!$employeeId || !isset($employeeNames[$employeeId])) {
                    continue;
                }

                $name = $employeeNames[$employeeId];
                $fullName = $name['first_name'];

                if (!is_null($name['middle_name'])) {
                    $fullName .= " " . $name['middle_name'];
                }

                $fullName .= " " . $name['last_name'];

                $breakData[$enrollmentNo] = [
                    'employee' => $fullName,
                    'emp_code' => $enrollmentNo,
                    'break_in' => 0,
                    'break_out' => 0,
                    'hours' => 'N/A'
                ];
            }

            // Process break time
            if ($log->inout_mode == 2 || $log->inout_mode == 3) {
                $breakTime = Carbon::parse($log->log_date, 'UTC')->format('h:i:s A');

                // Set break in time
                $breakData[$enrollmentNo]['break_in'] = $breakTime;

                // Set break out time (should be the earliest time)
                if ($breakData[$enrollmentNo]['break_out'] == 0) {
                    $breakData[$enrollmentNo]['break_out'] = $breakTime;
                } elseif ($breakTime < $breakData[$enrollmentNo]['break_out']) {
                    $breakData[$enrollmentNo]['break_out'] = $breakTime;
                }

                // Calculate hours if both in and out times are available
                if ($breakData[$enrollmentNo]['break_in'] != 0 && $breakData[$enrollmentNo]['break_out'] != 0) {
                    $breakData[$enrollmentNo]['hours'] = Carbon::parse($breakData[$enrollmentNo]['break_in'])
                        ->diff(Carbon::parse($breakData[$enrollmentNo]['break_out']))
                        ->format('%H:%I');
                }
            }
        }

        return collect($breakData);
    }

    public function getTimeStatus(string $time, string $condition, string $employeeTime): bool
    {
        $_time = strtotime(Carbon::parse($time, 'UTC')->format('h:i:s A'));
        $_employeeTime = strtotime($employeeTime);

        switch ($condition) {
            case ">":
                if ($_employeeTime > $_time)
                    return true;
                break;
            case ">=":
                if ($_employeeTime >= $_time)
                    return true;
                break;
            case "=":
                if ($_employeeTime == $_time)
                    return true;
                break;
            case "<":
                if ($_employeeTime < $_time)
                    return true;
                break;
            case "<=":
                if ($_employeeTime <= $_time)
                    return true;
                break;
        }

        return false;
    }
}
