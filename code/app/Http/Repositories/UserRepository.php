<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Constant;
use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use PermissionList;

class UserRepository extends Repository
{
  public function loginAsAnotherUser($userId, $return = false)
  {
    if (!$return) {
      if (!\auth()->user()->can(PermissionList::USER_IMPERSONATE))
        return $this->responseHelper(false, "You don't have permission to perform this task");
    }
    $user = User::find($userId);
    if (!$user) return $this->responseHelper(false, "User not found for the employee");

    $originalUserId = auth()->id();
    $originalUserName = auth()->user()->name ?? auth()->user()->username ?? "N/A";

    $impersonated = session('user_id_main');

    if (!$impersonated) {
      activity('impersonate')
        ->causedBy($originalUserId)
        ->performedOn($user)
        ->withProperties(['attributes' => [
          'original_user_id' => $originalUserId,
          'original_user_name' => $originalUserName,
          'target_user_id' => $user->id,
          'target_user_name' => $user->employee?->name ?? $user->username ?? "N/A",
        ]])
        ->event('impersonate.start')
        ->log('Started impersonation');

      session([
        'user_id_main' => session(Constant::SESSION_USER_ID),
        'main_user_name' => \currentEmployee()?->name ?? auth()->user()->username ?? "N/A",
        'user_name' => $user->employee?->name ?? $user->username ?? "N/A",
        Constant::SESSION_USER_ID => $user->id,
        Constant::SESSION_EMPLOYEE_ID => $user->employee?->id,
        Constant::SESSION_COMPANY => $user->employee?->company ?? \App\Models\configs\Company::where("is_parent", "Y")->first(),
        Constant::SESSION_CURRENT_FISCAL_YEAR => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first(),
        Constant::SESSION_EMPLOYEE => $user->employee,
        Constant::SESSION_IOPS_ID => $user->employee?->organizationInfo->iops_id ?? null,
      ]);
    } else {
      activity('impersonate')
        ->causedBy($originalUserId)
        ->performedOn($user)
        ->withProperties(['attributes' => [
          'original_user_id' => $originalUserId,
          'original_user_name' => $originalUserName,
          'target_user_id' => $user->id,
          'target_user_name' => $user->employee?->name ?? $user->username ?? "N/A",
        ]])
        ->event('impersonate.stop')
        ->log('Stopped impersonation');

      session([
        'user_id_main' => null,
        'main_user_name' => null,
        'user_name' => null,
        Constant::SESSION_USER_ID => $user->id,
        Constant::SESSION_EMPLOYEE_ID => $user->employee?->id,
        Constant::SESSION_COMPANY => $user->employee?->company ?? \App\Models\configs\Company::where("is_parent", "Y")->first(),
        Constant::SESSION_CURRENT_FISCAL_YEAR => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first(),
        Constant::SESSION_EMPLOYEE => $user->employee,
        Constant::SESSION_IOPS_ID => $user->employee?->organizationInfo->iops_id ?? null,
      ]);
    }
    Auth::login($user);
    return $this->responseHelper(true);
  }
  
  public function personalInfo($employeeId){
      return Employee::with("user:id,username,otp,otp_expiration_date")
                        ->where("id", $employeeId)
                        ->first();
  }
  
  public function generateOTP($userId) {
      $otp = rand(100000,999999);
      User::where("id", $userId)
          ->update([
              "otp" => $otp,
              "otp_expiration_date" => date("Y-m-d H:i:s",strtotime("+1 hour")),
          ]);
      return $otp;
  }
  
  public function fetchLoginCode($userId) {
        return User::where([
                            "id" => $userId,
                        ])->whereNotNull("app_login_code")
                        ->exists();
  }
  
  public function updateLoginCode($userId, $appLoginCode) {
      return User::where("id", $userId)
                  ->update([
                      "app_login_code"      => $appLoginCode,
                      "otp"                 => null,
                      "otp_expiration_date" => null,
                  ]);
  }
}
