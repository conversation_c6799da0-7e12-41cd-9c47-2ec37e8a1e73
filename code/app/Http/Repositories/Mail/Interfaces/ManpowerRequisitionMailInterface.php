<?php

namespace App\Http\Repositories\Mail\Interfaces;

interface ManpowerRequisitionMailInterface
{
    public function sendMail(array $data);
    public function getSentEmails($mrfId);
    public function getCompanyDetails($companyId);
    public function checkEmailExists($mrfId, $email);
    public function getRemainingVacancies($mrfId);
    public function getJobDescription($mrfId, $type);
    public function getTermsAndConditions($companyId, $type);
    public function mrfPayslipDetail($mrfId);
    public function getSentEmailsCount($mrfId);
    public function getEmailData($edfEmail);
    public function updateOfferLetterResponse($uuid, array $responseData);
    public function getOauthClient();
    public function getEmployeeStatus($mrfId);
    public function getEdfResponseMeta(string $id);
}
