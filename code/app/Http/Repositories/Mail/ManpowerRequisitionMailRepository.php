<?php

namespace App\Http\Repositories\Mail;

use App\Http\Repositories\Mail\Interfaces\ManpowerRequisitionMailInterface;
use App\Jobs\OfferLetterMailJob;
use App\Models\EdfEmail;
use App\Models\configs\MailConfigurationSetting;
use App\Models\configs\Setting;
use App\Models\Tickets\EdfRequest;
use App\Models\Tickets\ManpowerRequisition;
use Barryvdh\DomPDF\Facade\Pdf;
use CodeBright\OauthAnd2fa\Models\OauthClient;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Http\Services\OfferLetterService;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\Log;

class ManpowerRequisitionMailRepository implements ManpowerRequisitionMailInterface
{
    public function sendMail(array $data): mixed
    {
        return DB::transaction(callback: function () use ($data) {
            $mrf = ManpowerRequisition::find($data['mrf_id']);

            $companyDetails = $this->getCompanyDetails($mrf->company_id);
            $companyId = currentEmployee()?->company_id ?: 1;

            // $baseUrl = 'https://ultrafidev.vianet.com.np';
            $baseUrl = MailConfigurationSetting::where('key', 'url')->where('company_id', $companyId)->value('value') ?? 'https://ultrafidev.vianet.com.np';
            $oauthClient = $this->getOauthClient();

            $edfMeta = [
                'email' => $data['email'],
                'name' => $data['name'],
                'doj' => $data['doj'],
                'deadline' => $data['deadline'],
                'ctc_offer' => $data['ctc_offer'],
                'designation' => $mrf->designation->title ?? 'To be determined',
            ];
            $mailMeta = [
                'company_id' => $mrf->company_id,
                'company_name' => $companyDetails['company_name'],
                'address' => $companyDetails['address'],
                'authorized_signatory' => $companyDetails['authorized_signatory'],
                'signatory_designation' => $companyDetails['signatory_designation'],
                'responsibilities' => $data['responsibilities'] ?? [],
                'kra' => $data['kra'] ?? [],
                'ctc_offer' => $data['ctc_offer'],
                'terms_and_conditions' => $data['terms_and_conditions'] ?? [],
                'doj_eng' => LaravelNepaliDate::from($data['doj'])->toEnglishDate(),
            ];

            $edfEmail = EdfEmail::create([
                'mrf_id' => $data['mrf_id'],
                'email' => $data['email'],
                'name' => $data['name'],
                'doj' => $data['doj'],
                'deadline' => $data['deadline'],
                'mail_meta' => $mailMeta,
            ]);

            $attachments = [];

            $attachmentConfigs = [
                'responsibilities' => 'Responsibilities',
                'kra' => 'KRA (Key Responsibility Areas)',
                'terms_and_conditions' => 'Terms and Conditions',
            ];

            foreach ($attachmentConfigs as $key => $title) {
                if (!empty($data[$key])) {
                    $pdfData = [
                        'title' => $title,
                        'content' => $data[$key],
                        'content_type' => $this->detectContentType($data[$key])
                    ];

                    $attachments[] = $this->generatePdf(
                        'emails.pdf.offer-letter-common-template',
                        $pdfData,
                        "{$key}_"
                    );
                }
            }
            $encryptedToken = $this->createEncryptedKey(
                $edfEmail->id,
                url('/'),
                $oauthClient->id,
                $oauthClient->secret
            );
            $completeUrl = "$baseUrl/yakhrm-offer-letter/$encryptedToken";
            $validityInSeconds = 7 * 24 * 60 * 60; // 7 days
            $shortUrl = generateShortUrl($completeUrl, $validityInSeconds, \Illuminate\Support\Str::random(5));
            $edfEmail->update(['shortcut' => $shortUrl]);
            $edfMeta['shortcut'] = $shortUrl;

            OfferLetterMailJob::dispatch($edfMeta, $companyId, $mailMeta, $attachments);

            return $edfEmail;
        });
    }

    // Detect content type dynamically
    private function detectContentType($content): string
    {
        if (is_array($content)) {
            return 'array';
        }

        $content = strtolower(trim($content));

        if (str_contains($content, '<table') || str_contains($content, '<tr') || str_contains($content, '<td')) {
            return 'table';
        }

        if (str_contains($content, '<ul') || str_contains($content, '<ol') || str_contains($content, '<li')) {
            return 'list';
        }

        if (str_contains($content, '<h1') || str_contains($content, '<h2') || str_contains($content, '<h3')) {
            return 'structured';
        }

        if (strip_tags($content) !== $content) {
            return 'html';
        }

        return 'plain';
    }
    public function generatePdf(string $view, array $data, string $prefix = 'attachment_'): string
    {
        // Create the PDF
        $pdf = Pdf::loadView($view, $data);

        // Dynamic options based on content type
        $options = [
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
            'chroot' => public_path(),
            'isPhpEnabled' => true,
            'dpi' => 150,
        ];

        // Add specific options for different content types
        $contentType = $data['content_type'] ?? 'html';
        switch ($contentType) {
            case 'table':
                $options['isJavascriptEnabled'] = false; // Better for tables
                break;
            case 'structured':
                $options['isJavascriptEnabled'] = true;
                break;
            default:
                $options['isJavascriptEnabled'] = false;
        }

        $pdf->setOptions($options);

        // Get DomPDF instance
        $dompdf = $pdf->getDomPDF();
        $dompdf->setCallbacks([
            'watermark' => [
                'event' => 'end_document',
                'f' => function ($pageNumber, $pageCount, $canvas, $fontMetrics) {
                    // This ensures watermark is applied to each page individually
                    Log::info("Adding watermark to page {$pageNumber} of {$pageCount}");

                    $width = $canvas->get_width();
                    $height = $canvas->get_height();

                    $canvas->page_text(
                        $width / 5,
                        $height * 0.6,
                        "CONFIDENTIAL",
                        $fontMetrics->getFont("helvetica", "bolditalic"),
                        70,
                        [0.92, 0.92, 0.92, "alpha" => 0.2],
                        0.0,
                        0.0,
                        -48.0
                    );
                }
            ]
        ]);
        // Dynamic filename
        $fileName = $prefix . Str::slug($data['title'] ?? 'document') . '_' . now()->timestamp . '.pdf';
        $storageDir = 'temp/pdfs';
        $filePath = storage_path("app/{$storageDir}/{$fileName}");

        // Ensure directory exists
        Storage::makeDirectory($storageDir);

        // Save the PDF
        $pdf->save($filePath);

        return $filePath;
    }

    public function getSentEmails($mrfId)
    {
        return EdfEmail::where('mrf_id', $mrfId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getCompanyDetails($companyId)
    {
        return MailConfigurationSetting::where('company_id', $companyId)
            ->whereIn('key', ['company_name', 'address', 'authorized_signatory', 'signatory_designation'])
            ->pluck('value', 'key')
            ->toArray();
    }

    public function checkEmailExists($mrfId, $email)
    {
        return EdfEmail::where('mrf_id', $mrfId)
            ->where('email', $email)
            ->whereIn('status', ['Approved', 'Pending'])
            ->exists();
    }

    public function getEmployeeStatus($mrfId)
    {
        $edf = EdfEmail::where('mrf_id', $mrfId)
            ->get()
            ->pluck('response_meta')
            ->map(fn($meta) => $meta['form_data']['email'] ?? null)
            ->filter()
            ->toArray();

        $existingEmails = EdfRequest::whereIn('email', $edf)
            ->pluck('email', 'id')
            ->toArray();

        return $existingEmails;
    }

    public function getRemainingVacancies($mrfId)
    {
        $mrf = ManpowerRequisition::find($mrfId);
        if (!$mrf) {
            return 0;
        }

        $sentEmailsCount = EdfEmail::where('mrf_id', $mrfId)->whereNotIn('status', ['Rejected', 'Cancelled'])->count();

        return max(0, $mrf->number_vacancy - $sentEmailsCount);
    }

    public function getJobDescription($mrfId, $type)
    {
        $mrf = ManpowerRequisition::find($mrfId);
        if (!$mrf) {
            return [];
        }

        if ($type == 'kra')
            return $mrf->kra;
        if ($type == 'job_description')
            return $mrf->responsibilities;
    }

    public function getTermsAndConditions($companyId, $type)
    {
        $config = MailConfigurationSetting::where('company_id', $companyId)
            ->where('key', $type)
            ->value('value');
        return $config ?? '';
    }

    public function mrfPayslipDetail($mrfId)
    {
        $mrf = ManpowerRequisition::find($mrfId);
        if (!$mrf) {
            return [];
        }

        return json_decode($mrf->salary_metadata, true);
    }

    public function getSentEmailsCount($mrfId)
    {
        return EdfEmail::where('mrf_id', $mrfId)->count();
    }

    public function getEmailData($edfEmail): array
    {
        return [
            'edfMeta' => [
                'name' => $edfEmail->name,
                'doj' => $edfEmail->doj,
                'deadline' => $edfEmail->deadline,
                'designation' => $edfEmail->manpowerRequisition->designation->title ?? 'To be determined',
            ],
            'ctcOffer' => $edfEmail->mail_meta['ctc_offer'] ?? $edfEmail->manpowerRequisition->salary_detail['ctc'],
            'dojEng' => $edfEmail->mail_meta['doj_eng'],
            'companyName' => $edfEmail->mail_meta['company_name'] ?? 'Our Company',
            'currentDate' => \Carbon\Carbon::now()->format('F d, Y'),
            'address' => $edfEmail->mail_meta['address'] ?? 'Our Company Address',
            'designation' => $edfEmail->mail_meta['designation'] ?? 'To be determined',
            'authorizedSignatory' => $edfEmail->mail_meta['authorized_signatory'] ?? 'HR Manager',
            'signatoryDesignation' => $edfEmail->mail_meta['signatory_designation'] ?? 'Human Resources Manager',
        ];
    }

    public function updateOfferLetterResponse($uuid, array $responseData)
    {
        return DB::transaction(function () use ($uuid, $responseData) {
            $edfEmail = EdfEmail::where('id', $uuid)->first();

            if (!$edfEmail) {
                throw new \Exception("Offer letter not found for UUID: " . $uuid);
            }

            if (!isset($responseData['type'])) {
                throw new \Exception("Response type is required.");
            }

            $responseType = $responseData['type'];
            $updateData = [
                'status' => $responseType === 'accept' ? 'Approved' : 'Rejected',
            ];

            if ($responseType !== 'accept') {
                if (empty($responseData['reason'])) {
                    throw new \Exception("Rejection reason is required.");
                }
                $updateData['reject_reason'] = $responseData['reason'];
                $updateData['response_meta'] = [
                    'type' => $responseType,
                    'submitted_at' => now()->toDateTimeString(),
                    'reason' => $responseData['reason'],
                ];

                $edfEmail->update($updateData);
                return $edfEmail;
            }

            // Handle Acceptance Case
            if (empty($responseData)) {
                throw new \Exception("Form data is required for acceptance.");
            }

            $documentData = [];
            $formData = $responseData;
            $fileKeys = OfferLetterService::getFormFileKeys();

            foreach ($fileKeys as $fileKey) {
                if (isset($responseData[$fileKey]) && $responseData[$fileKey] instanceof \Illuminate\Http\UploadedFile) {
                    $path = $responseData[$fileKey]->store('uploads/offer_letter_documents', 'public');
                    $documentData[$fileKey] = $path;
                    unset($formData[$fileKey]);
                }
                $formData[$fileKey] = $documentData[$fileKey] ?? null;
            }

            $responseMeta = [
                'type' => $responseType,
                'submitted_at' => now()->toDateTimeString(),
                'form_data' => $formData,
            ];

            $updateData['response_meta'] = $responseMeta;

            $edfEmail->update($updateData);

            return $edfEmail;
        });
    }

    public function getEdfResponseMeta(string $id)
    {
        $edfMailDetail = EdfEmail::findOrFail($id);
        $responseMeta = $edfMailDetail->response_meta;

        $detail = OfferLetterService::buildResponseSchema($responseMeta['form_data']);
        return $detail;
    }


    public function getOauthClient()
    {
        $client = OauthClient::where('name', 'Offer Letter')->first();
        if (!$client) {
            throw new \Exception("Offer letter client not found.");
        }
        return $client;
    }


    private function createEncryptedKey(string $uuid, string $baseUrl, string $clientId, string $clientSecret): string
    {
        // $encryptionKey = 'test';
        $encryptionKey = Setting::where('key', 'mrf_public_key')->value('value');
        // 2. Prepare payload
        $payload = json_encode([
            'baseUrl' => $baseUrl,
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
        ]);

        // 3. Inner encryption key derived from UUID
        $uuidKey = substr(hash('sha256', $uuid), 0, 32);
        $innerEncrypter = new Encrypter($uuidKey, config('app.cipher'));

        // 4. Encrypt payload with UUID-based key
        $encodedData = $innerEncrypter->encrypt($payload);

        // 5. Concatenate "{uuid}-{encodedData}"
        $combined = $uuid . '__' . $encodedData;

        // 6. Outer encryption key derived from APP_KEY
        $appKey = substr(hash('sha256', $encryptionKey), 0, 32);
        $outerEncrypter = new Encrypter($appKey, config('app.cipher'));

        // 7. Encrypt the combined string
        $encryptedKey = $outerEncrypter->encrypt($combined);

        return $encryptedKey;
    }
}
