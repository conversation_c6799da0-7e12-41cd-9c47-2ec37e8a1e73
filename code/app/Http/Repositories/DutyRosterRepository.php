<?php

namespace App\Http\Repositories;

use App\Models\configs\EmployeeShift;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\DutyRoster;
use App\Models\Employee\Employee;
use App\Models\EmployeeTicket\DutyChangeTicket;
use App\Models\Leaves\Attendance;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DutyRosterRepository extends Repository
{
    public function getDutyByEmployeeAndDateRange(string | int | array $employeeId, string $startDate, string $endDate)
    {
        if (!is_array($employeeId)) {
            $employeeId = [$employeeId];
        }
        return DutyRoster::with('shift')
            ->where('date_en', '<=', $endDate)
            ->where('date_en', '>=', $startDate)
            ->whereIn('employee_id', $employeeId)
            ->get();
    }

    public function saveDutyRoster(string| int $employeeId, string $date, string | int | null $shiftId, bool $dayOff)
    {
        $dutyRoster = DutyRoster::where('employee_id', $employeeId)
            ->where('date_en', $date)
            ->first();

        if ($dutyRoster) {
            $dutyRoster->shift_id = $shiftId;
            $dutyRoster->day_off = $dayOff;
            $dutyRoster->save();
        } else {
            $dutyRoster = new DutyRoster();
            $dutyRoster->employee_id = $employeeId;
            $dutyRoster->date_en = $date;
            $dutyRoster->day_off = $dayOff;
            $dutyRoster->date_np = LaravelNepaliDate::from($date)->toNepaliDate();
            $dutyRoster->shift_id = $shiftId;
            $dutyRoster->save();
        }

        return $dutyRoster;
    }

    public function createDutyRosterTicket($data)
    {
        DB::beginTransaction();
        try {
            $dutyChangeTicket = DutyChangeTicket::create($data);
            \logInfo("Duty change request created.");
            $dutyChangeTicket->applyWorkflow(WorkflowName::DUTY_CHANGE_REQUEST);
            \logInfo("workflow applied to request");

            $ticketRepo = new TicketRepository();
            $ticketRepo->createRequestTicket($dutyChangeTicket, [
                'employee_id'       => $data['employee_id'],
                'current_owner_id'  => $data['approver_id'],
                'documents'         => $data['documents'] ?? []
            ]);
            \logInfo("Ticket for the duty change request created");

            DB::commit();
            return $this->successResponse("Duty Roster Ticket Created");
        } catch (Exception $e) {
            DB::rollBack();
            \logError("Error creating duty roster ticket", $e);
            return $this->errorResponse("Error creating duty roster ticket");
        }
    }

    public function approveDutyRosterTicket(DutyChangeTicket $request)
    {
        Log::info("Approving duty roster ticket of {$request->employee_id}");

        $employee = Employee::find($request->employee_id);
        if (!$employee)
            return $this->errorResponse("Employee not found");

        $dayOff = false;
        if ($request->shift_id == null) $dayOff = true;
        else $dayOff = Carbon::parse($request->date_en)->isoWeekday() === EmployeeShift::find($request->shift_id)->day_off ? true : false;

        $dutyRoster = $this->saveDutyRoster($request->employee_id, $request->date_en, $request->shift_id, $dayOff);
        \logInfo("Duty roster saved for the ticket", $dutyRoster->toArray());

        $existingAttendance = Attendance::where([
            ['date_en', $request->date_en],
            ['employee_id', $request->employee_id]
        ])->forceDelete();
        // dd($existingAttendance);

        $requestedDate = Carbon::parse($request->date_en);
        if ($requestedDate->lte(Carbon::today())) {
            // Validate biometric ID to ensure reliable attendance synchronization
            $biometricId = $employee->organizationInfo?->biometric_id;
            if (!$biometricId)
                return $this->errorResponse("Biometric ID has not been set up for this employee");

            $syncRepo = new SyncRepository;
            $syncRepo->syncAttendanceFromDeviceToDb($request->date_en, $employee->id);
        }

        return $this->successResponse("Duty Roster ticket approved");
    }

    public function setTicketDetail($request, $type)
    {
        switch ($type) {
            case "all":
            case "card":
                $request->applied_date = LaravelNepaliDate::from($request->created_at)->toNepaliDate('F j, Y D');
                $request->nep_date = LaravelNepaliDate::from($request->date_en)->toNepaliDate('F j, Y D');

                $existingShift = $request->existingShift;
                if ($existingShift)
                    $request->existingShiftName = "$existingShift->name ({$existingShift->start_time} - {$existingShift->end_time})";
                else
                    $request->existingShiftName = "Day Off";

                $updatedShift = $request->shift;
                if ($updatedShift)
                    $request->updatedShiftName = "$updatedShift->name ({$updatedShift->start_time} - {$updatedShift->end_time})";
                else
                    $request->updatedShiftName = "Day Off";
            default:
        }
    }
}
