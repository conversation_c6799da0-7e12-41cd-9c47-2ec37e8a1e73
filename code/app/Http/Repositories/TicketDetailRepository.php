<?php

namespace App\Http\Repositories;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use Carbon\Carbon;

class TicketDetailRepository
{

    public function getNepaliDate(string | Carbon| null $date, string $format = "F j, Y D"): string
    {
        if ($date)
            return LaravelNepaliDate::from($date)->toNepaliDate($format);
        return "";
    }

    public function setLeaveApprovalDetails($request, $type): void
    {
        switch ($type) {
                // no using break statement; as all need to have information of card and default and card needs to have default
            case "all":
                $request->stateName = "leave request";
                $repo = new \App\Http\Repositories\LeaveRepository;
                $request->leaveBalance = $repo->getLeaveBalance($request->employee);
                $request->replaced_dates = $request->replacedDates->map(fn($date) => $this->getNepaliDate($date->date))->toArray();
            case "card":
                $request->nep_start_date = $this->getNepaliDate($request->start_date);
                $request->nep_end_date = $this->getNepaliDate($request->end_date);
                $request->nep_replaced_start_date = $this->getNepaliDate($request->replaced_start_date);
                $request->nep_replaced_end_date = $this->getNepaliDate($request->replaced_end_date);
                $request->nep_applied_date = $this->getNepaliDate($request->created_at) . ' ' . Carbon::parse($request->created_at)->format('g:i a');
            default:
                $request->type = 'leave_request';
                $request->title = $request->leaveType?->name;
                $request->nep_date = $this->getNepaliDate($request->start_date, 'F j') . ' - ' . $this->getNepaliDate($request->end_date, 'F j');
                $repo = new \App\Http\Repositories\LeaveRepository;
                $request->leaveCount = $repo->getLeaveCount($request->employee);
        }
    }

    public function setTimeRequestApproval($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "time request";
            case "card":
                $attendance = $request->dateAttendance->filter(fn($att) => $att->employee_id === $request->employee_id)->first();
                $request->device_in = $attendance?->in_time;
                $request->device_out = $attendance?->out_time;
                $request->nep_applied_date = $this->getNepaliDate($request->created_at) . ' ' . Carbon::parse($request->created_at)->format('g:i a');
            default:
                $request->type = 'time_request';
                $request->title = "Time Request";
                $request->nep_date = $this->getNepaliDate($request->date, 'F j, Y');
                $request->in_time = $request->in_time ? Carbon::parse($request->in_time)->format('g:i A') : "";
                $request->out_time = $request->out_time ? Carbon::parse($request->out_time)->format('g:i A') : "";
                $repo = new \App\Http\Repositories\TimeRequestRepository;
                $request->leaveCount = $repo->getTimeRequestCount($request->employee);
        }
    }

    public function setManpowerRequisitionDetail($ticket, $request, $type)
    {
        switch ($type) {
            case "all":
                $request->stateName = "manpower requisition";
                $request->documents = $ticket->getDocuments();
            case "card":
                $request->requisition_type = $request->type;
                $request->nep_applied_date = $this->getNepaliDate($request->created_at);
                $request->timeline = $this->getNepaliDate(LaravelNepaliDate::from($request->timeline)->toEnglishDate());
            default:
                $request->type = "manpower_requisition";
                $request->title = "Manpower Requisition";
        }
    }

    public function setTerminationApproval($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "employee termination";
            case "card":
                $request->nep_termination_request_date = $this->getNepaliDate($request->termination_request_date);
                $request->nep_termination_date = $this->getNepaliDate($request->termination_date);
            default:
                $request->type = 'employee_termination';
                $request->title = "Employee Termination";
        }
    }

    public function setEmployeeTransfer($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "employee transfer";
            case "card":
                $employeeTicketRepo = new EmployeeTicketRepository;
                $response = $employeeTicketRepo->getEmployeeTicketDetail($request->id);
                if ($response['status']) {
                    $request->detail = $response['data'];
                }

                $request->newDesignation = $request->detail['new_payslip_information']['Designation'] ?? null;
                $request->oldDesignation = $request->detail['old_payslip_information']['Designation'] ?? null;
                $request->process = $request->detail['process'];
                $request->applied_date = $this->getNepaliDate($request->created_at);
            default:
                $request->type = 'employee_transfer';
                $request->title = "Employee Transfer";
                $request->employeeName = $request->employee->name;
        }
    }

    public function setPaySlipApproval($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "payslip approval";
                $payslip = Payslip::with(['approvedBy:id,first_name,middle_name,last_name'])
                    ->leftJoin('payslip_requests', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
                    ->where('payslip_requests.id', $request->id)
                    ->select('payslips.approved_by', 'payslips.approved_date', 'payslips.created_at', 'payslips.employee_id')
                    ->first();

                $previousPayslip = Payslip::where([
                    ['employee_id', $request->employee_id],
                    ['created_at', '<', $request->created_at],
                ])
                    ->select('payslip_request_id')
                    ->orderBy('created_at', 'desc')
                    ->first();

                $previousPayslipRequest = PayslipRequest::with([
                    'employee:id,first_name,middle_name,last_name,company_id',
                    'employee.organizationInfo:id,employee_id,employee_code,outsource_company_id,designation_id,branch_id,department_id',
                    'employee.company:id,name,code',
                    'employee.organizationInfo.outsource_company:id,name',
                    'company:id,name,code',
                    'job:id,name',
                    'band',
                    'designation:id,title',
                    'level:id,name',
                    'grade_structure',
                    'banded_grade_structure',
                    'employee_status:id,name',
                    'payslip:payslip_request_id,approved_by,approved_date',
                    'payslip.approvedBy:id,first_name,middle_name,last_name'
                ])
                    ->where('id', $previousPayslip?->payslip_request_id)
                    ->first();

                $request->approved_by = $payslip?->approvedBy?->name;
                $request->approved_date = $payslip?->approved_date;
                $request->previousPayslip = $previousPayslipRequest;
                if ($request->employee_ticket_id) {
                    $transferTicket = EmployeeTicket::find($request->employee_ticket_id);
                    $request->transferTicketLink = route('ticketPage', ['workflow' => $transferTicket->workflow, 'requestId' => $transferTicket->id]);
                }

                $approver = $request->getPerformer(WorkflowState::APPROVED) ?? $request->getPerformer(WorkflowState::UPLOADED);
                if ($approver) {
                    $request->isApprover = $approver->id === currentEmployee()?->id;
                }
            case "card":
                $request->applied_on = $this->getNepaliDate($request->created_at);

            default:
                $request->type = 'payslip_approval';
                $request->title = "payslip approval";
                $request->employepayslipeName = $request->employee?->name;
        }
    }

    public function setReplacementLeaveRequest($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "replacement leave request";
            case "card":
                $request->nep_start_date = $this->getNepaliDate($request->start_date);
                $request->nep_end_date = $this->getNepaliDate($request->end_date);
                $request->nep_applied_date = $this->getNepaliDate($request->created_at) . ' ' . Carbon::parse($request->created_at)->format('g:i a');
            default:
                $request->type = 'replacement_leave_request';
                $request->title = "Replacement Leave Request";
                $request->nep_date = $this->getNepaliDate($request->start_date, 'F j') . ' - ' . $this->getNepaliDate($request->end_date, 'F j');
        }
    }

    public function setDutyChangeRequest($request, $type): void
    {
        switch ($type) {
            case "all":
            case "card":
                $request->applied_date = $this->getNepaliDate($request->created_at);
                $request->nep_date = $this->getNepaliDate($request->date_en);

                $existingShift = $request->existingShift;
                if ($existingShift)
                    $request->existingShiftName = "$existingShift->name ({$existingShift->start_time} - {$existingShift->end_time})";
                else
                    $request->existingShiftName = "Day Off";

                $updatedShift = $request->shift;
                if ($updatedShift)
                    $request->updatedShiftName = "$updatedShift->name ({$updatedShift->start_time} - {$updatedShift->end_time})";
                else
                    $request->updatedShiftName = "Day Off";
            default:
        }
    }

    public function setOtRequestApproval($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "ot request";
            case "card":
                $attendance = $request->attendance->filter(fn($att) => $att->employee_id === $request->employee_id)->first();
                $request->in_time = $attendance?->in_time;
                $request->out_time = $attendance?->out_time;
                $request->nep_applied_date = $this->getNepaliDate($request->created_at) . ' ' . Carbon::parse($request->created_at)->format('g:i a');
            default:
                $request->type = 'ot_request';
                $request->title = "OT Request";
                $request->nep_date = $request->nep_date;
                $request->in_time = $request->in_time ? Carbon::parse($request->in_time)->format('g:i A') : "";
                $request->out_time = $request->out_time ? Carbon::parse($request->out_time)->format('g:i A') : "";
        }
    }

    public function setTeamOtRequest($request, $type): void
    {
        switch ($type) {
            case "all":
                $request->stateName = "team ot request";
            case "card":
                $request->nep_applied_date = $this->getNepaliDate($request->created_at) . ' ' . Carbon::parse($request->created_at)->format('g:i a');
            default:
                $request->type = 'team_ot_request';
                $request->title = "Team OT Request";
                $request->nep_date = $request->nep_date;
        }
    }
}
