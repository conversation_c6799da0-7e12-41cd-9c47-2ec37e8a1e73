<?php

namespace App\Http\Repositories;

use App\Models\configs\HolidayBranch;
use App\Models\Employee\EmployeeOrg;

class HolidayRepository extends Repository {
    
    public function list($params) {
        return HolidayBranch::search($params['search'] ?? null)->whereHas('holiday', function ($query) use($params){
                                    $query->where('fiscal_year', $params['fiscal_year_id']);
                                })->where('branch_id', $params['branch_id']);
    }
    
    public function empBranch($employeeId) {
        return EmployeeOrg::where('employee_id', $employeeId)
                    ->pluck("branch_id")
                    ->first();
    }
}
