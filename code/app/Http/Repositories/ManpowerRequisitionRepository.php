<?php

namespace App\Http\Repositories;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\EmployeeTransferDetails;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\Perk;
use App\Models\Payroll\PerkModel;
use App\Models\RequestTicket;
use App\Models\Tickets\ManpowerRequisition;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ManpowerRequisitionRepository extends Repository
{

	private TicketRepository $ticketRepo;
	public function __construct()
	{
		$this->ticketRepo = new TicketRepository;
	}

	/**
	 * Create a new manpower requisition.
	 *
	 * @param  array  $data
	 * @return array
	 */
	public function create(array $data)
	{
		DB::beginTransaction();
		\logInfo("Params for creating manpower requisition: ", $data);
		$files = [];

		try {
			$salaryResponse = $this->getSalaryDetail($data['designation_id'], $data['band_id'], $data['level_id']);
			if (!$salaryResponse['status']) {
				return $salaryResponse;
			}

			if (vianetHrm()) {
				$data['roi_calculation_metadata'] = json_encode([
					'tangible_benefits' => $data['dynamicRows'],
					'total_tangible_benefits' => $data['total_tangible_benefits'],
					'total_investment'  => $data['roi_total_investment'],
					'roi_calculation'   => $data['roi_calculation']
				]);

				$data['infrastructure_metadata'] = json_encode([
					'displayed' => $data['displayed'],
					'specific_details' => $data['specificDetails'],
				]);
			}

			// if (!scopeAll()) {
			// 	$data['company_id'] = currentEmployee()?->company_id;
			// 	if (scopeBranch()) {
			// 		$data['branch_id'] = currentEmployee()?->organizationInfo?->branch_id;
			// 	} else if (scopeDepartment()) {
			// 		$data['branch_id'] = currentEmployee()?->organizationInfo?->branch_id;
			// 		$data['department_id'] = currentEmployee()?->organizationInfo?->department_id;
			// 	}
			// }

			// Create Manpower Requisition
			$requisition = ManpowerRequisition::create([
				...$data,
				'employee_id'			=> currentEmployee()?->id,
				'salary_metadata'	=> json_encode($salaryResponse['data']),
			]);
			$requisition->applyWorkflow(WorkflowName::MANPOWER_REQUISITION);
			\logInfo("Manpower requisition created.", $requisition->toArray());

			// Assign perks to created manpower requisition
			$response = $this->assignPerks($data['perks'], $requisition);
			if (!$response['status']) {
				return $response;
			}
			\logInfo("Perks added for the requisition", $response['data']);

			$this->ticketRepo->createRequestTicket($requisition, [
				'current_owner_id'	=> $data['next_owner_id'],
				'employee_id'				=> \currentEmployee()?->id,
				'documents'					=> $data['documents']
			]);

			// Send email and notification
			// $this->sendMailAndNotification($requisition, $nextOwnerRole, $requestTicket->current_owner_id);

			DB::commit();
			return $this->responseHelper(true, "Manpower Requisition Created Successfully");
		} catch (\Exception $e) {
			DB::rollBack();
			Log::error("Error creating manpower requisition: " . \errorContext($e));

			// Delete uploaded file in case of error
			foreach ($files as $file) {
				File::delete(public_path('storage/' . $file));
			}

			return $this->responseHelper(false, "Error while creating manpower requisition");
		}
	}

	/**
	 * Update an existing manpower requisition.
	 *
	 * @param  int  $requisitionId
	 * @param  array  $data
	 * @return array
	 */
	public function update(int $requisitionId, array $data)
	{
		DB::beginTransaction();
		\logInfo("Params for updating manpower requisition: ", $data);
		$files = [];

		try {
			// check if the requisition exists or not
			$requisition = ManpowerRequisition::find($requisitionId);
			if (!$requisition) {
				return $this->responseHelper(false, "Manpower Requisition Not Found");
			}

			$salaryResponse = $this->getSalaryDetail($data['designation_id'], $data['band_id'], $data['level_id']);
			if (!$salaryResponse['status']) {
				return $salaryResponse;
			}

			if (vianetHrm()) {
				$data['roi_calculation_metadata'] = json_encode([
					'tangible_benefits' => $data['dynamicRows'],
					'total_tangible_benefits' => $data['total_tangible_benefits'],
					'total_investment'  => $data['roi_total_investment'],
					'roi_calculation'   => $data['roi_calculation']
				]);

				$data['infrastructure_metadata'] = json_encode([
					'displayed' => $data['displayed'],
					'specific_details' => $data['specificDetails'],
				]);
			}

			// update the requisition data
			$requisition->update([
				...$data,
				'salary_metadata'	=> json_encode($salaryResponse['data']),
			]);
			\logInfo("Requisition Updated", $requisition->toArray());

			// delete older perks value
			PerkModel::where(['model_type' => get_class($requisition), 'model_id' => $requisition->id])->delete();
			\logInfo("Older perks data deleted");

			// assign the perks to the requisition
			$response = $this->assignPerks($data['perks'], $requisition);
			if (!$response['status']) {
				return $response;
			}
			\logInfo("Perks added for the requisition", $response['data']);

			// update the request ticket associated with the requisition
			$oldVerifierId = $requisition->requestTicket->current_owner_id;
			$this->ticketRepo->updateRequestTicket($requisition, [
				'current_owner_id'			=> $data['next_owner_id'],
				'documents'							=> $data['documents'],
				'removing_document_ids'	=> $data['removing_document_ids']
			]);

			// send email and notification if the verifier is changed, else don't send any notification
			// if ($data['next_owner_id'] !== $oldVerifierId) {
			// 	$this->sendMailAndNotification($requisition, $requisition->nextOwnerRole(isSubmitting: true), $requestTicket->next_owner_id);
			// }

			DB::commit();
			\logInfo("Manpower Requisition Updated");
			return $this->responseHelper(true, "Manpower requisition updated successfully");
		} catch (\Exception $e) {
			DB::rollBack();
			foreach ($files as $file) {
				File::delete(public_path('storage/' . $file));
			}
			Log::error("Error while updating manpower requisition with id $requisitionId: " . \errorContext($e));
			return $this->responseHelper(false, "Error updating manpower requisition");
		}
	}

	public function getSalaryDetail($designationId, $bandId, $levelId)
	{
		// Get grade structure
		$gradeStructure = GradeStructure::where([
			['grade_id', $designationId],
			['band_id', $bandId],
			['pgrade_id', $levelId]
		])->first();

		// Check if grade structure exists
		if (!$gradeStructure) {
			return $this->responseHelper(false, "Grade structure not found for given band and designation");
		}
		$salaryDetails = [
			"basic_salary"  => $gradeStructure->basic_salary,
			"pf"            => $gradeStructure->pf,
			"ssf"           => $gradeStructure->ssf,
			"allowance"     => $gradeStructure->allowance,
			"gross_salary"  => $gradeStructure->gross_salary,
			"gratuity"      => $gradeStructure->gratuity,
			"ctc"           => $gradeStructure->ctc,
		];

		// prepare salary_metadata
		return $this->responseHelper(true, data: $salaryDetails);
	}

	/**
	 * assign perks to created manpower requisition, also check if the perk can be updated or not
	 * 
	 * @param array $perks
	 * @param \App\Models\Tickets\ManpowerRequisition $requisition
	 * @param bool $update used for updating perks of manpower requisition, will remove all old perks and add new perks
	 */
	private function assignPerks(array $perks, ManpowerRequisition $requisition, bool $update = false)
	{
		$assignedPerks = [];
		foreach ($perks as $id => $value) {
			if ($value) {
				$perkDetail = Perk::find($id);
				if (!$perkDetail) {
					return $this->responseHelper(false, "Perk Detail not found");
				}
				if ($perkDetail->is_global && $perkDetail->amount != (float)$value) {
					return $this->responseHelper(false, "Perk amount of $perkDetail->name not matched for global value");
				}
				if ($update) {
					$perk = PerkModel::updateOrCreate([
						'model_type'    => get_class($requisition),
						'model_id'      => $requisition->id,
						'perk_id'       => $id,
						'amount'        => $value
					], [
						'model_type'    => get_class($requisition),
						'model_id'      => $requisition->id,
						'perk_id'       => $id,
					]);
				} else {
					$perk = PerkModel::create([
						'model_type'    => get_class($requisition),
						'model_id'      => $requisition->id,
						'perk_id'       => $id,
						'amount'        => $value
					]);
				}
				array_push($perks, $perk->toArray());
			}
		}
		return $this->responseHelper(true, data: $assignedPerks);
	}

	/**
	 * send email and notification for manpower requisition
	 * 
	 * @param ManpowerRequisition $requisition
	 * @param string $nextOwnerRole
	 * @param int $nextOwnerId
	 */
	private function sendMailAndNotification(ManpowerRequisition $requisition, string $nextOwnerRole, int $nextOwnerId)
	{
		// Send mail
		Mail::to($requisition->currentOwner->organizationInfo->email)->send(new \App\Mail\NewManpowerRequisition(
			$requisition,
			route('myTickets', ['type' => 'toBeReviewedRequests', 'flow' => $requisition->workflow, 'id' => $requisition->model_id]),
		));

		// Send notification
		\App\Http\Repositories\TicketRepository::sendNotification(
			$requisition,
			"",
			$requisition->state,
			$requisition->employee_id,
			"Submitter",
			$nextOwnerId,
			$nextOwnerRole
		);
	}

	/**
	 * Update the seat count for the corresponding department and job.
	 *
	 * @param ManpowerRequisition $requisition The manpower requisition model instance.
	 * @return void
	 */
	public function approveManpowerRequisition(ManpowerRequisition $requisition): array
	{
		$replacedTypeRequisitons = ['replacement', 'resign', 'terminate'];
		if (in_array($requisition->type, $replacedTypeRequisitons) && isset($requisition->incumbent_id)) {
			logInfo("Update EMP Transfer Detail", [
				"Incubment Id" => $requisition->incumbent_id,
				"Company Id" => $requisition->company_id,
				"Branch Id" => $requisition->branch_id,
				"Department Id" => $requisition->department_id
			]);
			$update = EmployeeTransferDetails::where([
				"employee_id"   => $requisition->incumbent_id,
				"company_id" => $requisition->company_id,
				"branch_id" => $requisition->branch_id,
				"department_id" => $requisition->department_id
			])->update([
				"replaced" => "Y"
			]);

			logInfo("Update EMP Transfer Detail", [
				"Incubment Id" => $requisition->incumbent_id,
				"Company Id" => $requisition->company_id,
				"Branch Id" => $requisition->branch_id,
				"Department Id" => $requisition->department_id,
				"Response" => $update,
			]);
		} else {
			$repo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);

			$repo->increaseSeatPlan([
				'company_id' => $requisition->company_id,
				'branch_id' => $requisition->branch_id,
				'department_id' => $requisition->department_id,
				'job_id' => $requisition->job_id
			], $requisition->number_vacancy);
		}
		return $this->responseHelper(true);
	}
}
