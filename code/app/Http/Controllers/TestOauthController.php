<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class TestOauthController extends Controller
{
    private $clientId, $clientSecret, $oauthBaseUrl, $publicClientId;

    public function __construct()
    {
        $this->oauthBaseUrl = env('TEST_OAUTH_BASE_URL');
        $this->clientId = env('TEST_OAUTH_CLIENT_ID');
        $this->clientSecret = env('TEST_OAUTH_CLIENT_SECRET');
        $this->publicClientId = env('TEST_OAUTH_PUBLIC_CLIENT_ID');
        // dd($this->clientId, $this->clientSecret , $this->oauthBaseUrl);
        if (!$this->oauthBaseUrl || !$this->clientId || !$this->clientSecret) {
            abort('403', 'Missing OAuth configuration.');
        }
    }

    public function loginPage()
    {
        $state = Str::uuid()->toString();
        Cache::put("oauth_state:$state", true, now()->addHour());
        $redirectUri = route('testCallback');
        $query = http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $redirectUri,
            'response_type' => 'code',
            'state' => $state,
            'scope' => 'employee.get-own-detail'

        ]);
        // $link = "http://localhost:8010/oauth/authorize?{$query}";
        $link = "{$this->oauthBaseUrl}/oauth/authorize?{$query}";
        $linkV2 = "{$this->oauthBaseUrl}/oauth/v2/authorize?{$query}";
        // $linkV2 = "http://localhost:8010/oauth/v2/authorize?{$query}";
        return view('test.login', compact('link', 'linkV2'));
    }

    public function homePage()
    {
        $name = session('testName');
        return view('test.home', compact('name'));
    }

    public function guzzleClient(): GuzzleClient
    {
        $client = new GuzzleClient([
            'base_uri' => $this->oauthBaseUrl,
            'verify' => false,
            // 'http_errors' => false
        ]);
        return $client;
    }

    // for new implementation version 2.*
    public function callback2(Request $request)
    {
        if ($request->get('encrypted_token')) {
            return $this->callback1($request);
        }
        $code = $request->get('code');
        $state = $request->get('state');

        $cacheKey = "oauth_state:$state";
        if (!$state || !Cache::pull($cacheKey)) {
            abort(403, 'Invalid or missing OAuth state.');
        }

        if ($request->get('error')) {
            dd("Oauth Error", $request->all());
            abort(403, 'OAuth error: ' . $request->get('error_description', $request->get('error')));
        }

        $tokenResponse = $this->callApi(
            $this->guzzleClient(),
            'post',
            '/oauth/token',
            [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'redirect_uri' => route('testCallback'),
                    'code' => $code,
                ],
            ]
        );
        dump("Token Response", $tokenResponse);
        $accessToken = $tokenResponse['access_token'];

        $data = $this->callApi($this->guzzleClient(), 'post', '/api/get-auth-details', [
            'headers' => [
                'Authorization' => "Bearer {$accessToken}"
            ]
        ]);

        dd("Calling basic auth details", $data);
    }

    // for old implementation version 1.*
    public function callback1(Request $request)
    {
        $response = $this->callApi(
            $this->guzzleClient(),
            'post',
            'api/get-basic-auth-details',

            [
                'auth' => ['CodeBrightHRM', 'q~Y`2%0T2/vnaww=&3,C'],
                'form_params' => [
                    'encrypted_token' => $request->get('encrypted_token')
                ],
            ]
        );
        dd($response);
    }

    public function syncAttendanceLogs()
    {
        $this->syncBasicAuthAttendanceLogs();
        $response = $this->callApi(
            $this->guzzleClient(),
            'post',
            '/oauth/token',
            [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'scope' => 'attendance.sync-att-logs'
                ],
            ]
        );

        $accessToken = $response['access_token'];
        $response = $this->callApi($this->guzzleClient(), 'post', "/api/sync-att-logs", [
            'headers' => [
                'Authorization' => "Bearer {$accessToken}"
            ],
        ]);
        dd($response);
    }

    public function syncBasicAuthAttendanceLogs()
    {
        $response = $this->callApi(
            $this->guzzleClient(),
            'post',
            '/api/sync-att-logs',
            [
                'auth' => ['CodeBrightHRM', 'q~Y`2%0T2/vnaww=&3,C'],
            ]
        );
        dd($response);
    }

    private function callApi(GuzzleClient $guzzleClient, string $method, string $uri, array $options = [])
    {
        try {
            $response = $guzzleClient->request($method, $uri, $options);
            return json_decode($response->getBody(), true);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            dd($uri, $e);
            $error = json_decode($e->getResponse()->getBody(), true);
            dd($uri, $error);
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            dd($e);
            $error = json_decode($e->getResponse()->getBody(), true);
            dd($uri, $error);
        }
    }

    /* ------------- For PKCE ------------- */

    /** RFC 7636: verifier = 43–128 chars, URL-safe */
    private function makePkceVerifier(): string
    {
        $len = 64;
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
        $out = '';
        $bytes = random_bytes($len);
        for ($i = 0; $i < $len; $i++) {
            $out .= $alphabet[ord($bytes[$i]) % strlen($alphabet)];
        }
        return $out;
    }

    /** challenge = BASE64URL(SHA256(verifier)) without '=' padding */
    private function makeChallenge(string $verifier): string
    {
        $hash = hash('sha256', $verifier, true);
        return rtrim(strtr(base64_encode($hash), '+/', '-_'), '=');
    }

    public function pkceStart()
    {
        $verifier = $this->makePkceVerifier();
        $challenge = $this->makeChallenge($verifier);
        $state = Str::uuid()->toString();

        session([
            'pkce_verifier' => $verifier,
            'oauth_state' => $state
        ]);

        $query = http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => route('testCallback'),
            'response_type' => 'code',
            'state' => $state,
            'code_challenge' => $challenge,
            'code_challenge_method' => 'S256',
            'scope' => 'employee.get-own-detail'
        ]);

        $link = "{$this->oauthBaseUrl}/oauth/authorize?{$query}";
        return view('test.login', compact('link'));
    }
}
