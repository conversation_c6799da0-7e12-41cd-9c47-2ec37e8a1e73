<?php

namespace App\Http\Controllers;

use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class EmployeeController extends Controller
{
    public function getEmployeeWithoutIopsId()
    {
        $result = Employee::leftJoin('employee_org as org', 'employees.id', 'org.employee_id')
            ->leftJoin('companies as company', 'company.id', 'employees.company_id')
            ->select(
                'employees.id as employee_id',
                Employee::selectEmpCodeRawQuery('company', 'org', 'emp_code'),
                'org.biometric_id',
                'employees.first_name',
                'employees.middle_name',
                'employees.last_name',
            )
            ->whereNull('iops_id')
            ->get();
        return response()->json(ApiResponseHelper::getData($result));
    }

    public function updateIopsId(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'iops_id' => 'required',
        ]);

        $employeeOrg = EmployeeOrg::where('employee_id', $request->employee_id)->first();
        if (!$employeeOrg) return response()->json(ApiResponseHelper::notFound("Organization Info not found"));

        if ($employeeOrg->iops_id) return response()->json(ApiResponseHelper::errorMessage("Can't update iops id"));

        $employeeOrg->iops_id = $request->iops_id;
        $employeeOrg->save();
        return response()->json(ApiResponseHelper::successMessage("IOPS ID updated successfully"));
    }

    public function getEmployeeCodeAndBiometric(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'iops_id'       => 'required',
        ]);

        if ($validator->fails()) {
            $toReturn = ApiResponseHelper::validationError($validator->errors());
            return response()->json($toReturn, $toReturn['statusCode']);
        }

        $employeeOrgQuery = EmployeeOrg::leftJoin('employees', 'employee_org.employee_id', '=', 'employees.id')
            ->leftJoin('companies', 'companies.id', '=', 'employees.company_id')
            ->where('iops_id', $request->iops_id)
            ->select(
                DB::raw("TRIM(CONCAT(companies.code,'-',employee_org.employee_code)) as emp_code"),
                'employee_org.biometric_id as biometric_id',
            );

        $employeeOrg = $employeeOrgQuery->first();

        if (!$employeeOrg) {
            $isEmployeeTerminated = $employeeOrgQuery->withTrashed()->first();
            $message = $isEmployeeTerminated ? "Employee is terminated in HRM" : "Organization information not found";
            $toReturn = ApiResponseHelper::notFound($message);
            if ($isEmployeeTerminated) {
                $toReturn = ApiResponseHelper::forbidden($message);
            }
            return response()->json($toReturn);
        }

        $data = [
            "biometric_id" => $employeeOrg->biometric_id,
            "employee_code" => $employeeOrg->emp_code,
        ];

        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn);
    }

    public function getDesignationDetail(int $id)
    {
        $designation = \App\Models\Payroll\Designation::find($id);
        if (!$designation) {
            return ApiResponse::notFound("Designation not found");
        }
        return ApiResponse::getData(['title' => $designation->title]);
    }

    public function getJobDetail(int $id)
    {
        $job = \App\Models\configs\Job::find($id);
        if (!$job) {
            return ApiResponse::notFound("Job not found");
        }
        return ApiResponse::getData(['name' => $job->name]);
    }
}
