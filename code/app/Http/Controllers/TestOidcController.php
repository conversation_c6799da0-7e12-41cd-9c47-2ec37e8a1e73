<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class TestOidcController extends Controller
{
    private string $clientId;
    private string $clientSecret;
    private string $oauthBaseUrl;
    private string $redirectRouteName = 'oidcCallback';
    private string $scopes = 'openid'; 

    public function __construct()
    {
        $this->oauthBaseUrl = env('TEST_OAUTH_BASE_URL');         // e.g. https://cb-hrm.test
        $this->clientId     = env('TEST_OAUTH_CLIENT_ID');        // Passport auth code client_id
        $this->clientSecret = env('TEST_OAUTH_CLIENT_SECRET');    // Passport auth code client_secret

        if (!$this->oauthBaseUrl || !$this->clientId || !$this->clientSecret) {
            abort(403, 'Missing OIDC/OAuth config.');
        }
    }

    /**
     * 1. Show "Login with OIDC" link
     */
    public function loginPage()
    {
        // OIDC requires state (CSRF) and nonce (replay protection for ID token)
        $state = Str::uuid()->toString();
        $nonce = Str::uuid()->toString();

        // persist so we can verify later
        Cache::put("oidc_state:$state", true, now()->addHour());
        Cache::put("oidc_nonce:$state", $nonce, now()->addHour());

        $redirectUri = route($this->redirectRouteName);

        // PKCE optional; if you enforce PKCE in your server, include it here like your pkceStart() does.
        // I'll show without PKCE first.
        $query = http_build_query([
            'client_id'     => $this->clientId,
            'redirect_uri'  => $redirectUri,
            'response_type' => 'code',
            'scope'         => $this->scopes,
            'state'         => $state,
            'nonce'         => $nonce, // <-- important for OIDC
        ]);

        $authorizeUrl = "{$this->oauthBaseUrl}/oauth/v2/authorize?{$query}";
        // $authorizeUrl = "http://localhost:8010/oauth/v2/authorize?{$query}";

        return view('test.oidc-login', [
            'authorizeUrl' => $authorizeUrl,
        ]);
    }

    /**
     * 2. OIDC callback: exchange code -> tokens (access_token, id_token, refresh_token)
     */
    public function callback(Request $request)
    {
        $code  = $request->get('code');
        $state = $request->get('state');
        $error = $request->get('error');

        if ($error) {
            abort(403, 'OIDC Error: ' . $request->get('error_description', $error));
        }

        // Validate state
        $cacheKey = "oidc_state:$state";
        if (!$state || !Cache::pull($cacheKey)) {
            abort(403, 'Invalid or missing OIDC state.');
        }

        // We'll also grab stored nonce (we'll compare it with the nonce inside id_token later)
        $nonce = Cache::pull("oidc_nonce:$state");

        // Now exchange the code for tokens
        $tokenResponse = $this->callApi(
            $this->guzzleClient(),
            'post',
            '/oauth/token',
            [
                'form_params' => [
                    'grant_type'    => 'authorization_code',
                    'client_id'     => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'redirect_uri'  => route($this->redirectRouteName),
                    'code'          => $code,
                    // 'code_verifier' => ... if PKCE is enabled
                ],
            ]
        );

        // Example shape we expect:
        // [
        //   "token_type"    => "Bearer",
        //   "expires_in"    => 3600,
        //   "access_token"  => "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
        //   "refresh_token" => "....",
        //   "id_token"      => "eyJhbGciOiJSUzI1NiIsImtpZCI6IjEifQ..."
        // ]

        if (empty($tokenResponse['access_token'])) {
            dd('No access_token in token response', $tokenResponse);
        }

        // Optional but useful: inspect id_token claims
        $decodedIdToken = [];
        if (!empty($tokenResponse['id_token'])) {
            $decodedIdToken = $this->decodeJwtWithoutVerification(
                $tokenResponse['id_token']
            );

            // If you want, sanity check nonce:
            // if (($decodedIdToken['nonce'] ?? null) !== $nonce) { ... throw ... }
        }

        // 3. Call /oauth/userinfo using the access_token
        $userinfo = $this->callApi(
            $this->guzzleClient(),
            'get',
            '/oauth/userinfo',
            [
                'headers' => [
                    'Authorization' => 'Bearer ' . $tokenResponse['access_token'],
                    'Accept'        => 'application/json',
                ],
            ]
        );

        // Store anything you want in session (logged-in user name, etc.)
        session([
            'oidc_name'         => $userinfo['name']      ?? null,
            'oidc_sub'          => $userinfo['sub']       ?? null,
            'oidc_email'        => $userinfo['email']     ?? null,
            'oidc_id_token'     => $tokenResponse['id_token'] ?? null,
            'oidc_id_payload'   => $decodedIdToken,
            'oidc_access_token' => $tokenResponse['access_token'],
        ]);

        // Demo: dump everything
        return view('test.oidc-success', [
            'tokenResponse'   => $tokenResponse,
            'decodedIdToken'  => $decodedIdToken,
            'userinfo'        => $userinfo,
        ]);
    }

    /**
     * 3. A "home" or dashboard page after login
     */
    public function profilePage()
    {
        return view('test.oidc-profile', [
            'name'        => session('oidc_name'),
            'email'       => session('oidc_email'),
            'sub'         => session('oidc_sub'),
            'id_token'    => session('oidc_id_token'),
            'id_payload'  => session('oidc_id_payload'),
        ]);
    }

    /* ----------------- SUPPORT METHODS ----------------- */

    public function guzzleClient(): GuzzleClient
    {
        return new GuzzleClient([
            'base_uri' => $this->oauthBaseUrl,
            'verify'   => false,
        ]);
    }

    private function callApi(GuzzleClient $guzzleClient, string $method, string $uri, array $options = [])
    {
        try {
            $response = $guzzleClient->request($method, $uri, $options);
            return json_decode($response->getBody(), true);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $error = json_decode($e->getResponse()->getBody(), true);
            dd('ClientException '.$uri, $error);
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            $error = json_decode($e->getResponse()->getBody(), true);
            dd('ServerException '.$uri, $error);
        }
    }

    /**
     * Decode a JWT (id_token) into header+payload WITHOUT verifying signature.
     * This is only for debugging in dev.
     */
    private function decodeJwtWithoutVerification(string $jwt): array
    {
        $parts = explode('.', $jwt);

        if (count($parts) < 2) {
            return ['error' => 'invalid_jwt_structure'];
        }

        [$headerB64, $payloadB64] = $parts;
        $header  = json_decode($this->b64urlDecode($headerB64), true);
        $payload = json_decode($this->b64urlDecode($payloadB64), true);

        return [
            'header'  => $header,
            'payload' => $payload,
        ];
    }

    private function b64urlDecode(string $data): string
    {
        $replaced = strtr($data, '-_', '+/');
        $padLen = 4 - (strlen($replaced) % 4);
        if ($padLen < 4) {
            $replaced .= str_repeat('=', $padLen);
        }
        return base64_decode($replaced) ?: '';
    }
}
