<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Configs\Interfaces\INotificationRepository;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Http\Repositories\TicketRepository;
use PHPUnit\Framework\Attributes\Ticket;
use Tymon\JWTAuth\Facades\JWTAuth;

class DashboardController extends Controller
{

    public function __construct(
        private TicketRepository $ticketRepo,
        private INotificationRepository $notificationRepo
    ) {}

    /**
     * @lrd:start
     * ## Get Dashboard Data
     * Fetches all the necessary data for the main dashboard screen of the mobile app.
     * This includes a summary of pending tickets, the user's most recent tickets, and a list of available menu items.
     * It also returns the count of unread notifications for the authenticated user.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function dashboard()
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        $workflows = app(AppSettingRepositoryInterface::class)->getEnabled();

        $tickets = $workflows ? $this->ticketRepo->getMyTickets($employeeId, $workflows) : [];
        $highlights = [
            'Field Visit Task Checked in',
            'Field Visit Task Checked out',
            'Field Visit Task Stand by'
        ];

        $workflowIcons = WorkflowName::getAppIcons();

        foreach ($tickets as $key => $ticket) {
            $tickets[$key]['workflow_name'] = ucfirst(str_replace("_", " ", $ticket['workflow']));
            $tickets[$key]['workflow_icon'] = $workflowIcons[$ticket['workflow']] ?? 'default-icon';
        }

        $pendingTickets = $workflows ? $this->ticketRepo->getPendingTicket($employeeId, $workflows) : [];
        foreach ($pendingTickets as $key => $ticket) {
            $pendingTickets[$key]['workflow_name'] = ucfirst(str_replace("_", " ", $ticket['workflow']));
            $pendingTickets[$key]['workflow_icon'] = $workflowIcons[$ticket['workflow']] ?? 'default-icon';
        }

        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");
        $notifications = $this->notificationRepo->notificationList(['user_id' => $userId]);
        $unreadNotificationCount = count($notifications['unread_notifications']);

        $data = [
            "data" => [
                [
                    "title" => "Pending Tickets",
                    "type"  => "pending_tickets",
                    "content" => $pendingTickets,
                ],
                [
                    "title" => "My Tickets",
                    "type"  => "ticket",
                    "content" => $tickets,
                ],
                [
                    "title" => "Menus",
                    "type"  => "menu",
                    "content" => [
                        // [
                        //     "name" => "Search Employee",
                        //     "icon" => "icon",
                        //     "type" => "search-employee"
                        // ],
                        [
                            "name" => "My Attendance",
                            "icon" => "icon",
                            "type" => "attendance"
                        ],
                        // [
                        //     "name" => "My Leave Details",
                        //     "icon" => "icon",
                        //     "type" => "leave-details"
                        // ],
                        [
                            "name" => "My Tickets",
                            "icon" => "icon",
                            "type" => "tickets"
                        ],
                        // [
                        //     "name" => "My Holiday List",
                        //     "icon" => "icon",
                        //     "type" => "holiday-list"
                        // ]
                    ]
                ],
            ],
            "unread_notification_count" => $unreadNotificationCount,
            "highlights" => $highlights,
        ];

        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }
}
