<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Constant;
use App\Http\Repositories\LeaveRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;

class LeaveController extends Controller
{
    public function __construct() {
        $this->leaveRepo = new LeaveRepository();
    }
    
    /**
     * @lrd:start
     * ## My Leave Details
     * Fetches the leave details for the authenticated user for the current active fiscal year.
     * This includes a list of available leave types with their balances, options, and any extra parameters required for specific leave types (like for FedEx).
     * It also returns a list of potential verifiers for submitting leave requests.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function myLeaveDetails() {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        $params = [
                        "employeeId" => $employeeId,
                        "fiscalYear" => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first()
                ];
        $leaveDetails = $this->leaveRepo->myLeaveDetails($params);        
        $collectionWithoutIndexes = $leaveDetails->values();
        $leaves = $collectionWithoutIndexes->toArray();
        foreach ($leaves as $key =>$leave) {
            $params = [
                "leaveType"  => $leave['name'],
                "employeeId" => $employeeId,
            ];
            $leaveOptions = $this->leaveRepo->leaveOptions($params);
            $leaveOptions = array_map(function ($id, $name) {
                return [
                    'id'   => $id,
                    'name' => $name
                ];
            }, array_keys($leaveOptions), $leaveOptions);
            $leaves[$key]['leave_options'] = $leaveOptions;
            
            if(fedexHrm() && $leave['name'] == Constant::REPLACEMENT_LEAVE_NAME) {
                $leaves[$key]['extra_params'] = [
                                                    [
                                                        "name" => "replaced_start_date",
                                                        "title" => "On Duty Start Date"
                                                    ],
                                                    [
                                                        "name" => "replaced_end_date",
                                                        "title" => "On Duty End Date"
                                                    ]
                                                ];
            }
        }

        $params = [
            "employee_id"   => $employeeId,
            "is_submitting" => true,
        ];
        $verifiers = $this->leaveRepo->verifiers($params);
        
        $toReturn = ApiResponseHelper::getData([
            "leaves"    => $leaves,
            "verifiers" => $verifiers,
        ]);
        return response()->json($toReturn);
    }
    
    /**
     * @lrd:start
     * ## Assigned Leave Types
     * Fetches a list of leave types that are assigned to the authenticated user for the current fiscal year.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function assignedLeaveTypes() {
        $params = [
            "employee_id"    => JWTAuth::parseToken()->getPayload()->get("employee_id"),
            "fiscal_year_id" => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first(),
        ];
        $assignedLeaves =  $this->leaveRepo->assignedLeaves($params);
        $toReturn = ApiResponseHelper::getData([
            "assigned_leaves"  => $assignedLeaves,
        ]);
        return response()->json($toReturn);
    }
    
/*    //Remaining API
    public function apply(Request $request) {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        Log::info("Apply Leave. Employee Id =>".$employeeId.". Params =>".print_r($request->all(), true));
        $validator = Validator::make($request->all(), [
            'leave_type_id'    => 'required',
            'leave_start_date' => 'required',
            'leave_end_date'   => 'required',
            'leave_option'     => 'required',
            'reason'           => 'required',
            'verifier'         => 'required',
            'documents'        => 'required',
        ]);

        if ($validator->fails()) {
            Log::info("Apply Leave. Params =>" . print_r($request->all(), true).". Validation Failed => " . print_r($validator->errors(), true));
            $toReturn = ApiResponseHelper::validationError($validator->errors());
            return response()->json($toReturn, $toReturn['statusCode']);
        }

        $params = [
            'leave_type_id'         => $request->leave_type_id,
            'start_date'            => $request->leave_start_date,
            'end_date'              => $request->leave_end_date,
            'nep_start_date'        => $this->convertToNepDate($request->leave_start_date),
            'nep_end_date'          => $this->convertToNepDate($request->leave_end_date),
            'replaced_start_date'   => null,
            'replaced_end_date'     => null,
            'fiscal_year_id'        => $this->fiscalYearId,
            'num_days'              => (Carbon::parse($request->leave_start_date)->diffInDays($request->leave_end_date) + 1) * $multiplier,
            'leave_option_id'       => $request->leave_option_id,
            'applied_status'        => Carbon::now() < Carbon::parse($request->start_date) ? 'before' : 'after',
            'remarks'               => $this->remarks,
            'verifier_id'           => $this->verifier_id,
            'employee_id'           => $this->employee_id,
            'documents'             => $this->documents,
            'removing_document_ids' => $this->removingDocumentIds
        ];

        $applyLeave = $this->leaveRepo->apply($params);

        if($applyLeave) {
            $toReturn = ApiResponseHelper::getData($applyLeave, 'Leave Applied Successfully.');
        }
        else {
            $toReturn = ApiResponseHelper::unauthorized("Unable to apply leave.");
        }

        Log::info("Apply Leave. Params =>".print_r($request->all(), true)." Response => ".print_r(json_encode($toReturn), true));
        return response()->json($toReturn, $toReturn['statusCode']);
    }*/
}
