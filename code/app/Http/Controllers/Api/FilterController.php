<?php

namespace App\Http\Controllers\Api;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Constant;
use App\Http\Repositories\LeaveRepository;
use App\Http\Repositories\Reports\AttendanceRepository;
use Carbon\Carbon;

class FilterController extends Controller
{
    public function __construct() {
        $this->attendanceRepo = new AttendanceRepository();
        $this->leaveRepo = new LeaveRepository();
    }

    /**
     * @lrd:start
     * ## Get Filter Options
     * Fetches various lists of data that can be used to populate filter dropdowns in the application.
     * This includes lists for years, months, departments, fiscal years, leave types, and ticket states.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function filters() {
        $currentYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        for ($i = 0; $i < 5; $i++) {
            $yearList[] = $currentYear - $i;
        }

        $monthList = Constant::NEPALI_MONTH_LIST;
        $monthList = array_map(function ($id, $name) {
            return [
                "id"   => (string)$id,
                "name" => $name,
            ];
        }, array_keys($monthList), $monthList);

        $departmentList = $this->attendanceRepo->departmentList();
        $departmentList = array_map(function ($id, $name) {
            return [
                'id'   => $id,
                'name' => $name
            ];
        }, array_keys($departmentList), $departmentList);

        $activeFiscalYear = $this->leaveRepo->activeFiscalYear();
        $fiscalYearList = $this->leaveRepo->fiscalYearList();
        $fiscalYearList = array_map(function ($id, $name) use($activeFiscalYear) {
            return [
                'id'      => $id,
                'name'    => $name,
                'current' => ($activeFiscalYear == $id) ? 'Yes' : 'No',
            ];
        }, array_keys($fiscalYearList), $fiscalYearList);

        $leaveTypeList = $this->leaveRepo->leaveTypeList();
        $leaveTypeList = $leaveTypeList->select("id", "name");

        $stateList = $this->leaveRepo->stateList();

        $data = [
            "yearList"          => $yearList,
            "monthList"         => $monthList,
            "departmentList"    => $departmentList,
            "fiscalYearList"    => $fiscalYearList,
            "leaveTypeList"     => $leaveTypeList,
            "stateList"         => $stateList,
        ];

        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }
}
