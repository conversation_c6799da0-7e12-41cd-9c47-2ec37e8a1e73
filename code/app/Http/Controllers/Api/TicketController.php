<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\FieldVisit\FieldVisitTicketRepository;
use App\Http\Repositories\LeaveRepository;
use App\Http\Repositories\Setting\AppSettingRepository;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Http\Repositories\TicketApiRepository;
use App\Http\Repositories\TicketRepository;
use App\Http\Repositories\TimeRequestRepository;
use App\Http\Resources\TicketResource;
use App\Http\Resources\TicketResourceCollection;
use App\Http\Resources\VerifierResource;
use App\Models\Leaves\LeaveRequest;
use App\Models\TimeRequest;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\Rule;
use Tymon\JWTAuth\Facades\JWTAuth;

class TicketController extends Controller
{
    private LeaveRepository $leaveRepo;
    private TimeRequestRepository $timeRequestRepo;
    private TicketRepository $ticketRepo;

    private FieldVisitTicketRepository $fieldVisitTicketRepo;

    public function __construct(
        protected AppSettingRepositoryInterface $appSettingRepo
    ) {
        $this->leaveRepo = new LeaveRepository();
        $this->timeRequestRepo = new TimeRequestRepository();
        $this->ticketRepo = new TicketRepository();
        $this->fieldVisitTicketRepo = new FieldVisitTicketRepository();
    }

    private function acceptedWorkFlows()
    {
        return $this->appSettingRepo->getEnabled();
    }

    /**
     * @lrd:start
     * ## Get Workflow List
     * Fetches a list of available ticket workflows that the authenticated user can interact with.
     * This is useful for populating filter dropdowns for ticket lists (e.g., "Leave Approval", "Time Request").
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function workflowList(Request $request)
    {
        $accepted = array_merge(['all'], $this->acceptedWorkflows());
        $workflows = [];

        foreach ($this->ticketRepo->ticketWorkflows($request->user('api')) as $workflowKey => $workflowTitle) {
            if (in_array($workflowKey, $accepted)) {
                $workflows[] = [
                    'title' => ucwords($workflowTitle),
                    'value' => $workflowKey
                ];
            }
        }

        return ApiResponse::getData(['workflows' => $workflows]);
    }

    /**
     * @lrd:start
     * ## Get Ticket List
     * Fetches a paginated list of tickets based on the specified type and filters.
     * ## ticketType: The type of tickets to retrieve. Type can be: `myRequests`, `myRequestsHistory`, `reviewedRequests`, `toBeReviewedRequests`
     * ## workflow: Filter tickets by a specific workflow (e.g., `leave_approval`). Defaults to `all`.
     * ## page: The page number for pagination.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam ticketType string|required|in:myRequests,myRequestsHistory,reviewedRequests,toBeReviewedRequests
     * @LRDparam workflow string|nullable
     */
    public function ticketList(Request $request)
    {
        $accepted = $this->acceptedWorkFlows();
        $request->validate([
            'ticketType' => 'required|in:myRequests,myRequestsHistory,reviewedRequests,toBeReviewedRequests',
            'workflow' => ['nullable', Rule::in(array_merge(['all'], $accepted))],
            'page' => 'nullable|integer',
        ]);

        $perPage = 10;

        if (empty($accepted))
            return ApiResponse::getData(new TicketResourceCollection(new LengthAwarePaginator([], 0, $perPage)));


        $filters = [
            'workflow' => $request->workflow,
            'page'     => $request->page,
            'perPage'  => $perPage,
        ];
        $list = $this->ticketRepo->getTickets(
            $request->ticketType,
            $filters,
            $accepted,
        );

        return ApiResponse::getData(new TicketResourceCollection($list));
    }

    /**
     * @lrd:start
     * ## Get Ticket Details
     * Fetches the detailed information for a single ticket, identified by its ID.
     * This includes the ticket's history, current state, and associated data (like leave or time request details).
     * The user must be authenticated via JWT and have permission to view the ticket.
     * @lrd:end
     */
    public function showDetail(Request $request, int $ticketId)
    {
        $accepted = $this->acceptedWorkFlows();
        if (empty($accepted)) {
            $perPage = 10;
            return ApiResponse::getData(new TicketResourceCollection(new LengthAwarePaginator([], 0, $perPage)));
        }

        $ticketResponse = $this->ticketRepo->getTicket(ticketId: $ticketId);
        if (!$ticketResponse['status'])
            return ApiResponse::errorMessage($ticketResponse['message']);

        $ticket = $ticketResponse['data'];
        if (!in_array($ticket->workflow, $accepted)) {
            return ApiResponse::errorMessage("The workflow associated with this ticket is not currently enabled.");
        }

        return ApiResponse::getData(new TicketResource($ticket, true));
    }

    /**
     * Summary of verifiers [this function was thought for the use case of all verifiers, but dropped for now since some permission issues maybe, need to think again]
     * @param \Illuminate\Http\Request $request
     * @param string $ticketType
     * @return mixed|\Illuminate\Http\JsonResponse
     * @lrd:start
     * ## Get Verifiers by Ticket Type
     * Fetches a list of employees who can act as verifiers for a specific type of request (e.g., leave or time request).
     * This is used to populate a verifier selection dropdown when creating a new request.
     * ## ticketType: The type of ticket for which to fetch verifiers. Type can be: `leave-request`, `time-request`.
     * The user must be authenticated via JWT.
     * @lrd:end
     * @LRDparam ticketType string|required|in:time-request,leave-request
     */
    public function verifiers(Request $request, string $ticketType)
    {
        $validTicketTypes = ['time-request', 'leave-request', 'field-visit'];
        if (!in_array($ticketType, $validTicketTypes)) {
            return ApiResponse::errorMessage("No verifiers found for $ticketType");
        }
        $params = [
            "employee_id" => JWTAuth::parseToken()->getPayload()->get("employee_id"),
            "is_submitting" => true,
        ];
        $data = match ($ticketType) {
            'leave-request' => $this->leaveRepo->verifiers($params),
            'time-request' => $this->timeRequestRepo->verifiers($params),
            'field-visit' => $this->fieldVisitTicketRepo->verifiers($params),
        };
        return ApiResponse::getData(['verifiers' => VerifierResource::collection($data)]);
    }

    /**
     * @lrd:start
     * ## Change Ticket State
     * Performs an action (e.g., approve, reject, verify) on a ticket, changing its state.
     * This is the primary endpoint for interacting with the workflow of a ticket.
     * ## ticket_id: The ID of the ticket to update.
     * ## state: The action to perform on the ticket. State can be: `approve`, `cancel`, `reject`, `revert`, `verify`, `change_owner`, or `assign`.
     * ## comment: A comment or reason for the state change. Required for most actions.
     * ## next_owner_id: The ID of the next employee in the workflow. Required for `verify` and `change_owner` actions.
     * The user must be authenticated via JWT and have permission to perform the action on the ticket.
     * @lrd:end
     *
     * @LRDparam ticket_id integer|required
     * @LRDparam state string|required|in:approve,cancel,reject,revert,verify,change_owner,assign
     * @LRDparam comment string|nullable
     * @LRDparam next_owner_id integer|nullable
     */
    public function changeState(Request $request)
    {
        $request->validate([
            'ticket_id'     => 'required|exists:request_tickets,id',
            'state'         => 'required|in:approve,cancel,reject,revert,verify,change_owner,assign',
            'comment'       => 'required_unless:state,cancel,change_owner',
            'next_owner_id' => 'required_if:state,verify,change_owner'
        ]);

        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");

        $ticket = $this->ticketRepo->getTicketInfo($request->ticket_id, $this->acceptedWorkflows());
        if (!$ticket) return ApiResponse::errorMessage("Ticket Not Found");

        $response = [];
        if ($request->state == "change_owner")
            $response = $this->ticketRepo->changeTicketOwner($request->ticket_id, $request->next_owner_id);
        else
            $response = $this->ticketRepo->changeState($request->ticket_id, $request->state, $request->comment ?? "", $request->next_owner_id ?? $employeeId);

        if ($response['status'])
            return ApiResponse::successMessage("Ticket " . $request->state . " sucecessfully.", "Ticket " . $request->state . " sucecessfully.");

        return ApiResponse::errorMessage($response['message'], $response['message']);
    }

    /**
     * @lrd:start
     * ## Remove Ticket Document
     * Removes a document that was previously uploaded and attached to a ticket.
     * This is typically used when a user is editing a leave or time request before it has been approved.
     * ## ticket_id: The ID of the ticket from which to remove the document.
     * ## document_id: The ID of the document to remove.
     * The user must be authenticated via JWT and have permission to edit the ticket.
     * @lrd:end
     *
     * @LRDparam ticket_id integer|required
     * @LRDparam document_id integer|required
     */
    public function removeDocument(Request $request)
    {
        $request->validate([
            'ticket_id'     => 'required',
            'document_id'   => 'required|exists:ticket_documents,id',
        ]);

        $ticket = $this->ticketRepo->getTicketInfo($request->ticket_id, [WorkflowName::LEAVE_APPROVAL, WorkflowName::TIME_REQUEST_APPROVAL]);
        if (!$ticket) return ApiResponse::errorMessage("Ticket Not Found");

        $removeDocument = $this->ticketRepo->removeDocument($request->ticket_id, [$request->document_id]);

        if ($removeDocument)
            return ApiResponse::successMessage("Document removed successfully.", "Document removed successfully. Ticket Id: $request->ticket_id. Document Id: $request->document_id");

        return ApiResponse::errorMessage("Error removing document.", "Error removing document. Ticket Id: $request->ticket_id. Document Id: $request->document_id");
    }
}
