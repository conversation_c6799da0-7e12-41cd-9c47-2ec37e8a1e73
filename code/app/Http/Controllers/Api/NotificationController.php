<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Configs\NotificationRepository;
use App\Http\Services\FirebaseNotificationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;

class NotificationController extends Controller
{

    public function __construct(
        private NotificationRepository $notificationRepo
    ) {}

    /**
     * @lrd:start
     * ## Get Notification List
     * Fetches a list of notifications for the authenticated user.
     * The list is separated into `read_notifications` and `unread_notifications`.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function notificationList()
    {
        $params = [
            "user_id" => JWTAuth::parseToken()->getPayload()->get("user_id"),
        ];

        $data = $this->notificationRepo->notificationList($params);
        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    /**
     * @lrd:start
     * ## Mark Notification as Read
     * Marks a single notification as read for the authenticated user.
     * ## id: The ID of the notification to mark as read.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam id integer|required
     */
    public function markAsRead(Request $request)
    {
        Log::info("Notification Mark as Read. Request =>" . print_r($request->all(), true));
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:notifications,id',
        ]);

        if ($validator->fails()) {
            Log::info("Notification Mark as Read. Request =>" . print_r($request->all(), true) . ". Validation Failed => " . print_r($validator->errors(), true));
            $toReturn = ApiResponseHelper::validationError($validator->errors());
            return response()->json($toReturn, $toReturn['statusCode']);
        }

        $params = [
            "user_id"     => $request->user?->id,
            "id"          => $request->id,
        ];
        $data = $this->notificationRepo->markAsRead($params);
        if ($data) {
            $toReturn = ApiResponseHelper::successMessage("Notification marked as read.");
        } else {
            $toReturn = ApiResponseHelper::errorMessage("Error marking notification as read.");
        }
        Log::info("Notification Mark as Read. Request =>" . print_r($params, true) . ". Response => " . print_r($toReturn, true));
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    /**
     * @lrd:start
     * ## Mark All Notifications as Read
     * Marks all unread notifications as read for the authenticated user.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function markAllAsRead()
    {
        $params = [
            "user_id" => auth()->user()?->id,
        ];
        Log::info("Notification Mark all as Read. Request =>" . print_r($params, true));

        $data = $this->notificationRepo->markAllAsRead($params);
        if ($data) {
            $toReturn = ApiResponseHelper::successMessage("All Notifications marked as read.");
        } else {
            $toReturn = ApiResponseHelper::errorMessage("Error marking notifications as read.");
        }
        Log::info("Notification Mark all as Read. Request =>" . print_r($params, true) . ". Response => " . print_r($toReturn, true));
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    /**
     * @lrd:start
     * ## Test Firebase Notification (Admin Only)
     * An endpoint for super admins to test sending Firebase Cloud Messaging (FCM) notifications.
     * This can send a notification to a specific user (via FCM token) or to a topic.
     * ## type: The type of notification to send. Example: `fcm`, `topic`.
     * ## detailType: The type of detail for an FCM notification. Required if `type` is `fcm`. Example: `apply-leave-request`.
     * ## username: The username of the user to send the notification to. Required if `type` is `fcm`.
     * ## topic: The topic to send the notification to. Required if `type` is `topic`.
     * **Note:** This is an internal testing endpoint and requires super admin privileges. It is accessible via a web route, not the standard API prefix.
     * @lrd:end
     *
     * @LRDparam type string|required
     * @LRDparam detailType string|optional
     * @LRDparam username string|optional
     * @LRDparam topic string|optional
     */
    public function testNotification(Request $request, FirebaseNotificationService $service)
    {
        if (!auth()->user()?->hasRole(Constant::ROLE_SUPER_ADMIN)) {
            abort('403', 'Unauthorized, Only super admin can perform this action');
        }

        $validator = Validator::make($request->all(), [
            'type' => 'required|in:fcm,topic',
            'detailType' => 'required_if:type,fcm|in:apply-leave-request,apply-time-request,leave-detail,time-request-detail',
            'username' => 'required_if:type,fcm',
            'topic' => 'required_if:type,topic',
        ]);

        if ($validator->fails()) {
            $toReturn = ApiResponseHelper::validationError($validator->errors());
            return response()->json($toReturn, $toReturn['statusCode']);
        }


        if ($request->type == 'fcm') {
            $user = \App\Models\User::where('username', $request->username)->first();
            if (!$user) {
                return response()->json([
                    "success" => false,
                    "message" => "User not found."
                ]);
            }
            $employee = \App\Models\Employee\Employee::where('user_id', $user->id)->first();
            if (!$employee) {
                return response()->json([
                    "success" => false,
                    "message" => "Employee not found."
                ]);
            }

            $meta = [];
            $yesterdayDate = \Carbon\Carbon::yesterday()->format('Y-m-d');
            switch ($request->detailType) {
                case 'apply-leave-request':
                    $meta = [
                        'type' => "apply-leave-request",
                        'date' => $yesterdayDate
                    ];
                    break;
                case 'apply-time-request':
                    $meta = [
                        'type' => "apply-time-request",
                        'date' => $yesterdayDate
                    ];
                    break;
                case 'leave-detail':
                    $ticket = \App\Models\RequestTicket::where('employee_id', $employee->id)
                        ->where('workflow', WorkflowName::LEAVE_APPROVAL)
                        ->orderBy('created_at', 'desc')
                        ->first();
                    $meta = [
                        'type'      => "detail",
                        'workflow'  => $ticket->workflow,
                        'ticket_id' => $ticket->id,
                        'webLink' => route('ticketPage', ['requestId' => $ticket->model_id, 'workflow' => $ticket->workflow])
                    ];
                    break;
                case 'time-request-detail':
                    $ticket = \App\Models\RequestTicket::where('employee_id', $employee->id)
                        ->where('workflow', WorkflowName::TIME_REQUEST_APPROVAL)
                        ->orderBy('created_at', 'desc')
                        ->first();
                    $meta = [
                        'type'      => "detail",
                        'workflow'  => $ticket->workflow,
                        'ticket_id' => $ticket->id,
                        'webLink' => route('ticketPage', ['requestId' => $ticket->model_id, 'workflow' => $ticket->workflow])
                    ];
                    break;
            }
            dispatch(new \App\Jobs\FirebaseFcmNotificationJob(
                $user->id,
                "FCM: $request->detailType",
                "This is a test notification sent for $request->detailType",
                $meta
            ));
            return response()->json([
                "success" => true,
                "message" => "Notification sent successfully.",
                "sent_meta" => $meta
            ]);
        }
        $meta = [];
        $topic = $request->topic;
        $response = $service->sendToTopic(
            $request->topic,
            "Topic: $request->topic",
            "This is a test notification sent to $topic",
            $meta
        );
        return response()->json([
            "success" => true,
            "message" => "Notification sent successfully. " . $topic,
            "sent_meta" => $meta,
            'response' => $response,
        ]);
    }
}
