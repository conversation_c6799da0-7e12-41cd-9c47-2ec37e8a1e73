<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\ProfileRepository;
use App\Models\Employee\Employee;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class ProfileController extends Controller
{
    private ProfileRepository $profileRepo;

    public function __construct()
    {
        $this->profileRepo = new ProfileRepository();
    }

    /**
     * @lrd:start
     * ## Get Profile Details
     * Fetches the complete profile information for the authenticated user.
     * This includes general, personal, family, emergency contact, experience, education, documents, training, financial, and biometric information.
     * It also lists the user's approvers for leave and time requests.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function profileDetails()
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");

        $employeeDetails = $this->profileRepo->profileInfo($employeeId);

        $generalInformation = [
            "profile_img" => $employeeDetails?->profile_picture ? asset('storage/' . $employeeDetails?->profile_picture) : asset('build/img/team/' . ($employeeDetails?->gender ?? 'other') . '.png'),
            "name"  => $employeeDetails->first_name . " " . $employeeDetails->middle_name . " " . $employeeDetails->last_name,
            "email" => $employeeDetails->email  ?? "N/A",
            "company_email" => $employeeDetails->organizationInfo->email  ?? "N/A",
            "username"  => $employeeDetails->user->username ?? "N/A",
            "employee_code" => $employeeDetails->organizationInfo?->company_employee_code ?? "N/A",
            "pan" => $employeeDetails->organizationInfo?->pan_no ?? "N/A",
            "designation" => $employeeDetails->getJobDesignation() ?? "N/A",
            "unit" => $employeeDetails->organizationInfo?->unit?->name ?? "N/A",
            "department" => $employeeDetails->organizationInfo?->department?->name ?? "N/A",
            "sub_branch" => $employeeDetails->organizationInfo?->subBranch?->name ?? "N/A",
            "branch" => $employeeDetails->organizationInfo?->branch?->name ?? "N/A",
            "company" => $employeeDetails->company->name ?? "N/A",
            "phone" => $employeeDetails->phone  ?? "N/A",
            "birthday" => $employeeDetails->dob  ?? "N/A",
            "address" => $employeeDetails->permanent_address  ?? "N/A",
            "gender" => $employeeDetails->gender  ?? "N/A",
            "working_hours" => $employeeDetails->organizationInfo?->shift?->formatted_start_time . " to " . $employeeDetails->organizationInfo?->shift?->formatted_end_time,
            "reports_to" => $employeeDetails->organizationInfo?->supervisor?->name  ?? "N/A",
        ];

        $personalInformation = [
            "phone" => $employeeDetails->phone ?? "N/A",
            "email" => $employeeDetails->email ?? "N/A",
            "marital_status" => ucfirst($employeeDetails->mstat) ?? "N/A",
            "nationality" => $employeeDetails->nationality ?? "N/A",
            "citizenship_no" => $employeeDetails->citizenship ?? "N/A",
            "temporary_address" => $employeeDetails->temporary_address ?? "N/A",
            "permanent_address" => $employeeDetails->permanent_address ?? "N/A",
        ];

        $familyInformation = [
            "spouse" => $employeeDetails->spouse ?? "N/A",
            "father" => $employeeDetails->father ?? "N/A",
            "mother" => $employeeDetails->mother ?? "N/A",
            "grandfather" => $employeeDetails->grandfather ?? "N/A",
        ];

        $emergencyContactInformation = [
            "name" => $employeeDetails->contact_person ?? "N/A",
            "phone" => $employeeDetails->contact_phone ?? "N/A",
            "relation" => $employeeDetails->contact_relation ?? "N/A",
        ];

        $experience = $this->profileRepo->experienceList($employeeId);

        $experiences = [];
        foreach ($experience as $exp) {
            $experiences[] = [
                "name" => ($exp->position ? $exp->position . ' at ' . $exp->name : ''),
                "time" => $this->getFormattedDateForExperience($exp->period_from) . "-" . $this->getFormattedDateForExperience($exp->period_to)
            ];
        }

        $educationList = $this->profileRepo->educationList($employeeId);

        $educations = [];
        foreach ($educationList as $edu) {
            $educations[] = [
                "name" => $edu->level_name . '-' . ($edu->institute_name ?? 'N/A') . '[' . $edu->course . ']',
                "time" => $this->getYearFromDate($edu->joined_date) . '-' . $this->getYearFromDate($edu->completed_date)
            ];
        }

        $documents = $this->profileRepo->documentList($employeeId)->get()->map(fn($document) => [
            'name' => $document->type,
            'path' => asset('storage/' . $document->name)
        ]);

        $trainings = [];
        $trainingList = $this->profileRepo->trainingList($employeeId);
        foreach ($trainingList as $training) {
            $trainings[] = [
                "name" => $training->name ?? "N/A",
                "course" => $training->course ?? "N/A",
                "time" => $training->period_from . '-' . $training->period_to
            ];
        }

        $bankInformation = [
            "bank"                   => $employeeDetails->organizationInfo?->bank ?? 'N/A',
            "account_number"         => $employeeDetails->organizationInfo?->bank_account_no ?? 'N/A',
            "outsource_company_name" => $employeeDetails->organizationInfo?->outsource_company?->name ?? 'N/A',
        ];

        $retirementFund = [
            "retirement_fund_name" => $employeeDetails->company?->rf_scheme,
            "retirement_fund_nuumber" => ($employeeDetails->company?->rf_scheme) ? $employeeDetails->organizationInfo?->rf_no : "N/A",
            "cit_number" => ($employeeDetails->company?->cit_enabled) ? $employeeDetails->organizationInfo?->cit_no : "N/A",
        ];


        $biometricList = $biometricDeviceList = [];
        if (isset($employeeDetails->organizationInfo?->biometric_id) && $employeeDetails->organizationInfo?->biometric_id != null) {
            $biometricDeviceList = $this->profileRepo->biometricList($employeeDetails->organizationInfo->biometric_id);
            /*foreach ($biometricDeviceList as $biometricDevice) {
                $biometricList[] = [
                    "branch" => $biometricDevice->attDevice?->branch?->name ?? "N/A",
                    "device" => $biometricDevice->attDevice?->name ?? 'N/A',
                    "updated_at" => date('Y-m-d', strtotime($biometricDevice->updated_at)),
                ];
            }*/
        }

        $biometricInformation = [
            "biometric_number" => $employeeDetails->organizationInfo?->biometric_id ?? 'N/A',
            //            "biometric_list"   => $biometricDeviceList
        ];

        $employee = Employee::findOrFail($employeeId);
        $desiredWorkflow = ['leave_approval', 'time_request_approval'];
        $approver = $this->snakeCaseArrayKeys($this->profileRepo->performerList($employee, $desiredWorkflow));
        $data = [
            "general_information" => $generalInformation,
            "profile" => [
                "personal_information" => $personalInformation,
                "family_information" => $familyInformation,
                "emergency_contact_information" => $emergencyContactInformation,
                "experience" => $experiences,
                "education_information" => $educations,
            ],
            "documents" => $documents,
            "training" => $trainings,
            "misc" => [
                "bank_information" => $bankInformation,
                "retirement_fund" => $retirementFund,
                "biometric_information" => $biometricInformation,
            ],
            "salary_slip" => [],
            "payslip" => [],
            "approver" => $approver ? $approver : (object)[],

        ];

        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    private function getFormattedDateForExperience($date)
    {
        if ($date) {
            $carbonDate = \Illuminate\Support\Carbon::parse($date)->format('M Y');
            return $carbonDate;
        }
        return 'N/A';
    }

    private function getYearFromDate($date)
    {
        if ($date) {
            $carbonDate = \Illuminate\Support\Carbon::parse($date);
            return $carbonDate->year;
        }
        return 'N/A';
    }

    function snakeCaseArrayKeys(array $array): array
    {
        $formatted = [];

        foreach ($array as $key => $value) {
            $newKey = strtolower(str_replace(' ', '_', $key));

            if (is_array($value)) {
                $formatted[$newKey] = $this->snakeCaseArrayKeys($value);
            } else {
                $formatted[$newKey] = $value;
            }
        }

        return $formatted;
    }
}
