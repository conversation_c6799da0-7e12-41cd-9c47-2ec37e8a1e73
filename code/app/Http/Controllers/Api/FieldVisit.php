<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\FieldVisit\FieldVisitRepository;
use App\Http\Repositories\TicketRepository;
use Exception;
use Illuminate\Http\Request;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;

class FieldVisit extends Controller
{
    private FieldVisitRepository $fieldVisitRepo;
    private TicketRepository $ticketRepository;

    public function __construct()
    {
        $this->fieldVisitRepo = new FieldVisitRepository;
        $this->ticketRepository = new TicketRepository;
    }

    /**
     * @lrd:start
     * ## Field Visit Check In
     * Allows an employee to check in at a specific location. This endpoint validates the input and creates a checkin record in database.
     * On success, it returns the datails of created check in.
     * 
     * ## name : the name of the customer or site
     * ## location : the name of the location
     * ## purpose : purpose of visit
     * ## message : message given after the field visit
     * ## type : visit place type i.e site or customer
     * ## check_in_lat : check in latitude
     * ## check_in_lon : check in longitude
     * ## check_in_date_en : date of check in
     * ## check_in_time : time of check in
     * ## check_in_ip : ip from the device of loged in
     * @lrd:end
     * @LRDparam name required|string
     * @LRDparam location required|string
     * @LRDparam purpose required|string
     * @LRDparam message nullable|string
     * @LRDparam type required|enum
     * @LRDparam check_in_lat required|float
     * @LRDparam check_in_lon required|float
     * @LRDparam check_in_date_en nullable
     * @LRDparam check_in_date_np nullable
     * @LRDparam check_in_time nullable
     * @LRDparam check_in_ip nullable
     */
    public function checkIn(Request $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayLoad()->get('employee_id');
        $validated = $request->validate([
            'name' => 'required|string',
            'location' => 'required|string',
            'purpose' => 'required|string',
            'message' => 'nullable|string',
            'type' => 'required',
            'check_in_lat' => 'required|numeric',
            'check_in_lon' => 'required|numeric',
        ]);

        try {
            $data = $this->fieldVisitRepo->checkIn($employeeId, $validated);
            return ApiResponseHelper::getData($data, "Check In Successful");
        } catch (Exception $e) {
            return ApiResponseHelper::errorMessage($e->getMessage());
        }
    }

    /** 
     * @lrd:start
     * ## Field Visit Check Out
     * Allows Employee to check out in specific location. This end point validates input and update the checkout record in database.
     * on success it returns details of updated check out.
     * 
     * ## check_out_lat : check out latitude
     * ## check_out_lon : check out longitude
     * ## description : description of the work
     * ## images : images taken live on work space
     * ## alt : alternative text message in absence of image
     * ## verifier_id : ticket verifier id
     * ## check_out_time : check out time
     * ## check_out_date_np : nepali checkout date
     * ## check_out_date_eng : english check out date
     * ## check_out_ip : ip address from device from which checkout done
     * @lrd:end
     * @LRDparam check_out_lat required|float
     * @LRDparam check_out_lon required|float
     * @LRDparam description nullable|string
     * @LRDparam images nullable|array
     * @LRDparam alt nullable|string
     * @LRDparam verifier_id required|integer
     * @LRDparam check_out_time nullable
     * @LRDparam check_out_date_np nullable
     * @LRDparam check_out_date_en nullable
     * @LRDparam check_out_ip nullable
     */

    public function checkOut(Request $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayLoad()->get('employee_id');

        $validated = $request->validate([
            'check_out_lat' => 'required|numeric',
            'check_out_lon' => 'required|numeric',
            'description' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'nullable|image|mimes:jpg,png,jpeg|max:2048',
            'alt' => 'nullable|string',
            'verifier_id' => 'required',
            'check_out_time' => 'nullable',
            'check_out_date_np' => 'nullable',
            'check_out_date_en' => 'nullable',
            'check_out_ip' => 'nullable'
        ]);
        try {
            $data = $this->fieldVisitRepo->checkOut($employeeId, $validated);

            return ApiResponseHelper::getData($data, "Check Out Successfully");
        } catch (Exception $e) {
            return ApiResponseHelper::errorMessage($e->getMessage());
        }
    }

    /**
     * @lrd:start
     * Field Visit Status Check
     * Allows User to check the status check in and check out.
     * @lrd:end
     */
    public function checkStatus(Request $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayLoad()->get('employee_id');

        $activeVisitData = $this->fieldVisitRepo->getActiveVisit($employeeId);

        if ($activeVisitData)
            return ApiResponseHelper::getData([
                'has_active_visit' => !is_null($activeVisitData),
                'active_visit_data' => $activeVisitData
            ], "Status Checked Successfully");

        return response()->json([
            'has_active_visit' => !is_null($activeVisitData),
            'active_visit_data' => []
        ]);
    }
}
