<?php

namespace App\Http\Controllers\Api;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\LeaveRepository;
use App\Http\Repositories\LeaveRequestRepository;
use App\Http\Repositories\TicketRepository;
use App\Http\Requests\LeaveRequest;
use App\Http\Resources\VerifierResource;
use Exception;
use Illuminate\Http\Request;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class LeaveRequestController extends Controller
{
    private LeaveRepository $leaveRepo;
    private LeaveRequestRepository $leaveRequestRepo;
    private TicketRepository $ticketRepo;

    public function __construct()
    {
        $this->leaveRepo = new LeaveRepository();
        $this->leaveRequestRepo = new LeaveRequestRepository();
        $this->ticketRepo = new TicketRepository();
    }

    /**
     * @lrd:start
     * ## Apply for Leave
     * Submits a new leave request for the authenticated user.
     * ## leave_type_id: The ID of the leave type.
     * ## leave_option_id: The ID of the leave option (e.g., full day, half day).
     * ## nep_start_date: The start date of the leave in Nepali date format (YYYY-MM-DD).
     * ## nep_end_date: The end date of the leave in Nepali date format (YYYY-MM-DD).
     * ## verifier_id: The ID of the employee who will verify the request.
     * ## remarks: The reason for the leave.
     * ## documents: An array of document files to upload.
     * ## The user must be authenticated via JWT.
     * ## For FedEx HRM, `replaced_start_date` and `replaced_end_date` may be required for replacement leaves.
     * @lrd:end
     *
     * @LRDparam leave_type_id integer|required
     * @LRDparam leave_option_id integer|required
     * @LRDparam nep_start_date string|required
     * @LRDparam nep_end_date string|required
     * @LRDparam verifier_id integer|required
     * @LRDparam remarks string|required
     * @LRDparam documents file[]|nullable
     */
    public function apply(LeaveRequest $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");

        try {
            $response = $this->leaveRequestRepo->createOrUpdateLeaveRequest([
                ...$request->all(),
                "employee_id"       => $employeeId,
                "fiscal_year_id"    => currentFiscalYearId(),
            ]);

            if (!$response['status']) {
                return ApiResponse::errorMessage($response['message']);
            }

            return ApiResponse::successMessage($response['message']);
        } catch (Exception $exception) {
            logError("Error while creating leave request", ['error' => $exception->getMessage()]);
            return ApiResponse::errorMessage($exception->getMessage());
        }
    }

    /**
     * @lrd:start
     * ## Get Leave Request Details
     * Fetches the details of a specific leave request ticket, identified by its ticket ID.
     * This is used for viewing or editing an existing leave request.
     * The user must be authenticated via JWT and have permission to view the ticket.
     * @lrd:end
     */
    public function getLeaveRequest(Request $request, int $ticketId)
    {
        $ticket = $this->ticketRepo->getTicketInfo($ticketId, WorkflowName::LEAVE_APPROVAL);
        if (!$ticket) return ApiResponse::errorMessage("Leave Request Ticket Not Found");

        if (!$ticket->model->canEdit()) {
            return ApiResponse::errorMessage("You cannot view this leave request");
        }

        $leaveDetail = $this->leaveRequestRepo->getLeaveDetail($ticket->model_id);
        if (!$leaveDetail) return ApiResponse::errorMessage("Leave Request Not Found");
        $detail = [
            'leave_id'          => $leaveDetail->id,
            'leave_type_id'     => $leaveDetail->leave_type_id,
            'nep_start_date'    => $leaveDetail->nep_start_date,
            'nep_end_date'      => $leaveDetail->nep_end_date,
            'leave_option_id'   => $leaveDetail->leave_option_id,
            'remarks'           => $leaveDetail->remarks,
            'verifier_id'       => $leaveDetail->requestTicket->current_owner_id,
            'documents'         => $leaveDetail->requestTicket->getDocuments()
        ];
        if (fedexHrm()) {
            $detail = array_merge($detail, [
                'replaced_start_date'   => $leaveDetail->replaced_start_date ? LaravelNepaliDate::from($leaveDetail->replaced_start_date)->toNepaliDate() : "",
                'replaced_end_date'     => $leaveDetail->replaced_end_date ? LaravelNepaliDate::from($leaveDetail->replaced_end_date)->toNepaliDate() : "",
            ]);
        }
        return ApiResponse::getData($detail);
    }

    /**
     * @lrd:start
     * ## Update Leave Request
     * Updates an existing leave request ticket.
     * ## leave_type_id: The ID of the leave type.
     * ## leave_option_id: The ID of the leave option (e.g., full day, half day).
     * ## nep_start_date: The start date of the leave in Nepali date format (YYYY-MM-DD).
     * ## nep_end_date: The end date of the leave in Nepali date format (YYYY-MM-DD).
     * ## verifier_id: The ID of the employee who will verify the request.
     * ## remarks: The reason for the leave.
     * ## documents: An array of new document files to upload.
     * ## removing_document_ids: An array of existing document IDs to be removed.
     * ## replaced_start_date: (FedEx Only) The start date of the on-duty period being replaced.
     * ## replaced_end_date: (FedEx Only) The end date of the on-duty period being replaced.
     * ## The user must be authenticated via JWT and have permission to edit the ticket.
     * @lrd:end
     * 
     * @LRDparam leave_type_id required|integer
     * @LRDparam leave_option_id required|integer
     * @LRDparam nep_start_date required|string
     * @LRDparam nep_end_date required|string
     * @LRDparam verifier_id required|integer
     * @LRDparam remarks required|string
     * @LRDparam documents file[]|nullable
     * @LRDparam removing_document_ids integer[]|nullable
     * @LRDparam replaced_start_date string|nullable
     * @LRDparam replaced_end_date string|nullable
     */
    public function updateLeaveRequest(LeaveRequest $request, int $ticketId)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        $ticket = $this->ticketRepo->getTicketInfo($ticketId, WorkflowName::LEAVE_APPROVAL);
        if (!$ticket) {
            return ApiResponse::errorMessage("Leave Request Ticket not found");
        }

        if (!$ticket->model->canEdit()) {
            return ApiResponse::errorMessage("You cannot update this leave request");
        }

        $response = $this->leaveRequestRepo->createOrUpdateLeaveRequest([
            ...$request->all(),
            "employee_id"       => $employeeId,
            "fiscal_year_id"    => currentFiscalYearId(),
        ], $ticket->model_id);

        if (!$response['status']) {
            return ApiResponse::errorMessage($response['message']);
        }

        return ApiResponse::successMessage($response['message']);
    }

    /**
     * @lrd:start
     * ## Get Leave Details for Applying
     * Fetches the necessary data for the "Apply for Leave" screen.
     * This includes a list of leave types available to the user, their remaining balances, and the available leave options (e.g., full day, half day).
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function leaveDetailsForApply(Request $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        try {
            $leaveTypes = $this->leaveRepo->assignedLeaves([
                "employee_id" => $employeeId,
                "fiscal_year_id" => currentFiscalYearId(),
            ])->map(function ($leave) use ($employeeId) {
                $leaveOptions = null;
                if ($leave->name == Constant::REPLACEMENT_LEAVE_NAME) {
                    $leaveOptions = collect($this->leaveRepo->leaveOptions($employeeId, $leave->name))
                        ->map(fn($name, $id) => ["id" => $id, "name" => $name])
                        ->values();
                }

                $leaveCountResponse = $this->leaveRepo->getRemainingLeaveCount(
                    $employeeId,
                    $leave->id,
                    currentFiscalYearId()
                );

                if (!$leaveCountResponse['status'])
                    throw new Exception($leaveCountResponse['message']);

                $leaveCountMessage = $leaveCountResponse['message'];

                $data =  [
                    "id"                    => $leave->id,
                    "name"                  => $leave->name,
                    "leave_options"         => $leaveOptions,
                    "leave_count_message"   => $leaveCountMessage
                ];
                if (!$leaveOptions) unset($data['leave_options']);

                return $data;
            });
            $defaultLeaveOptions  = collect($this->leaveRepo->leaveOptions($employeeId))
                ->map(fn($name, $id) => ["id" => $id, "name" => $name])
                ->values();

            // $verifiers = $this->leaveRepo->verifiers([
            //     "employee_id" => $employeeId,
            //     "is_submitting" => true,
            // ]);

            return ApiResponse::getData([
                "default_leave_options" => $defaultLeaveOptions,
                // "verifiers"             => $verifiers,
                "leave_types"           => $leaveTypes,
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
    }
}
