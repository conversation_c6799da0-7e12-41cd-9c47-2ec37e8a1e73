<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\Auth\Interfaces\ILoginAppRepository;
use App\Http\Repositories\Setting\AppSettingRepository;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Token;

class LoginController extends Controller
{
    public function __construct(
        private ILoginAppRepository $loginRepo,
        private IDeviceLogRepository $deviceLogRepo,
    ) {}

    /**
     * @lrd:start
     * ## Check User
     * Validates a user's credentials and device information. This is the first step in the login process.
     * It checks if the user exists and if the device is allowed to log in.
     * On success, it may return information about whether 2FA (like OTP) is required.
     * ## username: The user's login username.
     * ## app_login_code: A unique code identifying the application instance.
     * ## fcm_token: The Firebase Cloud Messaging token for push notifications.
     * @lrd:end
     * @LRDparam username string|required
     * @LRDparam app_login_code string|required
     * @LRDparam fcm_token string|required
     */
    public function checkUser(Request $request)
    {
        logInfo("Check User Initiated, username: {$request->username}");
        $request->validate([
            'username' => 'required',
            'app_login_code' => 'required',
            'fcm_token' => 'required'
        ]);
        $params = [
            ...$request->all(),
            ...$this->getDeviceDetail($request->header())
        ];

        logInfo("Initiating Check User", $params);
        try {
            $checkUser = $this->loginRepo->checkUser($params);
            return ApiResponse::getData($checkUser, 'Valid User.');
        } catch (Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
    }

    /**
     * @lrd:start
     * ## Verify OTP
     * Verifies the One-Time Password (OTP) sent to the user.
     * If the OTP is correct, it completes the login process and returns an access token and a refresh token.
     * ## username: The user's login username.
     * ## app_login_code: A unique code identifying the application instance.
     * ## otp: The One-Time Password received by the user.
     * ## fcm_token: The Firebase Cloud Messaging token for push notifications.
     * @lrd:end
     * @LRDparam username string|required
     * @LRDparam app_login_code string|required
     * @LRDparam otp string|required
     * @LRDparam fcm_token string|required
     */
    public function verifyOTP(Request $request)
    {
        logInfo("Verify OTP Initiated", ["username" => $request->username, "otp" => $request->otp]);

        if (!app(AppSettingRepositoryInterface::class)->isFeatureEnabled('otp')) {
            return ApiResponse::errorMessage("OTP feature is disabled.");
        }

        $request->validate([
            'username'       => 'required',
            'app_login_code' => 'required',
            'otp'            => 'required',
            'fcm_token'      => 'required',
        ]);

        $verifyOtp = $this->loginRepo->verifyOTP([
            ...$request->all(),
            ...$this->getDeviceDetail($request->header())
        ]);

        $logMessage = "Verify OTP. username: $request->username, otp: $request->otp";
        if (count($verifyOtp)) {
            return ApiResponse::getData($verifyOtp, 'OTP is verified', $logMessage);
        }

        return ApiResponse::unauthorized("Unable to login.", $logMessage);
    }

    /**
     * @lrd:start
     * ## Refresh Token
     * Refreshes an expired access token using a valid refresh token.
     * It returns a new access token and a new refresh token. The old tokens are invalidated.
     * ## refresh_token: The refresh token received during login or a previous refresh.
     * @lrd:end
     * @LRDparam refresh_token string|required
     */
    public function refresh(Request $request)
    {
        Log::info("Refresh Token Initiated.");
        $validator = Validator::make($request->all(), [
            'refresh_token' => 'required',
        ]);

        if ($validator->fails())
            return ApiResponse::validationError($validator->errors(), $validator->errors());

        try {
            $parts = explode('.', request()->bearerToken());
            if (count($parts) !== 3) {
                throw new \Exception('Invalid token format');
            }

            $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);

            if (!$payload) {
                throw new \Exception('Unable to decode payload');
            }

            $accessTokenUserId = $payload['user_id'] ?? null;

            $user = User::find($accessTokenUserId);
            if (!isset($user))
                return ApiResponse::unauthorized("Invalid user.", logMessage: "Invalid user.");

            $newAccessToken = JWTAuth::fromUser($user);

            $refreshDecode = JWTAuth::decode(new Token($request->refresh_token));
            $refreshPayload = $refreshDecode->toArray();
            if (!isset($refreshPayload['is_refresh']))
                return ApiResponse::unauthorized("Invalid refresh token provided.", logMessage: "Invalid refresh token provided. is_refresh not found. User Id => $accessTokenUserId");

            $refreshTokenUserId = $refreshPayload['user_id'];

            if ($refreshTokenUserId != $accessTokenUserId)
                return ApiResponse::unauthorized("Invalid refresh token provided.", logMessage: "Invalid refresh token provided. user id of both tokens are different. User Id => $accessTokenUserId");

            JWTAuth::factory()->setTTL(config('jwt.refresh_ttl'));
            $newRefreshToken = JWTAuth::parseToken()->refresh();

            JWTAuth::invalidate(JWTAuth::getToken());
            JWTAuth::setToken($request->refresh_token);
            JWTAuth::invalidate(JWTAuth::getToken());

            $toReturn = ApiResponseHelper::getData([
                "token" => $newAccessToken,
                "refresh_token" => $newRefreshToken,
            ], 'Tokens refreshed successfully.');
            return response()->json($toReturn, $toReturn['statusCode']);
        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            Log::error("Refresh token expired => " . $e->getMessage());
            return ApiResponse::unauthorized("Refresh token expired.", logMessage: "Refresh token expired.");
        } catch (\Tymon\JWTAuth\Exceptions\JWTException $e) {
            Log::error("Invalid token => " . $e->getMessage());
            return ApiResponse::unauthorized("Token invalid.", logMessage: "Token invalid.");
        }
    }

    /**
     * @lrd:start
     * ## Set MPIN
     * Allows a logged-in user to set their MPIN (Mobile Personal Identification Number).
     * If the biometric feature is enabled, it also enables biometric authentication for the device.
     * ## mpin: A 4-digit personal identification number.
     * The user must be authenticated via JWT.
     * @lrd:end
     * @LRDparam mpin string|required|digits:4
     */
    public function setMpin(Request $request)
    {
        try {
            $jwtPayload = JWTAuth::parseToken()->getPayload();
            $userId = $jwtPayload->get("user_id");

            logInfo("Set MPIN.", ['user_id' => $userId, 'mpin' => $request->mpin]);
            $request->validate(['mpin' => 'required|digits:4']);

            $params = $this->prepareLoginData($request, ['mpin' => $request->mpin]);
            $setMpin = $this->loginRepo->setMpin($params);

            $logMessage = "Set MPIN. User ID: $userId. MPIN: $request->mpin";

            if ($setMpin) {
                return ApiResponse::successMessage("MPIN set successfully.");
            }

            return ApiResponse::errorMessage("Error setting MPIN.", $logMessage);
        } catch (\Exception $e) {
            logError("Error while setting MPIN.", $e);
            return ApiResponse::errorMessage("Error setting MPIN.");
        }
    }

    /**
     * @lrd:start
     * ## Verify MPIN
     * Verifies the user's MPIN for authentication within the app.
     * ## mpin: The 4-digit MPIN to be verified.
     * The user must be authenticated via JWT.
     * @lrd:end
     * @LRDparam mpin string|required
     */
    public function verifyMpin(Request $request)
    {
        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");

        logInfo("Verify User Initiated", ["user_id" => $userId, "mpin" => $request->mpin]);
        $request->validate(['mpin' => 'required']);

        $params = $this->prepareLoginData($request, ['mpin' => $request->mpin]);
        $verifyMpin = $this->deviceLogRepo->verifyMpin($params);

        $logMessage = "Verify MPIN attempt. User ID: $userId";
        if ($verifyMpin['status']) {
            return ApiResponse::getData(['token' => $verifyMpin['token']], 'MPIN verified.', $logMessage);
        }
        return ApiResponse::accessDenied("MPIN you entered doesn’t match. Please try again.", $logMessage);
    }

    /**
     * @lrd:start
     * ## Enable Biometric Authentication
     * Enables biometric authentication for the current device for a logged-in user.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function biometricEnabled(Request $request)
    {
        if (!app(AppSettingRepositoryInterface::class)->isFeatureEnabled('biometric')) {
            logInfo("Biometric feature disabled in config");
            return false;
        }

        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");

        if (!$request->biometric_enabled) {
            logInfo("Biometric is disabled or skipped", ['user_id' => $userId]);
            return false;
        }

        try {
            $params = $this->prepareLoginData($request, [
                'biometric_enabled' => $request->biometric_enabled
            ]);
            $this->deviceLogRepo->updateDeviceActivity($params);

            logInfo("Biometric enabled successfully", ['user_id' => $userId]);

            return true;
        } catch (Exception $e) {
            logError("Error enabling biometric", $e);
            return false;
        }
    }


    /**
     * @lrd:start
     * ## Verify Biometric Authentication
     * Verifies that biometric authentication is enabled for the user's device.
     * This is typically used when the app is launched and the user authenticates with biometrics locally on the device.
     * ## biometric_enabled: A boolean flag indicating if biometric was used.
     * The user must be authenticated via JWT.
     * @lrd:end
     * @LRDparam biometric_enabled boolean|required
     */
    public function verifyBiometric(Request $request)
    {
        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");
        logInfo("Verify User Biometric Initiated", ["user_id" => $userId, "biometric" => $request->biometric_enabled]);

        $params = $this->prepareLoginData($request);
        $params = [
            ...$params,
            'biometric_enabled' => $request->biometric_enabled
        ];
        $verifyBiometric = $this->deviceLogRepo->verifyBiometric($params);
        if ($verifyBiometric) {
            return ApiResponse::successMessage('Biometric verified.');
        }
        return ApiResponse::accessDenied("Biometric verification failed.");
    }

    /**
     * @lrd:start
     * ## Reset Biometric Authentication
     * Resets/disables biometric authentication for the current device for a logged-in user.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function resetBiometric(Request $request)
    {
        if (!app(AppSettingRepositoryInterface::class)->isFeatureEnabled('biometric')) {
            return ApiResponse::errorMessage("Biometric feature is disabled.");
        }
        try {
            $userId = JWTAuth::parseToken()->getPayload()->get("user_id");

            logInfo("User Biometric Reset", ['user_id' => $userId]);

            $params = $this->prepareLoginData($request, [
                'biometric_enabled' => false
            ]);

            $this->deviceLogRepo->updateDeviceActivity($params);

            return ApiResponse::successMessage("Biometric reset successfully.");
        } catch (Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
    }
    private function prepareLoginData(Request $request, $additionalData = [])
    {
        $jwtPayload = JWTAuth::parseToken()->getPayload();
        return [
            "user_id"        => $jwtPayload->get('user_id'),
            "username"       => $jwtPayload->get("username"),
            "app_login_code" => $jwtPayload->get("app_login_code"),
            "token"          => $request->bearerToken(),
            ...$this->getDeviceDetail($request->header()),
            ...$additionalData,
        ];
    }

    private function getDeviceDetail($headers)
    {
        return [
            "agent"            => $headers['user-agent'][0],
            "device"           => $headers['user-device'][0],
            "device_brand"     => $headers['user-device-brand'][0],
            "device_platform"  => $headers['user-device-platform'][0],
            "device_version"   => $headers['user-device-build'][0],
            "device_model"     => $headers['user-device-model'][0],
            "app_version"      => $headers['user-device-app-version'][0],
            "app_version_code" => $headers['user-device-app-version-code'][0],
        ];
    }

    /**
     * @lrd:start
     * ## Refresh User Details and Status
     * Fetches the latest user details and feature status for the logged-in user.
     * This is useful for keeping the app's state in sync with the server. It also updates the FCM token for the device.
     * ## fcm_token: The Firebase Cloud Messaging token for push notifications.
     * The user must be authenticated via JWT.
     * @lrd:end
     * @LRDparam fcm_token string|required
     */
    public function refreshUserDetailsAndStatus(Request $request)
    {
        $request->validate(['fcm_token' => 'required']);
        $params = $this->prepareLoginData($request);
        $userDetails = $this->loginRepo->fetchUserDetail($params);
        if (!$userDetails) {
            return ApiResponse::unauthorized("User not found");
        }
        $features = $this->loginRepo->checkFeatures([
            ...$params,
            'fcm_token' => $request->fcm_token
        ]);

        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");
        $appLoginCode = JWTAuth::parseToken()->getPayload()->get("app_login_code");
        $deviceDetails = [
            "user_id"        => $userId,
            "app_login_code" => $appLoginCode,
            'fcm_token' => $request->fcm_token,
            ...$this->getDeviceDetail($request->header()),
        ];

        $this->deviceLogRepo->updateDeviceActivity($deviceDetails);

        return ApiResponse::getData([
            "user_details"       => $userDetails,
            ...$features
        ]);
    }

    /**
     * @lrd:start
     * ## Logout
     * Logs out the current user by invalidating their JWT and deactivating the device.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function logout(Request $request)
    {
        $userId = JWTAuth::parseToken()->getPayload()->get("user_id");
        $appLoginCode = JWTAuth::parseToken()->getPayload()->get("app_login_code");
        try {
            logInfo("Employee Logout Initiated", ["user_id" => $userId]);

            $deviceDetails = [
                "user_id"        => $userId,
                "app_login_code" => $appLoginCode,
                ...$this->getDeviceDetail($request->header()),
            ];
            $deactivatedDevice = $this->deviceLogRepo->deactivateDevice($deviceDetails);
            if ($deactivatedDevice) {
                logInfo("Employee Logout Deactivated Device", ["user_id" => $userId]);
                $invalidateToken = JWTAuth::invalidate(JWTAuth::getToken());
                if ($invalidateToken) {
                    logInfo("Employee Logout Successful", ["user_id" => $userId]);
                    return ApiResponse::successMessage("Employee logged out successfully.");
                }
            }
            logError("Error Employee Logout", context: ["user_id" => $userId]);
            return ApiResponse::errorMessage("Error logging out employee.");
        } catch (Exception $e) {
            logError("Error Employee Logout, Exception ", $e);
            return ApiResponse::errorMessage("Error logging out employee.");
        }
    }
}
