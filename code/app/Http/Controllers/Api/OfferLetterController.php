<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\Mail\Interfaces\ManpowerRequisitionMailInterface;
use App\Http\Services\OfferLetterService;
use App\Mail\OfferLetterMail;
use App\Models\configs\MailConfigurationSetting;
use App\Models\EdfEmail;
use Illuminate\Encryption\Encrypter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class OfferLetterController extends Controller
{
    public function __construct(
        private ManpowerRequisitionMailInterface $edfEmailRepo
    ) {
    }
    public function sendOfferLetter(Request $request, $uuid)
    {
        $edfEmail = EdfEmail::where('id', $uuid)->first();
        $contactMail = MailConfigurationSetting::where('company_id', $edfEmail->mail_meta['company_id'])->where('key','contact_mail')->value('value');

        if (!$edfEmail) {
            $response = ApiResponseHelper::notFound("The UUID is invalid.Please check the URL.");
            return response()->json($response, $response['statusCode']);
        }

        if ($edfEmail->status !== "Pending") {
            return ApiResponse::getData(
                ['completed' => true],
                "Form was submitted successfully.
                        Please contact <strong>{$contactMail}</strong> for further assistance.",
            );
        }

        $emailData = $this->edfEmailRepo->getEmailData($edfEmail);
        $formSchema = OfferLetterService::offerLetterFormSchema();

        $fullHtmlContent = view('emails.partialOfferLetterLayout', $emailData)->render();

        return ApiResponse::getData([
            'html_content' => $fullHtmlContent,
            'form_schema' => $formSchema,
        ]);
    }

    public function getOfferLetterResponse(Request $request, $uuid)
    {
        $rules = [];
        if ($request->type == 'accept') {
            $rules = OfferLetterService::offerLetterFormRules();
            $rules['type'] = 'required|in:accept,reject';
        } else {
            $rules = [
                'reason' => 'required|string',
                'type' => 'required|in:accept,reject',
            ];
        }
        $validated = $request->validate($rules);
        try {
            $edfEmail = $this->edfEmailRepo->updateOfferLetterResponse($uuid, $validated);

            $edf = EdfEmail::where('id', $uuid)->first();
            $contactMail = MailConfigurationSetting::where('company_id', $edf->mail_meta['company_id'])->where('key','contact_mail')->value('value');

            return ApiResponse::successMessage(
                "Response submitted successfully.
                        Please contact <strong>{$contactMail}</strong> for further assistance.",
            );
        } catch (\Exception $e) {
            Log::error('Error storing offer letter response: ' . $e->getMessage());

            return ApiResponse::errorMessage(
                'Failed to store response data: ' . $e->getMessage()
            );
        }
    }
}
