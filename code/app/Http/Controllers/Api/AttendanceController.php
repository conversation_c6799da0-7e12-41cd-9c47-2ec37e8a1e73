<?php

namespace App\Http\Controllers\Api;

use App\Http\Repositories\TicketDetailRepository;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\Reports\AttendanceRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use PermissionList;
use Tymon\JWTAuth\Facades\JWTAuth;

class AttendanceController extends Controller
{
    private $attendanceRepo;
    private TicketDetailRepository $ticketDetailRepo;

    public function __construct()
    {
        $this->attendanceRepo = new AttendanceRepository();
        $this->ticketDetailRepo = new TicketDetailRepository();
    }

    /**
     * @lrd:start
     * ## My Attendance
     * Fetches the attendance list for the authenticated user for a specific month and year.
     * If year and month are not provided, it defaults to the current Nepali month and year.
     * ## year: `The Nepali year (e.g., 2081).`
     * ## month: `The Nepali month (e.g., 4 for Shrawan).`
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam year optional|integer
     * @LRDparam month optional|integer
     */
    public function myAttendance(Request $request)
    {

        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");

        if (isset($request->year)) {
            $year = $request->year;
        } else {
            $year = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        }

        if (isset($request->month)) {
            $month = $request->month;
        } else {
            $month = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        }

        $params = [
            "employeeId" => $employeeId,
            "date"       => "$year-$month",
            "sortBy"    => "date_en",
            "sortDirection" => "desc",
        ];

        $attendanceList = $this->attendanceRepo->attendanceList($params);

        $attendanceList = $attendanceList->map(function ($attendance) {
            $item = $attendance->toArray();
            // $item['time_request'] = $this->attendanceRepo->showApplyMissedPunch($item);
            // $item['leave_request'] = $this->attendanceRepo->showApplyLeave($item);
            $item['date_np_human_readable'] = $this->ticketDetailRepo->getNepaliDate($item['date_en']);
            foreach ($item as $key => $value) {
                if (is_null($value)) {
                    $item[$key] = 'N/A';
                }
            }
            return $item;
        });

        $data['attendance'] = $attendanceList;
        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    /**
     * @lrd:start
     * ## Team Member Attendance
     * Fetches the attendance list for the team members of the authenticated user for a specific date.
     * If the date is not provided, it defaults to the current Nepali date.
     * ## date The Nepali date in YYYY-MM-DD format.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam date string|optional
     */
    public function teamMemberAttendance(Request $request)
    {

        if (isset($request->date)) {
            $params['selectedDate'] = $request->date;
        } else {
            $params['selectedDate'] = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y-m-d');
        }

        $data = $this->attendanceRepo->teamMemberAttendanceList($params);
        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn, $toReturn['statusCode']);
    }

    /**
     * @lrd:start
     * ## Sync Attendance Logs (FedEx Only)
     * This endpoint is used by an external Node.js application to sync attendance logs in bulk.
     * This is specific to the FedEx HRM implementation.
     * ## data An array of attendance log objects.
     * ## data.*.enrollment_no The employee's enrollment number.
     * ## data.*.verify_type The verification type.
     * ## data.*.verify_state The verification state (in/out).
     * ## data.*.ip The IP address of the device.
     * ## data.*.record_time The timestamp of the log.
     * @lrd:end
     *
     * @LRDparam data array required.
     */
    public function syncAttendanceLogs(Request $request)
    {
        if (!fedexHrm()) return response()->json(['status' => false, 'message' => 'Only for fedex hrm']);
        $request->validate([
            'data' => 'required|array'
        ]);
        $data = array_map(function ($item) {
            return [
                'enrollment_no' => $item['enrollment_no'],
                'verify_mode' => $item['verify_type'],
                'inout_mode' => $item['verify_state'],
                'device_ip' => $item['ip'],
                'log_date' => $item['record_time'],
            ];
        }, $request->data);
        $this->attendanceRepo->saveAttendanceLogs($data);
        return response()->json([
            'status' => true,
            'message' => 'Attendance logs synced successfully',
            'clearAttendance' => true,
        ]);
    }

    /**
     * @lrd:start
     * ## Get Today's Attendance
     * Fetches the clock-in and clock-out times for the authenticated user for the current day.
     * It also returns a boolean indicating if the user is allowed to perform clock-in/out actions.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function getTodayAttendance(Request $request)
    {
        $attRepo = new AttendanceRepository;
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        try {
            $canClockInOut = $request->user?->can(PermissionList::APP_ATTENDANCE);

            $data = $canClockInOut ? $attRepo->getTodayAttendance($employeeId) : null;

            return ApiResponse::getData([
                'in_time' => $data?->in_time ?? "",
                'out_time' => $data?->out_time ?? "",
                'allowClockInOut' => $canClockInOut
            ]);
        } catch (\Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
    }

    /**
     * @lrd:start
     * ## Clock In
     * Allows the authenticated user to clock in for the day.
     * Requires the user's current latitude and longitude for location verification.
     * The user must be authenticated via JWT and have the necessary permissions.
     * @lrd:end
     *
     * @LRDparam latitude numeric|required
     * @LRDparam longitude numeric|required
     */
    public function clockIn(Request $request)
    {
        if (!$request->latitude || !$request->longitude) {
            return ApiResponse::errorMessage("Your location can't be retrieved. Please check your settings and try again");
        }
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);
        $attRepo = new AttendanceRepository;
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        try {
            $coordinates = [
                'latitude' => $request->latitude,
                'longitude' => $request->longitude
            ];
            $canClockIn = $attRepo->canClockInOut($employeeId, $coordinates);
            if (!$canClockIn) {
                return ApiResponse::errorMessage("You are too far from the office location to mark attendance. Please move closer and try again");
            }
            $data = $attRepo->clockInOut($employeeId, 'in', coordinates: $coordinates);
        } catch (\Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
        return ApiResponse::successMessage("Clock In Successfully");
    }

    /**
     * @lrd:start
     * ## Clock Out
     * Allows the authenticated user to clock out for the day.
     * Requires the user's current latitude and longitude for location verification.
     * The user must be authenticated via JWT and have the necessary permissions.
     * @lrd:end
     *
     * @LRDparam latitude numeric|required
     * @LRDparam longitude numeric|required
     */
    public function clockOut(Request $request)
    {
        if (!$request->latitude || !$request->longitude) {
            return ApiResponse::errorMessage("Unable to retrieve your location. Please allow location access to continue");
        }
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $attRepo = new AttendanceRepository;
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        try {
            $coordinates = [
                'latitude' => $request->latitude,
                'longitude' => $request->longitude
            ];
            $canClockIn = $attRepo->canClockInOut($employeeId, $coordinates);
            if (!$canClockIn) {
                return ApiResponse::errorMessage("You are too far from the office location to mark attendance. Please move closer and try again");
            }
            $data = $attRepo->clockInOut($employeeId, 'out', coordinates: $coordinates);
        } catch (\Exception $e) {
            return ApiResponse::errorMessage($e->getMessage());
        }
        return ApiResponse::successMessage("Clock Out Successfully");
    }
}
