<?php

namespace App\Http\Controllers\Api;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\TicketRepository;
use App\Http\Repositories\TimeRequestRepository;
use App\Http\Requests\TimeRequest;
use App\Http\Resources\VerifierResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;

class TimeRequestController extends Controller
{
    private TimeRequestRepository $timeRequestRepo;
    private TicketRepository $ticketRepo;

    public function __construct()
    {
        $this->timeRequestRepo = new TimeRequestRepository();
        $this->ticketRepo = new TicketRepository();
    }

    /**
     * @lrd:start
     * ## Get Time Request Verifiers
     * Fetches a list of employees who can verify a time request for the authenticated user.
     * This is typically used to populate a dropdown when a user is applying for a time request.
     * The user must be authenticated via JWT.
     * @lrd:end
     */
    public function verifiers()
    {
        $params = [
            "employee_id" => JWTAuth::parseToken()->getPayload()->get("employee_id"),
            "is_submitting" => true,
        ];
        $data = $this->timeRequestRepo->verifiers($params);
        return ApiResponse::getData(['verifiers' => VerifierResource::collection($data)]);
    }

    /**
     * Displays the time at which the employee has arrived at that particular date
     * @lrd:start
     * ## Get Recorded Time
     * Fetches the recorded clock-in and clock-out times for the authenticated user on a specific Nepali date.
     * This is used to pre-fill information when applying for a time request (missed punch).
     * ## date The Nepali date in YYYY-MM-DD format.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam date string|required
     */
    public function recordedTime(Request $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        Log::info("Recorded Time. Employee Id =>" . $employeeId . ". Params =>" . print_r($request->all(), true));
        $validator = Validator::make($request->all(), [
            'date' => 'required',
        ]);

        if ($validator->fails()) {
            Log::info("Recorded Time. Employee Id =>" . $employeeId . ". Params =>" . print_r($request->all(), true) . ". Validation Failed => " . print_r($validator->errors(), true));
            $toReturn = ApiResponseHelper::validationError($validator->errors());
            return response()->json($toReturn, $toReturn['statusCode']);
        }

        $params = [
            "employee_id" => $employeeId,
            "nep_date" => $request->date,
        ];
        $recordedTime = $this->timeRequestRepo->recordedTime($params);

        $data = [
            "check_in"  => $recordedTime->in_time ? date("H:i", strtotime($recordedTime->in_time)) : "N/A",
            "check_out" => $recordedTime->out_time ? date("H:i", strtotime($recordedTime->out_time)) : "N/A",
        ];
        $toReturn = ApiResponseHelper::getData($data);
        return response()->json($toReturn);
    }

    /**
     * @lrd:start
     * ## Apply for Time Request
     * Submits a new time request (e.g., for a missed punch) for the authenticated user. In time or Out time should be provided.
     * nep_date: The Nepali date for the time request (YYYY-MM-DD).
     * actual_in_time: The corrected clock-in time (HH:mm). Required if actual_out_time is not present.
     * actual_in_time_remarks: Remarks for the corrected clock-in time. Required if actual_in_time is present.
     * actual_out_time: The corrected clock-out time (HH:mm). Required if actual_in_time is not present.
     * actual_out_time_remarks: Remarks for the corrected clock-out time. Required if actual_out_time is present.
     * verifier_id: The ID of the employee who will verify the request.
     * documents: An array of document files to upload.
     * removing_document_ids: An array of existing document IDs to be removed.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam nep_date string required
     * @LRDparam actual_in_time string|nullable
     * @LRDparam actual_in_time_remarks string|nullable
     * @LRDparam actual_out_time string|nullable
     * @LRDparam actual_out_time_remarks string|nullable
     * @LRDparam verifier_id integer required
     * @LRDparam documents file[]|nullable
     * @LRDparam removing_document_ids integer[]|nullable
     */
    public function apply(TimeRequest $request)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        logInfo("Apply Time Request", [
            'employeeId' => $employeeId,
            'params' => $request->all()
        ]);

        try {
            $params = [
                "nep_date"              => $request->nep_date,
                "in_time"               => $request->actual_in_time,
                "in_note"               => $request->actual_in_time_remarks,
                "out_time"              => $request->actual_out_time,
                "out_note"              => $request->actual_out_time_remarks,
                "fiscal_year_id"        => currentFiscalYearId(),
                "employee_id"           => $employeeId,
                "verifier_id"           => $request->verifier_id,
                "documents"             => $request->documents ?? null,
                "removing_document_ids" => [],

            ];

            $data = $this->timeRequestRepo->createOrUpdateTimeRequest($params);
            Log::info("Apply Time Request Success", [
                'employeeId' => $employeeId,
                'params'     => $params,
                'response'   => $data
            ]);

            return ApiResponse::successMessage("Time Request Applied Successfully");
        } catch (\Exception $e) {
           return ApiResponse::errorMessage($e->getMessage());
        }
    }

    /**
     * @lrd:start
     * ## Get Time Request Details
     * Fetches the details of a specific time request ticket, identified by its ticket ID.
     * This includes the original recorded times and the requested corrected times.
     * The user must be authenticated via JWT and have permission to view the ticket.
     * @lrd:end
     */
    public function getTimeRequest(int $ticketId)
    {
        $ticket = $this->ticketRepo->getTicketInfo($ticketId, WorkflowName::TIME_REQUEST_APPROVAL);
        if (!$ticket)
            return ApiResponse::errorMessage("Time Request Ticket not found");

        if (!$ticket->model->canEdit())
            return ApiResponse::errorMessage("You cannot update this time request");

        $timeRequest = $this->timeRequestRepo->getTimeRequest($ticket->model_id);
        if (!$timeRequest)
            return ApiResponse::errorMessage("Time Request Not Found");

        $params = [
            "employee_id" => $timeRequest->requestTicket->employee_id,
            "nep_date"    => $timeRequest->nep_date,
        ];
        $recordedTime = $this->timeRequestRepo->recordedTime($params);

        // dd($recordedTime->in_time, date("H:i", strtotime($recordedTime?->in_time)) ?? "N/A");

        $detail = [
            'time_request_id'           => $timeRequest->id,
            'nep_date'                  => $timeRequest->nep_date,
            'check_in_time'             => $recordedTime?->in_time ? date("H:i", strtotime($recordedTime?->in_time)) : "N/A",
            'actual_in_time'            => $timeRequest->in_time ? date("H:i", strtotime($timeRequest->in_time)) : "N/A",
            'actual_in_time_remarks'    => $timeRequest->in_note ?? "N/A",
            'check_out_time'            => $recordedTime->out_time ? date("H:i", strtotime($recordedTime?->out_time)) : "N/A",
            'actual_out_time'           => $timeRequest->out_time ? date("H:i", strtotime($timeRequest->out_time)) : "N/A",
            'actual_out_time_remarks'   => $timeRequest->out_note ?? "N/A",
            'verifier_id'               => $timeRequest->requestTicket->current_owner_id,
            'documents'                 => $timeRequest->requestTicket->getDocuments()
        ];
        return ApiResponse::getData($detail);
    }

    /**
     * @lrd:start
     * ## Update Time Request
     * Updates an existing time request ticket.
     * nep_date: The Nepali date for the time request (YYYY-MM-DD).
     * actual_in_time: The corrected clock-in time (HH:mm). Required if `actual_out_time` is not present.
     * actual_in_time_remarks: Remarks for the corrected clock-in time. Required if `actual_in_time` is present.
     * actual_out_time: The corrected clock-out time (HH:mm). Required if `actual_in_time` is not present.
     * actual_out_time_remarks: Remarks for the corrected clock-out time. Required if `actual_out_time` is present.
     * verifier_id: The ID of the employee who will verify the request.
     * documents: An array of new document files to upload.
     * removing_document_ids: An array of existing document IDs to be removed.
     * The user must be authenticated via JWT and have permission to edit the ticket.
     * @lrd:end
     *
     * @LRDparam nep_date string required
     * @LRDparam actual_in_time string|nullable
     * @LRDparam actual_in_time_remarks string|nullable
     * @LRDparam actual_out_time string|nullable
     * @LRDparam actual_out_time_remarks string|nullable
     * @LRDparam verifier_id integer required
     * @LRDparam documents file[]|nullable
     * @LRDparam removing_document_ids integer[]|nullable
     */
    public function updateTimeRequest(TimeRequest $request, int $ticketId)
    {
        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        $ticket = $this->ticketRepo->getTicketInfo($ticketId, WorkflowName::TIME_REQUEST_APPROVAL);
        if (!$ticket)
            return ApiResponse::errorMessage("Time Request Ticket not found", "Time Request Ticket not found");

        if (!$ticket->model->canEdit())
            return ApiResponse::errorMessage("You cannot update this time request", "You cannot update this time request");

        if (isset($request->actual_in_time_remarks) && !isset($request->actual_in_time))
            $request->merge(['actual_in_time_remarks' => null]);

        if (isset($request->actual_out_time_remarks) && !isset($request->actual_out_time))
            $request->merge(['actual_out_time_remarks' => null]);

        $request->merge([
            'in_time'    => $request->actual_in_time,
            'in_note'    => $request->actual_in_time_remarks,
            'out_time'   => $request->actual_out_time,
            'out_note'   => $request->actual_out_time_remarks,
        ]);

        try {
            $response = $this->timeRequestRepo->createOrUpdateTimeRequest([
                ...$request->all(),
                "employee_id"    => $employeeId,
            ], $ticket->model_id);
            return ApiResponse::successMessage("Time Request Updated Successfully");
        } catch (\Exception $e) {
            return ApiResponse::errorMessage($e->getMessage(), $e->getMessage());
        }
    }
}
