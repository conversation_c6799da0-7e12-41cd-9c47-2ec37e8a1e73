<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use App\Http\Repositories\HolidayRepository;
use App\Http\Repositories\LeaveRepository;
use App\Http\Resources\HolidayResource;
use App\Http\Resources\HolidayResourceCollection;
use App\Http\Resources\PaginationResourceCollection;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class HolidayController extends Controller
{
    private HolidayRepository $holidayRepo;
    private LeaveRepository $leaveRepo;

    public function __construct()
    {
        $this->holidayRepo = new HolidayRepository();
        $this->leaveRepo = new LeaveRepository();
    }

    /**
     * @lrd:start
     * ## Get Holiday List
     * Fetches a paginated list of holidays for the authenticated user.
     * The list is automatically filtered by the user's branch.
     * ## per_page: The number of items per page. Defaults to 25.
     * ## fiscal_year_id: The ID of the fiscal year. Defaults to the active fiscal year.
     * The user must be authenticated via JWT.
     * @lrd:end
     *
     * @LRDparam per_page integer|optional
     * @LRDparam fiscal_year_id integer|optional
     */
    public function list(Request $request)
    {
        if (!isset($request->per_page)) {
            $request->merge(['per_page' => 25]);
        }

        if (!isset($request->fiscal_year_id)) {
            $fiscalYear = $this->leaveRepo->activeFiscalYear();
            $request->merge(['fiscal_year_id' => $fiscalYear]);
        }

        $employeeId = JWTAuth::parseToken()->getPayload()->get("employee_id");
        $branchId = $this->holidayRepo->empBranch($employeeId);
        $request->merge(['branch_id' => $branchId]);

        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 25);
        $holidayDetailList = $this->holidayRepo->list($request->all())->paginate(perPage: $perPage, page: $page);
        $toReturn =  new PaginationResourceCollection(HolidayResource::collection($holidayDetailList));
        return ApiResponse::getData($toReturn);
    }
}
