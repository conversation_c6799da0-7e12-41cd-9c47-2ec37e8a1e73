<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\configs\Branch;
use App\Models\configs\SubBranch;
use App\Models\configs\EmpStatus;
use App\Models\configs\Department;
use App\Models\configs\Unit;
use App\Models\configs\OutsourceCompany;
use App\Models\Payroll\Designation;
use App\Models\User;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Stmt\TryCatch;

class ImportController extends Controller
{
    protected $viahrm, $hrm, $roiopsdb, $rentalData;
    public function __construct()
    {
        $this->hrm = DB::connection('mysql1');
        $this->roiopsdb = DB::connection('mysql2');
        $this->rentalData = DB::connection('mysql3');
    }

    public function import(Request $request)
    {
        $type = $request->type;
        switch ($type) {
            case 'rentalOwner':
                $importRentalOwner = $this->_rentalOwner();
                break;
            case 'rentalAgreement':
                $importrentalAgreement = $this->_rentalAgreement();
                break;
            case 'rentalDocument':
                $importRentalDocument = $this->_rentalDocument();
                break;
            case 'rentalReject':
                $importRentalReject = $this->_rentalReject();
                break;
            case 'rentalIncrementDetails':
                $importrentalIncrementDetails = $this->_rentalIncrementDetails();
                break;
            case 'rentalAgreementAmount':
                $importrentalAgreementAmount = $this->_rentalAgreementAmount();
                break;
            case 'rentalElectricityBill':
                $importrentalElectricityBill = $this->_rentalElectricityBill();
                break;

                // case 'supervisorUpdate':
                //     $importSupervisor = $this->_supervisorUpdate();
                //     break;
                // case 'branchManagerUpdate':
                //     $importBranch = $this->_branchManagerUpdate();
                //     break;
                // case 'branch':
                //     $importBranch = $this->_importBranches();
                //     break;
                // case'sub_branch':
                //     $importCollectionCenter = $this->_importCollectionCenter();
                //     break;
                // case 'emp_status':
                //     $importEmpStatus = $this->_importEmpStatus();
                //     break;
                // case 'department':
                //     $importDepartment = $this->_importDepartment();
                //     break;
                // case 'unit':
                //     $importUnit = $this->_importUnit();
                //     break;

                // case 'designation':
                //     $importDesignation = $this->_importDesignation();
                //     break;

                // case 'outsource':
                //     $importOutsource = $this->_importOutsource();
                //     break;

                // case 'employee':
                //     $importEmployee = $this->_importEmployee();
                //     break;
        }
        dd('completed');

        // $importCollectionCenter = $this->_importCollectionCenter();
        // $importEmpStatus = $this->_importEmpStatus();
        // $importDepartment = $this->_importDepartment();
        // $importUnit = $this->_importUnit();
        // $importShift = $this->_importShift();
        // $importDesignation = $this->_importDesignation();
        // $importOutsource = $this->_importOutsource();
        // $importEmployee = $this->_importEmployee();


    }

    public function _rentalOwner()
    {
        $rentalOwner = $this->rentalData->table('hris_rental_owner')->get();

        $dataToBeInserted = [];
        foreach ($rentalOwner as $rentalOwner) {

            $transformedData = [
                'id' => $rentalOwner->id,
                'owner_name' => $rentalOwner->owner_name,
                'contact_number' => $rentalOwner->contact_number ?? null,
                'primary_bank_name' => $this->getBankIdFromName($rentalOwner->bank),
                'primary_account_number' => $rentalOwner->account_no,
                'primary_account_name' => $rentalOwner->account_name,
                'primary_bank_code' => $this->getBankCodeFromName($rentalOwner->bank),
                'primary_bank_branch' => $rentalOwner->bank_branch ?? null,
                'secondary_bank_name' => $this->getBankIdFromName($rentalOwner->secondary_bank),
                'secondary_account_number' => $rentalOwner->secondary_account_no,
                'secondary_account_name' => $rentalOwner->secondary_account_name,
                'secondary_bank_code' => $this->getBankCodeFromName($rentalOwner->secondary_bank),
                'secondary_bank_branch' => $rentalOwner->secondary_bank_branch ?? null,
                'branch_id' => $this->getNewBranchId($rentalOwner->branch_id),
                'oc_id' => $this->getNewSubBranchId($rentalOwner->oc_id),
                'rental_type_id' => $rentalOwner->rental_type_id,
                'location_type' => $rentalOwner->location_type,
                'payment_type' => $rentalOwner->payment_type ?? null,
                'location' => $rentalOwner->location,
                'status' => $rentalOwner->status,
                'termination_clause' => $rentalOwner->termination_clause ?? null,
                'rental_status' => $rentalOwner->rental_status ?: 'Rejected',
                'added_by' => $this->employeeMap($rentalOwner->added_by) ?? null,
                'approved_by' => $this->employeeMap($rentalOwner->approved_by),
                'rental_code' => $rentalOwner->rental_code,
                'pop_id' => $rentalOwner->pop_id ?? null,
                'grandfather_name' => $rentalOwner->grandfather_name,
                'father_name' => $rentalOwner->father_name,
                'citizenship_number' => $rentalOwner->citizenship_no,
                'customer_id' => $rentalOwner->customer_id ?? null,
                'created_at' => $rentalOwner->created_at,
                'updated_at' => $rentalOwner->updated_at,
                'deleted_at' => $rentalOwner->deleted_at ?? null,
            ];
            $dataToBeInserted[] = $transformedData;
        }

        if (!empty($dataToBeInserted)) {
            $chunks = array_chunk($dataToBeInserted, 500); // Split into chunks of 500 records

            DB::beginTransaction();
            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('rental_owner')->updateOrInsert(
                            ['id' => $data['id']],
                            $data
                        );
                    }
                }
                DB::commit();
                dd('Data has been bulk inserted successfully.');
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    public function _rentalAgreement()
    {
        $rentalAgreement = $this->rentalData->table('hris_rental_agreement')->get();
        $default = DB::table('rental_agreement')->first();
        $dataToBeInserted = [];

        foreach ($rentalAgreement as $agreement) {
            $transformedData = [
                'id' => $agreement->id,
                'rental_owner_id' => $agreement->rental_owner_id,
                'agreement_date' => $agreement->agreement_date,
                'agreement_end_date' => $agreement->agreement_end_date,
                'security_deposit'  => $agreement->security_deposit,
                'agreement_period_year' => $agreement->agreement_period_year,
                'agreement_period_month' => $agreement->agreement_period_month,
                'gross_rental_amount' => $agreement->gross_rental_amount,
                'net_rental_amount' => $agreement->net_rental_amount,
                'current_rental_amount' => $agreement->current_rental_amount,
                'tds'   => $agreement->tds,
                'electricity_rate' => $agreement->electricity_rate,
                'advance'  => $agreement->advance,
                'payment_period'   => $agreement->payment_period,
                'total_rent'   => $agreement->total_rent,
                'status' => $agreement->status,
                'terminated_date'  => $agreement->terminated_date,
                'remarks'  => $agreement->remarks,
                'locked'   => $agreement->locked,
                'district' => $agreement->district,
                'municipality' => $agreement->municipality,
                'ward_no'  => $agreement->ward_no,
                'floors_num'   => $agreement->floors_num,
                'agreement_floor'  => $agreement->agreement_floor,
                'area_floor'   => $agreement->area_floor,
                'kitta_no' => $agreement->kitta_no,
                'witnesses'    => $agreement->witnesses,
                'amendment_child_id'   => $agreement->amendment_child_id,
                'agreement_status' => $agreement->agreement_status ?: 'Rejected',
                'added_by' => $this->employeeMap($agreement->added_by),
                'approved_by'  => $this->employeeMap($agreement->approved_by),
                'tds_payable'  => $agreement->tds_payable,
                'created_at' => $agreement->created_at,
                'updated_at' => $agreement->updated_at,
                'deleted_at' => $agreement->deleted_at ?? null,
            ];
            $dataToBeInserted[] = $transformedData;
        }
        if (!empty($dataToBeInserted)) {
            $chunks = array_chunk($dataToBeInserted, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('rental_agreement')->updateOrInsert(
                            ['id' => $data['id']],
                            $data
                        );
                    }
                }
                DB::commit();
                dd('Data has been bulk inserted successfully.');
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    public function _rentalDocument()
    {

        $agreementDocuments = $this->rentalData->table('hris_rental_agreement_document')->get();
        $ownerDocuments = $this->rentalData->table('hris_rental_owner_document')->get();

        $insertData = [];

        $agreementData = $this->rentalData->table('hris_rental_agreement')
            ->whereIn('id', $agreementDocuments->pluck('rental_agreement_id'))
            ->get()
            ->keyBy('id');

        foreach ($agreementDocuments as $agreementDocument) {
            if (isset($agreementData[$agreementDocument->rental_agreement_id])) {
                $insertData[] = [
                    'type' => $agreementDocument->document_type,
                    'image_path' => $agreementDocument->document_path,
                    'owner_id' => $agreementData[$agreementDocument->rental_agreement_id]->rental_owner_id,
                    'agreement_id' => $agreementDocument->rental_agreement_id,
                    'created_at' => $agreementDocument->created_at,
                    'updated_at' => $agreementDocument->updated_at,
                ];
            }
        }

        foreach ($ownerDocuments as $ownerDocument) {
            $insertData[] = [
                'type' => $ownerDocument->type,
                'image_path' => $ownerDocument->document_path,
                'owner_id' => $ownerDocument->rental_owner_id,
                'agreement_id' => null,
                'created_at' => $ownerDocument->created_at,
                'updated_at' => $ownerDocument->updated_at,
            ];
        }
        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    DB::table('rental_owner_documents')->insert($chunk);
                }

                DB::commit();
                dd("Documents Uploaded Successfully");
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    private function _rentalReject()
    {

        $ownerRejects = $this->rentalData->table('hris_rental_owner_reject')->get();
        $agreementRejects = $this->rentalData->table('hris_rental_agreement_reject')->get();

        $insertData = [];

        $agreementData = $this->rentalData->table('hris_rental_agreement')
            ->whereIn('id', $agreementRejects->pluck('rental_agreement_id'))
            ->get()
            ->keyBy('id');

        foreach ($agreementRejects as $agreementReject) {
            if (isset($agreementData[$agreementReject->rental_agreement_id])) {
                $insertData[] = [
                    'owner_id' => $agreementData[$agreementReject->rental_agreement_id]->rental_owner_id,
                    'agreement_id' => $agreementReject->rental_agreement_id,
                    'reason' => $agreementReject->reject_reason,
                    'rejected_by' => $this->employeeMap($agreementReject->rejected_by),
                    'created_at' => $agreementReject->created_at,
                    'updated_at' => $agreementReject->updated_at,
                ];
            }
        }

        foreach ($ownerRejects as $ownerReject) {
            $insertData[] = [
                'owner_id' => $ownerReject->rental_owner_id,
                'agreement_id' => null,
                'reason' => $ownerReject->reject_reason,
                'rejected_by' => $this->employeeMap($ownerReject->rejected_by),
                'created_at' => $ownerReject->created_at,
                'updated_at' => $ownerReject->updated_at,
            ];
        }
        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('hrm_rental_reject')->insert($data);
                    }
                }
                DB::commit();
                dd("Rental rejected inserted successfully");
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    private function _rentalIncrementDetails()
    {
        $incrementDetails = $this->rentalData->table('hris_rental_increment_detail')->get();

        $insertData = [];

        foreach ($incrementDetails as $incrementDetail) {
            $transformedData = [
                'id' => $incrementDetail->id,
                'rental_agreement_id' => $incrementDetail->rental_agreement_id,
                'increment_percent' => $incrementDetail->increment_percent,
                'increment_amount' => $incrementDetail->increment_amount,
                'increment_after' => $incrementDetail->increment_after,
                'next_increment' => $incrementDetail->next_increment,
                'created_at' => $incrementDetail->created_at,
                'updated_at' => $incrementDetail->updated_at,
                'deleted_at' => $incrementDetail->deleted_at
            ];
            $insertData[] = $transformedData;
        }
        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('rental_increment_detail')->updateOrInsert($data);
                    }
                }

                DB::commit();
                dd("Increment details data successfully inserted");
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    private function _rentalAgreementAmount()
    {
        $rentalAgreementAmounts = $this->rentalData->table('hris_rental_agreement_amount')->get();

        $insertData = [];
        $paidMapping = [
            0 => 'Due',
            2 => 'Clear',
            3 => 'Partial',
        ];


        foreach ($rentalAgreementAmounts as $rentalAgreementAmount) {
            $transformedData = [
                'id' => $rentalAgreementAmount->id,
                'rental_agreement_id' => $rentalAgreementAmount->rental_agreement_id,
                'date' => $rentalAgreementAmount->date,
                'rental_amount' => $rentalAgreementAmount->rental_amount,
                'payment_amount' => $rentalAgreementAmount->payment_amount,
                'tds_amount' => $rentalAgreementAmount->tds_amount,
                'year' => $rentalAgreementAmount->year,
                'month' => $rentalAgreementAmount->month,
                'day' => null,
                'paid_status' => $paidMapping[$rentalAgreementAmount->paid] ?? 'Due',
                'remarks' => $rentalAgreementAmount->remarks,
                'advance_due'  => $rentalAgreementAmount->advance_due,
                'previous_due' => $rentalAgreementAmount->previous_due,
                'payment_status' => $rentalAgreementAmount->payment_status,
                'bank_status' => $rentalAgreementAmount->bank_status,
                'deleted_at' => $rentalAgreementAmount->deleted_at,
                'created_at' => $rentalAgreementAmount->created_at,
                'updated_at' => $rentalAgreementAmount->updated_at,
            ];
            $insertData[] = $transformedData;
        }

        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('rental_agreement_amount')->updateOrInsert(
                            ['id' => $data['id']],
                            $data
                        );
                    }
                }
                DB::commit();
                dd("Rental Agreement Amount data successfully inserted");
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    private function _rentalElectricityBill()
    {

        $rentalElectricityBills = $this->rentalData->table('hris_rental_paybill')->get();

        $insertData = [];

        foreach ($rentalElectricityBills as $rentalElectricityBill) {
            $transformedData = [
                'id' => $rentalElectricityBill->id,
                'rental_agreement_id' => $rentalElectricityBill->rental_agreement_id,
                'bill_date' => $rentalElectricityBill->bill_date,
                'month' => $rentalElectricityBill->month,
                'year' => $rentalElectricityBill->year,
                'amount' => $rentalElectricityBill->amount,
                'previous_meter_reading' => $rentalElectricityBill->previous_meter_reading,
                'current_meter_reading' => $rentalElectricityBill->current_meter_reading,
                'previous_unit_consumed' => $rentalElectricityBill->previous_unit_consumed,
                'unit_consumed' => $rentalElectricityBill->unit_consumed,
                'unit_difference' => $rentalElectricityBill->unit_difference,
                'difference_in_percent' => $rentalElectricityBill->difference_in_percent,
                'meter_reading_img' => $rentalElectricityBill->meter_reading_img,
                'receipt_upload_image' => $rentalElectricityBill->receipt_upload_image,
                'status_req' => $rentalElectricityBill->status_req,
                'billing_month' => $rentalElectricityBill->billing_month,
                'billing_year' => $rentalElectricityBill->billing_year,
                'extra_charges' => $rentalElectricityBill->extra_charges,
                'remarks' => $rentalElectricityBill->remarks,
                'verified' => $rentalElectricityBill->verified,
                'verified_by' => $this->employeeMap($rentalElectricityBill->verified_by),
                'added_by' => $this->employeeMap($rentalElectricityBill->added_by),
                'payment_type' => $rentalElectricityBill->payment_type,
                'created_at' => $rentalElectricityBill->created_at,
                'updated_at' => $rentalElectricityBill->updated_at,
                'updated_at' => $rentalElectricityBill->deleted_at,

            ];
            $insertData[] = $transformedData;
        }

        if (!empty($insertData)) {
            $chunks = array_chunk($insertData, 500); // Split into chunks of 500 records
            DB::beginTransaction();

            try {
                foreach ($chunks as $chunk) {
                    foreach ($chunk as $data) {
                        DB::table('rental_electricity_bill')->updateOrInsert(
                            $data
                        );
                    }
                }

                DB::commit();
                dd("Electricity bills data successfully inserted");
            } catch (\Exception $e) {
                DB::rollBack();
                dd('Error: ' . $e->getMessage());
            }
        }
    }

    private function getBankIdFromName($bankName)
    {
        $bank = DB::table('bank_code')->where('name', $bankName)->first();
        return $bank ? $bank->id : null;
    }
    private function getBankCodeFromName($bankName)
    {
        $bank = DB::table('bank_code')->where('name', $bankName)->first();
        return $bank ? $bank->code : null;
    }
    private function getNewBranchId($hrmBranchId)
    {
        $branch = DB::table('branches_map')->where('hrm_id', $hrmBranchId)->first();
        return $branch ? $branch->new_id : $hrmBranchId;
    }
    private function getNewSubBranchId($hrmSubBranchId)
    {
        $subBranch = DB::table('cc_map')->where('hrm_id', $hrmSubBranchId)->first();
        return $subBranch ? $subBranch->new_id : $hrmSubBranchId;
    }
    private function employeeMap($hrmEmployeeId)
    {
        $employee = DB::table('employee_map')->where('hrm_id', $hrmEmployeeId)->first();
        return $employee ? $employee->new_id : null;
    }

    private function _supervisorUpdate()
    {
        $hrmEmployee = DB::table('employee_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $empOrg = $this->hrm->table('hris_employee_organizations')->get();
        foreach ($empOrg as $org) {
            $supervisorId = $hrmEmployee[$org->supervisor_id] ?? null;
            $empOrg = EmployeeOrg::where('iops_id', $org->iops_id)->update([
                'supervisor_id'   => $supervisorId,
            ]);
        }
        dd('done');
    }

    private function _branchManagerUpdate()
    {
        $iopsEmployee = DB::table('employee_map')->get()->pluck('new_id', 'iops_id')->toArray();
        $branches = Branch::get();
        foreach ($branches as $branch) {
            $branch->branch_manager = $iopsEmployee[$branch->branch_manager] ?? null;
            $branch->save();
        }
        dd('done');
        dd($branches);
        dd($iopsEmployee);
    }

    private function _importEmployee()
    {
        set_time_limit(0);
        $employeeMap = DB::table('employee_map')->orderBy('id', 'desc')->first();
        $from = 0;
        if ($employeeMap) {
            $from = $employeeMap->hrm_id;
        }

        $branchesMap = DB::table('branches_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $empStatusMap = DB::table('emp_status_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $ccMap = DB::table('cc_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $departmentMap = DB::table('department_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $unitMap = DB::table('unit_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        $outsourceMap = DB::table('outsource_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        // $designationMap = DB::table('designation_map')->get()->pluck('new_id','hrm_id')->toArray();
        $hrisContactRelation = $this->hrm->table('hris_contact_relations')->get()->pluck('relation', 'id')->toArray();
        $units = unit::get()->pluck('department_id', 'id')->toArray();
        $hrisEmployees = $this->hrm->table('hris_employees')->where('id', '>', $from)->get();
        $hrisEmpOrg = $this->hrm->table('hris_employee_organizations')->get()->keyBy('employee_id')->toArray();

        $checkDuplicateIops =  $this->hrm->table('hris_employee_organizations')
            ->select('iops_id', DB::raw('count(*) as total'))->groupBy('iops_id')
            ->pluck('total', 'iops_id')->toArray();


        $checkEmpCode =  $this->hrm->table('hris_employee_organizations')
            ->select(DB::raw('UPPER(trim(emp_code)) as emp_code'), DB::raw('count(*) as total'))->groupBy('emp_code')
            ->pluck('total', 'emp_code')->toArray();
        $iopsEmp = $this->roiopsdb->table('employee')->get()->keyBy('employee_id')->toArray();
        foreach ($hrisEmployees as $key => $employee) {
            $empOrg = $hrisEmpOrg[$employee->id] ?? null;

            if (!$empOrg) {
                continue;
            }
            $iopsDetails = $iopsEmp[$empOrg->iops_id] ?? null;
            if (!$iopsDetails) {
                continue;
            }


            $username = $iopsDetails->username;
            if ($checkDuplicateIops[$empOrg->iops_id] > 1) {
                $username = $username . '_mul_' . $employee->id;
            }

            $userArray = [
                'username'  => $username,
                'password'  => $iopsDetails->password,
            ];

            $relation = $hrisContactRelation[$employee->contact_relation_id] ?? null;
            $empArray = [
                'first_name'        => $employee->first_name,
                'middle_name'       => $employee->middle_name,
                'last_name'         => $employee->last_name,
                'gender'            => $employee->gender,
                'dob'               => ($employee->dob == '0000-00-00') ? null : $employee->dob,
                'dob_nepali'        => $employee->dob_nepali,
                'mstat'             => $employee->mstat,
                'phone'             => $employee->cell,
                'email'             => null,
                'nationality'       => $employee->nationality,
                'citizenship'       => $employee->citizenship,
                'permanent_address' => $employee->permanent_address,
                'temporary_address' => $employee->temporary_address,
                'father'            => $employee->father_name,
                'mother'            => $employee->mother_name,
                'grandfather'       => $employee->grandfather_name,
                'spouse'            => ($relation == 'Spouse') ? $employee->contact_name : null,
                'contact_person'    => $employee->contact_name,
                'contact_phone'     => $employee->contact_cell,
                'contact_relation'  => $relation,
                'company_id'        => 1
            ];
            $unitId = $unitMap[$empOrg->unit_id] ?? null;
            if ($unitId) {
                $empArray['unit_id'] = $unitId;
                $departmentId = $units[$unitId] ?? null;
            } else {
                $departmentId = null;
            }
            $biometricId = preg_replace('/[^0-9]/', '', $empOrg->emp_code);
            $biometricId = $biometricId ? $biometricId : null;
            $employeeCode = trim(strtoupper($empOrg->emp_code));
            $empCode = $biometricId;
            // if(strtoupper($empOrg->emp_code) == 'NA' || strtoupper($empOrg->emp_code) == 'N\A' || strtoupper($empOrg->emp_code) == 'N/A' || strtoupper($empOrg->emp_code) == 'VIA-0000'){
            //     $empCode = $empOrg->emp_code.'-'.$key;
            // }
            if ($checkEmpCode[$employeeCode] > 1) {
                $empCode .= '-mul-' . $employee->id;
            } else {
                $checkOrg = EmployeeOrg::where('employee_code', $empCode)->first();
                if ($checkOrg) {
                    $empCode .= '-mul-' . $employee->id;
                }
            }
            if (!$empCode) {
                $empCode .= 'N/A-' . $employee->id;
            }
            $companyId = 1;
            if (str_contains($employeeCode, 'MS')) {
                $companyId = 3;
            } else if (str_contains($employeeCode, 'VSF')) {
                $companyId = 2;
            } else if (str_contains($employeeCode, 'KNT')) {
                $companyId = 4;
            }
            $empArray['company_id'] = $companyId;

            $empOrgArray = [
                'employee_code'             => $empCode,
                'email'                     => $empOrg->email,
                'branch_id'                 => $branchesMap[$empOrg->branch_id] ?? null,
                'sub_branch_id'             => $ccMap[$empOrg->collection_center_id] ?? null,
                'unit_id'                   => $unitId,
                'department_id'             => $departmentId,
                'outsource_company_id'      => $outsourceMap[$empOrg->outsource_company_id] ?? null,
                'employee_status_id'        => $empStatusMap[$empOrg->employee_status_id] ?? null,
                'doj'                       => ($empOrg->joined_date == '0000-00-00') ? null : $empOrg->joined_date,
                'doj_inhouse'               => ($empOrg->inhouse_joined_date == '0000-00-00') ? null : $empOrg->inhouse_joined_date,
                'supervisor_id'             => null,
                'bank'                      => $empOrg->bank,
                'bank_account_no'           => $empOrg->bank_account,
                'cug'                       => $empOrg->cug_no,
                'rf_no'                     => $empOrg->ssf_no,
                'cit_no'                    => $empOrg->cit_no,
                'biometric_id'              => $biometricId,
                'pan'                       => $empOrg->pan_no,
                'termination_request_date'  => ($empOrg->termination_requested_date == '0000-00-00') ? null : $empOrg->termination_requested_date,
                'termination_date'          => ($empOrg->termination_date == '0000-00-00') ? null : $empOrg->termination_date,
                'termination_reason'        => $empOrg->termination_reason,
                'designation_id'            =>  null,
                'iops_id'                   =>  $empOrg->iops_id,
                'shift_id'                  =>  1,
            ];
            if ($empArray['mstat'] == '' || $empArray['mstat'] == 'Other') {
                $empArray['mstat'] = null;
            }
            DB::beginTransaction();

            $insertUser = User::create($userArray);
            $empArray['user_id'] = $insertUser->id;
            $insertEmployee = Employee::create($empArray);
            $empOrgArray['employee_id'] = $insertEmployee->id;
            $insertEmpOrg = EmployeeOrg::create($empOrgArray);
            $mapArray = [
                'iops_id'       => $empOrg->iops_id,
                'new_id'        => $insertEmployee->id,
                'hrm_id'        => $employee->id,
            ];
            $inserMap = DB::table('employee_map')->insert($mapArray);
            DB::commit();
        }

        dd('done');
    }


    private function _importOutsource()
    {
        $outsources = $this->hrm->table('hris_outsource_company')->get();
        foreach ($outsources as $outsource) {
            $array = [
                'name'                  => $outsource->company_name,
                'address'               => null,
                'phone'                 => null,
                'pan'                   => null,
                'status'                => 1,
                'company_id'            => 1,
            ];

            $insertOutsource = OutsourceCompany::create($array);
            $mapArray['new_id'] = $insertOutsource->id;
            $mapArray['hrm_id'] = $outsource->id;
            $inserMap = DB::table('outsource_map')->insert($mapArray);
        }
        dd('completed');
    }

    private function _importDesignation()
    {
        $designations = $this->hrm->table('hris_grades')->get();
        foreach ($designations as $designation) {
            $array = [
                'title'             => $designation->title,
                'description'       => "N/A",
                'level'             => 1,
                'role'              => 'employee'
            ];

            $insertDesignation = Designation::create($array);
            $mapArray['new_id'] = $insertDesignation->id;
            $mapArray['hrm_id'] = $designation->id;
            $inserMap = DB::table('designation_map')->insert($mapArray);
        }
        dd('done');
    }

    private function _importShift()
    {
        dd('aaa');
    }

    private function _importUnit()
    {
        $units = $this->hrm->table('hris_units')->get();
        $departmentMaps = DB::table('department_map')->get()->pluck('new_id', 'hrm_id')->toArray();
        foreach ($units as $unit) {
            $array = [
                'name'          => $unit->title,
                'remarks'       => $unit->description,
                'department_id' => $departmentMaps[$unit->department_id] ?? null
            ];

            $insertUnit = Unit::create($array);
            $mapArray['new_id'] = $insertUnit->id;
            $mapArray['hrm_id'] = $unit->id;
            $inserMap = DB::table('unit_map')->insert($mapArray);
        }
        dd('done');
    }

    private function _importDepartment()
    {
        $departments = $this->hrm->table('hris_departments')->get();
        foreach ($departments as $key => $department) {
            $array = [
                'name'          => $department->department,
                'abbreviation'  => $key,
                'remarks'       => 'N/A',
                'need_unit'     => 1,
                'company_id'    => 1
            ];

            $insertDepartment = Department::create($array);
            $mapArray['new_id'] = $insertDepartment->id;
            $mapArray['hrm_id'] = $department->id;
            $inserMap = DB::table('department_map')->insert($mapArray);
        }
        dd('done');
    }

    private function _importBranches()
    {
        $branchMap = $this->hrm->table('hris_branch_map')->get();
        // $hrmBranches = $this->hrm->table('hris_branches')->get()->keyBy('id')->toArray();
        $iopsBranches = $this->roiopsdb->table('branches')->get()->toArray();
        foreach ($iopsBranches as $key => $branch) {
            $mapId = $branchMap->where('old_id', $branch->id)->first();
            $mapArray = [];
            if ($mapId) {
                $array = (array)$branch;
                unset($array['id']);
                $array['latitude'] = $array['lattitude'] ? $array['lattitude'] : "0";
                $array['longitude'] = $array['longitude'] ? $array['longitude'] : "0";
                $array['company_id'] = 1;
                $array['need_sub_branch'] = 1;
                $insertBranch = Branch::create($array);
                $mapArray['new_id'] = $insertBranch->id;
                $mapArray['hrm_id'] = $mapId->new_id;
                $mapArray['iops_id'] = $mapId->old_id;
                $insertBranchMap = DB::table('branches_map')->insert($mapArray);
            } else {
                dump('Branch Not found in HRM');
                dd($branch);
            }
        }
        dd('Completed');
    }

    private function _importCollectionCenter()
    {
        $branchesMap = DB::table('branches_map')->get()->pluck('new_id', 'iops_id')->toArray();
        $ccMap = $this->hrm->table('hris_collection_center_map')->get();
        // $hrmCollectionCenter = $this->hrm->table('hris_collection_centers')->get()->keyBy('id')->toArray();
        $iopsCC = $this->roiopsdb->table('collection_centers')->get()->toArray();
        foreach ($iopsCC as $key => $cc) {
            $mapId = $ccMap->where('old_id', $cc->id)->first();
            $mapArray = [];
            // if($mapId){
            $array = (array)$cc;
            unset($array['id']);
            $array['branch_id'] = $branchesMap[$array['branch_id']];
            $array['latitude'] = $array['lattitude'];
            unset($array['lattitude']);
            unset($array['billing_info_id']);
            unset($array['reseller_id']);
            unset($array['master_oc']);
            // dd($array);
            $insertCC = SubBranch::create($array);
            $mapArray['new_id'] = $insertCC->id;
            $mapArray['hrm_id'] = $mapId->new_id ?? null;
            $mapArray['iops_id'] = $mapId->old_id ?? $cc->id;
            $insertCCMap = DB::table('cc_map')->insert($mapArray);
            // }else{
            //     dump('collection center Not found in HRM');
            //     dd($cc);
            // }
        }
    }

    private function _importEmpStatus()
    {
        $empStatus = $this->hrm->table('hris_employee_status')->get();
        foreach ($empStatus as $key => $status) {
            $array = (array)$status;
            unset($array['id']);
            $array['is_active'] = 1;
            $array['remarks'] = 'N/A';
            $insertStatus = EmpStatus::create($array);

            $mapArray = [];
            $mapArray['new_id'] = $insertStatus->id;
            $mapArray['hrm_id'] = $status->id;
            $insertStatusMap = DB::table('emp_status_map')->insert($mapArray);
        }
        dd('done');
    }

    public function cugDataImport()
    {
        try {
            DB::beginTransaction();
            $cugEmployeesNcell = $this->hrm->table('hris_cug_employees')
                ->select("id", "mobile as mobile_number", DB::raw("'2' as provider_id"), "plan as plan_id", "previous_plan as previous_plan_id", "employee_id", "branch_id", "instock_department as in_stock_department", "status", "created_at", "updated_at", "deleted_at",)
                //                ->where("provider", "Ncell")
                ->whereIn("mobile", [
                    //Vianet Employee's CUG provided by Rajeeb Dai
                    //                    **********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,**********,9802341967,9801915386,9801904289,9802353472,9802353473,9802358490,9802358494,9802358493,9802365619,9802365636,9802365637,9802358491,9820109310,9801979406,9801906897,9820109302,9802341988,9802341989,9801131468,9820109305,9802342529,9801131466,9801236307,9801194918,9801227409,9801906707,9801227269,9801916803,9801227408,9801906730,9801906708,9801906706,9802342061,9802342062,9802358564,9801979856,9801194940,9801979367,9801906780,9801227315,9801236511,9801979369,9802342651,9801199498,9801199519,9801227399,9801193922,9801202079,9801202110,9801195240,9801195877,9801979365,9802353173,9802347261,9802347263,9801227467,9802351716,9802365598,9801911125,9801911129,9801911128,9801916946,9801904639,9801222215,9801348422,9801906863,9802347456,9802358863,9802325794,9802320791,9801204540,9801906739,9802365082,9801131492,9820109291,9801143325,9801143327,9801143712,9820109271,9802325683,9802355646,9801903806,9801902019,9801903802,9801904012,9802315217,9801904010,9801903804,9802364917,9802364915,9802363783,9801911108,9802342571,9801143329,9801916836,9801093598,9801916864,9801093657,9801979380,9801916849,9801195880,9802353458,9802355637,9801131469,9801131467,9801906749,9820118813,9820118814,9802353468,9820109319,9820109318,9820109290,9820109317,9801914933,9820109315,9802351718,9802347463,9802347462,9802347464,9802347465,9802347466,9801227250,9801048529,9801195866,9801166740,9801906779,9801195766,9801195353,9801227444,9801906833,9801025445,9801907434,9801903889,9801194902,9801906881,9801913883,9801048534,9801194933,9801236362,9801194906,9801975595,9801907454,9801227319,9801907451,9801906887,9801194916,9801236571,9801979404,9801194914,9801227369,9801906830,9801195834,9801127845,9801976396,9801110432,9801110436,9801979383,9801975597,9801196161,9801979385,9801913882,9801011753,9801914019,9801979403,9801237417,9801906832,9801232289,9801904015,9801915950,9801194946,9801195865,9801915396,9801915999,9801194905,9801906883,9801906740,9801975356,9801194900,9801907425,9801976356,9801137721,9801227382,9801195887,9801914071,9801196059,9801913098,9801166679,9801195788,9801979210,9801195833,9801227400,9801904739,9801978848,9801195760,9801914000,9801227263,9801227484,9801906705,9801915594,9801110561,9801976357,9801032444,9801904016,9801195235,9801906834,9801195781,9802309397,9801194943,9801195869,9801906885,9801979402,9801195755,9801195236,9801915944,9801227313,9801906831,9801236574,9801973825,9801975217,9801915398,9801915576,9801916948,9802325518,9801917674,9801917682,9802341990,9802341997,9801978769,9801195851,9801194919,9802342534,9801195237,9801227441,9820118815,9802342553,9801904014,9801227439,9802323868,9801915563,9801856787,9801857453,9801236579,9801979378,9801194904,9801196380,9820109295,9801227386,9801906818,9802346635,9802346674,9802347451,9802347452,9802305887,9802349576,9802349683,9802349577,9802346513,9801236548,9802353476,9802363793,9802358472,9802365086,9802347759,9801035947,9801059844,9802369801,9802369802,9801916945,9801236345,9801236516,9801979408,9801916930,9802342544,9801913878,9801975334,9802353391,9802353471,9802353390,9801196343,9801970984,9801910492,9802349879,9801903908,9801119712,9801094192,9801119138,9801975329,9801967430,9801967428,9801975328,9801970982,9801979857,9802342527,9801903906,9801910495,9802363778,9802363779,9802358569,9802342523,9802365599,9801979415,9801979379,9801979420,9801911396,9801967407,9801975351,9820109301,9801979362,9802353455,9802353456,9802365917,9802365918,9801166739,9801196098,9801906853,9801195765,9801194909,9801222211,9801975596,9801975589,9801856975,9801856945,9801856960,9801236549,9801905276,9801908896,9801919226,9801236497,9801227420,9801227440,9801194287,9801975587,9802342538,9801975583,9801236470,9802352737,9802352736,9802352735,9802352734,9802347753,9802353452,9802353451,9802364950,9802364952,9802364953,9802364954,9802364955,9802364951,9801195420,9801979372,9801979375,9820109329,9801979374,9801188605,9801110298,9801195878,9801195751,9802356839,9801975354,9801227276,9801195274,9801195764,9801235768,9801236354,9802342641,9801227447,9801236570,9802342654,9802353475,9802365044,9802365043,9801914967,9801914987,9801906898,9801979336,9801915285,9801914938,9801914935,9801119678,9801119676,9802341969,9801119079,9802365058,9802365057,9820109322,9820109325,9802306138,9801906741,9801119309,9801907433,9801195754,9801048520,9801194944,9801195849,9802309066,9801110490,9801195886,9801202115,9801166726,9801194908,9801907022,9801195761,9820109328,9802341994,9801194947,9802346675,9820109323,9802349682,9801228143,9801906738,9802349681,9801119287,9801195909,9802353383,9801236481,9802309399,9801979435,9802365608,9801236487,9801329494,9802369841,9802369846,9802369842,9802369843,9802369844,9802369845,9802479701,9801329492,9801917673,9801329496,9801973158,9801201918,9801227343,9801227383,9801227388,9801911135,9801911133,9802353380,9820118816,9802341982,9802341981,9802341974,9801227464,9801978863,9802341976,9802341978,9801914960,9801979437,9801976679,9801979851,9801978006,9801857043,9801194912,9801188606,9802358474,9802365126,9802365128,9802365616,9801975378,9801975421,9801976397,9801110487,9801911123,9801911122,9801911121,9801905932,9801907447,9801236483,9801166725,9801906822,9801195717,9801193840,9801907426,9801906777,9801195317,9801195410,9801198383,9801196095,9801194942,9801195417,9801907446,9802353179,9802365584,9802349186,9802365586,9802365583,9802365589,9802365587,9802342065,9802356650,9802364574,9802368078,9802368067,9801907460,9801976398,9801911137,9801911138,9801236394,9801236463,9801904179,9801236473,9801975218,9801902998,9801904657,9801916879,9801979832,9801904176,9801976399,9801904265,9801911131,9802323862,9802365037,9802365038,9802365265,9802365264,9820109308,9820109297,9820109306,9802342547,9801906890,9801906709,9820109296,9801194931,9801977197,9801906703,9802342579,9802342582,9802342580,9802342577,9802342573,9820109279,9802342520,9801914945,9820109278,9820109275,9820109314,9802342511,9801857298,9801857312,9820109277,9820109274,9801906736,9802345465,9802364926,9802364929,9802364927,9802364928,9801222217,9801911130,9801906862,9801911127,9801904648,9801210937,9802353470,9802353469,9801195899,9802309377,9801227423,9801194911,9802342548,9801906878,9820109282,9820109276,9820109286,9820109288,9820109321,9820109272,9801977978,9802355645,9802372721,9802365617,9802365919,9802342063,9801195422,9801906759,9802353467,9802353466,9802353465,9802353463,9802353462,9802353464,**********,9801978600,9801979896,9801979410,9801131445,9801979897,9801227465,9802342653,9802365597,9802349185,9802341972,9801906835,9801906726,9801119714,9801227637,9801227404,9802353462,9801131433,9801195313,9801227270,9802353466,9802353467,9801979382,9801195862,9801906867,9801195785,**********,9801247915,9801247916,9801247917,9801247918,9801247919,9801247920,9801247921,9801247922,9801247923,9801247925,9801247926,9801247927,9801247928,9801247929,9801247930,9801247931,9801247932,9801247933,9801247934,9801247935,9801247936,9801247937,9801247938,9801247939,9801247940,9801247941,9801247942,9801247943,9801247944,9801247945,9801247946,9801247947,9801247948,9801237578,9801237579,9801237580,9801237581,9801237582,9801237583,9801237584,9801237585,9801237586,9801237587,9801237588,9801237589,9801237590,9801237591,9802303156,9802303157,9802303158,9802303159,9802303160,9802303161,9802303162,9802303163,9802303164,9802303165,9802303166,9802303167,9802303168,9802303169,9802303192,9802303193,9802303151,9802378040,9802378041,9802378042,9802378043,9802378044,9802378045,9802378046,9802378047,9802378048,9802378049,9802378050,9802378051,9802378052,9802378053,9802378054,9802378055,9802378056,9802378057,9802378058,9802378059,9802378060,9802378061,9802378062
                    //Konnect Employee's CUG provided by Sabeen Ji
                    9801227296,
                    9801906719,
                    9802333806,
                    9801906715,
                    9801195327,
                    9801235748,
                    9801196344,
                    9820118820,
                    9801907025,
                    9801195787,
                    9801202082,
                    9820109326,
                    9801194293,
                    9801194289,
                    9801119253,
                    9801195848,
                    9820118818,
                    9801196254,
                    9801906791,
                    9801195892,
                    9801227389,
                    9801194291,
                    9801913099,
                    9801907020,
                    9801907026,
                    9802327821,
                    9801906819,
                    9801904011,
                    9820118824,
                    9820118826,
                    9820118825,
                    9820118823,
                    9801048510,
                    9801110430,
                    9801166706,
                    9801166736,
                    9801166737,
                    9801195318,
                    9801195320,
                    9801195321,
                    9801195426,
                    9801195867,
                    9801196056,
                    9801196363,
                    9801196368,
                    9801227254,
                    9801227273,
                    9801227274,
                    9801119715,
                    9801227314,
                    9801227474,
                    9801228124,
                    9801228126,
                    9801906788,
                    9801906810,
                    9801906854,
                    9801907047,
                    9801910497,
                    9801910498,
                    9801907455,
                    9801907429,
                    9801902727,
                    9801908899,
                    9801193928,
                    9801193929,
                    9801193931,
                    9801193941,
                    9801193944,
                    9801193973,
                    9801193946,
                    9801193947,
                    9801193977,
                    9801193948,
                    9801195316,
                    9801227300,
                    9802341995,
                    9802341996,
                    9801227381,
                    9801915249,
                    9801905685,
                    9801110263,
                    9801913869,
                    9801915391,
                    9801907031,
                    9801907444,
                    9801193829,
                    9801193834,
                    9801193836,
                    9801193837,
                    9801222213,
                    9801907448,
                    9801907445,
                    9801228797,
                    9801110273,
                    9801907034,
                    9801227432,
                    9801110271,
                    9801196068,
                    9801227299,
                    9801196004,
                    9801227429,
                    9801195855,
                    9801227437,
                    9801230774,
                    9801906851,
                    9801195416,
                    9801227435,
                    9801195238,
                    9801194910,
                    9801202116,
                    9801906849,
                    9801236479,
                    9801910499,
                    9801907033,
                    9801196381,
                    9801193859,
                    9801227428,
                    9801227384,
                    9801907427,
                    9801196024,
                    9801904712,
                    9801196349,
                    9801904713,
                    9801227277,
                    9801906857,
                    9801910496,
                    9801227317,
                    9801227253,
                    9801906856,
                    9801906807,
                    9801048537,
                    9801193885,
                    9801193886,
                    9801906893,
                    9801906701,
                    9801227402,
                    9801904287,
                    9801227636,
                    9801226978,
                    9801227639,
                    9801227635,
                    9801227407,
                    9801907032,
                    9801903792,
                    9801973062,
                    9801973078,
                    9801236447,
                    9801236373,
                    9802309396,
                    9801222587,
                    9801222568,
                    9801227410,
                    9801905294,
                    9801905295,
                    9801166728,
                    9801906729,
                    9801906825,
                    9802341992,
                    9802341993,
                    9801906711,
                    9801906826,
                    9801979881,
                    9801906858,
                    9801906768,
                    9801905296,
                    9801906720,
                    9801906767,
                    9820109298,
                    9820109300,
                    9801907441,
                    9801906712,
                    9801907439,
                    9801906762,
                    9801907438,
                    9801908884,
                    9801908885,
                    9801906756,
                    9801906754,
                    9801195853,
                    9801906716,
                    9802341983,
                    9802341984,
                    9801906827,
                    9820118808,
                    9820118810,
                    9801904653,
                    9801904652,
                    9801904656,
                    9801904651,
                    9801916912,
                    9801907458,
                    9801904266,
                    9801902068,
                    9801903791,
                    9801911104,
                    9801903790,
                    9801904655,
                    9801195871,
                    9801904711,
                    9801195322,
                    9801911136,
                    9801907457,
                    9801907462,
                    9820109284,
                    9820109287,
                    9801857028,
                    9820109283,
                    9801907453,
                    9801973089,
                    9801329495,
                    9801916838,
                    9801915218,
                    9802309374,
                    9801916937,
                    9801227433,
                    9801226975,
                    9801907042,
                    9801906869,
                    9801906774,
                    9801906772,
                    9801979846,
                    9801048508,
                    9801906758,
                    9801979407,
                    9801906866,
                    9801979835,
                    9801906868,
                    9801975343,
                    9801978071,
                    9802342545,
                    9820118811,
                    9802305886,
                    9802309068,
                    9801196080,
                    9802315218,
                    9801907044,
                    9801903896,
                    9801195858,
                    9801903803,
                    9801193882,
                    9801193867,
                    9801193863,
                    9801903800,
                    9802341979,
                    9802341975,
                    9802341980,
                    9801906752,
                    9801906751,
                    9801905298,
                    9801906859,
                    9801236547,
                    9801131491,
                    9801977612,
                    9802309379,
                    9801131476,
                    9801166734,
                    9801110256,
                    9801193914,
                    9801906731,
                    9801202124,
                    9801196345,
                    9801227397,
                    9801208815,
                    9801906875,
                    9801193916,
                    9801166730,
                    9801193917,
                    9801193924,
                    9801906873,
                    9801979866,
                    9801907037,
                    9801110283,
                    9801195767,
                    9801907041,
                    9801906872,
                    9801110243,
                    9801195319,
                    9801907035,
                    9801907435,
                    9801194938,
                    9801110242,
                    9801979370,
                    9801227325,
                    9801143718,
                    9801979366,
                    9801220003,
                    9801196346,
                    9801906828,
                    9801906814,
                    9801195304,
                    9801222219,
                    9801222584,
                    9801904647,
                    9802342535,
                    9801236374,
                    9801236390,
                    9801906865,
                    9801916921,
                    9801911126,
                    9801236569,
                    9801906801,
                    9801916813,
                    9802309378,
                    9802342489,
                    9801348423,
                    9801906815,
                    9802309375,
                    9801906871,
                    9802342541,
                    9801977974,
                    9802309376,
                    9801131478,
                    9801979883,
                    9801143714,
                    9801131477,
                    9801916804,
                    9801916809,
                    9801119714,
                    9801119719,
                    9820109313,
                    9801857305,
                    9801915284,
                    9801857143,
                    9801110286,
                    9801910494,
                    9801910493,
                    9801910490,
                    9801903878,
                    9801903909,
                    9801226973,
                    9801906755,
                    9801143715,
                    9801143716,
                    9802315219,
                    9802315220,
                    9802315221,
                    9801903879,
                    9801188602,
                    9801188604,
                    9801979376,
                    9801967429,
                    9801967424,
                    9802342528,
                    9801119158,
                    9802342526,
                    9802342524,
                    9801975297,
                    9801232287,
                    9801979419,
                    9801979361,
                    9801979389,
                    9801914979,
                    9801978866,
                    9801914976,
                    9801094287,
                    9801857088,
                    9801914986,
                    9801857370,
                    9801857326,
                    9801227442,
                    9801914936,
                    9801914959,
                    9801914972,
                    9801979358,
                    9801119673,
                    9801904269,
                    9801907463,
                    9801131453,
                    9801131454,
                    9801979438,
                    9802341987,
                    9802342581,
                    9802342575,
                    9802342574,
                    9820109280,
                    9802342512,
                    9802342515,
                    9802342516,
                    9802342517,
                    9802342518,
                    9801196340,
                    9801048533,
                    9801194927,
                    9801202113,
                    9801110239,
                    9801110245,
                    9801195789,
                    9801110289,
                    9801907437,
                    9801048507,
                    9801110479,
                    9801194928,
                    9801110235,
                    9801110246,
                    9801906846,
                    9801110296,
                    9801907049,
                    9802342554,
                    9801110231,
                    9801193909,
                    9801202123,
                    9801906894,
                    9801906783,
                    9802305888,
                    9801906844,
                    9801906888,
                    9801232279,
                    9801905285,
                    9801228137,
                    9801193981,
                    9801906770,
                    9801907023,
                    9801979363,
                    9801202107,
                    9801857014,
                    9801199518,
                    9801906743,
                    9801856925,
                    9801905283,
                    9801856992,
                    9801857022,
                    9801194924,
                    9801906843,
                    9801236397,
                    9801227418,
                    9801906786,
                    9801193978,
                    9802342546,
                    9801906845,
                    9801906886,
                    9801227385,
                    9801227472,
                    9801975586,
                    9801856920,
                    9801227412,
                    9801202118,
                    9801201720,
                    9801910529,
                    9801222569,
                    9801906795,
                    9801904409,
                    9801227425,
                    9801199516,
                    9801227251,
                    9801195411,
                    9801227298,
                    9801195425,
                    9801195894,
                    9801906797,
                    9801906879,
                    9801110437,
                    9801911139,
                    9801195429,
                    9801906760,
                    9801110474,
                    9801904715,
                    9801228135,
                    9801110294,
                    9801110471,
                    9801228134,
                    9801904407,
                    9801906789,
                    9801195239,
                    9801906895,
                    9801906896,
                    9801202114,
                    9801195859,
                    9801195424,
                    9801904408,
                    9801110439,
                    9820109311,
                    9801979381,
                    9801227424,
                    9801110562,
                    9801911349,
                    9801906761,
                    9801911389,
                    9801978614,
                    9801195861,
                    9801906899,
                    9801975358,
                    9801196069,
                    9801979421,
                    9801119209,
                    9801236492,
                    9801196376,
                    9801979422,
                    9801906764,
                    9801202106,
                    9801977962,
                    9802360826,
                    9801906775,
                    9801228144,
                    9801228121,
                    9820118817,
                    9801110472,
                    9801915951,
                    9801905274,
                    9801979417,
                    9801979411,
                    9820109307,
                    9801202105,
                    9801195763,
                    9801978812,
                    9802341966,
                    9801916832,
                    9801193864,
                    9801979387,
                    9801979360,
                    9805675689,
                    9801131490,
                    9801236185,
                    9801195323,
                    9801194334,
                    9801906769,
                    9820118809,
                    9802342556,
                    9802342557,
                    9801110489,
                    9801195898,
                    9801227452,
                    9820109289,
                    9802347454,
                    9802342519,
                    9820109299,
                    9820109316,
                    9801227434,
                    9801195242,
                    9820118806,
                    9802353172,
                    9802353170,
                    9802353171,
                    9801979211,
                    9801916903,
                    9801195885,
                    9802353174,
                    9802352738,
                    9802353175,
                    9802353176,
                    9802353177,
                    9802353178,
                    9802353464,
                    9802353385,
                    9802353474,
                    9802353388,
                    9802353381,
                    9802342514,
                    9801329493,
                    9802353450,
                    9820109303,
                    9802353453,
                    9802341998,
                    9802364916,
                    9802363782,
                    9801906870,
                    9802358473,
                    9802365129,
                    9802358562,
                    9802358470,
                    9802357582,
                    9802345464,
                    9802345463,
                    9802365056,
                    9802365084,
                    9802364897,
                    9802364896,
                    9802365085,
                    9802365083,
                    9802364898,
                    9802365618,
                    9801131479,
                    9802365596,
                    9802365609,
                    9802364573,
                    9801975469,
                    9801195252,
                    9802365916,
                    9802365915,
                    9820109294,
                    9801110287,
                    9704540369,
                    9802061608,
                    9704540361,
                    9704540362,
                    9704540363,
                    9704540366,
                    9704540367,
                    9704540368,
                    9704540364,
                    9704540360,
                    9704593401,
                    9704593402,
                    9802372021,
                    9802372023,
                    9802370014,
                    9802370051,
                    9801064423,
                    9802370052,
                    9801919080,
                    9802371885,
                    9802372569,
                    9802373017,
                    9802373018,
                    9820109281,
                    9801193889,
                    9801196347,
                    9801143719,
                    9801903795,
                    9802372576,
                    9801227270,
                    9801979435,
                    9802373019,
                    9802375273,
                    9802353412,
                    9802347466,
                    9801967407,
                    9802369834,
                    9801195274,
                    9801193227,
                    9802372022,
                    9802372021,
                    9801911130,
                    9802347464,
                    9802320790,
                    9802352959,
                    9801236453,
                    9802352970,
                    9801227326
                ])
                ->whereNull("deleted_at")
                ->orderBy("id", "asc")
                ->get();

            $cugEmployeesNcell = $cugEmployeesNcell->map(function ($item) {
                return (array) $item;
            })->toArray();
            foreach ($cugEmployeesNcell as $key => $cugEmployeeNcell) {
                $status = "in_stock";

                $cugEmployeesNcell[$key]['employee_id'] = $this->fetchEmployeeId($cugEmployeeNcell['employee_id']);
                $cugEmployeesNcell[$key]['branch_id'] = $this->fetchBranchId($cugEmployeeNcell['branch_id']);
                $cugEmployeesNcell[$key]['in_stock_department'] = $this->fetchDepartment($cugEmployeeNcell['in_stock_department']);

                if (isset($cugEmployeesNcell[$key]['employee_id']) && $cugEmployeesNcell[$key]['branch_id']) {
                    $status = "assigned";
                } else if ($cugEmployeesNcell[$key]['branch_id']) {
                    $status = "available";
                }
                $cugEmployeesNcell[$key]['status'] = $status;
            }

            foreach ($cugEmployeesNcell as $cugEmployeeNcell) {
                DB::table("cug_management_details")
                    ->insert($cugEmployeeNcell);
            }
            DB::commit();
            return "CUG Data imported successfully";
        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }
    }

    private function fetchEmployeeId($employeeId)
    {
        return DB::table('employee_map')
            ->where('hrm_id', $employeeId)
            ->pluck("new_id")
            ->first();
    }

    private function fetchBranchId($branchId)
    {
        return DB::table('branches_map')
            ->where('hrm_id', $branchId)
            ->pluck("new_id")
            ->first();
    }

    private function fetchDepartment($departmentId)
    {
        return DB::table('department_map')
            ->where('hrm_id', $departmentId)
            ->pluck("new_id")
            ->first();
    }

    public function cugPlanChangeRequestImport()
    {
        try {
            DB::beginTransaction();
            $cugEmployees = DB::table("cug_management_details")
                ->pluck("id")
                ->toArray();

            $cugPlanChangeRequest = $this->hrm->table("hris_cug_plan_change_requests")
                ->select(
                    "id",
                    "cug_id as detail_id",
                    "branch_id",
                    "requested_for",
                    "previous_plan_type as current_plan_id",
                    "change_plan_type as requested_plan_id",
                    "requested_by as employee_id",
                    "remarks",
                    DB::raw("'cug_management_plan_change_request' as workflow"),
                    DB::raw("CASE
                                                WHEN change_status = 1 THEN 'Approved'
                                                ELSE 'Submitted'
                                             END as state"),
                    "created_at",
                    "updated_at",
                    "deleted_at"
                )
                ->whereIn("cug_id", $cugEmployees)
                ->get();

            $cugPlanChangeRequest = $cugPlanChangeRequest->map(function ($item) {
                return (array) $item;
            })->toArray();

            foreach ($cugPlanChangeRequest as $key => $cugPlanChangeReq) {
                $cugPlanChangeRequest[$key]['branch_id'] = $this->fetchBranchId($cugPlanChangeReq['branch_id']);
                $cugPlanChangeRequest[$key]['requested_for'] = $this->fetchEmployeeId($cugPlanChangeReq['requested_for']);
                $cugPlanChangeRequest[$key]['employee_id'] = $this->fetchEmployeeId($cugPlanChangeReq['employee_id']);

                if ($cugPlanChangeRequest[$key]['requested_for'] == null) {
                    Log::info("Empty Requested for. Details => " . print_r($cugPlanChangeReq, true));
                    unset($cugPlanChangeRequest[$key]);
                    continue;
                }

                if ($cugPlanChangeRequest[$key]['employee_id'] == null) {
                    Log::info("Empty Employee Id. Details => " . print_r($cugPlanChangeReq, true));
                    unset($cugPlanChangeRequest[$key]);
                }
            }

            foreach ($cugPlanChangeRequest as $cugPlanChangeReq) {
                DB::table("cug_management_plan_change_requests")
                    ->insert($cugPlanChangeReq);
            }
            DB::commit();
            return "CUG Plan Change Request Data imported successfully";
        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }
    }
}
