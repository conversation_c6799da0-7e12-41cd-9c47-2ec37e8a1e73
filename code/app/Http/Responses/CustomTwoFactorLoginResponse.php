<?php

namespace App\Http\Responses;

use App\Http\Helpers\Constant;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Lara<PERSON>\Fortify\Contracts\TwoFactorLoginResponse as TwoFactorLoginResponseContract;

class CustomTwoFactorLoginResponse implements TwoFactorLoginResponseContract
{
    public function toResponse($request)
    {
       // Your custom logic here
        // For example, logging the login attempt
        Log::info('User logged in with 2FA', ['user_id' => $request->user()->id]);
        $user = Auth::user();

        // Update last_login column with current date and time
        $user->update(['last_login' => date("Y-m-d H:i:s")]);
        if (!$user->password_change_date) {
            session([
                Constant::SESSION_PASSWORD_CHANGE_TIME => true
            ]);
        }
        Session::put('user_id', $user->id);
        session([
            Constant::SESSION_USER_ID => $user->id,
            Constant::SESSION_EMPLOYEE_ID => $user->employee?->id,
            Constant::SESSION_COMPANY => $user->employee?->company ?? \App\Models\configs\Company::where("is_parent", "Y")->first(),
            Constant::SESSION_CURRENT_FISCAL_YEAR => \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first(),
            Constant::SESSION_EMPLOYEE => $user->employee,
            Constant::SESSION_IOPS_ID => $user->employee?->organizationInfo->iops_id ?? null,
        ]);

        // Redirect the user to their intended location
        return redirect()->intended(config('fortify.home'));
    }
}
