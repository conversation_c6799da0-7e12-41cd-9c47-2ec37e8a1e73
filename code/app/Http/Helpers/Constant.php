<?php

namespace App\Http\Helpers;

class Constant
{
  public const COMPANY = "company";
  public const REGION = "region";
  public const BRANCH = "branch";
  public const SUB_BRANCH = "sub_branch";
  public const DEPARTMENT = "department";

  public const ROLE_SUPER_ADMIN = "Super Admin";
  public const ROLE_GENERAL = "General";

  public const SESSION_USER_ID = "user_id";
  public const SESSION_EMPLOYEE_ID = "employee_id";
  public const SESSION_HAS_PARENT_COMPANY = "has_parent_company";
  public const SESSION_PARENT_COMPANY_ID = "parent_company_id";
  public const SESSION_COMPANY = "company";
  public const SESSION_CURRENT_FISCAL_YEAR = "current_fiscal_year";
  public const SESSION_PASSWORD_CHANGE_TIME = "password_change_time";
  public const SESSION_EMPLOYEE = "employee";
  public const SESSION_IOPS_ID = "iops_id";

  public const NOTIFY_POSITION = 'top-right';

  public const REPLACEMENT_LEAVE_NAME = "Replacement Leave";
  public const UNPAID_LEAVE = "Unpaid Leave";
  public const TRAVEL_LEAVE = "Travel Leave";

  public const BLOOD_GROUP_LIST = [
    'A+' => 'A+',
    'A-' => 'A-',
    'B+' => 'B+',
    'B-' => 'B-',
    'O+' => 'O+',
    'O-' => 'O-',
    'AB+' => 'AB+',
    'AB-' => 'AB-',
  ];

  public const NEPALI_MONTH_LIST = [
    "01" => 'Baisakh',
    "02" => 'Jestha',
    "03" => 'Asar',
    "04" => 'Shrawan',
    "05" => 'Bhadra',
    "06" => 'Aswin',
    "07" => 'Kartik',
    "08" => 'Mangsir',
    "09" => 'Poush',
    "10" => 'Magh',
    "11" => 'Falgun',
    "12" => 'Chaitra',
  ];
  public const SETTING_TYPES = [
    "01" => 'OverTime',
  ];

  public const OT_SETTING_NAME_LIST = [
    "01" => "Work On Day Off",
    "02" => "Work On Holiday",
    "03" => "Regular",
  ];

  public const DEFAULT_OT_CLAIM_DAYS = 30;

  public const DOCUMENT_TYPES = [
    'citizenship-front' => 'Citizenship Front',
    'citizenship-back' => 'Citizenship Back',
    'slc' => 'SLC',
    '+2' => '+2',
    'bachelors' => 'Bachelors',
    'masters' => 'Master\'s Degree',
    'pan-card' => 'PAN Card',
    'marriage-certificate' => 'Marriage Certificate',
    'training-certificates' => 'Training Certificates',
    'insurance-document' => 'Insurance Document ',
    'license' => 'License',
    'experience-letter' => 'Experience Letter',
    'national-identity-document' => 'NID',
    'pan_card' => 'PAN Card',
  ];

  public const SSF_10 = 0.1; // 0.1 = 10%
  public const SSF_833 = 0.0833; // 0.0833 = 8.33%
  public const SSF_167 = 0.0167; // 0.0167 = 1.67%
  public const SSF_DEDUCTION_MULTIPLIER = 0.31;
  public const SSF_DEDUCTION_MULTIPLIER_VIANET = 0.2833;

  public const SSF_EMPLOYER = 0.11;
  public const SSF_EMPLOYEE = 0.2;

  public const MIN_SSF_AMOUNT = 3354.2;

  public const ANNUAL_TAXABLE_INCOME_DEDUCTION_LIMIT = 500000;
  public const INSURANCE_DEDUCTION_LIMIT = 40000;
  public const MEDICAL_INSURANCE_DEDUCTION_LIMIT = 20000;
  public const HOUSE_INSURANCE_DEDUCTION_LIMIT = 5000;

  public const ADDITIONAL_INCOME_CALCULATE_FROM = [
    "basic_salary" => "Basic Salary"
  ];

  public const ADDITIONAL_DEDUCTION_CALCULATE_FROM = [
    "basic_salary" => "Basic Salary"
  ];

  public const STIPEND_FOR = ['Hourly', 'Intern'];

  public const INFRASTRUCTURE_REQUIREMENTS = [
    'laptop',
    'desk',
    'communication',
    'tools',
    'internet',
    'other',
    'date'
  ];

  public const INFRASTRUCTURE_NAMES = [
    'laptop' => 'Laptop/Workstation',
    'desk' => 'Desk and Chair',
    'communication' => 'Communication Facilities',
    'tools' => 'Specialized Tools (e.g: ARE bag, Equipment Tools)',
    'internet' => 'Internet Access/Connection',
    'other' => 'Other Equipment',
    'date' => 'Requested Date for Position to be filled by'
  ];


  public const EMP_APP_ID = 1001;
  public const WEB_APP_ID = 1002;
  public const HRM_APP_ID = 1000;

  public const ADJUSTMENT_PAYSLIP_HEADING = [
    [
      'id' => 1000,
      'name' => 'Basic Salary',
      'value' => 'current_basic_salary',
    ],
    [
      'id' => 1001,
      'name' => 'Allowance',
      'value' => 'current_allowance',
    ],
    [
      'id' => 2000,
      'name' => 'Insurance',
      'value' => 'insurance',
    ],
    [
      'id' => 2001,
      'name' => 'CIT',
      'value' => 'current_cit_deduction',
    ],
  ];
  public const TERMINATION_TYPES = [
    'resignation' => 'Resignation',
    'layoff' => 'LayOff',
    'termination' => 'Termination',
    'out-of-contact' => 'Out Of Contact',
  ];
  public const MAIL_TYPES = [
    'rental_action' => 'Rental Action',
    'tada' => 'TADA',
    'hr' => 'HR',
    'whistleBlower' => 'WhistleBlower',
    'electricity_reminder' => 'Electricity Reminder',
    'rental_renewal' => 'Rental Renewal',
  ];
}

namespace App\Http\Helpers\Enums;

use ReflectionClass;

class DesignationRoles
{
  public const HOD = 'hod';
  public const BranchManager = 'branch_manager';
  public const Supervisor = 'supervisor';
  public const Employee = 'employee';
}

class WorkflowName
{
  public const LEAVE_APPROVAL = "leave_approval";
  public const TIME_REQUEST_APPROVAL = "time_request_approval";
  public const MANPOWER_REQUISITION = "manpower_requisition";
  public const TERMINATION_APPROVAL = "termination_approval";
  public const EMPLOYEE_TICKET = "employee_ticket";
  public const EMPLOYEE_TRANSFER = "employee_transfer";
  public const REPLACEMENT_LEAVE_REQUEST = "replacement_leave_request";
  public const PAYSLIP_APPROVAL = "payslip_approval";
  public const DUTY_CHANGE_REQUEST = "duty_change_request";
  public const PETTY_CASH_TOPUP = 'petty_cash_topup';
  public const PETTY_CASH_EXPENSE_BOOKING = 'petty_cash_expense_booking';
  public const PETTY_CASH_BILL = 'petty_cash_bill';
  public const PETTY_CASH_SUMMARY = 'petty_cash_summary';
  public const IRREGULARITY_TICKET = "attendance_irregularity_request";
  public const CUG_MANAGEMENT_PLAN_CHANGE_REQUEST = 'cug_management_plan_change_request';
  public const EDF_REQUEST = "edf_request";
  public const OT_REQUEST = "ot_request";
  public const TRAVEL_TICKET = "travel_allowance_ticket";
  public const FINANCE_TICKET = "travel_finance_ticket";
  public const SETTLEMENT_TICKET = "travel_settlement_ticket";
  public const TEAM_OT_TICKET = "team_ot_ticket";
  public const FIELD_VISIT_OT_TICKET = 'field_visit_workflow';

  static function getConstants()
  {
    $oClass = new ReflectionClass(__CLASS__);
    return $oClass->getConstants();
  }
  public static function getAppIcons(): array
  {
    return [
      self::LEAVE_APPROVAL => 'employee',
      self::TIME_REQUEST_APPROVAL => 'clock',
      self::MANPOWER_REQUISITION => 'employee',
      self::TERMINATION_APPROVAL => 'employee',
      self::EMPLOYEE_TRANSFER => 'transfer',
      self::REPLACEMENT_LEAVE_REQUEST => 'round-arrow',
      self::PAYSLIP_APPROVAL => 'document',
      self::DUTY_CHANGE_REQUEST => 'clock',
      self::IRREGULARITY_TICKET => 'warning',
      self::EDF_REQUEST => 'document',
      self::OT_REQUEST => 'clock',
      self::FIELD_VISIT_OT_TICKET => 'clock',
    ];
  }

  public static function getAppIcon(string $workflow): string
  {
    return self::getAppIcons()[$workflow] ?? 'default-icon';
  }
}

class WorkflowState
{
  public const APPROVED = "Approved";
  public const UPLOADED = "Uploaded";
  public const ENDORSED = "Endorsed";
  public const VERIFIED = "Verified";
  public const ALLOCATED = "Allocated";
  public const REJECTED = "Rejected";
  public const SUBMITTED = "Submitted";
  public const SENT_FOR_REVIEW = "SentForReview";
  public const REVIEWED = "Reviewed";
  public const REVIEW_REJECTED = "ReviewRejected";
  public const CANCELLED = "Cancelled";
  public const ESCALATED = "Escalated";
  public const RETURNED = "Returned";
  public const RECALLED = "Recalled";
  public const REOPENED = "Reopened";
  public const ASSIGNED = "Assigned";
  public const REVERTED = "Reverted";
  public const EDITED = "Edited";
  public const PROCESSING = "Processing";
  public const TRANSFERRED = "Transferred";

  static function getConstants()
  {
    $oClass = new ReflectionClass(__CLASS__);
    return $oClass->getConstants();
  }
}

class WorkflowPerformer
{
  public const APPROVER = "Approver";
  public const VERIFIER = "Verifier";
  public const REVIEWER = "Reviewer";
}

class EmployeeTicketProcess
{
  public const Transfer = "Transfer";
}

class EmployeeTicketTransferKey
{
  public const FromCompany = "from_company";
  public const ToCompany = "to_company";
  public const FromBranch = "from_branch";
  public const ToBranch = "to_branch";
  public const FromSubBranch = "from_sub_branch";
  public const ToSubBranch = "to_sub_branch";
  public const FromDepartment = "from_department";
  public const ToDepartment = "to_department";
  public const FromUnit = "from_unit";
  public const ToUnit = "to_unit";
}

class AdvanceSalaryType
{
  public const EMI = "emi";
  public const BULK = "bulk";
}

class MarriedStatus
{
  public const MARRIED = "married";
  public const UNMARRIED = "unmarried";
}
class BypassType
{
  public const ATTENDANCE = "attendance";
  public const DISABLE_MARITAL_STATUS = "disable marital status";
}

class HRMPackages
{
  public const PETTY_CASH = "petty cash";
  public const TEMPLATE_MANAGER = "template manager";
  public const CUG_MANAGEMENT = "cug";
  public const RENTAL_MANAGEMENT = "rental management";
  public const TADA = "tada";
  public const HR_TICKET = "hr ticket";
}
class MailTypes
{
  public const HR = 'hr';
  public const RENTAL_RENEWAL = 'rental_renewal';
  public const ELECTRICTY_REMINDER = 'electricity_reminder';
  public const TADA = 'tada';
}

class AgeGroups
{
  public const AGE_45_49 = '45-49';
  public const AGE_50_54 = '50-54';
  public const AGE_55_59 = '55-59';
  public const AGE_60_AND_ABOVE = '60 and above';
}
