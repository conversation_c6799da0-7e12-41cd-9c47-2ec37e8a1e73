<?php

namespace App\Http\Helpers;

use Carbon\Carbon;

class DateRanges
{
    /**
     * If no range provided, use last 7 days ending yesterday.
     */
    public static function resolve(?string $start = null, ?string $end = null): array
    {
        $endDate = $end ? Carbon::parse($end)->endOfDay() : Carbon::yesterday();
        $startDate = $start ? Carbon::parse($start)->startOfDay() : (clone $endDate)->subDays(6);

        return [$startDate->startOfDay(), $endDate->startOfDay()];
    }

    /**
     * Shift the given period back by exactly one week (same duration).
     */
    public static function previousWeek(array $period): array
    {
        [$start, $end] = $period;
        $days = $start->diffInDays($end);
        $prevEnd = (clone $start)->subDay();
        $prevStart = (clone $prevEnd)->subDays($days);
        return [$prevStart->startOfDay(), $prevEnd->startOfDay()];
    }

    public static function normalizeDateRange(
        Carbon|string|null $date = null,
        Carbon|string|null $from = null,
        Carbon|string|null $to = null,
        Carbon|string|null $defaultDate = null
    ): array {

        if ($date && !$date instanceof Carbon)
            $date = Carbon::parse($date);
        if ($from && !$from instanceof Carbon)
            $from = Carbon::parse($from);
        if ($to && !$to instanceof Carbon)
            $to = Carbon::parse($to);

        if (!$defaultDate)
            $defaultDate = Carbon::now();

        if ($defaultDate && !$defaultDate instanceof Carbon)
            $defaultDate = Carbon::parse($defaultDate);

        if ($date instanceof Carbon) {
            $from = $to = $date->copy()->startOfDay();
        }

        if (!$from || !$to) {
            $from = $defaultDate->copy()->startOfDay();
            $to = $defaultDate->copy()->endOfDay();
        }

        // Ensure order and date-only strings for safety
        if ($from->gt($to)) {
            [$from, $to] = [$to, $from];
        }

        return [$from->toDateString(), $to->toDateString()];
    }
}
