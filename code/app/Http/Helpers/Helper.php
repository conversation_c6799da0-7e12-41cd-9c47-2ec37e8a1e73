<?php

use <PERSON>Bright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

function currentEmployee()
{
  return app('currentEmployee');
}

function currentEmployeeId()
{
  return currentEmployee() ? currentEmployee()->id : null;
}

/** for fetching current fiscal year for api and web */
function currentFiscalYearId()
{
  $fiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
  if (!$fiscalYearId) {
    // cached for 100 seconds, so that multiple requests don't hit database in one api call
    $fiscalYearId = cache()->remember("fiscal_year_id", 100, function () {
      return \App\Models\configs\FiscalYear::where('is_active', '1')->pluck('id')->first();
    });
  }
  return $fiscalYearId;
}

/**
 * Check if a given Nepali date or date range falls within the active fiscal year.
 *
 * If only a start date is provided, it is treated as a single-day check.
 * If both start and end dates are provided, the function checks if the entire range
 * is within the current active fiscal year.
 *
 * @param string $nepStartDate The Nepali start date in 'YYYY-MM-DD' format.
 * @param string $nepEndDate   (Optional) The Nepali end date in 'YYYY-MM-DD' format.
 *                             Defaults to the start date if not provided.
 *
 * @return bool True if the given date(s) fall within the active fiscal year; false otherwise.
 */
function isWithinActiveFiscalYear($nepStartDate, $nepEndDate = "")
{
  if ($nepEndDate == "") {
    $nepEndDate = $nepStartDate;
  }
  $fiscalYear = \App\Models\configs\FiscalYear::where('is_active', true)->first();
  return $fiscalYear->start_date <= $nepStartDate && $fiscalYear->end_date >= $nepEndDate;
}

function authorizePermission($permissions, $abort = true): bool
{
  if (!is_array($permissions)) {
    $permissions = [$permissions];
  }
  if (!Auth::user()->can($permissions)) {
    if ($abort)
      abort(401);
    return false;
  }
  return true;
}

function employeeList()
{
  return cache()->remember('all-employee-list', null, function () {
    return \App\Models\Employee\Employee::with('organizationInfo:employee_code,employee_id,id')
      ->select('id', 'first_name', 'middle_name', 'last_name')
      ->get();
  });
}

function field_enums($table, $field, $associative = true)
{
  $enums = array();
  if ($table == '' || $field == '')
    return $enums;
  $query = DB::select("SHOW COLUMNS FROM {$table} LIKE '{$field}'")[0]->Type;
  preg_match_all("/'(.*?)'/", $query, $matches);
  // \DB::enableQueryLog();
  foreach ($matches[1] as $key => $value) {
    $enums[$value] = $value;
  }
  if ($associative)
    return $enums;
  return array_values($enums);
}

/**
 * compare two nepali dates
 * @return -1 if  first date is smaller than second one;
 * @return 1 if first date is larger than second one;
 * @return 0 otherwise;
 */
function compareNepaliDates(string $date1, string $date2)
{
  // Parse Nepali dates
  $parts1 = explode('-', $date1);
  $parts2 = explode('-', $date2);

  // Extract year, month, and day components
  $year1 = intval($parts1[0]);
  $month1 = intval($parts1[1]);
  $day1 = intval($parts1[2]);

  $year2 = intval($parts2[0]);
  $month2 = intval($parts2[1]);
  $day2 = intval($parts2[2]);

  // Validate month and day components
  if (
    ($month1 < 1 || $month1 > 12 || $day1 < 1 || $day1 > 32) ||
    ($month2 < 1 || $month2 > 12 || $day2 < 1 || $day2 > 32)
  ) {
    throw new Exception("Invalid Nepali date format");
  }

  // Compare Nepali dates
  if ($year1 < $year2) {
    return -1;
  } elseif ($year1 > $year2) {
    return 1;
  } elseif ($month1 < $month2) {
    return -1;
  } elseif ($month1 > $month2) {
    return 1;
  } elseif ($day1 < $day2) {
    return -1;
  } elseif ($day1 > $day2) {
    return 1;
  } else {
    return 0; // Dates are equal
  }
}

function errorContext(\Exception $e)
{
  return print_r([
    // "code" => $e->getCode(),
    "message" => $e->getMessage(),
    "from" => $e->getFile() . ":" . $e->getLine(),
  ], true);
}

function logInfo(string $message, array $context = [], $normal = false)
{
  if (count($context) && !$normal) {
    $message .= " :: " . print_r($context, true);
  }
  Log::info($message, $context);
}

function logError(string $message, $e = null, array $context = []): void
{
  $logData = [];

  if ($e) {
    $logData['error_context'] = errorContext($e);
  }

  if (!empty($context)) {
    $logData['context'] = print_r($context, true);
  }

  Log::error($message, $logData);
}

function logWarning(string $message, $e = null, array $context = []): void
{
  if ($e) {
    Log::warning($message . " :: " . $e);
  } else {
    Log::warning($message, $context);
  }
}

function logCronInfo(string $message, array $context = [], $normal = false)
{
  if (count($context) && !$normal) {
    $message .= " :: " . print_r($context, true);
  }
  Log::channel('cron')->info($message, $context);
}

function logCronError(string $message, Exception $e = null)
{
  if ($e) {
    Log::channel('cron')->error($message . " :: " . \errorContext($e));
  } else {
    Log::channel('cron')->error($message);
  }
}

function getDocumentIcon(string $mimeType)
{
  $icons = [
    "image" => "image",
    "pdf" => "file-earmark-pdf",
    "excel" => "file-earmark-spreadsheet",
    "word" => "file-earmark-word",
    "other" => "file-earmark-fill",
  ];

  $wordMimeTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
    "application/vnd.ms-word.document.macroEnabled.12",
    "application/vnd.ms-word.template.macroEnabled.12"
  ];

  $excelMimeTypes = [
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
    "application/vnd.ms-excel.sheet.macroEnabled.12",
    "application/vnd.ms-excel.template.macroEnabled.12",
    "application/vnd.ms-excel.addin.macroEnabled.12",
    "application/vnd.ms-excel.sheet.binary.macroEnabled.12"
  ];

  if (str_starts_with($mimeType, "image/")) {
    return $icons["image"];
  } else if ($mimeType == "application/pdf") {
    return $icons["pdf"];
  } else if (in_array($mimeType, $excelMimeTypes)) {
    return $icons["excel"];
  } else if (in_array($mimeType, $wordMimeTypes)) {
    return $icons["word"];
  } else {
    return $icons["other"];
  }
}

function isImpersonated()
{
  return (bool) session('user_id_main');
}

function convertJsonToArray($value)
{
  if (\is_string($value)) {
    if (json_decode($value) !== null) {
      return json_decode($value, true);
    }
  }
  return $value;
}

function ticketNotificationTitle($workflow)
{
  $title = "";
  switch ($workflow) {
    case (WorkflowName::LEAVE_APPROVAL):
      $title = "Leave Request";
      break;
    case (WorkflowName::TIME_REQUEST_APPROVAL):
      $title = "Time Request";
      break;
    case (WorkflowName::MANPOWER_REQUISITION):
      $title = "Manpower Requisition";
      break;
    case (WorkflowName::TERMINATION_APPROVAL):
      $title = "Employee Termination";
      break;
    case (WorkflowName::EMPLOYEE_TRANSFER):
      $title = "Employee Transfer";
      break;
    case (WorkflowName::REPLACEMENT_LEAVE_REQUEST):
      $title = "Add Replacement Leave";
      break;
  }
  return $title;
}

function fedexHrm()
{
  return config('app.name') === 'fedex_hrm';
}

function vianetHrm()
{
  return config('app.name') === 'vianet_hrm';
}

function konnectHrm()
{
  return config('app.name') === 'konnect_hrm';
}

function isSuperAdmin()
{
  return auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN);
}

function permissionViewAll()
{
  return auth()->user()->can(PermissionList::VIEW_ALL);
}

function permissionViewCompany()
{
  return auth()->user()->can(PermissionList::VIEW_COMPANY);
}

function permissionViewBranchOnly()
{
  return auth()->user()->can(PermissionList::VIEW_BRANCH);
}

function permissionViewDepartmentOnly()
{
  return auth()->user()->can(PermissionList::VIEW_DEPARTMENT);
}

// just renaming function for easier use.
function scopeAll()
{
  return auth()->user()->can(PermissionList::VIEW_ALL);
}
function scopeCompany()
{
  return auth()->user()->can(PermissionList::VIEW_COMPANY);
}

function scopeRegion()
{
  return auth()->user()->can(PermissionList::VIEW_REGION);
}

function scopeBranch()
{
  return auth()->user()->can(PermissionList::VIEW_BRANCH);
}

function scopeSubBranch()
{
  return auth()->user()->can(PermissionList::VIEW_SUB_BRANCH);
}

function scopeDepartment()
{
  return auth()->user()->can(PermissionList::VIEW_DEPARTMENT);
}

function multipleAccess($type)
{
  if (auth()->user()?->employee?->id == null)
    return [];

  $employeeId = auth()->user()?->employee?->id;
  return Cache::tags('multiple-access')
    ->remember(
      "multiple-access-$employeeId-$type",
      config('app.filterCacheLifetime'),
      fn() => App\Models\Config\MultipleAccess::query()
        ->where('employee_id', $employeeId)
        ->where('expired', '0')
        ->where('type', $type)
        ->whereDate('from', '<=', now())
        ->where(function ($q) {
          $q->whereNull('to')
            ->orWhereDate('to', '>=', now());
        })
        ->pluck("model_id")
        ->toArray()
    );
}

function scopeMultipleCompany()
{
  return multipleAccess(Constant::COMPANY);
}

function scopeMultipleRegion()
{
  return multipleAccess(Constant::REGION);
}

function scopeMultipleBranch()
{
  return multipleAccess(Constant::BRANCH);
}

function scopeMultipleSubBranch()
{
  return multipleAccess(Constant::SUB_BRANCH);
}

function scopeMultipleDepartment()
{
  return multipleAccess(Constant::DEPARTMENT);
}

function checkScope($type): bool
{
  $scopeWiseView = (new \App\Http\Services\ScopeFetcher())->scopeWiseView();
  return in_array($type, $scopeWiseView);
}

function convertToNepDate(string|Carbon $date, $format = "Y-m-d")
{
  return LaravelNepaliDate::from($date)->toNepaliDate($format);
}


// for excel data
function transformArrayToAssociative($data, $dateFields = [], $timeFields = [])
{
  if (empty($data) || !isset($data[0])) {
    // Handle cases where the data is empty or does not have headers
    return [];
  }

  // Get the headers (first row)
  $headers = array_map(fn($d) => strtolower(trim($d)), $data[0]);

  // Initialize the result array
  $result = [];

  // Loop through the data excluding the first row (headers)
  foreach (array_slice($data, 1) as $row) {
    // Create an associative array for each row
    $isEmptyData = array_filter($row, fn($item) => $item === null);
    if (count($isEmptyData) == count($row)) {
      continue;
    }
    $rowAssoc = array_combine($headers, $row);
    foreach ($rowAssoc as $key => $value) {
      $key = strtolower($key);
      $dateFields = array_map(fn($item) => strtolower($item), $dateFields);
      if (in_array($key, $dateFields)) {
        if ($value)
          $rowAssoc[$key] = getDateFromExcel($value);
      }
      $timeFields = array_map(fn($item) => strtolower($item), $timeFields);
      if (in_array($key, $timeFields)) {
        if ($value) {
          $rowAssoc[$key] = getTimeFromExcel($value);
        }
      }
      if (is_string($value))
        $rowAssoc[$key] = trim($value);
    }

    // Use the unique identifier as the key (assuming the first column is unique)
    array_push($result, $rowAssoc);
  }

  return $result;
}

function getDateFromExcel($date, $format = "Y-m-d")
{
  if (is_numeric($date)) {
    $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($date);
    return $date->format($format);
  }

  // validation for date from excel in string if it's y-m-d
  $d = DateTime::createFromFormat($format, $date);
  if ($d && $d->format($format) === $date)
    return $date;

  throw new Exception("Invalid date format $date");
}

function getTimeFromExcel($time, $format = 'H:i:s') {
  if (is_numeric($time)) {
    $time = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($time);
    return $time->format($format);
  }

  $d = DateTime::createFromFormat($format, $time);
  if ($d && $d->format($format) === $time)
    return $time;

  throw new Exception("Invalid time format $time");
}

function filterEmployeesByScope(Builder|EloquentBuilder $query, $orgInfoJoin = 'org', string|null $empInfoJoin = null)
{
  if (!scopeAll()) {

    /* ------- for the employees table join when necessary ------- */
    // if query from eloquent query model, we can access the 'from' key through getQuery, if not i.e. DB query we can access 'from' key directly
    $tableName = $query instanceof EloquentBuilder ? $query->getQuery()->from : $query->from;
    $isEmployeeTable = str_contains($tableName, 'employees');
    if ($isEmployeeTable) {
      $empInfoJoin = 'employees';
    } else {
      // get all joins from the table, e.g: [{..., table: 'employees as e'}, {..., table: 'employee_org as org}]
      $queryJoins = $query instanceof EloquentBuilder ? $query->getQuery()->joins : $query->joins;

      // only fetch the table name from the joins, e.g: ['employees as e', 'employee_org as org']
      $joins = array_map(
        fn($query) => $query->table,
        $queryJoins
      );

      // check if the employees exists on the joins by counting the join having employees in it
      $employeeJoinExists = count(array_filter(
        $joins,
        fn($join) => str_contains($join, 'employees')
      ));

      // if the count is 0, employees join doesn't exists so join the employees table
      if (!$employeeJoinExists) {
        $query = $query->leftJoin('employees as e', 'e.id', '=', "$orgInfoJoin.employee_id");
        $empInfoJoin = 'e';
      } else {
        // if there is join then the join info must be provided.
        if (!$empInfoJoin) {
          throw new Exception("Please provide the join info");
        }
      }
    }
    /* ------- ------------------x------------- ------- */


    $orgInfo = currentEmployee()?->organizationInfo;

    if (scopeMultipleCompany()) {
      return $query->where(function ($query) use ($orgInfoJoin, $empInfoJoin) {
        $query->where("$empInfoJoin.company_id", currentEmployee()->company_id)
          ->orWhereIn("$empInfoJoin.company_id", scopeMultipleCompany());
      });
    }

    if (scopeCompany()) {
      return $query->where("$empInfoJoin.company_id", currentEmployee()->company_id);
    }

    if (scopeMultipleRegion()) {
      return $query->where(function ($query) use ($orgInfoJoin, $orgInfo) {
        $query->where("$orgInfoJoin.region_id", $orgInfo->region_id)
          ->orWhereIn("$orgInfoJoin.region_id", scopeMultipleRegion());
      });
    }

    if (scopeRegion()) {
      return $query->where("$empInfoJoin.company_id", currentEmployee()->company_id)
        ->where("$orgInfoJoin.region_id", $orgInfo->region_id);
    }

    if (scopeMultipleBranch()) {
      return $query->where(function ($query) use ($orgInfoJoin, $orgInfo) {
        $query->where("$orgInfoJoin.branch_id", $orgInfo->branch_id)
          ->orWhereIn("$orgInfoJoin.branch_id", scopeMultipleBranch());
      });
    }

    if (scopeBranch()) {
      return $query->where("$empInfoJoin.company_id", currentEmployee()->company_id)
        ->where("$orgInfoJoin.region_id", $orgInfo->region_id)
        ->where("$orgInfoJoin.branch_id", $orgInfo->branch_id);
    }

    if (scopeMultipleSubBranch()) {
      return $query->where(function ($query) use ($orgInfoJoin, $orgInfo) {
        $query->where("$orgInfoJoin.sub_branch_id", $orgInfo->sub_branch_id)
          ->orWhereIn("$orgInfoJoin.sub_branch_id", scopeMultipleSubBranch());
      });
    }

    if (scopeSubBranch()) {
      return $query->where("$empInfoJoin.company_id", currentEmployee()->company_id)
        ->where("$orgInfoJoin.region_id", $orgInfo->region_id)
        ->where("$orgInfoJoin.branch_id", $orgInfo->branch_id)
        ->where("$orgInfoJoin.sub_branch_id", $orgInfo->sub_branch_id);
    }

    if (scopeMultipleDepartment()) {
      return $query->where(function ($query) use ($orgInfoJoin, $orgInfo) {
        $query->where("$orgInfoJoin.department_id", $orgInfo->department_id)
          ->orWhereIn("$orgInfoJoin.department_id", scopeMultipleDepartment());
      });
    }

    if (scopeDepartment()) {
      return $query->where("$empInfoJoin.company_id", currentEmployee()->company_id)
        ->where("$orgInfoJoin.region_id", $orgInfo->region_id)
        ->where("$orgInfoJoin.branch_id", $orgInfo->branch_id)
        ->where("$orgInfoJoin.department_id", $orgInfo->department_id);
    }

    return $query->where("$orgInfoJoin.employee_id", currentEmployee()?->id);
  }
  return $query;
}

function allowJobSeat($companyId)
{
  if (!$companyId) {
    logError("Company Id is null or empty");
    return false;
  }
  $company = Company::find($companyId);
  return $company?->allow_job_seat === 'Y';
}

function getNepaliDate(string|Carbon $date, string $format = "F j, Y D", $addTime = false)
{
  $nepaliDate = LaravelNepaliDate::from($date)->toNepaliDate($format);
  if ($addTime) {
    if (!$date instanceof Carbon) {
      $date = Carbon::parse($date);
    }
    $time = $date->format('h:i A');
    $nepaliDate .= " $time";
  }
  return $nepaliDate;
}

function distanceMeasurement(array $userCoord, array $officeCoord): float
{
  $earthRadius = 6371000; // Radius of Earth in meters

  $latDelta = deg2rad($userCoord['latitude'] - $officeCoord['latitude']);
  $lonDelta = deg2rad($userCoord['longitude'] - $officeCoord['longitude']);

  $a = sin($latDelta / 2) * sin($latDelta / 2) +
    cos(deg2rad($officeCoord['latitude'])) * cos(deg2rad($userCoord['latitude'])) *
    sin($lonDelta / 2) * sin($lonDelta / 2);

  $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

  return round($earthRadius * $c, 2); // Distance in meters
}

/**
 * Summary of generateShortUrl
 * @param string $url 
 * @param int $validity in seconds
 * @param string $slug
 */
function generateShortUrl($url, $validity, $slug)
{
  $validDate = now()->addSeconds($validity)->format('Y-m-d H:i:s');
  $serverUrl = 'https://via.net.np';
  $client   = new \GuzzleHttp\Client();
  $response = $client->request('POST', $serverUrl, [
    'http_errors' => false,
    'headers'     => [
      'Content-Type'  => 'application/x-www-form-urlencoded',
      'x-api-key'     => 'e4b2d84d2a5e862b435ecfa77a88e2893e85dfa12fa3465501d352ac95e2c66a'
    ],
    'form_params' => [
      'url'      => $url,
      'validity' => $validDate,
      'slug'     => $slug,
    ],
    'verify'       => false,
  ]);
  $result = json_decode($response->getBody());
  Log::info('Generate short url result:' . print_r($result, true));
  if ($result->url)
    return $result->url;

  return null;
}
