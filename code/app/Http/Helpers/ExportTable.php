<?php

namespace App\Http\Helpers;

use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\WithPagination;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

class ExportTable
{
    public static function exportDataTable(
        $data,
        $exportIgnoreColumns = [],
        $exportColumnHeadersMap = [],
        $exportFileName = "export.xlsx"
    ) {
        return Excel::download(new class($data, $exportIgnoreColumns, $exportColumnHeadersMap) implements FromCollection, WithHeadings
        {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);

                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];
                // Provide headings for your export file
                // You can customize this based on your data structure
                if (is_array($this->data) && isset($this->data[0])) {
                    $item = is_array($this->data[0]) ? $this->data[0] : $this->data[0]->toArray();
                    $headings = array_keys($item);
                } else {
                    $item = is_array($this->data->first()) ? $this->data->first() : $this->data->first()->toArray();
                    $headings = array_keys($item ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return $headings;
            }
        }, $exportFileName);
    }
}
