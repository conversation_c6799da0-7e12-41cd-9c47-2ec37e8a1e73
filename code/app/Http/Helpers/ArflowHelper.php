<?php

namespace App\Http\Helpers;

use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Models\RequestTicket;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;

class ArflowHelper
{
  public static function getWorkflows()
  {
    $allWorkflows = array_keys(config('arflow.workflows') ?? []);
    $ignored = RequestTicket::IGNORE_WORKFLOWS ?? [];

    return array_values(array_diff($allWorkflows, $ignored));
  }

  public static function getStatesArray(string $workflow)
  {
    $states = config('arflow.workflows')[$workflow]['states'] ?? [];
    $stateArr = [];
    foreach ($states as $state) {
      $stateArr[$state] = $state;
    }
    return $stateArr;
  }

  public static function getFinalStates($workflow)
  {
    return config('arflow.workflows')[$workflow]['final_states'] ?? [];
  }

  public static function getVerificationLevel(string $workflow)
  {
    return config('arflow.workflows')[$workflow]['verification_level'] ?? 0;
  }

  public static function getSuccessFinalStates($workflow)
  {
    return config('arflow.workflows')[$workflow]['success_final_states'] ?? [];
  }

  public static function getPerformers($workflow)
  {
    $performers = config('arflow.workflows')[$workflow]['performers'] ?? [];
    $verifiers = config('arflow.workflows')[$workflow]['verifiers'] ?? [];
    $performersArray = [];
    if (count($verifiers)) {
      $index = array_search(WorkflowPerformer::VERIFIER, $performers);
      unset($performers[$index]);
      foreach ($verifiers as $key => $value) {
        $performersArray[WorkflowPerformer::VERIFIER . '-' . $key] = $value;
      }
    }

    foreach ($performers as $item) {
      $performersArray[$item] = $item;
    }

    return $performersArray;
  }

  public static function hasReviewer($workflow)
  {
    $performers = config('arflow.workflows')[$workflow]['performers'] ?? [];
    return in_array(WorkflowPerformer::REVIEWER, $performers);
  }
}
