<?php

namespace App\Http\Helpers;

class ApiResponseHelper
{
    public static function validationError($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 411,
            'message'   => "Validation Error",
            'errors'    => $message
        );
    }

    public static function accessDenied($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 403,
            'message'   => $message
        );
    }

    public static function unauthorized($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 401,
            'message'   => $message
        );
    }

    public static function expiredProfile($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 405,
            'message'   => $message
        );
    }

    public static function expired($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 402,
            'message'   => $message
        );
    }

    public static function forbidden($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 403,
            'message'   => $message
        );
    }

    public static function getData($data, $message = 'Data Found!!!', $status = '1')
    {
        $toreturn = array(
            'status'    => $status,
            'statusCode' => 200,
            'message'   => $message,
            'response'  => $data
        );
        return $toreturn;
    }

    public static function notFound($message = 'No Data Available!!!', $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 404,
            'message'   => $message
        );
    }

    public static function create($data, $message, $status = '1')
    {
        return array(
            'status'    => $status,
            'statusCode' => 201,
            'message'   => $message,
            'response'      => $data
        );
    }

    public static function updateError($message, $status = '0')
    {
        return array(

            'status'    => $status,
            'message'   => $message,
            'statusCode' => 401,
        );
    }

    public static function successMessage($message, $status = '1')
    {
        return array(
            "status" => $status,
            'statusCode' => 200,
            'message' => $message
        );
    }

    public static function errorMessage($message, $status = '0')
    {
        return array(
            "status" => $status,
            'statusCode' => 403,
            'message' => $message
        );
    }

    public static function serverError($message, $status = '0')
    {
        $toReturn = array(
            "status" => $status,
            'statusCode' => 500,
            'message' => 'Internal Server Error',
            'error'   => $message
        );
        if (config('app.environment') != "development")
            $toReturn['error'] = $message;

        return $toReturn;
    }


    public static function loginSucceful($data, $token, $status = '1')
    {
        return array(
            'status'    => $status,
            'statusCode' => 200,
            'message'   => 'Login Successful!!!',
            'api_token'      => $token,
            'response'      => $data
        );
    }


    public static function responseNotFound($status = '0', $message = 'No Data Available!!!')
    {
        return array(
            'status'    => $status,
            'statusCode' => 404,
            'message'   => $message,
            'count'     => '0',
            'response'  => []
        );
    }

    public static function location($status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 503,
            'message'   => 'Access has not been granted to your location ',
            'response'  => []
        );
    }

    public static function packageExpired($message, $status = '0')
    {
        return array(
            'status'    => $status,
            'statusCode' => 426,
            'message'   => $message
        );
    }

    public static function internetExpired($data, $message, $status = '1')
    {
        return array(
            'status'    => $status,
            'statusCode' => 206,
            'message'   => $message,
            'response'  => $data
        );
    }

    public static function ConcurrentRestriction($message, $status = '0')
    {
        return array(
            "status" => $status,
            'statusCode' => 403,
            'message' => $message
        );
    }

    public static function urlFetchStream($message, $status = '0')
    {
        return array(
            "status" => $status,
            'statusCode' => 409,
            'message' => $message
        );
    }

    public static function versionNotSupported($message, $status = '0')
    {
        return array(
            "status" => $status,
            'statusCode' => 505,
            'message' => $message
        );
    }

    public static function paymentRequired($message, $status = '0')
    {
        return array(
            "status" => $status,
            'statusCode' => 415,
            'message' => $message
        );
    }
}
