<?php

namespace App\Http\Helpers;

use Endroid\QrCode\Label\Font\Font;
use Endroid\QrCode\Label\Font\FontInterface;
use Endroid\QrCode\Label\Label;
use Endroid\QrCode\Label\LabelAlignment;
use Endroid\QrCode\Label\Margin\Margin;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

class QRCodeHelper
{
    public static function generateQRCode($data)
    {
        $decoded = json_decode($data);
        $label = $decoded->name . '( ' . $decoded->code . ' )';

        $labelLength = strlen($label);
        $qrCodeSize = max(200, min(1000, 200 + ($labelLength * 10)));
        $qrCode = QrCode::create($data)
            ->setSize($qrCodeSize);

        $label = Label::create($label)
            ->setmargin(new Margin(30, 30, 30, 30))
            ->setAlignment(LabelAlignment::Center);
        $writer = new PngWriter();
        $result = $writer->write($qrCode, label: $label);
        return $result->getDataUri();
    }
}
