<?php

namespace App\Http\Helpers;

use Exception;

class ApiResponse extends ApiResponseHelper
{
    public static function validationError($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::validationError($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Validation Error Response => ", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function accessDenied($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::accessDenied($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Access Denied Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function unauthorized($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::unauthorized($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Unauthorized Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function forbidden($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::forbidden($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Forbidden Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function getData($data, $message = "Data Found!!!", $logMessage = "", $status = "1")
    {
        $toReturn = parent::getData($data, $message, $status);
        if ($logMessage)
            logInfo("$logMessage Get Data Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function notFound($logMessage = "", $status = "0")
    {
        $toReturn = parent::notFound($status);
        logError(($logMessage ? "$logMessage " : "") . " Not Found Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function create($data, $message, $logMessage = "", $status = "1")
    {
        $toReturn = parent::create($data, $message, $status);
        logInfo(($logMessage ? "$logMessage " : "") . " Create Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function updateError($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::updateError($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Update Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function successMessage($message, $logMessage = "", $status = "1")
    {
        $toReturn = parent::successMessage($message, $status);
        logInfo(($logMessage ? "$logMessage " : "") . " Success Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function errorMessage($message, $logMessage = "", $status = "0")
    {
        $toReturn = parent::errorMessage($message, $status);
        logError(($logMessage ? "$logMessage " : "") . " Error Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }

    public static function serverError($message, $e = null)
    {
        $toReturn = parent::serverError($message, "0");
        if ($e instanceof Exception)
            logError("Server Error Response =>", e: $e, context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }


    public static function loginSucceful($data, $token, $status = "1")
    {
        $toReturn = parent::loginSucceful($data, $token, $status);
        logInfo("Login Successful Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }


    public static function responseNotFound($logMessage = "", $status = "0", $message = "No Data Available!!!")
    {
        $toReturn = parent::responseNotFound($message, $status);
        logError("Response Not Found Response =>", context: $toReturn);
        return response()->json($toReturn, $toReturn["statusCode"]);
    }
}
