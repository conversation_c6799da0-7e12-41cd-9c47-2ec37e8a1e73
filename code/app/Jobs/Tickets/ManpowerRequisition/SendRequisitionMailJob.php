<?php

namespace App\Jobs\Tickets\ManpowerRequisition;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Models\Employee\Employee;
use App\Models\Tickets\ManpowerRequisition;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendRequisitionMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract & ManpowerRequisition $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $ticket = $this->model->requestTicket;
        $metadata = json_decode($this->parameters['metadata'] ?? [], true);
        $initiatorId = $metadata['initiator_id'];
        $initiatorRole = $metadata['initiator_role'];
        $recipientId = $metadata['next_owner_id'];
        $recipientRole = $metadata['next_owner_role'];
        $initiatorEmployee = Employee::find($initiatorId);
        $recipientEmployee = Employee::find($recipientId);
        $title = "";
        switch ($ticket->model_type) {
            case (\App\Models\Leaves\LeaveRequest::class):
                $title = $this->model->leaveType->name;
                break;
            case (\App\Models\TimeRequest::class):
                $title = "Time Request";
            case (\App\Models\Tickets\ManpowerRequisition::class):
                $title = "Manpower Requisition";
        }
        $title .= " - {$ticket->employee?->name}";

        $linkType = "toBeReviewedRequests";
        if (in_array($this->to, ArflowHelper::getFinalStates($this->model->workflow))) {
            $recipientEmployee = $ticket->employee;
            $linkType = "myRequestsHistory";
        }

        if ($this->model->type === 'new') {
            Mail::to($recipientEmployee?->organizationInfo->email)->send(new \App\Mail\NewManpowerRequisition(
                $this->model,
                route('myTickets', ['type' => $linkType, 'flow' => $ticket->workflow, 'id' => $ticket->model_id]),
            ));
        }
    }
}
