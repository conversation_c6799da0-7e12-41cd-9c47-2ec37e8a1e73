<?php

namespace App\Jobs\Tickets\LeaveRequest;

use App\Contracts\ArflowModelInterface;
use App\Models\Leaves\LeaveRequest;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemovePendingLeaveJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract & LeaveRequest $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $employeeLeave = \App\Models\Leaves\EmployeeLeave::where('employee_id', $this->model->employee_id)
            ->where('leave_type_id', $this->model->leave_type_id)
            ->where('fiscal_year_id', $this->model->fiscal_year_id)
            ->first();

        if (!$employeeLeave) {
            throw new \Exception("Leave not assigned to the employee");
        }
        $employeeLeave->pending_leave = 0;
        $employeeLeave->save();
        \logInfo("Pending employee leave cleared");
    }
}
