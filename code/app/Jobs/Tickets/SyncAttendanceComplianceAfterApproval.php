<?php

namespace App\Jobs\Tickets;

use App\Contracts\ArflowModelInterface;
use App\Jobs\Snapshot\SyncAttendanceComplianceSnapshotJob;
use App\Models\Leaves\LeaveRequest;
use App\Models\TimeRequest;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncAttendanceComplianceAfterApproval implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private ArflowModelInterface|LeaveRequest|TimeRequest $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&\Illuminate\Database\Eloquent\Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract|LeaveRequest|TimeRequest $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /** @var LeaveRequest | TimeRequest */
        $request = $this->model;

        $request->load('employee.organizationInfo');
        $fromDate = null;
        $toDate = null;
        $companyId = $request->employee?->company_id;
        $branchId = $request->employee?->organizationInfo?->branch_id;
        $departmentId = $request->employee?->organizationInfo?->department_id;


        if ($request instanceof LeaveRequest) {
            $fromDate = $request->start_date;
            $toDate = $request->end_date;
        } elseif ($request instanceof TimeRequest) {
            $fromDate = $toDate = $request->date;
        }

        dispatch(new SyncAttendanceComplianceSnapshotJob(
            from: $fromDate,
            to: $toDate,
            branchId: $branchId,
            departmentId: $departmentId,
            companyId: $companyId
        ));
    }
}
