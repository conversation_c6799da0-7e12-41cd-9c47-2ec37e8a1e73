<?php

namespace App\Jobs;

use App\Http\Services\DynamicMailConfigService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Mail\WhistleBlower;

class SendWhistleBlowerEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $formData = [], $emailId = "<EMAIL>";
    protected ?int $companyId;

    /**
     * Create a new job instance.
     */
    public function __construct($formData, $emailAddress, ?int $companyId = null)
    {
        $this->formData = $formData;
        $this->emailId = $emailAddress;
        $this->companyId = $companyId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        app(DynamicMailConfigService::class)->apply($this?->companyId);

        $email = new WhistleBlower($this->formData);
        if ($this->formData['evidence']) {
            foreach ($this->formData['evidence'] as $evidence) {
                $email->attachData(base64_decode($evidence['content']), $evidence['name'], ['mime' => $evidence['mime']]);
            }
        }

        $mailResult = Mail::to($this->emailId)->send($email);
        if (!$mailResult) {
            logError("Unable to send whistleblower email." . print_r($this->formData, true));
        }
    }
}
