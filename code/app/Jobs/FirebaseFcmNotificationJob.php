<?php

namespace App\Jobs;

use App\Http\Repositories\Configs\NotificationRepository;
use App\Http\Services\FirebaseNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FirebaseFcmNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public readonly int $userId,
        public readonly string $title,
        public readonly string $body,
        public readonly array $data
    ) {}

    /**
     * Execute the job.
     */
    public function handle(NotificationRepository $notificationRepo): void
    {
        $notificationRepo->sendFcmFirebaseNotification($this->userId, $this->title, $this->body, $this->data);
    }

    public function failed(): void
    {
        logError('FCM Notification Job Failed');
    }
}
