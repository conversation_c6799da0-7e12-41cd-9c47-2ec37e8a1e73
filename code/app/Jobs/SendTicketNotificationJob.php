<?php

namespace App\Jobs;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\Employee;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;

class SendTicketNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract & Model $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(\App\Http\Repositories\Configs\Interfaces\INotificationRepository $notificationRepo): void
    {
        $ticket = $this->model->requestTicket;
        $metadata = json_decode($this->parameters['metadata'] ?? [], true);
        \logInfo("Ticket transition completed, starting notification job with data: ", [
            "from" => $this->from,
            "to" => $this->to,
            "metadata" => $metadata
        ]);
        $initiatorId = $metadata['initiator_id'];
        $initiatorRole = $metadata['initiator_role'];
        $recipientId = $metadata['next_owner_id'];
        $recipientRole = $metadata['next_owner_role'];
        $initiatorEmployee = Employee::find($initiatorId);
        $title = "";
        switch ($ticket->workflow) {
            case (WorkflowName::LEAVE_APPROVAL):
                $title = $this->model->leaveType->name;
                break;
            case WorkflowName::IRREGULARITY_TICKET:
                $title = $this->model->type . " Request";
                break;
            default:
                $formattedTitle = str_replace('_', ' ', $ticket?->workflow ?? 'ticket_detail');
                $title = ucwords($formattedTitle);
        }
        $title .= " - {$ticket->employee?->name}";
        $initiatorName = $initiatorEmployee?->name;
        $message = "";

        if ($this->to === WorkflowState::SUBMITTED && $recipientRole === WorkflowPerformer::VERIFIER) {
            $message = "{$initiatorName} has sent the request for verification";
        } else if ($this->to === WorkflowState::SUBMITTED && $recipientRole === WorkflowPerformer::APPROVER) {
            $message = "{$initiatorName} has sent the request for approval";
        } else if ($this->to === WorkflowState::VERIFIED && $recipientRole === WorkflowPerformer::APPROVER) {
            $message = "{$initiatorName} has verified the request and sent for approval";
        } else if ($this->to === WorkflowState::VERIFIED && $recipientRole === WorkflowPerformer::VERIFIER) {
            $message = "{$initiatorName} has verified the request and sent for next level verification";
        } else if ($this->to === WorkflowState::ESCALATED) {
            $message = "{$initiatorName} has escalated the request";
        } else if ($this->to === WorkflowState::RETURNED) {
            $message = "{$initiatorName} has returned the request for recall";
        } else if ($this->to === WorkflowState::RECALLED) {
            $message = "{$initiatorName} has recalled the request";
        } else if ($this->to === WorkflowState::SENT_FOR_REVIEW) {
            $message = "{$initiatorName} has sent the request for review";
        } else if ($this->to === WorkflowState::REVIEWED) {
            $message = "{$initiatorName} has reviewed the request";
        } else if ($this->to === WorkflowState::REVIEW_REJECTED) {
            $message = "{$initiatorName} has rejected the review request";
        } else if ($this->to === WorkflowState::APPROVED) {
            $message = "Your request has been approved";
        } else if ($this->to === WorkflowState::REJECTED) {
            $message = "Your request has been rejected";
        } else if ($this->to === WorkflowState::REOPENED) {
            $message = "{$initiatorName} has reopened the request";
        } else if ($this->to === WorkflowState::REVERTED) {
            $message = "{$initiatorName} has reverted the request";
        }


        if ($recipientId != $initiatorId) {
            if (!$recipientId) $recipientId = $this->model->requestTicket->submitted_by;
            $recipientEmployee = Employee::find($recipientId);
            $user = $recipientEmployee->user;
            $accepted = app(AppSettingRepositoryInterface::class)->getEnabled();
            logInfo("Sending notification to user: {$user->id} - {$user->name}", [
                'initiator' => "{$initiatorName} - {$initiatorRole}",
                'recipient' => "{$recipientEmployee->name} - {$recipientRole}",
                'ticket' => $ticket->id,
                'workflow' => $ticket->workflow,
                'accepted workflows' => $accepted
            ]);
            try {
                app(\App\Http\Repositories\TicketRepository::class)->createAndSendTicketNotification(
                    $user,
                    $title,
                    $message,
                    $ticket
                );
            } catch (\Throwable $e) {
                logError("Error sending notification", $e, [
                    'user_id' => $user->id,
                    'ticket_id' => $ticket->id,
                    'workflow' => $ticket->workflow
                ]);
            }
        }

        // $performerIds = \array_unique(
        //     \array_merge(
        //         $this->model->stateHistory->pluck('actor_model_id')->toArray(),
        //         [$recipientId],
        //     )
        // );
        // $key = array_search($initiatorId, $performerIds);
        // if ($key !== false) {
        //     unset($performerIds[$key]);
        // }

        // $performers = Employee::with('user')->whereIn('id', $performerIds)->get();
        // $performerNames = [];
        // foreach ($performers as $performer) {
        //     $performer?->user->notify($notification);
        // \array_push($performerNames, $performer->name);
        // }
        // dd($performerNames, $notification);

        // \logInfo("Ticket Notification sent", [
        //     "initiator" => "{$initiatorName} - {$initiatorRole}",
        //     "recipient" => $performerNames,
        //     "notification" => ["title" => $title, "message" => $message]
        // ]);
    }
}
