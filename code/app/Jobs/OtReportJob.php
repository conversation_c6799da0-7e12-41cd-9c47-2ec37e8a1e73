<?php

namespace App\Jobs;

use App\Http\Repositories\FieldTeam\Report\Interfaces\FieldTeamAttendanceRepositoryInterface;
use App\Http\Services\DynamicMailConfigService;
use App\Http\Repositories\OtReport\Interfaces\OtAttendanceReportRepositoryInterface;
use App\Http\Repositories\OtReport\Interfaces\OtEmployeeReportRepositoryInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Excel as ExcelExcel;
use Maatwebsite\Excel\Facades\Excel;

class OtReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $email = "<EMAIL>";
    public $timeout = 300; // 5 minutes
    /**
     * Create a new job instance.
     */
    public function __construct(
        protected string $title,
        protected string $reportType,
        protected ?bool $isVariance = false,
        protected ?int $companyId = null,
        protected ?array $filters = [],
        protected ?string $emailTo = null,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        app(DynamicMailConfigService::class)->apply($this->companyId);

        // Validate email before processing
        if (empty($this->emailTo) || !filter_var($this->emailTo, FILTER_VALIDATE_EMAIL)) {
            logError("Invalid or empty recipient email in OtReportJob", [
                'email' => $this->emailTo,
                'company_id' => $this->companyId
            ]);
            return;
        }

        $heading = [];
        $dataToReturn = [];

        if ($this->reportType === 'employee') {
            [$heading, $dataToReturn] = $this->processEmployeeData();
        } elseif ($this->reportType === 'attendance') {
            [$heading, $dataToReturn] = $this->processAttendanceData();
        } elseif ($this->reportType === 'field_team_attendance') {
            [$heading, $dataToReturn] = $this->processFieldTeamAttendanceData();
        }

        if (empty($this->emailTo)) {
            logError("Cannot send OT report, recipient email missing.");
            return;
        }
        $export = new \App\Exports\OtExport($this->title, $heading, $dataToReturn);
        $excelContent = Excel::raw($export, ExcelExcel::XLSX);
        $email = new \App\Mail\OtReportMail($excelContent, "$this->title.xlsx");

        try {
            Mail::to($this->emailTo)->send($email);
        } catch (\Throwable $th) {
            logError("Error sending OT report mail: " . $th->getMessage());
        }
        logInfo("OT Report sent successfully to " . $this->emailTo);
    }

    private function processEmployeeData(): array
    {
        $heading = [
            'Employee Name',
            'Employee Code',
            'Branch',
            'Department',
            'Vendor',
            'Vendor PAN',
            'Duty Start',
            'Duty End',
            'In Time',
            'Out Time',
            'Total Working Hours',
            'Total OT Hours',
            'OT Type',
            'Remarks',
            'Approved By',
            'Approved At',
            'Submitted At',
        ];

        $otReportRepo = app(OtEmployeeReportRepositoryInterface::class);
        $otDetails = $otReportRepo->getOtEmployeeReport($this->filters);

        // If it's a Builder, execute it
        if ($otDetails instanceof \Illuminate\Database\Eloquent\Builder) {
            $otDetails = $otDetails->get();
        }

        $items = $otDetails->toArray();

        // Use array_map
        $dataToReturn = array_map(function ($item) {
            return [
                $item['employee_name'] ?? 'N/A',
                $item['emp_code'] ?? 'N/A',
                $item['branch'] ?? 'N/A',
                $item['department'] ?? 'N/A',
                $item['company'] ?? 'N/A',
                $item['pan_no'] ?? 'N/A',
                $item['duty_start'] ?? '',
                $item['duty_end'] ?? '',
                $item['in_time'] ?? '',
                $item['out_time'] ?? '',
                $this->formatSecondsToTime($item['total_working_hours'] ?? 0),
                $this->formatSecondsToTime($item['total_ot_hours'] ?? 0),
                $item['type'] ?? 'N/A',
                $item['remarks'] ?? 'N/A',
                $item['approver_name'] ?? 'N/A',
                $item['approved_date'] ?? '',
                $item['nep_date'] ?? '',
            ];
        }, $items);

        Log::info('Final data to return count: ' . count($dataToReturn));

        return [$heading, $dataToReturn];
    }

    private function formatSecondsToTime($seconds): string
    {
        // Check if it's already a string format
        if (is_string($seconds) && !is_numeric($seconds)) {
            return $seconds; // Return as-is if it's already formatted
        }

        if (empty($seconds) || !is_numeric($seconds)) {
            return '0 hours 00 minutes';
        }

        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        return sprintf('%d hours %02d minutes', $hours, $minutes);
    }

    private function processAttendanceData(): array
    {
        $reportRepo = app(OtAttendanceReportRepositoryInterface::class);

        // Use the optimized repository method that handles everything in one call
        return $reportRepo->getOtAttendanceExportData($this->filters, $this->isVariance);
    }


    private function processFieldTeamAttendanceData(): array
    {
        $attendanceRepo = app(FieldTeamAttendanceRepositoryInterface::class);
        return $attendanceRepo->getExportData($this->filters);
    }

    protected function secondsToHours($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return sprintf("%d hrs %d min", $hours, $minutes);
    }
}
