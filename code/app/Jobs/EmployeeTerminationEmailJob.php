<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class EmployeeTermination<PERSON>mailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $data, $toEmail = "<EMAIL>", $ccEmail = ['<EMAIL>'], $date;

    /**
     * Create a new job instance.
     */
    public function __construct($data, $toEmail, array $ccEmail, $date)
    {
        $this->data = $data;
        $this->toEmail = $toEmail;
        $this->ccEmail = $ccEmail;
        $this->date = $date;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $mailResult = Mail::to($this->toEmail)->cc($this->ccEmail)->send(new \App\Mail\EmployeeTerminationEmail($this->data, $this->date));
        if (!$mailResult) {
            logError("Unable to send termination email.");
        }
        logInfo("Termination mail sent successfully of " . $this->date);
    }
}
