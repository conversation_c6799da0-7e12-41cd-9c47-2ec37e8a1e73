<?php

namespace App\Jobs;

use App\Http\Services\DynamicMailConfigService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class OfferLetterMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $edfMeta = [];
    protected ?int $companyId;
    protected $companyDetails = [];
    protected $attachments = [];

    /**
     * Create a new job instance.
     */
    public function __construct(
        $edfMeta,
        $companyId = null,
        $companyDetails = [],
        $attachments = []
    ) {
        $this->edfMeta = $edfMeta;
        $this->companyId = $companyId;
        $this->companyDetails = $companyDetails;
        $this->attachments = $attachments;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        app(DynamicMailConfigService::class)->apply($this?->companyId);

        // Ensure attachments is always an array
        $attachments = (array)$this->attachments;

        $mailResult = Mail::to($this->edfMeta['email'])->send(
            new \App\Mail\OfferLetterMail($this->edfMeta, $this->companyDetails, $attachments)
        );

        if (!$mailResult) {
            logError("Unable to send offer letter email to {$this->edfMeta['email']}.");
        } else {
            logInfo("Offer letter sent to: {$this->edfMeta['email']} - Name: {$this->edfMeta['name']}");
        }

        // Clean up attachments - only delete files that exist
        foreach ($attachments as $path) {
            if (is_string($path) && file_exists($path)) {
                try {
                    unlink($path);
                    logInfo("Deleted temporary attachment: $path");
                } catch (\Exception $e) {
                    logError("Failed to delete attachment: $path - " . $e->getMessage());
                }
            }
        }
    }
}
