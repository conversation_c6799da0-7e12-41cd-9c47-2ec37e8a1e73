<?php

namespace App\Jobs;

use App\Http\Repositories\LeaveRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class LeaveBalanceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly ?array $employeeIds = [],
        private readonly ?string $fiscalYearId = null,
    ) {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        foreach ($this->employeeIds as $employeeId) {
            $leaveRepo = new LeaveRepository;
            $leaveRepo->recalculateLeaveBalance($employeeId, $this->fiscalYearId);
            logCronInfo("Recalculated Leave Balance of employee: " . $employeeId . ". Fiscal Year Id: " . $this->fiscalYearId);
        }
    }
}
