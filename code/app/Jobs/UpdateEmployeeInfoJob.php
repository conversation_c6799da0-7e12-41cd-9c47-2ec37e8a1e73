<?php

namespace App\Jobs;

use App\Http\Repositories\IopsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateEmployeeInfoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $employeeData, $authResponse;

    /**
     * Create a new job instance.
     * @param $employeeData
     * @param $authResponse
     */
    public function __construct($employeeData, $authResponse)
    {
        $this->employeeData = $employeeData;
        $this->authResponse = $authResponse;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $iopsRepo = new IopsRepository();
        $iopsRepo->updateEmployeeDetails($this->employeeData, $this->authResponse);
    }
}
