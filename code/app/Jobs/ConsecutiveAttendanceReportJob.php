<?php

namespace App\Jobs;

use App\Exports\ConsecutiveAttendanceReportExport;
use Maatwebsite\Excel\Excel as ExcelFormat;
use App\Mail\ConsecutiveAttendanceReportMail;
use App\Models\configs\Setting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class ConsecutiveAttendanceReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    public $startDate;
    public $endDate;
    /**
     * Create a new job instance.
     */
    public function __construct($data, $startDate, $endDate)
    {
        $this->data = $data;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $fileName = 'Consecutive_Attendance_Report_' . now()
            ->format('Y-m-d-His') . '.xlsx';
        $export = new ConsecutiveAttendanceReportExport($this->data);

        $exportFile = Excel::raw(
            new ConsecutiveAttendanceReportExport($this->data),
            ExcelFormat::XLSX
        );

        $recipientTo = Setting::where('namespace', 'hr')
            ->where('key', 'to_address')
            ->pluck('value')
            ->map(fn($v) => json_decode($v, true))
            ->flatten()
            ->filter()
            ->values()
            ->all();

        $recipientCC = Setting::where('namespace', 'hr')
            ->where('key', 'cc_address')
            ->pluck('value')
            ->map(fn($v) => json_decode($v, true))
            ->flatten()
            ->filter()
            ->values()
            ->all();
        Mail::to($recipientTo)
            ->cc($recipientCC)
            ->send(new ConsecutiveAttendanceReportMail(
                $this->data,
                $fileName,
                $exportFile,
                $this->startDate,
                $this->endDate
            ));

        logInfo("Mail sent successfully.");
    }
}
