<?php

namespace App\Jobs;

use App\Http\Services\DynamicMailConfigService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class MailToDeveloper implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $email = "<EMAIL>", $title = "", $message = "";
    protected ?int $companyId;

    /**
     * Create a new job instance.
     */
    public function __construct($title, $message, $companyId = null)
    {
        $this->title = $title;
        $this->message = $message;
        $this->companyId = $companyId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
         app(DynamicMailConfigService::class)->apply($this?->companyId);

        $mailResult = Mail::to($this->email)->send(new \App\Mail\MailToDeveloper($this->title, $this->message));
        if (!$mailResult) {
            logError("Unable to send email to developer {$this->email}.");
        }
        logInfo("Developer email information. Title: " . $this->title);
        logInfo("Message: " . $this->message);
    }
}
