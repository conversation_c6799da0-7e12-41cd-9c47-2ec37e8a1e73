<?php

namespace App\Jobs\Snapshot;

use App\Http\Repositories\Snapshots\GenerateEmployeeCountSnapshotRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

class SyncEmployeeCountSnapshotJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private ?string $date = null,
        private ?string $from = null,
        private ?string $to = null,
        private ?int $branchId = null,
        private ?int $departmentId = null,
        private ?int $companyId = null,
        private ?int $categoryId = null,
    ) {
        $this->queue = 'snapshot';
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->date) {
            $this->from = $this->to = $this->date;
        }

        if (!$this->from || !$this->to) {
            $this->from = $this->to = Carbon::today()->toDateString();
        }

        $cursor = Carbon::parse($this->from)->startOfDay();
        $end = Carbon::parse($this->to)->endOfDay();
        $repo = new GenerateEmployeeCountSnapshotRepository();

        while ($cursor->lte($end)) {
            logCronInfo("Sync Attendance Compliance Snapshot: " . $cursor->toDateString() . ", Branch Id: " . $this->branchId . ", Department Id: " . $this->departmentId);
            $repo->handle($cursor->toDateString(), $this->branchId, $this->departmentId, $this->companyId, $this->categoryId);
            $cursor->addDay();
        }

    }
}
