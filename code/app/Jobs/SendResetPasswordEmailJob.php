<?php

namespace App\Jobs;

use App\Mail\ResetPasswordMail;
use App\Traits\WithNotify;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendResetPasswordEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $email = "", $username = "", $password = "", $name = "";

    /**
     * Create a new job instance.
     */
    public function __construct($email, $username, $password, $name = "")
    {
        $this->email = $email;
        $this->username = $username;
        $this->password = $password;
        $this->name = $name;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $mailResult = Mail::to($this->email)->send(new ResetPasswordMail($this->username, $this->password, $this->name));
        if (!$mailResult) {
            logError("Unable to send email for reset password to mail {$this->email}.");
        }
    }
}
