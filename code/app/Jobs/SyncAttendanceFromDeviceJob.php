<?php

namespace App\Jobs;

use App\Models\configs\AttDevice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncAttendanceFromDeviceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $attDevice;

    /**
     * Create a new job instance.
     */
    public function __construct(AttDevice $attDevice)
    {
        $this->attDevice = $attDevice;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $result = $this->attDevice->syncAttendanceToDb(true);

        if (!$result) {
            $message = "Sync failed in " . $this->attDevice->branch->name . " branch device: " . $this->attDevice->name . "[" . $this->attDevice->device_ip . "]";
            Log::error($message);
        }
    }

    public function tags()
    {
        return ["Sync Device", " Branch: " . $this->attDevice->branch->name . " and device: " . $this->attDevice->name . "[" . $this->attDevice->device_ip . "]"];
    }
}
