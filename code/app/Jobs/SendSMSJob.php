<?php

namespace App\Jobs;

use App\Http\Repositories\IopsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Send<PERSON><PERSON>Job implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message = "", $employee_id = "";

    /**
     * Create a new job instance.
     */
    public function __construct($employee_id, $message)
    {
        $this->employee_id = $employee_id;
        $this->message = $message;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $iopsRepo = new IopsRepository;
        $response = $iopsRepo->sendSMS($this->employee_id, $this->message);
        if ($response)
            logInfo("SMS sent successfully to employee with id: $this->employee_id");
        else
            logInfo("Unable to send SMS to employee with id: $this->employee_id");
    }
}
