<?php

namespace App\Jobs;

use App\Http\Helpers\Constant;
use App\Models\configs\EmployeeShift;
use App\Models\Employee\EmployeeOrg;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\BypassType;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\Company;
use App\Models\configs\DutyRoster;
use App\Models\Employee\Employee as EmployeeModel;
use App\Models\Employee\EmployeeOrg as OrgModel;
use App\Models\Leaves\Attendance as AttendanceModel;
use App\Models\configs\Holiday as HolidayModel;
use App\Models\Payroll\PayrollBypassEmployee;
use App\Models\configs\FiscalYear as FiscalYearModel;
use App\Models\configs\HolidayBranch as HolidayBranchModel;
use App\Models\Leaves\EmployeeLeaveDetail;
use App\Models\TimeRequest;
use Illuminate\Support\Facades\DB;

class AttendanceSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $syncType, $holidayByDate, $holidayByBranch, $companies, $allShifts, $nightShiftIds, $biometricWithNightShift,
        $date, $empCode, $travelingEmployees = [];

    private $empAppId = Constant::EMP_APP_ID, $webAppId = Constant::WEB_APP_ID;

    private $defaultSourceIP = "127.0.0.1";

    private $travelStatusName = "Field Visit";

    /**
     * Create a new job instance.
     */
    public function __construct($syncType = "regular", $date = null, $empCode = null)
    // public function __construct()
    {
        $this->syncType = $syncType;
        $this->date = $date;
        $this->empCode = $empCode;

        $fiscalYear = FiscalYearModel::current()->first();
        $this->companies = Company::get('id');
        $holidayIds = [];
        foreach ($this->companies as $company) {
            $this->holidayByDate[$company->id] = HolidayModel::where([
                ['fiscal_year', $fiscalYear->id],
                ['company_id', $company->id]
            ])->get()->keyBy('eng_date')->toArray();
            $holidayIds = [...array_column($this->holidayByDate[$company->id], 'id'), ...$holidayIds];
        }

        $holidayByBranch = HolidayBranchModel::whereIn('holiday_id', $holidayIds)->get();
        $this->holidayByBranch = [];
        foreach ($holidayByBranch as $value) {
            if (!isset($this->holidayByBranch[$value['holiday_id']]))
                $this->holidayByBranch[$value['holiday_id']] = [];
            $this->holidayByBranch[$value['holiday_id']][$value['branch_id']] = $value;
        }

        $this->allShifts = EmployeeShift::withTrashed()->get()->keyBy('id');
        $this->nightShiftIds = EmployeeShift::where('type', 'overnight')->pluck('id')->toArray();
        $this->biometricWithNightShift = EmployeeOrg::whereIn('shift_id', $this->nightShiftIds)->pluck('biometric_id')->toArray();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $date = $this->date;
        $empCode = $this->empCode;
        if (is_null($date)) {
            $today = $date = date('Y-m-d');
        } else {
            $today = $date;
        }

        $class = 'Codebright\\Projects\\Models\\TravelAllowanceTicket';
        if (class_exists($class)) {
            $this->travelingEmployees = $class::leftJoin('tada_travel_employee_mapping', 'tada_travel_tickets.id', '=', 'tada_travel_employee_mapping.travel_ticket_id')
                ->leftJoin('tada_tasks', 'tada_travel_tickets.task_id', '=', 'tada_tasks.id')
                ->whereDate(
                    DB::raw('COALESCE(actual_travel_eng_start_date, estimated_travel_eng_start_date)'),
                    '<=',
                    $today
                )
                ->whereDate(
                    DB::raw('COALESCE(actual_travel_eng_end_date, estimated_travel_eng_end_date)'),
                    '>=',
                    $today
                )
                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ENDORSED])
                ->get(['purpose_of_visit', 'tada_travel_employee_mapping.employee_id', 'tada_tasks.title'])
                ->keyBy('employee_id')
                ->toArray();
        }

        $todayBs = LaravelNepaliDate::from($today)->toNepaliDate();

        // Add night shift duty employees of previous day to biometricWithNightShift array
        $yesterday = Carbon::parse($today)->subDay()->format('Y-m-d');
        $employeeWithNightShiftDuty = DutyRoster::leftJoin('employee_org', 'duty_rosters.employee_id', '=', 'employee_org.employee_id')
            ->where('date_en', $yesterday)
            ->whereIn('duty_rosters.shift_id', $this->nightShiftIds)
            ->pluck('biometric_id')
            ->toArray();
        $this->biometricWithNightShift = array_unique(array_merge($this->biometricWithNightShift, $employeeWithNightShiftDuty), SORT_REGULAR);

        // process to delete previous fiscal year data from device logs
        $activeYears = explode('-', $date);
        $activeYear = $activeYears[0];
        $activeMonth = $activeYears[1];
        if ($activeMonth > 8) {
            //process to delete previous fiscal year data from device logs
            $prevYear = $activeYear - 1;
            $beforePrevYear = $prevYear - 1;
            $beforePrevYear = $beforePrevYear . '-07' . '-17 00:00:00'; // year before previous year
            $prevYear = $prevYear . '-07' . '-16 23:59:59'; // previous year
            $attLogRaw = DB::table('att_logs')
                ->whereBetween('log_date', [$beforePrevYear, $prevYear])
                ->delete();
        }
        // Delete process end

        if (is_null($empCode)) {
            $activeEmployees = EmployeeModel::whereHas('organizationInfo', function ($query) use ($today) {
                $query->whereNull('termination_date')->where('doj', '<=', $today);
            })
                ->get()
                ->pluck('id');
            $empOrgs = OrgModel::with('employee')
                ->whereIn('employee_id', $activeEmployees)
                ->whereNotNull('biometric_id')
                ->get(['employee_id', 'biometric_id', 'shift_id', 'branch_id', 'employee_code'])
                ->keyBy('biometric_id');
            $empCodes = array_column($empOrgs->toArray(), 'biometric_id');
        } elseif (!is_array($empCode)) {
            $empOrgs = OrgModel::with('employee')
                ->where('employee_id', $empCode)
                ->where('doj', '<=', $today)
                ->withTrashed()
                ->whereNotNull('biometric_id')
                ->get()
                ->keyBy('biometric_id');
            $empCodes = array_column($empOrgs->toArray(), 'biometric_id');
        } elseif (is_array($empCode)) {
            $empCodes = $empCode;
            $empOrgs = OrgModel::with('employee')
                ->whereIn('employee_id', $empCodes)
                ->where('doj', '<=', $today)
                ->withTrashed()
                ->whereNotNull('biometric_id')
                ->get()
                ->keyBy('biometric_id');
            $empCodes = array_column($empOrgs->toArray(), 'biometric_id');
        }

        $attendanceBypassEmployees = PayrollBypassEmployee::where('bypass_type', function ($query) {
            $query->select('id')
                ->from('payroll_bypass_types')
                ->where('name', BypassType::ATTENDANCE);
        })->pluck('employee_id')->toArray();
        $attendanceToday = AttendanceModel::where('date_en', $today)->get()->keyBy('employee_id');
        $where = " log_date like '" . $today . "%'";
        $attLogRaw = DB::table('att_logs')
            ->whereRaw($where)
            ->orderBy('log_date', 'asc')
            ->get();
        $attLogs = [];
        foreach ($attLogRaw as $value) {
            if (!isset($attLogs[$value->enrollment_no]))
                $attLogs[$value->enrollment_no] = [];
            $attLogs[$value->enrollment_no][] = $value;
        }

        switch ($this->syncType) {
            case "night_shift":
                $nightShiftBiometric = array_intersect($empCodes, $this->biometricWithNightShift);
                $this->attendanceSyncNightShift($today, $nightShiftBiometric, $empOrgs);
                $this->attendanceSyncNightShift(Carbon::parse($date)->addDay(), $nightShiftBiometric, $empOrgs);
                break;
            default:
                $filteredBiometrics = array_diff($empCodes, $this->biometricWithNightShift);
                $this->syncReqularAttendance($filteredBiometrics, $empOrgs, $attLogs, $today, $todayBs, $attendanceToday, $attendanceBypassEmployees);
                break;
        }
    }

    public function syncReqularAttendance($filteredBiometrics, $empOrgs, $attLogs, $today, $todayBs, $attendanceToday, $attendanceBypassEmployees)
    {
        foreach ($filteredBiometrics as $empCode) {
            $empDetails = $empOrgs->get($empCode);
            if (!$empDetails) continue;

            $enrollmentNo = preg_replace('/[^0-9]/', '', $empCode);
            $enrollmentNo = ltrim($enrollmentNo, '0');
            $attLog = $attLogs[$enrollmentNo] ?? [];
            $countData = count($attLog);
            $source = "Biometric Device";
            $source_ip = json_encode([
                "in" => '',
                "out" => ''
            ]);
            if (count($attLog)) {
                $firstAttLog = $attLog[0] ?? [];
                $lastAttLog = count($attLog) ? last($attLog) : [];
                $source1 = $this->determineSource($firstAttLog);
                $source2 = $this->determineSource($lastAttLog);
                $source = $source1 == $source2 ? $source1 : "$source1, $source2";
                $inIP = $firstAttLog->device_ip ?? $this->defaultSourceIP;
                $outIP = "";
                if ($firstAttLog?->id != $lastAttLog?->id) {
                    $outIP = $lastAttLog->device_ip ?? $this->defaultSourceIP;
                }
                $source_ip = json_encode([
                    "in" => $inIP,
                    "out" => $outIP
                ]);
            }

            // Bypass attendance of Employee.
            if (in_array($empDetails->employee_id, $attendanceBypassEmployees)) {
                $flag = $this->bypassAttendanceForEmployee($empDetails, $today, $todayBs, $source);
                if ($flag)
                    continue;
            }

            switch ($countData) {
                //no data found
                case 0:
                    $source = "";
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    $existingAttendance = null;
                    if ($attendanceDaily) {
                        $existingAttendance = AttendanceModel::find($attendanceDaily->id);
                        if (!is_null($existingAttendance)) {
                            if ($existingAttendance->leave_status) {
                                break;
                            }
                            if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                                break;
                            }
                            $timeRequests = TimeRequest::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            if ($existingAttendance->missed_punch_status || count($timeRequests)) {
                                $status = [];
                                foreach ($timeRequests as $timeRequest) {
                                    $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $existingAttendance->in_time;
                                    $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $existingAttendance->out_time;
                                    $status = $this->checkStatus($empDetails, $today, $inTime, $outTime);
                                    $status['in_time'] = $inTime;
                                    $status['out_time'] = $outTime;
                                    $status['in_remarks'] = $timeRequest?->in_note;
                                    $status['out_remarks'] = $timeRequest?->out_note;
                                    $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                                }
                                $status['missed_punch_status'] = true;
                                $status['source'] = 'Time Request';
                                $status['soruce_ip'] = $source_ip;
                                $status['duty_coverage'] = $this->calculateDutyCoverage($status['in_time'], $status['out_time'], $existingAttendance->duty_start, $existingAttendance->duty_end);
                                $existingAttendance->update($status);
                                break;
                            }
                            // $existingAttendance->forceDelete();
                        }
                    }
                    $attendance = new AttendanceModel;
                    if ($existingAttendance) $attendance = AttendanceModel::find($attendanceDaily->id);

                    $attendance->employee_id = $empDetails->employee_id;
                    $attendance->employee_code = $empDetails->employee_code;
                    $attendance->date_en = $today;
                    $attendance->date_np = $todayBs;
                    $attendance->in_time = null;
                    $attendance->out_time = null;
                    // $attendance->shift_id = $empDetails->shift_id;
                    $status = $this->checkStatus($empDetails, $today, null, null, $attendanceDaily);
                    if ($status == 0)
                        break;
                    $attendance->shift_id = $status['shift_id'];
                    $this->setDutyStartEndTimeForSaving($attendance, $empDetails);
                    $attendance->in_remarks = $status['in_remarks'];
                    $attendance->out_remarks = $status['out_remarks'];
                    $attendance->total_hours = $status['total_hours'];
                    $attendance->remarks = $status['remarks'];
                    $attendance->status = $status['status'];
                    $attendance->source = $source;
                    $attendance->source_ip = $source_ip;

                    if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                        $attendance->in_time = Carbon::parse($empDetails?->shift?->start_time, 'UTC')->format('h:i:s A');
                        $attendance->out_time = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                        $attendance->status = $this->travelStatusName;
                        $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        $attendance->total_hours = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                    }

                    $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                        ->first();

                    if ($employeeLeaveDetails) {
                        $attendance->remarks = $employeeLeaveDetails->remarks;
                        $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                        $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                        $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                    }

                    $timeRequests = TimeRequest::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                        ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                        ->get();
                    foreach ($timeRequests as $timeRequest) {
                        $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $existingAttendance->in_time;
                        $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $existingAttendance->out_time;
                        $status = $this->checkStatus($empDetails, $today, $inTime, $outTime);
                        $attendance->in_time = $inTime;
                        $attendance->out_time = $outTime;
                        $attendance->in_remarks = $timeRequest?->in_note;
                        $attendance->out_remarks = $timeRequest?->out_note;
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->missed_punch_status = true;
                        $attendance->source = 'Time Request';
                        $attendance->source_ip = $source_ip;
                    }

                    $attendance->duty_coverage = $this->calculateDutyCoverage(
                        $attendance->duty_start,
                        $attendance->duty_end,
                        $attendance->in_time,
                        $attendance->out_time
                    );

                    $attendance->save();

                    break;
                //only 1 data found in attendance log
                case 1:
                    $inTime = $outTime = null;
                    $punchTime = Carbon::parse($attLog[0]->log_date, 'UTC');
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    if ($attendanceDaily) {
                        if (is_null($attendanceDaily->in_time))
                            $inTime = $punchTime->format('h:i:s A');
                        else {
                            $inTime = $today . " " . $attendanceDaily->in_time;
                            $inTime = Carbon::parse($inTime, 'UTC');
                            if ($inTime->format('h:i:s A') == $punchTime->format('h:i:s A'))
                                $inTime = $punchTime;
                            else {
                                if ($inTime->lt($punchTime))
                                    $outTime = $punchTime->format('h:i:s A');
                            }
                            $inTime = $inTime->format('h:i:s A');
                        }
                        if ($attendanceDaily->missed_punch_status) {
                            $timeRequests = TimeRequest::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            foreach ($timeRequests as $timeRequest) {
                                $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $inTime;
                                $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $outTime;
                                $status['in_remarks'] = $timeRequest?->in_note;
                                $status['out_remarks'] = $timeRequest?->out_note;
                            }
                        }
                        $status = $this->checkStatus($empDetails, $today, $inTime, $outTime, $attendanceDaily);

                        if ($status == 0)
                            break;
                        $status['in_time'] = $inTime;
                        $status['out_time'] = $outTime;
                        if ($attendanceDaily->missed_punch_status) {
                            $status['source'] = 'Time Request';
                        } else {
                            $status['source'] = $source;
                        }
                        $status['source_ip'] = $source_ip;
                        $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                        $this->setDutyStartEndTimeForSaving($attendanceDaily, $empDetails);
                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $status['out_time'] = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                            $status['status'] = $this->travelStatusName;
                            $status['remarks'] = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                            $status['total_hours'] = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $status['remarks'] .= $employeeLeaveDetails->remarks;
                            $status['status'] = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $status['leave_status'] = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $status['leave_request_id'] = $employeeLeaveDetails->leaveRequest?->id;
                        }
                        $attendanceDaily->update($status);
                    } else {
                        $status = $this->checkStatus($empDetails, null, $punchTime, $outTime);
                        if ($status == 0)
                            break;
                        $attendance = new AttendanceModel;

                        $attendance->employee_id = $empDetails->employee_id;
                        $attendance->employee_code = $empDetails->employee_code;
                        $attendance->date_en = $today;
                        $attendance->date_np = $todayBs;
                        $attendance->in_time = $punchTime->format('h:i:s A');
                        $attendance->out_time = $outTime;
                        // $attendance->shift_id = $empDetails->shift_id;
                        $attendance->in_remarks = $status['in_remarks'];
                        $attendance->out_remarks = $status['out_remarks'];
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->source = $source;
                        $attendance->source_ip = $source_ip;

                        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);

                        $attendance->duty_coverage = $this->calculateDutyCoverage(
                            $attendance->duty_start,
                            $attendance->duty_end,
                            $attendance->in_time,
                            $attendance->out_time
                        );

                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $attendance->in_time = $punchTime->format('h:i:s A');
                            $attendance->out_time = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                            $attendance->status = $this->travelStatusName;
                            $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                            $attendance->total_hours = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $attendance->remarks .= $employeeLeaveDetails->remarks;
                            $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                        }

                        $attendance->save();
                    }
                    break;
                //more than 1 data found in attendance log
                default:
                    //get in time of first entry
                    $inTime = Carbon::parse($attLog[0]->log_date, 'UTC')->format('h:i:s A');
                    //get out time of last entry
                    $outTime = Carbon::parse(last($attLog)->log_date, 'UTC')->format('h:i:s A');
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    if ($attendanceDaily) {
                        $attInTime = $attendanceDaily->in_time;
                        if (!is_null($attInTime))
                            $inTime = $attInTime;
                        if ($attendanceDaily->missed_punch_status) {
                            $timeRequests = TimeRequest::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            foreach ($timeRequests as $timeRequest) {
                                $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $inTime;
                                $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $outTime;
                                $status['in_remarks'] = $timeRequest?->in_note;
                                $status['out_remarks'] = $timeRequest?->out_note;
                            }
                        }
                        $status = $this->checkStatus($empDetails, $today, $inTime, $outTime, $attendanceDaily);
                        if ($status == 0)
                            break;
                        $status['in_time'] = $inTime;
                        $status['out_time'] = $outTime;
                        if ($attendanceDaily->missed_punch_status) {
                            $status['source'] = 'Time Request';
                        } else {
                            $status['source'] = $source;
                        }
                        $status['source_ip'] = $source_ip;
                        $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                        $this->setDutyStartEndTimeForSaving($attendanceDaily, $empDetails);
                        $status['duty_coverage'] = $this->calculateDutyCoverage(
                            $attendanceDaily->duty_start,
                            $attendanceDaily->duty_end,
                            $inTime,
                            $outTime
                        );
                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $status['status'] = $this->travelStatusName;
                            $status['remarks'] = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $status['remarks'] .= $employeeLeaveDetails->remarks;
                            $status['status'] = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $status['leave_status'] = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $status['leave_request_id'] = $employeeLeaveDetails->leaveRequest?->id;
                        }
                        $attendanceDaily->update($status);
                    } else {
                        $status = $this->checkStatus($empDetails, $today, $inTime, $outTime);
                        if ($status == 0)
                            break;
                        $attendance = new AttendanceModel;

                        $attendance->employee_id = $empDetails->employee_id;
                        $attendance->employee_code = $empDetails->employee_code;
                        $attendance->date_en = $today;
                        $attendance->date_np = $todayBs;
                        $attendance->in_time = $inTime;
                        $attendance->out_time = $outTime;
                        // $attendance->shift_id = $empDetails->shift_id;
                        $attendance->in_remarks = $status['in_remarks'];
                        $attendance->out_remarks = $status['out_remarks'];
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->source = $source;
                        $attendance->source_ip = $source_ip;

                        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);

                        $attendance->duty_coverage = $this->calculateDutyCoverage(
                            $attendance->duty_start,
                            $attendance->duty_end,
                            $attendance->in_time,
                            $attendance->out_time
                        );

                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $attendance->in_time = $inTime;
                            $attendance->out_time = $outTime;
                            $attendance->status = $this->travelStatusName;
                            $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $today], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $attendance->remarks .= $employeeLeaveDetails->remarks;
                            $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                        }
                        $attendance->save();
                    }
                    break;
            }
        }
    }

    public function calculateDutyCoverage($dutyStartTime, $dutyEndTime, $startTime, $endTime)
    {
        if (!$dutyStartTime || !$dutyEndTime || !$startTime || !$endTime) {
            return 0;
        }


        $dutyStart = Carbon::parse($dutyStartTime);
        $dutyEnd = Carbon::parse($dutyEndTime);
        $logStart = Carbon::parse($startTime);
        $logEnd = Carbon::parse($endTime);

        // Total duty duration in minutes
        $totalDutyMinutes = $dutyStart->diffInMinutes($dutyEnd);
        if ($totalDutyMinutes === 0) {
            return 0;
        }

        $attendedMinutes = $logStart->diffInMinutes($logEnd);
        return min(1, round($attendedMinutes / $totalDutyMinutes, 2));
    }

    public function checkStatus($empOrg, $date = null, $inTime = null, $outTime = null, $existingAttendance = null)
    {
        if (!$existingAttendance) {
            $dutyTime = $empOrg->employee?->dutyTime($date);
        } else {
            $dutyTime = $empOrg->employee?->dutyTime($date, $existingAttendance);
        }

        $shiftId = $dutyTime['shift']?->id;
        $dayOff = $dutyTime['dayOff'];

        if (is_null($shiftId))
            return 0;
        $shifts = EmployeeShift::all()->keyBy('id');
        $employeeId = $empOrg->employee_id;
        $status = [];
        $status['in_remarks'] = null;
        $status['out_remarks'] = null;
        $status['total_hours'] = null;
        $status['remarks'] = null;
        $status['shift_id'] = $shiftId;
        if (is_null($date))
            $today = date('Y-m-d');
        else
            $today = $date;
        if (!$shifts->has($shiftId))
            return;
        $shift = $shifts[$shiftId];
        $graceTime = $shift->grace_time;
        $startTime = Carbon::parse($shift->start_time)->addMinutes((int) $graceTime ?? 0);
        $endTime = Carbon::parse($shift->end_time)->subMinutes((int) $graceTime ?? 0);
        $lateInCategoryTimes = \App\Models\configs\LateInCategoryTime::leftJoin("late_in_categories", 'late_in_category_id', '=', 'late_in_categories.id')->where("shift_id", $shiftId)->get();
        $holidayList = $this->holidayByDate[$empOrg?->employee?->company_id] ?? [];

        if (is_null($inTime) && is_null($outTime)) {
            $leaveDetail = EmployeeLeaveDetail::with("leaveType:id,name")->where([['employee_id', $employeeId], ['date', $today]])->first();
            if ($leaveDetail) {
                $status['status'] = ($leaveDetail->leaveType->name . " [" . $leaveDetail->leaveOption?->name . "]") ?? "On Leave";
                $status['remarks'] = $leaveDetail->remarks ?? "";
            } else {
                $shiftYetToBeStarted = $this->shiftYetToBeStarted($today);
                $status['status'] = in_array($shiftId, $shiftYetToBeStarted) ? "Shift Not Started" : "Absent";
            }
        } elseif (is_null($inTime)) {
            $status['status'] = 'Missed Punch';
            $outTime = Carbon::parse($outTime);
            if ($outTime->lt($endTime)) {
                $status['status'] .= '[Early Out]';
                $status['out_remarks'] = 'Early Out';
            }
        } elseif (is_null($outTime)) {
            $shiftEnded = $this->shiftEnded($today);
            $status['status'] = in_array($shiftId, $shiftEnded) ? "Missed Punch" : "Present";
            $startTime = strtotime($startTime);
            $_inTime = strtotime($inTime);
            if (($_inTime > $startTime) && !count($lateInCategoryTimes)) {
                $status['status'] .= '[Late In]';
                $status['in_remarks'] = 'Late In';
            }
            $storedLateTime = "";
            foreach ($lateInCategoryTimes as $lateInTime) {
                $flag = $this->getTimeStatus($lateInTime->late_time, $lateInTime->condition, $inTime);
                if ($flag) {
                    if ($_inTime > $startTime) {
                        if (!$storedLateTime || $lateInTime->late_time > $storedLateTime) {
                            $status['status'] .= "[{$lateInTime->name}]";
                            $status['in_remarks'] = $lateInTime->name;
                            $storedLateTime = strtotime(Carbon::parse($lateInTime->late_time, 'UTC')->format('h:i:s A'));
                        }
                    }
                }
            }
            // }
        } else {
            $outTime = Carbon::parse($outTime);
            $_inTime = Carbon::parse($inTime);
            $status['status'] = 'Present';
            if (($_inTime->gt($startTime)) && !count($lateInCategoryTimes)) {
                $status['status'] .= '[Late In]';
                $status['in_remarks'] = 'Late In';
            }
            $storedLateTime = "";
            foreach ($lateInCategoryTimes as $lateInTime) {
                $flag = $this->getTimeStatus($lateInTime->late_time, $lateInTime->condition, $inTime);
                if ($flag) {
                    if ($_inTime->gt($startTime)) {
                        if (!$storedLateTime || $lateInTime->late_time > $storedLateTime) {
                            $status['status'] .= "[{$lateInTime->name}]";
                            $status['in_remarks'] = $lateInTime->name;
                            $storedLateTime = strtotime(Carbon::parse($lateInTime->late_time, 'UTC')->format('h:i:s A'));
                        }
                    }
                }
            }
            // }
            if ($outTime->lt($endTime)) {
                $status['status'] .= '[Early Out]';
                $status['out_remarks'] = 'Early Out';
            }
            $status['total_hours'] = $outTime->diff($_inTime)->format('%H:%I');
        }
        $statusChanged = false;
        // check for holiday and day off
        if (array_key_exists($today, $holidayList)) {
            $holiday_id = $holidayList[$today]['id'];
            $branch_id = $empOrg->branch_id;
            $gender = $empOrg->employee->gender;
            $holiday_branch = $this->holidayByBranch[$holiday_id][$branch_id] ?? null;
            if ($holiday_branch) {
                if (strtolower($holidayList[$today]['gender'] ?? '') == strtolower("All") || strtolower($holidayList[$today]['gender'] ?? '') == strtolower($gender)) {
                    if (is_null($inTime) && is_null($outTime))
                        $status['status'] = "On Holiday(" . $holidayList[$today]['name'] . ")";
                    else
                        $status['status'] = "Work On Holiday(" . $holidayList[$today]['name'] . ")";
                    $statusChanged = true;
                }
            }
        }
        if ($dayOff && $statusChanged == false) {
            if (is_null($inTime) && is_null($outTime))
                $status['status'] = "Day Off";
            else
                $status['status'] = "Work On Day Off";
        }
        return $status;
    }

    public function bypassAttendanceForEmployee($empDetails, $today, $todayBs, $source)
    {
        $flag = true;
        $shift = $this->allShifts[$empDetails->shift_id] ?? null;
        $dayOff = $shift ? date('l', strtotime("Sunday +{$shift->day_off} days")) : null;

        if ($dayOff == date('l', strtotime($today))) {
            $flag = false;
        }

        $holidayList = $this->holidayByDate[$empDetails?->employee?->company_id] ?? [];
        if (array_key_exists($today, $holidayList)) {
            $holiday_id = $holidayList[$today]['id'];
            $branch_id = $empDetails->branch_id;
            $gender = $empDetails->employee->gender;
            $holiday_branch = $this->holidayByBranch[$holiday_id][$branch_id] ?? null;
            if ($holiday_branch) {
                if (strtolower($holidayList[$today]['gender'] ?? '') == strtolower("All") || strtolower($holidayList[$today]['gender'] ?? '') == strtolower($gender)) {
                    $flag = false;
                }
            }
        }

        if ($flag) {
            // Delete existing attendance record if any
            $existingAttendance = AttendanceModel::where('employee_id', $empDetails->employee_id)
                ->where('date_en', $today)
                ->get();

            if ($existingAttendance->count()) {
                $existingAttendance->each->forceDelete();
            }

            $attendance = new AttendanceModel();
            $attendance->employee_id = $empDetails->employee_id;
            $attendance->employee_code = $empDetails->employee_code;
            $attendance->date_en = $today;
            $attendance->date_np = $todayBs;
            $attendance->in_time = Carbon::parse($empDetails->shift->start_time, 'UTC')->format('h:i:s A');
            $attendance->out_time = Carbon::parse($empDetails->shift->end_time, 'UTC')->format('h:i:s A');
            $attendance->status = 'Present';
            $attendance->remarks = 'Biometric Device';
            $attendance->source = $source;
            $attendance->source_ip = json_encode([
                "in" => $this->defaultSourceIP,
                "out" => $this->defaultSourceIP
            ]);;
            $attendance->shift_id = $empDetails->shift_id;
            $attendance->duty_start = $empDetails->shift->start_time;
            $attendance->duty_end = $empDetails->shift->end_time;
            $attendance->total_hours = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
            $attendance->save();
        }

        return $flag;
    }

    public function attendanceSyncNightShift($date, $biometircs, $empOrgs)
    {
        $yesterday = Carbon::parse($date)->subDay()->format('Y-m-d');
        $yesterdayBs = LaravelNepaliDate::from($yesterday)->toNepaliDate();
        $attlogRaw = DB::table('att_logs')
            ->whereDate('log_date', Carbon::parse($date)->subDay()->toDateString())
            ->orWhereDate('log_date', Carbon::parse($date)->toDateString())
            ->orderBy('log_date', 'asc')
            ->get();
        $yesterdayAttLogs = [];
        foreach ($attlogRaw as $value) {
            if (!isset($yesterdayAttLogs[$value->enrollment_no]))
                $yesterdayAttLogs[$value->enrollment_no] = [];
            $yesterdayAttLogs[$value->enrollment_no][] = $value;
        }

        $attendanceBypassEmployees = PayrollBypassEmployee::where('bypass_type', function ($query) {
            $query->select('id')
                ->from('payroll_bypass_types')
                ->where('name', BypassType::ATTENDANCE);
        })->pluck('employee_id')->toArray();

        $attendanceToday = AttendanceModel::where('date_en', $yesterday)->get()->keyBy('employee_id');

        foreach ($biometircs as $empCode) {
            if (!isset($empOrgs[$empCode]))
                continue;
            $empDetails = $empOrgs[$empCode];
            $enrollmentNo = preg_replace('/[^0-9]/', '', $empCode);
            $enrollmentNo = ltrim($enrollmentNo, '0');
            $attLog[] = collect($yesterdayAttLogs[$enrollmentNo] ?? [])->first(function ($item) use ($date, $yesterday, $empDetails) {
                return $item->inout_mode == 0 &&
                    ($item->log_date > Carbon::parse($yesterday . " " . $empDetails->shift->start_time)->subHours(3)->format('Y-m-d H:i:s')) &&
                    ($item->log_date < Carbon::parse($date . " " . $empDetails->shift->end_time)->subHours(3)->format('Y-m-d H:i:s'));
            });

            $attLog[] = collect($yesterdayAttLogs[$enrollmentNo] ?? [])->last(function ($item) use ($date, $yesterday, $empDetails) {
                return $item->inout_mode == 1 &&
                    ($item->log_date > Carbon::parse($yesterday . " " . $empDetails->shift->start_time)->subHours(3)->format('Y-m-d H:i:s')) &&
                    ($item->log_date < Carbon::parse($date . " " . $empDetails->shift->end_time)->addHours(4)->format('Y-m-d H:i:s'));
            });
            // $attLog = $yesterdayAttLogs[$enrollmentNo] ?? [];
            $countData = count(array_filter($attLog));
            $source = "Biometric Device";
            $source_ip = json_encode([
                "in" => '',
                "out" => ''
            ]);
            $inIP = "";
            $outIP = "";

            // Check for In log existence
            if ($attLog[0]) {
                $firstAttLog = $attLog[0] ?? [];
                $source = $this->determineSource($firstAttLog);
                $inIP = $firstAttLog->device_ip ?? $this->defaultSourceIP;
            }

            // Check for Out log existence
            if ($attLog[1]) {
                $lastAttLog = count($attLog) ? last($attLog) : [];
                $source1 = $this->determineSource($lastAttLog);
                $source = $source1 == $source ? $source : "$source, $source1";
                if (isset($firstAttLog) && ($firstAttLog?->id != $lastAttLog?->id)) {
                    $outIP = $lastAttLog->device_ip ?? $this->defaultSourceIP;
                }
            }

            $source_ip = json_encode([
                "in" => $inIP,
                "out" => $outIP
            ]);

            // Bypass attendance of Employee.
            if (in_array($empDetails->employee_id, $attendanceBypassEmployees)) {
                $flag = $this->bypassAttendanceForEmployee($empDetails, $yesterday, $yesterdayBs, $source_ip);
                if ($flag)
                    continue;
            }

            switch ($countData) {
                //no data found
                case 0:
                    $source = "";
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    $existingAttendance = null;
                    if ($attendanceDaily) {
                        $existingAttendance = AttendanceModel::find($attendanceDaily->id);
                        if (!is_null($existingAttendance)) {
                            if ($existingAttendance->leave_status) {
                                break;
                            }
                            if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                                break;
                            }
                            $timeRequests = TimeRequest::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            if ($existingAttendance->missed_punch_status || count($timeRequests)) {
                                $status = [];
                                foreach ($timeRequests as $timeRequest) {
                                    $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $existingAttendance->in_time;
                                    $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $existingAttendance->out_time;
                                    $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime);
                                    $status['in_time'] = $inTime;
                                    $status['out_time'] = $outTime;
                                    $status['in_remarks'] = $timeRequest?->in_note;
                                    $status['out_remarks'] = $timeRequest?->out_note;
                                    $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                                }
                                $status['missed_punch_status'] = true;
                                $status['source'] = 'Time Request';
                                $status['soruce_ip'] = $source_ip;
                                $status['duty_coverage'] = $this->calculateDutyCoverage($status['in_time'], $status['out_time'], $existingAttendance->duty_start, $existingAttendance->duty_end);
                                $existingAttendance->update($status);
                                break;
                            }
                            // $existingAttendance->forceDelete();
                        }
                    }
                    $attendance = new AttendanceModel;
                    if ($existingAttendance) $attendance = AttendanceModel::find($attendanceDaily->id);

                    $attendance->employee_id = $empDetails->employee_id;
                    $attendance->employee_code = $empDetails->employee_code;
                    $attendance->date_en = $yesterday;
                    $attendance->date_np = $yesterdayBs;
                    $attendance->in_time = null;
                    $attendance->out_time = null;
                    // $attendance->shift_id = $empDetails->shift_id;
                    $status = $this->checkStatus($empDetails, $yesterday, null, null, $attendanceDaily);
                    if ($status == 0)
                        break;
                    $attendance->shift_id = $status['shift_id'];
                    $this->setDutyStartEndTimeForSaving($attendance, $empDetails);
                    $attendance->in_remarks = $status['in_remarks'];
                    $attendance->out_remarks = $status['out_remarks'];
                    $attendance->total_hours = $status['total_hours'];
                    $attendance->remarks = $status['remarks'];
                    $attendance->status = $status['status'];
                    $attendance->source = $source;
                    $attendance->source_ip = $source_ip;

                    if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                        $attendance->in_time = Carbon::parse($empDetails?->shift?->start_time, 'UTC')->format('h:i:s A');
                        $attendance->out_time = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                        $attendance->status = $this->travelStatusName;
                        $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        $attendance->total_hours = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                    }

                    $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                        ->first();

                    if ($employeeLeaveDetails) {
                        $attendance->remarks = $employeeLeaveDetails->remarks;
                        $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                        $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                        $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                    }

                    $timeRequests = TimeRequest::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                        ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                        ->get();
                    foreach ($timeRequests as $timeRequest) {
                        $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $existingAttendance->in_time;
                        $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $existingAttendance->out_time;
                        $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime);
                        $attendance->in_time = $inTime;
                        $attendance->out_time = $outTime;
                        $attendance->in_remarks = $timeRequest?->in_note;
                        $attendance->out_remarks = $timeRequest?->out_note;
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->missed_punch_status = true;
                        $attendance->source = 'Time Request';
                        $attendance->source_ip = $source_ip;
                    }

                    $attendance->duty_coverage = $this->calculateDutyCoverage(
                        $attendance->duty_start,
                        $attendance->duty_end,
                        $attendance->in_time,
                        $attendance->out_time
                    );

                    $attendance->save();

                    break;
                //only 1 data found in attendance log
                case 1:
                    $inTime = $outTime = null;
                    if ($attLog[0]->log_date ?? null) {
                        $inTime = Carbon::parse($attLog[0]->log_date, 'UTC')?->format('h:i:s A');
                    } else if ($attLog[1]->log_date ?? null) {
                        $outTime = Carbon::parse($attLog[1]->log_date, 'UTC')?->format('h:i:s A');
                    }
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    if ($attendanceDaily) {
                        if ($attendanceDaily->missed_punch_status) {
                            $timeRequests = TimeRequest::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            foreach ($timeRequests as $timeRequest) {
                                $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $inTime;
                                $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $outTime;
                                $status['in_remarks'] = $timeRequest?->in_note;
                                $status['out_remarks'] = $timeRequest?->out_note;
                            }
                        }
                        $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime, $attendanceDaily);

                        if ($status == 0)
                            break;
                        $status['in_time'] = $inTime;
                        $status['out_time'] = $outTime;
                        if ($attendanceDaily->missed_punch_status) {
                            $status['source'] = 'Time Request';
                        } else {
                            $status['source'] = $source;
                        }
                        $status['source_ip'] = $source_ip;
                        $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                        $this->setDutyStartEndTimeForSaving($attendanceDaily, $empDetails);
                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $status['out_time'] = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                            $status['status'] = $this->travelStatusName;
                            $status['remarks'] = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                            $status['total_hours'] = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $status['remarks'] .= $employeeLeaveDetails->remarks;
                            $status['status'] = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $status['leave_status'] = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $status['leave_request_id'] = $employeeLeaveDetails->leaveRequest?->id;
                        }
                        $attendanceDaily->update($status);
                    } else {
                        $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime);
                        if ($status == 0)
                            break;
                        $attendance = new AttendanceModel;

                        $attendance->employee_id = $empDetails->employee_id;
                        $attendance->employee_code = $empDetails->employee_code;
                        $attendance->date_en = $yesterday;
                        $attendance->date_np = $yesterdayBs;
                        $attendance->in_time = $inTime;
                        $attendance->out_time = $outTime;
                        // $attendance->shift_id = $empDetails->shift_id;
                        $attendance->in_remarks = $status['in_remarks'];
                        $attendance->out_remarks = $status['out_remarks'];
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->source = $source;
                        $attendance->source_ip = $source_ip;

                        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);

                        $attendance->duty_coverage = $this->calculateDutyCoverage(
                            $attendance->duty_start,
                            $attendance->duty_end,
                            $attendance->in_time,
                            $attendance->out_time
                        );

                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $attendance->in_time = $inTime;
                            $attendance->out_time = Carbon::parse($empDetails?->shift?->end_time, 'UTC')->format('h:i:s A');
                            $attendance->status = $this->travelStatusName;
                            $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                            $attendance->total_hours = gmdate('H:i', $empDetails->shift->total_working_hour * 3600);
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $attendance->remarks .= $employeeLeaveDetails->remarks;
                            $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                        }

                        $attendance->save();
                    }
                    break;
                //more than 1 data found in attendance log
                default:
                    if ($attLog[0]->log_date ?? null) {
                        $inTime = Carbon::parse($attLog[0]->log_date, 'UTC')->format('h:i:s A') ?? "";
                    } else $inTime = null;
                    if ($attLog[1]->log_date ?? null) {
                        $outTime = Carbon::parse(last($attLog)->log_date, 'UTC')->format('h:i:s A') ?? "";
                    } else $outTime = null;
                    $attendanceDaily = $attendanceToday[$empDetails->employee_id] ?? null;
                    if ($attendanceDaily) {
                        if ($attendanceDaily->missed_punch_status) {
                            $timeRequests = TimeRequest::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                                ->whereIn('state', [WorkflowState::APPROVED, WorkflowState::ASSIGNED])
                                ->get();
                            foreach ($timeRequests as $timeRequest) {
                                $inTime = $timeRequest?->in_time ? Carbon::parse($timeRequest?->in_time, 'UTC')?->format('h:i:s A') : $inTime;
                                $outTime = $timeRequest?->out_time ? Carbon::parse($timeRequest?->out_time, 'UTC')?->format('h:i:s A') : $outTime;
                                $status['in_remarks'] = $timeRequest?->in_note;
                                $status['out_remarks'] = $timeRequest?->out_note;
                            }
                        }
                        $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime, $attendanceDaily);
                        $status['total_hours'] = Carbon::parse($attLog[1]->log_date, 'UTC')->diff(Carbon::parse($attLog[0]->log_date, 'UTC'))->format('%H:%I');
                        if ($status == 0)
                            break;
                        $status['in_time'] = $inTime;
                        $status['out_time'] = $outTime;
                        if ($attendanceDaily->missed_punch_status) {
                            $status['source'] = 'Time Request';
                        } else {
                            $status['source'] = $source;
                        }
                        $status['source_ip'] = $source_ip;
                        $status['remarks'] = trim($attendanceDaily->remarks ? "{$attendanceDaily->remarks}" . ($status['remarks'] ? "; {$status['remarks']}" : '') : $status['remarks']);
                        $this->setDutyStartEndTimeForSaving($attendanceDaily, $empDetails);
                        $status['duty_coverage'] = $this->calculateDutyCoverage(
                            $attendanceDaily->duty_start,
                            $attendanceDaily->duty_end,
                            $inTime,
                            $outTime
                        );
                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $status['status'] = $this->travelStatusName;
                            $status['remarks'] = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $status['remarks'] .= $employeeLeaveDetails->remarks;
                            $status['status'] = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $status['leave_status'] = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $status['leave_request_id'] = $employeeLeaveDetails->leaveRequest?->id;
                        }
                        $attendanceDaily->update($status);
                    } else {
                        $status = $this->checkStatus($empDetails, $yesterday, $inTime, $outTime);
                        $status['total_hours'] = Carbon::parse($attLog[1]->log_date, 'UTC')->diff(Carbon::parse($attLog[0]->log_date, 'UTC'))->format('%H:%I');
                        if ($status == 0)
                            break;
                        $attendance = new AttendanceModel;

                        $attendance->employee_id = $empDetails->employee_id;
                        $attendance->employee_code = $empDetails->employee_code;
                        $attendance->date_en = $yesterday;
                        $attendance->date_np = $yesterdayBs;
                        $attendance->in_time = $inTime;
                        $attendance->out_time = $outTime;
                        // $attendance->shift_id = $empDetails->shift_id;
                        $attendance->in_remarks = $status['in_remarks'];
                        $attendance->out_remarks = $status['out_remarks'];
                        $attendance->total_hours = $status['total_hours'];
                        $attendance->remarks = $status['remarks'];
                        $attendance->status = $status['status'];
                        $attendance->source = $source;
                        $attendance->source_ip = $source_ip;

                        $this->setDutyStartEndTimeForSaving($attendance, $empDetails);

                        $attendance->duty_coverage = $this->calculateDutyCoverage(
                            $attendance->duty_start,
                            $attendance->duty_end,
                            $attendance->in_time,
                            $attendance->out_time
                        );

                        if ($this->travelingEmployees[$empDetails->employee_id] ?? null) {
                            $attendance->in_time = $inTime;
                            $attendance->out_time = $outTime;
                            $attendance->status = $this->travelStatusName;
                            $attendance->remarks = ($this->travelingEmployees[$empDetails->employee_id]['title'] . " [" . $this->travelingEmployees[$empDetails->employee_id]['purpose_of_visit'] . "]") ?? "";
                        }

                        $employeeLeaveDetails = EmployeeLeaveDetail::where([['date', $yesterday], ['employee_id', $empDetails->employee_id]])
                            ->first();

                        if ($employeeLeaveDetails) {
                            $attendance->remarks .= $employeeLeaveDetails->remarks;
                            $attendance->status = $employeeLeaveDetails->leaveType?->name . " [" . $employeeLeaveDetails->leaveOption?->name . "]";
                            $attendance->leave_status = $employeeLeaveDetails->leaveType?->paid ? true : false;
                            $attendance->leave_request_id = $employeeLeaveDetails->leaveRequest?->id;
                        }

                        $attendance->save();
                    }
                    break;
            }
        }
    }

    // Get the details of break in and out of employees.
    public function getBreakInOut($department = null, $date = null)
    {
        if (is_null($date))
            $today = date('Y-m-d');
        // $departments = \App\Models\configs\Department::pluck('id')->toArray();  can use if particular department is needed, commented for now.
        $employees = OrgModel::selectRaw('SUBSTRING(employee_code,5) as employee_code,employee_id')
            // ->whereIn('department_id',$departments)
            ->whereNull('termination_date')
            ->pluck('employee_id', 'employee_code')
            ->toArray();
        $employeeIds = array_values($employees);
        $employeeName = EmployeeModel::whereIn('id', $employeeIds)
            ->get(['first_name', 'middle_name', 'last_name', 'id'])
            ->keyBy('id')
            ->toArray();
        $employeeCodes = array_keys($employees);
        $enrollments = array_map(function ($value) {
            return (int)$value;
        }, $employeeCodes);
        $breakInOut = DB::table('att_logs')
            ->where('log_date', 'like', $date . "%")
            ->whereIn('enrollment_no', $enrollments)
            ->whereIn('inout_mode', [2, 3])
            ->orderBy('log_date')
            ->get();
        $breakInOutArray = [];
        foreach ($breakInOut as $value) {
            if (!isset($breakInOutArray[$value->enrollment_no])) {
                $breakInOutArray[$value->enrollment_no] = [];
                if (isset($employees[$value->enrollment_no]))
                    $name = $employeeName[$employees[$value->enrollment_no]];
                else {
                    $newKey = "0" . $value->enrollment_no;
                    if (isset($employees[$newKey]))
                        $name = $employeeName[$employees[$newKey]];
                    else
                        continue;
                }
                $fullName = $name['first_name'];
                if (!is_null($name['middle_name']))
                    $fullName .= " " . $name['middle_name'];
                $fullName .= " " . $name['last_name'];
                $breakInOutArray[$value->enrollment_no]['employee'] = $fullName;
                $breakInOutArray[$value->enrollment_no]['emp_code'] = $value->enrollment_no;
                $breakInOutArray[$value->enrollment_no]['break_in'] = 0;
                $breakInOutArray[$value->enrollment_no]['break_out'] = 0;
            }
            if ($value->inout_mode == 2 || $value->inout_mode == 3) {
                $breakInOutArray[$value->enrollment_no]['break_in'] = Carbon::parse($value->log_date, 'UTC')->format('h:i:s A');
                $breakInTime = Carbon::parse($value->log_date, 'UTC')->format('h:i:s A');
                if ($breakInOutArray[$value->enrollment_no]['break_out'] == 0)
                    $breakInOutArray[$value->enrollment_no]['break_out'] = $breakInTime;
                if ($breakInTime < $breakInOutArray[$value->enrollment_no]['break_out'])
                    $breakInOutArray[$value->enrollment_no]['break_out'] = $breakInTime;
            }
            if ($breakInOutArray[$value->enrollment_no]['break_in'] != 0 && $breakInOutArray[$value->enrollment_no]['break_out'] != 0)
                $breakInOutArray[$value->enrollment_no]['hours'] = Carbon::parse($breakInOutArray[$value->enrollment_no]['break_in'])->diff(Carbon::parse($breakInOutArray[$value->enrollment_no]['break_out']))->format('%H:%I');
            else
                $breakInOutArray[$value->enrollment_no]['hours'] = "N/A";
        }
        return collect($breakInOutArray);
    }

    public function getTimeStatus($time, $condition, $employeeTime)
    {
        $_time = strtotime(Carbon::parse($time, 'UTC')->format('h:i:s A'));
        $_employeeTime = strtotime($employeeTime);

        switch ($condition) {
            case ">":
                if ($_employeeTime > $_time)
                    return true;
                break;
            case ">=":
                if ($_employeeTime >= $_time)
                    return true;
                break;
            case "=":
                if ($_employeeTime == $_time)
                    return true;
                break;
            case "<":
                if ($_employeeTime < $_time)
                    return true;
                break;
            case "<=":
                if ($_employeeTime <= $_time)
                    return true;
                break;
        }
    }

    private function setDutyStartEndTimeForSaving(AttendanceModel $attendance, EmployeeOrg $empOrg)
    {
        if ($attendance->shift_id) {
            $dutyTime = $empOrg->employee?->dutyTime($attendance->date_en, $attendance);
        } else {
            $dutyTime = $empOrg->employee?->dutyTime($attendance->date_en);
        }
        $shift = $dutyTime['shift'];
        $attendance->duty_start =  $shift?->start_time;
        $attendance->duty_end =  $shift?->end_time;
        $attendance->shift_id = $shift?->id;
    }

    private function determineSource($attLog): string
    {
        if (($attLog->device_id ?? "") == $this->empAppId)
            return "App";
        else if (($attLog->device_id ?? "") == $this->webAppId)
            return "Web";
        else if (($attLog->device_id ?? "") == Constant::HRM_APP_ID) {
            return "HRM App";
        } else
            return "Biometric Device";
    }

    private function shiftYetToBeStarted($date_en): array
    {
        if (isset($date_en) && date("Y-m-d") == $date_en) {
            $currentTime = date("H:i:s");

            return EmployeeShift::where("start_time", ">", $currentTime)
                ->where("is_active", 1)
                ->pluck("id")
                ->toArray();
        }
        return [];
    }

    private function shiftEnded($date_en): array
    {
        if (isset($date_en) && date("Y-m-d") == $date_en) {
            $currentTime = date("H:i:s");

            return EmployeeShift::where("end_time", "<", $currentTime)
                ->where("is_active", 1)
                ->pluck("id")
                ->toArray();
        }
        return [];
    }
}
