<?php

namespace App\Jobs;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;


class SendAssignNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract & Model $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(\App\Http\Repositories\Configs\Interfaces\INotificationRepository $notificationRepo): void
    {
        $ticket = $this->model->requestTicket;
        $assigner = $ticket?->submittedBy;
        $recipient = $ticket?->employee;

        if ($ticket->workflow === WorkflowName::TIME_REQUEST_APPROVAL) {
            $title = "Time Request Assigned";
            $message = "You have been assigned a time request for {$this->model->nep_date} by {$assigner?->name}";
        } else if ($ticket->workflow === WorkflowName::LEAVE_APPROVAL) {
            $title = "Leave Assigned";
            // $message = "You have been assigned a {$this->model->leaveType?->name} ({$this->model->nep_start_date} - {$this->model->nep_end_date}) by {$assigner?->name}",
            $message = "You have been assigned a {$this->model->leaveType?->name} by {$assigner?->name}";
        } else if ($ticket->workflow === WorkflowName::IRREGULARITY_TICKET) {
            $title = "Irregularity Request Assigned";
            $message = "You have been assigned a irregularity request for {$this->model->type}.";
        }

        $user = $recipient?->user;
        try {
            app(\App\Http\Repositories\TicketRepository::class)->createAndSendTicketNotification(
                $user,
                $title,
                $message,
                $ticket
            );
        } catch (\Throwable $e) {
            logError("Error sending notification", $e, [
                'user_id' => $user->id,
                'ticket_id' => $ticket->id,
                'workflow' => $ticket->workflow
            ]);
        }
    }
}
