<?php

namespace App\Jobs;

use App\Http\Repositories\Configs\NotificationRepository;
use App\Http\Services\FirebaseNotificationService;
use App\Http\Services\NotificationTopicService;
use App\Models\CustomNotification;
use App\Notifications\TaskTicket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class CustomNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public CustomNotification $notification,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(
        NotificationTopicService $notificationTopicService,
        FirebaseNotificationService $firebaseNotificationService
    ): void {
        try {
            $this->notification->markAsPending();

            $users = $notificationTopicService->getNotificationUsers(
                $this->notification->target_type,
                $this->notification->target_ids
            );

            $notification = new TaskTicket($this->notification->title, $this->notification->body);

            $platforms = collect($this->notification->platforms);

            // Process in-app notifications if needed
            if ($platforms->contains('in_app')) {
                foreach ($users as $user) {
                    $user->notify($notification);
                }
                $platforms = $platforms->reject(fn($platform) => $platform === 'in_app');
                logCronInfo("Notification with title '{$this->notification->title}' sent in-app");
                echo "Notification with title '{$this->notification->title}' sent in-app\n";
            }

            // Skip further processing if no external platforms remain
            if ($platforms->isEmpty() || !config('app.firebaseNotification')) {
                $this->notification->markAsSent();
                return;
            }

            $topics = [];
            if ($this->notification->target === 'all') {
                foreach ($platforms as $platform) {
                    $topics[] = $notificationTopicService->getBaseTopic($platform);
                }
            } else {
                foreach ($platforms as $platform) {
                    $platformTopics = $notificationTopicService->notificationTopics(
                        $this->notification->target,
                        $this->notification->target_ids,
                        $platform
                    );

                    $topics = array_merge($topics, $platformTopics);
                }
            }

            foreach ($topics as $topic) {
                $firebaseNotificationService->sendToTopic(
                    $topic,
                    $this->notification->title,
                    $this->notification->body,
                    $this->notification->payload ?? []
                );

                $logMessage = "Notification with title '{$this->notification->title}' sent to topic $topic";
                logCronInfo($logMessage);
                echo "Notification with title '{$this->notification->title}' sent to topic $topic\n";
            }

            $this->notification->markAsSent();
        } catch (Throwable $e) {
            $errorMessage = "FCM Topic Notification Job Failed: {$e->getMessage()}";
            $this->notification->markAsFailed($errorMessage);
            logCronError($errorMessage);
            throw $e; // Re-throw to let Laravel job system handle it
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $error): void
    {
        $errorMessage = "FCM Topic Notification Job Failed: {$error->getMessage()} in {$error->getFile()} on line {$error->getLine()}";
        logCronError($errorMessage);
        $this->notification->markAsFailed($errorMessage);
    }
}
