<?php

namespace App\Jobs;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;

class SendAllocateNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    /**
     * Create a new job instance.
     * @param StateableModelContract&Model $model
     * @param string $from
     * @param string $to
     * @param array $parameters
     */
    public function __construct(StateableModelContract & Model $model, string $from, string $to, array $parameters = [])
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    /**
     * Execute the job.
     */
    public function handle(\App\Http\Repositories\Configs\Interfaces\INotificationRepository $notificationRepo): void
    {
        $ticket = $this->model->requestTicket;
        $assigner = $ticket?->submittedBy;
        $recipient = $ticket?->employee;
        $currentOwner = $ticket?->currentOwner;

        if ($ticket->workflow === WorkflowName::TIME_REQUEST_APPROVAL) {
            $title = "Time Request Allocated for {$recipient?->name}";
            $message = "{$assigner?->name} has allocated time request and sent for approval";
        } else if ($ticket->workflow === WorkflowName::LEAVE_APPROVAL) {
            $title = "Leave Request Allocated for {$recipient?->name}";
            $message = "{$assigner?->name} has allocated {$this->model->leaveType?->name} and sent for approval";
        }

        $user = $currentOwner?->user;
        try {
            app(\App\Http\Repositories\TicketRepository::class)->createAndSendTicketNotification(
                $user,
                $title,
                $message,
                $ticket
            );
        } catch (\Throwable $e) {
            logError("Error sending notification", $e, [
                'user_id' => $user->id,
                'ticket_id' => $ticket->id,
                'workflow' => $ticket->workflow
            ]);
        }
    }
}
