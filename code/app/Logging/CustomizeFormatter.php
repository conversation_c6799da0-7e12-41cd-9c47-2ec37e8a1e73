<?php

namespace App\Logging;

use Illuminate\Log\Logger;

class CustomizeFormatter
{
  /**
   * Customize the given logger instance.
   *
   * @param  \Illuminate\Log\Logger  $logger
   * @return void
   */
  public function __invoke(Logger $logger)
  {
    foreach ($logger->getHandlers() as $handler) {
      $handler->pushProcessor(function ($record) {
        // $record['extra']['user_id'] = auth()->user()->id;
        $record['extra']['employee_id'] = currentEmployee()?->id;
        return $record;
      });
    }
  }
}
