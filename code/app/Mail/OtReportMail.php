<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OtReportMail extends Mailable
{
    use Queueable, SerializesModels;


    public $excelContent;
    public $filename, $layout, $view;

    /**
     * Create a new message instance.
     */
    public function __construct($excelContent, $filename)
    {
        if (fedexHrm()) {
            $this->layout = 'emails.layouts.fedexEmailLayout';
        } elseif (vianetHrm()) {
            $this->layout = 'emails.layouts.vianetEmailLayout';
        } elseif (konnectHrm()) {
            $this->layout = 'emails.layouts.konnectEmailLayout';
        } else {
            $this->layout = 'emails.layouts.defaultEmailLayout'; // fallback
        }

        $this->excelContent = $excelContent;
        $this->filename = $filename ?? 'ot-report.xlsx';
        $this->view = 'emails.otReport';
    }

    /**
     * Define the envelope (subject, recipients etc.)
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->filename,
        );
    }

    /**
     * Define the content (email body view and variables)
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'layout' => $this->layout,
                'filename' => $this->filename,
            ]
        );
    }

    /**
     * Attach the Excel file to the email
     */
    public function attachments(): array
    {
        return [
            Attachment::fromData(fn() => $this->excelContent, $this->filename)
                ->withMime('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
        ];
    }
}
