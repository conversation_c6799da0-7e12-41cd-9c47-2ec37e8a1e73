<?php

namespace App\Mail;

use App\Models\configs\MailConfigurationSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Support\Facades\Log;

class OfferLetterMail extends Mailable
{
    use Queueable, SerializesModels;

    public $edfMeta;
    public $layout, $view;
    public $companyName;
    public $designation;
    public $address;
    public $authorizedSignatory;
    public $signatoryDesignation;
    public $companyId;
    public $ctcOffer;
    public $dojEng;
    public $files = [];

    /**
     * Create a new message instance.
     */
    public function __construct($edfMeta, $companyDetails = [], $attachments = [])
    {
        $this->edfMeta = $edfMeta;

        $this->companyName = $companyDetails['company_name'] ?? $this->getDefaultCompanyName();
        $this->address = $companyDetails['address'] ?? $this->getDefaultAddress();
        $this->authorizedSignatory = $companyDetails['authorized_signatory'] ?? $this->getDefaultSignatory();
        $this->signatoryDesignation = $companyDetails['signatory_designation'] ?? $this->getDefaultSignatoryDesignation();

        $this->view = 'emails.edfOfferLetterEmail';
        $this->designation = $edfMeta['designation'] ?? 'To be determined';
        $this->ctcOffer = $edfMeta['ctc_offer'] ?? 'To be determined';
        $this->dojEng = $companyDetails['doj_eng'] ?? '';
        $this->layout = $this->getCompanyLayout();

        // Debug the attachments parameter
        Log::info('OfferLetterMail constructor called', [
            'attachments_type' => gettype($attachments),
            'attachments_value' => $attachments,
            'is_array' => is_array($attachments)
        ]);

        $this->files = is_array($attachments) ? $attachments : [];
    }

    /**
     * Get company layout based on company ID or name
     */
    private function getCompanyLayout()
    {
        if (fedexHrm()) {
            return 'emails.layouts.fedexEmailLayout';
        } elseif (vianetHrm()) {
            return 'emails.layouts.vianetEmailLayout';
        } elseif (konnectHrm()) {
            return 'emails.layouts.konnectEmailLayout';
        }
        return 'emails.layouts.vianetEmailLayout';
    }

    /**
     * Get default company name based on helper functions (fallback)
     */
    private function getDefaultCompanyName()
    {
        if (fedexHrm()) {
            return 'FedEx';
        } elseif (vianetHrm()) {
            return 'Vianet';
        } elseif (konnectHrm()) {
            return 'Konnect';
        }
        return 'Our Company';
    }

    /**
     * Get default address
     */
    private function getDefaultAddress()
    {
        if (fedexHrm()) {
            return 'FedEx Corporate Address';
        } elseif (vianetHrm()) {
            return 'Vianet Corporate Address';
        } elseif (konnectHrm()) {
            return 'Konnect Corporate Address';
        }
        return 'Company Corporate Address';
    }

    /**
     * Get default authorized signatory
     */
    private function getDefaultSignatory()
    {
        if (fedexHrm()) {
            return 'FedEx HR Manager';
        } elseif (vianetHrm()) {
            return 'Vianet HR Manager';
        } elseif (konnectHrm()) {
            return 'Konnect HR Manager';
        }
        return 'HR Manager';
    }

    /**
     * Get default signatory designation
     */
    private function getDefaultSignatoryDesignation()
    {
        if (fedexHrm()) {
            return 'Human Resources Manager';
        } elseif (vianetHrm()) {
            return 'Human Resources Manager';
        } elseif (konnectHrm()) {
            return 'Human Resources Manager';
        }
        return 'Human Resources Manager';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Offer Letter - {$this->companyName}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'edfMeta' => $this->edfMeta,
                'layout' => $this->layout,
                'companyName' => $this->companyName,
                'designation' => $this->designation,
                'ctcOffer' => $this->ctcOffer,
                'dojEng' => $this->dojEng,
                'address' => $this->address,
                'authorizedSignatory' => $this->authorizedSignatory,
                'signatoryDesignation' => $this->signatoryDesignation,
                'currentDate' => now()->format('F d, Y'),
                'attachments' => is_array($this->files) ? $this->files : []
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];

        foreach ((array)$this->files as $path) {
            if (!is_string($path) || !file_exists($path)) {
                continue;
            }

            try {
                Log::info('Creating attachment from path', ['path' => $path]);

                $fileName = $this->generateCleanFileName($path);
                Log::info('Filename processing details', [
                    'original_path' => $path,
                    'original_filename' => pathinfo($path, PATHINFO_FILENAME),
                    'clean_filename' => $fileName,
                    'file_exists' => file_exists($path),
                    'file_size' => filesize($path)
                ]);
                Log::info('Clean file name generated', ['name' => $fileName]);
                $attachment = \Illuminate\Mail\Mailables\Attachment::fromPath($path)
                    ->as($fileName)
                    ->withMime('application/pdf');

                $attachments[] = $attachment;

                Log::info('Attachment created successfully', [
                    'original_path' => $path,
                    'file_name' => $fileName
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to create attachment from path: ' . $path, [
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Final attachments array', [
            'count' => count($attachments),
            'attachments' => array_map('get_class', $attachments)
        ]);

        return $attachments;
    }

    /**
     * Generate a clean file name from file path
     */
    protected function generateCleanFileName(string $path): string
    {
        $originalName = pathinfo($path, PATHINFO_FILENAME);
        $extension = pathinfo($path, PATHINFO_EXTENSION);

        // Extract base name (part before first separator)
        $baseName = $this->extractBaseName($originalName);

        // Clean and format the name
        $cleanName = $this->formatCleanName($baseName);

        return $cleanName . '.' . $extension;
    }

    /**
     * Extract base name from original filename
     */
    protected function extractBaseName(string $originalName): string
    {
        $parts = preg_split('/[_\-]+/', $originalName, 2);
        return $parts[0] ?? $originalName;
    }

    /**
     * Clean and format the filename according to rules
     */
    protected function formatCleanName(string $name): string
    {
        // Remove special characters, keep only letters, numbers, and spaces
        $clean = preg_replace('/[^A-Za-z0-9 ]+/', ' ', $name);

        // Collapse multiple spaces and trim
        $clean = trim(preg_replace('/\s+/', ' ', $clean));

        // Apply naming rules
        return $this->applyNamingRules($clean);
    }

    /**
     * Apply uppercase/sentence case rules based on length
     */
    protected function applyNamingRules(string $name): string
    {
        if (empty($name)) {
            return 'Attachment';
        }

        if (strlen($name) <= 4) {
            return strtoupper($name);
        }

        return ucwords(strtolower($name));
    }
}
