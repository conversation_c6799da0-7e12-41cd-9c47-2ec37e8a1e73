<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ConsecutiveAttendanceReportMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $fileName;
    public $excelContent;
    public $layout;
    public $startDate;
    public $endDate;

    /**
     * Create a new message instance.
     */
    public function __construct($data, $fileName, $excelContent, $startDate, $endDate)
    {
        $this->data = $data;
        $this->fileName = $fileName;
        $this->excelContent = $excelContent;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->layout = 'emails.layouts.vianetEmailLayout';
    }

    /**
     * Define email subject.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Consecutive Attendance Report',
        );
    }

    /**
     * Define email content.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.consecutiveAttendanceReport',
            with: [
                'filename' => $this->fileName,
                'data' => $this->data,
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
                'layout' => $this->layout,
            ]
        );
    }
    public function attachments()
    {
        $hasExceptions = collect($this->data)->contains(function ($record) {
            $status = strtolower($record['status'] ?? '');
            return
                str_contains($status, 'absent') ||
                str_contains($status, 'late in') ||
                str_contains($status, 'early out');
        });

        if ($hasExceptions) {
            return [
                Attachment::fromData(fn() => $this->excelContent, $this->fileName)
                    ->withMime('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
            ];
        }
        return [];
    }
}
