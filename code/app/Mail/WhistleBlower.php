<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class WhistleBlower extends Mailable
{
    use Queueable, SerializesModels;
    public $layout, $views, $formData;

    /**
     * Create a new message instance.
     */
    public function __construct($formData)
    {
        // Dynamically set the layout
        if (fedexHrm()) {
            $this->layout = 'emails.layouts.fedexEmailLayout';
        } elseif (vianetHrm()) {
            $this->layout = 'emails.layouts.vianetEmailLayout';
        } elseif (konnectHrm()) {
            $this->layout = 'emails.layouts.konnectEmailLayout';
        }
        $this->view = 'emails.whistleBlower';
        $this->formData = $formData;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Submission of Whistleblower Form',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'layout' => $this->layout,
                'formData' => $this->formData,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
