<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class MailToDeveloper extends Mailable
{
    use Queueable, SerializesModels;

    public $title = "", $description = "";
    public $layout , $views; 

    /**
     * Create a new message instance.
     */
    public function __construct($title, $message)
    {
        $this->title = $title;
        $this->description = $message;

         // Dynamically set the layout
         if (fedexHrm()) {
            $this->layout = 'emails.layouts.fedexEmailLayout';
        } elseif (vianetHrm()) {
            $this->layout = 'emails.layouts.vianetEmailLayout';
        } elseif (konnectHrm()) {
            $this->layout = 'emails.layouts.konnectEmailLayout';
        }
        $this->view = 'emails.mailToDeveloper';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Tamper in the system',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with:[
                'title' => $this->title,
                'description' => $this->description,
                'layout' => $this->layout
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
