<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ResetPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    public $username = "", $password = "", $name = "";
    public $layout, $views;

    /**
     * Create a new message instance.
     */
    public function __construct($username, $password, $name = "")
    {
        $this->username = $username;
        $this->password = $password;
        $this->name = $name;

        // Dynamically set the layout
        if (fedexHrm()) {
            $this->layout = 'emails.layouts.fedexEmailLayout';
            $this->view = 'emails.resetPassword.resetPasswordFedEx';
        } elseif (vianetHrm()) {
            $this->layout = 'emails.layouts.vianetEmailLayout';
            $this->view = 'emails.resetPassword.resetPasswordVianet';
        } elseif (konnectHrm()) {
            $this->layout = 'emails.layouts.konnectEmailLayout';
            $this->view = 'emails.resetPassword.resetPasswordKonnect';
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your New Password',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'username' => $this->username,
                'password' => $this->password,
                'name' => $this->name,
                'layout' => $this->layout
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
