<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EmployeeTerminationEmail extends Mailable
{
    use Queueable, SerializesModels;

    private $data, $date;
    private $layout, $views;

    /**
     * Create a new message instance.
     */
    public function __construct($data, $date)
    {
        $this->data = $data;
        $this->date = $date;
        if (fedexHrm()) {
            $this->layout = 'emails.layouts.fedexEmailLayout';
        } elseif (vianetHrm()) {
            $this->layout = 'emails.layouts.vianetEmailLayout';
        } elseif (konnectHrm()) {
            $this->layout = 'emails.layouts.konnectEmailLayout';
        }
        $this->view = 'emails.employeeTermination';
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Employee Termination List {{$this->date}}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view,
            with: [
                'data' => $this->data,
                'date' => $this->date,
                'layout' => $this->layout
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
