<?php

namespace App\Mail;

use App\Models\Tickets\ManpowerRequisition;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class NewManpowerRequisition extends Mailable
{
    use Queueable, SerializesModels;

    public ManpowerRequisition $requisition;
    public string $ticketLink;

    /**
     * Create a new message instance.
     */
    public function __construct(ManpowerRequisition $requisition, string $ticketLink)
    {
        $this->requisition = $requisition;
        $this->ticketLink = $ticketLink;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Manpower Requisition',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.newManpowerRequisition',
            with: [
                'initiator' => $this->requisition->employee->name,
                'positionDetails' => [
                    'Company' => $this->requisition->company?->name,
                    'Branch' => $this->requisition->branch->name,
                    'Department' => $this->requisition->department->department,
                    ...($this->requisition->unit ? ['Unit' => $this->requisition->unit?->name] : []),
                    'Position Type' => $this->requisition->positionType->name,
                ],
                'payrollDescription' => [
                    'Designation' => $this->requisition->designation->title,
                ],
                'requisitionDetails' => [
                    'Requisition Type' => $this->requisition->type,
                    'Number of Vacancy' => $this->requisition->number_vacancy,
                    'Timeline' => $this->requisition->timeline,
                    'Justification' => $this->requisition->justification,
                ],
                'jobDescription' => [
                    'Job Profile' => $this->requisition->job->name,
                    'Qualification' => $this->requisition->qualification,
                    'Experience' => $this->requisition->experience,
                    'Skills' => $this->requisition->skills,
                    'Responsibilities' => $this->requisition->responsibilities,
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];
        foreach ($this->requisition?->requestTicket?->documents ?? [] as $document) {
            $path = 'public/'.$document->document;
            $mimeType = Storage::mimeType($path);
            array_push($attachments, \Illuminate\Mail\Mailables\Attachment::fromStorage($path)->withMime($mimeType));
        }
        return $attachments;
    }
}
