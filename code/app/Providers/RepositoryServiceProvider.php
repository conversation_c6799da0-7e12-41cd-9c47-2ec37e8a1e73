<?php

namespace App\Providers;

use App\Http\Repositories\Auth\DeviceLogRepository;
use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\Auth\Interfaces\ILoginAppRepository;
use App\Http\Repositories\Auth\LoginAppRepository;
use App\Http\Repositories\Configs\CustomNotificationRepository;
use App\Http\Repositories\Configs\Interfaces\ICustomNotificationRepository;
use App\Http\Repositories\Configs\Interfaces\INotificationRepository;
use App\Http\Repositories\Configs\NotificationRepository;
use App\Http\Repositories\DashboardRepository;
use App\Http\Repositories\Interfaces\IDashboardRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->bind(ILoginAppRepository::class, LoginAppRepository::class);
        $this->app->bind(IDeviceLogRepository::class, DeviceLogRepository::class);
        $this->app->bind(INotificationRepository::class, NotificationRepository::class);
        $this->app->bind(ICustomNotificationRepository::class, CustomNotificationRepository::class);
        $this->app->bind(IDashboardRepository::class, DashboardRepository::class);
        $this->app->bind(\App\Http\Repositories\Setting\Interfaces\SettingRepositoryInterface::class, \App\Http\Repositories\Setting\SettingRepository::class);
        $this->app->bind(\App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface::class, \App\Http\Repositories\Setting\AppSettingRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\TeamTypes\Interfaces\TeamTypeRepositoryInterface::class, \App\Http\Repositories\FieldTeam\TeamTypes\TeamTypeRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\TransportationTypes\Interfaces\TransportationTypeRepositoryInterface::class, \App\Http\Repositories\FieldTeam\TransportationTypes\TransportationTypesRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface::class, \App\Http\Repositories\FieldTeam\TeamList\TeamListRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface::class, \App\Http\Repositories\FieldTeam\Team\TeamRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\TeamTicket\Interfaces\TeamOtRequestRepositoryInterface::class, \App\Http\Repositories\FieldTeam\TeamTicket\TeamOtRequestRepository::class);
        $this->app->bind(\App\Http\Repositories\Interfaces\ILeaveDashboardRepository::class, \App\Http\Repositories\LeaveDashboardRepository::class);
        $this->app->bind(\App\Http\Repositories\OtReport\Interfaces\OtAttendanceReportRepositoryInterface::class, \App\Http\Repositories\OtReport\OtAttendanceReportRepository::class);
        $this->app->bind(\App\Http\Repositories\OtReport\Interfaces\OtEmployeeReportRepositoryInterface::class, \App\Http\Repositories\OtReport\OtEmployeeReportRepository::class);
        $this->app->bind(\App\Http\Repositories\FieldTeam\Report\Interfaces\FieldTeamAttendanceRepositoryInterface::class, \App\Http\Repositories\FieldTeam\Report\FieldTeamAttendanceRepository::class);
        $this->app->bind(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class, \App\Http\Repositories\Configs\JobSeatRepository::class);
        $this->app->bind(\App\Http\Repositories\Mail\Interfaces\ManpowerRequisitionMailInterface::class, \App\Http\Repositories\Mail\ManpowerRequisitionMailRepository::class);
        $this->app->bind(\App\Http\Repositories\Configs\Interfaces\JobRepositoryInterface::class, \App\Http\Repositories\Configs\JobRepository::class);

        $this->app->bind(\CodeBright\OauthAnd2fa\Contracts\OidcUserInfoProvider::class, \App\Http\Services\Oidc\AppUserInfoProvider::class);

        $this->app->bind(
            \App\Http\Repositories\Configs\Interfaces\TemporaryAccessRepositoryInterface::class,
            \App\Http\Repositories\Configs\TemporaryAccessRepository::class
        );
    }
}
