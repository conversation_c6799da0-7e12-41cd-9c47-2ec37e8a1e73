<?php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use Illuminate\Support\Str;
use Laravel\Fortify\Fortify;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Contracts\LoginResponse as LoginResponseContract;
use Laravel\Fortify\Contracts\TwoFactorLoginResponse as TwoFactorLoginResponseContract;
use App\Http\Responses\CustomTwoFactorLoginResponse;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class FortifyServiceProvider extends ServiceProvider
{
    public function register(): void {}

    public function boot(): void
    {
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        $this->app->singleton(LoginResponseContract::class, CustomTwoFactorLoginResponse::class);
        $this->app->singleton(TwoFactorLoginResponseContract::class, CustomTwoFactorLoginResponse::class);

        Fortify::authenticateUsing(function (Request $request) {
            $loginRepo = new \App\Http\Repositories\Auth\LoginRepository;
            return $loginRepo->fortifyCustomLogin($request);
        });

        RateLimiter::for('login', function (Request $request) {
            $ip = $this->getIp()['address'] ?? '';
            $email = Str::lower($request->input(Fortify::username()));

            $maxAttemptsForIp = 10;
            $maxAttemptsForEmail = 5;
            $maxAttemptsForUsername = 10;
            $decaySecondsForIp = 300;
            $decaySecondsForUsername = 240;
            $decaySecondsForEmail = 120;

            $hash = sha1($ip . '|' . $request->device_id);
            $ipKey = "login_ip|{$hash}";
            $emailKey = "login_email|" . Str::transliterate($email) . "|{$ip}";
            $usernameKey = "login_username|" . Str::transliterate($email);

            Log::info('Username: ' . Str::transliterate(Str::lower($request->input(Fortify::username()))) . ', IP: ' . $ip . ', IP Type: ' . $this->getIp()['type'] ?? '' . ', Login throttleKey: ' . Str::transliterate($ipKey));
            // Check Username-based throttle. This has been added to prevent brute force attacks on specific usernames as seen in Kubernetes hosted application.
            if (RateLimiter::tooManyAttempts($usernameKey, $maxAttemptsForUsername)) {
                session()->flash('isRateLimited', true);
                $seconds = Cache::get($usernameKey . ':timer')
                    ? $decaySecondsForUsername
                    : RateLimiter::availableIn($usernameKey);
                Cache::put($usernameKey . ':timer', true, $decaySecondsForUsername);

                $attempts = RateLimiter::hit($usernameKey);

                // Log only if attempts exceed max
                if ($attempts > $maxAttemptsForUsername) {
                    Log::error('Username Throttle limit exceeded for Username and IP', [
                        'username' => $email,
                        'ip' => $ip,
                        'attempts' => $attempts,
                        'max_attempts' => $maxAttemptsForUsername,
                        'time_remaining' => $seconds,
                    ]);

                    if (config('app.environment') != "development" && class_exists(\App\Jobs\MailToDeveloper::class)) {
                        $title = "Username Based Rate Limit Activated";
                        $message = "Rate limit triggered for $email on IP: $ip";
                        \App\Jobs\MailToDeveloper::dispatch($title, $message);
                    }
                }
                throw ValidationException::withMessages([
                    'username' => trans('auth.throttle', [
                        'seconds' => $seconds
                    ])
                ]);
            }

            // Check IP-based throttle first (prevents rapid attempts)
            if (RateLimiter::tooManyAttempts($ipKey, $maxAttemptsForIp)) {
                session()->flash('isRateLimited', true);
                $seconds = Cache::get($ipKey . ':timer')
                    ? $decaySecondsForIp
                    : RateLimiter::availableIn($ipKey);
                Cache::put($ipKey . ':timer', true, $decaySecondsForIp);

                $attempts = RateLimiter::hit($ipKey);

                // Log only if attempts exceed max
                if ($attempts > $maxAttemptsForIp) {
                    Log::error('IP Throttle limit exceeded for Username and IP', [
                        'username' => $email,
                        'ip' => $ip,
                        'attempts' => $attempts,
                        'max_attempts' => $maxAttemptsForIp,
                        'time_remaining' => $seconds,
                    ]);

                    if (config('app.environment') != "development" && class_exists(\App\Jobs\MailToDeveloper::class)) {
                        $title = "IP Based Rate Limit Activated";
                        $message = "Rate limit triggered for $email on IP: $ip";
                        \App\Jobs\MailToDeveloper::dispatch($title, $message);
                    }
                }
                throw ValidationException::withMessages([
                    'username' => trans('auth.throttle', [
                        'seconds' => $seconds
                    ])
                ]);
            }

            // Check email+IP throttle (prevents targeting specific accounts)
            if (RateLimiter::tooManyAttempts($emailKey, $maxAttemptsForEmail)) {
                session()->flash('isRateLimited', true);
                $seconds = Cache::get($emailKey . ':timer')
                    ? $decaySecondsForEmail
                    : RateLimiter::availableIn($emailKey);
                Cache::put($emailKey . ':timer', true, $decaySecondsForEmail);

                $attempts = RateLimiter::hit($emailKey);

                if ($attempts > $maxAttemptsForEmail) {

                    Log::error('Username + IP Throttle limit exceeded for Username and IP', [
                        'username' => $email,
                        'ip' => $ip,
                        'attempts' => $attempts,
                        'max_attempts' => $maxAttemptsForEmail,
                        'time_remaining' => $seconds,
                    ]);
                }
                throw ValidationException::withMessages([
                    'username' => trans('auth.throttle', [
                        'seconds' => $seconds
                    ])
                ]);
            }

            // Record the attempts if within limits
            RateLimiter::hit($ipKey);
            RateLimiter::hit($emailKey);

            return [
                Limit::perMinutes($decaySecondsForIp / 60, $maxAttemptsForIp)->by($ipKey),
                Limit::perMinutes($decaySecondsForEmail / 60, $maxAttemptsForEmail)->by($emailKey)
            ];
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });

        // Causing issue in 2FA flow so commented out
        // Fortify::loginView(function () {
        //     return view('login');
        // });

        // Fortify::confirmPasswordView(function () {
        //     return view('oauth-and-2fa::confirm-password');
        // });

        // Fortify::twoFactorChallengeView(function () {
        //     return view('oauth-and-2fa::two-factor-challenge');
        // });
    }

    function getIp()
    {
        $ipType = 'X-Real-Ip';
        $ipAddress = request()->header('X-Real-Ip') ?? null;
        if ($ipAddress === null) {
            $ipType = 'X-Forwarded-For';
            $ipAddress = request()->header('X-Forwarded-For') ?? null;
        }

        if (filter_var($ipAddress, FILTER_VALIDATE_IP))
            return ['type' => $ipType, 'address' => $ipAddress];
        else {
            return ['type' => 'Request-Ip', 'address' => request()->ip()];
        }
    }
}
