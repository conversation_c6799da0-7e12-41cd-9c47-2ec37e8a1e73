<?php

namespace App\Facades;

use App\Traits\NepaliCalenderTrait;

class LaravelNepaliDate
{
  private $bs = array(
    0 => array(2000, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    1 => array(2001, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    2 => array(2002, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    3 => array(2003, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    4 => array(2004, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    5 => array(2005, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    6 => array(2006, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    7 => array(2007, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    8 => array(2008, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 29, 31),
    9 => array(2009, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    10 => array(2010, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    11 => array(2011, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    12 => array(2012, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 30, 30),
    13 => array(2013, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    14 => array(2014, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    15 => array(2015, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    16 => array(2016, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 30, 30),
    17 => array(2017, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    18 => array(2018, 31, 32, 31, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    19 => array(2019, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    20 => array(2020, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    21 => array(2021, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    22 => array(2022, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30),
    23 => array(2023, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    24 => array(2024, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    25 => array(2025, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    26 => array(2026, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    27 => array(2027, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    28 => array(2028, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    29 => array(2029, 31, 31, 32, 31, 32, 30, 30, 29, 30, 29, 30, 30),
    30 => array(2030, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    31 => array(2031, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    32 => array(2032, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    33 => array(2033, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    34 => array(2034, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    35 => array(2035, 30, 32, 31, 32, 31, 31, 29, 30, 30, 29, 29, 31),
    36 => array(2036, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    37 => array(2037, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    38 => array(2038, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    39 => array(2039, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 30, 30),
    40 => array(2040, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    41 => array(2041, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    42 => array(2042, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    43 => array(2043, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 30, 30),
    44 => array(2044, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    45 => array(2045, 31, 32, 31, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    46 => array(2046, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    47 => array(2047, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    48 => array(2048, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    49 => array(2049, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30),
    50 => array(2050, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    51 => array(2051, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    52 => array(2052, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    53 => array(2053, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30),
    54 => array(2054, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    55 => array(2055, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    56 => array(2056, 31, 31, 32, 31, 32, 30, 30, 29, 30, 29, 30, 30),
    57 => array(2057, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    58 => array(2058, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    59 => array(2059, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    60 => array(2060, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    61 => array(2061, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    62 => array(2062, 30, 32, 31, 32, 31, 31, 29, 30, 29, 30, 29, 31),
    63 => array(2063, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    64 => array(2064, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    65 => array(2065, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    66 => array(2066, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 29, 31),
    67 => array(2067, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    68 => array(2068, 31, 31, 32, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    69 => array(2069, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    70 => array(2070, 31, 31, 31, 32, 31, 31, 29, 30, 30, 29, 30, 30),
    71 => array(2071, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    72 => array(2072, 31, 32, 31, 32, 31, 30, 30, 29, 30, 29, 30, 30),
    73 => array(2073, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 31),
    74 => array(2074, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    75 => array(2075, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    76 => array(2076, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30),
    77 => array(2077, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 29, 31),
    78 => array(2078, 31, 31, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30),
    79 => array(2079, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    80 => array(2080, 31, 32, 31, 32, 31, 30, 30, 30, 29, 29, 30, 30),
    81 => array(2081, 31, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30),
    82 => array(2082, 31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30),
    83 => array(2083, 31, 31, 32, 31, 31, 30, 30, 30, 29, 30, 30, 30),
    84 => array(2084, 31, 31, 32, 31, 31, 30, 30, 30, 29, 30, 30, 30),
    85 => array(2085, 31, 32, 31, 32, 30, 31, 30, 30, 29, 30, 30, 30),
    86 => array(2086, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30),
    87 => array(2087, 31, 31, 32, 31, 31, 31, 30, 30, 29, 30, 30, 30),
    88 => array(2088, 30, 31, 32, 32, 30, 31, 30, 30, 29, 30, 30, 30),
    89 => array(2089, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30),
    90 => array(2090, 30, 32, 31, 32, 31, 30, 30, 30, 29, 30, 30, 30)
  );

  private $nep_date = array('year' => '', 'month' => '', 'date' => '', 'day' => '', 'nmonth' => '', 'num_day' => '');
  private $eng_date = array('year' => '', 'month' => '', 'date' => '', 'day' => '', 'emonth' => '', 'num_day' => '');
  public $debug_info = "";

  /***
   * return nepai month in array
   *
   * @param integer $year
   * @return boolean
   */
  public function nep_months()
  {
    $toreturn = array();
    for ($i = 1; $i <= 12; $i++) {
      $toreturn[$i] = $this->get_nepali_month($i);
    }
    return $toreturn;
  }
  public function eng_months()
  {
    $toreturn = array();
    for ($i = 1; $i <= 12; $i++) {
      if ($i < 10) {
        $toreturn['0' . $i] = $this->get_english_month($i);
      } else {
        $toreturn[$i] = $this->get_english_month($i);
      }
    }
    return $toreturn;
  }

  /**
   * Calculates wheather english year is leap year or not
   *
   * @param integer $year
   * @return boolean
   */
  public function is_leap_year($year)
  {
    $a = $year;
    if ($a % 100 == 0) {
      if ($a % 400 == 0) {
        return true;
      } else {
        return false;
      }
    } else {
      if ($a % 4 == 0) {
        return true;
      } else {
        return false;
      }
    }
  }

  public function get_nepali_month($m)
  {
    $n_month = false;

    switch ($m) {
      case 1:
        $n_month = "Baisakh";
        break;

      case 2:
        $n_month = "Jestha";
        break;

      case 3:
        $n_month = "Ashad";
        break;

      case 4:
        $n_month = "Shrawan";
        break;

      case 5:
        $n_month = "Bhadra";
        break;

      case 6:
        $n_month = "Ashwin";
        break;

      case 7:
        $n_month = "Kartik";
        break;

      case 8:
        $n_month = "Mangshir";
        break;

      case 9:
        $n_month = "Poush";
        break;

      case 10:
        $n_month = "Magh";
        break;

      case 11:
        $n_month = "Falgun";
        break;

      case 12:
        $n_month = "Chaitra";
        break;
    }
    return  $n_month;
  }

  private function get_english_month($m)
  {
    $eMonth = false;
    switch ($m) {
      case 1:
        $eMonth = "January";
        break;
      case 2:
        $eMonth = "February";
        break;
      case 3:
        $eMonth = "March";
        break;
      case 4:
        $eMonth = "April";
        break;
      case 5:
        $eMonth = "May";
        break;
      case 6:
        $eMonth = "June";
        break;
      case 7:
        $eMonth = "July";
        break;
      case 8:
        $eMonth = "August";
        break;
      case 9:
        $eMonth = "September";
        break;
      case 10:
        $eMonth = "October";
        break;
      case 11:
        $eMonth = "November";
        break;
      case 12:
        $eMonth = "December";
    }
    return $eMonth;
  }

  private function get_day_of_week($day)
  {
    switch ($day) {
      case 1:
        $day = "Sunday";
        break;

      case 2:
        $day = "Monday";
        break;

      case 3:
        $day = "Tuesday";
        break;

      case 4:
        $day = "Wednesday";
        break;

      case 5:
        $day = "Thursday";
        break;

      case 6:
        $day = "Friday";
        break;

      case 7:
        $day = "Saturday";
        break;
    }
    return $day;
  }


  private function is_range_eng($yy, $mm, $dd)
  {
    if ($yy < 1944 || $yy > 2033) {
      $this->debug_info = "Supported only between 1944-2022";
      return false;
    }

    if ($mm < 1 || $mm > 12) {
      $this->debug_info = "Error! value 1-12 only";
      return false;
    }

    if ($dd < 1 || $dd > 31) {
      $this->debug_info = "Error! value 1-31 only";
      return false;
    }

    return true;
  }

  private function is_range_nep($yy, $mm, $dd)
  {
    if ($yy < 2000 || $yy > 2090) {
      $this->debug_info = "Supported only between 2000-2089";
      return false;
    }

    if ($mm < 1 || $mm > 12) {
      $this->debug_info = "Error! value 1-12 only";
      return false;
    }

    if ($dd < 1 || $dd > 32) {
      $this->debug_info = "Error! value 1-31 only";
      return false;
    }

    return true;
  }


  /**
   * currently can only calculate the date between AD 1944-2033...
   *
   * @param unknown_type $yy
   * @param unknown_type $mm
   * @param unknown_type $dd
   * @return unknown
   */

  public function AD_to_BS($yy, $mm, $dd)
  {
    if ($this->is_range_eng($yy, $mm, $dd) == false) {
      return false;
    } else {

      // english month data.
      $month = array(31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
      $lmonth = array(31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

      $def_eyy = 1944;                  //spear head english date...
      $def_nyy = 2000;
      $def_nmm = 9;
      $def_ndd = 17 - 1;    //spear head nepali date...
      $total_eDays = 0;
      $total_nDays = 0;
      $a = 0;
      $day = 7 - 1;    //all the initializations...
      $m = 0;
      $y = 0;
      $i = 0;
      $j = 0;
      $numDay = 0;

      // count total no. of days in-terms of year
      for ($i = 0; $i < ($yy - $def_eyy); $i++) {  //total days for month calculation...(english)
        if ($this->is_leap_year($def_eyy + $i) == 1)
          for ($j = 0; $j < 12; $j++)
            $total_eDays += $lmonth[$j];
        else
          for ($j = 0; $j < 12; $j++)
            $total_eDays += $month[$j];
      }

      // count total no. of days in-terms of month					
      for ($i = 0; $i < ($mm - 1); $i++) {
        if ($this->is_leap_year($yy) == 1)
          $total_eDays += $lmonth[$i];
        else
          $total_eDays += $month[$i];
      }

      // count total no. of days in-terms of date
      $total_eDays += $dd;


      $i = 0;
      $j = $def_nmm;
      $total_nDays = $def_ndd;
      $m = $def_nmm;
      $y = $def_nyy;

      // count nepali date from array
      while ($total_eDays != 0) {
        $a = $this->bs[$i][$j];
        $total_nDays++;            //count the days
        $day++;                //count the days interms of 7 days
        if ($total_nDays > $a) {
          $m++;
          $total_nDays = 1;
          $j++;
        }
        if ($day > 7)
          $day = 1;
        if ($m > 12) {
          $y++;
          $m = 1;
        }
        if ($j > 12) {
          $j = 1;
          $i++;
        }
        $total_eDays--;
      }

      $numDay = $day;

      $this->nep_date["year"] = $y;
      $this->nep_date["month"] = $m;
      $this->nep_date["date"] = $total_nDays;
      $this->nep_date["day"] = $this->get_day_of_week($day);
      $this->nep_date["nmonth"] = $this->get_nepali_month($m);
      $this->nep_date["num_day"] = $numDay;
      return $this->nep_date;
    }
  }


  /**
   * currently can only calculate the date between BS 2000-2089
   *
   * @param unknown_type $yy
   * @param unknown_type $mm
   * @param unknown_type $dd
   * @return unknown
   */
  public function BS_to_AD($yy, $mm, $dd)
  {

    $def_eyy = 1943;
    $def_emm = 4;
    $def_edd = 14 - 1;    // init english date.
    $def_nyy = 2000;
    $def_nmm = 1;
    $def_ndd = 1;    // equivalent nepali date.
    $total_eDays = 0;
    $total_nDays = 0;
    $a = 0;
    $day = 4 - 1;    // initializations...
    $m = 0;
    $y = 0;
    $i = 0;
    $k = 0;
    $numDay = 0;

    $month = array(0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
    $lmonth = array(0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);

    if ($this->is_range_nep($yy, $mm, $dd) === false) {
      return false;
    } else {

      // count total days in-terms of year
      for ($i = 0; $i < ($yy - $def_nyy); $i++) {
        for ($j = 1; $j <= 12; $j++) {
          $total_nDays += $this->bs[$k][$j];
        }
        $k++;
      }

      // count total days in-terms of month			
      for ($j = 1; $j < $mm; $j++) {
        $total_nDays += $this->bs[$k][$j];
      }

      // count total days in-terms of dat
      $total_nDays += $dd;

      //calculation of equivalent english date...
      $total_eDays = $def_edd;
      $m = $def_emm;
      $y = $def_eyy;
      while ($total_nDays != 0) {
        if ($this->is_leap_year($y)) {
          $a = $lmonth[$m];
        } else {
          $a = $month[$m];
        }
        $total_eDays++;
        $day++;
        if ($total_eDays > $a) {
          $m++;
          $total_eDays = 1;
          if ($m > 12) {
            $y++;
            $m = 1;
          }
        }
        if ($day > 7)
          $day = 1;
        $total_nDays--;
      }
      $numDay = $day;

      $this->eng_date["year"] = $y;
      $this->eng_date["month"] = $m;
      $this->eng_date["date"] = $total_eDays;
      $this->eng_date["day"] = $this->get_day_of_week($day);
      $this->eng_date["emonth"] = $this->get_english_month($m);
      $this->eng_date["num_day"] = $numDay;

      return $this->eng_date;
    }
  }

  public function get_days_of_month($year, $month)
  {
    $bs = $this->bs;
    $toreturn = 0;
    foreach ($bs as $item) {
      if (isset($item[0]) and $item[0] == $year) {
        return $item[$month];
      } else {
        $toreturn = 0;
      }
    }
  }

  public function get_daterange_for_nepalimonth($englishdate = false)
  {
    $toreturn = array();
    $englishdate = !$englishdate ? date('Y-m-d') : $englishdate;
    $engdatearray = explode('-', $englishdate);
    $nepalidate = $this->AD_to_BS($engdatearray[0], $engdatearray[1], $engdatearray[2]);
    //echo '<pre>';print_r($nepalidate);echo '</pre>';
    $startdatearray = $this->BS_to_AD($nepalidate['year'], $nepalidate['month'], 01);
    //echo '<pre>';print_r($startdatearray);echo '</pre>';
    $toreturn['startdate'] = $startdatearray['year'] . '-' . sprintf("%02d", $startdatearray['month']) . '-' . sprintf("%02d", $startdatearray['date']);
    $lastdayofnepmonth = $this->get_days_of_month($nepalidate['year'], $nepalidate['month']);
    $enddatearray  = $this->BS_to_AD($nepalidate['year'], $nepalidate['month'], $lastdayofnepmonth);
    $toreturn['enddate']  = $enddatearray['year'] . '-' . sprintf("%02d", $enddatearray['month']) . '-' . sprintf("%02d", $enddatearray['date']);
    //print_r($toreturn);exit();
    return $toreturn;
  }

  public function get_engdaterange_for_nepalimonth($nepalimonth = false, $nepaliyear = false)
  {
    $toreturn = array();
    $today = date('Y-m-d');
    $nepalidate =  $this->AD_to_BS(date('Y'), date('m'), date('d'));
    $nepalidate['month'] = !$nepalimonth ? (int)$nepalidate['month'] : (int)$nepalimonth;
    $nepalidate['year'] = !$nepaliyear ? $nepalidate['year'] : $nepaliyear;
    //echo '<pre>';print_r($nepalidate);echo '</pre>';
    $startdatearray = $this->BS_to_AD($nepalidate['year'], $nepalidate['month'], 01);
    #echo '<pre>';print_r($startdatearray);echo '</pre>';exit;
    $toreturn['startdate'] = $startdatearray['year'] . '-' . sprintf("%02d", $startdatearray['month']) . '-' . sprintf("%02d", $startdatearray['date']);
    $lastdayofnepmonth = $this->get_days_of_month($nepalidate['year'], $nepalidate['month']);
    $enddatearray  = $this->BS_to_AD($nepalidate['year'], $nepalidate['month'], $lastdayofnepmonth);
    $toreturn['enddate']  = $enddatearray['year'] . '-' . sprintf("%02d", $enddatearray['month']) . '-' . sprintf("%02d", $enddatearray['date']);
    $toreturn['nepalimonth'] = $nepalidate['month'];
    $toreturn['nepaliyear'] = $nepalidate['year'];
    //print_r($toreturn);exit();
    return $toreturn;
  }

  // Added by Aaditya Shankar Shrestha
  public function getEnglishStartEndDateByHalf($nepaliyear, $nepalimonth, $half)
  {
    if ($half == 1) {
      $initial_range = $nepaliyear . '-' . $nepalimonth . '-' . '01';
      $initial_range = $this->BS_to_AD($nepaliyear, $nepalimonth, 01);
      if ($initial_range['month'] <= 9) {
        $initial_range['month'] = '0' . $initial_range['month'];
      }
      if ($initial_range['date'] <= 9) {
        $initial_range['date'] = '0' . $initial_range['date'];
      }
      $initial_range = $initial_range['year'] . '-' . $initial_range['month'] . '-' . $initial_range['date'];
      $final_range = $nepaliyear . '-' . $nepalimonth . '-' . '15';
      $final_range = $this->BS_to_AD($nepaliyear, $nepalimonth, 15);
      if ($final_range['month'] <= 9) {
        $final_range['month'] = '0' . $final_range['month'];
      }
      if ($final_range['date'] <= 9) {
        $final_range['date'] = '0' . $final_range['date'];
      }
      $final_range = $final_range['year'] . '-' . $final_range['month'] . '-' . $final_range['date'];
      $toreturn = array();
      $toreturn['startdate'] = $initial_range;
      $toreturn['finaldate'] = $final_range;
      return $toreturn;
    } else {
      $initial_range = $nepaliyear . '-' . $nepalimonth . '-' . '16';
      $initial_range = $this->BS_to_AD($nepaliyear, $nepalimonth, 16);
      if ($initial_range['month'] <= 9) {
        $initial_range['month'] = '0' . $initial_range['month'];
      }
      if ($initial_range['date'] <= 9) {
        $initial_range['date'] = '0' . $initial_range['date'];
      }
      $initial_range = $initial_range['year'] . '-' . $initial_range['month'] . '-' . $initial_range['date'];
      $a = $this->get_days_of_month($nepaliyear, $nepalimonth);
      $final_range = $nepaliyear . '-' . $nepalimonth . '-' . $a;
      $final_range = $this->BS_to_AD($nepaliyear, $nepalimonth, $a);
      if ($final_range['month'] <= 9) {
        $final_range['month'] = '0' . $final_range['month'];
      }
      if ($final_range['date'] <= 9) {
        $final_range['date'] = '0' . $final_range['date'];
      }
      $final_range = $final_range['year'] . '-' . $final_range['month'] . '-' . $final_range['date'];
      $toreturn = array();
      $toreturn['startdate'] = $initial_range;
      $toreturn['finaldate'] = $final_range;
      return $toreturn;
    }
  }

  // Added By Binesh
  //previosu month days 25 to nepali month 26 
  public function getEnglishStartEndDateByNepaliMonthYear($nepaliyear, $nepalimonth)
  {
    $endDate['year'] = $nepaliyear;
    $endDate['month'] = $nepalimonth;
    $endDate['days'] = 25;
    if ($nepalimonth == '01') {
      $startDate['year'] = $nepaliyear - 1;
      $startDate['month'] = 12;
    } else {
      $startDate['year'] = $nepaliyear;
      $startDate['month'] = $nepalimonth - 1;
    }
    $startDate['days'] = 26;
    $start = $this->BS_to_AD($startDate['year'], $startDate['month'], $startDate['days']);
    $end = $this->BS_to_AD($endDate['year'], $endDate['month'], $endDate['days']);
    $start_en = $start['year'] . '-' . str_pad($start['month'], 2, "0", STR_PAD_LEFT) . '-' . str_pad($start['date'], 2, "0", STR_PAD_LEFT);
    $end_en = $end['year'] . '-' . str_pad($end['month'], 2, "0", STR_PAD_LEFT) . '-' . str_pad($end['date'], 2, "0", STR_PAD_LEFT);
    $return = ['startdate' => $start_en, 'finaldate' => $end_en];
    return $return;
  }

  //Added by Aaditya Shankar Shrestha
  public function nepaliDateRangeArray($to, $from)
  {
    $ttlnepalidate = array();
    $datediff = strtotime($to) - strtotime($from);
    $datediff = round($datediff / (60 * 60 * 24));
    for ($i = 0; $i <= $datediff; $i++) {
      $engdate = date('Y-m-d', strtotime($from . ' + ' . $i . ' day'));
      $date = strtotime($engdate);
      $month = date('m', $date);
      $year = date("Y", $date);
      $ndate = date('j', $date);
      $day = date('l', $date);
      $show = $this->AD_to_BS($year, $month, $ndate);
      $nepali_date = (object)$show;
      //$nepali_date = $nepali_date->year.'-'.$nepali_date->month.'-'.$nepali_date->date;
      $ttlnepalidate[] = $nepali_date->year . '-' . $nepali_date->month . '-' . $nepali_date->date;
    }
    return $ttlnepalidate;
  }

  public function NptoEngOptionList($year = 1)
  {
    $npdate = $this->AD_to_BS(date('Y'), date('m'), date('d'));
    $npyear = $npdate['year'];
    $nparray = array();
    for ($y = ($npyear - $year); $y <= $npyear; $y++) {
      for ($m = 1; $m <= 12; $m++) {
        $engRange = $this->get_engdaterange_for_nepalimonth($m, $y);
        $npmonth = $this->get_nepali_month($m);
        $text = $npmonth . '-' . $y;
        $value = $engRange['startdate'] . ' to ' . $engRange['enddate'];
        $nparray[$value] = $text;
      }
    }
    return array_reverse($nparray);
  }

  public function getCurrentNpYearMonth($date = null)
  {
    if (!$date) $date = date('Y-m-d');
    $dateArr = explode('-', $date);
    $npdate = $this->AD_to_BS($dateArr[0], $dateArr[1], $dateArr[2]);
    $engRange = $this->get_engdaterange_for_nepalimonth($npdate['month'], $npdate['year']);
    $toreturn = $engRange['startdate'] . ' to ' . $engRange['enddate'];
    return $toreturn;
  }


  /** Added by Suman **/
  public function getToday($today = NULL)
  {
    if (is_null($today))
      $today = date('Y-m-d');
    $todayFrags = explode("-", $today);
    $todayNepali = $this->AD_to_BS($todayFrags[0], $todayFrags[1], $todayFrags[2]);
    return $todayNepali;
  }

  /**
   * Description
   * @param date $date1 start date
   * @param date $date2 end date
   * @return float number of years
   */
  public function nepaliDateDifference($date1, $date2)
  { //$date2 > $date1
    $date1Frags = explode("-", $date1);
    $date2Frags = explode("-", $date2);
    $yearDifference = $date2Frags[0] - $date1Frags[0];
    $monthDifference = $date2Frags[1] - $date1Frags[1];
    $dayDifference = $date2Frags[2] - $date1Frags[2];
    $difference = $yearDifference + ($monthDifference / 12) + ($dayDifference / 365);
    return $difference;
  }

  //added by prabin maharajan
  public function nep_fiscal_months()
  {
    $toreturn = array();
    $cnt = 4;
    for ($i = 1; $i <= 12; $i++) {
      if ($cnt == 13) $cnt = 1;
      $toreturn[$cnt] = $this->get_nepali_fiscal_month($i);
      $cnt++;
    }
    return $toreturn;
  }

  //added by prabin maharajan
  public function get_nepali_fiscal_month($m)
  {
    $n_month = false;

    switch ($m) {
      case 1:
        $n_month = "Shrawan";
        break;

      case 2:
        $n_month = "Bhadra";
        break;

      case 3:
        $n_month = "Ashwin";
        break;

      case 4:
        $n_month = "Kartik";
        break;

      case 5:
        $n_month = "Mangshir";
        break;

      case 6:
        $n_month = "Poush";
        break;

      case 7:
        $n_month = "Magh";
        break;

      case 8:
        $n_month = "Falgun";
        break;

      case 9:
        $n_month = "Chaitra";
        break;

      case 10:
        $n_month = "Baishak";
        break;

      case 11:
        $n_month = "Jestha";
        break;

      case 12:
        $n_month = "Ashad";
        break;
    }
    return  $n_month;
  }


  //added by Brishti Ranjitkar
  public function get_nepali_fiscal_year($year, $month)
  {
    $fiscal_year = null;
    if ($month >= 4) {
      $fiscal_year = $year . '/' . ($year + 1);
    } else {
      $fiscal_year = ($year - 1) . '/' . $year;
    }
    return $fiscal_year;
  }

  //added by Aaditya Shankar Shrestha
  //full date format: yyyy-mm-dd eg: 2078-05-01
  //Output format Shrawan 01, 2078
  public function getFullDateInNepaliFormat($fullDate)
  {
    $fullDate = explode('-', $fullDate);
    return $this->get_nepali_month(ltrim($fullDate[1], 0)) . ' ' . $fullDate[2] . ', ' . $fullDate[0];
  }

  /** 
   * retrieves the previous English date range based on a given Nepali month and year,
   * or the current Nepali date if not provided.
   *  
   * @param nepaliMonth 
   * @param nepaliYear 
   * @return englishDateRange
   */
  public function getPreviousEngDateRangeFromNepaliDate($nepaliMonth = null, $nepaliYear = null)
  {
    $nepaliDate =  $this->AD_to_BS(date('Y'), date('m'), date('d'));
    $currentMonth = (int)($nepaliMonth ?? $nepaliDate['month']);
    $currentYear = (int)($nepaliYear ?? $nepaliDate['year']);
    $previousMonth = $currentMonth != 1 ? $currentMonth - 1 : 12;
    $previousYear = $currentMonth != 1 ? $currentYear : $currentYear - 1;
    $previousRange = $this->get_engdaterange_for_nepalimonth($previousMonth, $previousYear);
    return $previousRange;
  }
}
