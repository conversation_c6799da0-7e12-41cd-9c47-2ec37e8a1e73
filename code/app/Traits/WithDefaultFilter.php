<?php

namespace App\Traits;

use App\Http\Services\ScopeFetcher;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Livewire\Attributes\Computed;
use Livewire\WithPagination;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

trait WithDefaultFilter
{
    public $filterCompanyId, $filterRegionId, $filterBranchId, $filterDepartmentId;

    // public function mountWithDefaultFilter()
    // {
    //     if (!scopeAll()) {
    //         $this->filterCompanyId = currentEmployee()?->company_id;
    //         if (!scopeCompany()) {
    //             if (scopeRegion()) {
    //                 $this->filterRegionId = currentEmployee()?->organizationInfo?->region_id;
    //             }
    //         }
    //     }
    // }

    #[Computed(persist: true)]
    public function companyList()
    {
        return (new ScopeFetcher())->fetchCompany()->reduce(function ($carry, $item) {
            $carry[$item->id] = $item->name;
            return $carry;
        }, []);
    }

    #[Computed(persist: true)]
    public function regionList()
    {
        return (new ScopeFetcher())->fetchRegion(company: (int)$this->filterCompanyId, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return (new ScopeFetcher())->fetchBranch(company: (int)$this->filterCompanyId, region: (int)$this->filterRegionId, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment(company: (int)$this->filterCompanyId, pluck: "id_name");
    }

    // use this function on updated ()
    public function updatedDefaultFilter($attr)
    {
        if (in_array($attr, ['filterCompanyId', 'filterBranchId', 'filterRegionId', 'filterDepartmentId'])) {
            unset($this->list);
        }

        if ($attr == 'filterCompanyId') {
            unset($this->regionList, $this->branchList, $this->departmentList);
            $this->reset('filterRegionId', 'filterBranchId', 'filterDepartmentId');
            if (!count($this->regionList)) {
                $this->filterBranchId = null;
                unset($this->branchList);
            }
        }

        if ($attr == 'filterRegionId') {
            unset($this->branchList);
            $this->reset('filterBranchId');
        }
    }

    private function defaultFilterQuery(Builder | EloquentBuilder $query, $employeeJoin = 'emp', $orgJoin = 'org')
    {
        return $query
            ->when($this->filterCompanyId, fn($query) => $query->where("$employeeJoin.company_id", $this->filterCompanyId))
            ->when($this->filterRegionId, fn($query) => $query->where("$orgJoin.region_id", $this->filterRegionId))
            ->when($this->filterBranchId, fn($query) => $query->where("$orgJoin.branch_id", $this->filterBranchId))
            ->when($this->filterDepartmentId, fn($query) => $query->where("$orgJoin.department_id", $this->filterDepartmentId));
    }
}
