<?php

namespace App\DTOs;

class RepoResponseDTO
{
    public bool $status;
    public string $message;
    public mixed $data;

    public function __construct(bool $status, string $message = "", mixed $data = [])
    {
        $this->status = $status;
        $this->message = $message;
        $this->data = $data ?? [];
    }

    public function toArray(): array
    {
        return [
            "status" => $this->status,
            "message" => $this->message,
            "data" => $this->data,
        ];
    }

    public static function success(string $message = "", mixed $data = []): self
    {
        return new self(true, $message, $data);
    }

    public static function error(string $message = "", mixed $data = []): self
    {
        return new self(false, $message, $data);
    }
}
