<?php

namespace App\Notifications;

use MBarlow\Megaphone\Types\BaseAnnouncement;

class TaskTicket extends BaseAnnouncement
{
    public array $meta;

    public function __construct($title, $body, $link = "#", $linkText = '', $meta = [])
    {
        parent::__construct($title, $body, $link, $linkText);
        $this->meta = $meta;
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toArray($notifiable)
    {
        return [
            ...parent::toArray($notifiable),
            'meta' => $this->meta,
            'icon' => 'bookmark-star', // Custom icon for TaskTicket notifications
        ];
    }
}
