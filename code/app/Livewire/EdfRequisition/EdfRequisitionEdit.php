<?php

namespace App\Livewire\EdfRequisition;

use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\Tickets\EdfRequest;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Validate;
use Livewire\Component;

class EdfRequisitionEdit extends Component
{
    use WithNotify;

    #[Locked]
    public $edfId;

    #[Validate('required')]
    public $payslip_approver_id;

    public function mount()
    {
        $edfRequest = EdfRequest::find($this->edfId);
        if (!$edfRequest) return $this->notify("EDF request not found")->send();
        $this->payslip_approver_id = $edfRequest->payslip_meta['data']['verifier_id'];
    }

    #[Computed(persist: true)]
    public function payslipApprovers()
    {
        $performerIds = TransitionPerformer::where('workflow', WorkflowName::PAYSLIP_APPROVAL)->pluck('performer_id')->toArray();
        return Employee::with('organizationInfo', 'company')->whereIn('id', $performerIds)->get();
    }

    public function save()
    {
        $this->validate();
        $edfRequest = EdfRequest::find($this->edfId);
        $updatedPayslipMeta = $edfRequest->payslip_meta;
        $updatedPayslipMeta['data']['verifier_id'] = $this->payslip_approver_id;
        $edfRequest->payslip_meta = $updatedPayslipMeta;
        $edfRequest->save();
        $this->notify("EDF request updated successfully")->send();
        $this->dispatch('hide-model');
        $this->dispatch('updated');
    }

    public function render()
    {
        return view('livewire.edf-requisition.edf-requisition-edit');
    }
}
