<?php

namespace App\Livewire;

use App\Http\Services\CelebrationService;
use Carbon\Carbon;
use Livewire\Component;

class WorkAndBirthday extends Component
{
    public $showPopUp = false;
    public $userName, $employee;
    public $yearsCompleted;

    public function mount()
    {
        if (!isSuperAdmin() && !session('isBothCelebrated', false)) {
            $employee = currentEmployee();
            $this->userName = $employee->first_name;

            $celebration = new CelebrationService();

            if ($celebration->isDoubleCelebration($employee)) {
                $doj = Carbon::parse($employee->organizationInfo->doj);
                $years = $doj->diffInYears(Carbon::today());
                if ($years >= 1) {
                    $this->yearsCompleted = $years;
                }

                $this->showPopUp = true;

                session()->put('isBdCelebrated', true);
                session()->put('isWorkAnnCelebrated', true);
                session()->put('isBothCelebrated', true);
            }
        }
    }

    public function closePopup()
    {
        $this->showPopUp = false;
    }

    public function render()
    {
        return view('livewire.work-and-birthday');
    }
}
