<?php

namespace App\Livewire\Arflow;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Transition Performer List")]
class TransitionPerformerList extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $workflow, $performer_id, $recipient_id, $state;

    public function mount()
    {
        $this->singleSelectAttributes = ['performer_id', 'recipient_id'];
    }

    #[Computed(persist: true)]
    public function workflowList()
    {
        return ArflowHelper::getWorkflows();
    }

    #[Computed()]
    public function stateList()
    {
        if (!$this->workflow) return [];
        return ArflowHelper::getPerformers($this->workflow);
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = DB::table('arflow_state_transition_performers as ast')
            ->leftJoin('employees as performer', 'performer.id', 'ast.performer_id')
            ->leftJoin('employees as recipient', 'recipient.id', 'ast.recipient_id')
            ->leftJoin('employee_org as rec_org', 'rec_org.employee_id', 'ast.recipient_id')
            ->when($this->workflow, fn($q) => $q->where('ast.workflow', $this->workflow))
            ->when($this->performer_id, fn($q) => $q->where('ast.performer_id', $this->performer_id))
            ->when($this->recipient_id, fn($q) => $q->where('ast.recipient_id', $this->recipient_id))
            ->when($this->state, function ($query) {
                $stateLevel = explode('-', $this->state);
                if (count($stateLevel) > 1) {
                    $query->where('ast.state', $stateLevel[0])->where('ast.level', $stateLevel[1]);
                } else {
                    $query->where('ast.state', $stateLevel[0]);
                }
            })
            ->select(
                'ast.id',
                'ast.workflow',
                'ast.state',
                'ast.level',
                Employee::selectNameRawQuery('performer', 'performer'),
                Employee::selectNameRawQuery('recipient', 'recipient')
            );

        $query = filterEmployeesByScope($query, 'rec_org', 'recipient');

        $result = $this->applySorting($query)->paginate($this->perPage);

        foreach ($result as $item) {
            if ($item->state === WorkflowPerformer::VERIFIER) {
                $verifiers = config('arflow.workflows')[$item->workflow]['verifiers'] ?? [];
                $item->state = $verifiers[$item->level] ?? $item->state;
            }
        }
        return $result;
    }

    public function updated($property)
    {
        if (in_array($property, ['workflow', 'performer_id', 'recipient_id', 'state'])) {
            $this->resetPage();
            unset($this->list);
        }
    }

    public function removeTransitionPerformer($id)
    {
        $transitionPerformer = TransitionPerformer::find($id);
        if (!$transitionPerformer) {
            return $this->notify("Transition Performer not found")->type("error")->send();
        }
        $transitionPerformer->delete();
        unset($this->list);
        $this->notify("Transition Performer removed successfully")->type("success")->send();
    }

    public function render()
    {
        return view('livewire.arflow.transition-performer-list');
    }
}
