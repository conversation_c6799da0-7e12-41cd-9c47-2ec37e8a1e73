<?php

namespace App\Livewire\Arflow;

use App\Http\Helpers\ArflowHelper;
use App\Http\Repositories\TransitionPerformerRepository;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Transition Performers')]
class StateTransitionPerformer extends Component
{
    use WithRowSelect, WithNotify, MultiselectEmployeeSearch;

    public array $performers = [];

    public $tableFilters = [
        "department" => "",
        "branch" => ""
    ];
    public $departmentId, $branchId, $companyId, $employeeIds = [], $regionId;
    public $selectAllData = false;
    public $withTrashedData = false;

    public string $workflow = "";
    public string $state = "";
    public function rules()
    {
        return [
            "workflow" => "required",
            "state" => "required"
        ];
    }

    public function __construct()
    {
        $this->tableListVariable = "recipientsList";
    }

    public function mount()
    {
        if (!scopeAll() && (scopeCompany() || scopeBranch())) {
            $this->companyId = currentEmployee()?->company_id;
        }
        $this->scopeWiseFilters();
        $this->multiSelectAttributes = ['performers', 'employeeIds' => [
            'with_trashed' => auth()->user()->can(PermissionList::TRANSITIONS_PERFORMER_ASSIGN_TERMINATE)
        ]];
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();
        $orgInfo = $currentEmployee?->organizationInfo;

        $this->companyId = $currentEmployee?->company_id;

        if (scopeCompany() || scopeRegion() || scopeBranch()) {
            $this->regionId = $orgInfo?->region_id;
        } elseif (scopeBranch()) {
            $this->branchId = $orgInfo?->branch_id;
        } else {
            $this->regionId = $orgInfo?->region_id;
            $this->branchId = $orgInfo?->branch_id;
            $this->departmentId = $orgInfo?->department_id;
        }
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return Department::orderBy('name', 'asc')->select("name", "id")->where('company_id', $this->companyId)->get();
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->select("name", "id")->get();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->companyId)->orderBy('name', 'asc')->select("name", "id")->get();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->companyId);
            if (!is_null($this?->regionId)) {
                $query->where('region_id', $this?->regionId);
            }
            return $query->orderBy('name', 'asc')->select("name", "id")->get();
        }
    }

    #[Computed(persist: true)]
    public function performerList()
    {
        return Employee::list();
    }

    /** Recipients List */
    #[Computed(persist: true)]
    public function recipientsList()
    {
        return $this->recipientListQuery()
            ->leftJoin('branches as brn', 'brn.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->select(
                "employees.id",
                "brn.name as branchName",
                "dept.name as departmentName",
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                DB::raw("CONCAT(comp.code,'-',org.employee_code) as code"),
            )
            ->when(($this->sortBy && \in_array($this->sortDirection, ['asc', 'desc'])), function ($query) {
                $query->orderBy($this->sortBy, $this->sortDirection);
            })
            ->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function workflowList()
    {
        return ArflowHelper::getWorkflows();
    }

    #[Computed(persist: true)]
    public function states()
    {
        return ArflowHelper::getPerformers($this->workflow);
    }

    public function recipientListQuery()
    {
        return Employee::query()
            ->when($this->withTrashedData, fn($query) => $query->withTrashed())
            ->leftJoin('employee_org as org', 'employees.id', '=', 'org.employee_id')
            ->leftJoin("companies as comp", "comp.id", '=', "employees.company_id")
            ->when($this->departmentId, function ($query) {
                $query->where('org.department_id', $this->departmentId);
            })
            ->when($this->branchId, function ($query) {
                $query->where('org.branch_id', $this->branchId);
            })
            ->when($this->companyId, function ($query) {
                $query->where('employees.company_id', $this->companyId);
            })
            ->when(count($this->employeeIds), fn($query) => $query->whereIn('employees.id', $this->employeeIds));
        // ->where(function ($query) {
        //     $query
        //         ->where(DB::raw("CONCAT(comp.code, '-' ,org.employee_code)"), 'LIKE', '%' . $this->search . '%')
        //         ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
        //         ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
        // });
    }

    public function updatedSelectAllData()
    {
        if ($this->selectAllData) {
            $this->selectedRows = $this->recipientListQuery()->pluck('employees.id')->toArray();
        } else {
            $this->selectedRows = [];
        }
    }

    public function updatedWorkflow()
    {
        $this->reset(['state']);
        unset($this->states);
    }

    public function updated($attr)
    {
        if (\in_array($attr, ['departmentId', 'branchId', 'companyId', 'employeeIds'])) {
            unset($this->recipientsList);
            $this->resetPage($this->pageName);
            $this->selectAllData = false;
            $this->updatedSelectAllData();
        }
        if (\in_array($attr, ['companyId'])) {
            unset($this->branches, $this->departments);
            $this->branchId = null;
            $this->departmentId = null;
        }
        if (in_array($attr, ['withTrashedData'])) {
            unset($this->recipientsList);
            $this->resetPage($this->pageName);
            $this->selectAllData = false;
            $this->updatedSelectAllData();
        }
    }

    public function assignPerformers()
    {
        $this->validate();
        if (!count($this->performers)) {
            $this->notify("Select at least one performer to assign")->type("error")->send();
            return;
        }
        if (!count($this->selectedRows)) {
            $this->notify("Select at least one employee to assign")->type("error")->send();
            return;
        }

        $repo = new TransitionPerformerRepository();
        $repo->assignPerformers($this->workflow, $this->state, $this->performers, $this->selectedRows);
        $this->notify("Performers assigned")->send();
    }

    public function unassignPerformers()
    {
        $this->validate();

        if (!count($this->performers)) {
            $this->notify("Select at least one performer to unassign")->type("error")->send();
            return;
        }
        if (!count($this->selectedRows)) {
            $this->notify("Select at least one employee to unassign")->type("error")->send();
            return;
        }

        Log::info("Unassigning performers: ", ['performers' => $this->performers, 'recipients' => $this->selectedRows]);

        $performers = Employee::with('recipients')->findOrFail($this->performers);
        $states = explode('-', $this->state);
        $performerName = $states[0];
        $level = $states[1] ?? 1;

        foreach ($performers as $performer) {
            foreach ($this->selectedRows as $selectedRow) {
                $performer->recipients()->wherePivot('workflow', $this->workflow)
                    ->wherePivot('state', $performerName)
                    ->wherePivot('level', $level)
                    ->detach($selectedRow);
            }
        }

        Cache::store('arflow')->flush();

        Log::info("Unassigned Performers: ", ['performers' => $this->performers, 'recipients' => $this->selectedRows]);
        $this->notify("Performers unassigned")->send();
    }


    public function viewPerformers($employeeId)
    {
        $this->dispatch("performer-view", $employeeId);
    }

    public function render()
    {
        return view('livewire.arflow.state-transition-performer');
    }
}
