<?php

namespace App\Livewire\Arflow;

use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\RequestTicket;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;

class ViewPerformer extends Component
{
    use WithNotify;

    #[Locked]
    public array $workflowList;

    public $performerList;

    public int | string $employeeId = "";
    public $employeeName = "";

    public string $search = "";
    public string $workflow = "";

    public function mount($workflowList)
    {
        $this->workflowList = $workflowList;
        $this->workflow = $this->workflowList[0] ?? "";
    }

    #[On('performer-view')]
    public function refreshView($employeeId)
    {
        $this->employeeId = $employeeId;
        $employee = Employee::withTrashed()->findOrFail($employeeId);
        $this->employeeName = $employee->name;
        $this->setPerformerList($employee);
    }

    #[On('hidden.bs.modal')]
    public function modalHidden()
    {
        $this->workflow = $this->workflowList[0] ?? "";
        $this->performerList = [];
        $this->employeeName = "";
    }

    public function removePerformer($pivotId)
    {
        Log::info("Removing performer: ", ['pivotId' => $pivotId]);
        try {
            $row = TransitionPerformer::findOrFail($pivotId);
            cache()->store('arflow')->flush();
            $requestTicket = RequestTicket::where([
                ['employee_id', $row->recipient_id],
                ['workflow', $row->workflow],
                ['current_owner_id', $row->performer_id],
                ['current_owner_role', $row->state],
                ['verification_level', $row->level - 1],
            ])->get();
            foreach ($requestTicket as $ticket) {
                $ticket->update(['current_owner_id' => null]);
            }
            $row->delete();
            $this->notify("Performer removed")->send();
            $this->refreshView($this->employeeId);
        } catch (\Exception $e) {
            Log::error("Error while removing performer: " . $e->getMessage());
            $this->notify("Error while removing performer")->type('error')->send();
        }
    }

    public function updatedWorkflow($value)
    {
        $this->refreshView($this->employeeId);
    }

    public function stateName($state, $level)
    {
        if ($state == WorkflowPerformer::VERIFIER) {
            return config('arflow.workflows')[$this->workflow]['verifiers'][$level] ?? WorkflowPerformer::VERIFIER;
        }
        return $state;
    }

    private function setPerformerList(Employee $employee)
    {
        $this->performerList = $employee->performers($this->workflow)
            ->with('organizationInfo:id,employee_id,employee_code')
            ->select('employees.id', 'first_name', 'middle_name', 'last_name')
            ->orderBy('state', 'asc')
            ->get();
    }

    public function render()
    {
        return view('livewire.arflow.view-performer');
    }
}
