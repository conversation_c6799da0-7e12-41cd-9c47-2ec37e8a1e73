<?php

namespace App\Livewire\Requisition\Manpower;

use App\Http\Repositories\Mail\Interfaces\ManpowerRequisitionMailInterface;
use App\Traits\WithNotify;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\On;
use Livewire\Component;

class ManpowerRequistionMail extends Component
{
    use WithNotify;

    public $mrf_id, $email, $name, $doj, $deadline, $ctc_offer;
    public $edfMeta = [];
    public $rejectReason = "";
    public $sentEmails = [];
    public $remainingVacancies = 0;
    public $sentEmailsCount = 0;
    public $employeeStatus = [];
    public $responsibilities = "", $kra = "", $terms_and_conditions = "";
    public $show_jd = false;

    private ManpowerRequisitionMailInterface $mailRepository;

    public function boot(ManpowerRequisitionMailInterface $mailRepository)
    {
        $this->mailRepository = $mailRepository;
    }

    #[On('openEdfEmailModal')]
    public function openModal($mrfId)
    {
        $this->mrf_id = $mrfId ?? null;
        $this->resetOfferLetterModal();
        $this->loadEmailData();
        $this->dispatch('show-edf-email-modal');
    }

    #[On('openSentEmailsModal')]
    public function openSentEmailsModal($mrfId)
    {
        $this->mrf_id = $mrfId ?? null;
        $this->loadSentEmails();
        $this->dispatch('show-sent-emails-modal');
    }
    public function updated($attr)
    {
        if ($attr === 'show_jd' && $this->show_jd) {
            $this->loadJobDescription($this->mrf_id);
        }
    }
    public function loadJobDescription($mrfId)
    {
        $this->responsibilities = $this->mailRepository->getJobDescription($mrfId, 'job_description');
        $this->kra = $this->mailRepository->getJobDescription($mrfId, 'kra');
        $this->terms_and_conditions = $this->mailRepository->getTermsAndConditions(currentEmployee()?->company_id ?? 1, 'terms_and_conditions');
        $this->dispatch("load-offer-letter-responsibility", $this->responsibilities);
        $this->dispatch("load-offer-letter-kra", $this->kra);
        $this->dispatch("load-offer-letter-terms-and-conditions", $this->terms_and_conditions);
    }

    private function loadEmailData()
    {
        if ($this->mrf_id) {
            $this->remainingVacancies = $this->mailRepository->getRemainingVacancies($this->mrf_id);
        }
        $payslipDetail = $this->mailRepository->mrfPayslipDetail($this->mrf_id);
        $this->ctc_offer = $payslipDetail['ctc'] ?? 0;
        $this->doj = LaravelNepaliDate::getToday();
    }

    private function loadSentEmails()
    {
        if ($this->mrf_id) {
            $this->sentEmails = $this->mailRepository->getSentEmails($this->mrf_id);
            $this->sentEmailsCount = $this->mailRepository->getSentEmailsCount($this->mrf_id);
            $this->remainingVacancies = $this->mailRepository->getRemainingVacancies($this->mrf_id);
            $this->employeeStatus = $this->mailRepository->getEmployeeStatus($this->mrf_id);
        }
    }

    #[On('hide-sent-emails-modal')]
    public function resetSentEmailsModal()
    {
        $this->reset('uuid');
        $this->resetErrorBag();
    }

    #[On('hide-edf-email-modal')]
    public function resetOfferLetterModal()
    {
        $this->reset(['email', 'name', 'doj', 'deadline', 'ctc_offer', 'show_jd', 'responsibilities', 'kra','terms_and_conditions']);
        $this->resetErrorBag();
    }

    public function sendMail()
    {
        $this->validate([
            'email' => 'required|email',
            'name' => 'required',
            'doj' => 'required',
            'deadline' => 'required',
            'ctc_offer' => 'required',
        ]);
        if ($this->doj < LaravelNepaliDate::getToday()) {
            throw ValidationException::withMessages([
                'doj' => 'The DOJ cannot be less than today',
            ]);
        } else if ($this->deadline < $this->doj) {
            throw ValidationException::withMessages([
                'deadline' => 'The deadline cannot be less than DOJ',
            ]);
        }
        if ($this->show_jd) {
            $this->validate([
                'responsibilities' => 'required',
                'kra' => 'required',
                'terms_and_conditions' => 'required',
            ]);
        }
        // Check remaining vacancies
        if ($this->remainingVacancies <= 0) {
            return $this->notify("No vacancies remaining to send emails")->type('error')->send();
        }

        // Check if email already sent
        if ($this->mailRepository->checkEmailExists($this->mrf_id, $this->email)) {
            return $this->notify("Email already sent to this candidate")->type('error')->send();
        }

        try {
            $data = [
                'mrf_id' => $this->mrf_id,
                'email' => $this->email,
                'name' => $this->name,
                'doj' => $this->doj,
                'deadline' => $this->deadline,
                'ctc_offer' => $this->ctc_offer,
            ];
            if ($this->show_jd) {
                $data['responsibilities'] = $this->responsibilities;
                $data['kra'] = $this->kra;
                $data['terms_and_conditions'] = $this->terms_and_conditions;
            }

            $this->mailRepository->sendMail($data);

            $this->dispatch('hide-edf-email-modal');
            $this->loadEmailData();

            $this->notify("Mail sent successfully")->send();
        } catch (\Exception $e) {
            $this->notify("Error sending mail: " . $e->getMessage())->type('error')->send();
            logError("Unable to send manpower requisition mail: " . $e->getMessage());
        }
    }
    public function render()
    {
        return view('livewire.requisition.manpower.manpower-requistion-mail');
    }
}
