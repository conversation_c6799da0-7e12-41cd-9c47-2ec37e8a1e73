<?php

namespace App\Livewire\Requisition\Manpower;

use App\Models\Employee\Employee;
use App\Models\Tickets\ManpowerRequisition;
use Barryvdh\DomPDF\Facade\Pdf;
use Dompdf\Options;
use Illuminate\Support\Facades\Response;
use Livewire\Component;

class PrintDetail extends Component
{
    public $detailId;
    public $detail;

    public $approver = null;

    public function mount($id)
    {
        $this->detailId = $id;
    }

    public function downloadPdf()
    {
        if (!$this->detail) {
            $this->detail = ManpowerRequisition::with([
                'company',
                'designation',
                'branch',
                'subBranch',
                'department',
                'unit',
                'positionType',
                'band',
                'level',
                'job',
                'replacedEmployee.company',
            ])->findOrFail($this->detailId);
            $this->approver = $this->detail->stateHistory()->where('to', 'Approved')->first()?->actor?->name;
            
            if ($this->detail->replacedEmployee) {
                $replaced = $this->detail->replacedEmployee;
                $empCode = $replaced->employeeCode;
                if (!str_starts_with($empCode, $replaced->company?->code . '-')) {
                    $empCode = $replaced->company?->code . '-' . $empCode;
                }
                $replaced->emp_code = $empCode;
            }
            $this->detail->history = $this->detail->stateHistory->map(function ($history) {
                return [
                    'employee' => $history->actor->name,
                    'employeeCode' => $history->actor?->employeeCode,
                    'state' => $history->to,
                ];
            });
        }

        $pdf = Pdf::loadView('pdf.manpower-requisition-detail-pdf', [
            'detail' => $this->detail,
            'approver' => $this->approver
        ])->setPaper('a4', 'portrait');

        // Get DomPDF instance
        $dompdf = $pdf->getDomPDF();

        // Set options BEFORE setting callbacks
        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);
        $dompdf->setOptions($options);

        // Set the callback
        $dompdf->setCallbacks([
            'watermark' => [
                'event' => 'begin_page_render', // CRITICAL: This renders BEFORE content
                'f' => function ($frame, $canvas, $fontMetrics) {
                    $width = $canvas->get_width();
                    $height = $canvas->get_height();

                    // Position: 10% from left, center vertically
                    $canvas->page_text(
                        $width / 5,          // x position
                        $height * 0.6,        // y position
                        "CONFIDENTIAL",       // text
                        $fontMetrics->getFont("helvetica", "bolditalic"), // font
                        70,                   // size
                        [0.92, 0.92, 0.92, "alpha" => 0.2],   // color (gray)
                        0.0,                  // word_space (required before angle)
                        0.0,                  // char_space (required before angle)
                        -48.0                 // angle: -60 degrees (60° clockwise)
                    );
                }
            ]
        ]);
        // IMPORTANT: Render the PDF BEFORE streamDownload
        $dompdf->render();

        return response()->streamDownload(
            fn() => print($dompdf->output()),
            'manpower-requisition-detail.pdf'
        );
    }

    public function render()
    {
        return view('livewire.requisition.manpower.print-detail');
    }
}
