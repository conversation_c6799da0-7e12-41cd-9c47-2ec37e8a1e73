<?php

namespace App\Livewire\Requisition\Manpower;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\ManpowerRequisitionRepository;
use App\Models\configs\Department as DepartmentModel;
use App\Models\configs\Branch as BranchModel;
use App\Models\configs\Company as CompanyModel;
use App\Models\configs\Unit as UnitModel;
use App\Models\configs\Job as JobModel;
use App\Models\Employee\EmployeeTransferDetails;
use App\Models\configs\EmpStatus as EmpStatusModel;
use App\Models\Employee\Employee;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\Perk;
use App\Models\Tickets\ManpowerRequisition as ManpowerRequisitionModal;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Title("Manpower Requisition Form")]
class ManpowerRequisitionFormPageVianet extends Component
{
    use WithNotify, WithFileUploads, MultiselectEmployeeSearch;

    public $editingId;
    public array $removingDocumentIds = [];
    public $existingDocuments;
    public $tempDisplayRoiTable;

    public $bands = [], $levels = [], $salaryDetails = [];

    public $selectedAdditionalAllowance = [];

    public ManpowerRequisitionForm $form;
    protected $listeners = ['confirmResetTable'];


    private ManpowerRequisitionRepository $repo;

    public function __construct()
    {
        $this->repo = new ManpowerRequisitionRepository;
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return CompanyModel::pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        if (!$this->form->company_id) return [];
        if (!scopeAll() && scopeRegion()) {
            return  BranchModel::where([['company_id', $this->form->company_id], ['region_id', currentEmployee()?->organizationInfo?->region_id]])
                ->orderBy('name')->pluck('name', 'id')->toArray();
        }
        return BranchModel::where('company_id', $this->form->company_id)
            ->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function subBranchList()
    {
        if (!$this->form->branch_id) return [];
        return \App\Models\configs\SubBranch::where('branch_id', $this->form->branch_id)
            ->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentsList()
    {
        if (!$this->form->company_id) return [];
        return DepartmentModel::where('company_id', $this->form->company_id)
            ->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function unitsList()
    {
        return UnitModel::where('department_id', $this->form->department_id)->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function jobList()
    {
        if (!$this->form->department_id) return [];
        $department = DepartmentModel::findOrFail($this->form->department_id);
        return $department->jobs()->pluck('jobs.name', 'jobs.id')->toArray();
    }

    #[Computed(persist: true)]
    public function designationsList()
    {
        if ($this->form->department_id)
            return DepartmentModel::find($this->form->department_id)?->designations()->pluck('designations.title', 'designations.id')->toArray();
        return [];
    }

    #[Computed(persist: true)]
    public function positionTypesList()
    {
        return EmpStatusModel::where('is_active', true)->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function appointmentTypesList()
    {
        return ['Regular', 'Work Based', 'Time Bound', 'Casual', 'Part-Time', 'Interns', 'Campus Trainee'];
    }

    #[Computed(persist: true)]
    public function reportsTo()
    {
        return ['HOD', 'Regional Manager', 'Branch Manager', 'Sales Coordinator', 'Small Business Unit'];
    }

    #[Computed(persist: true)]
    public function types()
    {
        return field_enums((new ManpowerRequisitionModal)->getTable(), 'type');
    }

    #[Computed(persist: true)]
    public function nextOwners()
    {
        return (new ManpowerRequisitionModal)->getNextOwners(isSubmitting: true);
    }

    #[Computed(persist: true)]
    public function allowanceList()
    {
        return Perk::where('is_required', '=', '1')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceList()
    {
        return Perk::where('is_required', '=', '0')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceFormList()
    {
        $formData = [];
        foreach ($this->additionalAllowanceList as $allowance) {
            if (in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                array_push($formData, $allowance);
            }
        }
        return $formData;
    }

    public function getHighestScopeLevel()
    {
        if (scopeAll()) {
            return 'all';
        } elseif (scopeRegion()) {
            return 'region';
        } elseif (scopeBranch()) {
            return 'branch';
        } elseif (scopeDepartment()) {
            return 'department';
        }
        return null;
    }

    #[Computed(persist: true)]
    public function terminatedEmployeeList()
    {
        if (!$this->form->department_id) return [];
        $options = [];

        $companyId = $this->form->company_id;
        $branchId = $this->form->branch_id;
        $departmentId = $this->form->department_id;

        $incumbentCount = ManpowerRequisitionModal::select('incumbent_id', DB::raw('COUNT(*) as total'))
            ->whereIn('state', [
                ...ArflowHelper::getFinalStates(WorkflowName::MANPOWER_REQUISITION),
                WorkflowState::SUBMITTED
            ])
            ->groupBy('incumbent_id')
            ->pluck('total', 'incumbent_id');

        $transferredEmpCount = EmployeeTransferDetails::where("replaced", "Y")
            ->where("employee_transfer_details.company_id", $companyId)
            ->where("employee_transfer_details.branch_id", $branchId)
            ->where("employee_transfer_details.department_id", $departmentId)
            ->select('employee_transfer_details.employee_id', DB::raw('COUNT(*) as total'))
            ->groupBy('employee_transfer_details.employee_id')
            ->pluck("total", "employee_transfer_details.employee_id");

        $cannotBeIncluded = [];
        foreach ($incumbentCount as $employeeId => $incumbentTotal) {
            $transferredTotal = $transferredEmpCount[$employeeId] ?? 0;

            // Only include if incumbent count is greater than transferred count
            if ($incumbentTotal < $transferredTotal) {
                $cannotBeIncluded[] = $employeeId;
            }
        }

        $canBeIncluded = ManpowerRequisitionModal::whereIn('state', [
            WorkflowState::CANCELLED,
            WorkflowState::REJECTED
        ])->pluck('incumbent_id')->toArray();
        $notToBeIncluded = array_values(
            array_filter(
                array_diff($cannotBeIncluded, $canBeIncluded),
                fn($value) => !is_null($value)
            )
        );

        $terminatedEmployees = Employee::withTrashed()
            ->leftJoin('employee_terminations as et', function ($join) {
                $join->on('employees.id', '=', 'et.employee_id')
                    ->where('et.state', WorkflowState::APPROVED);
            })
            ->leftJoin('companies as comp', 'comp.id', '=', 'employees.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->whereNotNull('et.id')
            ->where('employees.company_id', $companyId)
            ->where('org.branch_id', $branchId)
            ->where('org.department_id', $departmentId)
            ->when(!empty($notToBeIncluded), function ($query) use ($notToBeIncluded) {
                $query->whereNotIn('employees.id', $notToBeIncluded);
            })
            ->select([
                'employees.id',
                'employees.first_name',
                'employees.middle_name',
                'employees.last_name',
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as companyEmpCode")
            ])
            ->distinct()
            ->orderBy('name')
            ->get();

        foreach ($terminatedEmployees->toArray() as $employee) {
            $options[$employee['id']] = "{$employee['name']} ({$employee['companyEmpCode']})";
        };

        $transferredEmp = EmployeeTransferDetails::where("replaced", "N")
            ->where("employee_transfer_details.company_id", $this->form->company_id)
            ->where("employee_transfer_details.branch_id", $this->form->branch_id)
            ->where("employee_transfer_details.department_id", $this->form->department_id)
            ->pluck("employee_transfer_details.employee_id")
            ->toArray();

        $transferredEmpDetails = Employee::select("employees.id",  DB::raw('CONCAT(employees.first_name, " ", COALESCE(employees.middle_name, ""), " ", employees.last_name, " (", comp.code, "-", org.employee_code, ")") as emp_name'))
            ->leftJoin("employee_org as org", "employees.id", "org.employee_id")
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereIn("employees.id", $transferredEmp)
            ->pluck("emp_name", "employees.id")
            ->toArray();
        $options = $transferredEmpDetails + $options;
        asort($options);
        return $options;
    }

    public function updatedSelectedAdditionalAllowance()
    {
        unset($this->additionalAllowanceFormList);
        $this->loadDefaultAdditionalAllowances();
        $this->unsetAdditionalAllowanceErrors();
    }

    public function updatedFormDepartmentId()
    {
        $this->form->reset(['incumbent_id', 'unit_id', 'job_id', 'designation_id',]);
        unset($this->unitsList);
        unset($this->terminatedEmployeeList);
        unset($this->designationsList);
        unset($this->jobList);
        $this->updatedFormJobId();
    }

    public function updatedFormBranchId()
    {
        $this->form->reset(['sub_branch_id']);
        unset($this->subBranchList);
    }

    public function updatedFormCompanyId()
    {
        $this->form->reset(['branch_id', 'sub_branch_id', 'department_id']);
        unset($this->branchList);
        unset($this->subBranchList);
        unset($this->departmentsList);
    }

    public function updatedFormJobId()
    {
        // load the data from the job when the job id is selected
        $jobProfile = JobModel::find($this->form->job_id);
        $this->form->responsibilities = $jobProfile?->responsibilities;
        $this->form->qualification = $jobProfile?->qualifications;
        $this->form->skills = $jobProfile?->skills;
        $this->dispatch("load-responsibility", $this->form->responsibilities);
    }

    public function updatedFormDesignationId()
    {
        // setting bands array when designation id is selected
        $gradeStructure = GradeStructure::where('grade_id', $this->form->designation_id)->get();
        $bandIds = $gradeStructure->map(fn($structure) => $structure->band_id)->unique()->toArray();
        $this->reset("form.band_id");
        $this->bands = EmployeeBand::whereIn('id', $bandIds)->pluck('name', 'id')->toArray();
        // $this->form->band_id = array_keys($this->bands)[0] ?? "";
        $this->levels = [];
    }

    public function updatedFormBandId()
    {
        $gradeStructure =  GradeStructure::where([
            ['grade_id', $this->form->designation_id],
            ['band_id', $this->form->band_id],
        ])->get();
        $levelIds = $gradeStructure->map(fn($structure) => $structure->pgrade_id)->unique()->toArray();
        $this->reset("form.level_id");
        $this->levels = EmployeePgrade::whereIn('id', $levelIds)->pluck('name', 'id')->toArray();
    }

    public function updated($attr, $value)
    {
        if ($attr === 'form.designation_id' || $attr === 'form.band_id' || $attr === 'form.level_id') {
            $this->calculateSalaryDetail();
        }

        if ($attr === 'form.number_of_males' || $attr === 'form.number_of_females') {
            $this->calculatePersonnelRequired();
        }

        if ($attr === 'form.total_tangible_benefits' || $attr === 'form.roi_total_investment') {
            $this->ROICalculation();
        }
        if (str_contains($attr, 'dynamicRows')) {
            $this->totalTangibleBenefits();
        }

        if ($attr === "form.type") {
            $this->reset("form.incumbent_id");
            unset($this->terminatedEmployeeList);
        }
    }

    public function updatedFormDisplayRoiTable($value)
    {
        if (!$value) {
            $this->dispatch('confirm-reset-table', ['value' => $value]);
        } else {
            $this->form->dynamicRows[] = ['metric' => '', 'description' => '', 'value' => ''];
            $this->dispatch('reloadTooltip');
        }
    }

    public function addRow()
    {
        if (count($this->form->dynamicRows) < 10) {
            $this->form->dynamicRows[] = ['metric' => '', 'description' => '', 'value' => ''];
        }
        $this->dispatch('reloadTooltip');
    }

    public function removeRow($index)
    {
        array_splice($this->form->dynamicRows, $index, 1);
        $this->totalTangibleBenefits();
        $this->dispatch('reloadTooltip');
    }

    public function resetTableData()
    {
        $this->form->dynamicRows = [];
        $this->form->total_tangible_benefits = 0;
        $this->form->roi_total_investment = 0;
        $this->form->roi_calculation = 0;
    }

    public function confirmResetTable($value)
    {
        if ($value === false) {
            $this->resetTableData();
        } else {
            $this->form->displayRoiTable = $value;
            $this->dispatch('reloadTooltip');
        }
    }

    public function totalTangibleBenefits()
    {
        $totalBenefits = array_reduce($this->form->dynamicRows, function ($carry, $row) {
            return $carry + (float)($row['value'] ?? 0);
        }, 0);

        // Update the total tangible benefits in the form data
        $this->form->total_tangible_benefits = $totalBenefits;
        $this->ROICalculation();
    }
    public function boot()
    {
        $this->form->setAllowanceList($this->allowanceList, $this->additionalAllowanceList);
    }

    public function mount($id = null)
    {
        $this->singleSelectAttributes = ['dept_head'];
        $this->loadDefaultAllowances();

        $this->editingId = $id;
        if ($id) {
            $this->loadFormData($id); // load form data
            // for showing salary details and list of band
            $this->updatedFormDesignationId();
            $this->updatedFormBandId();
            $this->calculateSalaryDetail();
        } else {
            // load the default value of the employee on the form
            $this->form->company_id = currentEmployee()?->company_id;
            $this->form->sub_branch_id = currentEmployee()?->organizationInfo->sub_branch_id ?? "";
            $this->form->branch_id = currentEmployee()?->organizationInfo->branch_id;
            $this->form->department_id = currentEmployee()?->organizationInfo->department_id;
            $this->form->defaultTableValue();
        }
        if (!(scopeAll() || scopeCompany() || scopeBranch() || scopeRegion() || scopeDepartment()))
            abort(403, "You don't have enough scope permission to add manpower requisition");
    }

    public function loadDefaultAllowances()
    {
        // loading the required allowances
        foreach ($this->allowanceList as $allowance) {
            $this->form->perks['required'][$allowance->id] = [
                $allowance->name => $allowance->default_value ? $allowance->default_value : null
            ];
        }
        $this->loadDefaultAdditionalAllowances();
    }

    public function calculatePersonnelRequired()
    {
        $this->form->number_of_personnel_required = (int)$this->form->number_of_males + (int)$this->form->number_of_females;
    }

    public function ROICalculation()
    {
        $totalInvestment = (int)$this->form?->roi_total_investment;
        $this->form->roi_calculation = $totalInvestment > 0
            ? ((int)$this->form->total_tangible_benefits) / $totalInvestment
            : 0;
    }

    public function calculateSalaryDetail()
    {
        if ($this->form->designation_id && $this->form->band_id) {
            $response = $this->repo->getSalaryDetail($this->form->designation_id, $this->form->band_id, $this->form->level_id);
            if ($response['status']) {
                $this->salaryDetails = $response['data'];
            } else {
                $this->salaryDetails = [];
            }
        } else {
            $this->salaryDetails = [];
        }
    }

    public function loadDefaultAdditionalAllowances()
    {
        // loading the additional allowances if the value is not selected
        $this->additionalAllowanceList
            ->filter(fn($allowance) => !in_array($allowance->id, $this->selectedAdditionalAllowance))
            ->each(function ($allowance) {
                $this->form->perks['optional'][$allowance->id] = [
                    $allowance->name => $allowance->default_value ? $allowance->default_value : null
                ];
            });
    }

    public function loadFormData(int | string $id)
    {
        $requisition = ManpowerRequisitionModal::with('perks')->where('id', $id)->first();
        if (!$requisition) abort('404');
        if ($requisition->employee_id !== currentEmployee()?->id || $requisition->state !== WorkflowState::SUBMITTED) {
            abort('401', "You don't have access to edit this");
        }

        // load the form data of form object
        foreach ((new ManpowerRequisitionModal())->getFillable() as $fillable) {
            $this->form->{$fillable} = $requisition->{$fillable} ?? "";
        }
        $intraMetadata = json_decode($requisition->infrastructure_metadata, true);

        $this->form->displayed = $intraMetadata['displayed'] ?? [];
        $this->form->specificDetails = $intraMetadata['specific_details'] ?? [];

        $roiMetadata  = json_decode($requisition->roi_calculation_metadata, true);

        if (
            is_array($roiMetadata) && isset($roiMetadata['total_tangible_benefits'], $roiMetadata['total_investment'])
            && is_numeric($roiMetadata['total_tangible_benefits'])
            && is_numeric($roiMetadata['total_investment']) && ($roiMetadata['total_investment'] != 0)
        ) {
            $this->form->displayRoiTable = true;
        }


        $this->form->dynamicRows = $roiMetadata['tangible_benefits'] ?? [];
        $this->form->total_tangible_benefits =  $roiMetadata['total_tangible_benefits'] ?? [];
        $this->form->roi_total_investment =  $roiMetadata['total_investment'] ?? [];
        $this->form->roi_calculation =  $roiMetadata['roi_calculation'] ?? [];

        $this->form->next_owner_id = $requisition->requestTicket->current_owner_id;

        $this->dispatch("toggle-dept-id", [$this->form->head_of_dept]);
        // load existing documents to view
        $this->existingDocuments = $requisition->requestTicket->getDocuments();

        // load perks to form data
        foreach ($requisition->perks as $perk) {
            foreach ($this->allowanceList as $allowance) {
                if ($perk->perk_id === $allowance->id) {
                    $this->form->perks['required'] = [$allowance->id => [$allowance->name => $perk->amount]];
                }
            }
            foreach ($this->additionalAllowanceList as $allowance) {
                if ($perk->perk_id === $allowance->id) {
                    $this->form->perks['optional'][$allowance->id] = [$allowance->name => $perk->amount];
                    array_push($this->selectedAdditionalAllowance, $allowance->id);
                }
            }
        }
    }

    public function unsetAdditionalAllowanceErrors()
    {
        foreach ($this->additionalAllowanceList as $allowance) {
            if (!in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                $this->form->resetValidation("perks.optional.{$allowance->id}.{$allowance->name}");
            }
        }
    }

    public function getPerksIdAndAmount()
    {
        $perksIdAndAmount = [];
        foreach ($this->form->perks as $nestedArray) {
            foreach ($nestedArray as $key => $value) {
                if ($this->additionalAllowanceList->filter(fn($allowance) => $allowance->id == $key)->isNotEmpty()) {
                    if (in_array($key, $this->selectedAdditionalAllowance)) {
                        $perksIdAndAmount[$key] = last($value);
                    }
                } else {
                    $perksIdAndAmount[$key] = last($value);
                }
            }
        }
        return  $perksIdAndAmount;
    }

    #[On('form.head_of_dept-initialized')]
    public function employeeDropdownInitialized()
    {
        $this->dispatch("toggle-dept-id", [$this->form->head_of_dept]);
    }

    public function save()
    {
        $this->form->validateForm();
        $data = [
            ...$this->form->all(),
            "perks"     => $this->getPerksIdAndAmount(),
            "documents" => $this->form->documents,
        ];
        if ($this->editingId) {
            $data['removing_document_ids'] = $this->removingDocumentIds;
            $response = $this->repo->update((int)$this->editingId, $data);
        } else {
            $response = $this->repo->create($data);
        }
        $this->notify($response['message'])->type($response['status'] ? "success" : "error")->send();
        if ($response['status']) redirect(route('manpowerRequisitionList'));
    }

    public function render()
    {
        return view('livewire.requisition.manpower.manpower-requisition-form-vianet');
    }
}
