<?php

namespace App\Livewire\Requisition\Manpower;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\Region;
use App\Models\Tickets\ManpowerRequisition as ManpowerRequisitionModel;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Manpower Requisition List')]
class ManpowerRequisitionList extends Component
{
    use WithNotify, WithDataTable;

    public $selectedId;

    public string $type = "", $state = "", $branch_id = "", $department_id = "", $occupancy_status = "";
    public $region_id;
    public $companyId = "";

    public function mount()
    {
        // if (!scopeAll()) {
        //     $this->companyId = currentEmployee()?->company_id;
        //     if (!scopeCompany()) {
        //         if (scopeRegion()) {
        //             $this->region_id = currentEmployee()?->organizationInfo?->region_id;
        //         }
        //     }
        // }
        $message = session('message');
        if ($message) {
            $this->notify($message)->type("error")->duration(5)->send();
        }
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
        
            $this->companyId = $currentEmployee?->company_id ?? '';

            if (scopeCompany())
                return;

            $this->region_id = $orgInfo?->region_id ?? '';
            if (scopeRegion())
                return;

            $this->branch_id = $orgInfo?->branch_id ?? '';
            if (scopeBranch())
                return;

            $this->department_id = $orgInfo?->department_id ?? '';
        }
    }

    public function scopeList($query)
    {
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;

            $query->where('company_id', $currentEmployee?->company_id);
            if (scopeCompany()) {
                return $query;
            }
            $region = Region::find($this->region_id);
            if ($region) {
                $branchIds = $region->branches->pluck('id')->toArray();
                $query = $query->whereIn("branch_id", $branchIds);
            }
            if (scopeRegion()) {
                return $query;
            }
            $query->where('branch_id', $orgInfo->branch_id);
            if (scopeBranch()) {
                return $query;
            }
            $query->where('department_id', $orgInfo->department_id);
            if (scopeDepartment()) {
                return $query;
            }
        }
        return $query;
    }

    #[Computed(persist: true)]
    public function list()
    {
        $viewAll = auth()->user()->can(PermissionList::MANPOWER_REQUISITION_VIEW_ALL);
        $query = ManpowerRequisitionModel::with([
            "job:id,name",
            "branch:id,name",
            "department:id,name",
            "positionType:id,name",
            "band:id,name"
        ]);

        if (!scopeAll()) {
            $orgInfo = currentEmployee()?->organizationInfo;
            if (!scopeCompany()) {
                if (scopeRegion()) {
                    // $region = Region::find($this->region_id);
                    // if ($region) {
                    //     $branchIds = $region->branches->pluck('id')->toArray();
                    //     $query = $query->whereIn("branch_id", $branchIds);
                    // }
                } else if (scopeBranch()) {
                    $query = $query->where("branch_id", $orgInfo->branch_id);
                } else if (scopeDepartment()) {
                    $query = $query->where("department_id", $orgInfo->department_id);
                } else {
                    $query->where("branch_id", $orgInfo->branch_id);
                }
            }
        }

        $query->when($this->companyId, function ($query) {
            $query->where('company_id', $this->companyId);
        })
            ->when(!$viewAll, function ($query) {
                $query->where('employee_id', currentEmployee()->id);
            })
            ->when($this->companyId, function ($query) {
                $query->where('company_id', $this->companyId);
            })
            ->when($this->region_id, function ($query) {
                $region = Region::find($this->region_id);
                if ($region) {
                    $branchIds = $region->branches->pluck('id')->toArray();
                    $query = $query->whereIn("branch_id", $branchIds);
                }
            })
            ->when($this->branch_id, function ($query) {
                $query->where('branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('department_id', $this->department_id);
            })
            ->when($this->type, function ($query) {
                $query->where('type', $this->type);
            })
            ->when($this->state, function ($query) {
                $query->where('state', $this->state);
            })
            ->when($this->occupancy_status, function ($query) {
                $status = strtolower($this->occupancy_status);
                if ($status == "vacant") {
                    $query->whereColumn('number_vacancy', '>', 'number_fulfilled');
                } else if ($status == "fulfilled") {
                    $query->whereColumn('number_vacancy', '=', 'number_fulfilled');
                }
            })
            ->orderBy('created_at', 'desc');

        return $this->applySorting($query)->paginate($this->perPage);
    }

    // #[Computed(persist: true)]
    // public function selectedDetail()
    // {
    //     if ($this->selectedId) {
    //         $ticket = RequestTicket::where([
    //             ['model_type', ManpowerRequisitionModel::class],
    //             ['model_id', $this->selectedId]
    //         ])->first();
    //         if ($ticket) {
    //             $model = $ticket?->model;
    //             $model->requisition_type = $model->type;
    //             $model->ticket_id = $ticket->id;
    //             $model->type = 'manpower_requisition';
    //             return $model;
    //         }
    //         return null;
    //     }
    //     return null;
    // }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->companyId);

            if (!is_null($this?->region_id)) {
                $query->where('region_id', $this->region_id);
            }

            return $query->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        }
    }

    #[Computed(persist: true)]
    public function regionList()
    {
        if (!$this->companyId) return [];
        return \App\Models\configs\Region::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return Department::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function typeList()
    {
        return field_enums((new ManpowerRequisitionModel)->getTable(), 'type');
    }

    #[Computed(persist: true)]
    public function stateList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::MANPOWER_REQUISITION);
    }

    public function updated($attr)
    {
        $filters = ['type', 'state', 'branch_id', 'department_id', 'companyId', 'region_id', 'occupancy_status'];
        if (\in_array($attr, $filters)) {
            unset($this->list);
            $this->resetPage($this->pageName);
        }
        if (in_array($attr, ['companyId'])) {
            unset($this->branchList, $this->departmentList, $this->regionList);
            $this->branch_id = '';
            $this->department_id = '';
            $this->region_id = '';
        }
        if (in_array($attr, ['region_id'])) {
            unset($this->branchList);
            $this->branch_id = '';
        }
    }

    public function setDetail(int $id)
    {
        $this->selectedId = $id;
    }

    public function refreshList()
    {
        unset($this->list);
    }

    public function render()
    {
        return view('livewire.requisition.manpower.manpower-requisition-list');
    }
}
