<?php

namespace App\Livewire\Requisition\Manpower;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Traits\MultiselectEmployeeSearch;
use Livewire\Features\SupportFormObjects\Form;

class ManpowerRequisitionForm extends Form
{
    use  MultiselectEmployeeSearch;

    public $company_id = "", $job_id = "", $designation_id = "", $branch_id = "", $sub_branch_id = "", $department_id = "",
        $unit_id, $position_type_id = "", $type = "", $number_vacancy = 1, $responsibilities, $timeline, $head_of_dept,
        $qualification, $skills, $incumbent_id = "", $next_owner_id = "", $justification,
        $documents = [], $band_id = "", $level_id = "", $perks = [],
        $appointment_type = "", $required_position = "", $reports_to = "",
        $total_tangible_benefits, $roi_total_investment, $roi_calculation,
        $min_year_of_experience = 1, $max_year_of_experience = 1, $min_age_limit = 18, $max_age_limit, $number_of_males, $number_of_females, $number_of_personnel_required,
        $reallocation_justification, $sanction_consequences, $displayRoiTable = false, $experience,
        $region_id, $kra = "";

    private $allowanceList = [];
    private $additionalAllowanceList = [];
    public $displayed;
    public $specificDetails;
    public $dynamicRows = [
        ['metric' => '', 'description' => '', 'value' => ''],
    ];

    public function defaultTableValue()
    {
        $this->displayed = array_fill_keys(Constant::INFRASTRUCTURE_REQUIREMENTS, false);
        $this->specificDetails = array_fill_keys(Constant::INFRASTRUCTURE_REQUIREMENTS, '');
    }

    public function rules()
    {
        $commonRules = [
            'company_id' => 'required|exists:companies,id',
            'job_id' => 'required|exists:jobs,id',
            'designation_id' => 'required|exists:designations,id',
            'band_id' => 'required|exists:employee_bands,id',
            'level_id' => 'required|exists:employee_pgrades,id',
            'branch_id' => 'required|exists:branches,id',
            'sub_branch_id' => 'nullable|exists:sub_branches,id',
            'department_id' => 'required|exists:departments,id',
            'unit_id' => 'nullable|exists:units,id',
            'position_type_id' => 'required|exists:emp_statuses,id',
            'experience' => 'required|string|min:3',
            'number_vacancy' => 'required|integer|min:0',
            'responsibilities' => 'required|string|min:3',
            'timeline' => ['required', function ($attribute, $value, $fail) {
                try {
                    $todayNepaliDate = LaravelNepaliDate::from(\Carbon\Carbon::today())->toNepaliDate();
                    if (compareNepaliDates($todayNepaliDate, $value) == 1) {
                        $fail("The date should be greater than or equal to today.");
                    }
                } catch (\Exception $e) {
                    $fail("Invalid date format provided.");
                }
            }],
            'qualification' => 'required|max:191',
            'incumbent_id' => 'required_if:type,replacement|nullable|exists:employees,id',
            'skills' => 'required',
            'kra' => 'required',
            'next_owner_id' => 'required|exists:employees,id',
            'documents.*' => 'file|mimes:pdf,docx,doc,jpg,jpeg,png'
        ];

        if (vianetHrm()) {
            $vianetSpecificRules = [
                'head_of_dept' => 'required|exists:employees,id',
                'appointment_type' => 'required',
                'required_position' => 'required',
                'reports_to' => 'required',
                'experience' => 'nullable|string|min:3',
                'min_year_of_experience' => 'required|integer|min:0',
                'max_year_of_experience' => 'required|integer',
                'min_age_limit' => 'required|integer|min:18|lte:max_age_limit',
                'max_age_limit' => 'required|integer|max:60|gte:min_age_limit',
                'number_of_males' => 'required|integer|min:0',
                'number_of_females' => 'required|integer|min:0',
                'number_vacancy' => [
                    'required',
                    'integer',
                    'min:0',
                    function ($attribute, $value, $fail) {
                        try {
                            $males = (int) $this->number_of_males;
                            $females = (int) $this->number_of_females;
                            $total = $males + $females;

                            if ($total !== (int) $value) {
                                $fail("The sum of males and females should be equal to number of vacancy");
                            }
                        } catch (\Throwable $e) {
                            $fail("An error occurred while validating the number of vacancy");
                        }
                    }
                ],
                'number_of_personnel_required' => 'required|integer|min:0',
                'reallocation_justification' => 'required',
                'sanction_consequences' => 'required',
                'justification' => 'nullable|max:191',
                'type' => 'required|in:replacement,new,transfer,workload,resign,terminate',
                'dynamicRows.*.metric' => 'required_if:displayRoiTable,true|string|max:255',
                'dynamicRows.*.description' => 'required_if:displayRoiTable,true|string|max:255',
                'dynamicRows.*.value' => 'required_if:displayRoiTable,true|numeric',
            ];
            if ($this->displayRoiTable === true) {
                $vianetSpecificRules['roi_total_investment'] = 'required|numeric';
            }

            $ruleArray = array_merge($commonRules, $vianetSpecificRules);

            foreach (Constant::INFRASTRUCTURE_REQUIREMENTS as $requirement) {
                $ruleArray["specificDetails.$requirement"] = "required_if:displayed.$requirement,true";
            }
        } else {
            $commonRules['justification'] = 'required|max:191';
            $commonRules['type'] = 'required|in:replacement,new,transfer';
            $ruleArray = $commonRules;
        }
        // Validation rules for perks
        if (count($this->allowanceList)) {
            $perksRequiredRule = [];
            foreach ($this->allowanceList as $item) {
                $ruleProperty = "perks.required.{$item->id}.{$item->name}";
                $rule = ['required', 'numeric', 'min:0'];
                $perksRequiredRule[$ruleProperty] = $rule;
            }
            $ruleArray = [...$ruleArray, ...$perksRequiredRule];
        }

        if (count($this->additionalAllowanceList)) {
            $perksOptionalRule = [];
            foreach ($this->additionalAllowanceList as $item) {
                $ruleProperty = "perks.optional.{$item->id}.{$item->name}";
                $rule = ['nullable', 'numeric', 'min:0'];
                $perksOptionalRule[$ruleProperty] = $rule;
            }
            $ruleArray = [...$ruleArray, ...$perksOptionalRule];
        }
        return $ruleArray;
    }

    public function validationAttributes()
    {
        $attributes = [
            'position_type_id'  => 'employee status',
            'designation_id'    => 'designation',
            'band_id'           => 'band',
            'level_id'          => 'level',
            'job_id'            => 'job',
            'next_owner_id'     => 'verifier',
            'company_id'        => 'company name',
            'branch_id'         => 'branch name',
            'department_id'     => 'department name',
            'type'              => 'requisition type',
            'timeline'          => 'end date',
            'number_vacancy'    => 'number of vacancy',
            'specificDetails.laptop'  => 'specific for laptop',
            'specificDetails.desk'    => 'specific for desk',
            'specificDetails.communication'   => 'specific for communication facilities',
            'specificDetails.tools'   => 'specific for specialized tools',
            'specificDetails.internet' => 'specific for internet access',
            'specificDetails.other'   => 'specific for other equipment',
            'specificDetails.date'    => 'specific for request date',
            'displayed.laptop'  => 'laptop required',
            'displayed.desk'    => 'dask required',
            'displayed.communication'   => 'communication required',
            'displayed.tools'   => 'specialized tools required',
            'displayed.internet' => 'internet access required',
            'displayed.other'   => 'other equipment required',
            'displayed.date'    => 'request date required',
            'dynamicRows.*.metric' => 'metric',
            'dynamicRows.*.description' => 'description',
            'dynamicRows.*.value' => 'value',
        ];

        $perksAttribute = [];
        if (count($this->allowanceList)) {
            foreach ($this->allowanceList as $item) {
                $ruleProperty = "perks.required.{$item->id}.{$item->name}";
                $perksAttribute[$ruleProperty] = strtolower($item->name);
            }
        }

        if (count($this->additionalAllowanceList)) {
            foreach ($this->additionalAllowanceList as $item) {
                $ruleProperty = "perks.optional.{$item->id}.{$item->name}";
                $perksAttribute[$ruleProperty] = strtolower($item->name);
            }
        }

        return [...$attributes, ...$perksAttribute];
    }

    public function setAllowanceList($allowanceList, $additionalAllowanceList)
    {
        $this->allowanceList = $allowanceList;
        $this->additionalAllowanceList = $additionalAllowanceList;
    }

    public function validateForm()
    {
        $this->validate($this->rules());
    }
}
