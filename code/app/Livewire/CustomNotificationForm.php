<?php

namespace App\Livewire;

use App\Http\Repositories\Configs\Interfaces\ICustomNotificationRepository;
use App\Models\configs\Company;
use App\Models\configs\Region;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Livewire\Component;
use Illuminate\Support\Collection;
use Livewire\Attributes\Computed;

class CustomNotificationForm extends Component
{
    use MultiselectEmployeeSearch, WithNotify;

    private ICustomNotificationRepository $customNotificationRepo;

    public string $title = '',
    $body = '',
    $targetType = "all"; // all, companies, regions, branches, departments, employees

    public array $selectedTypes = [],
    $selectedCompanies = [],
    $selectedRegions = [],
    $selectedBranches = [],
    $selectedDepartments = [],
    $selectedEmployees = [];

    public $scheduled_at = NULL;

    public function rules()
    {
        return [
            'title' => 'required',
            'body' => 'required',
            'targetType' => 'required|in:all,companies,regions,branches,departments,employees',
            'selectedTypes' => 'required|array',
            'selectedTypes.*' => 'in:in_app,ios,android',
            'selectedCompanies' => 'required_if:targetType,companies|array',
            'selectedRegions' => 'required_if:targetType,regions|array',
            'selectedBranches' => 'required_if:targetType,branches|array',
            'selectedDepartments' => 'required_if:targetType,departments|array',
            'selectedEmployees' => 'required_if:targetType,employees|array',
        ];
    }

    public function mount()
    {
        $this->multiSelectAttributes = ['selectedEmployees'];
    }

    public function boot(ICustomNotificationRepository $customNotificationRepo)
    {
        $this->customNotificationRepo = $customNotificationRepo;
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::all()->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function regionList()
    {
        $query = Region::query();
        $query->whereIn('company_id', $this->selectedCompanies);
        return $query->select('name as label', 'id as value')->get()->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        $query = \App\Models\configs\Branch::query();
        $query->whereIn('company_id', $this->selectedCompanies)->whereIn('region_id', $this->selectedRegions);
        return $query->select('name as label', 'id as value')->get()->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        $query = \App\Models\configs\Department::query();
        $query->whereIn('company_id', $this->selectedCompanies);
        return $query->select('name as label', 'id as value')->get()->toArray();
    }

    public function updatedSelectedCompanies()
    {
        unset($this->regionList);
        unset($this->branchList);
        unset($this->departmentList);
        $this->dispatch("updateMultiselectItem", ["id" => "region-dropdown", "items" => $this->regionList]);
        $this->dispatch("updateMultiselectItem", ["id" => "branch-dropdown", "items" => $this->branchList]);
        $this->dispatch("updateMultiselectItem", ["id" => "department-dropdown", "items" => $this->departmentList]);
    }


    public function updatedSelectedRegions()
    {
        unset($this->branchList);
        $this->dispatch("updateMultiselectItem", ["id" => "branch-dropdown", "items" => $this->branchList]);
    }

    public function updatedSelectedBranches()
    {
        unset($this->employeeList);
    }


    public function updatedSelectedDepartments()
    {
        $this->dispatch("updateMultiselectItem", ["id" => "department-dropdown", "items" => $this->departmentList]);
    }

    public function save()
    {
        $this->validate();
        $this->customNotificationRepo->createCustomNotification($this->prepareData());
        if ($this->scheduled_at) {
            $this->notify("Notification scheduled successfully")->send();
        } else {
            $this->notify("Notification sent successfully")->send();
        }
        redirect(route('notification'));
    }

    public function saveAsDraft()
    {
        // TODO: to complete the flow, it was not completed before as it was not on priority
        return $this->notify("Draft is not completely implemented")->type("error")->send();
        $this->validate();
        $this->customNotificationRepo->createCustomNotification($this->prepareData(), true);
        $this->notify("Notification saved as draft")->send();
        redirect(route('notification'));
    }

    private function prepareData()
    {
        $target_ids = match ($this->targetType) {
            'companies' => $this->selectedCompanies,
            'regions' => $this->selectedRegions,
            'branches' => $this->selectedBranches,
            'departments' => $this->selectedDepartments,
            'employees' => $this->selectedEmployees,
            default => []
        };
        return [
            'title' => $this->title,
            'body' => $this->body,
            'platforms' => $this->selectedTypes,
            'target' => $this->targetType,
            'target_ids' => $target_ids,
            'scheduled_at' => $this->scheduled_at
        ];
    }

    public function render()
    {
        return view('livewire.custom-notification-form');
    }
}
