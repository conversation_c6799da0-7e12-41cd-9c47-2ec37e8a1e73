<?php

namespace App\Livewire\Payroll;

use App\Models\Payroll\Perk;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Exception;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Perk Management')]
class Perks extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null, $isEditing = false, $message = null;

    protected $model;

    public $name = '';
    public $default_value = 0;
    public $is_required = false;
    public $is_global = false;
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50',
            'default_value' => 'required|numeric|min:0',
            'is_required' => 'required',
            'is_global' => 'required',
        ];
    }
    public function validationAttributes()
    {
        return [
            'name' => 'perk name',
        ];
    }


    public function __construct()
    {
        $this->model = new \App\Models\Payroll\Perk;
    }

    public function mount()
    {
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save(): void
    {
        $this->validate();

        if ($this->isEditing) {
            $this->model->findOrFail($this->editingId)->fill($this->all())->save();

            $this->message = 'Perk edited successfully!!';
        } else {
            $this->model->create($this->all())->id;

            $this->message = 'Perk added successfully!!';
        }
        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id): void
    {
        try {
            $this->model->findOrFail($id)->delete();
            unset($this->list);

            $this->message = 'Perk deleted successfully!!';
            $this->notify($this->message)->send();
        } catch (Exception $e) {
            $this->notify('Cannot delete perk assigned for pay structure!!')->type('error')->send();
        }
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'default_value', 'is_required', 'is_global', 'isEditing']);
        $this->resetErrorBag();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->is_required = (bool)$this->is_required;
        $this->is_global = (bool)$this->is_global;
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)
        )->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.payroll.perks');
    }
}
