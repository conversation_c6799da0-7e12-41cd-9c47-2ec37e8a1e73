<?php

namespace App\Livewire\Payroll;

use App\Models\Payroll\GradeStructure;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Band Management')]
class Band extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null, $isEditing = false, $message = null;

    public $name, $basic_start, $gross_start, $basic_diff, $gross_diff, $status = true;

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\Payroll\EmployeeBand;
    }

    public function mount()
    {
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }

    public  function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'basic_start' => ['required', 'numeric', 'min:0'],
            'gross_start' => ['required', 'numeric', 'min:0'],
            'basic_diff' => ['required', 'numeric', 'min:0'],
            'gross_diff' => ['required', 'numeric', 'min:0'],
            'status' => ['required', 'boolean'],
        ];
    }
    public function validationAttributes()
    {
        return [
            'name'  => 'band name',
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save(): void
    {
        $this->validate();

        if ($this->isEditing) {
            $this->model->findOrFail($this->editingId)->fill($this->all())->save();

            $this->message = 'Band edited successfully!!';
        } else {
            $this->model->create($this->all());

            $this->message = 'Band added successfully!!';
        }
        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id): void
    {
        $isBandAssigned = GradeStructure::where("band_id", $id)->exists();
        if ($isBandAssigned) {
            $this->notify('You are not allowed to delete the band which are assigned.')->type("error")->duration(5)->send();
            return;
        }
        $this->model->findOrFail($id)->delete();
        unset($this->list);

        $this->notify('Band deleted successfully!!')->send();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'basic_start', 'gross_start', 'basic_diff', 'gross_diff', 'status', 'isEditing']);
        $this->resetErrorBag();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)
        )->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.payroll.band');
    }
}
