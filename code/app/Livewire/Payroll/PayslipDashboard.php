<?php

namespace App\Livewire\Payroll;

use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Payroll\PayslipRequest;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Payslip Dashboard')]
class PayslipDashboard extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $status, $employee_id = "", $enrollStatus, $paymentType;

    public function mount()
    {
        $this->sortBy = "created_at";
        $this->sortDirection = 'desc';
        $this->singleSelectAttributes = ['employee_list'];
    }

    #[Computed()]
    public function payslipList()
    {
        $payslipRequestResult = PayslipRequest::with(['band:id,name', 'level:id,name', 'grade_structure'])
            ->leftJoin('employees as emp', 'emp.id', '=', 'payslip_requests.employee_id')
            ->leftJoin('companies', 'companies.id', '=', 'payslip_requests.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'emp.id')
            ->leftJoin('payslips', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
            ->when($this->status, function ($query) {
                if ($this->status == WorkflowState::SUBMITTED)
                    $query->where('payslip_requests.state', $this->status);
                else
                    $query->where('payslips.status', $this->status);
            })
            ->when($this->enrollStatus, function ($query) {
                $query->where('payslip_requests.enroll_status', $this->enrollStatus);
            })
            ->when($this->paymentType, function ($query) {
                $query->where('payslip_requests.payment_type', $this->paymentType);
            })
            ->when($this->employee_id, function ($query) {
                $query->where('payslip_requests.employee_id', $this->employee_id);
            })
            ->select(
                'payslip_requests.id',
                'payslip_requests.company_id',
                'payslip_requests.employee_id',
                'payslip_requests.job_id',
                'payslip_requests.designation_id',
                'payslip_requests.band_id',
                'payslip_requests.pgrade_id',
                'payslip_requests.grade_structure_id',
                'payslip_requests.allowance_metadata',
                'payslip_requests.enroll_status',
                'payslip_requests.employee_status_id',
                'payslip_requests.stipend',
                'payslip_requests.cit',
                'payslip_requests.insurance',
                'payslip_requests.allow_ot',
                'payslip_requests.payment_type',
                'payslip_requests.remarks',
                'payslip_requests.workflow',
                'payslip_requests.state',
                'payslip_requests.created_at',
                'payslips.status',
                DB::raw("CONCAT(emp.first_name, ' ', emp.middle_name, ' ', emp.last_name) as employee_name"),
                DB::raw("CONCAT('[',companies.code,'-',org.employee_code, ']') as employee_code"),
            );

        return $payslipRequestResult
            ->orderBy($this->sortBy, $this->sortDirection ?? 'asc')
            ->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.payroll.payslip-dashboard');
    }
}
