<?php

namespace App\Livewire\Payroll\PayStructure;

use App\Http\Helpers\Constant;
use App\Models\configs\Company;
use App\Models\Payroll\Designation;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class PayStructure extends Component
{
    use WithDataTable, WithNotify;

    public $pay_structure_data = [], $rowDataNew = [], $rowDataAttributes = [], $generatePayStructureTitle, $showSaveButton = true, $editAccess = false, $add_pay_structure = false, $isEditing = [];
    public $company, $company_id, $designation, $band, $bandDetails, $level_from, $level_to, $company_id_filter, $edit_band_id, $selectedBand;

    public function __construct()
    {
        $this->search = '';
        $this->sortBy = 'band_id';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }

    public function mount($band_id)
    {
        $this->edit_band_id = $band_id;
        $this->band = $this->edit_band_id;
        $this->selectedBand = EmployeeBand::select('id', 'name', 'basic_start', 'gross_start', 'basic_diff', 'gross_diff', 'status')
            ->where('id', $band_id)
            ->first();
    }

    public function rules()
    {
        $level_from = $this->level_from;

        $defaultRules = [
            'company_id' => ['required', 'exists:' . Company::class . ',id'],
            'designation' => [
                'required', 'exists:' . Designation::class . ',id', function ($attribute, $value, $fail) {
                    if (GradeStructure::where('grade_id', $value)->where('band_id', $this->band)->where('company_id', $this->company_id)->exists()) {
                        $fail('The pay structure already created with selected designation and band.');
                    }
                },
            ],
            'band' => [
                'required', 'exists:' . EmployeeBand::class . ',id', function ($attribute, $value, $fail) {
                    if (GradeStructure::where('grade_id', $this->designation)->where('band_id', $value)->where('company_id', $this->company_id)->exists()) {
                        $fail('The pay structure already created with selected designation and band.');
                    }
                },
            ],
            'level_from' => ['required', 'numeric', 'exists:' . EmployeePgrade::class . ',id'],
            'level_to' => [
                'required', 'numeric', 'exists:' . EmployeePgrade::class . ',id',
                function ($attribute, $value, $fail) use ($level_from) {
                    $levelFrom = $level_from;

                    if (!is_numeric($levelFrom) || !is_numeric($value) || intval($value) < intval($levelFrom)) {
                        $fail("The 'Level To' must be greater than the 'Level From' value.");
                    }
                },
            ],
        ];

        return $defaultRules;
    }

    public function generatePayStructure()
    {
        $this->validate();

        $idArray = array_map(function ($item) {
            return $item['id'];
        }, $this->levelList()->toArray());
        $indexOfLevelForm = array_search($this->level_from, $idArray);
        $countLevelFromTo = 0;

        for ($i = $indexOfLevelForm; $i <= count($idArray); $i += 1) {
            $countLevelFromTo += 1;
            if ($this->level_to == $idArray[$i]) break;
        }

        $selectedLevelRange = array_slice($this->levelList()->toArray(), $indexOfLevelForm, $countLevelFromTo);

        $this->company = Company::where('id', '=', $this->company_id)->firstOrFail();

        $index = 0;
        $dataArray = [];
        $this->bandDetails = EmployeeBand::select('id', 'name', 'basic_start', 'gross_start', 'basic_diff', 'gross_diff', 'status')
            ->where('id', $this->band)
            ->where('status', '=', '1')
            ->first()
            ->toArray();
        foreach ($selectedLevelRange as $selectedLevel) {
            $basic_salary = sprintf("%.2f", $this->bandDetails['basic_start'] + $this->bandDetails['basic_diff'] * $index);
            $gross_salary = sprintf("%.2f", $this->bandDetails['gross_start'] + $this->bandDetails['gross_diff'] * $index);
            $pf_amount = 0;
            $ssf_amount = 0;
            $gratuity_amount = 0;
            if ($this->company->rf_scheme) {
                $pf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_10);
                $ssf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_167);
                $gratuity_amount = sprintf("%.2f", $basic_salary * Constant::SSF_833);
            }

            $allowance = sprintf("%.2f", $gross_salary - $basic_salary - $pf_amount - $gratuity_amount - $ssf_amount);

            $dataArray[] = [
                "pgrade_id"     => $selectedLevel["id"],
                "level"         => $selectedLevel["name"],
                "basic_salary"  => $basic_salary,
                "pf"            => $pf_amount,
                "gratuity"      => $gratuity_amount,
                "ssf"           => $ssf_amount,
                "allowance"     => $allowance,
                "gross_salary"  => $gross_salary,
                "ctc"           => $gross_salary,
            ];
            $index += 1;
        }

        $this->pay_structure_data = $dataArray;

        $this->rowDataAttributes = $this->pay_structure_data;
        $this->rowDataNew = $this->pay_structure_data;
        $this->editAccess = true;

        $this->showSaveButton = true;
        $this->generatePayStructureTitle = 'Preview Pay Structure';
        $this->dispatch('showGeneratePayStructure');
    }

    public function save()
    {
        DB::beginTransaction();
        try {
            foreach ($this->pay_structure_data as $data) {
                $gradeStructure = new GradeStructure();
                $gradeStructure->company_id = $this->company_id;
                $gradeStructure->grade_id = $this->designation;
                $gradeStructure->band_id = $this->band;
                $gradeStructure->pgrade_id = $data['pgrade_id'];
                $gradeStructure->basic_salary = $data['basic_salary'];
                $gradeStructure->pf = $data['pf'];
                $gradeStructure->ssf = $data['ssf'];
                $gradeStructure->allowance = $data['allowance'];
                $gradeStructure->gross_salary = $data['gross_salary'];
                $gradeStructure->gratuity = $data['gratuity'];
                $gradeStructure->ctc = $data['ctc'];
                $gradeStructure->save();
            }
            $this->notify("Pay structure added successfully")->send();
            DB::commit();
            $this->resetForm();
            unset($this->payStructureList);
        } catch (QueryException $e) {
            DB::rollback();
            $this->notify('Failed to add pay structure')->type('error')->send();
            Log::error("Failed to insert pay structure data: " . $e->getMessage());
        }

        $this->add_pay_structure = false;
        $this->dispatch('hideGeneratePayStructure');
    }

    //! Logic to be added: is only allowed if the grade is not being assigned to the job.
    public function delete($grade_id, $band_id, $company_id)
    {
        DB::beginTransaction();
        try {
            GradeStructure::where('grade_id', $grade_id)
                ->where('band_id', $band_id)
                ->where('company_id', $company_id)
                ->delete();
            DB::commit();
            unset($this->payStructureList);
            $this->notify('Pay structure deleted successfully')->send();
        } catch (QueryException $e) {
            DB::rollback();
            $this->notify('Failed to delete pay structure')->send();
            Log::error("Failed to delete pay structure: " . $e->getMessage());
        }
    }


    public function preview($grade_id, $band_id, $company_id)
    {
        $this->generatePayStructureTitle = "Preview Pay Structure";
        $this->showSaveButton = false;
        $this->pay_structure_data = GradeStructure::with(['band:id,name'])
            ->where('grade_id', $grade_id)
            ->where('band_id', $band_id)
            ->where('company_id', $company_id)
            ->get();
    }

    public function resetRowDataNew($index)
    {
        $this->resetErrorBag(['rowDataNew.*.basic_salary', 'rowDataNew.*.gross_salary']);
        $this->isEditing[$index] = false;
        $this->rowDataNew[$index] = $this->rowDataAttributes[$index];
        foreach ($this->rowDataAttributes[$index] as $attribute => $value) {
            $this->pay_structure_data[$index][$attribute] = $value;
        }
    }

    public function removeRowDataNew($index) {
        unset($this->rowDataNew[$index]);
        $this->pay_structure_data = $this->rowDataNew;
    }

    public function saveRowDataNew($index)
    {
        // Validation for editing the generated pay structure.
        $rules = [
            'rowDataNew.*.basic_salary' => 'required|numeric|min:0',
            'rowDataNew.*.gross_salary' => 'required|numeric|min:0',
        ];
        $attributes = [
            'rowDataNew.*.basic_salary' => 'basic salary',
            'rowDataNew.*.gross_salary' => 'gross salary',
        ];
        $this->validate($rules, [], $attributes);
        $this->isEditing[$index] = false;

        $this->company = Company::where('id', '=', $this->company_id)->firstOrFail();

        $basic_salary = $this->rowDataNew[$index]['basic_salary'];
        $allowance = $this->rowDataNew[$index]['allowance'];
        $gross_salary = $this->rowDataNew[$index]['gross_salary'];
        $pf_amount = 0;
        $ssf_amount = 0;
        $gratuity_amount = 0;
        if ($this->company->rf_scheme) {
            $pf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_10);
            $ssf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_167);
            $gratuity_amount = sprintf("%.2f", $basic_salary * Constant::SSF_833);
        }

        $ctc = sprintf("%.2f", $basic_salary + $pf_amount + $ssf_amount + $gratuity_amount + $allowance);

        $this->rowDataNew[$index] = [
            "basic_salary"  => $basic_salary,
            "pf"            => $pf_amount,
            "ssf"           => $ssf_amount,
            "gratuity"      => $gratuity_amount,
            "allowance"     => $allowance,
            "gross_salary"  => $gross_salary,
            "ctc"           => $ctc,
        ];


        foreach ($this->rowDataNew[$index] as $attribute => $value) {
            $this->pay_structure_data[$index][$attribute] = $value;
            $this->rowDataAttributes[$index][$attribute] = $value;
        }
    }

    #[On('hide.bs.modal')]
    public function resetFields()
    {
        $this->reset(['pay_structure_data', 'isEditing']);
    }

    public function resetForm()
    {
        $this->reset(['editAccess', 'company_id', 'showSaveButton', 'designation', 'bandDetails', 'level_from', 'level_to', 'pay_structure_data', 'isEditing']);
        $this->resetErrorBag();
    }

    public function updated($attr)
    {
        if ($attr == "company_id") {
            unset($this->designation);
        }

        if ($attr == "add_pay_structure") {
            if (!$this->add_pay_structure) {
                $this->resetForm();
            }
            $this->band = $this->edit_band_id;
        }
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::select('id', 'name')->get();
    }

    #[Computed()]
    public function payStructureList()
    {
        return $this->applySorting(
            GradeStructure::with(['company:id,name', 'designation:id,title', 'band:id,name'])
                ->join('designations', 'grade_structures.grade_id', '=', 'designations.id')
                ->where('band_id', $this->edit_band_id)
                ->where('designations.title', 'like', '%' . $this->search . '%')
                ->when($this->company_id_filter, function ($query) {
                    $query->join('companies', 'grade_structures.company_id', '=', 'companies.id')
                        ->where('companies.id', $this->company_id_filter);
                })
                ->select('grade_id', 'band_id', 'company_id', DB::raw('COUNT(*) as level_count'))
                ->groupBy('grade_id', 'band_id', 'company_id')
        )->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function designationList()
    {
        $query = Designation::select('id', 'title');
        if (Schema::hasColumn((new Designation())->getTable(), 'company_id')) {
            $query->when(
                $this->company_id,
                fn ($query) =>
                $query->where('company_id', $this->company_id)
            );
        }
        return $query->get();
    }

    #[Computed(persist: true)]
    public function bandList()
    {
        return EmployeeBand::select('id', 'name', 'basic_start', 'gross_start', 'basic_diff', 'gross_diff', 'status')
            ->where('status', '=', '1')
            ->get();
    }

    #[Computed()]
    public function isBandActive()
    {
        return EmployeeBand::select('status')
            ->where('id', $this->band)
            ->first()
            ->value('status');
    }

    #[Computed(persist: true)]
    public function levelList()
    {
        return EmployeePgrade::select('id', 'name')->get();
    }

    public function render()
    {
        return view('livewire.payroll.pay-structure.pay-structure');
    }
}
