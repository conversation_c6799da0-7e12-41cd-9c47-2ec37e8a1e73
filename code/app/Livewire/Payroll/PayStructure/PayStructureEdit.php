<?php

namespace App\Livewire\Payroll\PayStructure;

use App\Http\Helpers\Constant;
use App\Models\configs\Company;
use App\Models\Payroll\Designation;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Traits\WithNotify;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Edit Pay Structure')]
class PayStructureEdit extends Component
{
    use WithNotify;

    public $pay_structure_data = [], $rowDataNew = [], $rowDataAttributes = [], $generatePayStructureTitle, $showSaveButton = false, $editAccess = false, $isEditing = [];
    public $company, $org_grade_id, $org_band_id, $org_company_id;
    public $company_id, $designation, $band, $bandDetails, $level_from, $level_to, $level_to_old;

    public function mount($grade_id, $band_id, $company_id)
    {
        $this->org_grade_id = $grade_id;
        $this->org_band_id = $band_id;
        $this->org_company_id = $company_id;

        $gradeStructure = GradeStructure::with(['band:id,name', 'pgrade:id,name'])
            ->where('grade_id', $grade_id)
            ->where('band_id', $band_id)
            ->where('company_id', $company_id);

        $this->designation = $grade_id;
        $this->band = $band_id;
        $this->company_id = $company_id;
        $this->level_from =  $gradeStructure->min('pgrade_id');
        $this->level_to =  $gradeStructure->max('pgrade_id');
        $this->level_to_old = $this->level_to;
    }

    public function rules()
    {
        $level_from = $this->level_from;
        $level_to_old = $this->level_to_old;
        $defaultRules = [
            'company_id' => ['required', 'exists:' . Company::class . ',id'],
            'designation' => [
                'required', 'exists:' . Designation::class . ',id', function ($attribute, $value, $fail) {
                    if (GradeStructure::where('grade_id', $value)->where('band_id', $this->band)->where('company_id', $this->company_id)->exists() && ($this->org_grade_id != $value) && ($this->org_band_id != $this->band)) {
                        $fail('The pay structure already created with selected designation and band.');
                    }
                }
            ],
            'band' => [
                'required', 'exists:' . EmployeeBand::class . ',id', function ($attribute, $value, $fail) {
                    if (!EmployeeBand::where('id', $this->band)->where('status', 1)->exists()) {
                        $fail('The :attribute field is required.');
                    }
                    if (GradeStructure::where('grade_id', $this->designation)->where('band_id', $value)->where('company_id', $this->company_id)->exists() && ($this->org_grade_id != $this->designation) && ($this->org_band_id != $value)) {
                        $fail('The pay structure already created with selected designation and band.');
                    }
                }
            ],
            'level_from' => ['required', 'numeric', 'exists:' . EmployeePgrade::class . ',id'],
            'level_to' => [
                'required', 'numeric', 'exists:' . EmployeePgrade::class . ',id',
                function ($attribute, $value, $fail) use ($level_from, $level_to_old) {
                    $levelFrom = $level_from;
                    $levelToOld = $level_to_old;

                    $indexOfLevelForm = array_search($levelToOld, array_map(function ($item) {
                        return $item['id'];
                    }, $this->levelList()->toArray()));

                    if (!is_numeric($levelFrom) || !is_numeric($value) || intval($value) < intval($levelFrom)) {
                        $fail("The 'Level To' must be greater than the 'Level From' value.");
                    }

                    if (!is_numeric($levelToOld) || !is_numeric($value) || intval($value) <= intval($levelToOld)) {
                        $fail("The 'Level To' must be greater than the previously selected level '{$this->levelList()->toArray()[$indexOfLevelForm]['name']}'.");
                    }
                },
            ],
        ];

        return $defaultRules;
    }

    public function generatePayStructure()
    {
        $this->validate();

        $idArray = array_map(function ($item) {
            return $item['id'];
        }, $this->levelList()->toArray());
        $indexOfLevelForm = array_search($this->level_to_old + 1, $idArray);
        $countLevelFromTo = 0;

        for ($i = $indexOfLevelForm; $i <= count($idArray); $i += 1) {
            $countLevelFromTo += 1;
            if ($this->level_to == $idArray[$i]) break;
        }

        $selectedLevelRange = array_slice($this->levelList()->toArray(), $indexOfLevelForm, $countLevelFromTo);

        $company = Company::where('id', '=', $this->company_id)->firstOrFail();

        $index = $indexOfLevelForm;
        $dataArray = [];
        $this->bandDetails = EmployeeBand::where('id', $this->band)->where('status', 1)->select('basic_start', 'gross_start', 'basic_diff', 'gross_diff')->first()->toArray();
        foreach ($selectedLevelRange as $selectedLevel) {
            $basic_salary = sprintf("%.2f", $this->bandDetails['basic_start'] + $this->bandDetails['basic_diff'] * $index);
            $gross_salary = sprintf("%.2f", $this->bandDetails['gross_start'] + $this->bandDetails['gross_diff'] * $index);
            $pf_amount = 0;
            $ssf_amount = 0;
            $gratuity_amount = 0;
            if ($company->rf_scheme) {
                $pf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_10);
                $ssf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_167);
                $gratuity_amount = sprintf("%.2f", $basic_salary * Constant::SSF_833);
            }

            $allowance = sprintf("%.2f", $gross_salary - $basic_salary - $pf_amount - $gratuity_amount - $ssf_amount);

            $dataArray[] = [
                "pgrade_id"     => $selectedLevel["id"],
                "level"         => $selectedLevel["name"],
                "basic_salary"  => $basic_salary,
                "pf"            => $pf_amount,
                "gratuity"      => $gratuity_amount,
                "ssf"           => $ssf_amount,
                "allowance"     => $allowance,
                "gross_salary"  => $gross_salary,
                "ctc"           => $gross_salary,
            ];
            $index += 1;
        }

        $this->pay_structure_data = $dataArray;
        $this->rowDataAttributes = $this->pay_structure_data;
        $this->rowDataNew = $this->pay_structure_data;
        $this->editAccess = true;

        $this->showSaveButton = true;
        $this->generatePayStructureTitle = "Pay structure for new selected level";
        $this->dispatch('showGeneratePayStructure');
    }

    public function save()
    {
        DB::beginTransaction();
        try {
            foreach ($this->pay_structure_data as $data) {
                $gradeStructure = new GradeStructure();
                $gradeStructure->company_id = $this->company_id;
                $gradeStructure->grade_id = $this->designation;
                $gradeStructure->band_id = $this->band;
                $gradeStructure->pgrade_id = $data['pgrade_id'];
                $gradeStructure->basic_salary = $data['basic_salary'];
                $gradeStructure->pf = $data['pf'];
                $gradeStructure->ssf = $data['ssf'];
                $gradeStructure->allowance = $data['allowance'];
                $gradeStructure->gross_salary = $data['gross_salary'];
                $gradeStructure->gratuity = $data['gratuity'];
                $gradeStructure->ctc = $data['ctc'];
                $gradeStructure->save();
            }
            $this->notify("Pay structure edited successfully")->send();
            DB::commit();
            unset($this->payStructureList);
            $this->resetData();
        } catch (QueryException $e) {
            DB::rollback();
            $this->notify('Failed to edit pay structure')->send();
            Log::error("Failed to edit pay structure data: " . $e->getMessage());
        }

        $this->dispatch('hideGeneratePayStructure');
    }

    public function resetRowDataNew($index)
    {
        $this->resetErrorBag(['rowDataNew.*.basic_salary', 'rowDataNew.*.gross_salary']);
        $this->isEditing[$index] = false;
        $this->rowDataNew[$index] = $this->rowDataAttributes[$index];
        foreach ($this->rowDataAttributes[$index] as $attribute => $value) {
            $this->pay_structure_data[$index][$attribute] = $value;
        }
    }

    public function removeRowDataNew($index) {
        unset($this->rowDataNew[$index]);
        $this->pay_structure_data = $this->rowDataNew;
    }

    public function saveRowDataNew($index)
    {
        // Validation for editing the generated pay structure.
        $rules = [
            'rowDataNew.*.basic_salary' => 'required|numeric|min:0',
            'rowDataNew.*.gross_salary' => 'required|numeric|min:0',
        ];
        $attributes = [
            'rowDataNew.*.basic_salary' => 'basic salary',
            'rowDataNew.*.gross_salary' => 'gross salary',
        ];
        $this->validate($rules, [], $attributes);
        $this->isEditing[$index] = false;

        $this->company = Company::where('id', '=', $this->company_id)->firstOrFail();

        $basic_salary = $this->rowDataNew[$index]['basic_salary'];
        $allowance = $this->rowDataNew[$index]['allowance'];
        $gross_salary = $this->rowDataNew[$index]['gross_salary'];
        $pf_amount = 0;
        $ssf_amount = 0;
        $gratuity_amount = 0;
        if ($this->company->rf_scheme) {
            $pf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_10);
            $ssf_amount = sprintf("%.2f", $basic_salary * Constant::SSF_167);
            $gratuity_amount = sprintf("%.2f", $basic_salary * Constant::SSF_833);
        }

        $ctc = sprintf("%.2f", $basic_salary + $pf_amount + $ssf_amount + $gratuity_amount + $allowance);

        $this->rowDataNew[$index] = [
            "basic_salary"  => $basic_salary,
            "pf"            => $pf_amount,
            "ssf"           => $ssf_amount,
            "gratuity"      => $gratuity_amount,
            "allowance"     => $allowance,
            "gross_salary"  => $gross_salary,
            "ctc"           => $ctc,
        ];

        foreach ($this->rowDataNew[$index] as $attribute => $value) {
            $this->pay_structure_data[$index][$attribute] = $value;
            $this->rowDataAttributes[$index][$attribute] = $value;
        }
    }

    #[On('hide.bs.modal')]
    public function resetFields() {
        $this->reset(['pay_structure_data', 'isEditing']);
    }

    public function resetData()
    {
        $this->reset(['pay_structure_data', 'showSaveButton', 'isEditing']);
        $this->resetErrorBag();

        $gradeStructure = GradeStructure::with(['band:id,name', 'pgrade:id,name'])
            ->where('grade_id', $this->org_grade_id)
            ->where('band_id', $this->org_band_id)
            ->where('company_id', $this->org_company_id);

        $this->designation = $this->org_grade_id;
        $this->band = $this->org_band_id;
        $this->company_id = $this->org_company_id;
        $this->level_from =  $gradeStructure->min('pgrade_id');
        $this->level_to =  $gradeStructure->max('pgrade_id');
        $this->level_to_old = $this->level_to;
    }

    public function redirectToRoute()
    {
        return redirect()->route('band.edit', [
            'band_id' => $this->org_band_id,
        ]);
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::select('id', 'name')->get();
    }

    #[Computed(persist: true)]
    public function designationList()
    {
        $query = Designation::select('id', 'title');
        if (Schema::hasColumn((new Designation())->getTable(), 'company_id')) {
            $query->when(
                $this->company_id,
                fn ($query) =>
                $query->where('company_id', $this->company_id)
            );
        }
        return $query->get();
    }

    #[Computed(persist: true)]
    public function payStructureList()
    {
        return GradeStructure::with(['band:id,name', 'pgrade:id,name'])
            ->where('grade_id', $this->org_grade_id)
            ->where('band_id', $this->org_band_id)
            ->where('company_id', $this->org_company_id)->get();
    }

    #[Computed(persist: true)]
    public function bandList()
    {
        return EmployeeBand::select('id', 'name', 'basic_start', 'gross_start', 'basic_diff', 'gross_diff', 'status')
            ->where('status', '=', '1')
            ->get();
    }

    #[Computed(persist: true)]
    public function levelList()
    {
        return EmployeePgrade::select('id', 'name')->get();
    }

    public function render()
    {
        return view('livewire.payroll.pay-structure.pay-structure-edit');
    }
}
