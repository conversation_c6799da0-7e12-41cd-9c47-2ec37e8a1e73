<?php

namespace App\Livewire\Payroll\Reports;

use App\Exports\ConsolidateSalary;
use App\Models\configs\FiscalYear;
use App\Traits\WithDataTable;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class SalaryConsolidateReport extends Component
{

    use WithDataTable;

    public $fiscalYearId;

    public function mount()
    {
        $this->fiscalYearId = FiscalYear::where('is_active', 1)->first()->id;
    }

    public function export($type = '')
    {
        $fiscalYearName = $fiscalYearName = str_replace('/', '-',  $this->fiscalYearList[$this->fiscalYearId] ?? "");
        $fileName = "Consolidate Salary Report-{$fiscalYearName}.xlsx";
        $paymentArray = $this->list()->getCollection()->toArray();
        if ($type == 'all') {
            $paymentArray = $this->query()->get()->transform(function ($item) {
                return json_decode($item->adjusted_payment_meta, true);
            })->toArray();
            ini_set('memory_limit', '512M');
            set_time_limit(300);
        }

        try {
            return Excel::download(new ConsolidateSalary($paymentArray), fileName: $fileName);
        } catch (Exception $e) {
            logError("Unable to export consolidate salary report.", $e);
            $this->notify('Error exporting consolidate salary report.')->type('error')->send();
        }
    }

    public function updated($property)
    {
        if ($property == "fiscalYearId") {
            unset($this->query);
            unset($this->list);
        }
    }

    // This code is not supported in mysql version 5.7.3 so it is commented out.
    // #[Computed(persist: true)]
    // public function query(): mixed
    // {
    //     $subquery = DB::table('payments')
    //         ->select('id', 'employee_id', 'adjusted_payment_meta', 'fiscal_year', 'is_locked', 'year', 'month')
    //         ->selectRaw('ROW_NUMBER() OVER (PARTITION BY employee_id ORDER BY year DESC, month DESC) as row_num')
    //         ->where('fiscal_year', $this->fiscalYearId)
    //         ->where('is_locked', 1)
    //         ->whereNotNull('adjusted_payment_meta');

    //     $query = DB::table(DB::raw("({$subquery->toSql()}) as t1"))
    //         ->mergeBindings($subquery) // keep bindings intact
    //         ->join('employees', 't1.employee_id', '=', 'employees.id')
    //         ->where('t1.row_num', 1)
    //         ->where(function ($query) {
    //             $query->where(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
    //                 ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
    //         })
    //         ->orderBy(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'ASC')
    //         ->select('t1.adjusted_payment_meta');

    //     return $query;
    // }

    #[Computed(persist: true)]
    public function query(): mixed
    {
        $latestSubquery = DB::table('payments')
            ->select('employee_id')
            ->selectRaw("MAX(CONCAT(year, LPAD(month, 2, '0'))) as latest_period")
            ->where('fiscal_year', $this->fiscalYearId)
            ->where('is_locked', 1)
            ->whereNotNull('adjusted_payment_meta')
            ->groupBy('employee_id');

        $query = DB::table('payments as p')
            ->joinSub($latestSubquery, 'latest', function ($join) {
                $join->on('p.employee_id', '=', 'latest.employee_id')
                    ->whereRaw("CONCAT(p.year, LPAD(p.month, 2, '0')) = latest.latest_period");
            })
            ->join('employees', 'p.employee_id', '=', 'employees.id')
            ->where(function ($query) {
                $search = "%{$this->search}%";
                $query->whereRaw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) LIKE ?", [$search])
                    ->orWhereRaw("CONCAT_WS(' ', employees.first_name, employees.last_name) LIKE ?", [$search]);
            })
            ->orderByRaw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) ASC")
            ->select('p.adjusted_payment_meta');

        return $query;
    }

    #[Computed(persist: true)]
    public function list()
    {
        $toReturn = $this->query()->paginate($this->perPage);

        $transformed = $toReturn->getCollection()->transform(function ($item) {
            return json_decode($item->adjusted_payment_meta, true);
        });

        $toReturn->setCollection($transformed);

        return $toReturn;
    }

    #[Computed()]
    public function fiscalYearList()
    {
        return FiscalYear::whereNull('deleted_at')->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.payroll.reports.salary-consolidate-report');
    }
}
