<?php

namespace App\Livewire\Payroll\BypassSettings;

use App\Models\Employee\Employee;
use App\Models\Payroll\PayrollBypassEmployee;
use App\Models\Payroll\PayrollBypassType;
use App\Models\User;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Bypass Settings")]
class BypassSettings extends Component
{
    use WithDataTable, WithNotify, MultiselectEmployeeSearch;

    public $bypass_type = "";
    public $employee_ids = [];

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->bypass_type = array_keys($this->bypassTypes)[0] ?? "";
    }

    #[Computed(persist: true)]
    public function bypassTypes()
    {
        return PayrollBypassType::pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = PayrollBypassEmployee::query()
            ->leftJoin('payroll_bypass_types as type', 'type.id', 'payroll_bypass_employees.bypass_type')
            ->leftJoin('employees as emp', 'emp.id', 'payroll_bypass_employees.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id');

        Employee::searchEmployee($this->search, $query, 'comp', 'org', 'emp');

        $query->when($this->bypass_type, fn($query) => $query->where('type.id', $this->bypass_type));

        $query->select(
            Employee::selectNameRawQuery('emp', as: 'emp_name'),
            Employee::selectEmpCodeRawQuery('comp', as: 'emp_code'),
            'emp.id as employee_id',
        );

        return $this->applySorting($query)->paginate($this->perPage);
    }

    public function addEmployee()
    {
        $bypassType = PayrollBypassType::find($this->bypass_type);
        if (!$bypassType) return $this->notify("Bypass Type not found")->type("error")->send();
        $bypassType->employees()->syncWithoutDetaching($this->employee_ids);

        $causedBy = User::find(session('user_id_main') ?? session('user_id'));
        $isImpersonating = session('user_id_main');
        activity()
            ->createdAt(now())
            ->performedOn(new PayrollBypassEmployee())
            ->causedBy($causedBy)
            ->inLog($isImpersonating ? "impersonated.payroll_bypass_employee" : "payroll_bypass_employee")
            ->event('created')
            ->withProperties([
                'attributes' => [
                    'bypass_type' => $bypassType->name,
                    'employee_ids' => json_encode($this->employee_ids),
                    'created_at' => now(),
                ]
            ])
            ->log(
                ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                    (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
            );


        unset($this->list);
        $this->employee_ids = [];
        $this->dispatch("toggle-employee-ids", []);
        return $this->notify("Employees added to bypass list successfully.")->type("success")->send();
    }

    public function removeEmployee($employeeId)
    {
        $bypassType = PayrollBypassType::find($this->bypass_type);
        if (!$bypassType) return $this->notify("Bypass Type not found")->type("error")->send();
        $bypassType->employees()->detach($employeeId);

        $causedBy = User::find(session('user_id_main') ?? session('user_id'));
        $isImpersonating = session('user_id_main');
        activity()
            ->createdAt(now())
            ->performedOn(new PayrollBypassEmployee())
            ->causedBy($causedBy)
            ->inLog($isImpersonating ? "impersonated.payroll_bypass_employee" : "payroll_bypass_employee")
            ->event('deleted')
            ->withProperties([
                'attributes' => [
                    'bypass_type' => $bypassType->name,
                    'employee_id' => json_encode($employeeId),
                    'created_at' => now(),
                ]
            ])
            ->log(
                ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                    (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
            );

        unset($this->list);
        return $this->notify("Employee removed from bypass list successfully.")->type("success")->send();
    }

    public function updated($property)
    {
        if ($property == 'bypass_type') {
            unset($this->list);
            $this->employee_ids = [];
            $this->dispatch("toggle-employee-ids", []);
        }
    }

    public function render()
    {
        return view('livewire.payroll.bypass-settings.bypass-settings');
    }
}
