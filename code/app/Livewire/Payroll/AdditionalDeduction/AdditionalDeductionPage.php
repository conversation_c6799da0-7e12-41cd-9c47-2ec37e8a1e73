<?php

namespace App\Livewire\Payroll\AdditionalDeduction;

use App\Http\Helpers\Constant;
use App\Models\Payroll\AdditionalDeduction;
use App\Traits\WithDataTable;
use Exception;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Additional Deduction')]
class AdditionalDeductionPage extends Component
{
    use WithDataTable;

    public $name, $type = 'fixed', $calculate_from, $value;

    public function rules()
    {
        return [
            'name' => 'required',
            'type' => ['required', 'in:fixed,rate'],
            'calculate_from' => ['required_if:type,rate'],
            'value' => ['required'],
        ];
    }
    public function validationAttributes(){
        return [
            'name'      => 'additional deduction',
            'value'     =>'amount'
        ];
    }

    public function save()
    {
        $this->validate();
        try {
            AdditionalDeduction::create($this->all());
            unset($this->list);
            $this->notify('Additional deduction added successfully')->send();
        } catch (Exception $e) {
            // dump($e);
            $this->notify('Failed to add additional deduction')->type('error')->send();
        }
        $this->dispatch('hide-model');
    }

    #[On('hide.bs.modal')]
    public function resetFields()
    {
        $this->reset(['name', 'type', 'calculate_from', 'value']);
        $this->resetErrorBag();
    }

    #[Computed(persist: true)]
    public function list()
    {
        return AdditionalDeduction::paginate($this->perPage);
    }

    #[Computed()]
    public function additionalDeductionType()
    {
        return [
            "fixed" => "Fixed",
            "rate" => "Rate"
        ];
    }

    #[Computed()]
    public function calculateFrom()
    {
        return Constant::ADDITIONAL_DEDUCTION_CALCULATE_FROM;
    }

    public function render()
    {
        return view('livewire.payroll.additional-deduction.additional-deduction-page');
    }
}
