<?php

namespace App\Livewire\Payroll\AdditionalDeduction;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Models\Payroll\AdditionalDeduction;
use App\Models\Payroll\AdditionalDeductionMapping;
use App\Models\Payroll\Payslip;
use App\Traits\NepaliCalendarTrait;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class AssignAdditionalDeduction extends Component
{
    use WithDataTable, NepaliCalendarTrait, WithNotify;

    public $showTitle = true, $selectAdditionalDeduction, $selectDate, $employeeList = [], $selectedEmployees = [], $employeeSearch;

    public $selectAdditionalDeductionToView, $selectedFiscalYear, $year, $selectedMonth;

    public function mount($showTitle = true)
    {
        $payslipEmployeeIds = Payslip::where('status', 'Active')->pluck('employee_id')->toArray();
        $this->employeeList = array_values(array_filter(Employee::list(), function ($employee) use ($payslipEmployeeIds) {
            return in_array($employee['id'], $payslipEmployeeIds);
        }));

        $this->showTitle = $showTitle;
        $fiscalYear = FiscalYear::where('is_active', true)->first();
        $this->selectedFiscalYear = $fiscalYear?->id;
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->setYearAndMonth();
        $this->setExcelOptions();
    }

    public function rules()
    {
        return [
            'selectAdditionalDeduction' => ['required'],
            'selectDate' => ['required'],
        ];
    }

    public function validationAttributes()
    {
        return [
            'selectAdditionalDeduction' => 'additional deduction',
            'selectDate' => 'date',
        ];
    }

    public function setYearAndMonth()
    {
        $fiscalYear = FiscalYear::where('id', $this->selectedFiscalYear)->first();
        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);
        $this->year = $start_date_year;
        if ($this->selectedMonth < $start_date_month) {
            $this->year = $end_date_year;
        }
    }

    public function updated($property)
    {
        if ($property == 'selectAdditionalDeductionToView' || $property == 'selectedFiscalYear' || $property == 'selectedMonth') {
            $this->setYearAndMonth();
            unset($this->list);
        }
    }

    public function showEmployeeListModal()
    {
        $this->validate();
        $this->dispatch('show-employee-list-modal');
    }

    public function saveAssignedDeduction()
    {
        $this->validate(['selectedEmployees' => ['required']]);

        $payslips = Payslip::whereIn('employee_id', $this->selectedEmployees)
            ->where('status', 'Active')
            ->get();
        $additionalDeduction = AdditionalDeduction::where('id', $this->selectAdditionalDeduction)->first();
        $query = new AdditionalDeductionMapping;

        $data = [];
        if ($additionalDeduction->type == 'fixed') {
            foreach ($payslips as $payslip) {
                $data[] = [
                    'payslip_id'            => $payslip->id,
                    'additional_deduction_id'  => $additionalDeduction->id,
                    'amount'                => $additionalDeduction->value,
                    'date_nep'              => $this->selectDate,
                    'date_eng'              => $this->getEnglishDate($this->selectDate)
                ];
            }
        } else if ($additionalDeduction->type == 'rate') {
            foreach ($payslips as $payslip) {
                $amount = 0;
                if ($additionalDeduction->calculate_from == 'basic_salary') {
                    $amount = $payslip->basic_salary * $additionalDeduction->value;
                }
                $data[] = [
                    'payslip_id'            => $payslip->id,
                    'additional_deduction_id'  => $additionalDeduction->id,
                    'amount'                => $amount,
                    'date_nep'              => $this->selectDate,
                    'date_eng'              => $this->getEnglishDate($this->selectDate)
                ];
            }
        }

        $result = $query->insert($data);
        if ($result) {
            $this->notify('Additional deduction assigned successfully')->send();
            unset($this->list);
        } else $this->notify('Fail to assign additional deduction.')->type('error')->send();

        $this->reset(['selectAdditionalDeduction', 'selectDate', 'selectedEmployees']);
        $this->resetErrorBag();

        $this->resetEmployeeListModal();
    }

    public function exportAllData()
    {
        $data = $this->queryList()->get();


        $processedData = [];
        foreach ($data as $item) {
            $processedData[] = [
                $item->payslip?->employee?->outsource_company?->name ?? $item->payslip?->employee?->company?->name,
                $item->payslip?->employee?->branch?->name ?? "",
                $item->payslip?->employee?->department?->name ?? "",
                $item->payslip?->employee?->name,
                $item->payslip?->employee?->employee_code,
                $item->date_nep,
                $item->date_eng,
                $item->gross_amount,
                $item->tds,
                $item->net_amount
            ];
        }

        return Excel::download(new class($processedData, $this->exportColumnHeadersMap) implements FromArray, WithHeadings
        {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return $this->exportColumnHeadersMap;
            }
        }, $this->exportFileName);
    }

    public function queryList()
    {
        return AdditionalDeductionMapping::with(['payslip:id,employee_id', 'payslip.employee:id,first_name,middle_name,last_name,company_id', 'payslip.employee.company:id,name,code', 'payslip.employee.outsource_company:name', 'payslip.employee.branch:name', 'payslip.employee.department:name', 'additional_deduction'])
            ->when($this->selectAdditionalDeductionToView, function ($query) {
                return $query->where('additional_deduction_id', $this->selectAdditionalDeductionToView);
            })
            ->where([
                ['date_nep', 'like', "{$this->year}-{$this->selectedMonth}%"]
            ]);
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Assigned Additional Deduction List.xlsx";
        $this->exportColumnHeadersMap = [
            'Vendor',
            'Branch',
            'Department',
            'Employee Name',
            'Employee Code',
            "Nepali Date",
            "English Date",
            "Net Amount",
        ];
    }

    #[On('hide.bs.modal')]
    public function resetEmployeeListModal()
    {
        $this->dispatch('hide-employee-list-modal');
        $this->resetErrorBag('selectedEmployees');
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->queryList()->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function additionalDeductionList()
    {
        return AdditionalDeduction::all();
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::get();
    }

    public function render()
    {
        return view('livewire.payroll.additional-deduction.assign-additional-deduction');
    }
}
