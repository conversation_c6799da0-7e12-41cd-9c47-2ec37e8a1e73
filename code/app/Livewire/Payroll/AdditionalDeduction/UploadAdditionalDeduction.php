<?php

namespace App\Livewire\Payroll\AdditionalDeduction;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\AdditionalDeductionExcelSample;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Http\Repositories\SalaryRepository;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Payroll\AdditionalDeduction;
use App\Models\Payroll\AdditionalDeductionMapping;
use App\Models\Payroll\Payslip;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Additional Deduction')]
class UploadAdditionalDeduction extends Component
{
    use WithFileUploads, WithNotify;

    public $excelHeaders = [
        "employee name" => 'Employee Name',
        "employee code" => 'Employee Code',
        "deduction heading" => 'Deduction Heading',
        "nepali date" => 'Nepali Date',
        "amount" => 'Amount',
    ];

    public $excelValues = [
        "employee name" => 'Vianet',
        "employee code" => 'VIA-100',
        "deduction heading" => 'Non Cash Allowance',
        "nepali date" => '2081-06-15',
        "amount" => '5000.00',
    ];

    // [['data] => [], 'error' => ''];
    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $excelFile = "";
    public $excelValidationMessages = "";

    public $currentNepaliMonth;

    public function mount()
    {
        $this->currentNepaliMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('m');
    }

    public function updatedExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'excelValidationMessages', 'excelUploaded');
        $this->excelData = [];
    }

    public function validateData()
    {
        $this->excelData = [];
        if (!($data = $this->validateFile())) return;
        try {
            foreach ($data as $item) {
                $errorMessage = "";
                $response = $this->validations($item);
                if (isset($response['status']) ? !$response['status'] : false) {
                    $this->excelDataValidated = false;
                    $errorMessage = $response['message'];
                }
                array_push($this->excelData, ['data' => $item, 'error' => $errorMessage]);
            }
        } catch (\Exception $e) {
            logError("Error uploading additional deduction from excel", $e);
            return $this->notify("Error validating additional deduction: " . $e->getMessage())->type("error")->send();
        }
    }

    public function validations($data)
    {
        $employeeCode = $data['employee code'];
        $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$employeeCode'")
            ->select(
                'employees.id as id',
                'employees.company_id',
            )
            ->first();
        if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($employeeCode ? $employeeCode : '(NULL)'));

        $payslip = Payslip::where("employee_id", $employee->id)->first();
        if (!$payslip) return $this->errorResponse("Payslip not found for the employee");

        $additionalDeductionHeading = AdditionalDeduction::where('name', $data['deduction heading'])->first();
        if (!$additionalDeductionHeading) {
            return $this->errorResponse("Unable to find the deduction heading.");
        }

        if (!(is_float($data['amount']) || is_numeric($data['amount']))) {
            return $this->errorResponse("Only numbers are allowed in amount.");
        }

        $month = explode('-', $data['nepali date'] ?? "")[1] ?? "";

        if ($month < $this->currentNepaliMonth && !isSuperAdmin()) {
            return $this->errorResponse("Invalid date or date format.");
        }
    }

    function errorResponse(string $message = "", $data = null)
    {
        return [
            "status" => false,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    public function uploadData()
    {
        if (!$this->excelDataValidated) return $this->notify("Excel validation failed");
        $data = [];
        foreach ($this->excelData as $item) {
            if ($item['error']) return $this->notify("Excel validation failed: " . $item['error']);
            array_push($data, $item['data']);
        }
        if (!($data = $this->validateFile())) return;
        $this->excelValidationMessages = "";
        DB::beginTransaction();
        try {
            $additionalDeductionHeading = AdditionalDeduction::all()->keyBy('name');
            $employee_codes = array_column($data, 'employee code');
            $conditions = [];
            foreach ($employee_codes as $code) {
                $conditions[] = "CONCAT(comp.code, '-', employee_org.employee_code) = '{$code}'";
            }
            $whereClause = implode(' OR ', $conditions);

            $result = EmployeeOrg::leftJoin('employees', 'employees.id', '=', 'employee_org.employee_id')
                ->leftJoin('companies as comp', 'comp.id', '=', 'employees.company_id')
                ->leftJoin('payslips', 'payslips.employee_id', '=', 'employees.id')
                ->where('payslips.status', 'Active')
                ->select('employee_code', 'employees.id as employee_id', 'comp.code', 'payslips.id as payslip_id', 'employees.mstat', 'employees.gender', 'payslips.basic_salary')
                ->whereRaw($whereClause)
                ->get();

            $keyedResult = $result->keyBy(function ($item) {
                return "{$item->code}-{$item->employee_code}";
            });

            $additionalDeductions = [];
            $laravelNepaliDate = new FacadesLaravelNepaliDate();
            foreach ($data as $item) {
                $nepaliDateArray = explode('-', $item['nepali date']);
                $englishDateResult = $laravelNepaliDate->BS_to_AD($nepaliDateArray[0], $nepaliDateArray[1], $nepaliDateArray[2]);
                $englishDate = $englishDateResult['year'] . '-' . ($englishDateResult['month'] < 10 ? '0' . $englishDateResult['month'] : $englishDateResult['month']) . '-' . ($englishDateResult['date'] < 10 ? '0' . $englishDateResult['date'] : $englishDateResult['date']);
                $amount = $item['amount'] ?? 0;

                $uuid = ($keyedResult[$item['employee code']]['employee_id'] ?? 0) . '-' . ($item['nepali date']) . '-' . ($additionalDeductionHeading[$item['deduction heading']]['id'] ?? 0);
                $additionalDeductions[] = [
                    'uuid' => $uuid,
                    'payslip_id' => $keyedResult[$item['employee code']]['payslip_id'] ?? 0,
                    'additional_deduction_id' => $additionalDeductionHeading[$item['deduction heading']]['id'] ?? 0,
                    'amount' => $amount,
                    'date_nep' => $item['nepali date'],
                    'date_eng' => $englishDate,
                    'employee_id' => $keyedResult[$item['employee code']]['employee_id'] ?? 0,
                ];
                $this->excelValidationMessages .= "Added additional deduction of {$item['employee code']} , {$item['employee name']}.<br />";
            }
            AdditionalDeductionMapping::upsert($additionalDeductions, ['employee_id', 'date_nep', 'additional_deduction_id'], ['amount']);
            DB::commit();
            $this->excelUploaded = true;
            return $this->notify("Additional deduction added successfully.")->send();
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while adding additional deduction ", $e);
            return $this->notify("Error creating additional deduction")->type("error")->send();
        }
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray([], $this->excelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach ($this->excelHeaders as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function downloadSample()
    {
        return Excel::download(new AdditionalDeductionExcelSample(
            $this->excelHeaders,
            $this->excelValues
        ), 'AdditionalDeductionExcelSample.xlsx');
    }

    public function render()
    {
        return view('livewire.payroll.additional-deduction.upload-additional-deduction');
    }
}
