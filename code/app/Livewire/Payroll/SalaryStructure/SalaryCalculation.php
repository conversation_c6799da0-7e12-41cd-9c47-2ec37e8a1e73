<?php

namespace App\Livewire\Payroll\SalaryStructure;

use App\Http\Repositories\Reports\SalaryVarianceRepository;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\SalarySheetExport;
use App\Exports\SalarySheetExportArray;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\AdvanceSalaryType;
use App\Http\Repositories\SalaryRepository;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Payroll\AdvanceSalary;
use App\Models\Payroll\AttendanceCount;
use App\Models\Payroll\Payment;
use App\Models\Payroll\PaymentDeductionMapping;
use App\Models\Payroll\PaymentPerksMapping;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\Perk;
use App\Models\Payroll\SalaryCalculationStopDate;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class SalaryCalculation extends Component
{
    use WithDataTable, WithNotify;

    public $fiscal_year, $nep_year, $nep_month, $total_days_of_month, $salaryStructure = [], $page = 1, $showEmergencyFundField = false, $showAdvSalaryField = false, $company_id;
    public $attendanceLocked = false;
    public $salaryGenerated = false;
    public $showPreviewButton = false;
    public $showGenerateButton = true;
    public $showUploadSalaryButton = true;
    public $showLockSalaryButton = true;
    private SalaryVarianceRepository $salaryRepo;

    public function __construct()
    {
        $this->tableListVariable = "salaryListData";
        $this->salaryRepo = new SalaryVarianceRepository();
    }

    public function mount($fiscal_year = null, $nep_month = null)
    {
        if (!permissionViewAll() && permissionViewCompany()) {
            $this->company_id = currentEmployee()?->company_id;
        }

        $this->fiscal_year = $fiscal_year ?? FiscalYear::where('is_active', true)->first()?->id;
        $this->nep_month = $nep_month ?? LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm');

        $fiscalYear = FiscalYear::where('id', $this->fiscal_year)->first();

        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);

        $this->nep_year = $start_date_year;
        // Initialize year according to selected month
        if ($this->nep_month < $start_date_month) {
            $this->nep_year = $end_date_year;
        }

        $this->total_days_of_month = LaravelNepaliDate::daysInMonth($this->nep_month, $this->nep_year) ?? 0;
        $this->checkButtonsToDisplay();
    }

    public function generateSalary($withVariance = false)
    {
        $this->setDateAndYear();
        $this->salaryStructure = [];
        $attendanceCount = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month], ['is_locked', true],])
            ->get();
        if (!count($attendanceCount)) {
            $this->notify('First generate and lock attendance found for selected month to calculate salary.')->type('error')->send();
            return;
        }
        $salaryRepo = new SalaryRepository();

        ini_set('memory_limit', '512M');
        set_time_limit(300);
        $actual_payment_result = $salaryRepo->generateSalary(nep_month: $this->nep_month, fiscalYearId: $this->fiscal_year, filter: "withoutAdjustment");
        $adjusted_payment_result = $salaryRepo->generateSalary(nep_month: $this->nep_month, fiscalYearId: $this->fiscal_year, filter: "adjustmentOnly");

        if ($actual_payment_result['status'] && $adjusted_payment_result['status']) {
            $actual_payment = $actual_payment_result['data'];
            $adjusted_payment = array_column($adjusted_payment_result['data'], null, 'employee_id');
            $this->saveGeneratedSalary($actual_payment, $adjusted_payment);
            $this->salaryGenerated = true;
            $this->showPreviewButton = true;
            $this->dispatch('reloadTooltip');
            $this->previewSalary();
            unset($this->salaryList);

            $company_id = null;
            if (!permissionViewAll() && permissionViewCompany()) {
                $company_id = currentEmployee()?->company_id;
            }

            if ($withVariance) {
                $varianceFilters = [
                    "selectedYear"  => $this->nep_year,
                    "selectedMonth" => $this->nep_month,
                    "companyId"     => $company_id,
                ];
                $this->salaryRepo->listQuery($varianceFilters, store: true);
            }
        } else {
            $this->notify(trim($actual_payment_result['message'] ?? $adjusted_payment_result['message']))->type('error')->duration(5)->send();
        }
    }

    public function saveGeneratedSalary($actual_payment, $adjusted_payment)
    {
        // Check if the salary is lock.
        $payment = Payment::leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->where([['year', $this->nep_year], ['month', $this->nep_month], ['is_locked', 1]])
            ->first();
        if ($payment) {
            $this->notify('Salary for the month is already locked.')->type('error')->send();
            return;
        }

        // Delete the saved salary of the month if salary is not locked.
        logInfo('Start to delete the previously generated salary for the year ' . $this->nep_year . ' month ' . $this->nep_month);
        $paymentToDelete = Payment::where([['year', $this->nep_year], ['month', $this->nep_month], ['is_locked', 0]]);
        if (!permissionViewAll() && permissionViewCompany()) {
            $paymentToDelete->whereHas('employee', function ($query) {
                $query->where('company_id', $this->company_id)->withTrashed();
            });
        }
        $paymentToDelete->delete();
        logInfo('Completed deleting the previously generated salary.');

        $dataToSave = [];
        $perksDataToSave = [];
        $nonCashDeductionToSave = [];

        ini_set('memory_limit', '512M');
        set_time_limit(300);
        foreach ($actual_payment as $structure) {
            if (isset($adjusted_payment[$structure['employee_id']])) {
                $preparedData = $this->prepareDataToSave($adjusted_payment[$structure['employee_id']]);
                $preparedData['dataToSave'] = [
                    ...$preparedData['dataToSave'],
                    'actual_payment_meta' => json_encode($structure),
                    'adjusted_payment_meta' => json_encode($adjusted_payment[$structure['employee_id']]),
                ];
                $dataToSave[] = $preparedData['dataToSave'];
                $perksDataToSave = [...$perksDataToSave, ...$preparedData['perksDataToSave']];
                $nonCashDeductionToSave = [...$nonCashDeductionToSave, ...$preparedData['nonCashDeduction']];
            } else {
                $preparedData = $this->prepareDataToSave($structure);
                $preparedData['dataToSave'] = [
                    ...$preparedData['dataToSave'],
                    'actual_payment_meta' => json_encode($structure),
                    'adjusted_payment_meta' => null,
                ];
                $dataToSave[] = $preparedData['dataToSave'];
                $perksDataToSave = [...$perksDataToSave, ...$preparedData['perksDataToSave']];
                $nonCashDeductionToSave = [...$nonCashDeductionToSave, ...$preparedData['nonCashDeduction']];
            }
        }

        try {
            DB::beginTransaction();
            logInfo('Salary save after generation of data.');
            // Save the salary after generation.
            if (count($dataToSave)) {
                logInfo('Payment data insert begin');
                Payment::insert($dataToSave);
                logInfo('Payment data insert completed');
            }

            if (count($perksDataToSave)) {
                logInfo('Perks data insert begin');
                PaymentPerksMapping::insert($perksDataToSave);
                logInfo('Perks data insert completed');
            }

            if (count($perksDataToSave)) {
                logInfo('Non cash deduction data insert begin');
                PaymentDeductionMapping::insert($nonCashDeductionToSave);
                logInfo('Non cash deduction data insert completed');
            }

            DB::commit();
            $this->notify('Salary generated successfully.')->send();
            logInfo('Salary data saving process completed');
        } catch (Exception $e) {
            DB::rollBack();
            $this->notify('Unable to save salary data.')->type('error')->duration(5)->send();
            logError('Unable to save salary data.', $e);
        }
    }

    public function prepareDataToSave($structure)
    {
        // Prepare data for advance salary if cashInhandPerMonth is negative.
        if ($structure['cash_in_hand'] < 0 && vianetHrm()) {
            // Set the cash in hand per month to 0.
            $structure['cash_in_hand'] = 0;
        }

        $dataToSave = [
            'uuid'                  => "{$structure['id']}-{$structure['employee_id']}-{$structure['year']}-{$structure['month']}",
            'payslip_id'            => $structure['id'],
            'employee_id'           => $structure['employee_id'],
            'year'                  => $structure['year'],
            'month'                 => $structure['month'],
            'fiscal_year'           => $structure['fiscal_year'],
            'total_month_days'      => $structure['total_month_days'],
            'total_salary_days'     => $structure['total_salary_days'],
            'current_basic_salary'  => $structure['basic_salary']['current'],
            'earned_basic_salary'   => $structure['basic_salary']['earned'],
            'current_ssf'           => $structure['ssf_contribution']['current'],
            'earned_ssf'            => $structure['ssf_contribution']['earned'],
            'current_allowance'     => $structure['allowance']['current'],
            'earned_allowance'      => $structure['allowance']['earned'],
            'annual_taxable_income' => $structure['annual_taxable_income'],
            'deduction_limit'       => $structure['deduction_limit'],
            'insurance'             => $structure['insurance'],
            'current_cit_deduction' => $structure['cit']['current'],
            'past_cit_deduction'    => $structure['cit']['earned'],
            'current_ssf_deduction' => $structure['ssf_deduction']['current'],
            'past_ssf_deduction'    => $structure['ssf_deduction']['earned'],
            'current_ssf_deduction_for_tax' => $structure['ssf_deduction_for_tax']['current'],
            'past_ssf_deduction_for_tax'    => $structure['ssf_deduction_for_tax']['earned'],
            'annual_taxable_amount' => $structure['annual_taxable_amount'],
            'emergency_fund'        => $structure['emergency_fund'],
            // 'yearly_sst'            => $structure[''],
            'yearly_tds'            => $structure['yearly_tax'],
            // 'remaining_sst_paid'    => $structure[''],
            'remaining_tds'         => $structure['remaining_tax'],
            'current_sst_paid'      => $structure['current_sst_paid'],
            'paid_tds'              => $structure['paid_tds'],
            'paid_sst'              => $structure['paid_sst'],
            'current_tds_paid'      => $structure['tax'],
            'cash_in_hand'          => $structure['cash_in_hand'],
            'actual_payment_meta'   => json_encode($structure)
        ];

        $perksDataToSave = [];
        $additional_allowance = $structure['additional_allowance'];
        // foreach ($structure['payslip_perks'] as $perk) {
        //     $perkName = $perk['perks']['name'];
        //     $perksDataToSave[] = [
        //         'payment_uuid'              => "{$structure['id']}-{$structure['employee_id']}-{$structure['year']}-{$structure['month']}",
        //         "perks_id"          => $perk['perks']['id'],
        //         "actual_amount"     => $additional_allowance['actual'][$perkName] ?? 0,
        //         "earned_amount"     => $additional_allowance['earned'][$perkName] ?? 0,
        //         "current_amount"    => $additional_allowance['current'][$perkName] ?? 0,
        //     ];
        // }
        foreach ($structure['additional_allowance']['total'] as $perk_name => $perk_value) {
            $perkName = $perk_name;
            $perkId = $this->perkList[$perkName] ?? "";
            if ($perkId) {
                $perksDataToSave[] = [
                    'payment_uuid'      => "{$structure['id']}-{$structure['employee_id']}-{$structure['year']}-{$structure['month']}",
                    "perks_id"          => $perkId,
                    "actual_amount"     => $additional_allowance['actual'][$perkName] ?? 0,
                    "earned_amount"     => $additional_allowance['earned'][$perkName] ?? 0,
                    "current_amount"    => $additional_allowance['current'][$perkName] ?? 0,
                ];
            }
        }

        $deductionDataToSave = [];
        foreach ($structure['deduction'] as $deduction) {
            $deductionDataToSave[] = [
                'payment_uuid'      => "{$structure['id']}-{$structure['employee_id']}-{$structure['year']}-{$structure['month']}",
                'name'              => $deduction['name'],
                'actual_amount'     => $deduction['actual'],
                'deducted_amount'   => $deduction['earned'],
                'current_amount'    => $deduction['current'],
            ];
        }

        return ['dataToSave' => $dataToSave, 'perksDataToSave' => $perksDataToSave, 'nonCashDeduction' => $deductionDataToSave];
    }

    public function previewSalary()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(300);
        $this->reset(['page']);

        $paymentsQuery = Payment::leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
            ->where(function ($query) {
                $query->Where(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->where([
                ['year', $this->nep_year],
                ['month', $this->nep_month]
            ])
            ->select('payments.actual_payment_meta', 'payments.adjusted_payment_meta')->get();

        if (!count($paymentsQuery) && !$this->salaryGenerated) {
            if (!$this->search) {
                $this->notify('Please generate salary sheet first.')->type('error')->send();
                return;
            }
        }

        $this->salaryStructure = $paymentsQuery->map(function ($payment) {
            $meta = $payment->adjusted_payment_meta ?? $payment->actual_payment_meta;
            return json_decode($meta, true);
        })->toArray();

        // Remove null elements
        $this->salaryStructure = array_filter($this->salaryStructure, function ($value) {
            return !is_null($value);
        });

        unset($this->salaryList);
        $this->dispatch('reloadTooltip');
    }

    public function exportSalarySheet()
    {
        $this->setDateAndYear();

        $month = $this->nepaliMonthList[$this->nep_month];

        $fileName = "Salary Sheet ($this->nep_year $month).xlsx";
        ini_set('memory_limit', '512M');
        set_time_limit(300);
        try {
            return Excel::download(new SalarySheetExportArray($this->salaryStructure), fileName: $fileName);
        } catch (Exception $e) {
            logError("Unable to export salary.", $e);
            $this->notify('Error exporting salary sheet.')->type('error')->send();
        }
    }

    public function saveSalary()
    {
        // Check if the salary for the month is already locked.
        $payments = Payment::leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->where([['year', $this->nep_year], ['month', $this->nep_month], ['is_locked', 1]])
            ->first();
        if ($payments) {
            $this->notify('Salary for this month is already locked.')->type('error')->send();
            return;
        }

        // Settlement of advance salary
        $advanceSalaries = AdvanceSalary::where([['is_settled', false], ['nep_year', '<=', $this->nep_year], ['nep_month', '<=', $this->nep_month]])
            ->get()
            ->pluck('id')
            ->toArray();

        $currentNepaliDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y-m-d');

        $currentTimestamp = Carbon::now();
        $advanceSalaryDataToSave = [];

        $year = $this->nep_year;
        $month = $this->nep_month + 1;
        if ($month > 12) {
            $year += 1;
            $month = 1;
        }
        foreach ($this->salaryStructure as $structure) {
            if ($structure['advance_salary_deduct']) {
                AdvanceSalary::whereIn('id', $advanceSalaries)->where('employee_id', $structure['employee_id'])->update(['is_settled' => true, 'nep_settlement' => $currentNepaliDate]);
            }
            // Prepare data to save in advance salary if cashInhandPerMonth is negative.
            if ($structure['cash_in_hand'] < 0 && vianetHrm()) {
                $month = $month < 10 ? "0" . $month : $month;
                $advanceSalaryDataToSave[] = [
                    'employee_id'       => $structure['employee_id'],
                    'payslip_id'        => $structure['id'],
                    'nep_year'          => $year,
                    'nep_month'         => $month,
                    'nep_taken_on'      => $currentNepaliDate,
                    'amount'            => abs($structure['cash_in_hand']),
                    'is_settled'        => false,
                    'nep_settlement'    => '',
                    'type'              => AdvanceSalaryType::BULK, // EMI or Bulk
                    'remarks'           => json_encode(array_filter([
                        'total_month_income'    => $structure['total_per_month'],
                        'cit'                   => $structure['cit']['current'] > 0 ? $structure['cit']['current'] : null,
                        'ssf_deduction'         => $structure['ssf_deduction']['current'] > 0 ? $structure['ssf_deduction']['current'] : null,
                        'tax'                   => $structure['tax'] > 0 ? $structure['tax'] : 0,
                        'emergency_fund'        => $structure['emergency_fund'] > 0 ? $structure['emergency_fund'] : null
                    ])),
                    'created_at'        => $currentTimestamp,
                    'updated_at'        => $currentTimestamp
                ];
            }
        }

        try {
            DB::beginTransaction();
            logInfo('Salary data locking process begin');

            // Change the lock status of the payments
            Payment::leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->where([['year', $this->nep_year], ['month', $this->nep_month]])
                ->update(['is_locked' => 1]);

            // Save advance salary
            if (count($advanceSalaryDataToSave)) {
                logInfo('Advance salary data insert begin');
                AdvanceSalary::insert($advanceSalaryDataToSave);
                logInfo('Advance salary data insert completed');
            }

            // After salary is saved, expire the payslip of terminated employee of the month.
            logInfo('Expire payslip of employee who is terminated in the month.');
            $nep_start_date = "{$this->nep_year}-{$this->nep_month}-01";
            $nep_end_date = "{$this->nep_year}-{$this->nep_month}-{$this->total_days_of_month}";
            $eng_start_date = LaravelNepaliDate::from($nep_start_date)->toEnglishDate('Y-m-d');
            $eng_end_date = LaravelNepaliDate::from($nep_end_date)->toEnglishDate('Y-m-d');

            $terminatedEmployeeOfMonth = EmployeeOrg::query()
                ->onlyTrashed()
                ->leftJoin('employees', 'employees.id', '=', 'employee_org.employee_id')
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->whereBetween('employee_org.termination_date', [$eng_start_date, $eng_end_date])
                ->pluck('employee_id');
            logInfo('List of terminated employee of the month: ', $terminatedEmployeeOfMonth->toArray());
            if (count($terminatedEmployeeOfMonth->toArray())) {
                Payslip::whereIn('employee_id', $terminatedEmployeeOfMonth)->update(['status' => 'Expired']);
                logInfo('Payslip expired successfully of terminated employee.');
            }

            // Unfreeze the salary of the employee.
            logInfo('Unfreezing employee for salary calculation');
            $querySalaryCalculationStopDate = SalaryCalculationStopDate::leftJoin('employees', 'employees.id', '=', 'salary_calculation_stop_dates.employee_id')
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->where('nep_start_date', '<=', $nep_end_date)
                ->where('nep_stop_date', '<=', $nep_end_date)
                ->where('is_active', 1);
            if ($querySalaryCalculationStopDate->exists()) {
                logInfo('Unfreezed employee for salary calculation: ' . print_r($querySalaryCalculationStopDate?->pluck('employee_id')?->toArray() ?? [], true));
                $querySalaryCalculationStopDate->update([
                    'is_active' => 0,
                ]);
            } else {
                logInfo('No employee found to unfreeze the salary calculation.');
            }

            DB::commit();
            $this->notify('Salary data lock successfully')->duration(5)->send();
            logInfo('Salary data locking process completed');
            $this->checkButtonsToDisplay();
        } catch (Exception $e) {
            DB::rollBack();
            $this->notify('Unable to lock salary data.')->type('error')->duration(5)->send();
            logError('Unable to lock salary data.', $e);
        }
    }

    public function updated($property)
    {
        $validProperties = ["search", "perPage"];
        if (in_array($property, $validProperties)) {
            $this->previewSalary();
        }
    }

    public function gotoPage($page)
    {
        $this->page = $page;
        unset($this->salaryList);
        $this->dispatch('reloadTooltip');
    }

    public function nextPage()
    {
        $this->page = ($this->page ?: LengthAwarePaginator::resolveCurrentPage() ?: 1) == count($this->salaryStructure) ? count($this->salaryStructure) : $this->page + 1;
        unset($this->salaryList);
        $this->dispatch('reloadTooltip');
    }

    public function previousPage()
    {
        $this->page = ($this->page ?: LengthAwarePaginator::resolveCurrentPage() ?: 1) == 1 ? 1 : $this->page - 1;
        unset($this->salaryList);
        $this->dispatch('reloadTooltip');
    }

    #[Computed(persist: true)]
    public function salaryList()
    {
        $page = $this->page ?: LengthAwarePaginator::resolveCurrentPage() ?: 1;
        $slice = array_slice($this->salaryStructure, ($page - 1) * $this->perPage, $this->perPage);

        $salary_structure_paginated = new LengthAwarePaginator(
            $slice,
            count($this->salaryStructure),
            $this->perPage,
            $page,
        );

        return $salary_structure_paginated;
    }

    public function setDateAndYear()
    {
        $fiscalYear = $this->fiscal_year ? FiscalYear::where('id', $this->fiscal_year)->first() : FiscalYear::where('is_active', true)->first();

        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);

        $this->nep_year = $start_date_year;

        // Initialize year according to selected month
        if ($this->nep_month < $start_date_month) {
            $this->nep_year = $end_date_year;
        }

        $this->showEmergencyFundField = false;

        if (vianetHrm()) {
            $this->showEmergencyFundField = true;
            $this->showAdvSalaryField = true;
        }
    }

    public function selectedEmployeeIds()
    {
        return Employee::leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->where(function ($query) {
                $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
            })
            ->pluck(
                'employees.id as employee_id',
            )->toArray();
    }

    public function getFiscalYear($id, $fiscalYearList)
    {
        $fiscalYearArray = $fiscalYearList->toArray();
        $index = array_search($id, array_column($fiscalYearArray, 'id'));
        return ($index !== false) ? $fiscalYearArray[$index]['name'] : null;
    }

    public function checkButtonsToDisplay()
    {
        $payment = function () {
            return Payment::leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->where([['year', $this->nep_year], ['month', $this->nep_month]]);
        };

        if (count($payment()->get())) {
            if (count($payment()->where('actual_payment_meta', '!=', null)->get()))
                $this->showPreviewButton = true;
        }

        $hasPayment = $payment()
            ->where('is_locked', true)
            ->get();
        if (count($hasPayment)) {
            $this->showGenerateButton = false;
            $this->showUploadSalaryButton = false;
            $this->showLockSalaryButton = false;
        }
    }

    #[Computed(persist: true)]
    public function perkList()
    {
        return Perk::all()->pluck('id', 'name')->toArray();
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::whereNull('deleted_at')->get();
    }

    public function render()
    {
        return view('livewire.payroll.salary-structure.includes.salary-calculation');
    }
}
