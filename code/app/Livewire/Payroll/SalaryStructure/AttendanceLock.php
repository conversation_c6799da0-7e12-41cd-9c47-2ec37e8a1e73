<?php

namespace App\Livewire\Payroll\SalaryStructure;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\AttendanceForSalaryExport;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\BypassType;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Models\Payroll\AttendanceCount;
use App\Models\Payroll\PayrollBypassEmployee;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Attendance Lock')]
class AttendanceLock extends Component
{
    use WithDataTable, WithNotify;

    public $fiscal_year, $nep_year, $nep_month, $showAttendanceList = false;

    public $company_id, $region_id, $branch_id, $department_id, $employee_id;

    public function __construct()
    {
        $this->sortBy = "employees.first_name, employees.middle_name, employees.last_name";
        $this->sortDirection = "asc";
        $this->tableListVariable = "attendanceData";
    }

    public function mount($fiscal_year = null, $nep_month = null)
    {
        $this->fiscal_year = $fiscal_year ?? FiscalYear::where('is_active', true)->first()?->id;
        $this->nep_month = $nep_month ?? LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm');

        $this->setDateAndYear();

        if (!scopeAll()) {
            $this->company_id = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->region_id = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->branch_id = currentEmployee()?->organizationInfo?->branch_id;

            if (scopeBranch())
                return;
            $this->department_id = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    public function rules()
    {
        return [
            'fiscal_year' => 'required',
            'nep_month' => 'required'
        ];
    }

    public function validationAttributes()
    {
        return [
            'nep_month' => 'nepali month'
        ];
    }

    public function generateAttendance()
    {
        $this->reset(['search']);
        $this->validate();
        unset($this->attendanceBypassEmployeeIds);
        $this->setDateAndYear();
        $this->saveGeneratedAttendance();
        $this->showAttendanceList = true;
    }

    public function previewAttendance()
    {
        $this->reset(['search']);
        $this->validate();
        $this->setDateAndYear();
        $this->showAttendanceList = true;
        $this->dispatch("reloadTooltip");
    }

    // public function calculateAttendance()
    // {
    //     $this->setDateAndYear();

    //     $daysInMonth = LaravelNepaliDate::daysInMonth("" . $this->nep_month, "" . $this->nep_year);
    //     $laravelNepaliDate = new FacadesLaravelNepaliDate;
    //     $engDateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth($this->nep_month, $this->nep_year);

    //     $terminatedEmployeeQuery = EmployeeOrg::whereBetween('termination_date', [$engDateRange['startdate'], $engDateRange['enddate']])
    //         ->withTrashed();

    //     // Delete attendance of terminated employee from termination date.
    //     $terminatedEmployees = $terminatedEmployeeQuery->select("employee_id", "termination_date")->get();
    //     foreach ($terminatedEmployees as $employee) {
    //         if (fedexHrm())
    //             Attendance::where([['employee_id', $employee->employee_id], ['date_en', '>=', $employee->termination_date]])->delete();
    //         else
    //             Attendance::where([['employee_id', $employee->employee_id], ['date_en', '>', $employee->termination_date]])->delete();
    //     }

    //     // Delete attendance of newly join employee before their date of join.
    //     $newEmployeeQuery = EmployeeOrg::whereBetween('doj', [$engDateRange['startdate'], $engDateRange['enddate']])->select("employee_id", "doj")->get();
    //     foreach ($newEmployeeQuery as $employee) {
    //         Attendance::where([['employee_id', $employee->employee_id], ['date_en', '<', $employee->doj]])->delete();
    //     }

    //     unset($this->attendanceBypassEmployeeIds);

    //     $terminatedEmployeeIds = $terminatedEmployeeQuery->pluck('employee_id')->toArray();
    //     $query = Employee::query()
    //         ->leftJoin('attendance as att', 'employees.id', '=', 'att.employee_id')
    //         ->leftJoin('leave_requests as lr', 'att.leave_request_id', '=', 'lr.id')
    //         ->leftJoin('leave_options as lo', 'lr.leave_option_id', '=', 'lo.id')
    //         ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
    //         ->leftJoin('companies as company', 'company.id', '=', 'employees.company_id')
    //         ->leftJoin('branches as branch', 'branch.id', '=', 'org.branch_id')
    //         ->leftJoin('departments as department', 'department.id', '=', 'org.department_id')
    //         ->leftJoin('outsource_companies as vendor', 'vendor.id', '=', 'org.outsource_company_id')
    //         ->when($this->employee_id, function ($query) {
    //             $query->where('employees.id', $this->employee_id);
    //         })
    //         ->when($this->company_id, function ($query) {
    //             $query->where('employees.company_id', $this->company_id);
    //         })
    //         ->when($this->region_id, function ($query) {
    //             $query->where('org.region_id', $this->region_id);
    //         })
    //         ->when($this->branch_id, function ($query) {
    //             $query->where('org.branch_id', $this->branch_id);
    //         })
    //         ->when($this->department_id, function ($query) {
    //             $query->where('org.department_id', $this->department_id);
    //         })
    //         ->where(function ($query) use ($terminatedEmployeeIds) {
    //             $query->where('att.date_np', 'like', "$this->nep_year-$this->nep_month-%")
    //                 ->whereNull('att.deleted_at')
    //                 ->where(function ($query) {
    //                     $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
    //                         ->orWhere(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
    //                         ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
    //                 });
    //         })
    //         ->orWhere(function ($query) use ($terminatedEmployeeIds) {
    //             $query->where('att.date_np', 'like', "$this->nep_year-$this->nep_month-%")
    //                 ->whereNull('att.deleted_at')
    //                 ->where(function ($query) {
    //                     $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
    //                         ->orWhere(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
    //                         ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
    //                 })
    //                 ->whereIn('employees.id', $terminatedEmployeeIds);
    //         })
    //         ->orWhere(function ($query) {
    //             $query->where(function ($query) {
    //                 $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
    //                     ->orWhere(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
    //                     ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
    //             })
    //                 ->whereIn('employees.id', $this->attendanceBypassEmployeeIds);
    //         })
    //         ->orderBy(DB::raw("CONCAT_WS(' ', $this->sortBy)"), $this->sortDirection)
    //         ->select(
    //             'employees.id as employee_id',
    //         )
    //         ->withTrashed();

    //     $query->selectRaw(DB::raw("TRIM(CONCAT_WS(' ', first_name, NULLIF(middle_name, ''), last_name)) as employee"));
    //     $query->selectRaw(DB::raw("CONCAT(company.code, '-', org.employee_code) as code"));
    //     $query->selectRaw(DB::raw("company.name as company"));
    //     $query->selectRaw(DB::raw("vendor.name as vendor"));
    //     $query->selectRaw(DB::raw("branch.name as branch"));
    //     $query->selectRaw(DB::raw("department.name as department"));
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COUNT(CASE WHEN att.out_remarks = 'Early Out' THEN 1 END)
    //         END AS early_out"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COUNT(CASE WHEN att.in_remarks = 'Late In' THEN 1 END)
    //         END AS late_in"
    //     );
    //     // $query->selectRaw(
    //     //     "CASE
    //     //         WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //     //         ELSE COUNT(CASE WHEN att.leave_status = True THEN 1 END)
    //     //     END AS leave_days"
    //     // );
    //     $query->selectRaw("SUM(
    //     CASE 
    //         WHEN att.leave_status = 1 THEN IFNULL(lo.num_days, 0)
    //         ELSE 0
    //     END
    // ) AS leave_days");
    //     // $totalDaysCountCase = "COUNT(CASE WHEN !(att.leave_status = 0 AND (att.status = 'Absent' OR att.status LIKE '%Unpaid Leave%')) THEN 1 END)";
    //     // $totalDaysCountCase = "SUM(
    //     //     CASE
    //     //         WHEN att.status IN ('Day Off', 'Holiday', 'Work on Day Off', 'Work on Holiday')
    //     //             OR att.in_time IS NOT NULL
    //     //             OR att.out_time IS NOT NULL
    //     //         THEN 1
    //     //         WHEN att.leave_status = 1 THEN IFNULL(lo.num_days, 0)
    //     //         ELSE 0
    //     //     END
    //     // )";
    //     $totalDaysCountCase = "SUM(
    //         CASE
    //             WHEN att.leave_status = 1 AND (att.in_time IS NULL AND att.out_time IS NULL)
    //             THEN IFNULL(lo.num_days, 0)
    //             WHEN att.leave_status = 1 AND (att.in_time IS NOT NULL OR att.out_time IS NOT NULL)
    //             THEN 1
    //             WHEN att.in_time IS NOT NULL
    //                 OR att.out_time IS NOT NULL
    //             THEN 1
    //             WHEN !(att.leave_status = 0 AND (att.status = 'Absent' OR att.status LIKE '%Unpaid Leave%'))
    //             THEN 1
    //             ELSE 0
    //         END
    //     )";
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COUNT(CASE WHEN att.leave_status = false AND (att.status = 'Absent' OR att.status LIKE '%Unpaid Leave%') THEN 1 END)
    //         END AS absent_days"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COUNT(CASE WHEN att.leave_status = True OR att.status LIKE '%' THEN 1 END)
    //         END AS attendance_days"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COUNT(CASE WHEN att.status = 'Day Off' THEN 1 END)
    //         END AS day_off"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE SUM(CASE WHEN att.in_time LIKE '%' OR att.out_time LIKE '%' AND att.leave_status = 1 THEN IFNULL(lo.num_days, 1) ELSE 0 END)
    //         END AS present_days"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN {$daysInMonth}
    //             ELSE CASE
    //                 WHEN {$totalDaysCountCase} < 0
    //                 THEN 0
    //                 WHEN {$totalDaysCountCase} > {$daysInMonth}
    //                 THEN {$daysInMonth}
    //                 ELSE {$totalDaysCountCase}
    //             END
    //         END AS salary_days"
    //     );
    //     $query->selectRaw(
    //         "CASE
    //             WHEN " . (empty($this->attendanceBypassEmployeeIds) ? 'false' : 'employees.id IN (' . implode(',', $this->attendanceBypassEmployeeIds) . ')') . " THEN 0
    //             ELSE COALESCE(ROUND(SUM(TIME_TO_SEC(att.total_hours)) / 3600, 2), 0)
    //         END AS total_working_hour"
    //     );
    //     $query->selectRaw("'{$this->nep_year}' as nep_year");
    //     $query->selectRaw("'{$this->nep_month}' as nep_month");

    //     $query->groupBy(['employee_id', 'employees.first_name', 'employees.middle_name', 'employees.last_name', 'company.code', 'company.name', 'vendor.name', 'branch.name', 'department.name', 'org.employee_code']);
    //     $this->dispatch("reloadTooltip");
    //     return $query;
    // }



    public function calculateAttendance()
    {
        $this->setDateAndYear();

        // Input validation
        if (empty($this->nep_year) || empty($this->nep_month)) {
            throw new InvalidArgumentException('Nepali year and month are required');
        }

        $daysInMonth = LaravelNepaliDate::daysInMonth($this->nep_month, $this->nep_year);
        $laravelNepaliDate = new FacadesLaravelNepaliDate;
        $engDateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth($this->nep_month, $this->nep_year);

        // Clean up attendance data for terminated and new employees
        $this->cleanupAttendanceData($engDateRange);

        // Get terminated employee IDs for the month
        $terminatedEmployeeIds = $this->getTerminatedEmployeeIds($engDateRange);

        // Get attendance bypass employee IDs (with null check)
        $attendanceBypassEmployeeIds = $this->attendanceBypassEmployeeIds ?? [];

        // Build the main query
        $query = $this->buildMainQuery($terminatedEmployeeIds, $attendanceBypassEmployeeIds, $daysInMonth);

        $this->dispatch("reloadTooltip");
        return $query;
    }

    private function cleanupAttendanceData($engDateRange)
    {
        // Handle terminated employees
        $terminatedEmployees = EmployeeOrg::whereBetween('termination_date', [
            $engDateRange['startdate'],
            $engDateRange['enddate']
        ])
            ->withTrashed()
            ->select('employee_id', 'termination_date')
            ->get();

        $operator = fedexHrm() ? '>=' : '>';
        foreach ($terminatedEmployees as $employee) {
            Attendance::where('employee_id', $employee->employee_id)
                ->where('date_en', $operator, $employee->termination_date)
                ->delete();
        }

        // Handle new employees
        $newEmployees = EmployeeOrg::whereBetween('doj', [
            $engDateRange['startdate'],
            $engDateRange['enddate']
        ])
            ->select('employee_id', 'doj')
            ->get();

        foreach ($newEmployees as $employee) {
            Attendance::where('employee_id', $employee->employee_id)
                ->where('date_en', '<', $employee->doj)
                ->delete();
        }
    }

    private function getTerminatedEmployeeIds($engDateRange)
    {
        return EmployeeOrg::whereBetween('termination_date', [
            $engDateRange['startdate'],
            $engDateRange['enddate']
        ])
            ->withTrashed()
            ->pluck('employee_id')
            ->toArray();
    }

    private function buildMainQuery($terminatedEmployeeIds, $attendanceBypassEmployeeIds, $daysInMonth)
    {
        // First, create a subquery to get the latest attendance record per employee per day
        $latestAttendanceSubquery = DB::table('attendance as att_sub')
            ->select('att_sub.employee_id', 'att_sub.date_en', DB::raw('MAX(att_sub.id) as latest_id'))
            ->where('att_sub.date_np', 'like', "{$this->nep_year}-{$this->nep_month}-%")
            ->whereNull('att_sub.deleted_at')
            ->groupBy('att_sub.employee_id', 'att_sub.date_en');

        $query = Employee::query()
            ->leftJoinSub($latestAttendanceSubquery, 'latest_att', function ($join) {
                $join->on('employees.id', '=', 'latest_att.employee_id');
            })
            ->leftJoin('attendance as att', 'latest_att.latest_id', '=', 'att.id')
            ->leftJoin('leave_requests as lr', 'att.leave_request_id', '=', 'lr.id')
            ->leftJoin('leave_types as lt', 'lr.leave_type_id', '=', 'lt.id')
            ->leftJoin('leave_options as lo', 'lr.leave_option_id', '=', 'lo.id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->leftJoin('companies as company', 'company.id', '=', 'employees.company_id')
            ->leftJoin('branches as branch', 'branch.id', '=', 'org.branch_id')
            ->leftJoin('departments as department', 'department.id', '=', 'org.department_id')
            ->leftJoin('outsource_companies as vendor', 'vendor.id', '=', 'org.outsource_company_id')
            ->withTrashed();

        // Apply filters
        $this->applyFilters($query);

        // Apply search and attendance conditions
        $this->applySearchAndAttendanceConditions($query, $terminatedEmployeeIds, $attendanceBypassEmployeeIds);

        // Add select clauses
        $this->addSelectClauses($query, $attendanceBypassEmployeeIds, $daysInMonth);

        // Add ordering and grouping
        $query->orderBy(DB::raw("TRIM(CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name))"), $this->sortDirection)
            ->groupBy([
                'employees.id',
                'employees.first_name', // Fixed typo: was 'employes.first_name'
                'employees.middle_name',
                'employees.last_name',
                'company.code',
                'company.name',
                'vendor.name',
                'branch.name',
                'department.name',
                'org.employee_code'
            ]);

        return $query;
    }

    private function applyFilters($query)
    {
        $query->when($this->employee_id, function ($query) {
            $query->where('employees.id', $this->employee_id);
        })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->region_id, function ($query) {
                $query->where('org.region_id', $this->region_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            });
    }

    private function applySearchAndAttendanceConditions($query, $terminatedEmployeeIds, $attendanceBypassEmployeeIds)
    {
        $searchCondition = function ($query) {
            if (!empty($this->search)) {
                $query->where(function ($query) {
                    $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
                        ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                        ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
                });
            }
        };

        $query->where(function ($query) use ($terminatedEmployeeIds, $attendanceBypassEmployeeIds, $searchCondition) {
            // Regular employees with attendance data
            $query->where(function ($query) use ($searchCondition) {
                $query->where('att.date_np', 'like', "{$this->nep_year}-{$this->nep_month}-%")
                    ->whereNull('att.deleted_at');
                $searchCondition($query);
            })
                // Include terminated employees
                ->orWhere(function ($query) use ($terminatedEmployeeIds, $searchCondition) {
                    $query->where('att.date_np', 'like', "{$this->nep_year}-{$this->nep_month}-%")
                        ->whereNull('att.deleted_at')
                        ->whereIn('employees.id', $terminatedEmployeeIds);
                    $searchCondition($query);
                })
                // Include bypass employees (even without attendance data)
                ->orWhere(function ($query) use ($attendanceBypassEmployeeIds, $searchCondition) {
                    if (!empty($attendanceBypassEmployeeIds)) {
                        $query->whereIn('employees.id', $attendanceBypassEmployeeIds);
                        $searchCondition($query);
                    }
                });
        });
    }

    private function addSelectClauses($query, $attendanceBypassEmployeeIds, $daysInMonth)
    {
        $isBypassEmployee = $this->getBypassEmployeeCondition($attendanceBypassEmployeeIds);

        $query->select('employees.id as employee_id')
            ->selectRaw("TRIM(CONCAT_WS(' ', employees.first_name, NULLIF(employees.middle_name, ''), employees.last_name)) as employee")
            ->selectRaw("CONCAT(company.code, '-', org.employee_code) as code")
            ->selectRaw("company.name as company")
            ->selectRaw("vendor.name as vendor")
            ->selectRaw("branch.name as branch")
            ->selectRaw("department.name as department")
            ->selectRaw("'{$this->nep_year}' as nep_year")
            ->selectRaw("'{$this->nep_month}' as nep_month");

        // Attendance metrics
        $this->addAttendanceMetrics($query, $isBypassEmployee, $daysInMonth);
    }

    private function getBypassEmployeeCondition($attendanceBypassEmployeeIds)
    {
        if (empty($attendanceBypassEmployeeIds)) {
            return 'FALSE';
        }

        // Safely create IN clause with escaped values
        $escapedIds = array_map(function ($id) {
            return (int) $id; // Ensure integer values
        }, $attendanceBypassEmployeeIds);

        return 'employees.id IN (' . implode(',', $escapedIds) . ')';
    }

    private function addAttendanceMetrics($query, $isBypassEmployee, $daysInMonth)
    {
        // Early out count
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE COUNT(CASE WHEN att.out_remarks = 'Early Out' THEN 1 END) END AS early_out");

        // Late in count
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE COUNT(CASE WHEN att.in_remarks = 'Late In' THEN 1 END) END AS late_in");

        // Leave days (with proper decimal handling)
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE SUM(CASE WHEN att.leave_status = 1 THEN COALESCE(lo.num_days, 0) ELSE 0 END) END AS leave_days");

        // Absent days
        $query->selectRaw("
            CASE 
                WHEN {$isBypassEmployee} THEN 0 
                ELSE SUM(
                    CASE 
                        WHEN att.leave_status = 0 
                            AND att.status = 'Absent'
                            OR (lt.paid = 0 
                            AND (att.in_time IS NULL AND att.out_time IS NULL)) 
                            THEN 1
                        WHEN att.leave_status = 0 
                            AND lt.paid = 0 
                            AND (att.in_time IS NOT NULL AND att.out_time IS NOT NULL) 
                            THEN (1 - COALESCE(lo.num_days, 0))
                        WHEN att.leave_status = 1 
                            AND (att.in_time IS NULL AND att.out_time IS NULL) THEN (1 - COALESCE(lo.num_days, 0))
                        ELSE 0
                    END
                )
            END AS absent_days
        ");

        // Present days (fixed logic)
        $query->selectRaw("
            CASE 
                WHEN {$isBypassEmployee} THEN 0 
                ELSE SUM(
                    CASE 
                        WHEN (att.in_time IS NOT NULL OR att.out_time IS NOT NULL) AND att.leave_status = 1 
                            THEN COALESCE(lo.num_days, 1) 
                        WHEN lt.paid = 0 
                            AND (att.in_time IS NOT NULL AND att.out_time IS NOT NULL) 
                            THEN (1 - COALESCE(lo.num_days, 0))
                        WHEN att.in_time IS NOT NULL OR att.out_time IS NOT NULL 
                            THEN 1 
                        ELSE 0 
                    END
                ) 
            END AS present_days
        ");

        // Day off count
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE COUNT(CASE WHEN att.status = 'Day Off' THEN 1 END) END AS day_off");

        // Total working hours
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE COALESCE(ROUND(SUM(TIME_TO_SEC(att.total_hours)) / 3600, 2), 0) END AS total_working_hour");

        // Salary days calculation
        $totalDaysCase = $this->getSalaryDaysCalculation();
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN {$daysInMonth} ELSE CASE WHEN ({$totalDaysCase}) < 0 THEN 0 WHEN ({$totalDaysCase}) > {$daysInMonth} THEN {$daysInMonth} ELSE ({$totalDaysCase}) END END AS salary_days");

        // Attendance days (fixed logic)
        $query->selectRaw("CASE WHEN {$isBypassEmployee} THEN 0 ELSE COUNT(CASE WHEN att.leave_status = 1 OR att.status IS NOT NULL THEN 1 END) END AS attendance_days");
    }

    private function getSalaryDaysCalculation()
    {
        return "SUM(
            CASE
                -- Paid leave
                WHEN att.leave_status = 1 THEN
                    CASE 
                        WHEN att.in_time IS NULL AND att.out_time IS NULL 
                            THEN COALESCE(lo.num_days, 0)
                        ELSE 1
                    END
    
                -- Unpaid leave
                WHEN att.leave_status = 0 AND lt.paid = 0 THEN
                    CASE
                        WHEN att.in_time IS NOT NULL OR att.out_time IS NOT NULL
                            THEN (1 - COALESCE(lo.num_days, 0))
                        ELSE 0
                    END
    
                -- Present for work
                WHEN att.in_time IS NOT NULL OR att.out_time IS NOT NULL
                    THEN 1
    
                -- Not marked as absent
                WHEN NOT (att.leave_status = 0 AND att.status = 'Absent')
                    THEN 1
    
                ELSE 0
            END
        )";
    }

    public function lockAttendanceData()
    {
        $attendanceQuery = function () {
            return AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
                ->when($this->employee_id, function ($query) {
                    $query->where('employees.id', $this->employee_id);
                })
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->when($this->branch_id, function ($query) {
                    $query->where('org.branch_id', $this->branch_id);
                })
                ->when($this->department_id, function ($query) {
                    $query->where('org.department_id', $this->department_id);
                })
                ->where('nep_year', $this->nep_year)
                ->where('nep_month', $this->nep_month);
        };

        // Check if attendance is generated
        $isAttendanceGenerated = $attendanceQuery()->where('is_locked', false)->first();
        if (!$isAttendanceGenerated) {
            $this->notify("Please generate attendance before locking it.")->type('error')->send();
            return;
        }

        // Check if attendance is already locked
        $isAttendanceLocked = $attendanceQuery()->where('is_locked', true)->first();
        if ($isAttendanceLocked) {
            $this->notify("Attendance for the selected year and month is already locked.")->type('error')->send();
            return;
        }

        // Lock the attendance
        DB::beginTransaction();
        try {
            $attendanceQuery()->update(['is_locked' => true]);
            $this->notify("Attendance for the selected year and month has been locked. Now salary processing can be done.")
                ->duration(6)
                ->type('success')
                ->send();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $this->notify("Something went wrong.")->type('error')->send();
            logError('Fail to lock attendance for salary processing. ', $e);
        }
    }

    public function saveGeneratedAttendance()
    {
        $calcAttendance = $this->calculateAttendance();
        $attendanceData = $calcAttendance->get()->toArray();
        $data = [];
        $dailyAttendance = $this->getEmployeeDailyAttendanceForMonth($calcAttendance->pluck('employee_id'));
        $uploadedAttendance = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month], ['is_locked', false], ['is_uploaded', true]])
            ->pluck('org.employee_id')
            ->toArray();

        ini_set('memory_limit', '512M');
        set_time_limit(300);
        foreach ($attendanceData as $attData) {
            if (in_array($attData["employee_id"], $uploadedAttendance)) {
                continue;
            }
            if ($dailyAttendance[$attData["employee_id"]] ?? null) {
                $attData['daily_attendance'] = $dailyAttendance[$attData["employee_id"]];
                $data[] = [
                    'nep_year' => $attData["nep_year"],
                    'nep_month' => $attData["nep_month"],
                    'employee_id' => $attData["employee_id"],
                    'attendance_days' => $attData["attendance_days"],
                    'salary_days' => $attData["salary_days"],
                    'present_days' => $attData["present_days"],
                    'absent_days' => $attData["absent_days"],
                    'day_off' => $attData["day_off"],
                    'leave_days' => $attData["leave_days"],
                    'total_working_hour' => $attData["total_working_hour"],
                    'meta' => json_encode($attData),
                ];
            }
        }
        if (count($data)) {
            AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
                ->when($this->employee_id, function ($query) {
                    $query->where('employees.id', $this->employee_id);
                })
                ->when($this->company_id, function ($query) {
                    $query->where('employees.company_id', $this->company_id);
                })
                ->when($this->branch_id, function ($query) {
                    $query->where('org.branch_id', $this->branch_id);
                })
                ->when($this->department_id, function ($query) {
                    $query->where('org.department_id', $this->department_id);
                })
                ->where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month], ['is_locked', false], ['is_uploaded', false]])
                ->delete();
            $result = AttendanceCount::insert($data);
            if ($result) $this->notify("Generated Attendance of " . count($data) . " employee(s).")->type('success')->send();
            else $this->notify("Fail to generate attendance.")->type('error')->send();
        }
    }

    public function exportAttendance($type = null)
    {
        $month = $this->nepaliMonthList[$this->nep_month];

        $fileName = "Attendance for ($this->nep_year $month).xlsx";

        ini_set('memory_limit', '512M');
        set_time_limit(300);
        $attendance = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where([
                ['nep_year', $this->nep_year],
                ['nep_month', $this->nep_month],
            ])
            ->select('attendance_counts.*')
            ->first();
        if ($attendance && !$attendance->meta) {
            return Excel::download(
                export: new AttendanceForSalaryExport($this->attendanceList, $this->monthDate, $this->nep_year, $this->nep_month),
                fileName: $fileName
            );
        }

        $query = AttendanceCount::where([
            ['nep_year', $this->nep_year],
            ['nep_month', $this->nep_month],
        ])
            ->leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->leftJoin('companies as company', 'company.id', '=', 'employees.company_id')
            ->leftJoin('branches as branch', 'branch.id', '=', 'org.branch_id')
            ->leftJoin('departments as department', 'department.id', '=', 'org.department_id')
            ->leftJoin('outsource_companies as vendor', 'vendor.id', '=', 'org.outsource_company_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->selectRaw(DB::raw("TRIM(CONCAT_WS(' ', first_name, NULLIF(middle_name, ''), last_name)) as employee"))
            ->selectRaw(DB::raw("CONCAT(company.code, '-', org.employee_code) as code"))
            ->selectRaw(DB::raw("company.name as company"))
            ->selectRaw(DB::raw("vendor.name as vendor"))
            ->selectRaw(DB::raw("branch.name as branch"))
            ->selectRaw(DB::raw("department.name as department"))
            ->selectRaw(DB::raw("attendance_counts.*"))
            ->orderBy(DB::raw("CONCAT_WS(' ', $this->sortBy)"), $this->sortDirection)
            ->get()
            ->map(function ($item) {
                $meta = json_decode($item->meta, true);
                $meta['daily_attendance'] = (array)$meta['daily_attendance'];
                $item->meta = (object)$meta;
                return $item;
            });

        return Excel::download(
            export: new AttendanceForSalaryExport($query, $this->monthDate, $this->nep_year, $this->nep_month),
            fileName: $fileName
        );
    }

    public function updated($property)
    {
        if ($property === "nep_year" || $property === "nep_month") {
            $this->reset(['showAttendanceList']);
            unset($this->attendanceList);
            unset($this->monthDate);
        }
    }

    public function getEmployeeDailyAttendanceForMonth($employeeIds)
    {
        $result = Attendance::whereIn('employee_id', $employeeIds)
            ->where('date_np', 'like', "$this->nep_year-$this->nep_month-%")
            ->select('employee_id', 'in_time', 'out_time', 'status', 'date_en')
            ->get()
            ->groupBy('employee_id');

        $attendanceMonthlyTemplate = [];
        foreach ($this->monthDate as $key => $monthDate) {
            $attendanceMonthlyTemplate[$key] = [
                'cell_color'    => "",
                'in_time'       => "",
                'out_time'      => "",
                'status'        => "",
                'status_short'  => ""
            ];
        }

        $employeeAttendanceData = [];
        foreach ($result as $emp_id => $attendance) {
            $formattedResults = $attendanceMonthlyTemplate;

            foreach ($attendance as $att) {
                $formattedDate = $att['date_en']; //LaravelNepaliDate::from($att['date_en'])->toNepaliDate(format: 'D/d');
                if ($formattedResults[$formattedDate] ?? false) {
                    $tooltipInfo = $this->getTooltipInfo($att['status']);
                    $status_short = $tooltipInfo['stat'];
                    if (isset($att['status']) && $att['status'] != "N/A") {
                        $status_short = $this->getInitials($att['status']);
                    }
                    $formattedResults[$formattedDate] = [
                        'cell_color'    => $tooltipInfo['cell_color'],
                        'in_time'       => $att['in_time'] ?? "N/A",
                        'out_time'      => $att['out_time'] ?? "N/A",
                        'status'        => $att['status'] ?? "N/A",
                        'status_short'  => $status_short,
                    ];
                }
            }
            $employeeAttendanceData[$emp_id] = $formattedResults;
        }
        return $employeeAttendanceData;
    }

    private function getInitials($text)
    {
        $words = preg_split('/[^a-zA-Z0-9]+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        $initials = '';
        foreach ($words as $word) {
            $initials .= strtoupper($word[0]);
        }
        return $initials;
    }

    public function getTooltipInfo($status)
    {
        $stat = 'N/A';
        $cell_color = "";
        switch ($status) {
            case (strpos($status, 'Present') !== false):
                $stat = 'P';
                $cell_color = "bg-cell-present";
                break;
            case 'Absent':
                $stat = 'A';
                $cell_color = "bg-cell-absent";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $stat = 'MP';
                $cell_color = "bg-cell-missed-punch";
                break;
            case 'Day Off':
                $stat = 'DO';
                $cell_color = "bg-cell-day-off";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $stat = 'WODO';
                $cell_color = "bg-cell-work-on-day-off";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $stat = 'WOH';
                $cell_color = "bg-cell-work-on-holiday";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $stat = 'OH';
                $cell_color = "bg-cell-on-holiday";
                break;
        }

        if (strpos($status, 'Late In')) {
            $stat .= '[LI]';
        }
        if (strpos($status, 'Early Out')) {
            $stat .= '[EO]';
        }

        return ['stat' => $stat, 'cell_color' => $cell_color];
    }

    public function setDateAndYear()
    {
        $fiscalYear = $this->fiscal_year ? FiscalYear::where('id', $this->fiscal_year)->first() : FiscalYear::where('is_active', true)->first();

        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);

        $this->nep_year = $start_date_year;

        // Initialize year according to selected month
        if ($this->nep_month < $start_date_month) {
            $this->nep_year = $end_date_year;
        }
    }

    #[Computed()]
    public function isAttendanceLocked()
    {
        $result = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month], ['is_locked', true]])
            ->first();
        if ($result) return true;
        return false;
    }

    #[Computed()]
    public function canPreviewAttendance()
    {
        $result = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where([['nep_year', $this->nep_year], ['nep_month', $this->nep_month]])
            ->first();
        if ($result) return true;
        return false;
    }

    #[Computed()]
    public function attendanceList()
    {
        if (!$this->showAttendanceList) return [];

        $attendance = AttendanceCount::leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where([
                ['nep_year', $this->nep_year],
                ['nep_month', $this->nep_month],
            ])
            ->select('attendance_counts.*')
            ->first();
        if ($attendance && !$attendance->meta) {
            $query = $this->calculateAttendance()->paginate($this->perPage);
            $attendanceData = $this->getEmployeeDailyAttendanceForMonth($query->pluck('employee_id'));

            $query->getCollection()->transform(function ($item) use ($attendanceData) {
                if (array_key_exists($item->employee_id, $attendanceData)) {
                    $item->daily_attendance = $attendanceData[$item->employee_id];
                } else {
                    $item->daily_attendance = [];
                }
                return $item;
            });

            return $query;
        }
        $this->dispatch("reloadTooltip");
        return AttendanceCount::select('attendance_counts.*')
            ->where([
                ['nep_year', $this->nep_year],
                ['nep_month', $this->nep_month],
            ])
            ->leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->where(function ($query) {
                $query->where('org.employee_code', 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
            })
            ->orderBy(DB::raw("CONCAT_WS(' ', $this->sortBy)"), $this->sortDirection)
            ->paginate($this->perPage)
            ->through(function ($item) {
                $meta = json_decode($item->meta, true); // Decode JSON into an array
                $meta['daily_attendance'] = (array)($meta['daily_attendance'] ?? []); // Ensure daily_attendance is an array
                $item->meta = (object)$meta; // Convert back to an object
                return $item;
            });
    }

    #[Computed(persist: true)]
    public function attendanceBypassEmployeeIds()
    {
        return PayrollBypassEmployee::with(['bypassType'])->whereHas('bypassType', function ($query) {
            $query->where('name', BypassType::ATTENDANCE);
        })
            ->leftJoin('employees', 'employees.id', '=', 'payroll_bypass_employees.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'payroll_bypass_employees.employee_id')
            ->when($this->employee_id, function ($query) {
                $query->where('employees.id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->pluck('payroll_bypass_employees.employee_id')->toArray();
    }

    #[Computed(persist: true)]
    public function monthDate()
    {
        $this->setDateAndYear();

        $daysInMonth = LaravelNepaliDate::daysInMonth("" . $this->nep_month, "" . $this->nep_year);
        $monthDate = [];
        for ($i = 1; $i <= $daysInMonth; $i++) {
            $day = $i < 10 ? "0" . $i : $i;
            $date = "{$this->nep_year}-{$this->nep_month}-{$day}";
            $engDate = LaravelNepaliDate::from($date)->toEnglishDate(format: 'Y-m-d');
            $monthDate[$engDate] = LaravelNepaliDate::from($engDate)->toNepaliDate(format: 'D/d');
        }
        return $monthDate;
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::whereNull('deleted_at')->get();
    }

    public function render()
    {
        return view('livewire.payroll.salary-structure.includes.attendance-lock');
    }
}
