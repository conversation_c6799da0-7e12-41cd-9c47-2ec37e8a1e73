<?php

namespace App\Livewire\Payroll\SalaryStructure;

use App\Exports\SalarySheetExcelSample;
use App\Http\Repositories\PayslipApprovalRepository;
use App\Http\Repositories\SalaryRepository;
use App\Models\Payroll\Perk;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Salary Sheet')]
class UploadSalarySheet extends Component
{
    use WithFileUploads, WithNotify;

    public $salarySheetExcelHeaders = [];

    public $employeeInfo = [
        "employee_code" => 'CB-000',
        "name" => 'Test Employee',
        "year" => '2081',
        "month" => '04',
    ];

    public $salarySheetIncomes = [
        "earned_basic_salary" => '0',
        "earned_ssf" => '0',
        "earned_allowance" => '0',
    ];

    public $salarySheetRemainingHeaders = [
        "annual_taxable_income" => '0',
        "past_cit_deduction" => '0',
        "past_ssf_deduction" => '0',
        "past_ssf_deduction_for_tax" => '0',
        "annual_taxable_amount" => '0',
        "paid_tds" => '0',
    ];

    public $allowanceHeaders = [];

    // [['data] => [], 'error' => ''];
    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file', as: 'salary sheet excel file')]
    public $payslipExcelFile = "";
    public $payslipExcelValidationMessages = "";

    public function mount()
    {
        if (fedexHrm()) {
            $this->salarySheetRemainingHeaders['non_cash_deduction'] = "0";
        }
        $this->salarySheetExcelHeaders = [...$this->employeeInfo, ...$this->salarySheetIncomes, ...$this->salarySheetRemainingHeaders];

        $perks = Perk::all();
        foreach ($perks as $perk) {
            $this->allowanceHeaders[$perk->name] = $perk->default_value;
        }
    }

    public function updatedPayslipExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'payslipExcelValidationMessages', 'excelUploaded');
        $this->excelData = [];
    }

    public function validateSalarySheetData()
    {
        $this->excelData = [];
        if (!($data = $this->validateFile())) return;
        $repo = new SalaryRepository;
        try {
            foreach ($data as $item) {
                $payslipErrorMessage = "";
                $response = $repo->validateSalarySheetForExcel($item);
                if (!$response['status']) {
                    $this->excelDataValidated = false;
                    $payslipErrorMessage = $response['message'];
                }
                array_push($this->excelData, ['data' => $item, 'error' => $payslipErrorMessage]);
            }
        } catch (\Exception $e) {
            logError("Error creating salary sheet from excel", $e);
            return $this->notify("Error validating salary sheet: " . $e->getMessage())->type("error")->send();
        }
    }

    public function uploadSalarySheetData()
    {
        if (!$this->excelDataValidated) return $this->notify("Excel validation failed");
        $data = [];
        foreach ($this->excelData as $item) {
            if ($item['error']) return $this->notify("Excel validation failed: " . $item['error']);
            array_push($data, $item['data']);
        }
        if (!($data = $this->validateFile())) return;
        $this->payslipExcelValidationMessages = "";
        $repo = new SalaryRepository;
        DB::beginTransaction();
        try {
            foreach ($data as $item) {
                $this->payslipExcelValidationMessages .= "Creating salary sheet of {$item['employee_code']} , {$item['name']}.<br />";
                $response = $repo->createSalarySheetFromExcel($item);
                if (!$response['status']) {
                    $this->payslipExcelValidationMessages .= "<span class='text-danger fw-bold'>Error: {$response['message']}</span><br />";
                    $this->payslipExcelValidationMessages .= "<span class='text-danger fw-bold'>All created salary data are reverted</span><br />";
                    return $this->errorResponse("Some error occurred. All the salary sheet uploaded are reverted");
                }
                $this->payslipExcelValidationMessages .= "{$response['message']}<br />";
                $this->payslipExcelValidationMessages .= "Salary Sheet of {$item['employee_code']} created.<br />";
                $ticket = $response['data'];
            }
            DB::commit();
            $this->excelUploaded = true;
            return $this->notify("Salary Sheet Uploaded Successfully")->send();
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while creating salary sheet", $e);
            return $this->notify("Error creating salary sheet: " . $e->getMessage())->type("error")->send();
        }
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray([], $this->payslipExcelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach (array_keys($this->salarySheetExcelHeaders) as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    function errorResponse(string $message = "", $data = null)
    {
        return [
            "status" => false,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    public function downloadSample()
    {
        return Excel::download(new SalarySheetExcelSample([
            ...$this->employeeInfo,
            ...$this->salarySheetIncomes,
            ...$this->allowanceHeaders,
            ...$this->salarySheetRemainingHeaders,
        ]), 'SalarySheetExcelSample.xlsx');
    }

    public function render()
    {
        return view('livewire.payroll.salary-structure.upload-salary-sheet');
    }
}
