<?php

namespace App\Livewire\Payroll\SalaryStructure;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\FiscalYear;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Salary Structure')]
class SalaryStructurePage extends Component
{
    use WithDataTable, WithNotify;

    public $fiscal_year, $nep_year, $nep_month;

    public function mount()
    {
        $this->fiscal_year = FiscalYear::where('is_active', true)->first()?->id;
        $this->nep_month = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm');
    }

    public function rules()
    {
        return [
            'fiscal_year' => 'required',
            'nep_month' => 'required'
        ];
    }

    public function validationAttributes()
    {
        return [
            'nep_month' => 'nepali month'
        ];
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::whereNull('deleted_at')->get();
    }

    public function render()
    {
        return view('livewire.payroll.salary-structure.salary-structure-page');
    }
}
