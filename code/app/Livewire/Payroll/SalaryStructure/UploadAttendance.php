<?php

namespace App\Livewire\Payroll\SalaryStructure;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\AttendanceLogHourExcelSample;
use App\Models\Employee\Employee;
use App\Models\Payroll\AttendanceCount;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Attendance')]
class UploadAttendance extends Component
{
    use WithFileUploads, WithNotify;

    public $excelHeaders = [
        "employee name" => 'Employee Name',
        "employee code" => 'Employee Code',
        "salary days" => 'Salary Days',
        "log hour" => 'Log Hour',
    ];

    public $excelValues = [
        "employee name" => 'Vianet',
        "employee code" => 'VIA-100',
        "salary days" => '31',
        "log hour" => '100',
    ];

    // [['data] => [], 'error' => ''];
    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $attendanceExcelFile = "";
    public $attendanceExcelValidationMessages = "";

    public $nep_year, $nep_month;

    public function mount()
    {
        $nep_date_arr = explode('-', LaravelNepaliDate::from(date('Y-m-d'))->toNepaliDate('Y-m-d'));
        $prev_month = $nep_date_arr[1] - 1;
        $this->nep_year =  $nep_date_arr[0];
        $this->nep_month =  $prev_month;
        if ($prev_month < 1) {
            $this->nep_year = $this->nep_year - 1;
            $this->nep_month = 12;
        }
    }

    public function updatedAttendanceExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'attendanceExcelValidationMessages', 'excelUploaded');
        $this->excelData = [];
    }

    public function validateAttendanceData()
    {
        $this->excelData = [];
        if (!($data = $this->validateFile())) return;
        try {
            foreach ($data as $item) {
                $attendanceErrorMessage = "";
                $response = $this->validations($item);
                if (isset($response['status']) ? !$response['status'] : false) {
                    $this->excelDataValidated = false;
                    $attendanceErrorMessage = $response['message'];
                }
                array_push($this->excelData, ['data' => $item, 'error' => $attendanceErrorMessage]);
            }
        } catch (\Exception $e) {
            logError("Error updating log hour for attendance from excel", $e);
            return $this->notify("Error validating attendance log: " . $e->getMessage())->type("error")->send();
        }
    }

    public function validations($data)
    {
        $employeeCode = $data['employee code'];
        $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$employeeCode'")
            ->select(
                'employees.id as id',
                'employees.company_id',
            )
            ->withTrashed()
            ->first();
        if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($employeeCode ? $employeeCode : '(NULL)'));

        if (!(is_float($data['salary days']) || is_numeric($data['salary days']))) {
            return $this->errorResponse("Only numbers are allowed in salary days.");
        }

        if (!(is_float($data['log hour']) || is_numeric($data['log hour']))) {
            return $this->errorResponse("Only numbers are allowed in log hour.");
        }
    }

    function errorResponse(string $message = "", $data = null)
    {
        return [
            "status" => false,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    public function uploadAttendanceData()
    {
        if (!$this->excelDataValidated) return $this->notify("Excel validation failed");
        $data = [];
        foreach ($this->excelData as $item) {
            if ($item['error']) return $this->notify("Excel validation failed: " . $item['error']);
            array_push($data, $item['data']);
        }
        if (!($data = $this->validateFile())) return;
        $this->attendanceExcelValidationMessages = "";
        DB::beginTransaction();
        try {
            foreach ($data as $item) {
                $attCount = AttendanceCount::leftJoin('employee_org as org', 'org.employee_id', '=', 'attendance_counts.employee_id')
                    ->leftJoin('employees', 'employees.id', '=', 'attendance_counts.employee_id')
                    ->leftJoin('companies as comp', 'comp.id', '=', 'employees.company_id')
                    ->where([
                        ['attendance_counts.nep_year', $this->nep_year],
                        ['attendance_counts.nep_month', $this->nep_month],
                        ['attendance_counts.is_locked', false]
                    ])
                    ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = ?", $item['employee code']);

                if ($attCount->first()) {
                    $result = $attCount->update([
                        'log_hour' => $item['log hour'],
                        'salary_days' => $item['salary days'],
                        'is_uploaded' => true
                    ]);
                } else {
                    $this->notify("Unable to upload attendance data.")->type("error")->send();
                }
                $this->attendanceExcelValidationMessages .= "Updated attendance of {$item['employee code']} , {$item['employee name']}.<br />";
            }
            DB::commit();
            $this->excelUploaded = true;
            return $this->notify("Attendance Log Hour Uploaded Successfully")->send();
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while creating attendance log hour ", $e);
            return $this->notify("Error creating attendance log hour: " . $e->getMessage())->type("error")->send();
        }
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray([], $this->attendanceExcelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach (array_keys($this->excelHeaders) as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function downloadSample()
    {
        return Excel::download(new AttendanceLogHourExcelSample(
            $this->excelHeaders,
            $this->excelValues
        ), 'AttendanceLogHourExcelSample.xlsx');
    }

    public function render()
    {
        return view('livewire.payroll.salary-structure.upload-attendance');
    }
}
