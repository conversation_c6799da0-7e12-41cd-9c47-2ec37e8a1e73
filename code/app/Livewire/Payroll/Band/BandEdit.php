<?php

namespace App\Livewire\Payroll\Band;

use App\Traits\WithNotify;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Edit Band')]
class BandEdit extends Component
{
    use WithNotify;

    public $band_id, $name, $basic_start, $gross_start, $basic_diff, $gross_diff, $status, $pay_structure_key = 1;

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\Payroll\EmployeeBand;
    }

    public function mount($band_id)
    {
        $this->band_id = $band_id;
        $this->fillFormModel($band_id);
    }

    public  function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:50'],
            'basic_start' => ['required', 'numeric', 'min:0'],
            'gross_start' => ['required', 'numeric', 'min:0'],
            'basic_diff' => ['required', 'numeric', 'min:0'],
            'gross_diff' => ['required', 'numeric', 'min:0'],
            'status' => ['required', 'boolean'],
        ];
    }

    public function save(): void
    {
        $this->validate();

        $this->model->findOrFail($this->band_id)->fill($this->all())->save();

        $this->pay_structure_key += 1;

        $this->notify('Band edited successfully!!')->send();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    public function render()
    {
        return view('livewire.payroll.band.band-edit');
    }
}
