<?php

namespace App\Livewire\Payroll\AdvanceSalary;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\Payroll\AdvanceSalary;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Advance Salary')]
class AdvanceSalaryPage extends Component
{
    use WithDataTable, WithNotify;

    public $year, $installmentList;

    public function mount()
    {
        $this->year = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y');
    }

    public function updated($property)
    {
        $updateOn =  ['year', 'search'];
        if (in_array($property, $updateOn)) {
            unset($this->advanceSalaryData);
        }
    }

    public function getInstallments($employee_id, $takenOn)
    {
        $this->installmentList = AdvanceSalary::where([['employee_id', $employee_id], ['nep_taken_on', $takenOn]])->get();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['installmentList']);
    }

    #[Computed(persist: true)]
    public function advanceSalaryData()
    {
        $advanceSalaries = AdvanceSalary::with([
            'employee:id,first_name,middle_name,last_name,company_id',
            'employee.organizationInfo:id,employee_id,employee_code',
            'employee.company:id,code',
        ])
            ->when($this->year, function ($query) {
                return $query->where('nep_taken_on', 'like', $this->year . '%');
            })
            ->when($this->search, function ($query) {
                return $query->whereHas('employee', function ($q) {
                    $q->whereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", '%' . $this->search . '%')
                        ->orWhereRaw("CONCAT(first_name, ' ', middle_name, ' ', last_name) LIKE ?", '%' . $this->search . '%')
                        ->orWhereHas('organizationInfo', function ($q) {
                            $q->where('employee_code', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->get()
            ->groupBy(function ($item) {
                return $item->nep_taken_on . '_' . $item->employee_id; // Grouping by a combination of nep_taken_on and employee_id
            });

        $advanceSalariesData = [];
        foreach ($advanceSalaries as $advanceSalaryGrouped) {
            $installment = 0;
            $installments = count($advanceSalaryGrouped);
            $taken_on = "";
            $remaining = $installments;
            $employee_name = "";
            $employee_id = "";
            foreach ($advanceSalaryGrouped as $advanceSalary) {
                $employee = $advanceSalary->employee;
                $employee_id = $employee->id;
                $employee_name = trim("$employee->first_name $employee->middle_name $employee->last_name");
                $employee_code = $employee?->company?->code . '-' . $employee?->organizationInfo?->employee_code;
                $installment = $advanceSalary['amount'];
                $taken_on = $advanceSalary['nep_taken_on'];
                if ($advanceSalary['is_settled']) {
                    $remaining -= 1;
                }
            }
            $advanceSalariesData[] = [
                'employee_id' => $employee_id,
                'employee_name' => $employee_name,
                'employee_code' => $employee_code,
                'advance' => $installment * $installments,
                'taken_on' => $taken_on,
                'installment' => $installment,
                'paid' => $installment * ($installments - $remaining),
                'balance' => $installment * $remaining,
                'installments' => $installments,
                'remaining' => $remaining
            ];
        }

        return $advanceSalariesData;
    }

    #[Computed(persist: true)]
    public function yearList()
    {
        return AdvanceSalary::selectRaw('DISTINCT SUBSTRING(nep_taken_on, 1, 4) as taken_on')->pluck('taken_on', 'taken_on') ?? [];
    }

    #[Computed(persist: true)]
    public function monthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function render()
    {
        return view('livewire.payroll.advance-salary.advance-salary-page');
    }
}
