<?php

namespace App\Livewire\Payroll\AdvanceSalary;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\AdvanceSalaryType;
use App\Models\Payroll\AdvanceSalary;
use App\Models\Payroll\Payslip;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Add Advance Salary')]
class AddAdvanceSalary extends Component
{
    use WithDataTable, MultiselectEmployeeSearch, WithNotify;

    public $employee_id = "";

    public $advanceAmount, $takenOn, $paidOver = 1, $startFromMonth, $startFromYear;

    public $calculatedResult, $installmentList;

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_list'];
        $this->withTrashed = true;
        $this->initialize();
    }

    public function rules()
    {
        return [
            'advanceAmount' => ['required', 'numeric'],
            'takenOn' => ['required'],
            'paidOver' => ['required', 'numeric', 'min:1'],
            'startFromMonth' => ['required'],
            'startFromYear' => ['required'],
        ];
    }

    public function calculateAdvance()
    {
        $this->reset(['calculatedResult']);
        $this->validate();
        $monthlyPayment = round($this->advanceAmount / $this->paidOver, 2);
        $lastMonth = $this->startFromMonth + $this->paidOver - 1; // 1 is deducted as payment will be done from the selected month
        $lastYear = $this->startFromYear;
        if ($lastMonth > 12) {
            $lastYear += (int)($lastMonth / 12);
            $lastMonth = $lastMonth % 12;
        }

        $lastMonth = $lastMonth < 10 ? "0" . $lastMonth : $lastMonth;
        $this->calculatedResult = [
            'monthly_payment' => $monthlyPayment,
            'last_payment' => $this->monthList[$lastMonth] . '-' . $lastYear
        ];
    }

    public function saveAdvance()
    {
        $this->validate();
        $payslip = Payslip::where([['employee_id', $this->employee_id], ['status', 'Active']])->first();
        if (!$payslip) {
            $this->notify('No active payslip found for the selected employee')->type('error')->send();
            return;
        }
        $monthlyPayment = round($this->advanceAmount / $this->paidOver, 2);
        $dataToSave = [];

        $year = $this->startFromYear;
        $month = $this->startFromMonth;
        for ($i = 0; $i < $this->paidOver; $i++) {
            $dataToSave[] = [
                'employee_id'   => $this->employee_id,
                'payslip_id'    => $payslip->id,
                'nep_year'      => $year,
                'nep_month'     => $month,
                'nep_taken_on'  => $this->takenOn,
                'amount'        => $monthlyPayment,
                'is_settled'    => 0,
                'type'          => AdvanceSalaryType::EMI,
                'remarks'       => "",
            ];
            $month += 1;
            if ($month > 12) {
                $month = 1;
                $year += 1;
            }
            $month = $month < 10 ? "0" . $month : $month;
        }

        try {
            logInfo('Begin to save advance salary');
            DB::beginTransaction();
            AdvanceSalary::insert($dataToSave);
            DB::commit();
            $this->notify("Advance salary saved successfully.")->send();
            logInfo('Advance salary saved successfully.');
        } catch (Exception $e) {
            DB::rollBack();
            $this->notify("Unable to save advance salary.")->type('error')->send();
            logError('Unable to save advance salary. ', $e);
        }
        $this->dispatch('hide-model');
        unset($this->advanceSalaryData);
    }

    public function getInstallments($takenOn)
    {
        $this->installmentList = AdvanceSalary::where([['employee_id', $this->employee_id], ['nep_taken_on', $takenOn]])->get();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['advanceAmount', 'takenOn', 'paidOver', 'startFromMonth', 'startFromYear', 'calculatedResult', 'installmentList']);
        $this->resetErrorBag();
        $this->initialize();
    }

    public function initialize()
    {
        $nepaliDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y-m');
        $this->startFromYear = explode("-", $nepaliDate)[0];
        $this->startFromMonth = explode("-", $nepaliDate)[1];
    }

    #[Computed()]
    public function employee_name()
    {
        return collect($this->employeeList)->firstWhere('value', $this->employee_id)['label'] ?? null;
    }

    #[Computed()]
    public function advanceSalaryData()
    {
        $advanceSalaries = AdvanceSalary::where('employee_id', $this->employee_id)->get()->groupBy('nep_taken_on');
        $advanceSalariesData = [];
        foreach ($advanceSalaries as $advanceSalaryGrouped) {
            $installment = 0;
            $installments = count($advanceSalaryGrouped);
            $taken_on = "";
            $remaining = $installments;
            foreach ($advanceSalaryGrouped as $advanceSalary) {
                $installment = $advanceSalary['amount'];
                $taken_on = $advanceSalary['nep_taken_on'];
                if ($advanceSalary['is_settled']) {
                    $remaining -= 1;
                }
            }
            $advanceSalariesData[] = [
                'advance' => $installment * $installments,
                'taken_on' => $taken_on,
                'installment' => $installment,
                'paid' => $installment * ($installments - $remaining),
                'balance' => $installment * $remaining,
                'installments' => $installments,
                'remaining' => $remaining
            ];
        }

        return $advanceSalariesData;
    }

    #[Computed(persist: true)]
    public function yearList()
    {
        $currentYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y') - 1;
        $yearList = [];
        for ($i = $currentYear; $i <= $currentYear + 2; $i++) {
            $yearList[$i] = $i;
        }
        return $yearList;
    }

    #[Computed(persist: true)]
    public function monthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function render()
    {
        return view('livewire.payroll.advance-salary.add-advance-salary');
    }
}
