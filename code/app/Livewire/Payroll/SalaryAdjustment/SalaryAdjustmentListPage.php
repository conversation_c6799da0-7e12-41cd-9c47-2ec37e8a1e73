<?php

namespace App\Livewire\Payroll\SalaryAdjustment;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\Payroll\Payment;
use App\Models\Payroll\Perk;
use App\Models\Payroll\SalaryAdjustment;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Salary Adjustment List')]
class SalaryAdjustmentListPage extends Component
{
    use WithDataTable;

    public $year, $month, $adjustmentHeading;

    public function mount()
    {
        $this->tableListVariable = 'salaryAdjustmentList';
        $this->perPage = 10;
        $this->year = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y');
        $this->month = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('m');
    }

    public function deleteAdjustment($id)
    {
        if (!$this->canPerformAction()) {
            $this->notify('You are not allowed to delete the adjustment when salary is locked.')->type("error")->duration(5)->send();
            return;
        }
        SalaryAdjustment::findOrFail($id)->delete();
        unset($this->salaryAdjustmentList);

        $this->notify('Salary adjustment deleted successfully!!')->send();
    }

    public function updated($props)
    {
        if (in_array($props, ['year', 'month', 'adjustmentHeading', 'search', 'perPage'])) {
            unset($this->salaryAdjustmentList);
        }
    }

    #[Computed]
    public function canPerformAction()
    {
        return Payment::where([["year", $this->year], ["month", $this->month], ["is_locked", 1]])->exists() ? false : true;
    }

    #[Computed(persist: true)]
    public function salaryAdjustmentList()
    {
        $query = SalaryAdjustment::with(['employee:id,first_name,middle_name,last_name,company_id', 'employee.company:id,code', 'employee.organizationInfo:id,employee_id,employee_code'])
            ->where('year', $this->year)
            ->where('month', $this->month)
            ->when($this->adjustmentHeading, function ($query) {
                return $query->where('adjustment_heading', $this->adjustmentHeading);
            })
            ->whereHas('employee', function ($query) {
                $query->where(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', first_name, last_name)"), 'LIKE', '%' . $this->search . '%');
            });

        $results = $query->paginate($this->perPage ?: 10);
        $results->getCollection()->transform(function ($salaryAdjustment) {

            $employee = $salaryAdjustment->employee;

            $salaryAdjustment->employee_name = trim("{$employee->first_name} {$employee->middle_name} {$employee->last_name}");
            $salaryAdjustment->employee_code = "{$employee->company->code}-{$employee->organizationInfo->employee_code}";

            return $salaryAdjustment;
        });
        return $results;
    }

    #[Computed(persist: true)]
    public function adjustmentHeadings()
    {
        $toReturn = Constant::ADJUSTMENT_PAYSLIP_HEADING;

        $perks = Perk::all();
        $additionalAllowance = [];
        foreach ($perks as $perk) {
            $additionalAllowance[] = [
                'id' => $perk->id,
                'name' => $perk->name,
                'value' => $perk->name,
            ];
        }

        $toReturn = [...$toReturn, ...$additionalAllowance];

        return $toReturn;
    }

    #[Computed(persist: true)]
    public function monthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function yearList()
    {
        $selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        $yearList[$selectedYear - 1] = $selectedYear - 1;
        for ($i = 0; $i < 2; $i++) {
            $yearList[$selectedYear + $i] = $selectedYear + $i;
        }
        return $yearList;
    }

    public function render()
    {
        return view('livewire.payroll.salary-adjustment.salary-adjustment-list-page');
    }
}
