<?php

namespace App\Livewire\Payroll\SalaryAdjustment;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\SalaryAdjustmentExcelSample;
use App\Http\Helpers\Constant;
use App\Models\Employee\Employee;
use App\Models\Payroll\Payment;
use App\Models\Payroll\Perk;
use App\Models\Payroll\SalaryAdjustment;
use App\Models\User;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Add Salary Adjustment')]
class SalaryAdjustmentPage extends Component
{
    use MultiselectEmployeeSearch, WithFileUploads, WithNotify;

    public $selectedAdjustment, $selectedYear, $selectedMonth, $employees = [], $adjustmentData = [], $payslipHeadings, $salaryAdjustmentExcelHeading, $showUploadSection = false;

    public $excelData = [];
    public $excelDataValidated = true;

    public $salaryAdjustmentExcelFile = "";

    public $company_id, $region_id, $branch_id, $department_id;

    public function mount()
    {
        $this->payslipHeadings = Constant::ADJUSTMENT_PAYSLIP_HEADING;
        $this->multiSelectAttributes = ['employees'];
        $this->withTrashed = true;
        $this->salaryAdjustmentExcelHeading = [
            'Employee Name',
            'Employee Code',
            'Year',
            'Month',
            'Adjustment Heading',
            'Current Amount',
            'Adjustment Amount',
        ];

        if (!scopeAll()) {
            $this->company_id = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->region_id = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->branch_id = currentEmployee()?->organizationInfo?->branch_id;

            if (scopeBranch())
                return;
            $this->department_id = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    public function updated($property)
    {
        if (in_array($property, ['selectedYear', 'selectedMonth', 'selectedAdjustment'])) {
            if (count($this->adjustmentData)) $this->reset('adjustmentData');
            $this->showUploadSection = false;
            $this->reset(['excelData', 'excelDataValidated', 'salaryAdjustmentExcelFile']);
            $this->resetValidation(['excelData', 'excelDataValidated', 'salaryAdjustmentExcelFile']);
        }
        if (strpos($property, "adjustmentData") !== false) {
            $adjustmentArray = explode(".", $property);
            $index = $adjustmentArray[1];

            $this->adjustmentData[$index] = $this->adjustValue($index);
        }
        if ($property == "salaryAdjustmentExcelFile" && !$this->salaryAdjustmentExcelFile) {
            $this->reset(['excelData', 'excelDataValidated', 'salaryAdjustmentExcelFile']);
            $this->resetValidation(['excelData', 'excelDataValidated', 'salaryAdjustmentExcelFile']);
        }
    }

    public function adjustValue($index)
    {
        $data = $this->adjustmentData[$index];
        $data['adjusted_amount'] = (float)($data['actual_amount'] ?? 0) + (float)($data['adjustment_amount'] ?? 0);
        return $data;
    }

    public function performAdjustment()
    {
        $salaryAdjustment = new SalaryAdjustment();
        $dataToStore = [];
        $existingConditions = [];

        foreach ($this->adjustmentData as $data) {
            $existingConditions[] = [
                'year' => $data['year'],
                'month' => $data['month'],
                'employee_id' => $data['employee_id'],
                'adjustment_heading' => $data['adjustment_heading'],
                'adjustment_id' => $data['adjustment_id'],
            ];

            $dataToStore[] = [
                'year' => $data['year'],
                'month' => $data['month'],
                'employee_id' => $data['employee_id'],
                'adjustment_heading' => $data['adjustment_heading'],
                'adjustment_id' => $data['adjustment_id'],
                'actual_amount' => $data['actual_amount'],
                'adjustment_amount' => $data['adjustment_amount'],
                'adjusted_amount' => $data['adjusted_amount'],
            ];
        }

        if (!empty($existingConditions)) {
            foreach ($existingConditions as $condition) {
                $salaryAdjustment->where([
                    'year' => $condition['year'],
                    'month' => $condition['month'],
                    'employee_id' => $condition['employee_id'],
                    'adjustment_heading' => $condition['adjustment_heading'],
                    'adjustment_id' => $condition['adjustment_id'],
                ])->delete();
            }
        }

        $salaryAdjustment->insert($dataToStore);

        $causedBy = User::find(session('user_id_main') ?? session('user_id'));
        $isImpersonating = session('user_id_main');
        foreach ($dataToStore as $data) {
            activity()
                ->createdAt(now())
                ->performedOn(new SalaryAdjustment())
                ->causedBy($causedBy)
                ->inLog($isImpersonating ? "impersonated.salary_adjustment" : "salary_adjustment")
                ->event('created')
                ->withProperties(["attributes" => $data])
                ->log(
                    ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                        (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
                );
        }

        $this->notify("Adjustment added successfully. Now regenerate the salary to get the adjustment reflected.")->duration(6)->send();
        $this->reset([
            'selectedAdjustment',
            'selectedYear',
            'selectedMonth',
            'employees',
            'adjustmentData',
        ]);
        $this->dispatch('toggle-employee-id');
    }

    public function removeItem($index)
    {
        unset($this->adjustmentData[$index]);
    }

    public function checkAdjustmentType($adjustment)
    {
        $adjustmentData = json_decode($adjustment) ?? [];
        $valueLookup = [];
        foreach ($this->payslipHeadings as $heading) {
            $valueLookup[$heading['value']] = true;
        }

        return [
            'type' => isset($valueLookup[$adjustmentData->value]) ? 'payslip' : 'perks',
            'adjustmentData' => $adjustmentData,
        ];
    }

    public function fetchDate()
    {
        $this->showUploadSection = false;
        $this->resetErrorBag(['employees', 'selectedYear', 'selectedMonth', 'selectedAdjustment']);
        $this->validateAdjustmentForm();

        $preparedAdjustmentData = $this->prepareAdjustmentData();

        if ($preparedAdjustmentData)
            $this->adjustmentData = [...$this->adjustmentData, ...$preparedAdjustmentData];
    }

    public function prepareAdjustmentData()
    {
        if (!$this->canPerformAdjustment($this->selectedYear, $this->selectedMonth)) return false;
        $toReturn = [];
        $adjustmentType = $this->checkAdjustmentType($this->selectedAdjustment);

        $salaryAdjustments = SalaryAdjustment::with(['employee'])
            ->where([['year', $this->selectedYear], ['month', $this->selectedMonth], ['adjustment_heading', $adjustmentType['adjustmentData']->value]])
            ->whereIn('employee_id', array_values($this->employees))
            ->get();

        foreach ($salaryAdjustments as $row) {
            // Filter existing data to check if employee_id already exists
            $exists = !empty(array_filter($this->adjustmentData, function ($existingRow) use ($row) {
                return $existingRow['employee_id'] === $row->employee_id;
            }));

            // Add new data only if employee_id does not exist
            if (!$exists) {
                $toReturn[] = [
                    'year' => $row->year,
                    'month' => $row->month,
                    'employee_id' => $row->employee_id,
                    'employee_code' => $row->employee->companyEmpCode,
                    'adjustment_heading' => $row->adjustment_heading,
                    'adjustment_id' => $row->adjustment_id,
                    'employee_name' => $row->employee->name,
                    'actual_amount' => $row->actual_amount,
                    'adjustment_amount' => $row->adjustment_amount,
                    'adjusted_amount' => $row->adjusted_amount,
                ];
            }
        }

        $payment = Payment::with(['payment_perks' => function ($query) use ($adjustmentType) {
            $query->select('payment_uuid', DB::raw('current_amount as perk_value'), 'perks_id');
            if ($adjustmentType['type'] == 'perks') {
                $query->where('perks_id', $adjustmentType['adjustmentData']->id);
            }
        }, 'payment_perks.perks'])
            ->where([['year', $this->selectedYear], ['month', $this->selectedMonth]])
            ->leftJoin('employees', 'payments.employee_id', '=', 'employees.id')
            ->leftJoin('employee_org as org', 'payments.employee_id', '=', 'org.employee_id')
            ->leftJoin('companies', 'companies.id', '=', 'employees.company_id')
            ->whereIn('payments.employee_id', array_values($this->employees))
            ->select(
                "payments.employee_id",
                'payments.id',
                'uuid',
                DB::raw("TRIM(CONCAT_WS(' ', employees.first_name, NULLIF(employees.middle_name, ''), employees.last_name)) as name"),
                DB::raw("TRIM(CONCAT(companies.code,'-',org.employee_code)) as emp_code")
            );

        if ($adjustmentType['type'] != 'perks') {
            $columnName = $adjustmentType['adjustmentData']->value;
            $payment->addSelect("$columnName as actual_amount");
        }

        $result = $payment->get()
            ->groupBy('employee_id')
            ->map(function ($group) {
                return $group->first();
            });

        if ($adjustmentType['type'] == 'perks') {
            foreach ($result as $employeePayment) {
                if (!count($employeePayment->payment_perks)) {
                    continue;
                }
                $amount = $employeePayment->payment_perks[0]?->perk_value ?? 0;
                $perks_id = $adjustmentType['adjustmentData']->id;

                $employeePayment->actual_amount = $amount;
                $employeePayment->perks_id = $perks_id;

                $result[$employeePayment->employee_id] = $employeePayment;
            }
        }

        foreach ($result as $row) {
            // Filter existing data in data to return
            $existInDataToReturn = !empty(array_filter($toReturn, function ($existingRow) use ($row) {
                return $existingRow['employee_id'] === $row->employee_id;
            }));

            // Filter existing data to check if employee_id already exists
            $exists = !empty(array_filter($this->adjustmentData, function ($existingRow) use ($row) {
                return $existingRow['employee_id'] === $row->employee_id;
            }));

            // Add new data only if employee_id does not exist
            if (!$exists && !$existInDataToReturn) {
                $toReturn[] = [
                    'year' => $this->selectedYear,
                    'month' => $this->selectedMonth,
                    'employee_id' => $row->employee_id,
                    'employee_code' => $row->emp_code,
                    'adjustment_heading' => $adjustmentType['adjustmentData']->value,
                    'adjustment_id' => $row->perks_id ?? null,
                    'employee_name' => $row->name,
                    'actual_amount' => $row->actual_amount,
                    'adjustment_amount' => 0,
                    'adjusted_amount' => $row->actual_amount,
                ];
            }
        }

        return $toReturn;
    }

    public function canPerformAdjustment($year, $month)
    {
        $paymentQuery = Payment::where([['year', $year], ['month', $month]])
            ->leftJoin('employees', 'employees.id', '=', 'payments.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'payments.employee_id')
            ->when($this->company_id, function ($query) {
                $query->where('employees.company_id', $this->company_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            });
        if (!count($paymentQuery->get())) {
            $this->notify('Generate salary sheet to perform adjustment.')->type('error')->send();
            return false;
        } else if (count($paymentQuery->where('is_locked', 1)->get())) {
            $this->notify('You are not allowed to perform adjustment in locked salary.')->type('error')->send();
            return false;
        }

        return true;
    }

    public function downloadSample()
    {
        $adjustmentData = $this->prepareAdjustmentData();
        if (!$adjustmentData) {
            return;
        }
        $values = array_map(function ($item) {
            return [
                $item['employee_name'],
                $item['employee_code'],
                $item['year'],
                $item['month'],
                $item['adjustment_heading'],
                $item['actual_amount'],
                $item['adjustment_amount'],
            ];
        }, $adjustmentData);

        $fileName = "Salary_Adjustment_{$this->selectedYear}_{$this->selectedMonth}_" . str_replace(' ', '_', json_decode($this->selectedAdjustment)->value ?? '') . ".xlsx";
        return Excel::download(new SalaryAdjustmentExcelSample('Salary Adjustment', $this->salaryAdjustmentExcelHeading, $values), $fileName);
    }

    public function validateAdjustmentForm()
    {
        $this->validate([
            'selectedAdjustment' => 'required',
            'selectedYear' => 'required',
            'selectedMonth' => 'required',
            'employees' => 'required',
        ]);
    }

    public function validateExcelFile()
    {
        $this->reset(['excelData']);
        $this->validate([
            'salaryAdjustmentExcelFile' => 'required|file',
        ]);

        $data = Excel::toArray([], $this->salaryAdjustmentExcelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach ($this->salaryAdjustmentExcelHeading as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please data in all the columns")->type("error")->send();
                return false;
            }
        }

        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }

        try {
            foreach ($data as $item) {
                $errorMessage = "";
                $response = $this->validateSalaryAdjustmentExcelData($item);
                if (!$response['status']) {
                    $this->excelDataValidated = false;
                    $errorMessage = $response['message'];
                }
                array_push($this->excelData, ['data' => $item, 'error' => $errorMessage]);
            }
        } catch (\Exception $e) {
            logError("Error creating salary adjustment from excel", $e);
            return $this->notify("Error validating salary adjustment: " . $e->getMessage())->type("error")->send();
        }
    }

    public function validateSalaryAdjustmentExcelData($data)
    {
        $empCode = $data['employee code'];

        $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$empCode'")
            ->select(
                'employees.id as id',
                'employees.company_id',
            )
            ->withTrashed()
            ->first();

        if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($empCode ? $empCode : '(NULL)'));

        $paymentQuery = Payment::where([['year', $data['year']], ['month', $data['month']]]);
        if (!count($paymentQuery->get())) {
            return $this->errorResponse("Generate salary sheet to perform adjustment.");
        } else if (count($paymentQuery->where('is_locked', 1)->get())) {
            return $this->errorResponse("You are not allowed to perform adjustment in locked salary.");
        }

        $headingArray = array_map(function ($item) {
            return $item['value'];
        }, $this->adjustmentHeadings);

        if (!in_array($data['adjustment heading'], $headingArray)) {
            return $this->errorResponse("Invalid adjustment heading " . $data['adjustment heading']);
        }

        if ($data['adjustment heading'] != json_decode($this->selectedAdjustment)?->value) {
            return $this->errorResponse("Select adjustment " . (json_decode($this->selectedAdjustment)?->value ?? '') . " doesn't matched with the uploaded adjustment heading " . $data['adjustment heading']);
        }

        $headers = [
            "current amount",
            "adjustment amount",
        ];

        foreach ($headers as $header) {
            if (!(is_float($data[$header]) || is_numeric($data[$header]))) {
                return $this->errorResponse("Only numbers are allowed in {$header}.");
            }
        }

        return $this->successResponse("");
    }

    public function uploadExcelSheet()
    {

        $this->excelDataValidated = true;
        $this->validateExcelFile();

        if (!$this->excelDataValidated) return;
        $data = $this->excelData;
        $employeeCodes = array_map(function ($item) {
            return $item['data']['employee code'] ?? null;
        }, $data);

        $employee_ids = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            // ->whereRaw("CONCAT(comp.code, '-', org.employee_code) IN (?)", [$employeeCodes])
            ->whereIn(DB::raw("CONCAT(comp.code, '-', org.employee_code)"), $employeeCodes)
            ->select(
                'employees.id as id',
                'employees.first_name as first_name',
                'employees.middle_name as middle_name',
                'employees.last_name as last_name',
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as employee_code"),
                DB::raw("TRIM(CONCAT_WS(' ', employees.first_name, NULLIF(employees.middle_name, ''), employees.last_name)) as name")
            )
            ->withTrashed()
            ->get()
            ->keyBy('employee_code')
            ->toArray();

        $selectedAdjustment = array_filter($this->adjustmentHeadings, function ($item) use ($data) {
            return $item['value'] == $data[0]['data']['adjustment heading'];
        });
        $parsedResult = reset($selectedAdjustment);
        $adjustmentType = $this->checkAdjustmentType(json_encode($parsedResult));

        foreach ($data as $adjustmentData) {
            $adjustment = $adjustmentData['data'];

            $this->adjustmentData[] = [
                'year' => $adjustment['year'],
                'month' => $adjustment['month'],
                'employee_id' => $employee_ids[$adjustment['employee code']]['id'],
                'employee_code' => $adjustment['employee code'],
                'adjustment_heading' => $adjustmentType['adjustmentData']->value,
                'adjustment_id' => $adjustmentType['type'] == 'perks' ? $adjustmentType['adjustmentData']->id ?? null : null,
                'employee_name' => $employee_ids[$adjustment['employee code']]['name'],
                'actual_amount' => $adjustment['current amount'],
                'adjustment_amount' => $adjustment['adjustment amount'],
                'adjusted_amount' => $adjustment['current amount'] + $adjustment['adjustment amount'],
            ];
        }
        $this->reset(['excelData', 'showUploadSection']);
    }

    public function validateToDisplayUploadSection()
    {
        $this->validateAdjustmentForm();
        if (!$this->canPerformAdjustment($this->selectedYear, $this->selectedMonth)) return;
        $this->showUploadSection = true;
    }

    protected function successResponse(string $message = "", $data = null)
    {
        return [
            "status" => true,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    public function errorResponse(string $message = "", $data = null)
    {
        return [
            "status" => false,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    #[Computed()]
    public function errorExcelData()
    {
        return array_filter($this->excelData, function ($item) {
            return !empty($item['error']);
        });
    }

    #[Computed(persist: true)]
    public function monthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function yearList()
    {
        $selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        $yearList[] = $selectedYear - 1;

        for ($i = 0; $i < 2; $i++) {
            $yearList[] = $selectedYear + $i;
        }
        return $yearList;
    }

    #[Computed()]
    public function adjustmentHeadings()
    {
        $toReturn = $this->payslipHeadings;

        $perks = Perk::all();
        $additionalAllowance = [];
        foreach ($perks as $perk) {
            $additionalAllowance[] = [
                'id' => $perk->id,
                'name' => $perk->name,
                'value' => $perk->name,
            ];
        }

        $toReturn = [...$toReturn, ...$additionalAllowance];

        return $toReturn;
    }

    public function render()
    {
        return view('livewire.payroll.salary-adjustment.salary-adjustment-page');
    }
}
