<?php

namespace App\Livewire\Payroll;

use App\Models\Payroll\Designation as mDesignation;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Desigantions')]
class Designation extends Component
{
    use WithDataTable, WithNotify;

    public $editingId;

    public $title, $description, $level = 1, $role = "employee", $departmentIds = [];

    public $designation_roles = [
        "employee" => "Employee",
        "supervisor" => "Supervisor",
        "hod" => "HOD",
        "branch_manager" => "Branch Manager"
    ];

    public function rules()
    {
        return [
            "title" => "required|max:191",
            "description" => "required",
            "level" => "required|integer",
            "role" => "required|in:branch_manager,supervisor,hod,employee",
            "departmentIds" => "required",
            "departmentIds.*" => "exists:departments,id",
        ];
    }

    public function validationAttributes()
    {
        return [
            'departmentIds' => 'departments ',
            'title' => 'designation',
        ];
    }
    public function messages()
    {
        return [
            'role.required' => 'The designation is required.',
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->applySorting(mDesignation::search($this->search))->paginate($this->perPage);
    }

    public function save()
    {
        $validated = $this->validate();
        if ($this->editingId) {
            authorizePermission(PermissionList::DESIGNATION_UPDATE);
            Log::info("Editing designation with id {$this->editingId}: ", $validated);
            $designation = mDesignation::findOrFail($this->editingId);
            $designation->fill($validated)->save();
            $designation->departments()->sync($this->departmentIds);
            Log::info("Designation updated.");
        } else {
            authorizePermission(PermissionList::DESIGNATION_CREATE);
            Log::info("Parameters for creating designation: ", $validated);
            $designation = mDesignation::create($validated);
            $designation->departments()->sync($this->departmentIds);
            Log::info("Designation created.");
        }
        unset($this->list);
        $this->dispatch("hide-model");
        $this->notify("Designation saved successfully")->send();
    }

    public function edit($id)
    {
        $this->editingId = $id;
        $row = mDesignation::findOrFail($id);
        foreach ((new mDesignation)->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->departmentIds = $row->departments->pluck("id")->toArray();
        $this->dispatch("toggle-choices", $this->departmentIds);
    }

    #[Computed(persist:true)]
    public function departmentList()
    {
        return \App\Models\configs\Department::pluck('name', 'id')->toArray();
    }

    public function delete($id)
    {
        authorizePermission(PermissionList::DESIGNATION_DELETE);
        mDesignation::find($id)->delete();
        unset($this->list);
        $this->notify("Designation deleted successfully")->send();
    }

    #[On("hidden.bs.modal")]
    public function modalHidden()
    {
        $this->editingId = null;
        $this->reset(["title", "description", "level", "role"]);
        $this->resetValidation();
        $this->dispatch("toggle-choices", []);
    }

    public function render()
    {
        return view('livewire.payroll.designation');
    }
}
