<?php

namespace App\Livewire\Payroll\AdditionalIncome;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\AdditionalIncomeExcelSample;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Repositories\SalaryRepository;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Payroll\AdditionalIncome;
use App\Models\Payroll\AdditionalIncomeMapping;
use App\Models\Payroll\Payment;
use App\Models\Payroll\Payslip;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Additional Income')]
class UploadAdditionalIncome extends Component
{
    use WithFileUploads, WithNotify;

    public $excelHeaders = [
        "employee name" => 'Employee Name',
        "employee code" => 'Employee Code',
        "income heading" => 'Income Heading',
        "nepali date" => 'Nepali Date',
        "gross amount" => 'Gross Amount',
    ];

    public $excelValues = [
        "employee name" => 'Vianet',
        "employee code" => 'VIA-100',
        "income heading" => 'Dashain Allowance',
        "nepali date" => '2081-06-15',
        "gross amount" => '5000.00',
    ];

    // [['data] => [], 'error' => ''];
    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $excelFile = "";
    public $excelValidationMessages = "";

    public $currentNepaliMonth;

    public function mount()
    {
        $this->currentNepaliMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('m');
    }

    public function updatedExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'excelValidationMessages', 'excelUploaded');
        $this->excelData = [];
    }

    public function validateData()
    {
        $this->excelData = [];
        if (!($data = $this->validateFile())) return;
        try {
            foreach ($data as $item) {
                $errorMessage = "";
                $response = $this->validations($item);
                if (isset($response['status']) ? !$response['status'] : false) {
                    $this->excelDataValidated = false;
                    $errorMessage = $response['message'];
                }
                array_push($this->excelData, ['data' => $item, 'error' => $errorMessage]);
            }
        } catch (\Exception $e) {
            logError("Error uploading additional income from excel", $e);
            return $this->notify("Error validating additional income: " . $e->getMessage())->type("error")->send();
        }
    }

    public function validations($data)
    {
        $employeeCode = $data['employee code'];
        $employee = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin("companies as comp", "comp.id", "employees.company_id")
            ->whereRaw("CONCAT(comp.code, '-', org.employee_code) = '$employeeCode'")
            ->select(
                'employees.id as id',
                'employees.company_id',
            )
            ->first();
        if (!$employee) return $this->errorResponse("Employee not found with employee code: " . ($employeeCode ? $employeeCode : '(NULL)'));

        $payslip = Payslip::where("employee_id", $employee->id)->first();
        if (!$payslip) return $this->errorResponse("Payslip not found for the employee");

        $additionalIncomeHeading = AdditionalIncome::where('name', $data['income heading'])->first();
        if (!$additionalIncomeHeading) {
            return $this->errorResponse("Unable to find the income heading.");
        }

        if (!(is_float($data['gross amount']) || is_numeric($data['gross amount']))) {
            return $this->errorResponse("Only numbers are allowed in gross amount.");
        }

        $month = explode('-', $data['nepali date'] ?? "")[1] ?? "";

        if ($month < ($this->currentNepaliMonth - 1) && !isSuperAdmin()) {
            return $this->errorResponse("Invalid date or date format.");
        }
    }

    function errorResponse(string $message = "", $data = null)
    {
        return [
            "status" => false,
            "message" => $message,
            "data" => $data ?? []
        ];
    }

    public function uploadData()
    {
        if (!$this->excelDataValidated) return $this->notify("Excel validation failed");
        $data = [];
        foreach ($this->excelData as $item) {
            if ($item['error']) return $this->notify("Excel validation failed: " . $item['error']);
            array_push($data, $item['data']);
        }
        if (!($data = $this->validateFile())) return;
        $this->excelValidationMessages = "";
        DB::beginTransaction();
        try {
            $additionalIncomeHeading = AdditionalIncome::all()->keyBy('name');
            $employee_codes = array_column($data, 'employee code');
            $conditions = [];
            foreach ($employee_codes as $code) {
                $conditions[] = "CONCAT(comp.code, '-', employee_org.employee_code) = '{$code}'";
            }
            $whereClause = implode(' OR ', $conditions);

            $result = EmployeeOrg::leftJoin('employees', 'employees.id', '=', 'employee_org.employee_id')
                ->leftJoin('companies as comp', 'comp.id', '=', 'employees.company_id')
                ->leftJoin('payslips', 'payslips.employee_id', '=', 'employees.id')
                ->where('payslips.status', 'Active')
                ->select('employee_code', 'employees.id as employee_id', 'comp.code', 'payslips.id as payslip_id', 'employees.mstat', 'employees.gender', 'payslips.basic_salary')
                ->whereRaw($whereClause)
                ->get();

            $keyedResult = $result->keyBy(function ($item) {
                return "{$item->code}-{$item->employee_code}";
            });

            $taxResult = [];
            if (!$additionalIncomeHeading[$data[0]['income heading']]['tax_in_salary'] ?? 0)
                $taxResult = $this->calculateTax($data, $keyedResult);

            $additionalIncomes = [];
            $laravelNepaliDate = new FacadesLaravelNepaliDate();
            foreach ($data as $item) {
                $nepaliDateArray = explode('-', $item['nepali date']);
                $englishDateResult = $laravelNepaliDate->BS_to_AD($nepaliDateArray[0], $nepaliDateArray[1], $nepaliDateArray[2]);
                $englishDate = $englishDateResult['year'] . '-' . ($englishDateResult['month'] < 10 ? '0' . $englishDateResult['month'] : $englishDateResult['month']) . '-' . ($englishDateResult['date'] < 10 ? '0' . $englishDateResult['date'] : $englishDateResult['date']);
                $gross_amount = $item['gross amount'] ?? 0;
                $paid_tds = 0;
                // Calculate tds only in the incomes whose tax in salary is false
                if (!$additionalIncomeHeading[$item['income heading']]['tax_in_salary'] ?? 0)
                    $paid_tds = $taxResult[$keyedResult[$item['employee code']]['employee_id']] ?? 0;

                if ($item['tax amount'] ?? 0)
                    $paid_tds = $item['tax amount'] ?? 0;
                $net_amount = $gross_amount - $paid_tds;
                $uuid = ($keyedResult[$item['employee code']]['employee_id'] ?? 0) . '-' . ($item['nepali date']) . '-' . ($additionalIncomeHeading[$item['income heading']]['id'] ?? 0);
                $additionalIncomes[] = [
                    'uuid' => $uuid,
                    'payslip_id' => $keyedResult[$item['employee code']]['payslip_id'] ?? 0,
                    'additional_income_id' => $additionalIncomeHeading[$item['income heading']]['id'] ?? 0,
                    'gross_amount' => $gross_amount,
                    'tds' => $paid_tds,
                    'net_amount' => $net_amount,
                    'date_nep' => $item['nepali date'],
                    'date_eng' => $englishDate,
                    'employee_id' => $keyedResult[$item['employee code']]['employee_id'] ?? 0,
                ];
                $this->excelValidationMessages .= "Added additional income of {$item['employee code']} , {$item['employee name']}.<br />";
            }
            AdditionalIncomeMapping::upsert($additionalIncomes, ['employee_id', 'date_nep', 'additional_income_id'], ['gross_amount', 'tds', 'net_amount']);
            DB::commit();
            $this->excelUploaded = true;
            return $this->notify("Additional income added successfully.")->send();
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error while adding additional income ", $e);
            return $this->notify("Error creating additional income")->type("error")->send();
        }
    }

    public function calculateTax($items, $employees)
    {
        $salaryRepo = new SalaryRepository();

        $toReturn = [];
        foreach ($items as $item) {
            $dateArray = explode('-', $item['nepali date']);
            $year = $dateArray[0];
            $month = $dateArray[1] - 1;
            if ($month < 1) {
                $month = 12;
                $year -= 1;
            }
            if (!($employees[$item['employee code']] ?? 0)) continue;
            $payment = Payment::where([['employee_id', $employees[$item['employee code']]['employee_id']], ['year', $year], ['month', $month]])->select('annual_taxable_income', 'annual_taxable_amount', 'yearly_tds')->first();
            $remaining_month = json_decode($payment?->adjusted_payment_meta ?? $payment?->actual_payment_meta);
            $ssf_tax_multiplier = Constant::SSF_DEDUCTION_MULTIPLIER;
            if (vianetHrm()) $ssf_tax_multiplier = Constant::SSF_DEDUCTION_MULTIPLIER_VIANET;
            $calValue = [
                'mstat'                     => $employees[$item['employee code']]['mstat'],
                'gender'                    => $employees[$item['employee code']]['mstat'],
                'calculatedTaxableIncome'   => [
                    'annual_taxable_income' => ($payment->annual_taxable_income ?? 0) + ($item['gross amount'] ?? 0),
                ],
                'calculatedTaxableAmount'   => [
                    'annual_taxable_amount' => ($payment->annual_taxable_amount ?? 0) + ($item['gross amount'] ?? 0),
                    'ssf_deduction_for_tax' => [
                        'current' => $employees[$item['employee code']]['basic_salary'] * $ssf_tax_multiplier
                    ]
                ],
                'remainingMonths'           => $remaining_month,
            ];

            $additional_income_tds = ($salaryRepo->calculateTax($calValue)['tds'] ?? 0) * 12 - ($payment->yearly_tds ?? 0);
            $toReturn[$employees[$item['employee code']]['employee_id']] = $additional_income_tds;
        }

        return $toReturn;
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray([], $this->excelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach ($this->excelHeaders as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function downloadSample()
    {
        return Excel::download(new AdditionalIncomeExcelSample(
            $this->excelHeaders,
            $this->excelValues
        ), 'AdditionalIncomeExcelSample.xlsx');
    }

    public function render()
    {
        return view('livewire.payroll.additional-income.upload-additional-income');
    }
}
