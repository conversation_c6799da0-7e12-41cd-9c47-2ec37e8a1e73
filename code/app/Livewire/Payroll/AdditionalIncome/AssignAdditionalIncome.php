<?php

namespace App\Livewire\Payroll\AdditionalIncome;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Models\Payroll\AdditionalIncome;
use App\Models\Payroll\AdditionalIncomeMapping;
use App\Models\Payroll\Payslip;
use App\Traits\NepaliCalendarTrait;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Assign Additional Income')]
class AssignAdditionalIncome extends Component
{
    use WithDataTable, NepaliCalendarTrait, WithNotify;

    public $showTitle = true, $selectAdditionalIncome, $selectDate, $employeeList = [], $selectedEmployees = [], $employeeSearch;

    public $selectAdditionalIncomeToView, $selectedFiscalYear, $year, $selectedMonth;

    public function mount($showTitle = true)
    {
        $payslipEmployeeIds = Payslip::where('status', 'Active')->pluck('employee_id')->toArray();
        $this->employeeList = array_values(array_filter(Employee::list(), function ($employee) use ($payslipEmployeeIds) {
            return in_array($employee['id'], $payslipEmployeeIds);
        }));

        $this->showTitle = $showTitle;
        $fiscalYear = FiscalYear::where('is_active', true)->first();
        $this->selectedFiscalYear = $fiscalYear?->id;
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->setYearAndMonth();
        $this->setExcelOptions();
    }

    public function rules()
    {
        return [
            'selectAdditionalIncome' => ['required'],
            'selectDate' => ['required'],
        ];
    }

    public function validationAttributes()
    {
        return [
            'selectAdditionalIncome' => 'additional income',
            'selectDate' => 'date',
        ];
    }

    public function setYearAndMonth()
    {
        $fiscalYear = FiscalYear::where('id', $this->selectedFiscalYear)->first();
        list($start_date_year, $start_date_month, $start_date_day) = explode('-', $fiscalYear->start_date);
        list($end_date_year, $end_date_month, $end_date_day) = explode('-', $fiscalYear->end_date);
        $this->year = $start_date_year;
        if ($this->selectedMonth < $start_date_month) {
            $this->year = $end_date_year;
        }
    }

    public function updated($property)
    {
        if ($property == 'selectAdditionalIncomeToView' || $property == 'selectedFiscalYear' || $property == 'selectedMonth') {
            $this->setYearAndMonth();
            unset($this->list);
        }
    }

    public function showEmployeeListModal()
    {
        $this->validate();
        $this->dispatch('show-employee-list-modal');
    }

    public function saveAssignedIncome()
    {
        $this->validate(['selectedEmployees' => ['required']]);

        $payslips = Payslip::whereIn('employee_id', $this->selectedEmployees)
            ->where('status', 'Active')
            ->get();
        $additionalIncome = AdditionalIncome::where('id', $this->selectAdditionalIncome)->first();
        $query = new AdditionalIncomeMapping;

        $data = [];
        if ($additionalIncome->type == 'fixed') {
            foreach ($payslips as $payslip) {
                $data[] = [
                    'payslip_id'            => $payslip->id,
                    'employee_id'            => $payslip->employee_id,
                    'additional_income_id'  => $additionalIncome->id,
                    'amount'                => $additionalIncome->value,
                    'date_nep'              => $this->selectDate,
                    'date_eng'              => $this->getEnglishDate($this->selectDate)
                ];
            }
        } else if ($additionalIncome->type == 'rate') {
            foreach ($payslips as $payslip) {
                $amount = 0;
                if ($additionalIncome->calculate_from == 'basic_salary') {
                    $amount = $payslip->basic_salary * $additionalIncome->value;
                }
                $data[] = [
                    'payslip_id'            => $payslip->id,
                    'employee_id'            => $payslip->employee_id,
                    'additional_income_id'  => $additionalIncome->id,
                    'amount'                => $amount,
                    'date_nep'              => $this->selectDate,
                    'date_eng'              => $this->getEnglishDate($this->selectDate)
                ];
            }
        }

        $result = $query->insert($data);
        if ($result) {
            $this->notify('Additional income assigned successfully')->send();
            unset($this->list);
        } else $this->notify('Fail to assign additional income.')->type('error')->send();

        $this->reset(['selectAdditionalIncome', 'selectDate', 'selectedEmployees']);
        $this->resetErrorBag();

        $this->resetEmployeeListModal();
    }

    public function exportAllData()
    {
        $data = $this->queryList()->get();


        $processedData = [];
        foreach ($data as $item) {
            $processedData[] = [
                $item->payslip?->employee?->outsource_company?->name ?? $item->payslip?->employee?->company?->name,
                $item->payslip?->employee?->branch?->name ?? "",
                $item->payslip?->employee?->department?->name ?? "",
                $item->payslip?->employee?->name,
                $item->payslip?->employee?->employee_code,
                $item->date_nep,
                $item->date_eng,
                $item->gross_amount,
                $item->tds,
                $item->net_amount
            ];
        }

        return Excel::download(new class($processedData, $this->exportColumnHeadersMap) implements FromArray, WithHeadings
        {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function array(): array
            {
                return $this->data;
            }

            public function headings(): array
            {
                return $this->exportColumnHeadersMap;
            }
        }, $this->exportFileName);
    }

    public function queryList()
    {
        return AdditionalIncomeMapping::with(['payslip:id,employee_id', 'payslip.employee:id,first_name,middle_name,last_name,company_id', 'payslip.employee.company:id,name,code', 'payslip.employee.outsource_company:name', 'payslip.employee.branch:name', 'payslip.employee.department:name', 'additional_income'])
            ->when($this->selectAdditionalIncomeToView, function ($query) {
                return $query->where('additional_income_id', $this->selectAdditionalIncomeToView);
            })
            ->where([
                ['date_nep', 'like', "{$this->year}-{$this->selectedMonth}%"]
            ]);
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Assigned Additional Income List.xlsx";
        $this->exportColumnHeadersMap = [
            'Vendor',
            'Branch',
            'Department',
            'Employee Name',
            'Employee Code',
            "Nepali Date",
            "English Date",
            'Gross Amount',
            "TDS",
            "Net Amount",
        ];
    }

    #[On('hide.bs.modal')]
    public function resetEmployeeListModal()
    {
        $this->dispatch('hide-employee-list-modal');
        $this->resetErrorBag('selectedEmployees');
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->queryList()->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function additionalIncomeList()
    {
        return AdditionalIncome::all();
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::get();
    }

    public function render()
    {
        return view('livewire.payroll.additional-income.assign-additional-income');
    }
}
