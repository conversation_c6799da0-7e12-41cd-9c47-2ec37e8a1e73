<?php

namespace App\Livewire\Payroll\AdditionalIncome;

use App\Facades\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Repositories\SalaryRepository;
use App\Models\Payroll\AdditionalIncome;
use App\Models\Payroll\Payment;
use App\Traits\WithDataTable;
use Exception;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Additional Income')]
class AdditionalIncomePage extends Component
{
    use WithDataTable;

    public $name, $type = 'fixed', $calculate_from, $value, $tax_in_salary = 1;

    public function rules()
    {
        return [
            'name' => 'required',
            'type' => ['required', 'in:fixed,rate'],
            'calculate_from' => ['required_if:type,rate'],
            'value' => ['required'],
            'tax_in_salary' => ['required', 'boolean'],
        ];
    }

    public function validationAttributes()
    {
        return [
            'tax_in_salary' => 'calculate tax in salary',
            'name'          =>'additional income',
            'value'         =>'amount',
        ];
    }

    public function save()
    {
        $this->validate();
        try {
            AdditionalIncome::create($this->all());
            unset($this->list);
            $this->notify('Additional income added successfully')->send();
        } catch (Exception $e) {
            $this->notify('Failed to add additional income')->type('error')->send();
        }
        $this->dispatch('hide-model');
    }

    public function delete(int $id): void
    {
        try {
            AdditionalIncome::findOrFail($id)->delete();
            unset($this->list);

            $this->message = 'Additional income deleted successfully!!';
            $this->notify($this->message)->send();
        } catch (Exception $e) {
            $this->notify('Cannot delete assigned additional income!!')->type('error')->send();
        }
    }

    #[On('hide.bs.modal')]
    public function resetFields()
    {
        $this->reset(['name', 'type', 'calculate_from', 'value', 'tax_in_salary']);
        $this->resetErrorBag();
    }

    #[Computed(persist: true)]
    public function list()
    {
        return AdditionalIncome::paginate($this->perPage);
    }

    #[Computed()]
    public function additionalIncomeType()
    {
        return [
            "fixed" => "Fixed",
            "rate" => "Rate"
        ];
    }

    #[Computed()]
    public function calculateFrom()
    {
        return Constant::ADDITIONAL_INCOME_CALCULATE_FROM;
    }

    public function render()
    {
        return view('livewire.payroll.additional-income.additional-income-page');
    }
}
