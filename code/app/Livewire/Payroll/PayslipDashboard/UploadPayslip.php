<?php

namespace App\Livewire\Payroll\PayslipDashboard;

use App\Exports\PayslipExcelSample;
use App\Http\Repositories\PayslipApprovalRepository;
use App\Models\Payroll\Perk;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Payslips')]
class UploadPayslip extends Component
{
    use WithFileUploads, WithNotify;

    public $payslipExcelHeaders = [
        "employee code" => 'CB-000',
        "name" => 'Test Employee',
        "designation" => 'Sr. User',
        "band-level" => '8-C',
        "employee status" => "Permanent",
        "start date" => 'mm/dd/yyyy or yyyy-mm-dd',
        "end date" => 'mm/dd/yyyy or yyyy-mm-dd',
    ];

    public $optionalExcelHeaders = [
        "job" => 'Developer',
        "cit" => 0.00,
        "insurance" => 0.00,
        'insurance expiry date' => 'mm/dd/yyyy or yyyy-mm-dd',
        "medical insurance" => 0.00,
        'medical insurance expiry date' => 'mm/dd/yyyy or yyyy-mm-dd',
        "house insurance" => 0.00,
        'house insurance expiry date' => 'mm/dd/yyyy or yyyy-mm-dd',
        'cit' => 0.00,
        'stipend' => 0.00,
    ];

    public $allowanceHeaders = [];

    // [['data] => [], 'error' => ''];
    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $payslipExcelFile = "";
    public $payslipExcelValidationMessages = "";

    public function mount()
    {
        $this->payslipExcelHeaders['enroll status'] = implode(", ", field_enums('payslip_requests', 'enroll_status'));
        $this->payslipExcelHeaders['payment type'] = implode(", ", field_enums('payslip_requests', 'payment_type'));
        $this->payslipExcelHeaders['payslip type'] = implode(", ", field_enums('payslip_requests', 'payslip_type'));
        $this->payslipExcelHeaders['payslip for'] = implode(", ", field_enums('payslip_requests', 'payslip_for'));
        $this->payslipExcelHeaders['allow ot'] = implode(", ", ['Y', 'N']);
        $this->optionalExcelHeaders['payment frequency'] = implode(", ", field_enums('payslip_requests', 'payment_frequency'));

        $perks = Perk::all();
        foreach ($perks as $perk) {
            $this->allowanceHeaders[$perk->name] = $perk->default_value;
        }

        if (fedexHrm()) {
            $this->optionalExcelHeaders['added basic salary'] = "0.00";
            $this->optionalExcelHeaders['non cash deduction'] = "0.00";
        }
    }

    public function updatedPayslipExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'payslipExcelValidationMessages', 'excelUploaded');
        $this->excelData = [];
    }

    // public function validatePayslipData()
    // {
    //     $this->excelData = [];
    //     if (!($data = $this->validateFile())) return;
    //     $repo = new PayslipApprovalRepository;
    //     try {
    //         foreach ($data as $item) {
    //             $payslipErrorMessage = "";
    //             $response = $repo->validatePayslipDataForExcel($item);
    //             if (!$response['status']) {
    //                 $this->excelDataValidated = false;
    //                 $payslipErrorMessage = $response['message'];
    //             }
    //             array_push($this->excelData, ['data' => $item, 'error' => $payslipErrorMessage]);
    //         }
    //     } catch (\Exception $e) {
    //         logError("Error creating payslip from excel", $e);
    //         return $this->notify("Error validating payslip: " . $e->getMessage())->type("error")->send();
    //     }
    // }

    // public function uploadPayslipData()
    // {
    //     if (!$this->excelDataValidated) return $this->notify("Excel validation failed");
    //     $data = [];
    //     foreach ($this->excelData as $item) {
    //         if ($item['error']) return $this->notify("Excel validation failed: " . $item['error']);
    //         array_push($data, $item['data']);
    //     }
    //     if (!($data = $this->validateFile())) return;
    //     $this->payslipExcelValidationMessages = "";
    //     $repo = new PayslipApprovalRepository;
    //     DB::beginTransaction();
    //     try {
    //         foreach ($data as $item) {
    //             $this->payslipExcelValidationMessages .= "Creating payslip of {$item['employee code']} , {$item['name']}.<br />";
    //             $response = $repo->createPayslipFromExcel($item);
    //             if (!$response['status']) {
    //                 $this->payslipExcelValidationMessages .= "<span class='text-danger fw-bold'>Error: {$response['message']}</span><br />";
    //                 $this->payslipExcelValidationMessages .= "<span class='text-danger fw-bold'>All created payslips are reverted</span><br />";
    //                 return $this->errorResponse("Some error occurred. All the payslips uploaded are reverted");
    //             }
    //             $this->payslipExcelValidationMessages .= "{$response['message']}<br />";
    //             $this->payslipExcelValidationMessages .= "Payslip of {$item['employee code']} created.<br />";
    //             $ticket = $response['data'];
    //             $this->payslipExcelValidationMessages .=
    //                 "<a href='" .
    //                 route('ticketPage', ['workflow' => $ticket->workflow, 'requestId' => $ticket->id]) .
    //                 "' style='color: blue; text-decoration: underline' target='_blank'>View Payslip</a><br /><br />";
    //         }
    //         DB::commit();
    //         $this->excelUploaded = true;
    //         return $this->notify("Payslip Uploaded Successfully")->send();
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         logError("Error while creating payslip", $e);
    //         return $this->notify("Error creating payslip: " . $e->getMessage())->type("error")->send();
    //     }
    // }

    public function validatePayslipData()
    {
        if (!($data = $this->validateFile())) {
            return;
        }

        $repo = new PayslipApprovalRepository;
        try {
            $validationResults = $repo->validatePayslipDataForExcel($data);
            foreach ($validationResults as $index => $result) {
                $payslipErrorMessage = "";
                if (!$result['status']) {
                    $this->excelDataValidated = false;
                    $payslipErrorMessage = $result['message'];
                }
                $this->excelData[] = ['data' => $data[$index], 'error' => $payslipErrorMessage];
            }
            if (!$this->excelDataValidated) $this->notify("Some data are not validated")->type("error")->send();
        } catch (\Exception $e) {
            logError("Error creating payslip from excel", $e);
            $this->notify("Error validating payslip: " . $e->getMessage())->type("error")->send();
        }
    }

    public function uploadPayslipData()
    {
        if (!$this->excelDataValidated) {
            return $this->notify("Excel validation failed");
        }

        $data = collect($this->excelData)->pluck('data')->all();

        $repo = new PayslipApprovalRepository;
        $response = $repo->createPayslipsFromExcel($data);

        if (!empty($response['data']))
            $this->payslipExcelValidationMessages = $response['data']['validation'];

        if (!$response['status']) {
            $this->notify($response['message'])->type('error')->send();
            return;
        }

        $this->excelUploaded = true;
        return $this->notify("Payslips Uploaded Successfully")->send();
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray([], $this->payslipExcelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach (array_keys($this->payslipExcelHeaders) as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0], ['start date', 'end date', 'insurance expiry date', 'medical insurance expiry date', 'house insurance expiry date']);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function downloadSample()
    {
        return Excel::download(new PayslipExcelSample([
            ...$this->payslipExcelHeaders,
            ...$this->optionalExcelHeaders,
            ...$this->allowanceHeaders,
        ]), 'PayslipExcelSample.xlsx');
    }

    public function render()
    {
        return view('livewire.payroll.payslip-dashboard.upload-payslip');
    }
}
