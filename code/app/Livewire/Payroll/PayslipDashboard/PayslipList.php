<?php

namespace App\Livewire\Payroll\PayslipDashboard;

use App\Exports\PayslipExport;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\Unit;
use App\Models\Employee\Employee;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use App\Models\Payroll\Perk;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;

#[Title('Payslip List')]
class PayslipList extends Component
{
    use WithDataTable, MultiselectEmployeeSearch, WithFileUploads, WithNotify;

    public $existingDocuments = [], $removingDocumentIds = [], $perks = [];

    public $status, $employee_id = "", $enrollStatus, $paymentType, $company_id, $region_id, $branch_id, $department_id, $unit_id;

    public $bands = [], $levels = [], $salaryDetails = [], $newSalaryDetails = [];

    public $selectedAdditionalAllowance = [];

    public $showPayslipForm = false;

    public $employee_department_id, $employee_branch_id;

    #[Url(as: 'expiring')]
    public $expiringPayslip = false;

    #[Url(as: 'expired')]
    public $expiredPayslip = false;

    #[Url(as: 'active')]
    public $activePayslip = false;

    #[Url(as: 'pending')]
    public $pendingPayslip = false;

    #[Url(as: 'terminatedEmployee')]
    public $terminatedEmployeePayslip = false;

    public PayslipForm $payslipForm;
    public bool $showDojInhouse = false;

    public $payslipQuery, $startDate, $endDate, $employeeType;

    public function mount()
    {
        $this->singleSelectAttributes = ['payslip_approver', 'employee_list', 'payslip_employee_id'];
        $this->withTrashed = true;
        $this->sortBy = "created_at";
        $this->sortDirection = 'desc';
        $this->setPermissionForEmployeeDropdown();
        $this->initializeDate();
        if (!scopeAll()) {
            $this->company_id = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->region_id = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->branch_id = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
            $this->department_id = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    public function initializeDate()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $this->startDate = "{$currentYear}-{$currentMonth}-01";
        $this->endDate = $nepaliDate;
    }

    public function exportPayslip()
    {
        $title = "Payslip List";

        $heading = [
            'Employee Code',
            'Name',
            'Branch',
            'Department',
            'Employee Status',
            'Vendor',
            'Contract Start Date(AD)',
            'Contract End Date(AD)',
            'Contract Start Date(BS)',
            'Contract Stop Date(BS)',
            'Gender',
            'Married Status',
            'Job',
            'Band Grade',
            'Stipend',
            'Basic Salary',
        ];

        if (fedexHrm()) {
            $heading = [...$heading, 'Added Basic Salary', 'Total Basic Salary'];
        }

        $heading = [...$heading, 'SSF 20%', 'Allowance'];

        $perks = Perk::all();
        $perkHeading = [];
        foreach ($perks as $perk) {
            $perkHeading[$perk->id] = $perk->name;
        }
        $heading = [...$heading, ...$perkHeading, 'Total CTC', 'CIT', 'Insurance', 'Medical Insurance', 'House Insurance'];

        $dataToReturn = [];
        $payslipData = $this->getPayslipQuery()->get();
        foreach ($payslipData as $data) {
            $danger = false;
            if ($data->deleted_at) {
                $danger = true;
            }
            $toReturn = [
                $data->employee_code,
                $data->employee_name,
                $data->orgInfo?->branch?->name,
                $data->orgInfo?->department?->name,
                $data->employee_status?->name ?? 'N/A',
                $data->orgInfo->outsource_company?->name ?? $data->company_name,
                $data->start_date_eng,
                $data->stop_date_eng,
                $data->start_date_nep,
                $data->stop_date_nep,
                $data->gender,
                $data->mstat,
                $data->job?->name,
                $data->band?->name . $data->level?->name,
                $data->stipend,
            ];

            if (fedexHrm()) {
                $grade_structure = $data->banded_grade_structure_id ? $data->banded_grade_structure : $data->grade_structure;
                $toReturn = [
                    ...$toReturn,
                    $grade_structure?->original_basic_salary ?? 0,
                    $grade_structure?->added_basic_salary ?? 0,
                    $grade_structure?->basic_salary,
                    $grade_structure?->basic_salary * 0.2,
                    $grade_structure?->allowance,
                ];
            } else {
                $toReturn = [
                    ...$toReturn,
                    $data->grade_structure?->basic_salary,
                    $data->grade_structure?->basic_salary * 0.2,
                    $data->grade_structure?->allowance
                ];
            }

            $perksData = array_map(function () {
                return 0;
            }, $perkHeading);
            foreach (json_decode($data?->allowance_metadata) ?? [] as $key => $perks) {
                foreach ($perks as $amount) {
                    $perksData[$key] = $amount;
                }
            }

            $ctc = $data->stipend + $data->grade_structure?->ctc + $data->total_perks_amount - ($data->non_cash_deduction ?? 0);
            $cit = $data->cit;
            $insurance = $data->insurance;
            $medical_insurance = $data->medical_insurance;
            $house_insurance = $data->house_insurance;

            $toReturn = ['danger' => $danger, 'rowData' => [...$toReturn, ...$perksData, $ctc, $cit, $insurance, $medical_insurance, $house_insurance]];
            $dataToReturn[] = $toReturn;
        }

        ini_set('memory_limit', '512M');
        set_time_limit(300);
        return Excel::download(
            new PayslipExport($title, $heading, $dataToReturn),
            "$title.xlsx"
        );
    }

    public function getPayslipQuery()
    {
        $displayWithoutCancelPayslip = \App\Models\configs\Setting::where('key', 'show_cancel_payslip')
            ->where('namespace', 'payslip_setting')
            ->value('value') ?? true;

        $designationLevel = $this->designationLevel;
        $payslipRequestResult = PayslipRequest::with([
            'band:id,name',
            'level:id,name',
            'job',
            'grade_structure',
            'banded_grade_structure',
            'orgInfo' => function ($query) {
                $query->withTrashed()->select('employee_id', 'branch_id', 'department_id', 'unit_id', 'id', 'outsource_company_id');
            },
            'orgInfo.branch:id,name',
            'orgInfo.department:id,name',
            'orgInfo.unit:id,name',
            'orgInfo.outsource_company:id,name'
        ])
            ->leftJoin('employees as emp', 'emp.id', '=', 'payslip_requests.employee_id')
            ->leftJoin('companies', 'companies.id', '=', 'payslip_requests.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'emp.id')
            ->leftJoin('payslips', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
            ->leftJoin('payslip_perks_mapping as perks', 'perks.payslip_id', '=', 'payslips.id')
            ->leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->when($this->employeeType, function ($query) {
                if ($this->employeeType == "active")
                    $query->whereNull('org.termination_date');
                else
                    $query->whereNotNull('org.termination_date');
            })
            ->when($this->status, function ($query) {
                if ($this->status == WorkflowState::SUBMITTED)
                    $query->where('payslip_requests.state', $this->status);
                else
                    $query->where('payslips.status', $this->status);
            })
            ->when($this->enrollStatus, function ($query) {
                $query->where('payslip_requests.enroll_status', $this->enrollStatus);
            })
            ->when($this->paymentType, function ($query) {
                $query->where('payslip_requests.payment_type', $this->paymentType);
            })
            ->when($this->employee_id, function ($query) {
                $query->where('payslip_requests.employee_id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('emp.company_id', $this->company_id);
            })
            ->when($this->region_id, function ($query) {
                $query->where('org.region_id', $this->region_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->when($this->unit_id, function ($query) {
                $query->where('org.unit_id', $this->unit_id);
            })
            ->when($this->expiringPayslip, function ($query) {
                if ($this->startDate <= $this->endDate)
                    return $query->where([
                        ['payslips.stop_date_nep', '>=', $this->startDate],
                        ['payslips.stop_date_nep', '<=', $this->endDate]
                    ])->where('payslips.status', 'Active');
            })
            ->when($this->expiredPayslip, function ($query) {
                if ($this->startDate <= $this->endDate)
                    return $query->where([
                        ['payslips.stop_date_nep', '>=', $this->startDate],
                        ['payslips.stop_date_nep', '<=', $this->endDate]
                    ])->where('payslips.status', 'Expired');
            })
            ->when($this->activePayslip, function ($query) {
                return $query->where('payslips.status', 'Active');
            })
            ->when($this->pendingPayslip, function ($query) {
                return $query->where('payslip_requests.state', WorkflowState::SUBMITTED);
            })
            ->when($this->terminatedEmployeePayslip, function ($query) {
                return $query->whereNotNull('org.termination_date')
                    ->whereNotNull('org.deleted_at')
                    ->whereRaw('payslips.id = (
                        SELECT p.id 
                        FROM payslips p
                        WHERE p.employee_id = emp.id
                        ORDER BY p.created_at DESC
                        LIMIT 1
                    )');
            })
            ->when(!auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP), function ($query) use ($designationLevel) {
                if ($designationLevel !== null) {
                    return $query->where(function ($q) use ($designationLevel) {
                        $q->whereNull('designation.level')
                            ->orWhere('designation.level', '<=', $designationLevel);
                    });
                }
                return $query->whereNull('designation.level');
            })
            ->when(!$displayWithoutCancelPayslip, function ($query) {
                return $query->whereNot('payslip_requests.state', WorkflowState::CANCELLED);
            })
            ->select(
                'payslip_requests.id',
                'payslip_requests.company_id',
                'payslip_requests.employee_id',
                'payslip_requests.job_id',
                'payslip_requests.designation_id',
                'payslip_requests.band_id',
                'payslip_requests.pgrade_id',
                'payslip_requests.grade_structure_id',
                'payslip_requests.banded_grade_structure_id',
                'payslip_requests.allowance_metadata',
                'payslip_requests.enroll_status',
                'payslip_requests.employee_status_id',
                'payslip_requests.stipend',
                'payslip_requests.cit',
                'payslip_requests.insurance',
                'payslip_requests.medical_insurance',
                'payslip_requests.house_insurance',
                'payslip_requests.allow_ot',
                'payslip_requests.payment_type',
                'payslip_requests.non_cash_deduction',
                'payslip_requests.remarks',
                'payslip_requests.workflow',
                'payslip_requests.state',
                'payslip_requests.created_at',
                'payslip_requests.start_date_nep',
                'payslip_requests.stop_date_nep',
                'payslip_requests.start_date_eng',
                'payslip_requests.stop_date_eng',
                'payslips.status',
                'emp.gender',
                'emp.mstat',
                'org.deleted_at',
                DB::raw("CONCAT_WS(' ', emp.first_name, NULLIF(emp.middle_name,''), emp.last_name) as employee_name"),
                DB::raw("CONCAT(companies.code,'-',org.employee_code) as employee_code"),
                DB::raw('SUM(perks.amount) as total_perks_amount'),
                DB::raw('companies.name as company_name')
            )
            ->groupBy(
                'payslip_requests.id',
                'payslip_requests.company_id',
                'payslip_requests.employee_id',
                'payslip_requests.job_id',
                'payslip_requests.designation_id',
                'payslip_requests.band_id',
                'payslip_requests.pgrade_id',
                'payslip_requests.grade_structure_id',
                'payslip_requests.banded_grade_structure_id',
                'payslip_requests.allowance_metadata',
                'payslip_requests.enroll_status',
                'payslip_requests.employee_status_id',
                'payslip_requests.stipend',
                'payslip_requests.cit',
                'payslip_requests.insurance',
                'payslip_requests.medical_insurance',
                'payslip_requests.house_insurance',
                'payslip_requests.allow_ot',
                'payslip_requests.payment_type',
                'payslip_requests.non_cash_deduction',
                'payslip_requests.remarks',
                'payslip_requests.workflow',
                'payslip_requests.state',
                'payslip_requests.created_at',
                'payslip_requests.start_date_nep',
                'payslip_requests.stop_date_nep',
                'payslip_requests.start_date_eng',
                'payslip_requests.stop_date_eng',
                'payslips.status',
                'emp.gender',
                'emp.mstat',
                'org.deleted_at',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'companies.code',
                'org.employee_code',
                'companies.name',
            );

        $payslipRequestResult = filterEmployeesByScope($payslipRequestResult, 'org', 'emp');
        return $payslipRequestResult;
    }

    #[Computed()]
    public function payslipList()
    {
        if ($this->getPayslipQuery())
            return $this->getPayslipQuery()
                ->orderBy($this->sortBy, $this->sortDirection ?? 'asc')
                ->paginate($this->perPage);

        return [];
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::where('is_active', 1)->orderBy('name', 'asc')->get();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->select("name", "id")->get();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->company_id)->orderBy('name', 'asc')->select("name", "id")->get();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->company_id);

            if (!is_null($this?->region_id)) {
                $query->where('region_id', $this->region_id);
            }

            return $query->orderBy('name', 'asc')->select("name", "id")->get();
        }
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return Department::where('company_id', $this->company_id)->orderBy('name', 'asc')->get();
    }

    #[Computed(persist: true)]
    public function unitList()
    {
        return Unit::where('department_id', $this->department_id)->orderBy('name', 'asc')->get();
    }

    #[Computed(persist: true)]
    public function jobList()
    {
        return app(\App\Http\Repositories\Configs\Interfaces\JobRepositoryInterface::class)->getJobs($this->employee_branch_id, $this->employee_department_id);
        // if ($this->employee_department_id)
        //     return Department::find($this->employee_department_id)?->jobs()?->pluck('jobs.name', 'jobs.id')->toArray();
        // return [];
    }

    #[Computed(persist: true)]
    public function designationsList()
    {
        if ($this->employee_department_id)
            return Department::findOrFail($this->employee_department_id)?->designations()->pluck('designations.title', 'designations.id')->toArray();
        return [];
    }

    #[Computed(persist: true)]
    public function additionalAllowanceFormList()
    {
        $formData = [];
        foreach ($this->additionalAllowanceList as $allowance) {
            if (in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                array_push($formData, $allowance);
            }
        }

        return $formData;
    }

    #[Computed(persist: true)]
    public function designationLevel()
    {
        return Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', currentEmployee()->id ?? ''], ['status', 'Active']])
            ->value('designation.level');
    }

    public function updatedPayslipFormDesignationId()
    {
        $gradeStructure = GradeStructure::where('grade_id', $this->payslipForm->designation_id)->get();
        $bandIds = $gradeStructure->map(fn($structure) => $structure->band_id)->unique()->toArray();
        $this->bands = EmployeeBand::whereIn('id', $bandIds)->where('status', 1)->pluck('name', 'id')->toArray();
        $this->levels = [];
    }

    public function updatedPayslipFormBandId()
    {
        $gradeStructure = GradeStructure::where([
            ['grade_id', $this->payslipForm->designation_id],
            ['band_id', $this->payslipForm->band_id],
        ])->get();
        $levelIds = $gradeStructure->map(fn($structure) => $structure->pgrade_id)->unique()->toArray();
        $this->levels = EmployeePgrade::whereIn('id', $levelIds)->pluck('name', 'id')->toArray();
    }

    public function updated($attr)
    {
        if ($attr === 'payslipForm.payslip_for') {
            $this->reset(['payslipForm.stipend', 'payslipForm.payment_frequency']);
        }

        if ($attr === 'company_id') {
            $this->reset(['branch_id', 'department_id', 'unit_id']);
            unset($this->branchList, $this->departmentList, $this->unitList);
        } else if ($attr === 'branch_id') {
            $this->reset(['department_id', 'unit_id']);
            unset($this->departmentList, $this->unitList);
        } else if ($attr === 'department_id') {
            $this->reset(['unit_id']);
            unset($this->unitList);
        }
    }

    public function setAdditionalAllowance()
    {
        $perksKeyAndAmount = [];

        if (!$this->payslipForm->perks) {
            return;
        }

        foreach ($this->payslipForm->perks as $perksArray) {
            foreach ($perksArray as $key => $value) {
                if ($this->additionalAllowanceList->filter(fn($allowance) => $allowance->id == $key)->isNotEmpty()) {
                    if (in_array($key, $this->selectedAdditionalAllowance)) {
                        $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                    }
                } else {
                    $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                }
            }
        }
        // $this->payslipForm->additionalAllowance = json_encode($perksKeyAndAmount);
        $this->payslipForm->additionalAllowance = $perksKeyAndAmount;
    }

    public function save()
    {
        $this->setAdditionalAllowance();
        $payslipRequestTicket = PayslipRequest::where('employee_id', $this->payslipForm->employee_id)->whereNotIn("state", ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL))->get();
        if (count($payslipRequestTicket)) {
            $this->notify("A payslip has already been requested.")->type("error")->duration(5)->send();
            return;
        }
        $data = Employee::join('employee_org', 'employee_org.employee_id', 'employees.id')
            ->where('employees.id', $this->payslipForm->employee_id)
            ->first();

        $result = $this->payslipForm->save();
        if ($result["status"] ?? false) {
            unset($this->payslipList);
            $this->notify("Payslip requested successfully")->send();
            $this->dispatch('hide-payslip-modal');
            $this->reset(['bands', 'levels', 'salaryDetails', 'newSalaryDetails', 'existingDocuments', 'removingDocumentIds']);
        } elseif (!$result["status"] ?? '' === false) {
            if (!$result['message']) {
                $this->notify("Some error occurred!!")->type("error")->send();
            }
            $this->notify($result['message'])->type("error")->send();
        } else {
            $this->notify("Some error occured!")->type("error")->send();
            $this->dispatch('hide-payslip-modal');
            $this->reset(['bands', 'levels', 'salaryDetails', 'newSalaryDetails', 'existingDocuments', 'removingDocumentIds']);
        }
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->dispatch("toggle-selected-employee-id", ['']);
        $this->payslipForm->resetEmployeeId();
        $this->resetFields();
    }

    public function resetFields()
    {
        $this->dispatch("toggle-verifier-id", ['']);
        $this->reset(['selectedAdditionalAllowance', 'showPayslipForm', 'existingDocuments', 'removingDocumentIds', 'bands', 'levels', 'salaryDetails', 'newSalaryDetails']);
        unset($this->additionalAllowanceFormList);
        $this->payslipForm->resetFormField();
        $this->resetErrorBag();
    }

    public function render()
    {
        return view('livewire.payroll.payslip-dashboard.payslip-list');
    }
}
