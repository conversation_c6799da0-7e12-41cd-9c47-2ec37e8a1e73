<?php

namespace App\Livewire\Payroll\PayslipDashboard;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\PayslipApprovalRepository;
use App\Http\Repositories\PayslipRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\configs\Department;
use App\Models\configs\EmpStatus;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use App\Models\Payroll\Perk;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithFileUploads;
use PermissionList;

#[Title('Payslip Form')]
class PayslipForm extends Component
{
    use MultiselectEmployeeSearch, WithNotify, WithFileUploads;
    public $company, $employee_id, $employee_status, $perks = [], $documents = [];
    public $job_id, $designation_id, $band_id, $level_id, $enroll_status, $doj_inhouse, $added_basic_salary = 0, $stipend = 0, $cit = 0, $insurance = 0, $allow_ot = false, $payment_type, $payslip_type, $payslip_remarks, $additionalAllowance, $verifier_id, $insurance_expiry_date, $start_date_nep, $stop_date_nep;
    public $payslip_for = "Full_time", $payment_frequency;
    public bool $showDojInhouse = false;
    public $showStipendFor = Constant::STIPEND_FOR;
    public $medical_insurance = 0;
    public $medical_insurance_expiry_date;
    public $house_insurance = 0;
    public $house_insurance_expiry_date;
    public $non_cash_deduction = 0;
    public bool $showPayslipForm = false;
    public $employee_department_id;
    public $bands = [], $levels = [], $salaryDetails = [], $newSalaryDetails = [], $selectedAdditionalAllowance = [];
    public $existingDocuments = [], $removingDocumentIds = [];
    public $employeeType, $status, $enrollStatus, $paymentType, $company_id;
    public $region_id, $branch_id, $department_id, $unit_id;
    public $employee_name = "";

    #[Url(as: 'expiring')]
    public $expiringPayslip = false;

    #[Url(as: 'expired')]
    public $expiredPayslip = false;

    #[Url(as: 'active')]
    public $activePayslip = false;

    #[Url(as: 'pending')]
    public $pendingPayslip = false;

    #[Url(as: 'terminatedEmployee')]
    public $terminatedEmployeePayslip = false;
    public $perPage;
    public string $sortBy;
    public string $sortDirection;
    public $allowanceList = [];
    public $previewDetail = null;

    public function rules()
    {
        $ruleArray = [
            "payslip_for" => ['required', 'in:Full_time,Part_time,Intern,Trainee,Hourly'],
            "job_id" => ['required'],
            "designation_id" => ['required'],
            "band_id" => ['required'],
            "added_basic_salary" => ['numeric', 'min:0'],
            "level_id" => ['required'],
            "enroll_status" => ['required'],
            "stipend" => ['numeric', 'min:0'],
            "cit" => ['numeric', 'min:0'],
            "insurance" => ['numeric', 'min:0'],
            "insurance_expiry_date" => ['required_if:insurance,>0'],
            "medical_insurance" => ['numeric', 'min:0'],
            "medical_insurance_expiry_date" => ['required_if:medical_insurance,>0'],
            "house_insurance" => ['numeric', 'min:0'],
            "house_insurance_expiry_date" => ['required_if:house_insurance,>0'],
            "allow_ot" => ['boolean'],
            "payment_type" => ['required'],
            "payslip_type" => ['required'],
            "stop_date_nep" => ['required'],
            "start_date_nep" => ['required', function ($attribute, $value, $fail) {
                $startDate = LaravelNepaliDate::from($value)->toEnglishDate();
                if ($this->stop_date_nep !== null) {
                    $stopDate = LaravelNepaliDate::from($this->stop_date_nep)->toEnglishDate();
                    if ($startDate >= $stopDate) {
                        $fail('The start date should be earlier than the stop date.');
                    }
                }
            }],
            "payslip_remarks" => ['nullable', 'max:250'],
            "verifier_id" => ['required'],
            "documents.*" => 'file|mimes:pdf,docx,doc,jpg,jpeg,png'
        ];

        if (in_array($this->payslip_for, $this->showStipendFor)) {
            $rules = [
                'stipend' => ['required', 'numeric', 'min:1'],
                "designation_id" => ['nullable'],
                "band_id" => ['nullable'],
                "level_id" => ['nullable'],
                'payment_frequency' => ['required', 'in:Day,Hour,Month']
            ];

            $ruleArray = [...$ruleArray, ...$rules];
        }

        if (count($this->perks['required'] ?? [])) {
            $perksOptionalRule = [];
            foreach ($this->perks['required'] as $key => $allowance) {
                foreach ($allowance as $name => $value) {
                    $ruleProperty = "perks.required.{$key}.{$name}";
                    $rule = ['required', 'numeric', 'min:0'];
                    $perksOptionalRule[$ruleProperty] = $rule;
                }
            }
            $ruleArray = [...$ruleArray, ...$perksOptionalRule];
        }

        if (fedexHrm()) {
            $rules = [
                "non_cash_deduction" => ['nullable', 'min:0'],
            ];

            $ruleArray = [...$ruleArray, ...$rules];
        }

        if (app(PayslipRepository::class)->needDojInhouse($this->employee_id, $this->enroll_status)) {
            $ruleArray['doj_inhouse'] = ['required'];
        }

        return $ruleArray;
    }

    public function validationAttributes()
    {
        $attributes = [
            'designation_id' => 'designation',
            'band_id' => 'band',
            'level_id' => 'level',
            'added_basic_salary' => 'band grade',
            'job_id' => 'job',
            'start_date_nep' => 'start date',
            'stop_date_nep' => 'stop date',
            'verifier_id' => 'approver',
        ];
        $perksAttribute = [];

        if (count($this->perks['required'] ?? [])) {
            foreach ($this->perks['required'] as $key => $allowance) {
                foreach ($allowance as $name => $value) {
                    $ruleProperty = "perks.required.{$key}.{$name}";
                    $perksAttribute[$ruleProperty] = strtolower($name);
                }
            }
        }

        return [...$attributes, ...$perksAttribute];
    }

    public function mount()
    {
        $this->singleSelectAttributes = ['payslip_approver', 'employee_list', 'payslip_employee_id'];
        $this->withTrashed = false;
        $this->sortBy = "created_at";
        $this->sortDirection = 'desc';
    }
    public function render()
    {
        return view('livewire.payroll.payslip-dashboard.includes.payslip-modal-form');
    }

    #[Computed(persist: true)]
    public function jobList()
    {
        if ($this->employee_department_id)
            return Department::find($this->employee_department_id)?->jobs()?->pluck('jobs.name', 'jobs.id')->toArray();
        return [];
    }

    #[Computed(persist: true)]
    public function designationsList()
    {
        if ($this->employee_department_id)
            return Department::findOrFail($this->employee_department_id)?->designations()->pluck('designations.title', 'designations.id')->toArray();
        return [];
    }

    #[Computed(persist: true)]
    public function positionTypesList()
    {
        return EmpStatus::where('is_active', true)->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function allowanceList()
    {
        return Perk::where('is_required', '=', '1')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceList()
    {
        return Perk::where('is_required', '=', '0')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceFormList()
    {
        $formData = [];
        foreach ($this->additionalAllowanceList as $allowance) {
            if (in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                array_push($formData, $allowance);
            }
        }
        return $formData;
    }

    #[Computed(persist: true)]
    public function designationLevel()
    {
        return Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', currentEmployee()->id ?? ''], ['status', 'Active']])
            ->value('designation.level');
    }

    #[Computed()]
    public function payslipList()
    {
        if ($this->getPayslipQuery())
            return $this->getPayslipQuery()
                ->orderBy($this->sortBy, $this->sortDirection ?? 'asc')
                ->paginate($this->perPage);

        return [];
    }

    public function getPayslipQuery()
    {
        $designationLevel = $this->designationLevel;
        $payslipRequestResult = PayslipRequest::with([
            'band:id,name',
            'level:id,name',
            'job',
            'grade_structure',
            'banded_grade_structure',
            'orgInfo' => function ($query) {
                $query->withTrashed()->select('employee_id', 'branch_id', 'department_id', 'unit_id', 'id', 'outsource_company_id');
            },
            'orgInfo.branch:id,name',
            'orgInfo.department:id,name',
            'orgInfo.unit:id,name',
            'orgInfo.outsource_company:id,name'
        ])
            ->leftJoin('employees as emp', 'emp.id', '=', 'payslip_requests.employee_id')
            ->leftJoin('companies', 'companies.id', '=', 'payslip_requests.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'emp.id')
            ->leftJoin('payslips', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
            ->leftJoin('payslip_perks_mapping as perks', 'perks.payslip_id', '=', 'payslips.id')
            ->leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->when($this->employeeType, function ($query) {
                if ($this->employeeType == "active")
                    $query->whereNull('org.termination_date');
                else
                    $query->whereNotNull('org.termination_date');
            })
            ->when($this->status, function ($query) {
                if ($this->status == WorkflowState::SUBMITTED)
                    $query->where('payslip_requests.state', $this->status);
                else
                    $query->where('payslips.status', $this->status);
            })
            ->when($this->enrollStatus, function ($query) {
                $query->where('payslip_requests.enroll_status', $this->enrollStatus);
            })
            ->when($this->paymentType, function ($query) {
                $query->where('payslip_requests.payment_type', $this->paymentType);
            })
            ->when($this->employee_id, function ($query) {
                $query->where('payslip_requests.employee_id', $this->employee_id);
            })
            ->when($this->company_id, function ($query) {
                $query->where('emp.company_id', $this->company_id);
            })
            ->when($this->region_id, function ($query) {
                $query->where('org.region_id', $this->region_id);
            })
            ->when($this->branch_id, function ($query) {
                $query->where('org.branch_id', $this->branch_id);
            })
            ->when($this->department_id, function ($query) {
                $query->where('org.department_id', $this->department_id);
            })
            ->when($this->unit_id, function ($query) {
                $query->where('org.unit_id', $this->unit_id);
            })
            ->when($this->expiringPayslip, function ($query) {
                if ($this->startDate <= $this->endDate)
                    return $query->where([
                        ['payslips.stop_date_nep', '>=', $this->startDate],
                        ['payslips.stop_date_nep', '<=', $this->endDate]
                    ])->where('payslips.status', 'Active');
            })
            ->when($this->expiredPayslip, function ($query) {
                if ($this->startDate <= $this->endDate)
                    return $query->where([
                        ['payslips.stop_date_nep', '>=', $this->startDate],
                        ['payslips.stop_date_nep', '<=', $this->endDate]
                    ])->where('payslips.status', 'Expired');
            })
            ->when($this->activePayslip, function ($query) {
                return $query->where('payslips.status', 'Active');
            })
            ->when($this->pendingPayslip, function ($query) {
                return $query->where('payslip_requests.state', WorkflowState::SUBMITTED);
            })
            ->when($this->terminatedEmployeePayslip, function ($query) {
                return $query->whereNotNull('org.termination_date')
                    ->whereNotNull('org.deleted_at')
                    ->whereRaw('payslips.id = (
                        SELECT p.id 
                        FROM payslips p
                        WHERE p.employee_id = emp.id
                        ORDER BY p.created_at DESC
                        LIMIT 1
                    )');
            })
            ->when(!auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP), function ($query) use ($designationLevel) {
                if ($designationLevel !== null) {
                    return $query->where(function ($q) use ($designationLevel) {
                        $q->whereNull('designation.level')
                            ->orWhere('designation.level', '<=', $designationLevel);
                    });
                }
                return $query->whereNull('designation.level');
            })
            ->select(
                'payslip_requests.id',
                'payslip_requests.company_id',
                'payslip_requests.employee_id',
                'payslip_requests.job_id',
                'payslip_requests.designation_id',
                'payslip_requests.band_id',
                'payslip_requests.pgrade_id',
                'payslip_requests.grade_structure_id',
                'payslip_requests.banded_grade_structure_id',
                'payslip_requests.allowance_metadata',
                'payslip_requests.enroll_status',
                'payslip_requests.employee_status_id',
                'payslip_requests.stipend',
                'payslip_requests.cit',
                'payslip_requests.insurance',
                'payslip_requests.medical_insurance',
                'payslip_requests.house_insurance',
                'payslip_requests.allow_ot',
                'payslip_requests.payment_type',
                'payslip_requests.non_cash_deduction',
                'payslip_requests.remarks',
                'payslip_requests.workflow',
                'payslip_requests.state',
                'payslip_requests.created_at',
                'payslip_requests.start_date_nep',
                'payslip_requests.stop_date_nep',
                'payslip_requests.start_date_eng',
                'payslip_requests.stop_date_eng',
                'payslips.status',
                'emp.gender',
                'emp.mstat',
                'org.deleted_at',
                DB::raw("CONCAT_WS(' ', emp.first_name, NULLIF(emp.middle_name,''), emp.last_name) as employee_name"),
                DB::raw("CONCAT(companies.code,'-',org.employee_code) as employee_code"),
                DB::raw('SUM(perks.amount) as total_perks_amount'),
                DB::raw('companies.name as company_name')
            )
            ->groupBy(
                'payslip_requests.id',
                'payslip_requests.company_id',
                'payslip_requests.employee_id',
                'payslip_requests.job_id',
                'payslip_requests.designation_id',
                'payslip_requests.band_id',
                'payslip_requests.pgrade_id',
                'payslip_requests.grade_structure_id',
                'payslip_requests.banded_grade_structure_id',
                'payslip_requests.allowance_metadata',
                'payslip_requests.enroll_status',
                'payslip_requests.employee_status_id',
                'payslip_requests.stipend',
                'payslip_requests.cit',
                'payslip_requests.insurance',
                'payslip_requests.medical_insurance',
                'payslip_requests.house_insurance',
                'payslip_requests.allow_ot',
                'payslip_requests.payment_type',
                'payslip_requests.non_cash_deduction',
                'payslip_requests.remarks',
                'payslip_requests.workflow',
                'payslip_requests.state',
                'payslip_requests.created_at',
                'payslip_requests.start_date_nep',
                'payslip_requests.stop_date_nep',
                'payslip_requests.start_date_eng',
                'payslip_requests.stop_date_eng',
                'payslips.status',
                'emp.gender',
                'emp.mstat',
                'org.deleted_at',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'companies.code',
                'org.employee_code',
                'companies.name',
            );

        $payslipRequestResult = filterEmployeesByScope($payslipRequestResult, 'org', 'emp');
        return $payslipRequestResult;
    }

    public function updated($attr)
    {
        if ($attr === 'enroll_status') {
            $this->reset('doj_inhouse');
            $this->showDojInhouse = app(PayslipRepository::class)->needDojInhouse($this->employee_id, $this->enroll_status);
        }

        if ($attr === 'designation_id' || $attr === 'band_id' || $attr === 'level_id') {
            $this->reset('newSalaryDetails');
            if ($attr == 'designation_id') {
                $this->reset('band_id');
                $this->reset('level_id');
            } elseif ($attr == 'band_id') {
                $this->reset('level_id');
            }
            $this->calculateSalaryDetail();
        } else if ($attr == "employee_id") {
            if ($this->employee_id) {
                $this->showPayslipForm = false;
                $this->resetFields();
                $result = $this->loadFormData($this->employee_id);
                if ($result) {
                    $this->updatedDesignationId();
                    $this->updatedBandId();
                    $this->calculateSalaryDetail();
                    $this->showPayslipForm = true;
                }
                $this->employee_name = $this->getEmployeeName($this->employee_id);
            }
            $unscoped_employee_list = Employee::list();
            $arr = [];
            foreach ($unscoped_employee_list as $item) {
                \array_push($arr, [
                    'value' => $item['id'],
                    'label' => "{$item['full_name']} ({$item['code']})",
                    'selected' => false
                ]);
            }

            $this->dispatch("updateMultiselectItem", ["id" => "employee-dropdown-payslip_approver", "items" => $arr]);
        }

        if ($attr === 'payslip_for') {
            $this->reset(['stipend', 'payment_frequency']);
        }
    }

    public function calculateSalaryDetail()
    {
        if ($this->designation_id && $this->band_id && $this->level_id) {
            $response = $this->getSalaryDetail($this->designation_id, $this->band_id, $this->level_id);
            if ($response['status']) {
                $this->salaryDetails = $response['data'];
            } else {
                $this->salaryDetails = [];
            }
        } else {
            $this->salaryDetails = [];
        }
    }

    public function updatedDesignationId()
    {
        $gradeStructure = GradeStructure::where('grade_id', $this->designation_id)->get();
        $bandIds = $gradeStructure->map(fn($structure) => $structure->band_id)->unique()->toArray();
        $this->bands = EmployeeBand::whereIn('id', $bandIds)->where('status', 1)->pluck('name', 'id')->toArray();
        $this->levels = [];
    }

    public function updatedBandId()
    {
        $gradeStructure = GradeStructure::where([
            ['grade_id', $this->designation_id],
            ['band_id', $this->band_id],
        ])->get();
        $levelIds = $gradeStructure->map(fn($structure) => $structure->pgrade_id)->unique()->toArray();
        $this->levels = EmployeePgrade::whereIn('id', $levelIds)->pluck('name', 'id')->toArray();
    }

    public function getSalaryDetail($designationId, $bandId, $levelId, $additional_basic_salary = 0)
    {
        // Get grade structure
        $gradeStructure = GradeStructure::where([
            ['grade_id', $designationId],
            ['band_id', $bandId],
            ['pgrade_id', $levelId]
        ])->first();

        // Check if grade structure exists
        if (!$gradeStructure) {
            return ["status" => false, "data" => "Grade structure not found for given band and designation"];
        }
        $salaryDetails = [
            "basic_salary"  => $gradeStructure->basic_salary,
            "pf"            => $gradeStructure->pf,
            "ssf"           => $gradeStructure->ssf,
            "allowance"     => $gradeStructure->allowance,
            "gross_salary"  => $gradeStructure->gross_salary,
            "gratuity"      => $gradeStructure->gratuity,
            "ctc"           => $gradeStructure->ctc,
        ];
        if ($additional_basic_salary > 0) {
            $salaryDetails["basic_salary"] += $additional_basic_salary;
            $salaryDetails["ssf"] = $salaryDetails["basic_salary"] * Constant::SSF_167;
            $salaryDetails["gratuity"] = $salaryDetails["basic_salary"] * Constant::SSF_833;
            $salaryDetails["pf"] = $salaryDetails["basic_salary"] * Constant::SSF_10;
            $salaryDetails["gross_salary"] = $salaryDetails["basic_salary"] + $salaryDetails["ssf"] + $salaryDetails["gratuity"] + $salaryDetails["pf"] + $salaryDetails['allowance'];
            $salaryDetails["ctc"] = $salaryDetails["gross_salary"];
        }

        // prepare salary_metadata
        return [
            "status" => true,
            "data" => $salaryDetails,
        ];
    }

    public function resetFields()
    {
        $this->dispatch("toggle-verifier-id", ['']);
        $this->reset(['selectedAdditionalAllowance', 'showPayslipForm', 'existingDocuments', 'removingDocumentIds', 'bands', 'levels', 'salaryDetails', 'newSalaryDetails']);
        unset($this->additionalAllowanceFormList);
        $this->resetFormField();
        $this->resetErrorBag();
    }

    public function save()
    {
        $this->setAdditionalAllowance();
        $this->validate();

        $payslipRequestTicket = PayslipRequest::where('employee_id', $this->employee_id)->whereNotIn("state", ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL))->get();
        if (count($payslipRequestTicket)) {
            $this->notify("A payslip has already been requested.")->type("error")->duration(5)->send();
            return;
        }

        $payslipApprovalRepository = new PayslipApprovalRepository();

        $data = [
            'verifier_id'           => $this->verifier_id,
            'employee_id'           => $this->employee_id,
            'company_id'            => $this->company,
            'job_id'                => $this->job_id,
            'designation_id'        => $this->designation_id,
            'band_id'               => $this->band_id,
            'level_id'              => $this->level_id,
            'added_basic_salary'    => $this->added_basic_salary,
            'allowance_metadata'    => $this->additionalAllowance,
            'non_cash_deduction'    => $this->non_cash_deduction,
            'enroll_status'         => $this->enroll_status,
            'doj_inhouse'           => $this->doj_inhouse,
            'employee_status_id'    => $this->employee_status,
            'stipend'               => $this->stipend ? $this->stipend : 0,
            'cit'                   => $this->cit ? $this->cit : 0,
            'insurance'             => $this->insurance ? $this->insurance : 0,
            'insurance_expiry_date' => $this->insurance_expiry_date ? $this->insurance_expiry_date : null,
            'medical_insurance'     => $this->medical_insurance ? $this->medical_insurance : 0,
            'medical_insurance_expiry_date' => $this->medical_insurance_expiry_date ? $this->medical_insurance_expiry_date : null,
            'house_insurance'       => $this->house_insurance ? $this->house_insurance : 0,
            'house_insurance_expiry_date' => $this->house_insurance_expiry_date ? $this->house_insurance_expiry_date : null,
            'allow_ot'              => $this->allow_ot,
            'payment_type'          => $this->payment_type,
            'payslip_type'          => $this->payslip_type,
            'payslip_for'           => $this->payslip_for,
            'payment_frequency'     => $this->payment_frequency,
            'payslip_remarks'       => $this->payslip_remarks,
            'start_date_eng'        => $this->start_date_nep,
            'stop_date_eng'         => $this->stop_date_nep,
            'start_date_nep'        => $this->start_date_nep,
            'stop_date_nep'         => $this->stop_date_nep,
            'documents'             => $this->documents
        ];

        $payslipResult = $payslipApprovalRepository->createPayslip($data);
        if ($payslipResult['status']) {
            $this->notify($payslipResult['message'])->send();
            return redirect(route("payslipList"));
        } else {
            $this->notify($payslipResult['message'] ?? "Error creating payslip")->type("error")->send();
        }
    }

    public function resetEmployeeId()
    {
        $this->reset('employee_id');
    }

    public function resetFormField()
    {
        $this->reset(['company', 'employee_status', 'perks', 'documents']);
        $this->reset(['job_id', 'designation_id', 'band_id', 'added_basic_salary', 'level_id', 'enroll_status', 'payslip_for', 'stipend', 'payment_frequency', 'cit', 'insurance', 'insurance_expiry_date', 'medical_insurance', 'medical_insurance_expiry_date', 'house_insurance', 'house_insurance_expiry_date', 'allow_ot', 'payment_type', 'payslip_type', 'start_date_nep', 'stop_date_nep', 'payslip_remarks', 'additionalAllowance', 'verifier_id']);
        $this->reset(['non_cash_deduction', 'showDojInhouse']);
        $this->resetErrorBag();
    }

    public function loadFormData(int|string $id)
    {
        $employeeDesignationLevel = Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['payslips.employee_id', $id], ['payslips.status', 'Active']])
            ->value('designation.level');
        if (($employeeDesignationLevel > $this->designationLevel) && !auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP)) {
            $this->notify("You are not authorized to create payslip for this employee.")->type("error")->duration(5)->send();
            return false;
        }

        $payslip_request = PayslipRequest::where('employee_id', $id)->whereNotIn("state", ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL))->first();
        if ($payslip_request) {
            $this->notify("A payslip has already been submitted.")->type("error")->duration(5)->send();
            return false;
        }

        $this->company = Employee::where('id', $id)->value('company_id');
        $this->employee_status = EmployeeOrg::where('employee_id', $id)->value('employee_status_id');
        $this->employee_department_id = EmployeeOrg::where('employee_id', $id)->value('department_id');
        unset($this->jobList, $this->designationsList);
        $payslip = Payslip::leftJoin('payslip_requests', 'payslips.payslip_request_id', '=', 'payslip_requests.id')
            ->with(['payslip_perks', 'payslip_perks.perks:id,name'])
            ->where([['payslips.employee_id', $id], ['status', 'Active']])
            ->select(
                'payslips.id',
                'payslips.company_id',
                'payslips.employee_id',
                'payslips.job_id',
                'payslips.designation_id',
                'payslips.band_id',
                'payslips.pgrade_id',
                'payslips.grade_structure_id',
                'payslips.banded_grade_structure_id',
                'payslips.added_basic_salary',
                'payslips.allowance_metadata',
                'payslips.enroll_status',
                'payslips.employee_status_id',
                'payslips.payslip_type',
                'payslips.added_basic_salary',
                'payslips.payslip_for',
                'payslips.payment_frequency',
                'payslips.stipend',
                'payslips.cit',
                'payslips.insurance',
                'payslips.insurance_expiry_date',
                'payslips.medical_insurance',
                'payslips.medical_insurance_expiry_date',
                'payslips.house_insurance',
                'payslips.house_insurance_expiry_date',
                'payslips.allow_ot',
                'payslips.payment_type',
                'payslips.start_date_nep',
                'payslips.stop_date_nep',
                'payslips.remarks',
                'status',
            )
            ->first();
        $this->loadDefaultAllowances();

        if (!$payslip) {
            $this->notify("Unable to find existing payslip. Please create a new one.")->type("info")->duration(4)->send();
            return true;
        }

        $this->added_basic_salary = $payslip->added_basic_salary;
        $this->payslip_for = $payslip->payslip_for;
        $this->payment_frequency = $payslip->payment_frequency;
        $this->stipend = $payslip->stipend;
        $this->job_id = $payslip->job_id;
        $this->designation_id = $payslip->designation_id;
        $this->band_id = $payslip->band_id;
        $this->level_id = $payslip->pgrade_id;
        $this->added_basic_salary = $payslip->added_basic_salary;
        $this->enroll_status = $payslip->enroll_status;
        $this->cit = $payslip->cit;
        $this->insurance = $payslip->insurance;
        $this->insurance_expiry_date = $payslip->insurance_expiry_date;
        $this->medical_insurance = $payslip->medical_insurance;
        $this->medical_insurance_expiry_date = $payslip->medical_insurance_expiry_date;
        $this->house_insurance = $payslip->house_insurance;
        $this->house_insurance_expiry_date = $payslip->house_insurance_expiry_date;
        $this->allow_ot = $payslip->allow_ot;
        $this->payment_type = $payslip->payment_type;
        $this->payslip_type = $payslip->payslip_type;
        $this->start_date_nep = $payslip->start_date_nep;
        $this->stop_date_nep = $payslip->stop_date_nep;
        $this->payslip_remarks = $payslip->payslip_remarks;
        $this->verifier_id = TransitionPerformer::where([
            ["workflow", WorkflowName::PAYSLIP_APPROVAL],
            ["state", WorkflowPerformer::APPROVER],
            ["recipient_id", $id],
            ["level", 1]
        ])->value('performer_id');
        $this->dispatch("toggle-verifier-id", [$this->verifier_id]);

        $this->existingDocuments = $payslip->requestTicket?->getDocuments() ?? [];

        // load perks to form data
        foreach ($payslip->payslip_perks as $value) {
            foreach ($this->allowanceList as $allowance) {
                if ($value->perks_id === $allowance->id) {
                    $this->perks['required'] = [$allowance->id => [$value->perks->name => $value->amount]];
                }
            }
            foreach ($this->additionalAllowanceList as $allowance) {
                if ($value->perks_id === $allowance->id) {
                    $this->perks['optional'][$allowance->id] = [$value->perks->name => $value->amount];
                    array_push($this->selectedAdditionalAllowance, $allowance->id);
                }
            }
        }

        if (fedexHrm())
            $this->non_cash_deduction = $payslip->non_cash_deduction;

        unset($this->additionalAllowanceFormList);
        if ($payslip->added_basic_salary > 0) {
            $this->reCalculateSalaryDetails();
        }

        return true;
    }

    public function loadDefaultAllowances()
    {
        foreach ($this->allowanceList as $allowance) {
            $this->perks['required'] = [$allowance->id => [$allowance->name => $allowance->default_value]];
        }
        $this->loadDefaultAdditionalAllowances();
    }

    public function loadDefaultAdditionalAllowances()
    {
        $this->additionalAllowanceList
            ->filter(fn($allowance) => !in_array($allowance->id, $this->selectedAdditionalAllowance))
            ->each(function ($allowance) {
                $this->perks['optional'][$allowance->id] = [$allowance->name => $allowance->default_value];
            });
    }

    public function reCalculateSalaryDetails()
    {
        $this->validate([
            'added_basic_salary' => 'required|numeric|min:0',
        ]);
        if ($this->added_basic_salary == 0) {
            $this->newSalaryDetails = [];
        } else if ($this->designation_id && $this->band_id && $this->level_id) {
            $response = $this->getSalaryDetail($this->designation_id, $this->band_id, $this->level_id, $this->added_basic_salary);
            if ($response['status']) {
                $this->newSalaryDetails = $response['data'];
            } else {
                $this->newSalaryDetails = [];
            }
        } else {
            $this->newSalaryDetails = [];
        }
    }

    public function setAdditionalAllowance()
    {
        $perksKeyAndAmount = [];

        if (!$this->perks) {
            return;
        }

        foreach ($this->perks as $perksArray) {
            foreach ($perksArray as $key => $value) {
                if ($this->additionalAllowanceList->filter(fn($allowance) => $allowance->id == $key)->isNotEmpty()) {
                    if (in_array($key, $this->selectedAdditionalAllowance)) {
                        $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                    }
                } else {
                    $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                }
            }
        }
        $this->additionalAllowance = $perksKeyAndAmount;
    }

    public function preparePreviewData()
    {
        $this->setAdditionalAllowance();

        $gradeStructure = GradeStructure::where([
            ['grade_id', $this->designation_id],
            ['band_id', $this->band_id],
            ['pgrade_id', $this->level_id],
        ])->first();

        $total = $gradeStructure?->ctc;
        $allowanceTotal = 0;
        foreach ($this->additionalAllowance as $allowance) {
            foreach ($allowance as $value) {
                $allowanceTotal += (float) $value;
            }
        }
        $new_ctc = $total + $allowanceTotal - ($this->non_cash_deduction ?? 0);

        $this->previewDetail = [
            'employee_name' => $this->getEmployeeName($this->employee_id),
            'employee_code' => $this->getEmployeeCode($this->employee_id),
            'job' => $this->jobList()[$this->job_id] ?? "N/A",
            'designation' => $this->designationsList()[$this->designation_id] ?? "N/A",
            'grade' => ($this->bandList[$this->band_id] ?? '') . ($this->levelList[$this->level_id] ?? ''),
            'enroll_status' => $this->enroll_status,
            'employee_status' => $this->positionTypesList()[$this->employee_status] ?? "N/A",
            'stipend' => $this->stipend,
            'ot_allowed' => $this->allow_ot ? 'Yes' : 'No',
            'payment_type' => $this->payment_type,
            'payslip_type' => $this->payslip_type,
            'payslip_for' => $this->payslip_for == 'Full_time' ? 'Regular' : $this->payslip_for,
            'payment_frequency' => $this->payment_frequency ?? 'N/A',
            'insurance_expiry_date' => $this->insurance_expiry_date ?? '',
            'medical_insurance_expiry_date' => $this->medical_insurance_expiry_date ?? '',
            'house_insurance_expiry_date' => $this->house_insurance_expiry_date ?? '',
            'verifier' => $this->getEmployeeName($this->verifier_id),
            'remarks' => $this->payslip_remarks,
            'start_date_nep' => $this->start_date_nep,
            'stop_date_nep' => $this->stop_date_nep,
            'start_date_eng' => LaravelNepaliDate::from($this->start_date_nep)->toEnglishDate(),
            'stop_date_eng' => LaravelNepaliDate::from($this->stop_date_nep)->toEnglishDate(),
            'original_basic_salary' => $gradeStructure->basic_salary,
            'added_basic_salary' => $this->added_basic_salary,
            'total_basic_salary' => $gradeStructure->basic_salary + $this->added_basic_salary ?? 0,
            'pf' => $gradeStructure->pf,
            'ssf' => $gradeStructure->ssf,
            'allowance' => $gradeStructure->allowance,
            'gratuity' => $gradeStructure->gratuity,
            'ctc' => $gradeStructure->ctc,
            'allowance_detail' => $this->additionalAllowance,
            'non_cash_deduction' => $this->non_cash_deduction ?? '0',
            'new_ctc' => $new_ctc,
            'cit' => $this->cit ?? '0',
            'insurance' => $this->insurance ?? '0',
            'medical_insurance' => $this->medical_insurance ?? '0',
            'house_insurance' => $this->house_insurance ?? '0',
        ];
    }

    private function getEmployeeName($id)
    {
        return Employee::where('id', $id)
            ->pluck(DB::raw("CONCAT_WS(' ',first_name,IFNULL(middle_name,''),last_name) as name"))
            ->first();
    }

    private function getEmployeeCode($id)
    {
        return Employee::where('employees.id', $id)
            ->leftJoin('companies', 'companies.id', '=', 'employees.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->pluck(DB::raw("CONCAT(companies.code,'-',org.employee_code) as code"))
            ->first();
    }


    public function updatedSelectedAdditionalAllowance()
    {
        unset($this->additionalAllowanceFormList);
        $this->loadDefaultAdditionalAllowances();
        $this->unsetAdditionalAllowanceErrors();
    }

    public function unsetAdditionalAllowanceErrors()
    {
        foreach ($this->allowanceList as $allowance) {
            if (!in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                $this->resetValidation("perks.required.{$allowance->id}.{$allowance->name}");
            }
        }

        foreach ($this->additionalAllowanceList as $allowance) {
            if (!in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                $this->resetValidation("perks.optional.{$allowance->id}.{$allowance->name}");
            }
        }
    }

    #[Computed(persist: true)]
    public function bandList()
    {
        return EmployeeBand::get()->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function levelList()
    {
        return EmployeePgrade::get()->pluck("name", "id")->toArray();
    }

    #[On('hide.bs.modal')]
    public function resetPreviewData()
    {
        $this->reset('previewDetail');
    }

    public function previewPayslip()
    {
        $this->validate();
        $this->preparePreviewData();
        $this->dispatch('show-payslip-detail');
    }
}
