<?php

namespace App\Livewire\Payroll\PayslipDashboard;

use App\Models\configs\Company;
use App\Models\Employee\Employee;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('No Payslips')]
class NoPayslipList extends Component
{
    use WithDataTable;
    public $company = null;

    public function mount()
    {
        $this->setExcelOptions();
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = Employee::leftJoin('payslips', 'employees.id', '=', 'payslips.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin('companies as company', 'company.id', 'employees.company_id')
            ->leftJoin('branches as branch', 'branch.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->leftJoin('emp_statuses as status', 'status.id', 'org.employee_status_id')
            ->leftJoin('payslip_requests', 'employees.id', '=', 'payslip_requests.employee_id')
            ->where(function ($query) {
                $query->whereNull('payslips.employee_id')
                    ->whereNull('payslip_requests.employee_id');
            })
            ->when($this->company, function ($query) {
                $query->where("employees.company_id", $this->company);
            })
            // ->orWhereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('payslips as expired_payslips')
            //         ->whereColumn('expired_payslips.employee_id', 'employees.id')
            //         ->where('expired_payslips.status', 'Active');
            // })
            ->whereNull('payslips.deleted_at')
            ->whereNull('payslip_requests.deleted_at')
            ->search($this->search);
        if (!scopeAll()) {
            $query = filterEmployeesByScope($query, 'org');
        }

        return $this->applySorting($query->select(
            'employees.id as id',
            'company.name as company',
            DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
            DB::raw("CONCAT(company.code, '-', org.employee_code) as emp_code"),
            'dept.name as department',
            'branch.name as branch',
            'status.name as emp_status',
        ))
            ->distinct('employees.id')
            ->paginate($this->perPage);
    }
    public function updatedCompany($value)
    {
        $this->company = $value ?: null; 
        $this->resetPage(); 
    }


    public function setExcelOptions()
    {
        $this->exportFileName = "No Payslips Employee List.xlsx";
        $this->exportIgnoreColumns = ['id'];
        // $this->exportColumnHeadersMap = [
        //     'full_name' => 'Employee Name',
        //     'emp_code' => 'Employee Code',
        //     'email' => 'Email',
        //     'phone' => 'Phone',
        //     'org_email' => 'Organization Email',
        //     'cug' => 'CUG',
        //     'emp_status' => 'Status',
        //     'branch_name' => 'Branch',
        //     'dep_name' => "Department",
        // ];
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return \App\Models\configs\Company::orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return \App\Models\configs\Department::orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return \App\Models\configs\Branch::orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.payroll.payslip-dashboard.no-payslip-list');
    }
}
