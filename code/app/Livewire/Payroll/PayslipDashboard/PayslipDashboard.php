<?php

namespace App\Livewire\Payroll\PayslipDashboard;

use App\Http\Repositories\PayslipRepository;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Payslip Dashboard')]
class PayslipDashboard extends Component
{
    public $payslipDetails = [];

    private PayslipRepository $repo;

    public function __construct()
    {
        $this->repo = new PayslipRepository;
    }

    public function mount()
    {
        $this->payslipDetails = [
            'Active Payslips' => [
                'count' => $this->repo->getActivePayslipsCount(),
                'link' => route('payslipList') . '?active=' . true,
            ],
            'Pending Payslips' => [
                'count' => $this->repo->getPendingPayslipRequestCount(),
                'link' => route('payslipList') . '?pending=' . true,
            ],
            'Expiring Payslips' => [
                'count' => $this->repo->getExpiringPayslipCount(),
                'link' => route('payslipList') . '?expiring=' . true,
                'description' => "Upcoming 30 days count"
            ],
            'Expired Payslips' => [
                'count' => $this->repo->getExpiredPayslipsCount(),
                'link' => route('payslipList') . '?expired=' . true,
                'description' => "Past 30 days count"
            ],
            'No Payslips' => [
                'count' => $this->repo->getEmployeeWithNoPayslipsCount(),
                'link' => route('noPayslipList'),
            ],
            'Terminated Employee Payslips' => [
                'count' => $this->repo->getTerminatedEmployeePayslipCount(),
                'link' => route('payslipList') . '?terminatedEmployee='. true,
                'description' => "Terminated employees count"
            ]
        ];
    }

    public function render()
    {
        return view('livewire.payroll.payslip-dashboard.payslip-dashboard');
    }
}
