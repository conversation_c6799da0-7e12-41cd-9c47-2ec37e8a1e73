<?php

namespace App\Livewire\Report;

use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\CarbonImmutable;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Employee Login Status Report')]
class EmployeeLoginStatusReport extends Component
{
    use WithDefaultFilter, WithDataTable, MultiselectEmployeeSearch;

    public $filterCompanyId;
    public $filterBranchId;
    public $filterRegionId;
    public $filterDepartmentId;

    public $filterLastLoginDays = 1;
    public $filterStatus = 'all'; // all | active | inactive | never

    public function mount()
    {
        $this->sortBy = 'last_login';
        $this->sortDirection = 'desc';
    }


    #[Computed]
    public function lastLoginActivityOptions()
    {
        return [
            '1'   => 'Today',  
            '7'   => 'Last 7 Days',
            '15'  => 'Last 15 Days',
            '30'  => 'Last 1 Month',
            '90'  => 'Last 3 Months',
            '180' => 'Last 6 Months',
        ];
    }


    /** Resolve the window date once */
    protected function windowDate(): ?string
    {
        $days = (int) ($this->filterLastLoginDays ?? 0);
        return $days > 0 ? CarbonImmutable::now()->subDays($days)->toDateTimeString() : null;
    }

    protected function baseQuery(): Builder
    {

        $query = Employee::query()
            ->leftJoin('users', 'employees.user_id', '=', 'users.id')
            ->leftJoin('companies as comp', 'employees.company_id', '=', 'comp.id')
            ->leftJoin('employee_org as org',  'org.employee_id', '=', 'employees.id')
            ->leftJoin('branches as br', 'org.branch_id', '=', 'br.id')
            ->leftJoin('departments as dept', 'org.department_id', '=', 'dept.id')
            ->leftJoin('regions as region', 'org.region_id', '=', 'region.id')
            ->when($this->filterCompanyId, fn($q) => $q->where('comp.id', $this->filterCompanyId))
            ->when($this->filterBranchId, fn($q) => $q->where('br.id', $this->filterBranchId))
            ->when($this->filterRegionId, fn($q) => $q->where('region.id', $this->filterRegionId))
            ->when($this->filterDepartmentId, fn($q) => $q->where('dept.id', $this->filterDepartmentId));
        
        $query = filterEmployeesByScope($query, 'org');
        return $query;
    }

    public function listQuery(): Builder
    {
        $q = $this->baseQuery();
        $window = $this->windowDate();

        switch ($this->filterStatus) {
            case 'active':
                $q->whereNotNull('users.last_login');
                if ($window) {
                    $q->where('users.last_login', '>=', $window);
                }
                break;

            case 'inactive':
                // "inactive" = logged in before but not inside window
                if ($window) {
                    $q->whereNotNull('users.last_login')
                        ->where('users.last_login', '<', $window);
                } else {
                    // No window => treat as "has logged in at least once"
                    $q->whereNotNull('users.last_login');
                }
                break;

            case 'never':
                $q->whereNull('users.last_login');
                break;

            default: // 'all'
                if ($window) {
                    $q->where(function ($qq) use ($window) {
                        $qq->where('users.last_login', '>=', $window)
                            ->orWhereNull('users.last_login');
                    });
                }
                break;
        }

        return $q->select([
            'employees.id',
            Employee::selectNameRawQuery(),
            'users.last_login',
            DB::raw("CONCAT_WS('-', comp.code, org.employee_code) as emp_code"),
            'br.name as branch_name',
            'dept.name as dept_name',
        ]);
    }

    #[Computed]
    public function list()
    {
        return $this->applySorting($this->listQuery())->paginate($this->perPage);
    }

    #[Computed]
    public function kpis(): array
    {
        $base   = $this->baseQuery();
        $window = $this->windowDate();

        $total   = (clone $base)->distinct('employees.id')->count('employees.id');

        $activeQ = (clone $base)->whereNotNull('users.last_login');
        if ($window) {
            $activeQ->where('users.last_login', '>=', $window);
        }
        $active = $activeQ->distinct('employees.id')->count('employees.id');

        $never  = (clone $base)->whereNull('users.last_login')
            ->distinct('employees.id')->count('employees.id');

        // inactive = everything else (ensures partition and avoids extra scan / edge cases)
        $inactive = max(0, $total - $active - $never);

        return [
            'total'    => $total,
            'active'   => $active,
            'inactive' => $inactive,
            'never'    => $never,
            'window'   => $window ? $this->filterLastLoginDays . ' days' : 'All time',
        ];
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);

        if (in_array($attr, [
            'filterCompanyId',
            'filterBranchId',
            'filterRegionId',
            'filterDepartmentId',
            'filterLastLoginDays',
            'filterStatus',
        ], true)) {
            $this->resetPage();
        }

        if (in_array($attr, [
            'filterCompanyId',
            'filterBranchId',
            'filterRegionId',
            'filterDepartmentId',
            'filterLastLoginDays',
        ], true)) {
            $this->dispatch('kpis-updated', kpis: $this->kpis);
        }
    }

    public function clear()
    {
        $this->reset([
            'filterBranchId',
            'filterRegionId',
            'filterDepartmentId',
            'filterCompanyId',
        ]);
        $this->filterLastLoginDays = 7;
        $this->filterStatus = 'all';

        $this->resetPage();
        $this->refreshEmployeeList();
    }


    public function render()
    {
        return view('livewire.report.employee-login-status-report');
    }
}
