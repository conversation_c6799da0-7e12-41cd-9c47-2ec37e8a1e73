<?php

namespace App\Livewire\Report;

use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Report Page')]
class ReportPage extends Component
{
    public array $attendanceReport = [];
    public array $otReport = [];

    public function mount()
    {
        $this->attendanceReport = [
            [
                'title' => 'Employee Daily Attendance Report',
                'link'  =>  route('employeeAttendanceStatusReport'),
                'icon'  =>  "calendar-day",
                'permission'    =>  PermissionList::REPORT_EMPLOYEE_DAILY_ATTENDANCE,
            ],
            // [
            //     'title' => 'Employee Daily Attendance Report',
            //     'link' => route('employeeDailyAttendanceReport'),
            //     'icon'  => "list",
            //     'permission' => PermissionList::REPORT_EMPLOYEE_DAILY_ATTENDANCE,
            // ],
            /*[
                'title' => 'Employee Monthly Attendance Report',
                'link' => route('employeeMonthlyAttendanceReport'),
                'icon'  => "calendar-month",
                'permission' => PermissionList::REPORT_EMPLOYEE_MONTHLY_ATTENDANCE,
            ],*/
            [
                'title' => 'Employee Monthly Attendance Report',
                'link' => route('employeeTardinessReport'),
                'icon' => "clock",
                'permission' => PermissionList::REPORT_EMPLOYEE_TARDINESS_VIEW,
            ],
            [
                'title' => 'Employee Yearly Attendance Report',
                'link' => route('employeeYearlyAttendanceReport'),
                'icon'  => "bar-chart",
                'permission' => PermissionList::REPORT_EMPLOYEE_YEARLY_ATTENDANCE,
            ],
            [
                'title' => 'Consecutive Attendance Report',
                'link' => route('consecutive-attendance-issue'),
                'icon' => 'arrow-repeat',
                'permission' => '',
            ],
            [
                'title' => 'Monthly Attendance Summary',
                'link'  => route('monthlyAttendanceSummary'),
                'icon'  => 'calendar-day',
                'permission' => PermissionList::MONTHLY_ATTENDANCE_SUMMARY_VIEW,
            ]
        ];

        $this->otReport = [
            [
                'title' => 'OT Attendance Report',
                'link' => route('otAttendanceReport'),
                'icon' => "clock",
                'permission' => PermissionList::OT_ATTENDANCE_REPORT,
            ],
            [
                'title' => 'OT Salary Report',
                'link' => route('otSalaryReport'),
                'icon' => "cash-stack",
                'permission' => PermissionList::OT_SALARY_REPORT,
            ],
            [
                'title' => 'OT Employee Report',
                'link' => route('otEmployeeReport'),
                'icon' => "person-fill-gear",
                'permission' => PermissionList::OT_EMPLOYEE_ATEENDANCE_REPORT,
            ],
        ];
    }

    public function render()
    {
        return view('livewire.report.report-page');
    }
}
