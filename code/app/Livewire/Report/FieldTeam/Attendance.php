<?php

namespace App\Livewire\Report\FieldTeam;

use App\Http\Repositories\FieldTeam\Report\Interfaces\FieldTeamAttendanceRepositoryInterface;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithPagination;

#[Title('Field Team Attendance Report')]
class Attendance extends Component
{
    use WithPagination, WithDataTable, MultiselectEmployeeSearch;

    public $startDate, $endDate, $filterApply = false, $team_id;
    public $employee_id = "";

    protected FieldTeamAttendanceRepositoryInterface $attendanceRepo;

    public function boot(FieldTeamAttendanceRepositoryInterface $attendanceRepo)
    {
        $this->attendanceRepo = $attendanceRepo;
    }

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_id'];
        $this->initialFilters();
    }

    public function initialFilters()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $this->startDate = "{$currentYear}-{$currentMonth}-01";
        $this->endDate = $nepaliDate;
    }

    #[Computed(persist: true)]
    public function teams()
    {
        return $this->attendanceRepo->getTeamTypes();
    }

    public function listQuery()
    {
        if (!$this->filterApply) {
            return collect();
        }

        $filters = [
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'team_id' => $this->team_id,
            'employee_id' => $this->employee_id,
        ];

        if ($this->employee_id || $this->team_id) {
            return $this->attendanceRepo->getDetailedAttendance($filters);
        } else {
            return $this->attendanceRepo->getSummaryAttendance($filters);
        }
    }

    public function getPaginatedList()
    {
        if (!$this->filterApply) {
            return collect();
        }

        $filters = [
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'team_id' => $this->team_id,
            'employee_id' => $this->employee_id,
        ];

        if ($this->employee_id || $this->team_id) {
            return $this->attendanceRepo->getPaginatedDetailedAttendance($filters, $this->perPage);
        } else {
            return $this->attendanceRepo->getPaginatedSummaryAttendance($filters, $this->perPage);
        }
    }

    public function applyFilter()
    {
        $this->filterApply = true;
        $this->resetPage(); // Reset to first page when filters are applied
        unset($this->list);
    }

    public function updated($attr)
    {
        if (in_array($attr, ['startDate', 'endDate', 'employee_id', 'team_id'])) {
            $this->filterApply = false;
            $this->resetPage(); // Reset pagination when filters change
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->getPaginatedList();
    }

    // Add method to handle per page changes
    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function export($exportAll = false)
    {
        $emailTo = currentEmployee()?->organizationInfo?->email;

        if (empty($emailTo)) {
            $this->notify("Cannot send OT report, no recipient email found for employee.")
                ->type('error')->send();
            logError("Cannot send OT report, no recipient email found for employee.");
            return;
        }

        // Validate email format
        if (!filter_var($emailTo, FILTER_VALIDATE_EMAIL)) {
            $this->notify("Invalid email address format. Please update your email address.")
                ->type('error')->send();
            logError("Invalid email address format for employee", ['email' => $emailTo]);
            return;
        }

        try {
            $filters = [
                'start_date' => $this->startDate,
                'end_date' => $this->endDate,
                'employee_id' => $this->employee_id,
                'team_id' => $this->team_id,
                'is_detailed' => !empty($this->employee_id),
                'export_type' => $exportAll ? 'all' : 'paginated',
                'per_page' => $this->perPage,
                'current_page' => $this->getPage(),
            ];

            $startDateFormatted = Carbon::createFromFormat('Y-m-d', $this->startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::createFromFormat('Y-m-d', $this->endDate)->format('Y-m-d');

            $reportType = !empty($this->employee_id) ? 'Detailed' : 'Summary';
            $exportScope = $exportAll ? 'All Data' : 'Current Page';
            $title = "Field Team OT Attendance {$reportType} Report - {$exportScope} ($startDateFormatted to $endDateFormatted)";

            dispatch(new \App\Jobs\OtReportJob(
                title: $title,
                reportType: 'field_team_attendance',
                isVariance: false,
                companyId: currentEmployee()?->company_id ?: 1,
                emailTo: $emailTo,
                filters: $filters,
            ));

            $this->notify("Field Team OT Attendance Report ({$exportScope}) has been queued for export and will be sent to your email.")->send();
        } catch (\Throwable $th) {
            $this->notify("Error while queuing export job: " . $th->getMessage())->type('error')->send();
            logError("Error in Field Team Attendance export: " . $th->getMessage());
        }
    }

    /**
     * Get current page for pagination
     */
    public function getPage()
    {
        return $this->getPaginatedList()->currentPage();
    }

    public function render()
    {
        return view('livewire.report.field-team.attendance', [
            'attendanceList' => $this->getPaginatedList(),
        ]);
    }
}
