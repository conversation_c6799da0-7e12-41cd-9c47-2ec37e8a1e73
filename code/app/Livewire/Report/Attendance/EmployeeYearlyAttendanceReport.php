<?php

namespace App\Livewire\Report\Attendance;

use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Models\configs\FiscalYear;
use App\Models\Leaves\EmployeeAttendanceMonthlyDetail as mEmployeeAttendanceMonthlyDetail;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title("Employee Yearly Attendance Report")]
class EmployeeYearlyAttendanceReport extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    #[Validate('required|exists:employees,id')]
    public $employee_id;

    #[Validate('required|exists:fiscal_years,id')]
    public $fiscalYearId;
    public $employeeId = "";
    public $filtered = false;

    public $monthList = [];

    public $leaveDetailHeaders = [];

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_id'];
        $this->withTrashed = true;
        $this->monthList = (new FacadesLaravelNepaliDate)->nep_months();
        // dd($this->monthList[1]);
        $this->fiscalYearId = FiscalYear::activeFiscalYearId();

        $this->setPermissionForEmployeeDropdown();
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->applySorting(
            mEmployeeAttendanceMonthlyDetail::where([
                ['fiscal_year_id', $this->fiscalYearId],
                ['employee_id', $this->employeeId],
            ])
        )->orderBy('year', 'asc')->orderBy('month', 'asc')->get();
    }
    public function validationAttributes()
    {
        return [
            'employee_id' => 'employee name'

        ];
    }

    public function filter()
    {
        $this->validate();
        $this->employeeId = $this->employee_id;
        $this->filtered = true;
        unset($this->list);
        foreach ($this->list as $item) {
            $this->leaveDetailHeaders = array_merge($this->leaveDetailHeaders, array_keys($item->leaveDetail));
            // dump($item->leaveDetail);
        }
        $this->leaveDetailHeaders = array_unique($this->leaveDetailHeaders);
    }

    public function export()
    {
        $fiscalYear = FiscalYear::find($this->fiscalYearId);
        $title = "Yearly Attendance Report";

        // if ($fiscalYear)
        //     $title .= "Yearly Attendance Report";

        $employee = \App\Models\Employee\Employee::find($this->employeeId);
        if ($employee) $title .= " ($employee->name)";

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\YearlyAttendanceReport($this->list, $this->leaveDetailHeaders, $title),
            $title . '.xlsx'
        );
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.report.attendance.employee-yearly-attendance-report');
    }
}
