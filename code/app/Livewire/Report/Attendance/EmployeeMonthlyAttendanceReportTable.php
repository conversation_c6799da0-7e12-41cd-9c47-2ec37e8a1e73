<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Helpers\Constant;
use App\Models\configs\LateInCategory;
use App\Models\Employee\Employee;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Modelable;
use PowerComponents\LivewirePowerGrid\Column;
use PowerComponents\LivewirePowerGrid\Exportable;
use PowerComponents\LivewirePowerGrid\Footer;
use PowerComponents\LivewirePowerGrid\Header;
use PowerComponents\LivewirePowerGrid\PowerGrid;
use PowerComponents\LivewirePowerGrid\PowerGridFields;
use PowerComponents\LivewirePowerGrid\PowerGridComponent;
use PowerComponents\LivewirePowerGrid\Traits\WithExport;

final class EmployeeMonthlyAttendanceReportTable extends PowerGridComponent
{
    use WithExport;

    public string $tableName = 'Employee Monthly Attendance Report Table';

    public bool $showFilters = true;
    // public bool $deferLoading = true;

    public string $checkboxAttribute = 'employees.id';
    public string $radioAttribute = 'employees.id';

    public string $primaryKey = 'employees.id';
    public string $sortField = 'employees.first_name';
    public string $sortDirection = 'asc';
    // public bool $showExporting = true;

    public array $perPageValues = [0, 10, 25, 50, 100];

    public $remarkCategories;

    #[Modelable]
    public $customFilters = [
        'employees' => [],
        'branch' => [],
        'department' => [],
        'year' => '',
        'month' => '',
    ];

    public function boot()
    {
        $this->resetPage();
    }

    public function updatedCustomFilters()
    {
        $this->fillData();
    }

    public function setUp(): array
    {
        $this->remarkCategories = LateInCategory::where('is_active', true)->pluck('name')->toArray() ?? [];
        $this->showCheckBox('id');
        $month = Constant::NEPALI_MONTH_LIST[$this->customFilters['month']];

        return [
            Exportable::make("Employee Monthly Attendance Report for {$month} {$this->customFilters['year']}")
                ->type(Exportable::TYPE_XLS, Exportable::TYPE_CSV)
            // ->queues(1)
            // ->onQueue("reports")
            // ->onConnection('redis')
            ,
            Header::make(),
            Footer::make()
                ->showPerPage(perPageValues: $this->perPageValues)
                ->showRecordCount(),
        ];
    }

    public function datasource()
    {
        $query = Employee::query()
            ->leftJoin('attendance as att', 'employees.id', '=', 'att.employee_id')
            ->leftJoin('leave_requests', 'leave_requests.id', '=', 'att.leave_request_id')
            ->leftJoin('leave_options', 'leave_options.id', '=', 'leave_requests.leave_option_id')
            ->leftJoin('leave_types', 'leave_types.id', '=', 'leave_requests.leave_type_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->leftJoin('departments as dept', 'org.department_id', '=', 'dept.id')
            ->leftJoin('branches as br', 'org.branch_id', '=', 'br.id')
            ->leftJoin('companies as company', 'employees.company_id', '=', 'company.id')
            ->when(count($this->customFilters['employees']), function ($query) {
                $query->whereIn('employees.id', $this->customFilters['employees']);
            })
            ->when($companyId = $this->customFilters['companyId'], fn($query) => $query->where('employees.company_id', $companyId))
            ->when($branchId = $this->customFilters['branch'], fn($query) => $query->where('br.id', $branchId))
            ->when($departmentId = $this->customFilters['department'], fn($query) => $query->where('dept.id', $departmentId))
            ->where('date_np', 'like', "{$this->customFilters['year']}-{$this->customFilters['month']}-%")
            ->whereNull('att.deleted_at')
            ->select(
                'employees.id',
                'dept.name as department',
                'br.name as branch'
            );

        if (!empty($this->remarkCategories)) {
            foreach ($this->remarkCategories as $category) {
                $alias = strtolower(str_replace(' ', '_', $category));
                $query->selectRaw(
                    "COUNT(CASE WHEN att.status LIKE ? THEN 1 END) AS {$alias}",
                    ['%' . $category . '%']
                );
            }
        } else {
            $query->selectRaw(
                "COUNT(CASE WHEN att.status LIKE ? THEN 1 END) AS late_in",
                ['%Late In%']
            );
        }
        // $query->selectRaw(DB::raw("CONCAT(employees.first_name, ' ', employees.middle_name, ' ', employees.last_name) as employee"));
        $query->selectRaw(Employee::selectNameRawQuery('employees', 'employee'));
        $query->selectRaw(Employee::selectEmpCodeRawQuery());
        $query->selectRaw("COUNT(CASE
        WHEN att.out_time IS NOT NULL AND att.duty_end IS NOT NULL
        AND STR_TO_DATE(att.out_time, '%r') < att.duty_end
        THEN 1
        END) AS early_out");
        $query->selectRaw("SUM(CASE 
        WHEN (att.leave_status = 1
        AND leave_requests.replaced_start_date IS NULL 
        AND leave_requests.replaced_end_date IS NULL
        AND leave_requests.state IN ('Approved', 'Assigned')
        ) THEN IFNULL(leave_options.num_days, 0)
        ELSE 0
        END
        ) AS leave_count");
        $query->selectRaw("SUM(CASE
                    WHEN att.leave_status = false
                        AND att.leave_request_id IS NOT NULL
                        AND leave_types.paid = 0
                        THEN IFNULL(leave_options.num_days, 0)
                    ELSE 0
                    END) AS total_unpaid_leave");
        $query->selectRaw("SUM(CASE 
                    WHEN (att.leave_status = 1 
                          AND leave_requests.replaced_start_date IS NOT NULL 
                          AND leave_requests.replaced_end_date IS NOT NULL 
                          AND leave_requests.state IN ('Approved', 'Assigned')
                    ) THEN IFNULL(leave_options.num_days, 0)
                    ELSE 0
                    END
                    ) AS substitue_leave_days");
        $query->selectRaw("COUNT(CASE
                WHEN att.missed_punch_status = 1
                    OR (att.in_time IS NOT NULL AND att.out_time IS NULL) 
                    OR (att.in_time IS NULL AND att.out_time IS NOT NULL)
                THEN 1
            END) AS missed_punch");
        $query->selectRaw("COUNT(CASE WHEN att.status LIKE 'On Holiday%' THEN 1 END) AS holiday");
        $query->selectRaw("COUNT(CASE WHEN att.status LIKE 'Work On Holiday%' THEN 1 END) AS work_on_holiday");
        $query->selectRaw("COUNT(CASE WHEN att.status LIKE 'Day Off%' THEN 1 END) AS week_end");
        $query->selectRaw("COUNT(CASE WHEN att.status LIKE 'Work On Day Off%' THEN 1 END) AS work_on_week_end");
        $query->selectRaw("SUM(
                CASE
                    WHEN att.leave_request_id IS NOT NULL
                        AND att.in_time IS NOT NULL 
                        AND att.out_time IS NOT NULL
                        AND leave_options.num_days < 1
                    THEN 1 - leave_options.num_days
                    WHEN att.in_time IS NOT NULL
                        AND att.out_time IS NOT NULL 
                        AND att.status NOT LIKE '%Work On Holiday%'
                        AND att.status NOT LIKE '%Work On Day Off%'
                    THEN 1
                    ELSE 0
                    END
                    ) AS present");
        $query->selectRaw("COUNT(CASE
                WHEN att.in_time IS NULL AND att.out_time IS NULL
                    AND att.leave_status = false
                    AND att.status = 'Absent' 
                THEN 1
            END) AS absent");

        $query->groupBy(['employees.id', 'dept.name', 'br.name', 'employees.first_name', 'employees.middle_name', 'employees.last_name', 'emp_code']);

        return $query;
    }

    public function relationSearch(): array
    {
        return [];
    }

    public function fields(): PowerGridFields
    {
        $powerGrid = PowerGrid::fields()
            ->add('id')
            ->add('employee', fn($row) => $row->employee . ' [' . $row->emp_code . ']')
            ->add('employee_name', fn($row) => $row->employee)
            ->add('employee_code', fn($row) => $row->emp_code)
            ->add('branch')
            ->add('department')
            ->add('early_out')
            ->add('leave_count')
            ->add('substitue_leave_days')
            ->add('total_unpaid_leave')
            ->add('missed_punch')
            ->add('holiday')
            ->add('work_on_holiday')
            ->add('week_end')
            ->add('work_on_week_end');

        if (fedexHrm()) {
            foreach ($this->remarkCategories as $category) {
                $powerGrid->add(strtolower(str_replace(' ', '_', $category)));
            }
        } else {
            $powerGrid->add('late_in');
        }

        return $powerGrid;
    }

    public function columns(): array
    {
        $columnList = [
            Column::make('Employee', 'employee', 'employees.first_name')->sortable()->visibleInExport(false),

            Column::make('Employee Name', 'employee_name')->hidden()->visibleInExport(true),
            Column::make('Employee Code', 'employee_code')->hidden()->visibleInExport(true),

            Column::make('Branch', 'branch', "br.name")->sortable(),
            Column::make('Department', 'department', "dept.name")->sortable(),
            Column::make('Present', 'present'),
            Column::make('Absent', 'absent'),
        ];

        if (fedexHrm()) {
            foreach ($this->remarkCategories as $category) {
                $columnList[] = Column::make($category, strtolower(str_replace(' ', '_', $category)));
            }
        } else {
            $columnList[] = Column::make("Late In", "late_in");
        }

        return array_merge($columnList, [
            Column::make('Early Out', 'early_out'),
            Column::make('Missed Punch', 'missed_punch'),
            Column::make('Leave', 'leave_count'),
            Column::make('Substitue Leave', 'substitue_leave_days'),
            Column::make('Unpaid Leave', 'total_unpaid_leave'),
            Column::make('Week-End', 'week_end'),
            Column::make('Week-End Duty', 'work_on_week_end'),
            Column::make('Holiday', 'holiday'),
            Column::make('Holiday Duty', 'work_on_holiday'),
        ]);
    }

    #[\Livewire\Attributes\On('edit')]
    public function edit($rowId): void
    {
        $this->js('alert(' . $rowId . ')');
    }

    /*
    public function actionRules($row): array
    {
       return [
            // Hide button edit for ID 1
            Rule::button('edit')
                ->when(fn($row) => $row->id === 1)
                ->hide(),
        ];
    }
    */
}
