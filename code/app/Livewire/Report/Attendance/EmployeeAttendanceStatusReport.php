<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Services\ScopeFetcher;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\Employee\Employee;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\LateInCategory;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Employee Attendance Status Report')]

class EmployeeAttendanceStatusReport extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $employee_id = "";
    public $selectedYear = "";
    public $selectedMonth = "";

    public $branchId, $departmentId, $regionId, $companyId;
    public $filtered = false;
    public $yearList = [];

    public array $scopeWiseView = [];

    public $attendance_filter_type = null;
    public $filterTypes = [
        '' => 'All',
        'leave' => 'Leave',
        'absent' => 'Absent',
        'present' => 'Present',
        'Day off' => 'Day Off',
        'early out' => 'Early Out',
        'missed punch' => 'Missed Punch',
        'work' => 'Work On Day Off',
    ];

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_id'];
        $this->withTrashed = true;
        $this->sortBy = 'attendance.date_np';
        $this->sortDirection = 'asc';
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');

        $this->setPermissionForEmployeeDropdown();

        $this->yearList = range($this->selectedYear - 2, $this->selectedYear + 2);

        $lateInCategorieNames = LateInCategory::pluck('name');
        foreach ($lateInCategorieNames as $name) {
            $this->filterTypes[$name] = $name;
        }
        $this->scopeWiseView = (new ScopeFetcher())->scopeWiseView();
    }

    public function updated($propertyName)
    {
        if ($propertyName === 'attendance_filter_type') {
            $this->filtered = true;
            unset($this->list);
        }
        if ($propertyName === 'selectedMonth') {
            $this->attendance_filter_type = null;
            $this->filtered = false;
            unset($this->list);
        }
        if (in_array($propertyName, ['branchId', 'departmentId'])) {
            $this->attendance_filter_type = null;
            $this->filtered = false;
            $this->refreshEmployeeDropdown();
            unset($this->list);
        }
        if ($propertyName === 'employee_id' || empty($this->employee_id)) {
            $this->attendance_filter_type = null;
            $this->filtered = false;
            unset($this->list);
        }
    }
    public function refreshEmployeeDropdown()
    {
        $this->employeeSearchFilter['branch_ids'] = $this->branchId ? [$this->branchId] : [];
        $this->employeeSearchFilter['department_ids'] = $this->departmentId ? [$this->departmentId] : [];
        $this->refreshEmployeeList();
    }

    public function validationAttributes()
    {
        return [
            'employee_id' => 'employee name'
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = DB::table('attendance')
            ->where('employee_id', $this->employee_id)
            ->where('date_np', 'like', "$this->selectedYear-$this->selectedMonth-%")
            ->whereNull('deleted_at')

            ->when($this->attendance_filter_type, function ($query) {
                $query->where('status', 'like', "%{$this->attendance_filter_type}%");
            })
            ->when($this->sortBy && $this->sortDirection, fn(QueryBuilder $query) => $query->orderBy($this->sortBy, $this->sortDirection));
        $data = $query->get()->transform(function ($data) {
            $sourceIp = $data->source_ip ? json_decode($data->source_ip, true) : null;

            $data->source_ip_in = $sourceIp['in'] ?? '';
            $data->source_ip_out = $sourceIp['out'] ?? '';
            return $data;
        });
        return $data;
    }
    #[Computed(persist: true)]
    public function branchList()
    {
        $this->regionId = $this->regionId == "" ? null : $this->regionId;
        return (new ScopeFetcher())->fetchBranch($this->companyId, $this->regionId)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment($this->companyId)->pluck("name", "id")->toArray();
    }

    public function filter()
    {
        $this->validate([
            'employee_id' => 'required',
        ]);
        $this->filtered = true;
        unset($this->list);
        return [
            'branch' => $this->branchId,
            'department' => $this->departmentId,
        ];
    }

    public function filterList()
    {
        $this->validate([
            'employee_id' => 'required',
        ]);
        $this->filtered = true;
        unset($this->list);
    }

    public function timeFormat($time)
    {
        if ($time)
            return Carbon::parse($time)->format('g:i:s A');
        else
            return "";
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function getRowColorClass($status, $source)
    {
        $cell_color = "";
        switch ($status) {
            case 'Absent':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Leave') !== false);
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $cell_color = "bg-row-missed-punch";
                break;
            case 'Day Off':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $cell_color = "bg-row-absent";
                break;
        }
        switch ($source) {
            case 'Time Request';
                $cell_color = "bg-row-missed-punch";
                break;
        }
        return $cell_color;
    }

    public function export()
    {
        $monthName = $this->nepaliMonthList[$this->selectedMonth];
        $title = "Employee Attendance Status Report $monthName $this->selectedYear";

        $employee = Employee::find($this->employee_id);
        if ($employee)
            $title .= " ($employee->name)";

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\EmployeeAttendanceStatusReportExport($this->list, $title),
            $title . '.xlsx'
        );
    }

    public function render()
    {
        return view('livewire.report.attendance.employee-attendance-status-report');
    }
}
