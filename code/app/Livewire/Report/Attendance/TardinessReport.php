<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Helpers\Constant;
use App\Models\configs\LateInCategory;
use App\Models\configs\LateInCategoryTime;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

#[Title("Employee Tardiness Report")]
class TardinessReport extends Component
{
    use WithDataTable, WithDefaultFilter, MultiselectEmployeeSearch;

    public $employee_ids = [];
    public $generate = false;
    public $selectedMonth, $selectedYear, $yearList = [];
    public array $leaveColumns = [];

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->recentlyTerminated = true;

        $this->initializeDate();
        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }

        $this->setPermissionForEmployeeDropdown();
    }

    public function initializeDate()
    {
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
    }
    public function filter()
    {
        $this->generate = true;
        unset($this->list);
    }

    public function clear()
    {
        $this->reset(['filterBranchId', 'filterCompanyId', 'filterRegionId', 'filterDepartmentId', 'employee_ids', 'generate']);
        $this->initializeDate();
        $this->resetPage();
        $this->refreshEmployeeList();
    }


    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
    }
    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function listQuery()
    {
        $query = Employee::query()
            ->leftJoin('attendance as att', 'employees.id', '=', 'att.employee_id')
            ->leftJoin('leave_requests', 'leave_requests.id', '=', 'att.leave_request_id')
            ->leftJoin('leave_options', 'leave_options.id', '=', 'leave_requests.leave_option_id')
            ->leftJoin('leave_types', 'leave_types.id', '=', 'leave_requests.leave_type_id')
            ->leftJoin("companies as comp", "comp.id", "=", "employees.company_id")
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'employees.id')
            ->leftJoin('departments as dept', 'org.department_id', '=', 'dept.id')
            ->leftJoin('branches as br', 'org.branch_id', '=', 'br.id')
            ->leftJoin('regions as region', 'org.region_id', '=', 'region.id')
            ->leftJoin('units as unit', 'org.unit_id', '=', 'unit.id')
            ->leftJoin('payslips', function ($join) {
                $join->on('employees.id', '=', 'payslips.employee_id')
                    ->where('payslips.status', 'Active');
            })
            ->leftJoin('jobs', 'payslips.job_id', '=', 'jobs.id')
            ->whereNull('att.deleted_at')
            ->when(count($this->employee_ids), fn($query) => $query->whereIn('employees.id', $this->employee_ids))
            ->where('att.date_np', 'like', "$this->selectedYear-$this->selectedMonth-%")
            ->when($this->filterCompanyId, fn($query) => $query->where('comp.id', $this->filterCompanyId))
            ->when($this->filterRegionId, fn($query) => $query->where('org.region_id', $this->filterRegionId))
            ->when($this->filterBranchId, fn($query) => $query->where('br.id', $this->filterBranchId))
            ->when($this->filterDepartmentId, fn($query) => $query->where('dept.id', $this->filterDepartmentId));
        $totalDaysInMonth = LaravelNepaliDate::daysInMonth($this->selectedMonth, $this->selectedYear);
        $remarkCategories = LateInCategory::where('is_active', true)->pluck('name', 'id')->toArray() ?? [];

        $leaveDayFractions = DB::table('leave_options')
            ->select('num_days', 'name')
            ->whereNotNull('num_days')
            ->distinct()
            ->get();

        $likeConditions = collect($remarkCategories)
            ->map(fn($c) => "att.status LIKE '%" . $c . "%'")
            ->implode(' OR ');
        if (empty($likeConditions)) {
            $likeConditions = "att.status LIKE '%Late In%'";
        }
        $punctualityInConditions = collect($remarkCategories)
            ->map(fn($c) => "att.status NOT LIKE '%" . $c . "%'")
            ->implode(' AND ');
        if (empty($punctualityInConditions)) {
            $punctualityInConditions = "att.status NOT LIKE '%Late In%'";
        }

        $query = $query->select(
            'employees.id as id',
            Employee::selectNameRawQuery(),
            DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
            'region.name as region_name',
            'br.name as branch_name',
            'dept.name as dept_name',
            'unit.name as unit',
            'jobs.name as jobs',
            DB::raw("$totalDaysInMonth as total_days"),
            DB::raw("SUM(
                CASE
                    WHEN att.leave_request_id IS NOT NULL
                        AND att.in_time IS NOT NULL 
                        AND att.out_time IS NOT NULL
                        AND leave_options.num_days < 1
                    THEN 1 - leave_options.num_days
                    WHEN att.in_time IS NOT NULL
                        AND att.out_time IS NOT NULL 
                    THEN 1
                    ELSE 0
                END
            ) AS total_present"),

            DB::raw("COUNT(CASE
                WHEN att.in_time IS NULL AND att.out_time IS NULL
                    AND att.leave_status = false
                    AND att.status = 'Absent' 
                THEN 1
            END) AS total_absent"),

            // DB::raw("COUNT(CASE WHEN att.in_time IS NULL AND att.out_time IS NULL AND att.status NOT LIKE '%Day Off%' AND att.status NOT LIKE '%On Holiday%' THEN 1 END) AS total_absent"),

            DB::raw("COUNT(CASE
                WHEN att.status LIKE 'Day Off'
                THEN 1
            END) AS total_week_holiday"),

            DB::raw("COUNT(CASE
                WHEN att.status LIKE 'On Holiday'
                THEN 1
            END) AS holiday"),

            DB::raw("COUNT(CASE
                WHEN $punctualityInConditions
                    AND att.in_time IS NOT NULL 
                    AND att.duty_start IS NOT NULL
                    AND att.status LIKE '%Present%' 
                THEN 1
            END) AS punctuality_in"),

            DB::raw("COUNT(CASE
                WHEN att.out_time IS NOT NULL AND att.duty_end IS NOT NULL
                    AND STR_TO_DATE(att.out_time, '%r') >= att.duty_end 
                    AND att.status NOT LIke '%Work On%'
                THEN 1
            END) AS punctuality_out"),

            DB::raw("COUNT(CASE 
                WHEN ($likeConditions ) 
                    AND att.in_time IS NOT NULL AND att.duty_start IS NOT NULL
                THEN 1 
            END) AS late_in"),

            DB::raw("COUNT(CASE
                WHEN att.out_time IS NOT NULL AND att.duty_end IS NOT NULL
                    AND STR_TO_DATE(att.out_time, '%r') < att.duty_end
                THEN 1
            END) AS early_out"),

            DB::raw("COUNT(CASE
                WHEN att.status LIKE '%Work On Holiday%'
                THEN 1
            END) AS work_on_holiday"),

            DB::raw("COUNT(CASE
                WHEN att.status LIKE '%Work On Day Off%'
                THEN 1
            END) AS work_on_day_off"),

            DB::raw("COUNT(CASE
                WHEN att.missed_punch_status = 1
                    OR (att.in_time IS NOT NULL AND att.out_time IS NULL) 
                    OR (att.in_time IS NULL AND att.out_time IS NOT NULL)
                THEN 1
            END) AS missed_punch"),

            DB::raw("SUM(
                CASE 
                WHEN att.leave_status = 1 THEN IFNULL(leave_options.num_days, 0)
                ELSE 0
                END
                ) AS leave_days"),

            DB::raw("SUM(CASE
                WHEN att.leave_status = false
                    AND att.leave_request_id IS NOT NULL
                    AND leave_types.paid = 0
                    THEN IFNULL(leave_options.num_days, 0)
                ELSE 0
                END) AS total_unpaid_leave"),
        );

        foreach ($leaveDayFractions as $leaveOption) {
            $numDay = $leaveOption->num_days;
            $name = $leaveOption->name;

            $alias = Str::slug($name, '_') . '_' . 'leave';
            $this->leaveColumns[$name] = $alias;

            $query->addSelect(
                DB::raw("COUNT(CASE 
                WHEN att.leave_status = 1 
                AND att.leave_request_id IS NOT NULL
                AND leave_options.name = '$name'
                AND leave_options.num_days = $numDay 
                THEN 1 
            END) AS $alias")
            );
        }

        // Grouping
        $query->groupBy(
            'employees.id',
            DB::raw("CONCAT(comp.code, '-', org.employee_code)"),
            'region.name',
            'br.name',
            'dept.name',
            'unit.name',
            'jobs.name'
        );

        $query = filterEmployeesByScope($query, 'org');

        return $this->applySorting($query);
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    public function tableColumns()
    {
        return [
                'full_name'          => 'Employee Name',
                'emp_code'           => 'Employee Code',
                'region_name'        => 'Region',
                'branch_name'        => 'Branch',
                'dept_name'          => 'Department',
                'unit'               => 'Unit',
                'jobs'               => 'Job Title',
                'total_days'         => 'Total Days',
                'total_present'      => 'Present',
                'late_in'            => 'Late In',
                'punctuality_in'     => 'Timely In',
                'early_out'          => 'Early Out',
                'punctuality_out'    => 'Timely Out',
                'total_week_holiday' => 'Total Day Off',
                'work_on_day_off'    => 'Work on Day Off',
                'holiday'            => 'Holiday',
                'work_on_holiday'    => 'Work on Holiday',
                'missed_punch'       => 'Total Missed Punch',
                'total_absent'       => 'Absent',
                'leave_days'         => 'Total Leave Count',
                'total_unpaid_leave' => 'Unpaid Leave',
            ] + $this->leaveColumns; // Add dynamic leave columns
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Employee Tardiness Report - {$this->selectedMonth} {$this->selectedYear}.xlsx";
        $this->exportIgnoreColumns = ['id'];
        $this->exportColumnHeadersMap = $this->tableColumns();
    }
    public function exportTardinessReport()
    {
        $data = $this->listQuery()->get();
        $monthName = LaravelNepaliDate::getMonth($this->selectedMonth);
        $year = $this->selectedYear;
        $title = "Employee Tardiness Report - {$monthName} {$year}";
        $this->exportFileName = $title . ".xlsx";
        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap, $title, $this->leaveColumns)
        implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithCustomStartCell {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;
            private $columnCount;
            private $title;
            private $leaveColumns;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap, $title, $leaveColumns)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
                $this->title = $title;
                $this->leaveColumns = $leaveColumns;
            }

            public function startCell(): string
            {
                // Start headings from row 2
                return 'A2';
            }

            public function collection()
            {
                $columns = array_keys($this->exportColumnHeadersMap);
/*                $columnsLeave = array_keys($this->leaveColumns);
                
                $columns = array_merge($columns, $columnsLeave);*/

                return $this->data->map(function ($item) use ($columns) {
                    $row = [];

                    foreach ($columns as $col) {
                        // Safely fetch value (works for arrays, objects, relations)
                        $value = data_get($item, $col, 0);

                        // Convert null, "" or false → 0
                        if ($value === null || $value === "" || $value === false) {
                            $value = "'0'";
                        }

                        $row[$col] = "$value";
                    }
                    return $row;
                });
            }


            public function headings(): array
            {
                $headings = array_values($this->exportColumnHeadersMap);
                $this->columnCount = count($headings); // <-- IMPORTANT
                return $headings;
            }
            public function registerEvents(): array
            {
                return [
                    \Maatwebsite\Excel\Events\BeforeSheet::class => function (\Maatwebsite\Excel\Events\BeforeSheet $event) {

                        // Get last column letter (A, B, C... AA, AB, etc.)
                        $lastColumn = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($this->columnCount);

                        // Merge A1 to last column
                        $event->sheet->mergeCells("A1:{$lastColumn}1");

                        // Set the value
                        $event->sheet->setCellValue('A1', $this->title);

                        // Style the title row
                        $event->sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
                        $event->sheet->getStyle('A1')->getAlignment()->setHorizontal(
                            \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER
                        );
                    },
                ];
            }
        }, $this->exportFileName);
    }
    public function render()
    {
        return view('livewire.report.attendance.tardiness-report');
    }
}
