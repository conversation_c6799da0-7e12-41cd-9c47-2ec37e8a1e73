<?php

namespace App\Livewire\Report\Attendance;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\Leaves\Attendance;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title("Employee Daily Attendance Report")]
class DailyAttendanceReport extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $employee_id = ""; // for live property of employee dropdown

    public $employeeId = ""; // setting after filter click, all details are filtered by this id.
    public $selectedYear = "";
    public $selectedMonth = "";
    public $filtered = false;

    public $yearList = [];

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_id'];
        $this->withTrashed = true;
        $this->sortBy = 'att.date_en';
        $this->sortDirection = 'asc';
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');

        $this->setPermissionForEmployeeDropdown();

        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }
    }
    public function validationAttributes()
    {
        return [
            'employee_id' => 'employee name'
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        if (!$this->employee_id) return null;
        $paginator = DB::table('attendance as att')
            // ->leftJoin('employees as emp', 'emp.id', 'att.employee_id')
            // ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            // ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->where('att.employee_id', $this->employeeId)
            ->where('date_np', 'like', "$this->selectedYear-$this->selectedMonth-%")
            ->whereNull('att.deleted_at')
            ->when($this->sortBy && $this->sortDirection, fn(QueryBuilder $query) => $query->orderBy($this->sortBy, $this->sortDirection))
            ->select(
                'att.id',
                'att.date_en',
                'att.date_np',
                'att.in_time',
                'att.out_time',
                'att.duty_start',
                'att.duty_end',
                'att.status',
                'att.source',
                'att.total_hours',
            )
            ->when($this->sortBy && $this->sortDirection, fn($query) => $query->orderBy($this->sortBy, $this->sortDirection))
            ->get()->transform(function ($data) {
                $data->duty_start = $this->timeFormat($data->duty_start);
                $data->duty_end = $this->timeFormat($data->duty_end);
                return $data;
            });
        return $paginator;
    }

    public function getRowColorClass($status, $source)
    {
        $cell_color = "";
        switch ($status) {
            case 'Absent':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Leave') !== false);
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $cell_color = "bg-row-missed-punch";
                break;
            case 'Day Off':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $cell_color = "bg-row-absent";
                break;
        }
        switch ($source) {
            case 'Time Request';
                $cell_color = "bg-row-missed-punch";
                break;
        }
        return $cell_color;
    }

    public function filter()
    {
        $this->validate([
        'employee_id'=>'required',
        ]);
        $this->filtered = true;
        $this->employeeId = $this->employee_id;
        unset($this->list);
    }

    public function timeFormat($time)
    {
        if ($time)
            return Carbon::parse($time)->format('g:i:s A');
        else return "";
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }


    public function export()
    {
        $monthName = $this->nepaliMonthList[$this->selectedMonth];
        $title = "Daily Attendance Report $monthName $this->selectedYear";

        $employee = \App\Models\Employee\Employee::find($this->employeeId);
        if ($employee) $title .= " ($employee->name)";

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\DailyAttendanceReportExport($this->list, $title),
            $title . '.xlsx'
        );
    }

    public function render()
    {
        return view('livewire.report.attendance.daily-attendance-report');
    }
}
