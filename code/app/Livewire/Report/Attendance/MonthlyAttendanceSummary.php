<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Services\ScopeFetcher;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class MonthlyAttendanceSummary extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $company, $region, $branch, $department, $reportMonth, $reportYear, $date_nep, $employee_ids = [];
    public $totalWeekends = 0;
    public $homeLeaveId, $sickLeaveIdInhouse, $sickLeaveIdOuthouse, $replacementLeave;

    #[Title('Monthly Attendance Summary')]

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $date = now();
        $this->date_nep = LaravelNepaliDate::from($date)->toNepaliDate();
        $this->company = currentEmployee()?->company_id;
    }

    protected function getNepaliDateMonthRange()
    {
        $nepaliDateParts = explode('-', $this->date_nep);

        $bsYear  = $nepaliDateParts[0];
        $bsMonth = $nepaliDateParts[1];

        $daysInMonth = LaravelNepaliDate::daysInMonth($bsMonth, $bsYear);

        $bsStartOfMonth = new LaravelNepaliDate($bsYear, $bsMonth, 1);
        $bsEndOfMonth   = new LaravelNepaliDate($bsYear, $bsMonth, $daysInMonth);

        $startDate = $bsStartOfMonth->toEnglishDate();
        $endDate   = $bsEndOfMonth->toEnglishDate();

        return compact('startDate', 'endDate', 'bsYear', 'bsMonth');
    }

    protected function getPreviousNepaliMonthRange()
    {
        $nepaliDateParts = explode('-', $this->date_nep);

        $bsYear  = (int) $nepaliDateParts[0];
        $bsMonth = (int) $nepaliDateParts[1];

        if ($bsMonth === 1) {
            $prevYear  = $bsYear - 1;
            $prevMonth = 12;
        } else {
            $prevYear  = $bsYear;
            $prevMonth = $bsMonth - 1;
        }

        $daysInPrevMonth = LaravelNepaliDate::daysInMonth($prevMonth, $prevYear);

        $bsPrevStart = new LaravelNepaliDate($prevYear, $prevMonth, 1);
        $bsPrevEnd   = new LaravelNepaliDate($prevYear, $prevMonth, $daysInPrevMonth);

        $prevStartDate = $bsPrevStart->toEnglishDate();
        $prevEndDate   = $bsPrevEnd->toEnglishDate();

        return compact('prevStartDate', 'prevEndDate');
    }


    protected function getNepaliFiscalYearStart()
    {
        $nepaliDateParts = explode('-', $this->date_nep);

        $bsYear  = (int) $nepaliDateParts[0];
        $bsMonth = (int) $nepaliDateParts[1];

        $fiscalYear = ($bsMonth < 4) ? $bsYear - 1 : $bsYear;

        $bsFiscalStart = new LaravelNepaliDate($fiscalYear, 4, 1);

        $startOfYear = $bsFiscalStart->toEnglishDate();

        return $startOfYear;
    }



    #[Computed(persist: true)]
    public function companies()
    {
        return (new ScopeFetcher)->fetchCompany();
    }
    #[Computed(persist: true)]
    public function branches()
    {
        return (new ScopeFetcher)->fetchBranch();
    }
    #[Computed(persist: true)]
    public function departments()
    {
        return (new ScopeFetcher)->fetchDepartment();
    }
    #[Computed(persist: true)]
    public function regions()
    {
        return (new scopeFetcher)->fetchRegion($this->company);
    }
    public function listQuery()
    {
        $current = $this->getNepaliDateMonthRange();
        $previous = $this->getPreviousNepaliMonthRange();

        $startDate = $current['startDate'];
        $endDate   = $current['endDate'];

        $prevStartDate = $previous['prevStartDate'];
        $prevEndDate   = $previous['prevEndDate'];

        $startOfYear = $this->getNepaliFiscalYearStart();


        $totalLeaveUptoLastMonth = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->selectRaw("
        att.employee_id,
        SUM(IFNULL(lo.num_days, 0)) AS total_leave_upto_last_month
    ")
            ->whereBetween('att.date_en', [$startOfYear, $prevEndDate])
            ->groupBy('att.employee_id');


        $prevMonthLeaveQuery = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->selectRaw("
        att.employee_id,
        SUM(IFNULL(lo.num_days, 0)) AS prev_month_total_leave
    ")
            ->whereBetween('att.date_en', [$prevStartDate, $prevEndDate])
            ->groupBy('att.employee_id');


        $prevMonthReplacementQuery = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->leftJoin("leave_types as lt", "lr.leave_type_id", "=", "lt.id")
            ->selectRaw("
            att.employee_id,
        SUM(CASE WHEN att.status LIKE '%Substitute Leave%' THEN IFNULL(lo.num_days, 0)ELSE 0 END) AS prev_month_replacement_leave")
            ->whereBetween('att.date_en', [$prevStartDate, $prevEndDate])
            ->groupBy('att.employee_id');

        $uptoLastMonthReplacementQuery = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->leftJoin("leave_types as lt", "lr.leave_type_id", "=", "lt.id")

            ->selectRaw("att.employee_id,SUM(CASE WHEN att.status LIKE '%Substitute Leave%' THEN IFNULL(lo.num_days,0)ELSE 0 END) AS upto_last_month_replacement")
            ->whereBetween('att.date_en', [$startOfYear, $prevEndDate])
            ->groupBy('att.employee_id');

        $uptolastMonthTotalOtherLeaveWithOutReplacementLeave = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->leftJoin("leave_types as lt", "lr.leave_type_id", "=", "lt.id")
            ->selectRaw("
        att.employee_id,
        SUM(CASE 
            WHEN att.status NOT LIKE '%Substitute Leave%'
            THEN IFNULL(lo.num_days, 0) 
            ELSE 0 
        END) AS other_leave_upto_last_month
    ")
            ->whereBetween('att.date_en', [$startOfYear, $prevEndDate])
            ->groupBy('att.employee_id');

        $totalLeaveTakenTillNow = DB::table('attendance as att')
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")
            ->leftJoin("leave_options as lo", "lr.leave_option_id", "=", "lo.id")
            ->leftJoin("leave_types as lt", "lr.leave_type_id", "=", "lt.id")
            ->selectRaw("
        att.employee_id,
        SUM(IFNULL(lo.num_days, 0)) as total_leave_taken_till_now
    ")
            ->whereBetween('att.date_en', [$startOfYear, $endDate])
            ->groupBy('att.employee_id');

        $activeFiscalYearId = DB::table('fiscal_years')
            ->where('is_active', 1)
            ->value('id');

        $employeeRemainingLeavesQuery = DB::table('employee_leaves')
            ->selectRaw("employee_id, SUM(remaining_days) AS total_remaining_leave")
            ->whereRaw("TRIM(fiscal_year_id) = ?", [trim($activeFiscalYearId)])
            ->groupBy('employee_id');


        $query = Employee::leftJoin("employee_org", "employees.id", "=", "employee_org.employee_id")
            ->leftJoin("companies as company", "employees.company_id", "=", "company.id")
            ->leftJoin("regions as region", "employee_org.region_id", "=", "region.id")
            ->leftJoin("branches as branch", "employee_org.branch_id", "=", "branch.id")
            ->leftJoin("departments as department", "employee_org.department_id", "=", "department.id")
            ->leftJoin("attendance as att", function ($join) use ($startDate, $endDate) {
                $join->on("att.employee_id", "=", "employees.id")
                    ->whereBetween('att.date_en', [$startDate, $endDate]);
            })

            ->leftJoin("leave_requests as main_lr", "main_lr.id", "=", "att.leave_request_id")
            ->leftJoin("leave_options as main_lo", "main_lr.leave_option_id", "=", "main_lo.id")
            ->leftJoin("leave_types as main_lt", "main_lt.id", "=", "main_lr.leave_type_id")

            ->leftJoinSub($totalLeaveUptoLastMonth, "year_att", "year_att.employee_id", "=", "employees.id")
            ->leftJoinSub($prevMonthLeaveQuery, "prev_att", "prev_att.employee_id", "=", "employees.id")
            ->leftJoinSub($prevMonthReplacementQuery, "prev_month_att", "prev_month_att.employee_id", "=", "employees.id")
            ->leftJoinSub($uptoLastMonthReplacementQuery, "upto_last_month_att", "upto_last_month_att.employee_id", "=", "employees.id")
            ->leftJoinSub($uptolastMonthTotalOtherLeaveWithOutReplacementLeave, "other_leave_att", "other_leave_att.employee_id", "=", "employees.id")
            ->leftJoinSub($totalLeaveTakenTillNow, "total_leave_att", "total_leave_att.employee_id", "=", "employees.id")
            ->leftJoinSub(
                $employeeRemainingLeavesQuery,
                'emp_leave_sub',
                'emp_leave_sub.employee_id',
                '=',
                'employees.id'
            )
            ->leftJoin("leave_requests as lr", "att.leave_request_id", "=", "lr.id")

            ->when($this->company, function ($query) {
                $query->where("employees.company_id", $this->company);
            })
            ->when($this->region, function ($query) {
                $query->where("employee_org.region_id", $this->region);
            })
            ->when($this->branch, function ($query) {
                $query->where("employee_org.branch_id", $this->branch);
            })->when($this->department, function ($query) {
                $query->where("employee_org.department_id", $this->department);
            })
            ->when($this->employee_ids, function ($query) {
                $query->where('employees.id', $this->employee_ids);
            });

        $query = filterEmployeesByScope($query, 'employee_org');

        $query->select(
            'employees.id as id',
            DB::raw("MAX(CONCAT(employees.first_name,' ',employees.middle_name,' ',employees.last_name)) as full_name"),
            DB::raw("MAX(CONCAT(company.code,'-',employee_org.employee_code)) as emp_code"),
            DB::raw("MAX(branch.name) as branch_name"),
            DB::raw("MAX(department.name) as department_name"),
            DB::raw("CASE WHEN MAX(employee_org.doj) BETWEEN '{$startDate}' AND '{$endDate}' THEN MAX(employee_org.doj) ELSE NULL END AS date_of_join"),
            DB::raw("CASE WHEN MAX(employee_org.termination_date) BETWEEN '{$startDate}' AND '{$endDate}' THEN MAX(employee_org.termination_date)ELSE NULL END AS last_date"),

            DB::raw("COUNT(CASE WHEN att.status = 'Day Off' THEN 1 END) as weekends"),
            DB::raw("COUNT(CASE WHEN att.status LIKE '%On Holiday%'AND att.in_time IS NULL AND att.out_time IS NULL THEN 1 END) AS total_holidays"),

            DB::raw("COUNT(CASE WHEN (att.status <> 'Day Off' AND att.status NOT LIKE '%On Holiday%' AND att.status NOT LIKE 'Work On Holiday%')
                                OR ((att.status LIKE '%Day Off%' OR att.status LIKE '%On Holiday%') 
                    AND (att.in_time IS NOT NULL OR att.out_time IS NOT NULL))
                                OR (att.status LIKE 'Work On Holiday%' 
                    AND (att.in_time IS NOT NULL OR att.out_time IS NOT NULL))THEN 1 END) AS total_duty_days"),

            DB::raw("COUNT(CASE WHEN att.in_time IS NOT NULL OR att.out_time IS NOT NULL THEN 1 END) as total_present_days"),

            DB::raw("SUM(CASE WHEN main_lt.name LIKE '%Annual Leave%' THEN IFNULL(main_lo.num_days, 0) ELSE 0 END) as home_leave"),
            DB::raw("SUM(CASE WHEN main_lt.name LIKE '%Sick Leave%' THEN IFNULL(main_lo.num_days, 0) ELSE 0 END) as total_sick_leave"),

            DB::raw("SUM(IFNULL(main_lo.num_days, 0)) as total_combined_leave"),

            DB::raw("SUM(CASE WHEN att.status LIKE '%Substitute Leave%' THEN IFNULL(main_lo.num_days, 0) ELSE 0 END) as replacement_leave"),

            DB::raw("GREATEST(0, DATEDIFF(LEAST('{$endDate}', IFNULL(MAX(employee_org.termination_date), '{$endDate}')), GREATEST('{$startDate}', MAX(employee_org.doj))) + 1) as total_tenure_days"),

            DB::raw("IFNULL(prev_att.prev_month_total_leave, 0) AS prev_month_total_leave"),
            DB::raw("IFNULL(year_att.total_leave_upto_last_month,0) AS total_leave_upto_last_month"),


            DB::raw("IFNULL(prev_month_att.prev_month_replacement_leave,0) AS prev_month_replacement_leave"),
            DB::raw("IFNULL(upto_last_month_att.upto_last_month_replacement,0) AS upto_last_month_replacement"),


            DB::raw("IFNULL(other_leave_att.other_leave_upto_last_month,0) AS other_leave_upto_last_month"),
            DB::raw("IFNULL(total_leave_att.total_leave_taken_till_now,0) AS total_leave_taken_till_now"),
            DB::raw("IFNULL(emp_leave_sub.total_remaining_leave,0) AS total_remaining_leave")
        );

        $query->groupBy('employees.id');

        return $query;
    }


    public function updated($value)
    {
        if ($value == 'company') {
            $this->reset('branch', 'department', 'region');
            unset($this->regions, $this->branches, $this->departments);
        }
        if (\in_array($value, ['company'])) {
            unset($this->branches, $this->departments);
            $this->branch = null;
            $this->department = null;
        }

        if ($value == 'region') {
            unset($this->branches);
            $this->reset('branch');
        }
        unset($this->list);
    }

    public function resetFilters()
    {
        $this->reset('company', 'region', 'branch', 'department');
        unset($this->regions, $this->branches, $this->departments);
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    public $exportColumnHeadersMaps = [
        'full_name' => 'Full name',
        'emp_code' => 'Emp code',
        'branch_name' => 'Branch name',
        'department_name' => 'Department name',
        'region_name' => 'Region name',
        'date_of_join' => 'Date of join',
        'last_date' => 'Last date',
        'weekends' => 'Total Weekends',
        'total_holidays' => 'Total holidays',
        'total_duty_days' => 'Total duty days',
        'total_present_days' => 'Total present days',
        'home_leave' => 'Total Home leave',
        'total_sick_leave' => 'Total sick leave',
        'total_combined_leave' => 'Total combined leave',
        'replacement_leave' => 'Replacement leave',
        'total_tenure_days' => 'Total tenure days',
        'prev_month_total_leave' => 'Previous Month total leave',
        'total_leave_upto_last_month' => 'Total Leave Upto Last Month',
        'prev_month_replacement_leave' => 'Previous Month Total Substitute Leave',
        'upto_last_month_replacement' => 'Total Substitute Leave up to Last Month',
        'other_leave_upto_last_month' => 'Other leave upto last month',
        'total_leave_taken_till_now' => 'Total leave taken till now',
        'total_remaining_leave' => 'Total remaining leave On Monthly Basis',
    ];

    public function exportAllData($exportAll = false)
    {
        // Get data
        if ($exportAll) {
            $data = $this->listQuery()->get();
        } else {
            $data = $this->list;
            if ($data instanceof LengthAwarePaginator) {
                $data = $data->getCollection();
            }
        }

        if ($data->isEmpty()) {
            session()->flash('warning', 'No Data to Display');
            return;
        }

        $data = $data->map(function ($item) {
            return collect($item)->except('id')->toArray();
        });

        return Excel::download(new class($data, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings, WithStyles {
            private $data;
            private $headers;

            public function __construct($data, $headers)
            {
                $this->data = $data;
                $this->headers = $headers;
            }

            public function collection()
            {
                return collect($this->data);
            }

            public function headings(): array
            {
                if ($this->data->isEmpty()) return [];

                $firstRow = $this->data->first();
                $headings = [];
                foreach ($firstRow as $key => $value) {
                    $headings[] = $this->headers[$key] ?? ucfirst(str_replace('_', ' ', $key));
                }
                return $headings;
            }

            public function styles(Worksheet $sheet)
            {
                return [
                    1 => ['font' => ['bold' => true]], // first row bold
                ];
            }
        }, 'monthly-attendance.xlsx');
    }

    public function render()
    {
        return view('livewire.report.attendance.monthly-attendance-summary');
    }
}
