<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Services\ScopeFetcher;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\LateInCategory;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\LeaveOption;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDateRangeFilter;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Employee Monthly Attendance Report")]
class EmployeeMonthlyAttendanceReport extends Component
{
    use WithDateRangeFilter, MultiselectEmployeeSearch;

    public $generate = false;

    public $branchId, $departmentId, $employeeIds = [], $companyId, $regionId;
    public $selectedMonth, $selectedYear, $yearList = [];

    public $filters = [
        'employees' => [],
        'branch' => [],
        'department' => [],
        'month' => '',
        'year' => '',
        'companyId' => '',
    ];

    public array $scopeWiseView = [];

    public function filter()
    {
        // $this->validate();
        $this->filters = [
            'employees' => $this->employeeIds,
            'branch' => $this->branchId,
            'department' => $this->departmentId,
            'month' => $this->selectedMonth,
            'year' => $this->selectedYear,
            'companyId' => $this->companyId,
        ];
        $this->generate = true;
    }

    public function clear()
    {
        $this->reset(['branchId', 'departmentId']);
        $this->employeeIds = [];
        $this->dispatch("toggle-employee-ids", []);
        $this->setInitialFilters();
    }


    public function mount()
    {
        $this->multiSelectAttributes = ['employeeIds'];
        $this->withTrashed = true;
        $this->setInitialFilters();
        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }
        $this->scopeWiseView = (new ScopeFetcher())->scopeWiseView();
    }

    public function setInitialFilters()
    {
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');

        $this->setPermissionForEmployeeDropdown();

        if ($this->generate) $this->filter();
    }

    #[Computed(persist:true)]
    public function companyList()
    {
     return (new ScopeFetcher())->fetchCompany()->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        $this->regionId = $this->regionId == "" ? null : $this->regionId;
        return (new ScopeFetcher())->fetchBranch($this->companyId, $this->regionId)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment($this->companyId)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function updated($attr)
    {
        if (in_array($attr, ['branchId', 'departmentId'])) {
            $this->employeeIds = [];
            $this->refreshEmployeeDropdown();
        }
        if (in_array($attr, ['companyId'])) {
            unset($this->branchList, $this->departmentList);
        }
    }

    public function refreshEmployeeDropdown()
    {
        $this->employeeSearchFilter['company_ids'] = $this->companyId ? [$this->companyId] : [];
        $this->employeeSearchFilter['region_ids'] = $this->regionId ? [$this->companyId] : [];
        $this->employeeSearchFilter['branch_ids'] = $this->branchId ? [$this->branchId] : [];
        $this->employeeSearchFilter['department_ids'] = $this->departmentId ? [$this->departmentId] : [];
        $this->refreshEmployeeList();
    }

    public function render()
    {
        return view('livewire.report.attendance.employee-monthly-attendance-report');
    }
}
