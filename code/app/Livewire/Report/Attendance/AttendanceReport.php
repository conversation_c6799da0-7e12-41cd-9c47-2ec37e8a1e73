<?php

namespace App\Livewire\Report\Attendance;

use App\Http\Services\ScopeFetcher;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Models\configs\Department;
use App\Models\configs\Unit;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

//this page was changed from attendance report to attendance
#[Title('Attendance')]
class AttendanceReport extends Component
{
    use WithDataTable, MultiselectEmployeeSearch, WithNotify;

    // filters
    public $startDate, $endDate, $companyId, $regionId, $branchId, $departmentId, $unitId, $employeeIds = [], $gender, $designationId;

    public function mount()
    {
        $this->multiSelectAttributes = ['employeeIds'];
        $this->withTrashed = true;
        $this->setPermissionForEmployeeDropdown();
        $this->sortBy = 'date_en';
        $this->sortDirection = 'desc';
        $this->initialFilters();
    }

    public function initialFilters()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $this->startDate = "{$currentYear}-{$currentMonth}-01";
        $this->endDate = $nepaliDate;

        if (!scopeAll()) {
            $this->companyId = currentEmployee()?->company_id;
            if (!scopeCompany()) {
                if (scopeRegion()) {
                    $this->regionId = currentEmployee()?->organizationInfo?->region_id;
                } else {
                    if (!scopeBranch() && scopeDepartment()) {
                        $this->departmentId = currentEmployee()?->organizationInfo->department_id;
                    }
                }
            }
        };
    }

    public function listQuery(): Builder
    {
        // dd($this->employeeIds);
        $query = Attendance::leftJoin('employees as emp', 'emp.id', 'attendance.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
            ->leftJoin('companies as company', 'company.id', 'emp.company_id')
            ->leftJoin('branches as branch', 'branch.id', 'org.branch_id')
            ->leftJoin('units as unit', 'unit.id', 'org.unit_id')
            ->leftJoin('departments as department', 'department.id', 'org.department_id')
            ->leftJoin("payslips as payslip", "emp.id", "payslip.employee_id")
            ->leftJoin("designations as designation", "designation.id", "payslip.designation_id")
            ->leftJoin("jobs as job", "job.id", "payslip.job_id")
            ->whereBetween('attendance.date_np', [$this->startDate, $this->endDate])
            ->when($this->companyId, fn($q) => $q->where('emp.company_id', $this->companyId))
            ->when($this->regionId, fn($q) => $q->where('org.region_id', $this->regionId))
            ->when($this->branchId, fn($q) => $q->where('org.branch_id', $this->branchId))
            ->when($this->departmentId, fn($q) => $q->where('org.department_id', $this->departmentId))
            ->when($this->unitId, fn($q) => $q->where('org.unit_id', $this->unitId))
            ->when($this->designationId, fn($q) => $q->where('org.designation_id', $this->designationId))
            ->when($this->gender, fn($q) => $q->where('emp.gender', $this->gender))
            ->when(count($this->employeeIds), fn($q) => $q->whereIn('emp.id', $this->employeeIds))
            ->where(function ($query) {
                $query->whereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = emp.id
                    AND p.status = "Active"
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = emp.id
                    AND p.status = "Expired"
                    AND NOT EXISTS (
                        SELECT 1 FROM payslips active
                        WHERE active.employee_id = p.employee_id
                        AND active.status = "Active"
                    )
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereNull('payslip.id');
            });

        $query = filterEmployeesByScope($query, empInfoJoin: 'emp');

        $query->select(
            Employee::selectNameRawQuery('emp', 'emp_name'),
            DB::raw("CONCAT(company.code, '-', org.employee_code) as emp_code"),
            'emp.deleted_at as emp_deleted',
            'branch.name as branch',
            'department.name as department',
            'unit.name as unit',
            'designation.title as designation',
            'job.name as job',
            'attendance.date_en',
            'attendance.date_np',
            'attendance.duty_start',
            'attendance.duty_end',
            'attendance.in_time',
            'attendance.out_time',
            'attendance.status as status',
            'attendance.source as source',
            'attendance.remarks as remarks',
            'attendance.total_hours',
            DB::raw("JSON_UNQUOTE(JSON_EXTRACT(attendance.source_ip, '$.in')) as in_ip"),
            DB::raw("JSON_UNQUOTE(JSON_EXTRACT(attendance.source_ip, '$.out')) as out_ip")
        );

        return $query;
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Attendance Report.xlsx";
        $this->exportIgnoreColumns = ['emp_deleted'];
        $this->exportColumnHeadersMap = [
            'emp_name'      => 'Employee Name',
            'emp_code'      => 'Employee Code',
            'date_en'       => 'Date (A.D.)',
            'date_np'       => 'Date (B.S.)',
            'duty_start'    => 'Shift Start',
            'duty_end'      => 'Shift End',
            'in_time'       => 'Check In Time',
            'out_time'      => 'Check Out Time',
            'in_ip'         => 'Check In IP',
            'out_ip'        => 'Check Out IP',
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->applySorting($this->listQuery())->paginate($this->perPage);
    }

    public function filterList()
    {
        if (!$this->startDate || !$this->endDate)
            return $this->notify("Start Date and End Date is required")->type('error')->send();

        $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
        $endDate = LaravelNepaliDate::from($this->endDate)->toEnglishDate();

        if (Carbon::parse($startDate)->gt($endDate))
            return $this->notify("Start date must be smaller than end date")->type('error')->send();

        $dateDifference = Carbon::parse($startDate)->diffInDays($endDate);
        // dd($dateDifference);
        if ($dateDifference > 90) {
            return $this->notify("The date range cannot exceed 90 days")->type('error')->send();
        }

        unset($this->list);
    }

    public function resetFilters()
    {
        $this->reset('companyId', 'branchId', 'departmentId', 'unitId', 'employeeIds');
        $this->initialFilters();
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        if (!scopeAll()) return [];
        return (new ScopeFetcher())->fetchCompany(pluck: 'id_name');
    }

    #[Computed(persist: true)]
    public function regionList()
    {
        if (!$this->companyId) return [];
        return (new ScopeFetcher())->fetchRegion(company: $this->companyId, pluck: 'id_name');
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        if (!$this->companyId || (!$this->regionId && count($this->regionList()))) return [];
        return (new ScopeFetcher())->fetchBranch(company: $this->companyId, region: $this->regionId, pluck: 'id_name');
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        if (!$this->companyId) return [];
        return (new ScopeFetcher())->fetchDepartment(company: $this->companyId, pluck: 'id_name');
    }

    #[Computed(persist: true)]
    public function unitList()
    {
        if (!$this->departmentId) return [];
        return Unit::where('department_id', $this->departmentId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function designationList()
    {
        if (!$this->departmentId) return [];
        return Department::findOrFail($this->departmentId)->designations()
            ->orderBy('designations.title')
            ->pluck('designations.title', 'designations.id')->toArray();
    }

    public function updated($property)
    {
        if ($property == 'companyId') {
            $this->reset('branchId', 'departmentId', 'designationId', 'unitId', 'regionId');
            unset($this->regionList, $this->branchList, $this->departmentList, $this->designationList, $this->unitList);
        }

        if ($property == 'regionId') {
            $this->reset('branchId');
            unset($this->branchList);
        }

        if ($property == 'departmentId') {
            $this->reset('designationId', 'unitId');
            unset($this->designationList, $this->unitList);
        }
    }

    #[Computed(persist: true)]
    public function genderList()
    {
        return [
            'male' => 'Male',
            'female' => 'Female',
            'other' => 'Other',
        ];
    }

    public function getRowColorClass($status, $source)
    {
        $cell_color = "";
        switch ($status) {
            case 'Absent':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Leave') !== false);
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $cell_color = "bg-row-missed-punch";
                break;
            case 'Day Off':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $cell_color = "bg-row-absent";
                break;
        }
        switch ($source) {
            case 'Time Request';
                $cell_color = "bg-row-missed-punch";
                break;
        }
        return $cell_color;
    }

    public function render()
    {
        return view('livewire.report.attendance.attendance-report');
    }
}
