<?php

namespace App\Livewire\Report\OT;

use App\Http\Helpers\Constant;
use App\Http\Repositories\OtReport\Interfaces\OtEmployeeReportRepositoryInterface;
use App\Http\Repositories\OtRequestRepository;
use App\Models\OtRequests;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('OT Employee Report')]
class OtEmployee extends Component
{
    use WithDefaultFilter, WithDataTable, MultiselectEmployeeSearch;

    public $selectedYear, $selectedMonth, $filterCompanyId, $filterRegionId, $filterBranchId, $filterDepartmentId, $varianceMonth, $varianceYear;
    public $employee_id = "";
    public $yearList = [];
    public $otDetails = [];
    public $filtered = false;

    protected OtEmployeeReportRepositoryInterface $otReportRepo;
    public function __construct()
    {
        $this->otReportRepo = app(OtEmployeeReportRepositoryInterface::class);
    }

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_id'];
        $this->withTrashed = true;
        $this->initialFilters();
        $this->setPermissionForEmployeeDropdown();
    }

    public function initialFilters()
    {
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        $this->varianceYear = $this->selectedYear;
        $this->varianceMonth = LaravelNepaliDate::from(Carbon::now()->subMonth())->toNepaliDate(format: 'm', locale: 'en');

        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }
    }

    public function filter()
    {
        $this->filtered = true;
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function list()
    {
        $filters = [
            'year' => $this->selectedYear,
            'month' => $this->selectedMonth,
            'branch' => $this->filterBranchId,
            'department' => $this->filterDepartmentId,
            'employee_id' => $this->employee_id,
        ];

        $query = $this->otReportRepo->getOtEmployeeReport($filters);

        $paginated = $this->applySorting($query)->paginate($this->perPage);

        $paginated->getCollection()->transform(function ($item) {
            $item->total_hours_formatted = $this->secondsToHours($item->total_ot_hours);
            return $item;
        });

        return $paginated;
    }

    public function updated($property)
    {
        if (
            in_array($property, [
                'company',
                'region',
                'branch',
                'department',
                'employee_id',
                'selectedMonth',
                'selectedYear',
            ])
        ) {
            $this->resetPage();
        }
    }


    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function export()
    {
        $monthName = $this->nepaliMonthList[$this->selectedMonth];
        $title = "OT Employee Report $monthName $this->selectedYear";

        $emailTo = currentEmployee()?->organizationInfo?->email;
        $filters = [
            'year' => $this->selectedYear,
            'month' => $this->selectedMonth,
            'branch' => $this->filterBranchId,
            'department' => $this->filterDepartmentId,
            'employee_id' => $this->employee_id,
        ];
        if (empty($emailTo)) {
            $this->notify("Cannot send OT report, no recipient email found for employee.")->type('error')->send();
            logError("Cannot send OT report, no recipient email found for employee.");
            return;
        }

        try {
            (\App\Jobs\OtReportJob::dispatch(
                title: $title,
                reportType: 'employee',
                companyId: currentEmployee()?->company_id ? currentEmployee()?->company_id : 1,
                emailTo: $emailTo,
                filters: $filters,
            ));
            $this->notify("Mail Sent Successfully")->send();
        } catch (\Throwable $th) {
            $this->notify("Error while sending mail")->type('error')->send();
            logError("Error while sending mail: " . $th->getMessage());
        }
    }

    protected function secondsToHours($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return sprintf("%d hrs %d min", $hours, $minutes);
    }

    public function render()
    {
        return view('livewire.report.ot.ot-employee');
    }
}
