<?php

namespace App\Livewire\Report\EmployeeList;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Models\DailyEmployeeCount;
use App\Traits\WithDataTable;
use Livewire\Component;
use Livewire\Attributes\Title;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Computed;
use Carbon\Carbon;
use App\Http\Helpers\Constant;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

#[Title('Employee Count Dashboard')]
#[Layout('layouts.app')]
class EmployeeDashboard extends Component
{

    use WithDataTable;
    public $year, $month, $years, $months;

    public function mount()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $this->month = $currentMonth;
        $this->year = $currentYear;

        $this->months = Constant::NEPALI_MONTH_LIST;
        $this->years = range($currentYear - 1, $currentYear + 1);
    }

    public function getActiveEmployeesAvg()
    {
        $query = $this->query()->get();
        $totalActiveEmployees = $query->sum('active_employees_count');
        $totalDays = $query->count();
        $avgActiveEmployees = $totalDays > 0 ? $totalActiveEmployees / $totalDays : 0;
        return round($avgActiveEmployees);
    }
    public function getNewEmployeesAvg()
    {
        $query = $this->query()->get();
        $totalNewJoins = $query->sum('new_joins_count');
        $totalDays = $query->count();
        $avgNewJoins = $totalDays > 0 ? $totalNewJoins / $totalDays : 0;
        return round($avgNewJoins);
    }
    public function getTerminatingEmployeesAvg()
    {
        $query = $this->query()->get();
        $totalTerminatingEmployee = $query->sum('terminating_employees_count');
        $totalDays = $query->count();
        $avgTerminatingEmp = $totalDays > 0 ? $totalTerminatingEmployee / $totalDays : 0;
        return round($avgTerminatingEmp);
    }
    public function getTerminatedEmployeesAvg()
    {
        $query = $this->query()->get();
        $totalTerminatedEmployee = $query->sum('terminated_employees_count');
        $totalDays = $query->count();
        $avgTerminatedEmp = $totalDays > 0 ? $totalTerminatedEmployee / $totalDays : 0;
        return round($avgTerminatedEmp);
    }

    #[Computed()]
    public function details()
    {
        return [
            [
                'label' => 'Active Employees(Avg)',
                'value' => $this->getActiveEmployeesAvg()
            ],
            [
                'label' => 'New Joins(Avg)',
                'value' => $this->getNewEmployeesAvg()
            ],
            [
                'label' => 'Terminating Employees(Avg)',
                'value' => $this->getTerminatingEmployeesAvg()
            ],
            [
                'label' => 'Terminated Employees(Avg)',
                'value' => $this->getTerminatedEmployeesAvg()
            ]
        ];
    }

    public function updated($propertyName)
    {
        if ($propertyName === 'month' || $propertyName === 'year') {
            $this->updateData();
        }
    }

    public function updateData()
    {
        $this->resetPage();
    }

    public function query()
    {
        $this->validate([
            'year' => 'required'
        ]);
        $query = DailyEmployeeCount::query()
            ->when($this->year && $this->month, function ($query) {
                $query->where('nep_date', 'Like', "{$this->year}-{$this->month}-%");
            });
        return $query->orderBy('created_at', 'desc');
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->query()->get();
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Employee Count Report.xlsx";
        $this->exportIgnoreColumns = ['id', 'created_at', 'updated_at'];
        $this->exportColumnHeadersMap = [
            'eng_date' => 'Date (in A.D.)',
            'nep_date' => 'Date (in B.S.)',
            'active_employees_count' => 'Active Employees Count',
            'new_joins_count' => 'New Join Count',
            'terminating_employees_count' => 'Terminating Employee Count',
            'terminated_employee_count' => 'Terminated Employee Count',
        ];
    }

    public function exportEmployeeCount()
    {
        $data = $this->list();

        $avgActiveEmployees = $this->getActiveEmployeesAvg();
        $avgNewEmployees = $this->getNewEmployeesAvg();
        $avgTerminatingEmployees = $this->getTerminatingEmployeesAvg();
        $avgTerminatedEmployees = $this->getTerminatedEmployeesAvg();

        $averageRow = [
            $avgActiveEmployees,
            $avgNewEmployees,
            $avgTerminatingEmployees,
            $avgTerminatedEmployees,
        ];

        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap, $averageRow) implements FromCollection, WithHeadings, WithStrictNullComparison, ShouldAutoSize, WithStyles {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;
            private $averageRow;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap, $averageRow)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
                $this->averageRow = $averageRow;
            }

            public function collection()
            {
                $items = collect($this->data);  // converting data into collections
                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $averageRowHeading = [
                    'Active Employee Count Avg',
                    'New Joins Count Avg',
                    'Terminating Employee Count Avg',
                    'Terminated Employee Count Avg'
                ];

                $headings = [];

                // Provide headings for your export file
                // You can customize this based on your data structure
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return [$averageRowHeading, $this->averageRow, [], $headings];
            }
            public function styles(Worksheet $sheet)
            {
                $lastRow = $sheet->getHighestRow();
                $lastColumn = $sheet->getHighestColumn();
                $sheet->getStyle('A5:' . $lastColumn . $lastRow)->applyFromArray([
                    'font' => [
                        'bold' => false,
                        'color' => ['rgb' => '000000'],
                        'size' => 12
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'outline' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => '000000']
                        ],
                        'inside' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => ['rgb' => '000000']
                        ]
                    ]
                ]);
                return [
                    1 => [
                        'font' => [
                            'bold' => true,
                            'color' => ['rgb' => '000000'],
                            'size' => 12
                        ],
                        'fill' => [
                            'fillType' => Fill::FILL_SOLID,
                            'startColor' => ['argb' => 'bdbdbd'],
                        ],
                        'alignment' => [
                            'horizontal' => Alignment::HORIZONTAL_CENTER,
                            'vertical' => Alignment::VERTICAL_CENTER,
                        ],
                        'borders' => [
                            'outline' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ],
                            'inside' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ]
                        ],
                    ],
                    2 => [
                        'font' => [
                            'bold' => false,
                            'color' => ['rgb' => '000000'],
                            'size' => 12
                        ],
                        'alignment' => [
                            'horizontal' => Alignment::HORIZONTAL_CENTER,
                            'vertical' => Alignment::VERTICAL_CENTER,
                        ],
                        'borders' => [
                            'outline' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ],
                            'inside' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ]
                        ]
                    ],
                    4 => [
                        'font' => [
                            'bold' => true,
                            'color' => ['rgb' => '000000'],
                            'size' => 12
                        ],
                        'fill' => [
                            'fillType' => Fill::FILL_SOLID,
                            'startColor' => ['argb' => 'bdbdbd'],
                        ],
                        'alignment' => [
                            'horizontal' => Alignment::HORIZONTAL_CENTER,
                            'vertical' => Alignment::VERTICAL_CENTER,
                        ],
                        'borders' => [
                            'outline' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ],
                            'inside' => [
                                'borderStyle' => Border::BORDER_THIN,
                                'color' => ['rgb' => '000000']
                            ]
                        ]
                    ]
                ];
            }
        }, $this->exportFileName);
    }

    public function render()
    {
        return view('livewire.report.employee-list.employee-dashboard');
    }
}
