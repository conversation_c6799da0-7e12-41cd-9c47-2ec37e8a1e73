<?php

namespace App\Livewire\Report\EmployeeList;

use App\Http\Services\ScopeFetcher;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Component;
use Livewire\Attributes\Title;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Schema;

#[Title('Employee List Record')]
class EmployeeList extends Component
{
    use WithNotify, WithDataTable, MultiselectEmployeeSearch;

    private $model;

    public $company, $region, $branch, $department, $unit, $designation, $outsourceCompany, $employeeStatus, $showTerminatedEmployee = false, $employee_ids = [], $confirmed = false, $showDOJColumn, $terminationFilter;

    public $dojStartDateId, $dojEndDateId, $dojStartDate, $dojEndDate, $terminationStartDateId, $terminationEndDateId, $terminationStartDate, $terminationEndDate;

    public function __construct()
    {
        $this->model = new \App\Models\Employee\Employee;
    }

    public function mount()
    {
        if (!scopeAll())
            $this->company = currentEmployee()?->company_id;
        // if (!scopeAll()) {
        // $this->company = currentEmployee()?->company_id;
        //     if (scopeCompany())
        //         return;
        //     $this->region = currentEmployee()?->organizationInfo?->region_id;

        //     if (scopeRegion())
        //         return;
        //     $this->branch = currentEmployee()?->organizationInfo?->branch_id;
        //     if (scopeBranch())
        //         return;
        //     $this->department = currentEmployee()?->organizationInfo?->department_id;
        // }
        $this->multiSelectAttributes = ['employee_ids'];
        $this->withTrashed = true;
        $this->setPermissionForEmployeeDropdown();
        $this->setExcelOptions();
        $this->initialFilters();
    }

    public function initialFilters()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $startDate = "{$currentYear}-{$currentMonth}-01";
        $endDate = $nepaliDate;

        $this->dojStartDateId = $startDate;
        $this->dojEndDateId = $endDate;
        $this->terminationStartDateId = $startDate;
        $this->terminationEndDateId = $endDate;

        $this->dojStartDate = LaravelNepaliDate::from($this->dojStartDateId)->toEnglishDate();
        $this->dojEndDate = LaravelNepaliDate::from($this->dojEndDateId)->toEnglishDate();
        $this->terminationStartDate = LaravelNepaliDate::from($this->terminationStartDateId)->toEnglishDate();
        $this->terminationEndDate = LaravelNepaliDate::from($this->terminationEndDateId)->toEnglishDate();
    }
    public function updated($value)
    {
        if ($value == 'company') {
            $this->reset('branch', 'department', 'designation', 'unit', 'region', 'outsourceCompany', 'employeeStatus');
            unset($this->regions, $this->branches, $this->departments, $this->designations, $this->units, $this->outsourceCompanies, $this->employeeStatuses);
        }
        if (\in_array($value, ['company'])) {
            unset($this->branches, $this->departments);
            $this->branch = null;
            $this->department = null;
        }

        if ($value == 'region') {
            unset($this->branches);
            $this->reset('branch');
        }
        if ($value == 'department') {
            $this->reset('designation', 'unit');
            unset($this->designationList, $this->units);
        }
        if ($value == 'showTerminatedEmployee') {
            $this->reset('terminationFilter');
        }
    }

    private function listQuery()
    {
        $query = Employee::leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin("companies as comp", "comp.id", "=", "employees.company_id")
            ->leftJoin("branches as branch", "branch.id", "=", "employee_org.branch_id")
            ->leftJoin("departments as dep", "dep.id", "=", "employee_org.department_id")
            ->leftJoin("emp_statuses as emp_status", "emp_status.id", "=", "employee_org.employee_status_id")
            ->leftJoin("outsource_companies as outsource", "outsource.id", "=", "employee_org.outsource_company_id")
            ->leftJoin("designations as des", "des.id", "employee_org.designation_id")
            ->leftJoin("shifts as shift", "shift.id", "=", "employee_org.shift_id")
            ->leftJoin("users as user", "user.id", "=", "employees.user_id")
            ->leftJoin("units as unit", "unit.id", "=", "employee_org.unit_id")
            ->leftJoin("employee_terminations", 'employees.id', '=', 'employee_terminations.employee_id')
            ->when(count($this->employee_ids), function ($query) {
                $query->whereIn('employees.id', $this->employee_ids);
            })
            ->when($this->company, function ($query) {
                $query->where("employees.company_id", $this->company);
            })
            ->when($this->region, function ($query) {
                $query->where('employee_org.region_id', $this->region);
            })
            ->when($this->branch, function ($query) {
                $query->where("employee_org.branch_id", $this->branch);
            })
            ->when($this->department, function ($query) {
                $query->where("employee_org.department_id", $this->department);
            })
            ->when($this->unit, function ($query) {
                $query->where("employee_org.unit_id", $this->unit);
            })
            ->when($this->designation, function ($query) {
                $query->where("employee_org.designation_id", $this->designation);
            })
            ->when($this->outsourceCompany, function ($query) {
                $query->where("employee_org.outsource_company_id", $this->outsourceCompany);
            })
            ->when($this->employeeStatus, function ($query) {
                $query->where("employee_org.employee_status_id", $this->employeeStatus);
            })
            ->when($this->confirmed && $this->terminationFilter == null, function ($query) {
                $query->whereBetween('employee_org.doj', [$this->dojStartDate, $this->dojEndDate]);
            })
            ->when($this->terminationFilter != null, function ($query) {
                if ($this->terminationFilter == 1) {
                    $query->whereNotNull('employee_terminations.termination_request_date')
                        ->whereBetween('employee_terminations.termination_request_date', [$this->terminationStartDate, $this->terminationEndDate]);
                } elseif ($this->terminationFilter == 2) {
                    $query->withTrashed()
                        ->where('employee_terminations.state', 'Approved')
                        ->whereNotNull('employee_org.termination_date')
                        ->whereBetween('employee_org.termination_date', [$this->terminationStartDate, $this->terminationEndDate]);
                } elseif ($this->terminationFilter == 3) {
                    $query->where(function ($query) {
                        $query->where('employee_terminations.state', 'Approved')
                            ->whereNotNull('employee_terminations.termination_request_date')
                            ->whereBetween('employee_terminations.termination_request_date', [$this->terminationStartDate, $this->terminationEndDate])
                            ->orWhere(function ($subQuery) {
                                $subQuery->whereNotNull('employee_org.termination_date')
                                    ->whereBetween('employee_org.termination_date', [$this->terminationStartDate, $this->terminationEndDate]);
                            })->withTrashed();
                    });
                }
            })
            ->when($this->showTerminatedEmployee, function ($query) {
                $query->withTrashed()->distinct();
            })
            ->when(!$this->showTerminatedEmployee && $this->terminationFilter == null, function ($query) {
                $query->whereNull("employee_org.termination_date");
            })
            ->where(function ($query) {
                $query->where('employee_org.employee_code', 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            });
        $query = filterEmployeesByScope($query, 'employee_org');

        $query = $this->applySorting($query->select(
            'employees.id as id',
            Employee::selectNameRawQuery(),
            DB::raw("CONCAT(comp.code, '-', employee_org.employee_code) as emp_code"),
            'user.username as username',
            'employees.gender',
            'employees.dob',
            'employees.dob_nepali',
            'employees.mstat as marital_status',
            'employees.email',
            'employees.phone',
            'employees.nationality',
            'employees.citizenship',
            'employees.user_id',
            'employee_org.email as org_email',
            'employee_org.cug',
            'employees.permanent_address',
            'employees.temporary_address',
            'employees.father',
            'employees.mother',
            'employees.spouse',
            'employees.contact_phone',
            'employees.contact_person',
            'employee_org.doj as date_of_joining',
            'employee_org.doj_inhouse',
            'employee_org.bank',
            'employee_org.bank_account_no',
            'employee_org.rf_no',
            'employee_org.cit_no',
            'employee_org.pan_no',
            'employee_org.biometric_id',

            'emp_status.name as emp_status',
            'branch.name as branch_name',
            'dep.name as dep_name',
            'unit.name as unit_name',
            'des.title as designation',
            'outsource.name as outsource_company',
            'employee_org.deleted_at',
            'employee_org.termination_date as termination_date',
            'employee_org.termination_request_date as termination_request_date',
            'employee_terminations.termination_request_date as termination_request_dates',
            'employee_org.termination_reason as termination_reason',
            'shift.start_time as shift_start_time',
            'shift.end_time as shift_end_time',
        ));
        if (Schema::hasColumn('employee_org', 'iops_id')) {
            $query->addSelect('employee_org.iops_id as iops_id');
        } else {
            $query->addSelect(DB::raw('NULL as iops_id'));
        }
        return $query;
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Employee List.xlsx";
        $this->exportIgnoreColumns = ['id', 'user_id', 'deleted_at', 'termination_request_dates'];
        $this->exportColumnHeadersMap = [
            'full_name' => 'Employee Name',
            'emp_code' => 'Employee Code',
            'username' => 'Username',
            'email' => 'Email',
            'phone' => 'Phone',
            'org_email' => 'Organization Email',
            'cug' => 'CUG',
            'emp_status' => 'Status',
            'branch_name' => 'Branch',
            'dep_name' => "Department",
            'unit_name' => "Unit",
            'date_of_joining' => 'Date Of Joining (AD)',
            'date_of_joining_bs' => 'Date of Joining (BS)',
            'termination_date' => 'Termination Date (AD)',
            'termination_request_date' => 'Termination Request Date (AD)',
            'nep_termination_date' => 'Termination Date(BS)',
            'nep_termination_request_date' => 'Termination Request Date(BS)',
            'iops_id' => 'IOPS ID',

        ];
    }

    public function applyFilters()
    {
        $this->dojStartDate = LaravelNepaliDate::from($this->dojStartDateId)->toEnglishDate();
        $this->dojEndDate = LaravelNepaliDate::from($this->dojEndDateId)->toEnglishDate();
        $this->terminationStartDate = LaravelNepaliDate::from($this->terminationStartDateId)->toEnglishDate();
        $this->terminationEndDate = LaravelNepaliDate::from($this->terminationEndDateId)->toEnglishDate();
        $this->showDOJColumn = $this->confirmed;

        unset($this->list);
    }

    public function resetFilters()
    {
        $this->reset('company', 'branch', 'region', 'department', 'unit', 'designation', 'outsourceCompany', 'employeeStatus', 'showTerminatedEmployee');
        $this->confirmed = false;
        $this->showDOJColumn = $this->confirmed;
        $this->terminationFilter = null;
        $this->initialFilters();
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return (new ScopeFetcher())->fetchCompany();
    }

    #[Computed(persist: true)]
    public function regions()
    {
        return (new ScopeFetcher())->fetchRegion(company: $this->company, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function branches()
    {
        // if (scopeAll()) {
        //     return \App\Models\configs\Branch::orderBy('name', 'asc')->select("name", "id")->get();
        // } elseif (scopeCompany()) {
        //     return \App\Models\configs\Branch::where('company_id', $this->company)->orderBy('name', 'asc')->select("name", "id")->get();
        // } else {
        //     $query = \App\Models\configs\Branch::where('company_id', $this->company);

        //     if (!is_null($this->region)) {
        //         $query->where('region_id', $this->region);
        //     }

        //     return $query->orderBy('name', 'asc')->select("name", "id")->get();
        // }
        $this->region = $this->region == "" ? null : $this->region;
        return (new ScopeFetcher())->fetchBranch($this->company, $this->region);
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return (new ScopeFetcher())->fetchDepartment($this->company);
    }

    #[Computed(persist: true)]
    public function units()
    {
        return \App\Models\configs\Unit::where('department_id', $this->department)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function designationList()
    {
        if (!$this->department) return [];
        return \App\Models\configs\Department::findOrFail($this->department)->designations()
            ->orderBy('designations.title')
            ->pluck('designations.title', 'designations.id')->toArray();
    }

    #[Computed(persist: true)]
    public function outsourceCompanies()
    {
        return \App\Models\configs\OutsourceCompany::where('company_id', $this->company)
            ->orderBy('name', 'asc')
            ->pluck('name', 'id')
            ->toArray();
    }

    #[Computed(persist: true)]
    public function employeeStatuses()
    {
        return \App\Models\configs\EmpStatus::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function exportAllEmployees($exportAll = false)
    {
        if ($exportAll) {
            $data = $this->listQuery()->get();
        } else {
            $data = $this->{$this->tableListVariable}(); // Retrieve the data from the computed property

            if ($data instanceof LengthAwarePaginator) {
                $data = $data->getCollection();
            }
        }

        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings
        {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);
                $filteredItems = $items->map(function ($item) {
                    // Exclude ignored columns
                    $item = collect($item)->except($this->exportIgnoreColumns);

                    $bsDOJ = '';
                    $bsTerminationDate = '';
                    $bsTerminationRequestDate = '';

                    // Process date columns and add Nepali date
                    if ($item->has('date_of_joining')) {
                        $bsDOJ = $item['date_of_joining'] !== null
                            ? LaravelNepaliDate::from($item['date_of_joining'])->toNepaliDate()
                            : '';
                    }

                    if ($item->has('termination_date')) {
                        $bsTerminationDate = $item['termination_date'] !== null
                            ? LaravelNepaliDate::from($item['termination_date'])->toNepaliDate()
                            : '';
                    }

                    if ($item->has('termination_request_date')) {
                        $bsTerminationRequestDate = $item['termination_request_date'] !== null
                            ? LaravelNepaliDate::from($item['termination_request_date'])->toNepaliDate()
                            : '';
                    }

                    // Manipulate data order, inserting new columns
                    $itemArray = $item->toArray();
                    $positionedArray = [];

                    foreach ($itemArray as $key => $value) {
                        if ($key === 'iops_id' && is_null($value)) {
                            $positionedArray['iops_id'] = '';
                        } else {
                            $positionedArray[$key] = $value ?? '';
                        }
                    }

                    $positionedArray['date_of_joining_bs'] = $bsDOJ;
                    $positionedArray['nep_termination_date'] = $bsTerminationDate;
                    $positionedArray['nep_termination_request_date'] = $bsTerminationRequestDate;

                    // Rebuild the item collection with new columns
                    $item = collect($positionedArray);
                    return $item;
                });
                $priorityOrder = $this->headingOrder();

                $sorted = $filteredItems->map(function ($item) use ($priorityOrder) {
                    $sortedItem = [];
                    $item = (array) $item->toArray();
                    foreach ($priorityOrder as $key) {
                        if (array_key_exists($key, $item)) {
                            $sortedItem[$key] = $item[$key];
                        }
                    }
                    return $sortedItem;
                });
                return $sorted;
                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];

                // Provide headings for your export file
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }
                // Filter out ignored columns
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);


                $iopsIndex = array_search('Employee Code', $headings);
                $lastColumn = end($headings);
                if ($lastColumn === 'IOPS ID') {
                    array_pop($headings);
                    if ($iopsIndex !== false) {
                        array_splice($headings, $iopsIndex + 1, 0, 'IOPS ID');
                    }
                }
                $dateOfJoiningIndex = array_search('Date Of Joining (AD)', $headings);
                if ($dateOfJoiningIndex !== false) {
                    array_splice($headings, $dateOfJoiningIndex + 1, 0, 'Date of Joining (BS)');
                }

                $terminationDateIndex = array_search('Termination Date (AD)', $headings);
                $terminationRequestDateIndex = array_search('Termination Request Date (AD)', $headings);

                if ($terminationDateIndex !== false) {
                    array_splice($headings, $terminationDateIndex + 1, 0, 'Termination Date (BS)');
                }
                if ($terminationRequestDateIndex !== false) {
                    array_splice($headings, $terminationRequestDateIndex + 1, 0, 'Termination Request Date (BS)');
                }
                return $headings;
            }

            public function headingOrder(): array
            {
                $heading = [];

                // Provide heading for your export file
                if (is_array($this->data) && isset($this->data[0])) {
                    $heading = array_keys($this->data[0]);
                } else {
                    $heading = array_keys($this->data->first()->toArray() ?? []);
                }
                // Filter out ignored columns
                $heading = array_diff($heading, $this->exportIgnoreColumns);

                $iopsIndex = array_search('emp_code', $heading);
                $lastColumn = end($heading);
                if ($lastColumn === 'iops_id') {
                    array_pop($heading);
                    if ($iopsIndex !== false) {
                        array_splice($heading, $iopsIndex + 1, 0, 'iops_id');
                    }
                }
                $dateOfJoiningIndex = array_search('date_of_joining', $heading);
                if ($dateOfJoiningIndex !== false) {
                    array_splice($heading, $dateOfJoiningIndex + 1, 0, 'date_of_joining_bs');
                }

                $terminationDateIndex = array_search('termination_date', $heading);
                $terminationRequestDateIndex = array_search('termination_request_date', $heading);

                if ($this->data->isNotEmpty()) {
                    foreach ($this->data as $index => $record) {

                        $terminationDateAD = $record['termination_date'] ?? null;
                        $terminationRequestDateAD = $record['termination_request_date'] ?? null;

                        if (is_null($terminationDateAD)) {
                            $this->data[$index]['nep_termination_date'] = '';
                        }
                        if (is_null($terminationRequestDateAD)) {
                            $this->data[$index]['nep_termination_request_date'] = '';
                        }
                    }
                }

                if ($terminationDateIndex !== false) {
                    array_splice($heading, $terminationDateIndex + 1, 0, 'nep_termination_date');
                }
                if ($terminationRequestDateIndex !== false) {
                    array_splice($heading, $terminationRequestDateIndex + 1, 0, 'nep_termination_request_date');
                }
                return $heading;
            }
        }, $this->exportFileName);
    }

    #[Layout('layouts.app')]
    public function render()
    {
        return view('livewire.report.employee-list.employee-list');
    }
}
