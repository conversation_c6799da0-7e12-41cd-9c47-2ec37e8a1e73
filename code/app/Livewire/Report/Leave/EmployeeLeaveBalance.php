<?php

namespace App\Livewire\Report\Leave;

use App\Exports\EmployeeLeaveBalanceExport;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Leaves\EmployeeLeave;
use App\Models\Leaves\LeaveOption;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDateRangeFilter;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;
use Str;

#[Title('Employee Leave Balance')]
class EmployeeLeaveBalance extends Component
{
    use MultiselectEmployeeSearch, WithDataTable;

    public $employee_ids = [];
    public $leave_type_id;
    public $leave_option_id;
    public $fiscal_year_id;

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->setPermissionForEmployeeDropdown();
        $this->fiscal_year_id = currentFiscalYearId();
    }

    public function listQuery()
    {
        $query = DB::table('employee_leaves as el')
            ->leftJoin('employees as emp', 'emp.id', '=', 'el.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'emp.id')
            ->leftJoin('companies as comp', 'comp.id', '=', 'emp.company_id')
            ->leftJoin('fiscal_years as fy', 'fy.id', '=', 'el.fiscal_year_id')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'el.leave_type_id')
            ->selectRaw("
                 CASE
                WHEN emp.`middle_name` IS NULL OR TRIM(emp.`middle_name`) = '' THEN
                    CONCAT(TRIM(emp.`first_name`), ' ', TRIM(emp.`last_name`))
                ELSE
                    CONCAT(TRIM(emp.`first_name`), ' ', TRIM(emp.`middle_name`), ' ', TRIM(emp.`last_name`))
                END AS employee_name,
                CONCAT(comp.code,'-',org.employee_code) as code,
                fy.name as fiscal_year,
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                    'leave_type', lt.name,
                    'remaining', el.remaining_days,
                    'assigned', el.assigned_leave,
                    'pending', el.pending_leave,
                    'taken', el.leave_taken
                    )
                ) AS leaves
            ")
            ->groupBy(
                'emp.id',
                'emp.first_name',
                'emp.middle_name',
                'emp.last_name',
                'comp.code',
                'org.employee_code',
                'fy.id',
                'fy.name'
            );


        $query->when($this->employee_ids, fn($query) => $query->whereIn('emp.id', $this->employee_ids));
        $query->when($this->leave_type_id, fn($query) => $query->where('lt.id', $this->leave_type_id));
        $query->when($this->fiscal_year_id, fn($query) => $query->where('fy.id', $this->fiscal_year_id));

        return $this->applySorting($query);
    }

    #[Computed(persist: true)]
    public function list()
    {
        $result = $this->listQuery()->paginate($this->perPage);

        $transformed = $result->through(function ($item) {
            $item->leaves = json_decode($item->leaves, true);
            return $item;
        });

        return $transformed;
    }

    #[Computed(persist: true)]
    public function leaveTypeList()
    {
        $exceptions = [Constant::REPLACEMENT_LEAVE_NAME, Constant::UNPAID_LEAVE];
        return LeaveType::whereNotIn('name', $exceptions)->get();
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::orderBy('start_date', 'asc')->pluck("name", "id")->toArray();
    }

    public function updated($attr)
    {
        if (in_array($attr, ['leave_type_id', 'fiscal_year_id', 'employee_ids'])) {
            unset($this->list);
        }
    }

    public function exportAllData()
    {
        $data = $this->listQuery()->get();
        $fiscalYear = \Illuminate\Support\Str::slug(FiscalYear::find($this->fiscal_year_id)?->name ?? "");
        $fileName = "Employee Leave Balance - {$fiscalYear}.xlsx";

        return Excel::download(
            new EmployeeLeaveBalanceExport($data, $this->leaveTypeList),
            $fileName
        );
    }

    public function render()
    {
        return view('livewire.report.leave.employee-leave-balance');
    }
}
