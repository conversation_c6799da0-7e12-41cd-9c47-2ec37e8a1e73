<?php

namespace App\Livewire\Report\Leave;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Leaves\LeaveOption;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDateRangeFilter;
use App\Traits\WithDefaultFilter;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Employee Leave Record')]
class EmployeeLeaveRecord extends Component
{
    use WithDateRangeFilter, MultiselectEmployeeSearch, WithDefaultFilter;

    public $generate = false;

    public $employeeIds = [], $leaveTypes = [], $leaveOptions = [];
    public $filters = [
        'company'       => null,
        'employees'     => [],
        'branch'        => null,
        'department'    => null,
        'leave_types'   => [],
        'leave_options' => [],
        'startDate'     => '',
        'endDate'       => '',
    ];

    public function filter()
    {
        $this->validate();
        $this->filters = [
            'company'       => $this->filterCompanyId,
            'employees'     => $this->employeeIds,
            'branch'        => $this->filterBranchId,
            'department'    => $this->filterDepartmentId,
            'leave_types'   => $this->leaveTypes,
            'leave_options' => $this->leaveOptions,
            'startDate'     => $this->startDate,
            'endDate'       => $this->endDate,
        ];
        $this->generate = true;
    }

    public function clear()
    {
        $this->filterBranchId = null;
        $this->filterDepartmentId = null;
        $this->employeeIds = [];
        $this->setInitialFilters();
    }


    public function mount()
    {
        $this->multiSelectAttributes = ['employeeIds'];
        $this->withTrashed = true;
        $this->setInitialFilters();
    }

    public function setInitialFilters()
    {
        $laravelNepaliDate = new FacadesLaravelNepaliDate;
        $engDateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth();
        $this->startDate = LaravelNepaliDate::from($engDateRange['startdate'])->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from($engDateRange['enddate'])->toNepaliDate();

        $currEmployee = auth()->user()?->employee;
        $organizationInfo = $currEmployee?->organizationInfo;
        $this->setPermissionForEmployeeDropdown();

        $this->leaveTypes = array_keys($this->leaveTypeList);
        $this->leaveOptions = array_keys($this->leaveOptionList);
        if ($this->generate) $this->filter();
    }

    #[Computed(persist: true)]
    public function leaveTypeList()
    {
        return LeaveType::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function leaveOptionList()
    {
        return LeaveOption::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }


    public function updated($attr)
    {
        if (in_array($attr, ['filterBranchId', 'filterDepartmentId'])) {
            $this->employeeIds = [];
            $this->refreshEmployeeDropdown();
        }
        $this->updatedDefaultFilter($attr);
    }

    public function refreshEmployeeDropdown()
    {
        $this->employeeSearchFilter['branch_ids'] = $this->filterBranchId ? [$this->filterBranchId] : [];
        $this->employeeSearchFilter['department_ids'] = $this->filterDepartmentId ? [$this->filterDepartmentId] : [];
        $this->refreshEmployeeList();
    }

    public function render()
    {
        return view('livewire.report.leave.employee-leave-record');
    }
}
