<?php

namespace App\Livewire\Report\Leave;

use App\Models\Employee\Employee;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Modelable;
use PermissionList;
use PowerComponents\LivewirePowerGrid\Column;
use PowerComponents\LivewirePowerGrid\Exportable;
use PowerComponents\LivewirePowerGrid\Footer;
use PowerComponents\LivewirePowerGrid\Header;
use PowerComponents\LivewirePowerGrid\PowerGrid;
use PowerComponents\LivewirePowerGrid\PowerGridFields;
use PowerComponents\LivewirePowerGrid\PowerGridComponent;
use PowerComponents\LivewirePowerGrid\Traits\WithExport;

use function Laravel\Prompts\select;

final class EmployeeLeaveRecordTable extends PowerGridComponent
{
    use WithExport;

    public bool $showFilters = true;
    // public bool $deferLoading = true;

    public string $checkboxAttribute = 'eld_id';
    public string $radioAttribute = 'eld_id';

    public string $primaryKey = 'eld.id';

    public string $sortField = 'eld.date';
    public string $sortDirection = 'asc';

    #[Modelable]
    public $customFilters = [
        'company'       => null,
        'employees'     => [],
        'branch'        => null,
        'department'    => null,
        'leave_types'   => [],
        'leave_options' => [],
        'startDate'     => '',
        'endDate'       => '',
    ];

    public function boot()
    {
        $this->resetPage();
    }

    public function updatedCustomFilters()
    {
        $this->fillData();
    }

    public function setUp(): array
    {
        $this->showCheckBox('eld_id');

        return [
            Exportable::make("Employee Leave Record")
                ->type(Exportable::TYPE_XLS, Exportable::TYPE_CSV)
            // ->queues(1)
            // ->onQueue('reports')
            // ->onConnection('redis')
            ,
            Header::make(),
            Footer::make()
                ->showPerPage()
                ->showRecordCount(),
        ];
    }

    public function datasource(): Builder
    {
        $dataSource =  DB::table('employee_leave_details as eld')
            ->leftJoin('leave_types as lt', 'lt.id', '=', 'eld.leave_type_id')
            ->leftJoin('leave_options as lo', 'lo.id', '=', 'eld.leave_option_id')
            ->leftJoin('fiscal_years as fy', 'fy.id', '=', 'eld.fiscal_year_id')
            ->leftJoin('employees as emp', 'emp.id', '=', 'eld.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'emp.id')
            ->leftJoin('departments as dept', 'org.department_id', '=', 'dept.id')
            ->leftJoin('branches as brch', 'org.branch_id', '=', 'brch.id')
            ->leftJoin('companies as company', 'company.id', '=', 'emp.company_id')
            ->when(count($this->customFilters['employees']), function ($query) {
                $query->whereIn('emp.id', $this->customFilters['employees']);
            })
            ->when($companyId = $this->customFilters['company'], fn($query) => $query->where('emp.company_id', $companyId))
            ->when($departmentId = $this->customFilters['department'], fn($query) => $query->where('dept.id', $departmentId))
            ->when($branchId = $this->customFilters['branch'], fn($query) => $query->where('brch.id', $branchId))
            ->whereIn('eld.leave_type_id', $this->customFilters['leave_types'])
            ->whereIn('eld.leave_option_id', $this->customFilters['leave_options'])
            ->where(function ($query) {
                $startDate = LaravelNepaliDate::from($this->customFilters['startDate'])->toEnglishDate();
                $endDate = LaravelNepaliDate::from($this->customFilters['endDate'])->toEnglishDate();
                $endDate = Carbon::parse($endDate)->endOfDay();
                $query->whereBetween('eld.date', [$startDate, $endDate]);
            });

        $dataSource = filterEmployeesByScope($dataSource, 'org', 'emp');

        $dataSource = $dataSource->select(
            DB::raw(Employee::selectEmpCodeRawQuery()), // emp_code
            'eld.id as eld_id',
            'eld.date as date',
            'eld.nep_date as nep_date',
            'eld.num_days as num_days',
            'eld.replaced_date as replaced_date',
            'eld.remarks as remarks',
            'eld.performers as performers',
            'lt.name as leave_type',
            'lo.name as leave_option',
            'fy.name as fiscal_year',
            'dept.name as department',
            'brch.name as branch',
            DB::raw("TRIM(CONCAT_WS(' ',emp.first_name, NULLIF(emp.middle_name, ''), emp.last_name)) as employee"),
            'company.name as company',
        );
        // dd($dataSource->toSql());
        return $dataSource;
    }

    public function relationSearch(): array
    {
        return [];
    }

    public function fields(): PowerGridFields
    {
        return PowerGrid::fields()
            ->add('eld_id')
            ->add('employee', fn($row) => $row->employee . ' [' . $row->emp_code . ']')
            ->add('employee_name', fn($row) => $row->employee)
            ->add('emp_code', fn($row) => $row->emp_code)
            ->add('eld.date')
            ->add('year', function ($model) {
                return LaravelNepaliDate::from(Carbon::parse($model->date))->toNepaliDate(format: 'Y', locale: 'en');
            })
            ->add('month', function ($model) {
                return LaravelNepaliDate::from(Carbon::parse($model->date))->toNepaliDate(format: 'F', locale: 'en');
            })
            ->add('nep_date', function ($model) {
                return LaravelNepaliDate::from($model->date)->toNepaliDate();
            })
            ->add('num_days')
            ->add('replaced_date', fn($model) => $model->replaced_date ? LaravelNepaliDate::from($model->replaced_date)->toNepaliDate() : "N/A")
            ->add('remarks')
            ->add('leave_type')
            ->add('leave_option')
            ->add('num_days')
            ->add('fiscal_year')
            ->add('department')
            ->add('branch')
            ->add('verified_by', function ($model) {
                $performers = \convertJsonToArray($model->performers);
                return $performers['verified_by'] ?? "";
            })
            ->add('approved_by', function ($model) {
                $performers = \convertJsonToArray($model->performers);
                return $performers['approved_by'] ?? "";
            });
    }

    public function columns(): array
    {
        return [
            Column::make('Employee', 'employee', 'emp.first_name')->sortable()->visibleInExport(false),
            Column::make('Employee Name', 'employee_name')->hidden()->visibleInExport(true),
            Column::make('Employee Code', 'emp_code')->hidden()->visibleInExport(true),
            Column::make('Branch', 'branch', "brch.name")->sortable(),
            Column::make('Department', 'department', "dept.name")->sortable(),
            Column::make('Year', 'year'),
            Column::make('Month', 'month'),
            Column::make('Date', 'nep_date', 'eld.date')->sortable(),
            Column::make('Leave Type', 'leave_type', 'lt.name')->sortable(),
            Column::make('Leave Option', 'leave_option', 'lo.name')->sortable(),
            Column::make('No. of Days', 'num_days', 'eld.num_days')->sortable(),
            Column::make('Replaced Date', 'replaced_date', 'eld.replaced_date')->sortable(),
            Column::make('Remarks', 'remarks', 'eld.remarks')->sortable(),
            Column::make('Verified By', 'verified_by'),
            Column::make('Approved By', 'approved_by'),
        ];
    }

    // public function filters(): array
    // {
    //     return [
    //         Filter::datepicker('date'),
    //         Filter::datepicker('replaced_date'),
    //     ];
    // }

    #[\Livewire\Attributes\On('edit')]
    public function edit($rowId): void
    {
        $this->js('alert(' . $rowId . ')');
    }

    /*
    public function actionRules($row): array
    {
       return [
            // Hide button edit for ID 1
            Rule::button('edit')
                ->when(fn($row) => $row->id === 1)
                ->hide(),
        ];
    }
    */
}
