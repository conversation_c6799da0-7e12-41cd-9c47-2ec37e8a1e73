<?php

namespace App\Livewire\Report\Salary;

use App\Http\Repositories\Reports\SalaryVarianceRepository;
use Livewire\Attributes\Modelable;
use PowerComponents\LivewirePowerGrid\Column;
use PowerComponents\LivewirePowerGrid\Exportable;
use PowerComponents\LivewirePowerGrid\Footer;
use PowerComponents\LivewirePowerGrid\Header;
use PowerComponents\LivewirePowerGrid\PowerGridComponent;
use PowerComponents\LivewirePowerGrid\PowerGridFields;
use PowerComponents\LivewirePowerGrid\Traits\WithExport;

final class SalaryVarianceTable extends PowerGridComponent
{
    use WithExport;

    public bool $showFilters = true;

    public string $checkboxAttribute = 'id';
    public string $radioAttribute = 'id';

    public string $sortField = 'id';
    public string $sortDirection = 'asc';

    #[Modelable]
    public $customFilters = [
        'company' => null,
        'branch' => null,
        'department' => null,
        'status' => [],
        'year' => [],
        'month' => '',
    ];
    private SalaryVarianceRepository $salaryVarianceRepo;

    public function __construct()
    {
        $this->salaryVarianceRepo = new SalaryVarianceRepository();
    }

    public function boot()
    {
        $this->resetPage();
    }

    public function updatedCustomFilters()
    {
        $this->fillData();
    }

    public function setUp(): array
    {
        $this->showCheckBox('id');

        return [
            Exportable::make("Salary Variance Report")
                ->type(Exportable::TYPE_XLS, Exportable::TYPE_CSV)
            ,
            Header::make(),
            Footer::make()
                ->showPerPage()
                ->showRecordCount(),
        ];
    }

    public function datasource(): array
    {
        return $this->salaryVarianceRepo->listQuery($this->customFilters);
    }

    public function relationSearch(): array
    {
        return [];
    }

    public function fields(): PowerGridFields
    {
    }

    public function columns(): array
    {
        return [
            Column::make('Branch', 'branch', "brch.name")->sortable(),
            Column::make('Department', 'department', "dept.name")->sortable(),
            Column::make('Employee', 'employee', 'emp.first_name')->sortable(),
            Column::make('Year', 'year'),
            Column::make('Month', 'month'),
            Column::make('Date', 'nep_date', 'eld.date')->sortable(),
            Column::make('Leave Type', 'leave_type', 'lt.name')->sortable(),
            Column::make('Leave Option', 'leave_option', 'lo.name')->sortable(),
            Column::make('No. of Days', 'num_days', 'eld.num_days')->sortable(),
            Column::make('Replaced Date', 'replaced_date', 'eld.replaced_date')->sortable(),
            Column::make('Remarks', 'remarks', 'eld.remarks')->sortable(),
            Column::make('Verified By', 'verified_by'),
            Column::make('Approved By', 'approved_by'),
        ];
    }

    #[\Livewire\Attributes\On('edit')]
    public function edit($rowId): void
    {
        $this->js('alert(' . $rowId . ')');
    }
}
