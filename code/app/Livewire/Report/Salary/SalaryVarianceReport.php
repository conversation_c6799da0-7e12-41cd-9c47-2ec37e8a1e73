<?php

namespace App\Livewire\Report\Salary;

use App\Http\Helpers\Constant;
use App\Http\Repositories\Reports\SalaryVarianceRepository;
use App\Http\Services\ScopeFetcher;
use App\Models\configs\OutsourceCompany;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDateRangeFilter;
use App\Traits\WithNotify;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Facades\Excel;

#[Title("Salary Variance Report")]
class SalaryVarianceReport extends Component
{
    use WithDateRangeFilter, MultiselectEmployeeSearch, WithDataTable, WithNotify;

    public ?string $departmentId = null;
    public ?string $branchId = null;
    public ?string $companyId = null;
    public ?string $vendorId = null;
    public ?string $selectedMonth = "";
    public ?string $selectedYear = "";
    public array $yearList = [];
    public ?string $statusId = "";
    public array $year;
    public array $month;
    /**
     * @var string[]
     */
    public array $sumFields;
    private SalaryVarianceRepository $salaryRepo;
    public bool $generate = false;

    public function rules()
    {
        return [
            'selectedYear' => 'required',
            'selectedMonth' => 'required',
        ];
    }

    public function __construct()
    {
        $this->salaryRepo = new SalaryVarianceRepository();
    }

    public function clear()
    {
        $this->reset(['companyId', 'branchId', 'departmentId', 'departmentId', 'statusId', 'selectedYear', 'selectedMonth', 'generate', 'vendorId']);
        $this->setInitialFilters();
    }


    public function mount()
    {
        $this->withTrashed = true;
        $this->setInitialFilters();
        $this->scopeWiseFilters();
        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }
    }

    public function scopeWiseFilters()
    {
        $this->companyId = $this->branchId = $this->departmentId = null;

        if(!checkScope(\App\Http\Helpers\Constant::DEPARTMENT))
            $this->departmentId = currentEmployee()?->organizationInfo?->department_id;

        if(!checkScope(\App\Http\Helpers\Constant::BRANCH))
            $this->branchId = currentEmployee()?->organizationInfo?->branch_id;

        if(!checkScope(\App\Http\Helpers\Constant::COMPANY))
            $this->companyId = currentEmployee()?->company_id;
    }

    public function setInitialFilters()
    {
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return (new ScopeFetcher())->fetchCompany("id_name");
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return (new ScopeFetcher())->fetchBranch(company:$this->companyId, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment(company:$this->companyId, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ["Inhouse", "Outsource"];
    }

    #[Computed(persist: true)]
    public function vendorList()
    {
        return OutsourceCompany::orderBy('name', 'asc')->where('company_id', $this->companyId)->pluck("name", "id")->toArray();
    }

    public function updated($attr)
    {
        if (in_array($attr, ['companyId'])) {
            unset($this->branchList, $this->departmentList, $this->vendorList);
        }
        $this->dispatch('reloadTooltip');
    }

    public function render()
    {
        return view('livewire.report.salary.salary-variance-report');
    }

    public function exportSalaryVarianceExport()
    {
        $this->validate();
        $data = $this->salaryRepo->getVarianceData(filters: $this->all(), array: true);
        if (count($data) == 0) {
            $this->notify('No Data Found.')->type('error')->send();
            return;
        }

        $this->exportIgnoreColumns = ['id','company_id','branch_id','department_id','vendor_id','payment_id'];
        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;
            private $groupedFields = [];

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                return collect($this->data)->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns)->map(function ($value) {
                        return $value === 0 || $value === 0.0 ? '0' : $value;
                    });
                });
            }

            public function headings(): array
            {
                $firstRow = collect($this->data)->first();
                if (!$firstRow) return [];

                $groupRow = [];
                $fieldRow = [];
                $groupMap = [];

                foreach ($firstRow as $key => $value) {
                    if (in_array($key, $this->exportIgnoreColumns)) continue;

                    if (preg_match('/^(.*?)_(current|previous|diff)$/', $key, $matches)) {
                        $group = $matches[1];
                        $groupMap[$group][] = $key;
                    } else {
                        $groupMap[$key] = [$key];
                    }
                }

                $this->groupedFields = $groupMap;

                foreach ($groupMap as $group => $fields) {
                    $colspan = count($fields);
                    $label = $this->exportColumnHeadersMap[$group] ?? ucfirst(str_replace('_', ' ', $group));

                    if ($colspan === 1) {
                        $groupRow[] = $label;
                        $fieldRow[] = '';
                    } else {
                        foreach ($fields as $field) {
                            $groupRow[] = $label;
                        }
                        foreach ($fields as $field) {
                            $subLabel = ucfirst(str_replace('_', ' ', str_replace($group . '_', '', $field)));
                            $fieldRow[] = $this->exportColumnHeadersMap[$field] ?? $subLabel;
                        }
                    }
                }

                return [$groupRow, $fieldRow]; // No empty row here
            }

            public function registerEvents(): array
            {
                return [
                    AfterSheet::class => function (AfterSheet $event) {
                        $sheet = $event->sheet;
                        $worksheet = $sheet->getDelegate();

                        // Shift everything 1 row down to make space for TOTAL
                        $worksheet->insertNewRowBefore(1, 1);

                        // Add TOTAL label
                        $worksheet->setCellValue('A1', 'TOTAL');

                        // Compute totals
                        $columnIndex = 1;
                        $dataRowStart = 4; // Headings are now rows 2 and 3; data starts at 4
                        $dataRowEnd = $dataRowStart + count($this->data) - 1;
                        $sumFields = ["gross", "ctc", "net", "before_attn"];

                        foreach ($this->groupedFields as $group => $fields) {
                            foreach ($fields as $field) {
                                $isNumeric = is_numeric(collect($this->data)->first()[$field] ?? null) && in_array($group, $sumFields);
                                if ($isNumeric) {
                                    $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex);
                                    $formula = "=SUBTOTAL(9, {$colLetter}{$dataRowStart}:{$colLetter}{$dataRowEnd})";
                                    $worksheet->setCellValue("{$colLetter}1", $formula);
                                }
                                $columnIndex++;
                            }
                        }

                        // Merge and style header
                        $columnIndex = 1;
                        foreach ($this->groupedFields as $group => $fields) {
                            $colStart = $columnIndex;
                            $colEnd = $columnIndex + count($fields) - 1;

                            $colLetterStart = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colStart);
                            $colLetterEnd = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colEnd);

                            if (count($fields) === 1) {
                                $sheet->mergeCells("{$colLetterStart}2:{$colLetterStart}3");
                            } else {
                                $sheet->mergeCells("{$colLetterStart}2:{$colLetterEnd}2");
                            }

                            $columnIndex += count($fields);
                        }

                        $lastCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex - 1);

                        // Style headers and totals
                        $sheet->getStyle("A1:{$lastCol}1")->applyFromArray([
                            'font' => ['bold' => true],
                            'fill' => [
                                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                'startColor' => ['argb' => 'FFEFEFEF'],
                            ],
                            'alignment' => ['horizontal' => 'center', 'vertical' => 'center'],
                            'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]],
                        ]);

                        $sheet->getStyle("A2:{$lastCol}3")->applyFromArray([
                            'font' => ['bold' => true],
                            'alignment' => ['horizontal' => 'center', 'vertical' => 'center'],
                            'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]],
                        ]);
                    },
                ];
            }
        }, "Salary Variance Report.xlsx");
    }

    public function preview()
    {
        $this->generate = true;
        unset($this->list);
    }

    #[Computed()]
    public function list()
    {
        $this->validate();
        $query = $this->salaryRepo->getVarianceData($this->all());
        
        $query = $this->applySorting($query);
        
        return $query->paginate($this->perPage);
    }
}



  
