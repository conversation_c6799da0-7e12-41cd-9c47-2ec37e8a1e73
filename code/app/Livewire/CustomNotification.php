<?php

namespace App\Livewire;

use App\Http\Repositories\Configs\Interfaces\ICustomNotificationRepository;
use App\Models\configs\Company;
use App\Models\configs\Region;
use App\Models\Employee\Employee;
use App\Traits\WithDataTable;
use Livewire\Component;
use Illuminate\Support\Collection;
use Livewire\Attributes\Computed;

class CustomNotification extends Component
{
    use WithDataTable;

    public $targetFilter = null;
    public $statusFilter = null;
    public $selectedId = null;

    #[Computed(persist: true)]
    public function list()
    {
        return app(ICustomNotificationRepository::class)->getNotificationsList([
            'perPage' => $this->perPage,
            'page' => $this->getPage(),
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
        ]);
    }

    public function updating($attr)
    {
        if (in_array($attr, ['targetFilter', 'statusFilter'])) {
            unset($this->list);
        }
    }

    public function viewDetail($id)
    {
        $this->selectedId = $id;
    }

    public function retry($id)
    {
        dd("Retry not implemented", $id);
    }

    public function render()
    {
        return view('livewire.custom-notification');
    }
}
