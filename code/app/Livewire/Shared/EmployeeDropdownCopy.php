<?php

namespace App\Livewire\Shared;

use App\Models\Employee\Employee;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Component;

class EmployeeDropdownCopy extends Component
{
    public $search = "";

    #[Modelable]
    public $employeeId = "";

    #[Reactive]
    public $errors = ""; // props; form errors

    public $placeholder = ""; // props, placeholder text
    public $label = "Employee"; // props, form label
    public $required = false; // props, from required
    public $emptyMessage = "Employee Not Found"; // props, message when no employee found

    public $designationRole = ""; // props
    public $showBelowLevel = ""; // props
    public $showFromBranchId = ""; // props
    public $showFromDepartmentId = ""; // props
    public $showFromSupervisorId = ""; // props

    public $buttonValue = "";

    #[Computed(persist: true)]
    public function employeeList()
    {
        // $designationIds = Designation::where('role', $this->designationRole)->pluck('id'); // To show employee with specific designation.
        return Employee::list([
            "branch_ids"     => $this->showFromBranchId ? [$this->showFromBranchId] : [],
            "department_ids" => $this->showFromDepartmentId ? [$this->showFromDepartmentId] : [],
            "supervisor_ids" => $this->showFromSupervisorId ? [$this->showFromSupervisorId] : [],
            "level"          => $this->showBelowLevel,
        ]);
    }

    public function unsetEmployeeDropdownList()
    {
        unset($this->employeeList);
    }

    #[On('employee_id-changed')]
    public function employeeIdChange($value)
    {
        $this->employeeId = $value;
    }

    public function render()
    {
        return view('livewire.shared.employee-dropdown-copy');
    }
}
