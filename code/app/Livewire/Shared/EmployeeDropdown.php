<?php

namespace App\Livewire\Shared;

use App\Models\Employee\Employee;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Component;

class EmployeeDropdown extends Component
{
    public $search = "";

    #[Reactive]
    public $value = ""; // props; pass the employee id

    #[Reactive]
    public $errors = ""; // props; form errors

    public $placeholder = ""; // props, placeholder text
    public $label = "Employee"; // props, form label
    public $required = false; // props, from required
    public $emptyMessage = "Employee Not Found"; // props, message when no employee found

    public $designationRole = ""; // props
    public $showBelowLevel = ""; // props
    public $showFromBranchId = ""; // props
    public $showFromDepartmentId = ""; // props
    public $showFromSupervisorId = ""; // props

    public $buttonValue = "";
    public $selectedId = null;

    #[Computed(persist: true)]
    public function employeeList()
    {
        // $designationIds = Designation::where('role', $this->designationRole)->pluck('id'); // To show employee with specific designation.
        $designationIds = collect();
        $employee_org_alias = "org";
        $result =
            Employee::whereHas('organizationInfo')
            ->leftJoin("employee_org as {$employee_org_alias}", "{$employee_org_alias}.employee_id", "=", "employees.id")
            ->where(
                fn ($query) =>
                $query->where('employees.id', 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere("{$employee_org_alias}.employee_code", 'like', "%{$this->search}%")
                    ->orWhere('employees.email', 'like', "%{$this->search}%")
            )
            ->when($this->showFromBranchId, fn ($query) => $query->where("{$employee_org_alias}.branch_id", '=', $this->showFromBranchId))
            ->when($this->showFromDepartmentId, fn ($query) => $query->where("{$employee_org_alias}.department_id", '=', $this->showFromDepartmentId))
            ->when($this->showFromSupervisorId, fn ($query) => $query->where("{$employee_org_alias}.supervisor_id", '=', $this->showFromSupervisorId))
            ->when($this->showBelowLevel, fn ($query) => $query->byDesignationLevel($employee_org_alias, $this->showBelowLevel))
            ->when($designationIds->isNotEmpty(), function ($query) use ($employee_org_alias, $designationIds) {
                $query->whereIn("{$employee_org_alias}.designation_id", $designationIds);
            })
            ->whereNull("{$employee_org_alias}.termination_date")
            ->select(
                "employees.id",
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                "org.employee_code as employee_code"
            )->take(5)->get();

        return $result;
    }

    #[On('unsetEmployeeDropdownList')]
    public function unsetEmployeeDropdownList()
    {
        unset($this->employeeList);
    }

    public function updatedSearch()
    {
        $this->unsetEmployeeDropdownList();
    }

    /** function for showing the employee name on the button when employee is selected */
    #[Computed()]
    public function getEmployeeName()
    {
        $this->selectedId = $this->value; // since above line not working putting it here. 
        if ($this->selectedId) {
            $employee = Employee::find($this->selectedId);
            if ($employee)
                return $employee->name;
        }
        return $this->placeholder;
    }

    /** function for setting the employee id when certain option is selected */
    public function setEmployee($id)
    {
        $this->selectedId = $id;
        $this->dispatch("employee-dropdown-select", $id);
    }

    public function render()
    {
        return view('livewire.shared.employee-dropdown');
    }
}
