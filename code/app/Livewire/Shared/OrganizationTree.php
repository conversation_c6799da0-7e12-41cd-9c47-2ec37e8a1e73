<?php

namespace App\Livewire\Shared;

use App\Models\Employee\EmployeeOrg;
use Livewire\Attributes\On;
use Livewire\Component;

class OrganizationTree extends Component
{
    public $employeeId;
    public $supervisors = [];
    public $currentEmployee = [];
    public $subordinates = [];

    public function mount($employeeId)
    {
        $this->employeeId = $employeeId;
        $this->loadEmployees();
    }

    #[On('employeeId-Dispatch')]
    public function employeeIdDispatch($employeeId)
    {
        $this->employeeId = $employeeId;
        $this->loadEmployees();
    }

    public function updatedEmployeeId($value)
    {
        $this->employeeId = $value;
        $this->loadEmployees();
    }

    public function loadEmployees()
    {
        $this->supervisors = [];
        $this->currentEmployee = [];
        $this->subordinates = [];

        if (!$this->employeeId) {
            return;
        }

        $employeeOrg = EmployeeOrg::with([
            'employee',
            'branch',
            'designation',
            'department',
            'supervisor',
            'supervisor.branch',
            'supervisor.department',
            'supervisor.designation',
            'supervisor.organizationInfo'
        ])
            ->where('employee_id', $this->employeeId)
            ->whereNull('termination_date')
            ->first();

        if (!$employeeOrg) {
            return;
        }

        if ($employeeOrg->supervisor) {
            $sup = $employeeOrg->supervisor;
            $this->supervisors[] = [
                'id'  => $sup->id,
                'employee_name' => $sup?->name,
                'employee_code' => $sup->organizationInfo?->company_employee_code,
                'designation'   => $sup->designation?->title,
                'department'    => $sup->department?->name ?? '',
                'subordinate_count' => EmployeeOrg::where('supervisor_id', $sup->id)->count(),
                'is_current'    => false,
                'gender' => $sup?->gender ?? '',
                'picture' => $sup?->profile_picture
            ];
        }

        $this->currentEmployee[] = [
            'id'  => $employeeOrg?->employee_id,
            'employee_name' => $employeeOrg->employee?->name,
            'employee_code' => $employeeOrg?->company_employee_code,
            'designation'   => $employeeOrg->designation?->title,
            'department'    => $employeeOrg->department?->name ?? '',
            'subordinate_count' => EmployeeOrg::where('supervisor_id', $employeeOrg->employee_id)->count(),
            'is_current'    => true,
            'gender' => $employeeOrg->employee?->gender ?? '',
            'picture' => $employeeOrg->employee?->profile_picture
        ];

        $this->subordinates = EmployeeOrg::with(['employee', 'branch', 'designation', 'department'])
            ->where('supervisor_id', $employeeOrg->employee_id)
            ->get()
            ->map(function ($sub) {
                return [
                    'id'  => $sub->employee_id,
                    'employee_name' => $sub->employee?->name,
                    'employee_code' => $sub?->company_employee_code,
                    'designation'   => $sub?->designation?->title,
                    'department'    => $sub?->department->name ?? '',
                    'subordinate_count' => EmployeeOrg::where('supervisor_id', $sub->employee_id)->count(),
                    'is_current'    => false,
                    'gender' => $sub->employee?->gender ?? '',
                    'picture' => $sub->employee?->profile_picture

                ];
            })
            ->toArray();
    }

    public function selectEmployee($employeeId)
    {
        $this->employeeId = $employeeId;
        $this->loadEmployees();
        $this->dispatch('employeeSelected', employeeId: $employeeId);
    }

    public function render()
    {
        return view('livewire.shared.organization-tree');
    }
}
