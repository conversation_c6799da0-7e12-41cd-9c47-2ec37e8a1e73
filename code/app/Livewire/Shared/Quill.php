<?php

namespace App\Livewire\Shared;

use Livewire\Attributes\Modelable;
use Livewire\Component;

class Quill extends Component
{
    // #[Modelable]
    public $value;

    public $quillId;

    public function mount($value = '')
    {
        $this->value = $value;
        $this->quillId = 'quill-' . uniqid();
    }

    public function render()
    {
        return view('livewire.shared.quill');
    }
}
