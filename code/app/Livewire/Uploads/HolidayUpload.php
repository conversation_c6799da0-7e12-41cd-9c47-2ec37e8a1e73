<?php

namespace App\Livewire\Uploads;

use App\Contracts\ExcelImportComponentAbstract;
use Livewire\Component;

class HolidayUpload extends ExcelImportComponentAbstract
{

    public function headers(): array
    {
        return [
            "Holiday Name"  => "Maghi",
            "Date (A.D)"    => 'mm/dd/yyyy or yyyy-mm-dd',
            "Gender"        => "all, male, female, other",
            "Rate"          => "Rate",
            "Remarks"       => "Only for certain branches"
        ];
    }

    public function requiredHeaders(): array
    {
        return ["Holiday Name", "Date (A.D)", "Gender", "Rate"];
    }

    public function headerMap(): array
    {
        return [
            "Holiday Name" => "name",
            "Date (A.D)" => "date eng"
        ];
    }

    public function getDateFields(): array
    {
        return ['Date (A.D)'];
    }

    protected function getSampleFileName(): string
    {
        return 'Holiday Sample.xlsx';
    }

    protected function getExportOutputFileName(): string
    {
        return 'Holiday Validation Output.xlsx';
    }
    protected function getHandlerType(): string
    {
        return 'holiday';
    }

    public function render()
    {
        return view('livewire.uploads.holiday-upload');
    }
}
