<?php

namespace App\Livewire\Uploads;

use App\Contracts\ExcelImportComponentAbstract;
use Livewire\Component;

class ShiftUpload extends ExcelImportComponentAbstract
{
    public function headers(): array
    {
        return [
            "Shift Name"         => "Regular",
            "Type"               => "regular, halfday or overnight",
            "Start Time"         => "09:30:00 AM",
            "End Time"           => "05:30:00 PM",
            "Leisure Start Time" => "01:00:00 PM",
            "Leisure End Time"   => "02:00:00 PM",
            "Grace Time(mins)"   => "15",
            "Day Off"            => "Saturday"
        ];
    }

    public function requiredHeaders(): array
    {
        return array_keys($this->headers());
    }

    public function headerMap(): array
    {
        return [
            "Shift Name" => "name",
            "Type" => "type",
            "Start Time" => "start_time",
            "End Time" => "end_time",
            "Leisure Start Time" => "leisure_start_time",
            "Leisure End Time" => "leisure_end_time",
            "Grace Time(mins)" => "grace_time",
            "Day Off" => "day_off"
        ];
    }

    public function getTimeFields(): array
    {
        return ['Start Time', 'End Time', 'Leisure Start Time', 'Leisure End Time'];
    }

    public function getOptionalExcelHeaders(): array
    {
        return [];
    }

    protected function getSampleFileName(): string
    {
        return 'Shift Sample.xlsx';
    }

    protected function getExportOutputFileName(): string
    {
        return 'Shift Validation Output.xlsx';
    }
    protected function getHandlerType(): string
    {
        return 'shift';
    }

    public function render()
    {
        return view('livewire.uploads.shift-upload');
    }
}
