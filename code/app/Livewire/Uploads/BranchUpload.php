<?php

namespace App\Livewire\Uploads;

use App\Contracts\ExcelImportComponentAbstract;
use Livewire\Component;

class BranchUpload extends ExcelImportComponentAbstract
{

    public function headers(): array
    {
        return [
            "Branch Name" => "Kathmandu",
            "Address" => "<PERSON><PERSON><PERSON><PERSON>, Kathmandu",
            "Branch Code" => "BHKT",
            "Mobile No." => '9800000000',

        ];
    }

    public function requiredHeaders(): array
    {
        return ["Branch Name", "Address", "Branch Code"];
    }

    public function headerMap(): array
    {
        return [
            "Branch Name"   => "name",
            "Branch Code"   => "branch code",
            "Mobile No."    => 'phone',
        ];
    }

    protected function getSampleFileName(): string
    {
        return 'Branch Sample.xlsx';
    }

    protected function getExportOutputFileName(): string
    {
        return 'Branch Validation Output.xlsx';
    }
    protected function getHandlerType(): string
    {
        return 'branch';
    }

    public function render()
    {
        return view('livewire.uploads.branch-upload');
    }
}
