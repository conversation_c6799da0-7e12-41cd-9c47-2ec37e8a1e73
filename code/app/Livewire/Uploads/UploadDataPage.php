<?php

namespace App\Livewire\Uploads;

use Livewire\Attributes\Computed;
use Livewire\Component;
use PermissionList;

class UploadDataPage extends Component
{

    #[Computed(persist: true)]
    public function uploadMenus()
    {
        $user = auth()->user();
        return [
            [
                'label' => 'Department',
                'icon' => 'bi bi-building',
                'href' => route('uploads.department'),
                'condition' => $user->can(PermissionList::DEPARTMENT_UPLOAD)
                // 'condition' => true
            ],
            [
                'label' => 'Branch',
                'icon' => 'bi bi-geo-alt',
                'href' => route('uploads.branch'),
                'condition' => $user->can(PermissionList::BRANCH_UPLOAD)
                // 'condition' => true
            ],
            [
                'label' => 'Shift',
                'icon' => 'bi bi-clock-history',
                'href' => route('uploads.shift'),
                'condition' => $user->can(PermissionList::SHIFT_UPLOAD)
                // 'condition' => true
            ],
            [
                'label' => 'Holiday',
                'icon' => 'bi bi-calendar-event',
                'href' => route('uploads.holiday'),
                'condition' => $user->can(PermissionList::HOLIDAY_UPLOAD)
                // 'condition' => true
            ],
            [
                'label' => 'Employee',
                'icon' => 'bi bi-person-workspace',
                'href' => route('uploads.employee'),
                // 'href' => '#',
                'condition' => $user->can(PermissionList::EMPLOYEE_UPLOAD)
                // 'condition' => true
            ],
        ];
    }

    public function render()
    {
        return view('livewire.uploads.upload-data-page');
    }
}
