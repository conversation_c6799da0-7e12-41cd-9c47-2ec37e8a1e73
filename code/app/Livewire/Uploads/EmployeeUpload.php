<?php

namespace App\Livewire\Uploads;

use App\Contracts\ExcelImportComponentAbstract;
use Livewire\Component;

class EmployeeUpload extends ExcelImportComponentAbstract
{

    public function headers(): array
    {
        return [
            "First Name"                    => "<PERSON>",
            "Middle Name"                   => "<PERSON>",
            "Last Name"                     => "Doe",
            "Username"                      => "john1",
            "Gender"                        => "'male', 'female', 'other'",
            "DOB A.D."                      => "mm/dd/yyyy or yyyy-mm-dd",
            "Marital Status"                => "'unmarried', 'married'",
            "Mobile"                        => "Personal contact number",
            "Email"                         => "Personal email",
            "Nationality"                   => "Nationality",
            "Citizenship"                   => "Citizenship",
            "Permanent Address"             => "Permanent Address",
            "Temporary Address"             => "Temporary Address",
            "Father's Name"                 => "Father's Name",
            "Father DOB"                    => "mm/dd/yyyy or yyyy-mm-dd",
            "Mother's Name"                 => "Mother's Name",
            "Mother DOB"                    => "mm/dd/yyyy or yyyy-mm-dd",
            "<PERSON><PERSON><PERSON>'s Name"            => "<PERSON><PERSON><PERSON>'s Name",
            "Grandfather DOB"               => "mm/dd/yyyy or yyyy-mm-dd",
            "Spouse's Name"                 => "Spouse's Name",
            "Spouse DOB"                    => "Spouse's DOB",
            "Immediate Contact"             => "Immediate Contact Person",
            "Immediate Contact Mobile"      => "Immediate Contact Mobile",
            "Immediate Contact Relation"    => "Immediate Contact Relation",
            "Employee Code"                 => "Employee Code",
            "Branch Name"                   => "Branch Name",
            "Department Name"               => "Department Name",
            "Shift Name"                    => "Shift Name",
            "Date Of Join"                  => "Date Of Join",
            "Organization Email"            => "Organization Email",
            "Outsource Company"             => "Outsource Company Name",
            "Employee Status"               => "Employee Status",
            "Supervisor Name"               => "Supervisor Name",
            "Bank Name"                     => "Bank Name",
            "Bank Account No"               => "Bank Account No",
            "CUG Number"                    => "CUG Number",
            "RF No"                         => "RF No",
            "CIT No"                        => "CIT No",
            "Biometric Id"                  => "Biometric Id",
            "PAN No"                        => "PAN No"
        ];
    }

    public function requiredHeaders(): array
    {
        return [
            "First Name",
            "Last Name",
            "Gender",
            "DOB A.D.",
            "Marital Status",
            "Mobile",
            "Email",
            "Nationality",
            "Citizenship",
            "Permanent Address",
            "Father's Name",
            "Father DOB",
            "Mother's Name",
            "Mother DOB",
            "GrandFather's Name",
            "Grandfather DOB",
            "Immediate Contact",
            "Immediate Contact Mobile",
            "Immediate Contact Relation",
            "Employee Code",
            "Date of Join",
            "Branch Name",
            "Department Name",
            "Shift Name",
        ];
    }

    public function getDateFields(): array
    {
        return ['DOB A.D.', 'Date B.S.', 'Date Of Join', "Grandfather DOB", "Father DOB", "Mother DOB", "Spouse DOB"];
    }

    public function headerMap(): array
    {
        return [
            "DOB A.D."           => "dob_eng",

            "Father DOB"         => "father_dob_eng",
            "Mother DOB"         => "mother_dob_eng",
            "Grandfather DOB"    => "grandfather_dob_eng",
            "Spouse DOB"         => "spouse_dob_eng",

            "Father's Name"      => "father_name",
            "Mother's Name"      => "mother_name",
            "GrandFather's Name" => "grandfather_name",
            "Spouse's Name"        => "spouse_name",
        ];
    }


    protected function getSampleFileName(): string
    {
        return 'Employee Sample.xlsx';
    }

    protected function getExportOutputFileName(): string
    {
        return 'Employee Validation Output.xlsx';
    }
    protected function getHandlerType(): string
    {
        return 'employee';
    }

    public function render()
    {
        return view('livewire.uploads.employee-upload');
    }
}
