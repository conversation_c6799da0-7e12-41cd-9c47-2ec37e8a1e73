<?php

namespace App\Livewire\Uploads;

use App\Contracts\ExcelImportComponentAbstract;
use App\Http\Services\Imports\ImportHandlerFactory;
use Livewire\Component;

class DepartmentUpload extends ExcelImportComponentAbstract
{
    public function headers(): array
    {
        return [
            "Department Name" => "Human Resource",
            "Abbreviation" => "HR",
            "Remarks" => "Remark related department"
        ];
    }

    public function requiredHeaders(): array
    {
        return ["Department Name", "Abbreviation"];
    }

    public function headerMap(): array
    {
        return [
            "Department Name" => "name",
        ];
    }

    protected function getSampleFileName(): string
    {
        return 'Department Sample.xlsx';
    }

    protected function getExportOutputFileName(): string
    {
        return 'Department Validation Output.xlsx';
    }

    protected function getHandlerType(): string
    {
        return 'department';
    }

    public function render()
    {
        return view('livewire.uploads.department-upload');
    }
}
