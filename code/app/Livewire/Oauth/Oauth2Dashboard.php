<?php

namespace App\Livewire\Oauth;

use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use CodeBright\OauthAnd2fa\Http\Services\OauthClientService;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class Oauth2Dashboard extends Component
{
    use WithDataTable, WithNotify;

    public $name = '';
    /** @var array<int, string> */
    public $redirects = [''];      // multiple redirect inputs
    public $scopes = [];

    public $password_client = false;
    public $public = false;        // public => secret=null (PKCE)

    public $details = [];
    public $editingId = null;

    private OauthClientService $service;

    public function boot(OauthClientService $service)
    {
        $this->service = $service;
    }

    public function rules()
    {
        return $this->service->validationRules($this->editingId);
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->service->list(
            $this->search,
            $this->perPage,
            $this->getPage($this->pageName)
        );
    }

    public function viewDetail($id)
    {
        try {
            $client = $this->service->get($id, true);
            $this->details = $client->toArray();
        } catch (\Exception $e) {
            $this->notify("Client not found")->type("error")->send();
            $this->details = [];
        }
    }

    public function addRedirect()
    {
        $this->redirects[] = '';
    }

    public function removeRedirect(int $index)
    {
        unset($this->redirects[$index]);
        $this->redirects = array_values($this->redirects);
        if (empty($this->redirects)) {
            $this->redirects = [''];
        }
    }

    public function save()
    {
        $this->validate();

        $redirects = array_values(array_filter(
            array_map(fn($u) => trim((string)$u), $this->redirects),
            fn($u) => $u !== ''
        ));

        $payload = [
            'name'             => $this->name,
            'redirects'         => $redirects,          // let service join with PHP_EOL
            'scopes'           => $this->scopes,
        ];

        if ($this->editingId) {
            $this->service->update($this->editingId, $payload);
            $this->notify("Client updated")->send();
        } else {
            // If public is enabled, password grant must not be used (it requires a secret)
            $passwordClient = $this->public ? false : (bool)$this->password_client;

            $payload = [
                ...$payload,
                'public'           => (bool)$this->public, // public => secret=null
                'password_client'  => $passwordClient,
            ];
            $this->service->create($payload);
            $this->notify("Client added")->send();
        }

        unset($this->list);
        $this->dispatch('hide-model');
        $this->clearForm();
    }

    public function editClient($id)
    {
        $this->editingId = $id;
        $client = $this->service->get($id);

        $this->name   = $client->name;
        $this->scopes = $client->scopes ?? [];

        $this->redirects = $this->splitRedirects((string)($client->redirect ?? ''));
        if (empty($this->redirects)) $this->redirects = [''];

        $this->public         = is_null($client->secret);                // secret=null => public
        $this->password_client = (bool)($client->password_client ?? false);
        if ($this->public) {
            $this->password_client = false;
        }
    }

    public function revoke($id)
    {
        $this->service->revoke($id);
        $this->notify("Client Revoked")->send();
        unset($this->list);
    }

    public function activate($id)
    {
        $this->service->activate($id);
        $this->notify("Client Activated")->send();
        unset($this->list);
    }

    public function delete($id)
    {
        $this->service->delete($id);
        $this->notify("Client deleted")->send();
        unset($this->list);
    }

    #[On('hidden.bs.modal')]
    public function clearForm()
    {
        $this->reset('name', 'scopes', 'editingId', 'password_client', 'public');
        $this->redirects = [''];
    }

    #[Computed(persist: true)]
    public function availableScopes()
    {
        return $this->service->availableScopes();
    }

    public function render()
    {
        return view('livewire.oauth.oauth2-dashboard');
    }

    /** @return array<int, string> */
    private function splitRedirects(string $value): array
    {
        $value = trim($value);
        if ($value === '') return [];

        $parts = explode(',', $value) ?: [];
        return array_map('trim', $parts);
    }
}
