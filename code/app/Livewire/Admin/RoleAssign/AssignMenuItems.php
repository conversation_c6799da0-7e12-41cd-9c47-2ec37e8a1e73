<?php

namespace App\Livewire\Admin\RoleAssign;

use App\Http\Repositories\MenuRepository;
use App\Models\Admin\Menu;
use App\Models\MenuRole;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Attributes\Validate;
use Livewire\Component;

class AssignMenuItems extends Component
{
    use WithNotify;

    #[Reactive]
    public $roleId;
    public $local_role_id;

    #[Validate("required|exists:menus,id")]
    public $menu_id = "";

    public $selectedMenuItems;

    public function boot()
    {
        if ($this->roleId != $this->local_role_id) {
            $this->local_role_id = $this->roleId;
            $this->selectMenuItems();
        }
    }

    #[Computed(persist: true)]
    public function menuList()
    {
        return Menu::pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function menuItems()
    {
        if (!$this->menu_id) return [];
        $menu = Menu::find($this->menu_id);
        $menuRepo = new MenuRepository($menu->name);
        // dump($menuRepo->getAllMenuTree());
        return $menuRepo->getAllMenuTree();
    }

    public function updatedMenuId()
    {
        unset($this->menuItems);
        $this->selectMenuItems();
    }

    public function mount()
    {
        $this->menu_id = \array_keys($this->menuList)[0];
        $this->selectMenuItems();
    }


    public function selectMenuItems()
    {
        if ($this->roleId && $this->menu_id) {
            $this->selectedMenuItems = DB::table('menus_roles')
                ->leftJoin('menu_items', 'menu_items.id', '=', 'menus_roles.menu_item_id')
                ->where([
                    ['menu_items.menu_id', $this->menu_id],
                    ['menus_roles.role_id', $this->roleId]
                ])->distinct()->select('menus_roles.menu_item_id as id', 'menu_items.label as name')->get();
        } else {
            $this->selectedMenuItems = [];
        }

        $this->dispatch("select-menu-items", $this->selectedMenuItems);
    }

    #[On('assign-menu-items')]
    public function assignMenuItems($menuIds)
    {
        DB::beginTransaction();
        try {
            $menuItemIds = $this->selectedMenuItems?->pluck('id')->toArray() ?? [];
            MenuRole::where('role_id', $this->roleId)->whereIn('menu_item_id', $menuItemIds)->delete();
            foreach ($menuIds as $menu_item_id) {
                MenuRole::create([
                    "role_id"       => $this->roleId,
                    "menu_item_id"  => $menu_item_id,
                ]);
            }
            DB::commit();
            \Illuminate\Support\Facades\Cache::flush();
            $this->notify("Menu Item Assigned Successfully")->send();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->notify("Error while assigning menu item")->type("error")->send();
        }
    }

    public function render()
    {
        return view('livewire.admin.role-assign.assign-menu-items');
    }
}
