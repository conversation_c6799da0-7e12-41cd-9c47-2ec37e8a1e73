<?php

namespace App\Livewire\Admin\RoleAssign;

use App\Http\Helpers\Constant;
use App\Models\Admin\Role;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Assign Permission and Menu')]
class AssignPermissionMenu extends Component
{
    public $role_id;

    #[Computed(persist: true)]
    public function roleList()
    {
        return Role::where('name', '!=', Constant::ROLE_SUPER_ADMIN)->pluck("name", "id")->toArray();
    }

    public function mount()
    {
        $this->role_id = array_keys($this->roleList)[0];
    }
    
    public function render()
    {
        return view('livewire.admin.role-assign.assign-permission-menu');
    }
}
