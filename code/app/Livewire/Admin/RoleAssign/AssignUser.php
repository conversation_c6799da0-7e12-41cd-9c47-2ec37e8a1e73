<?php

namespace App\Livewire\Admin\RoleAssign;

use App\Http\Helpers\Constant;
use App\Http\Services\ScopeFetcher;
use App\Models\Admin\Role;
use App\Models\Employee\Employee;
use App\Models\User;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Assign Role to Users')]
class AssignUser extends Component
{
    use WithRowSelect, WithNotify, MultiselectEmployeeSearch;

    public array $roles = [], $employee_ids = [];
    public $role_id = "", $company_id = "", $region_id = "", $branch_id = "", $department_id = "";
    public $selectAllData = false;
    public array $scopeWiseView = [];


    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->selectAttribute = "user_id";
        $this->scopeWiseView = (new ScopeFetcher())->scopeWiseView();
        $this->setPermissionForEmployeeDropdown();
    }

    public function updatedEmployeeIds()
    {
        unset($this->list);
    }

    public function updatedRoleId()
    {
        unset($this->list);
    }

    public function updated($attr)
    {
        if (in_array($attr, ['company_id', 'region_id', 'branch_id', 'department_id'])) {
            unset($this->list);
        }
    }

    public function updatedSelectAllData()
    {
        if ($this->selectAllData) {
            $query = Employee::with(['user.roles', 'organizationInfo:id,employee_id,employee_code,region_id,branch_id,department_id', 'company:id,code', 'user:id']);

            $query = $this->assignScopeForList($query);

            $this->selectedRows = $query->pluck("user_id")->toArray();
        } else {
            $this->selectedRows = [];
        }
    }

    #[Computed(persist: true)]
    public function roleList()
    {
        return Role::whereNotIn('name', [Constant::ROLE_GENERAL, Constant::ROLE_SUPER_ADMIN])->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        $this->region_id = $this->region_id == "" ? null : $this->region_id;
        return (new ScopeFetcher())->fetchBranch($this->company_id, $this->region_id)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment($this->company_id)->pluck("name", "id")->toArray();
    }


    #[Computed(persist: true)]
    public function list()
    {
        $query = Employee::with(['user.roles', 'organizationInfo:id,employee_id,employee_code,region_id,branch_id,department_id', 'company:id,code', 'user:id']);

        $query = $this->assignScopeForList($query);

        return $query->when(count($this->employee_ids), function ($query) {
            $query->whereIn('id', $this->employee_ids);
        })
            ->when($this->role_id, fn($query) => $query->whereHas('user.roles', fn($query) => $query->where('id', $this->role_id)))
            ->when($this->company_id, fn($query) => $query->where('company_id', $this->company_id))
            ->when($this->region_id, fn($query) => $query->whereHas('organizationInfo', fn($query) => $query->where('region_id', $this->region_id)))
            ->when($this->branch_id, fn($query) => $query->whereHas('organizationInfo', fn($query) => $query->where('branch_id', $this->branch_id)))
            ->when($this->department_id, fn($query) => $query->whereHas('organizationInfo', fn($query) => $query->where('department_id', $this->department_id)))
            ->select("id", "first_name", "middle_name", "last_name", "user_id", "company_id")
            ->orderBy('first_name', 'asc')->paginate($this->perPage);
    }

    private function assignScopeForList($query)
    {
        if (!scopeAll()) {
            $query->whereHas('organizationInfo', function ($q) use ($query) {
                $query->where('company_id', currentEmployee()?->company_id);
                if (scopeCompany()) return;

                $q->where('region_id', currentEmployee()?->organizationInfo?->region_id);
                if (scopeRegion()) return;

                $q->where('branch_id', currentEmployee()?->organizationInfo?->branch_id);
                if (scopeBranch()) return;

                $q->where('department_id', currentEmployee()?->organizationInfo?->department_id);
                if (scopeDepartment()) return;

                $query->where('id', currentEmployee()?->id);
            });
        }
        return $query;
    }

    public function assignRoles()
    {
        if (!count($this->roles)) {
            $this->notify("Please select at least one role to assign")->type("error")->send();
            return;
        }
        if (!count($this->selectedRows)) {
            $this->notify("Please select at least one employee to assign")->type("error")->send();
            return;
        }
        try {
            $roleItems = Role::whereIn("id", $this->roles)->get();

            foreach ($roleItems as $roleItem) {
                $roleItem->users()->syncWithoutDetaching($this->selectedRows);
            }
            $this->notify("Roles assigned to users")->send();
            unset($this->list);
            \Illuminate\Support\Facades\Cache::flush();
            $this->selectAll = false;
        } catch (\Exception $e) {
            \logError("Error while assigning role to users", $e);
            $this->notify("Some Error Occurred")->type("error")->send();
        }
        // dd($this->roles, $this->selectedRows);
    }

    public function unassignRoles()
    {
        if (!count($this->roles)) {
            $this->notify("Please select at least one role to unassign")->type("error")->send();
            return;
        }

        if (!count($this->selectedRows)) {
            $this->notify("Please select at least one employee to unassign from")->type("error")->send();
            return;
        }

        try {
            $roleItems = Role::whereIn("id", $this->roles)->get();

            foreach ($roleItems as $roleItem) {
                $roleItem->users()->detach($this->selectedRows);
            }
            $this->notify("Roles unassigned from users")->send();
            unset($this->list);
            \Illuminate\Support\Facades\Cache::flush();
            $this->selectAll = false;
        } catch (\Exception $e) {
            \logError("Error while unassigning role from users", $e);
            $this->notify("Some Error Occurred")->type("error")->send();
        }
    }

    public function removeRole($roleId, $userId)
    {
        $role = Role::findOrFail($roleId);
        $user = User::findOrFail($userId);

        $role->users()->detach($user->id);
        unset($this->list);
        \Illuminate\Support\Facades\Cache::flush();
        $this->notify("Role removed")->send();
    }

    #[On('roles-changed')]
    public function togglePerformers($values)
    {
        $this->roles = $values;
    }

    public function render()
    {
        return view('livewire.admin.role-assign.assign-user');
    }
}
