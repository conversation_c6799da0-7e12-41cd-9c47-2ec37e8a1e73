<?php

namespace App\Livewire\Admin\RoleAssign;

use App\Models\Admin\Permission;
use App\Models\Admin\Role;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Component;

class AssignPermission extends Component
{
    use WithNotify;

    #[Reactive]
    public $roleId;
    public $localRoleId;

    public $selectedPermissions;

    #[Computed()]
    public function permissionList()
    {
        $permissions = Permission::select("id", "name")->get();
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name, 2);
            $groupName = $parts[0];

            $subParts = explode('.', $parts[1], 2);
            $subGroupName = $subParts[0];

            if (!isset($groupedPermissions[$groupName])) {
                $groupedPermissions[$groupName] = [];
            }

            if (!isset($groupedPermissions[$groupName][$subGroupName])) {
                $groupedPermissions[$groupName][$subGroupName] = [];
            }

            $groupedPermissions[$groupName][$subGroupName][] = (object)[
                'id' => $permission->id,
                'name' => $parts[1],
            ];
        }
        return $groupedPermissions;
    }

    public function boot()
    {
        if ($this->roleId != $this->localRoleId) {
            $this->setPermission();
        }
    }

    public function setPermission()
    {
        $role = Role::findOrFail($this->roleId);
        $this->localRoleId = $this->roleId;
        $this->selectedPermissions = $role->permissions->map(fn($permission) => (object)[
            "id" => $permission->id,
            "name" => $permission->name,
        ]);
        $this->dispatch("select-permissions", $this->selectedPermissions);
    }

    #[On("assign-permission")]
    public function assignPermission($permissionIds)
    {
        try {
            $role = Role::findOrFail($this->roleId);
            $role->permissions()->sync($permissionIds);
            $this->notify("Permissions assigned successfully")->send();
            app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
            Cache::store('arflow')->flush();
        } catch (\Exception $e) {
            Log::error("Error while assigning permission: " . $e->getMessage());
            $this->notify("Some error occurred while assigning permission")->type("error")->send();
        }
    }

    public function render()
    {
        return view('livewire.admin.role-assign.assign-permission');
    }
}
