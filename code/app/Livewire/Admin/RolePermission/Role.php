<?php

namespace App\Livewire\Admin\RolePermission;

use App\Models\Admin\Role as RoleModel;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Manage Roles')]
class Role extends Component
{
    use WithDataTable, WithNotify;

    #[Rule("required")]
    public $name;

    public $editingId = null;
    public $isEditing = false;

    public function __construct()
    {
        $this->tableListVariable = "roles";
    }

    #[On("role-add-modal-closed")]
    public function resetRoleForm()
    {
        $this->reset("name");
        $this->resetValidation("name");
        $this->editingId = null;
    }

    #[Computed(persist:true)]
    public function roles()
    {
        $roles = RoleModel::search($this->search);
        $roles = $this->applySorting($roles)->paginate($this->perPage);
        return $roles;
    }

    public function addRole()
    {
        $this->validateOnly("name");
        if ($this->isEditing) {
            $role = RoleModel::find($this->editingId);
            $role->name = $this->name;
            $role->save();
            $this->notify("Role updated")->send();
        } else {
            RoleModel::create(["name" => $this->name]);
            $this->notify("Role added")->send();
        }
        unset($this->roles);
        $this->dispatch('hide-model');
    }

    public function editRole($roleId)
    {
        $this->editingId = $roleId;
        $this->isEditing = true;
        $role = RoleModel::findOrFail($roleId);
        $this->name = $role->name;
    }

    public function deleteRole($roleId)
    {
        $role = RoleModel::findOrFail($roleId);
        $role->delete();
        $this->dispatch("role-deleted");
        unset($this->roles);
    }

    public function render()
    {
        if ($this->search) $this->resetPage();
        return view('livewire.admin.role-permission.role');
    }
}
