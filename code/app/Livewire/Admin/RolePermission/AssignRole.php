<?php

namespace App\Livewire\Admin\RolePermission;

use App\Http\Helpers\Constant;
use App\Models\Admin\Role;
use App\Models\User;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Assign Role')]
class AssignRole extends Component
{
    use WithDataTable, WithNotify;

    public $role;
    public $userIdsForUnassign = []; // from data table

    public $employeeIdsForAssign = []; // from the multiselect
    public $selectAllAssigned = [];
    public $selectAllUnassigned = [];

    public function mount($id)
    {
        $this->role = Role::findOrFail($id);
        if ($this->role->name === Constant::ROLE_SUPER_ADMIN || $this->role->name === Constant::ROLE_GENERAL) {
            abort("403", "You can't assign employees to {$this->role->name} role");
        }
    }

    /** For the users of multiselect */
    #[Computed()]
    public function unassignedUsers($selectAll = false)
    {
        $assignedUsersIds = $this->role->users()->select("id")->get()->pluck("id")->toArray();
        $query = User::with("employee:id")->whereNotIn("id", $assignedUsersIds);

        if (!$selectAll) {
            if ($this->search) {
                $searchTerm = $this->search;

                $query->where(function ($query) use ($searchTerm) {
                    $query->where('users.email', 'like', "%{$searchTerm}%")
                        ->orWhere('employee.id', 'like', "%{$searchTerm}%")
                        ->orWhere('employee.first_name', 'like', "%{$searchTerm}%")
                        ->orWhere('employee.middle_name', 'like', "%{$searchTerm}%")
                        ->orWhere('employee.last_name', 'like', "%{$searchTerm}%");
                });
            }
            $query = $this->applySorting($query)->paginate(1);;
        } else {
            $query = $query->get();
        }
        $users = $query->map(function ($user) {
            return (object)[
                "id" => $user->id,
                "email" => $user->email,
                "employee_id" => $user->employee?->id,
                "employee_name" => $user->employee?->name
            ];
        });
        return $users;
    }

    /** triggered when clicked checkbox of header of data table */
    public function selectAllAssignedEmployee()
    {
        if (count($this->assignedUsers) < 1) {
            $this->selectAllAssigned = false;
            return;
        }

        if ($this->selectAllAssigned) {
            $this->userIdsForUnassign = $this->assignedUsers(true)->pluck("id")->toArray();
        } else {
            $this->userIdsForUnassign = [];
        }
    }

    /** triggered for the event when the multiselect is changed */
    #[On("employeeIdsForAssignChanged")]
    public function changeEmployeeIdsForAssign($values)
    {
        $this->employeeIdsForAssign = $values;
    }

    public function render()
    {
        return view('livewire.admin.role-permission.assign-role');
    }
}
