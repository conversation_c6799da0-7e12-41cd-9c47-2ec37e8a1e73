<?php

namespace App\Livewire\Admin\RolePermission;

use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class UnassignRoleComponent extends Component
{
    use WithNotify, WithRowSelect, MultiselectEmployeeSearch;

    public $role;

    public $selectAll;
    public $employee_ids1 = [];

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids', 'employee_ids1'];
    }

    public function updatedEmployeeIds1()
    {
        unset($this->users);
    }

    public function __construct()
    {
        $this->tableListVariable = 'users';
        $this->pageName = 'assigned-users';
    }

    #[Computed()]
    // list of users having role
    public function users()
    {
        $query = $this->role->users()->leftJoin('employees', 'users.id', '=', 'employees.user_id')
            ->leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin("companies as comp", "employees.company_id", '=', 'comp.id')
            ->select(
                "users.id",
                DB::raw("CONCAT(comp.code, '-' ,employee_org.employee_code) as employee_code"),
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                "employees.email as email"
            )
            ->where("users.id", '!=', auth()->user()->id);

        if (count($this->employee_ids1))
            $query->whereIn('employees.id', $this->employee_ids1);

        if ($this->search) {
            $query->where(function ($query) {
                $query->where(DB::raw("CONCAT(comp.code, '-' ,employee_org.employee_code)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(employees.first_name, ' ', employees.middle_name, ' ', employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            });
        }
        return $query->paginate($this->perPage, pageName: $this->pageName);
    }

    public function unassign()
    {
        Log::info("Data for un-assigning users for role {$this->role->id}", $this->selectedRows);
        try {
            if (!count($this->selectedRows)) {
                $this->notify("Select user to revoke")->type("error")->send();
                return;
            }
            $this->role->users()->detach($this->selectedRows);
            \Illuminate\Support\Facades\Cache::flush();
            $this->notify("User revoked from role")->send();
            $this->dispatch("role-unassigned");
            $this->refreshList();
        } catch (Exception $e) {
            Log::error("Error while un-assigning the role: " . $e->getMessage());
            $this->dispatch("Error while un-assigning the role");
        }
    }

    #[On("role-assigned")]
    public function refreshList()
    {
        unset($this->users);
        $this->removeAllSelectedRows();
    }

    public function render()
    {
        return view('livewire.admin.role-permission.unassign-role-component');
    }
}
