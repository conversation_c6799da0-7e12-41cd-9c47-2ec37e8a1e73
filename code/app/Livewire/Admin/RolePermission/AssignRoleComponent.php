<?php

namespace App\Livewire\Admin\RolePermission;

use App\Models\User;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class AssignRoleComponent extends Component
{
    use WithNotify, WithRowSelect, MultiselectEmployeeSearch;

    public $role;
    public $employee_ids = [];

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids', 'employee_ids1'];
    }

    public function updatedEmployeeIds()
    {
        unset($this->users);
    }

    public function __construct()
    {
        $this->tableListVariable = 'users';
        $this->pageName = 'unassigned-users';
    }

    #[Computed()]
    public function users()
    {
        $assignedUsersIds = $this->role->users()->pluck("id")->toArray();
        $query = User::leftJoin('employees', 'users.id', '=', 'employees.user_id')
            ->leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin("companies as comp", "employees.company_id", '=', 'comp.id')
            ->select(
                "users.id",
                "users.username",
                DB::raw("CONCAT(comp.code, '-' ,employee_org.employee_code) as employee_code"),
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                "employees.email as email"
            )
            ->whereNotIn("users.id", $assignedUsersIds)
            ->where("users.id", '!=', auth()->user()->id);

        if (count($this->employee_ids))
            $query->whereIn('employees.id', $this->employee_ids);

        if ($this->search) {
            $query->where(function ($query) {
                $query->where(DB::raw("CONCAT(comp.code, '-' ,employee_org.employee_code)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(employees.first_name, ' ', employees.middle_name, ' ', employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            });
        }
        return $query->paginate($this->perPage, pageName: $this->pageName);
    }

    public function assign()
    {
        try {
            if (!count($this->selectedRows)) {
                $this->notify("Select user to assign")->type("error")->send();
                return;
            }
            $this->role->users()->attach($this->selectedRows);
            \Illuminate\Support\Facades\Cache::flush();
            $this->notify("User assigned to role")->send();
            $this->dispatch("role-assigned");
            $this->refreshList();
        } catch (Exception $e) {
            Log::error("Error while assigning the role: " . $e->getMessage());
            $this->notify("Error while assigning to role")->type("error")->send();
        }
    }

    #[On("role-unassigned")]
    public function refreshList()
    {
        unset($this->users);
        $this->removeAllSelectedRows();
    }

    public function render()
    {
        return view('livewire.admin.role-permission.assign-role-component');
    }
}
