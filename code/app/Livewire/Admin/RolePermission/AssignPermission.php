<?php

namespace App\Livewire\Admin\RolePermission;

use App\Http\Helpers\Constant;
use App\Models\Admin\Permission;
use App\Models\Admin\Role as ModelsRole;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Assign Permission')]
class AssignPermission extends Component
{
    use WithNotify;
    public $role;
    public $selectedPermissions = [];

    public function mount($id)
    {
        $this->role = ModelsRole::findOrFail($id);
        if ($this->role->name === Constant::ROLE_SUPER_ADMIN) {
            abort("403", "You can't assign permission to super admin");
        }
        $this->selectedPermissions = $this->role->permissions->map(fn ($permission) => (object)[
            "id" => $permission->id,
            "name" => $permission->name,
        ]);
    }

    #[Computed()]
    public function permissionList()
    {
        $permissions = Permission::select("id", "name")->get();
        $groupedPermissions = [];

        foreach ($permissions as $permission) {
            $parts = explode('.', $permission->name, 2);
            $groupName = $parts[0];

            $subParts = explode('.', $parts[1], 2);
            $subGroupName = $subParts[0];

            if (!isset($groupedPermissions[$groupName])) {
                $groupedPermissions[$groupName] = [];
            }
            
            if (!isset($groupedPermissions[$groupName][$subGroupName])) {
                $groupedPermissions[$groupName][$subGroupName] = [];
            }
        
            $groupedPermissions[$groupName][$subGroupName][] = (object)[
                'id' => $permission->id,
                'name' => $parts[1],
            ];
        }
        return $groupedPermissions;
    }

    #[On("assign-permission")]
    public function assignPermission($permissionIds)
    {

        try {
            $this->role->permissions()->sync($permissionIds);
            $this->notify("Permissions assigned successfully")->send();
            app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
        } catch (\Exception $e) {
            Log::error("Error while assigning permission: " . $e->getMessage());
            $this->notify("Some error occurred while assigning permission")->type("error")->send();
        }
    }

    public function render()
    {
        return view('livewire.admin.role-permission.assign-permission');
    }
}
