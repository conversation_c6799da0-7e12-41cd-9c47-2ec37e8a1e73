<?php

namespace App\Livewire\Admin\RolePermission;

use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Component;
use App\Models\Admin\Permission as ModelsPermission;
use App\Rules\UniqueCrudPermissions;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule as ValidationRule;
use Livewire\Attributes\On;

class Permission extends Component
{
  use WithDataTable, WithNotify;

  public $isEditing, $editingPermission;
  public $name, $isCrud = false;

  public function __construct() {
    $this->tableListVariable = "permissions";
  }

  public function rules()
  {
    return [
      "name" => ["required", "max:255"],
      "isCrud" => "boolean"
    ];
  }

  #[Computed(persist: true)]
  public function permissions()
  {
    $permissions = ModelsPermission::search($this->search);
    $permissions = $this->applySorting($permissions)->paginate($this->perPage);
    return $permissions;
  }

  public function addEditPermission()
  {
    $this->validate();
    $permissionName = $this->name;
    if ($this->isEditing) {
      $this->validate(["name" => ValidationRule::unique("permissions", "name")->ignore($this->editingPermission->id)]);
      $this->editingPermission->update([
        "name" => $this->name
      ]);;
      $this->notify("Permission created")->send();
    } else {
      if (!$this->isCrud) {
        $this->validate(["name" => ValidationRule::unique("permissions", "name")]);
        ModelsPermission::create([
          "name" => $this->name
        ]);
        $this->notify("Permission created")->send();
      } else {
        $this->validate(["name" => [new UniqueCrudPermissions]]);
        $permissions = array_map(function ($p) use ($permissionName) {
          return ['name' => $permissionName . '_' . $p];
        }, ["create", "read", "update", "delete"]);
        ModelsPermission::insert($permissions);
        $this->notify("Permission created with CRUD")->send();
      }
    }
    unset($this->permissions);
    $this->dispatch("hide-model");
  }

  public function edit($permissionId)
  {
    $this->isEditing = true;
    $permission = ModelsPermission::findOrFail($permissionId);
    $this->editingPermission = $permission;
    $this->name = $permission->name;
  }

  public function delete($permissionId)
  {
    $permission = ModelsPermission::findOrFail($permissionId);
    try {
      $permission->delete();
      unset($this->permissions);
      $this->notify("Permission deleted")->send();
    } catch (\Exception $e) {
      Log::error("Error while deleting permission with $permissionId: " . $e->getMessage());
      $this->notify("Can't delete this permission.")->type("error")->send();
    }
  }

  #[On('hidden.bs.modal')]
  public function resetValues()
  {
    $this->isEditing = false;
    $this->editingPermission = null;
    $this->reset(['isCrud', 'name']);
    $this->resetValidation(['isCrud', 'name']);
  }

  public function render()
  {
    return view('livewire.admin.role-permission.permission');
  }
}
