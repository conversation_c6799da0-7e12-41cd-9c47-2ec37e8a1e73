<?php

namespace App\Livewire\Admin;

use App\Models\configs\Job;
use App\Models\Employee\Employee;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\PayslipRequest;
use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

class UploadEmployeeInfo extends Component
{
    use WithFileUploads, WithNotify;

    public $uploadExcelFile, $result;

    public function uploadData()
    {
        $this->employeeRoleUpdate();
    }

    // Update the employee role (Job).
    public function employeeRoleUpdate() {
        $data = Excel::toArray([], $this->uploadExcelFile);
        try {
            $data = transformArrayToAssociative($data[0]);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }

        $jobArray = Job::pluck('id', 'name')->toArray();

        DB::beginTransaction();
        try {
            foreach ($data as $employee) {
                $payslip = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
                    ->leftJoin("companies as comp", "comp.id", "employees.company_id")
                    ->leftJoin("payslips as payslip", "payslip.employee_id", "employees.id")
                    ->where(DB::raw("CONCAT(comp.code, '-', org.employee_code)"), $employee['employee code'])
                    ->where(function ($query) {
                        $query->whereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Active"
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                            ->orWhereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Expired"
                    AND NOT EXISTS (
                        SELECT 1 FROM payslips active
                        WHERE active.employee_id = p.employee_id
                        AND active.status = "Active"
                    )
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                            ->orWhereNull('payslip.id');
                    })
                    ->select('payslip.id', 'payslip.payslip_request_id')
                    ->first();

                $jobId = $jobArray[$employee['employee role']] ?? null;
                $employeeText = "{$employee['employee name']} [{$employee['employee code']}]";
                if ($payslip && $jobId) {
                    $this->result .= "$employeeText employee role has been update to {$employee['employee role']}.<br/>";
                    Payslip::where('id', $payslip->id)->update(['job_id'=>$jobId]);
                    PayslipRequest::where('id', $payslip->payslip_request_id)->update(['job_id'=>$jobId]);
                } else {
                    if (!$payslip){
                        $this->result .= "<span class='text-danger fw-bold'>Error:$employeeText payslip not found.<span><br/>";
                        throw new \Exception("$employeeText payslip not found. All data reverted.");
                    }
                    else if (!$jobId) {
                        $this->result .= "<span class='text-danger fw-bold'>Error:$employeeText job not found.</span><br/>";
                        throw new \Exception("$employeeText job not found. All data reverted.");
                    }
                    
                }
            }
            DB::commit();
            $this->notify("Updated employee role")->send();
        } catch (Exception $e) {
            DB::rollBack();
            logError("Error while updating employee role", $e);
            $this->notify("Error updating employee role: " . $e->getMessage())->duration(6)->type("error")->send();
            $this->reset('result');
        }
    }

    public function render()
    {
        return view('livewire.admin.upload-employee-info');
    }
}
