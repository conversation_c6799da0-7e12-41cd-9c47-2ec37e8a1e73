<?php

namespace App\Livewire\Admin\Menu;

use App\Http\Helpers\Constant;
use App\Http\Repositories\MenuRepository;
use App\Models\Admin\Menu as MenuModel;
use App\Models\Admin\MenuItem as MenuItemModel;
use App\Models\Admin\Role;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Menu Items')]
class MenuItems extends Component
{
    public $menu;
    public MenuItemForm $form;

    public $isEditing = false;
    public MenuItemModel $editingMenu;

    #[Computed(persist:true)]
    public function parentMenuItems()
    {
        $menuItems = MenuItemModel::where("menu_id", $this->menu->id);
        if ($this->isEditing && $this->menu) {
            $menuItems->where("id", "!=", $this->editingMenu->id);
        }
        $menuItems = $menuItems->pluck("label", "id");
        return $menuItems;
    }

    #[Computed(persist:true)]
    public function roles()
    {
        return Role::select("id", "name")->where("name", "!=", Constant::ROLE_SUPER_ADMIN)->get();
    }

    #[Computed(persist:true)]
    public function menuItems()
    {
        $menuRepo = new MenuRepository($this->menu->name);
        return $menuRepo->menuJsonForConfig() ?? [];
    }

    public function mount($id)
    {
        $this->editingMenu = new MenuItemModel();
        $this->menu = MenuModel::findOrFail($id);
    }

    public function addOrUpdate()
    {
        if ($this->isEditing) {
            $updated = $this->form->update();
            if (!$updated) return;
            $this->dispatch("notify", [
                "message" => "Menu Updated"
            ]);
        } else {
            $stored = $this->form->store($this->menu);
            if (!$stored) return;
            $this->dispatch("notify", [
                "message" => "Menu Added",
            ]);
        }
        $this->refreshComputed();
        $this->dispatch('hide-model');
    }

    private function refreshComputed()
    {
        unset($this->menuItems);
        unset($this->parentMenuItems);
        Cache::flush();
        $this->dispatch("menu-position-updated", $this->menuItems);
    }

    #[On("roles-changed")]
    public function rolesChanged($values)
    {
        $this->form->setRoles($values);
    }

    #[On("update-menu-position")]
    public function updatePosition($updatedItems)
    {
        try {
            foreach ($updatedItems as $item) {
                $updatedMenu = MenuItemModel::find($item['id']);
                if ($updatedMenu) {
                    $updatedMenu->parent_id = $item['parent'] ?? null;
                    $updatedMenu->position = $item['order'];
                    $updatedMenu->save();
                }
            }
            $this->refreshComputed();
        } catch (Exception $e) {
            Log::error("Error while updating position of menu: ", $e->getMessage());
            $this->dispatch("notify", [
                "message" => "Some Error Occurred while updating menu",
                "type" => "error"
            ]);
        }
    }

    #[On('menu-item-edit')]
    public function edit($id)
    {
        $this->isEditing = true;
        unset($this->parentMenuItems);
        $this->editingMenu = MenuItemModel::findOrFail($id);
        $this->form->set($this->editingMenu);
        $this->dispatch("toggle-choices", $this->editingMenu->roles->pluck("id")->toArray());
    }

    #[On('menu-item-delete')]
    public function delete($id)
    {
        $menuItem = MenuItemModel::findOrFail($id);
        $menuItem->delete();
        $this->refreshComputed();
        $this->dispatch("notify", ["message" => "Menu Item Deleted"]);
    }

    #[On('hidden.bs.modal')]
    public function resetModal()
    {
        $selectedRoles = $this->editingMenu->roles->pluck("id")->toArray() ?? [];
        $this->form->resetForm();
        $this->editingMenu = new MenuItemModel;
        $this->isEditing = false;
        $this->dispatch("toggle-choices", []);
    }

    public function render()
    {
        return view('livewire.admin.menu.menu-items');
    }
}
