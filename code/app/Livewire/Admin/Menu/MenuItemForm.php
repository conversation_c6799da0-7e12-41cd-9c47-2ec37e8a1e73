<?php

namespace App\Livewire\Admin\Menu;

use App\Models\Admin\Menu;
use App\Models\Admin\MenuItem as MenuItemModel;
use App\Models\MenuPermission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Livewire\Attributes\Rule;
use Livewire\Form;

class MenuItemForm extends Form
{
  public ?MenuItemModel $menuItem;

  #[Rule("nullable|exists:menu_items,id")]
  public $parent_id = null;

  #[Rule("required|max:255")]
  public $label;

  #[Rule("nullable|max:255")]
  public $route_name;

  #[Rule("max:255")]
  public $icon;

  #[Rule("required|in:_self,_blank")]
  public $target = "_self";

  #[Rule("required|in:internal,page")]
  public $link_type = "internal";

  #[Rule("required|integer")]
  public $position = 0;

  #[Rule("boolean")]
  public $wire_navigate = false;

  // #[Rule("required")]
  public $roles = [];

  public function store(Menu $menu)
  {
    $this->validate();
    if ($this->route_name) {
      if (!Route::has($this->route_name)) {
        $this->addError("route_name",  "Given route name doesn't exist");
        return false;
      }
    }
    Log::info("Parameters for adding the menu item: ",  $this->all());
    DB::beginTransaction();
    try {
      $menuItem = MenuItemModel::create([
        "menu_id" => $menu->id ?? null,
        "parent_id" => $this->parent_id ?? null,
        "label" => $this->label,
        "route_name" => $this->route_name,
        "icon" => $this->icon,
        "wire_navigate" => $this->wire_navigate,
        "target" => $this->target,
        "link_type" => $this->link_type,
        "position" => $this->position,
      ]);
      Log::info("Menu Item Created with data: ", $menuItem->toArray());
      $menuItem->roles()->attach($this->roles);
      Log::info("Permission attached to menu item");
      DB::commit();
      $this->resetValidation();
      return true;
    } catch (\Exception $e) {
      Log::error("Error while creating menu item" . $e->getMessage());
      DB::rollBack();
      return false;
    }
  }

  public function setRoles($values)
  {
    $this->roles = $values;
  }

  public function set($menuItem)
  {
    $this->menuItem = $menuItem;
    $this->parent_id = $menuItem->parent_id;
    $this->label = $menuItem->label;
    $this->route_name = $menuItem->route_name;
    $this->icon = $menuItem->icon;
    $this->target = $menuItem->target;
    $this->link_type = $menuItem->link_type;
    $this->position = $menuItem->position;
    $this->wire_navigate = (bool)$menuItem->wire_navigate;
    $this->roles = $menuItem->roles->pluck("id")->toArray();
  }

  public function update()
  {
    $this->validate();
    if ($this->route_name) {
      if (!Route::has($this->route_name)) {
        $this->addError("route_name",  "Given route name doesn't exist");
        return false;
      }
    }
    Log::info("Parameters for updating the menu item with id {$this->menuItem->id}: ",  $this->all());
    DB::beginTransaction();
    try {
      $this->menuItem->update([
        "parent_id" => $this->parent_id ? $this->parent_id : null,
        "label" => $this->label,
        "route_name" => $this->route_name,
        "icon" => $this->icon,
        "target" => $this->target,
        "link_type" => $this->link_type,
        "wire_navigate" => $this->wire_navigate,
        "position" => $this->position,
      ]);
      Log::info("Menu Item Updated");
      $this->menuItem->roles()->sync($this->roles);
      Log::info("Permission for menu item synced");
      $this->resetValidation();
      DB::commit();
      return true;
    } catch (\Exception $e) {
      Log::error("Error while creating menu item: " . $e->getMessage());
      DB::rollBack();
      return false;
    }
  }

  public function resetForm()
  {
    $this->reset();
    $this->resetValidation();
  }
}
