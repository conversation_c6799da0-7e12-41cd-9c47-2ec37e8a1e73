<?php

namespace App\Livewire\Admin\Menu;

use App\Models\Admin\Menu as MenuModel;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Manage Menus')]
class Menu extends Component
{
    use WithDataTable, WithNotify;

    #[Rule("required|max:255")]
    public $name = "";

    #[Rule("required|boolean")]
    public $status = true;

    public $isEditing, $editingMenuId;

    public function __construct() {
        $this->tableListVariable = "menuList";
    }

    #[Computed(persist:true)]
    public function menuList()
    {
        $menus = MenuModel::search($this->search);
        $menus = $this->applySorting($menus)->paginate($this->perPage);
        return $menus;
    }

    public function addOrUpdate()
    {
        $validated = $this->validate();
        if ($this->isEditing) {
            $menu = MenuModel::findOrFail($this->editingMenuId);
            $menu->name = $this->name;
            $menu->status = $this->status;
            $menu->save();
            $this->notify("Menu updated")->send();
        } else {
            MenuModel::create($validated);
            $this->notify("Menu created")->send();
        }
        unset($this->menuList);
        $this->dispatch("hide-model");
    }

    public function edit($menuId)
    {
        $this->editingMenuId = $menuId;
        $this->isEditing = true;
        $menu = MenuModel::findOrFail($menuId);
        $this->name = $menu->name;
        $this->status = $menu->status;
    }

    public function delete($menuId)
    {
        $menu = MenuModel::findOrFail($menuId);
        $menu->delete();
        unset($this->menuList);
        $this->dispatch("menu-deleted");
    }

    public function render()
    {
        return view('livewire.admin.menu.menu');
    }
}
