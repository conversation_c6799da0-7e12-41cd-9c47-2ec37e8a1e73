<?php

namespace App\Livewire\Admin;

use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Run Console Command")]
class RunConsoleCommand extends Component
{
    use WithNotify;

    public $command;

    public function rules()
    {
        return [
            'command' => 'required|string',
        ];
    }

    public function runCommand()
    {
        $this->validate();
        try {
            Artisan::call($this->command);
        } catch (Exception $e) {
            logError("Failed to run the command. ", $e);
            $this->notify('Failed to run the command')->type('error')->send();
        }
        $this->resetErrorBag();
    }

    #[Computed()]
    public function output()
    {
        return Artisan::output() ?? "";
    }

    public function render()
    {
        return view('livewire.admin.run-console-command');
    }
}
