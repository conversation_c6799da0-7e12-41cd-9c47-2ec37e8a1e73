<?php

namespace App\Livewire\Admin;

use App\Exports\QueryResultExport;
use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Excel as ExcelWriter;

class RunDatabaseCommand extends Component
{
    use WithNotify;

    public $command, $result, $exportTable;

    public function rules()
    {
        return [
            'command' => 'required|string',
        ];
    }

    public function runCommand()
    {
        $this->validate();
        try {
            $this->result = DB::select($this->command);
        } catch (Exception $e) {
            logError("Failed to run the database command. ", $e);
            $this->notify('Failed to run the database command')->type('error')->send();
        }
        $this->resetErrorBag();
    }

    public function downloadCsv()
    {
        if (empty($this->result) || count((array) $this->result) === 0) {
            $this->notify('No result to export. Run a query first.')->type('warning')->send();
            return;
        }

        $filename = 'db_result_' . now()->format('Y-m-d_H-i-s') . '.csv';

        // Stream CSV using Laravel-Excel
        return Excel::download(
            new QueryResultExport((array) $this->result),
            $filename,
            ExcelWriter::CSV,
            ['Content-Type' => 'text/csv; charset=UTF-8']
        );
    }


    public function downloadSql()
    {
        if (empty($this->result) || count((array) $this->result) === 0) {
            $this->notify('No result to export. Run a query first.')->type('warning')->send();
            return;
        }

        // Determine table name
        $table = trim((string) $this->exportTable);
        if ($table === '') {
            $table = $this->inferTableFromQuery($this->command) ?: 'export_table';
        }

        $rows = collect($this->result)->map(fn($r) => (array) $r)->values();
        if ($rows->isEmpty()) {
            $this->notify('No rows to export.')->type('warning')->send();
            return;
        }

        $columns = array_keys($rows->first());

        $filename = 'db_result_' . now()->format('Y-m-d_H-i-s') . '.sql';

        return response()->streamDownload(function () use ($rows, $columns, $table) {
            $out = fopen('php://output', 'w');

            $write = function (string $s) use ($out) {
                fwrite($out, $s);
            };

            // Header
            $write("-- SQL Export generated at " . now()->toDateTimeString() . PHP_EOL);
            $write("SET NAMES utf8mb4;" . PHP_EOL);
            $write("SET FOREIGN_KEY_CHECKS=0;" . PHP_EOL . PHP_EOL);

            // Quote identifiers with backticks
            $qIdent = fn(string $id) => '`' . str_replace('`', '``', $id) . '`';

            // Build insert head
            $insertHead = "INSERT INTO " . $qIdent($table) . " (" .
                implode(', ', array_map($qIdent, $columns)) . ") VALUES" . PHP_EOL;

            // Chunk values to keep lines reasonable
            $chunkSize = 500; // adjust if needed
            $rows->chunk($chunkSize)->each(function ($chunk, $i) use ($columns, $insertHead, $write) {
                $write($insertHead);

                $idx = 0;
                foreach ($chunk as $row) {
                    $values = [];
                    foreach ($columns as $col) {
                        $values[] = $this->sqlValue($row[$col] ?? null);
                    }

                    $write('(' . implode(', ', $values) . ')');
                    $idx++;

                    if ($idx < $chunk->count()) {
                        $write(',' . PHP_EOL);
                    } else {
                        $write(';' . PHP_EOL . PHP_EOL);
                    }
                }
            });

            // Footer
            $write("SET FOREIGN_KEY_CHECKS=1;" . PHP_EOL);
            fclose($out);
        }, $filename, [
            'Content-Type' => 'application/sql; charset=UTF-8',
        ]);
    }

    /**
     * Turn a PHP value into a SQL literal (MySQL-friendly).
     */
    private function sqlValue($v): string
    {
        if ($v === null) return 'NULL';

        if ($v instanceof \DateTimeInterface) {
            return $this->quoteSql($v->format('Y-m-d H:i:s'));
        }

        if (is_bool($v)) return $v ? '1' : '0';

        if (is_int($v) || is_float($v)) return (string) $v;

        if (is_array($v) || is_object($v)) {
            return $this->quoteSql(json_encode($v, JSON_UNESCAPED_UNICODE));
        }

        return $this->quoteSql((string) $v);
    }

    /**
     * Quote and escape a string for SQL (' becomes '' and common control chars escaped).
     */
    private function quoteSql(string $s): string
    {
        // MySQL-safe approximation without a live connection escape helper.
        $s = str_replace(
            ["\\",   "\0",  "\n",  "\r",  "\x1a", "'"],
            ["\\\\", "\\0", "\\n", "\\r", "\\Z", "''"],
            $s
        );
        return "'" . $s . "'";
    }

    /**
     * Best-effort table name inference for simple queries:
     *   SELECT ... FROM table [AS t] ...
     * Returns null if ambiguous/complex.
     */
    private function inferTableFromQuery(string $sql): ?string
    {
        $normalized = trim(preg_replace('/\s+/', ' ', strtolower($sql)));

        // Quick bail-outs for non-selects or joins
        if (!str_starts_with($normalized, 'select')) {
            return null;
        }
        if (preg_match('/\bjoin\b/', $normalized)) {
            return null;
        }
        if (!preg_match('/\bfrom\s+([`"\[]?)([a-z0-9_.]+)\1\b/iu', $sql, $m)) {
            return null;
        }

        // $m[2] includes schema/table; we only want the final segment
        $candidate = $m[2];
        $parts = preg_split('/[.]/', $candidate);
        return $parts ? end($parts) : $candidate;
    }

    public function render()
    {
        return view('livewire.admin.run-database-command');
    }
}
