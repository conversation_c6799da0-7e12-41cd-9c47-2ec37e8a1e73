<?php

namespace App\Livewire\Admin\Company\Form;

use App\Models\configs\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Form;

class GratuityForm extends Form
{
  public $companyId;
  public $gratuity;
  public function rules()
  {
    return [
      'gratuity' => 'required|numeric|min:0|max:99',
    ];
  }

  public function set(Company $company)
  {
    $this->gratuity = $company->gratuity;
    $this->companyId = $company->id;
  }

  public function update()
  {
    $this->validate();
    Log::info("Params for updating general information for company with id {$this->companyId}: ", $this->all());
    $company = Company::findOrFail($this->companyId);
    DB::beginTransaction();
    try {
      $company->update(['gratuity' => $this->gratuity]);
      DB::commit();
    } catch (\Exception $e) {
      DB::rollBack();
      Log::error("Error while updating general info: ", $e->getMessage());
      return false;
    }
  }
}
