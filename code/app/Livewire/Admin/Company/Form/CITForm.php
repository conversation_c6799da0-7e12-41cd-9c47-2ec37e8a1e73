<?php

namespace App\Livewire\Admin\Company\Form;

use App\Models\configs\Company;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Validate;
use Livewire\Form;


class CITForm extends Form
{
  public Company $company;

  #[Rule('boolean')]
  public $cit_enabled = false;

  #[Validate('required_if:cit_enabled,true', message: "The bank code field is required")]
  public $cit_code;

  #[Validate('required_if:cit_enabled,true', message: "The bank name field is required")]
  public $cit_bank;

  #[Validate('boolean')]
  public $is_limit_activated = false;

  public function set(Company $company)
  {
    $this->cit_enabled = $company->cit_enabled;
    $this->cit_code = $company->cit_code;
    $this->cit_bank = $company->cit_bank;
    $this->is_limit_activated = $company->is_limit_activated;
    $this->company = $company;
  }

  public function update()
  {
    $this->validate();
    $this->company->update([
      "cit_enabled" => $this->cit_enabled,
      "cit_code" => $this->cit_code,
      "cit_bank" => $this->cit_bank,
      "is_limit_activated" => $this->is_limit_activated,
    ]);
  }
}
