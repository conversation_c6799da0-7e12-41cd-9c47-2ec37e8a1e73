<?php

namespace App\Livewire\Admin\Company\Form;

use App\Models\configs\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Form;

class CompanySettingForm extends Form
{
    public $allow_job_seat;
    public $companyId;

    public function rules()
    {
        return [
            'allow_job_seat' => 'required|in:Y,N',
        ];
    }

    public function set(Company $company)
    {
        $this->allow_job_seat = $company->allow_job_seat;
        $this->companyId = $company->id;
    }

    public function update()
    {
        $this->validate();
        Log::info("Params for updating company setting for company with id {$this->companyId}: ", $this->all());
        $company = Company::findOrFail($this->companyId);
        DB::beginTransaction();
        try {
            $company->update(['allow_job_seat' => $this->allow_job_seat]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error while updating general info: ", $e->getMessage());
            return false;
        }
    }
}
