<?php

namespace App\Livewire\Admin\Company\Form;

use App\Models\configs\Company;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule as ValidationRule;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Validate;
use Livewire\Form;


class RFForm extends Form
{
  public Company $company;

  #[Rule('required|in:SSF,PF,OPT')]
  public $rf_scheme = "SSF";

  public $rfc_company = 0;
  public $rfc_employee = 0;
  public $rf_scheme_no;
  public $rf_scheme_start_date;
  public $rf_limit;

  public function rules()
  {
    return [
      "rfc_company" => [ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
      "rfc_employee" => [ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
      "rf_scheme_no" => ["max:50", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
      "rf_scheme_start_date" => ["date", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
      "rf_limit" => ["numeric", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
    ];
  }

  public function messages()
  {
    return [
      "rf_scheme_start_date.date_format" => "Invalid Date Format"
    ];
  }

  public function set(Company $company)
  {
    $this->rf_scheme = $company->rf_scheme;
    $this->rfc_company = $company->rfc_company;
    $this->rfc_employee = $company->rfc_employee;
    $this->rf_scheme_no = $company->rf_scheme_no;
    $this->rf_scheme_start_date = $company->rf_scheme_start_date;
    $this->rf_limit = $company->rf_limit;
    $this->company = $company;
  }

  public function update()
  {
    Log::info("Params for updating RF Information: ", $this->all());
    $this->validate();
    $this->company->update([
      "rf_scheme" => $this->rf_scheme ? $this->rf_scheme : null,
      "rfc_company" => $this->rfc_company,
      "rfc_employee" => $this->rfc_employee,
      "rf_scheme_no" => $this->rf_scheme_no,
      "rf_scheme_start_date" => $this->rf_scheme_start_date,
      "rf_limit" => $this->rf_limit,
    ]);
    Log::info("RF Information Updated");
  }
}
