<?php

namespace App\Livewire\Admin\Company\Form;

use App\Http\Helpers\Constant;
use App\Models\configs\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Rule;
use Livewire\Form;

class GeneralInfoForm extends Form
{

  public Company $company;

  #[Rule('required|string|max:100')]
  public $name;

  #[Rule('nullable|image|mimes:jpg,png,jpeg|max:5000')]
  public $logo;

  public $logoUrl = "";

  #[Rule('required|string|max:100')]
  public $contact_person;

  #[Rule('required|string|max:10')]
  public $code;

  #[Rule('required|string|max:20')]
  public $phone;

  #[Rule('required|email')]
  public $email;

  #[Rule('max:255')]
  public $website;

  #[Rule('required|string|max:200')]
  public $address;

  #[Rule('string|max:50')]
  public $registration_no;

  #[Rule('required|string|max:50')]
  public $pan_no;

  #[Rule('boolean')]
  public $is_active = true;

  public function set(Company $company)
  {
    $this->name = $company->name;
    $this->logoUrl = $company->logo ? asset("storage/" . $company->logo) : "";
    $this->logo = null;
    $this->contact_person = $company->contact_person;
    $this->code = $company->code;
    $this->phone = $company->phone;
    $this->email = $company->email;
    $this->website = $company->website;
    $this->address = $company->address;
    $this->registration_no = $company->registration_no;
    $this->pan_no = $company->pan_no;
    $this->company = $company;
  }

  public function update()
  {
    $this->validate();
    Log::info("Params for updating general information for company with id {$this->company->id}: ", $this->all());
    DB::beginTransaction();
    try {
      $this->company->update([
        "name" => $this->name,
        "contact_person" => $this->contact_person,
        "code" => $this->code,
        "phone" => $this->phone,
        "email" => $this->email,
        "website" => $this->website,
        "address" => $this->address,
        "registration_no" => $this->registration_no,
        "pan_no" => $this->pan_no,
        "is_active" => $this->is_active,
      ]);
      Log::info("General Information Updated");
      if ($this->logo) {
        if ($this->company->logo) {
          File::delete(public_path('storage/' . $this->company->logo));
          Log::info("Old Logo Deleted");
        }
        $file = $this->logo;
        $companyLogo = $this->logo->storeAs("uploads/company/logo", $this->company->id . '-' . time() . '.' . $file->extension(), "public");
        $this->company->logo = $companyLogo;
        $this->company->save();
        Log::info("New Logo saved and stored as $companyLogo");
      }
      session([Constant::SESSION_COMPANY => $this->company]);
      DB::commit();
      return true;
    } catch (\Exception $e) {
      DB::rollBack();
      Log::error("Error while updating general info: ", $e->getMessage());
      return false;
    }
  }
}
