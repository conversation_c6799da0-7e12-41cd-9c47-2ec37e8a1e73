<?php

namespace App\Livewire\Admin\Company;

use App\Http\Helpers\Constant;
use App\Traits\WithNotify;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;

#[Title('Add Company')]

class AddCompany extends Component
{
    use WithFileUploads, WithNotify;

    public CompanyForm $form;
    public $hasParent = false;

    public function mount()
    {
        $this->form->setHasParent(session(Constant::SESSION_HAS_PARENT_COMPANY));
    }

    public function addCompany()
    {
        $created = $this->form->create();
        if ($created) {
            redirect(route('companyInfo'));
            $this->notify("Company created")->send();
        } else {
            $this->notify("Something went wrong")->type("error")->send();
        }
    }

    public function updated($attrs)
    {
        if ($attrs === "form.rf_scheme") {
            $this->form->is_limit_activated = false;
        }
    }

    public function resetRfScheme()
    {
        $this->form->resetRFValue();
    }

    public function render()
    {
        return view('livewire.admin.company.add-company');
    }
}
