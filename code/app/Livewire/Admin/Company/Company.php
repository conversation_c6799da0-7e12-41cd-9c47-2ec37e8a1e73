<?php

namespace App\Livewire\Admin\Company;

use App\Http\Helpers\Constant;
use App\Livewire\Admin\Company\Form\CITForm;
use App\Livewire\Admin\Company\Form\CompanySettingForm;
use App\Livewire\Admin\Company\Form\GeneralInfoForm;
use App\Livewire\Admin\Company\Form\GratuityForm;
use App\Livewire\Admin\Company\Form\RFForm;
use App\Models\configs\Company as CompanyModel;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Title('Company')]
class Company extends Component
{
    use WithNotify, WithFileUploads;

    public $companyCode;
    public CompanyModel $companyInfo;
    public $isParentCompany;

    public GeneralInfoForm $generalInfoForm;
    public RFForm $rfForm;
    public CITForm $citForm;
    public GratuityForm $gratuityForm;
    public CompanySettingForm $companySettingForm;

    public function mount($code = null)
    {
        $this->companyCode = $code;
        $this->companyInfo = new CompanyModel;
        $this->setCompanyInfo();
    }

    public function setCompanyInfo()
    {
        $company = null;
        if ($this->companyCode) {
            $company = CompanyModel::where("code", $this->companyCode)->first();
            if (!$company) abort("404", "Company Not Found");
        } else {
            $company = CompanyModel::findOrFail(session(Constant::SESSION_COMPANY)?->id);
        }
        $this->companyInfo = $company;
        $this->isParentCompany = $this->companyInfo->is_parent === "Y";
        $this->setFormData();
    }

    #[On('hidden.bs.modal')]
    public function setFormData()
    {
        $this->generalInfoForm->set($this->companyInfo);
        $this->rfForm->set($this->companyInfo);
        $this->citForm->set($this->companyInfo);
        $this->gratuityForm->set($this->companyInfo);
        $this->companySettingForm->set($this->companyInfo);
        $this->dispatch("clear-company-logo");
    }

    public function toggleState()
    {
        $this->companyInfo->allow_job_seat = $this->allowJobSeat ? "Y" : "N";
        $this->companyInfo->save();
    }

    public function updateGeneralInfo()
    {
        $updated = $this->generalInfoForm->update();
        if ($updated) {
            $this->notify("General information updated")->send();
            $this->dispatch("hide-general-model");
            $this->setCompanyInfo();
        } else {
            $this->notify("Error while updating general information")->type("error")->send();
        }
    }

    public function updateRfInfo()
    {
        $this->rfForm->update();
        $this->notify("RF information updated")->send();
        $this->dispatch("hide-rf-model");
        $this->setCompanyInfo();
    }

    public function updateCITInfo()
    {
        $this->citForm->update();
        $this->notify("CIT information updated")->send();
        $this->dispatch("hide-cit-model");
        $this->setCompanyInfo();
    }

    public function updateGratuityInfo()
    {
        $this->gratuityForm->update();
        $this->notify("Gratuity information updated")->send();
        $this->dispatch("hide-gratuity-model");
        $this->setCompanyInfo();
    }

    public function updateCompanySetting()
    {
        $this->companySettingForm->update();
        $this->notify("Company Setting Updated")->send();
        $this->dispatch("hide-company-setting-modal");
        $this->setCompanyInfo();
    }

    #[Computed()]
    public function childCompanies()
    {
        return $this->companyInfo?->childCompanies;
    }

    public function render()
    {
        return view('livewire.admin.company.company');
    }
}
