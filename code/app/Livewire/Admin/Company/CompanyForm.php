<?php

namespace App\Livewire\Admin\Company;

use App\Http\Helpers\Constant;
use App\Models\configs\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule as ValidationRule;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Validate;
use Livewire\Form;

class CompanyForm extends Form
{
    public $hasParent;

    #[Rule('required|string|max:100')]
    public $name;

    #[Rule('required|image|mimes:jpg,png,jpeg|max:5000')]
    public $logo;

    #[Rule('required|string|max:100')]
    public $contact_person;

    #[Rule('required|string|max:10|unique:companies,code')]
    public $code;

    #[Rule('required|string|max:20')]
    public $phone;

    #[Rule('required|email')]
    public $email;

    #[Rule('max:255')]
    public $website;

    #[Rule('required|string|max:200')]
    public $address;

    #[Rule('nullable|string|max:50')]
    public $registration_no;

    #[Rule('required|string|max:50')]
    public $pan_no;

    public $parent_id = null;

    #[Validate('boolean')]
    public $is_active = true;

    #[Rule('nullable|in:SSF,PF,OPT')]
    public $rf_scheme;

    public $rfc_company = 0;
    public $rfc_employee = 0;
    public $rf_scheme_no;
    public $rf_scheme_start_date;
    public $rf_limit = 300000;

    #[Rule('boolean')]
    public $cit_enabled = false;

    #[Validate('required_if:cit_enabled,true', message: "The bank code field is required")]
    public $cit_code;

    #[Validate('required_if:cit_enabled,true', message: "The bank name field is required")]
    public $cit_bank;

    #[Validate('boolean')]
    public $is_limit_activated = false;

    #[Validate('required|numeric|min:0|max:99')]
    public $gratuity = 8.33;

    public function rules(): array
    {
        return [
            "rfc_company" => ["numeric", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
            "rfc_employee" => ["numeric", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
            "rf_scheme_no" => ["max:50", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
            "rf_scheme_start_date" => ["max:50",  "date", ValidationRule::requiredIf(fn () => $this->rf_scheme != null), "nullable"],
            "rf_limit" => ["numeric", ValidationRule::requiredIf(fn () => $this->rf_scheme != null)],
        ];
    }

    public function messages()
    {
        return [
            "rf_scheme_start_date.date_format" => "Invalid Date Format"
        ];
    }
    public function validationAttributes()
    {
        return [
            'name' => 'company name',
        ];
    }


    public function setHasParent($hasParent)
    {
        $this->hasParent = $hasParent;
    }

    public function resetCITInfo()
    {
        $this->reset(["cit_code", "cit_bank"]);
        $this->resetValidation(["cit_code", "cit_bank"]);
    }

    public function create()
    {
        $validated = $this->validate();
        Log::info("Parameters for creating company: ", $validated);
        DB::beginTransaction();
        try {
            $file = $this->logo;
            $validated['logo'] = "";
            $validated['parent_id'] = session(Constant::SESSION_PARENT_COMPANY_ID) ?? null;
            $validated['is_parent'] = session(Constant::SESSION_HAS_PARENT_COMPANY) ? "N" : "Y";
            $validated['rf_scheme'] = $validated['rf_scheme'] ? $validated['rf_scheme'] : null;
            $company = Company::create($validated);
            $validated['logo'] = $this->logo->storeAs("uploads/company/logo", $company->id . '-' . time() . '.' . $file->extension(), "public");
            $company->logo = $validated['logo'];
            $company->save();

            if (!session(Constant::SESSION_HAS_PARENT_COMPANY))
                session([Constant::SESSION_COMPANY => $company]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error while creating company " . $e->getMessage());
            return false;
        }
    }

    public function resetRFValue()
    {
        $this->reset(["rfc_company", "rfc_employee", "rf_scheme_no", "rf_scheme_start_date", "rf_limit"]);
        $this->resetValidation(["rfc_company", "rfc_employee", "rf_scheme_no", "rf_scheme_start_date", "rf_limit"]);
    }
}
