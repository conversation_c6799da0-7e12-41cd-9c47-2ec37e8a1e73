<?php

namespace App\Livewire;

use App\Http\Repositories\UserRepository;
use App\Traits\WithNotify;
use Livewire\Component;

class ImpersonationBar extends Component
{
    use WithNotify;

    public function returnBack()
    {
        $userRepo = new UserRepository;
        $response = $userRepo->loginAsAnotherUser(session('user_id_main'), true);
        if (!$response['status']) $this->notify($response['message'])->type('error')->send();
        else return \redirect(route('employeelist'));
    }
    public function render()
    {
        return view('livewire.impersonation-bar');
    }
}
