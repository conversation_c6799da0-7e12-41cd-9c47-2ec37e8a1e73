<?php

namespace App\Livewire\Actions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

class Logout
{
    /**
     * Log the current user out of the application.
     */
    public function __invoke(): void
    {
        Cache::tags("employee-".auth()->user()?->id)->flush();
        $user = Auth::user();

        // Clear the remember token if the user is logged in
        if ($user) {
            $user->remember_token = null;
            $user->save();
        }
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();
    }
}
