<?php

namespace App\Livewire;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class Profile extends Component
{
    public string $first_name = '';
    public string $last_name = '';
    public string $email = '';
    public string $gender = '';
    public string $address = '';
    public string $zip_code = '';
    public string $city = '';
    public string $phone = '';

    public function mount()
    {
        $this->first_name = auth()->user()->first_name ?? '';
        $this->last_name = auth()->user()->last_name ?? '';
        $this->email = auth()->user()->email ?? '';
        $this->gender = auth()->user()->gender ?? '';
        $this->address = auth()->user()->address ?? '';
        $this->zip_code = auth()->user()->zip_code ?? '';
        $this->city = auth()->user()->city ?? '';
        $this->phone = auth()->user()->phone ?? '';
    }

    public function updateProfile(): void
    {
        $user = Auth::user();
        $validated = $this->validate([
            'first_name' => ['required', 'string', 'max:150'],
            'last_name' => ['required', 'string', 'max:150'],
            'email' => ['required', 'email', 'max:150', Rule::unique('users')->ignore($user->id)],
            'gender' => ['required', Rule::in(['male', 'female', 'other'])],
            'address' => ['required', 'string', 'max:255'],
            'zip_code' => ['string', 'max:10'],
            'city' => ['required', 'string', 'max:100'],
            'phone' => ['required', 'string', 'max:20']
        ]);

        $user->fill($validated);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }
        $user->save();

        $this->dispatch('profile-updated', name: $user->name);
    }

    #[Computed()]
    public function timelineList()
    {
        return \Spatie\Activitylog\Models\Activity::where('causer_type', 'App\Models\User')
            ->where('causer_id', auth()->user()->id)
            ->latest('created_at')
            ->take(3)
            ->get();
    }

    #[Layout('layouts.app')]
    #[Title('Profile')]
    public function render()
    {
        return view('livewire.profile')->with(
            ['user' => User::find(auth()->user()->id)]
        );
    }
}
