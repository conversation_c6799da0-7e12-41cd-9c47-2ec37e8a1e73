<?php

namespace App\Livewire\Employees;

use App\Http\Repositories\IopsRepository;
use App\Http\Helpers\Enums\AgeGroups;
use App\Http\Repositories\TerminationRepository;
use App\Http\Services\ScopeFetcher;
use App\Jobs\SendResetPasswordEmailJob;
use App\Models\configs\Job;
use App\Models\configs\OutsourceCompany;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\EmployeeCategory;
use App\Models\User;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use CodeBright\OauthAnd2fa\OauthAnd2fa;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;

#[Title('Employee List')]
class EmployeeList extends Component
{
    use WithNotify, WithDataTable, MultiselectEmployeeSearch;

    private $model;

    public $company, $region, $branch,  $password, $resetPasswordEmail,
        $showTerminatedEmployee = false, $employee_ids = [];

    #[Url(as: 'type', except: '')]
    public $type = '';
    #[Url(as: 'gender', except: '')]

    public $gender = '';

    #[Url(as: 'maritalStatus', except: '')]
    public $maritalStatus = '';

    #[Url(as: 'department', except: '')]
    public $department = '';

    #[Url(as: 'job', except: '')]
    public $job = '';

    #[Url(as: 'ageGroup', except: '')]
    public $ageGroup = '';

    public array $scopeWiseView = [];
    #[Url(as: 'category', except: '')]
    public $category = '';

    public $outsourceCompany = null;

    public function __construct()
    {
        $this->model = new \App\Models\Employee\Employee;
    }

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->withTrashed = true;
        $this->setPermissionForEmployeeDropdown();
        $this->setExcelOptions();

        if (request()->has('department')) {
            $this->department = request()->query('department');
        }
        if(request()->has('category')){
            $types = request()->query('category');
            $this->category = $this->getEmployeeTypeId($types);
        }

        $this->scopeWiseView = (new ScopeFetcher())->scopeWiseView();
    }

    public function profile(int $id)
    {
        $this->redirect('profile/' . $id);
    }

    public function getEmployeeTypeId(int|string $types)
    {
        if(is_numeric($types)) return $types;
        return EmployeeCategory::where('type', $types)->value('id');
    }

    public function resetUserPassword($employee)
    {
        $user = User::leftJoin("employees", 'users.id', '=', 'employees.user_id')->where('user_id', '=', $employee['user_id'])->select('users.id', 'users.username', 'users.password')->first();
        if (!$user) {
            $this->notify('Unable to reset password for the user!!')->type('error')->send();
            return false;
        }

        $this->resetPasswordEmail = $employee['org_email'] ?? '';
        $this->password = Str::random(10);
        $hashedPassword = Hash::make($this->password);
        DB::beginTransaction();
        try {
            $user->password = $hashedPassword;
            $user->password_change_date = null;
            $user->save();

            if (vianetHrm() || konnectHrm()) {
                $iopsRepo = new IopsRepository;
                $smsContent = "Your HRIS password has been changed. Your new password is: $this->password.";
                if (konnectHrm()) {
                    $smsContent = "Your Konnect HRM password has been changed. Your new password is: $this->password.";
                }
                $response = $iopsRepo->sendSMS($employee['id'], $smsContent);
                if (!$response['status']) {
                    logInfo('Unable to send SMS for password reset: ' . print_r($response, true));
                    $this->notify('Unable to send SMS.')->type('error')->send();
                    DB::rollBack();
                    return false;
                }
                $employee_iops_id = EmployeeOrg::where('employee_id', $employee['id'])->pluck('iops_id')->first();
                if ($employee_iops_id) {
                    $iopsRepo->changePassword('', $this->password, $employee_iops_id);
                }
            }

            DB::commit();
            if ($user->id !== Auth::id()) {
                // Auth::setUser($user)->logoutOtherDevices($this->password);
                $currentSessionId = session()->getId();
                DB::table('sessions')->where('user_id', $user->id)->where('id', '!=', $currentSessionId)->delete();
            }
            $this->dispatch("show-user-password-model");

            if (!vianetHrm()) {
                try {
                    if ($this->resetPasswordEmail) {
                        SendResetPasswordEmailJob::dispatch($this->resetPasswordEmail, $user->username, $this->password, $employee['full_name'] ?? "")->onQueue('resetPasswordEmail');
                    } else {
                        $this->notify('Unable to send email. You don\'t have an email.')->type('error')->send();
                    }
                } catch (\Exception $e) {
                    Log::error('Unable to send email for reset password: ' . $e->getMessage());
                    $this->notify('Unable to send email.')->send();
                }
            }
            $this->notify('Password reset successfully')->type('success')->send();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Unable to reset password: ' . $e->getMessage());
            $this->notify('Unable to reset password!!')->type('error')->send();
        }
    }

    #[On("hide.bs.modal")]
    public function resetPropertyPasswordAndEmail()
    {
        $this->reset(['password', 'resetPasswordEmail']);
    }

    public function updated($value)
    {
        // if (\in_array($value, ['company', 'region', 'branch', 'department', 'employee_ids', 'category'])) {
            $this->resetPage();
        // }
        if (\in_array($value, ['company'])) {
            unset($this->branches, $this->departments, $this->outsourceCompanyList);
            $this->branch = null;
            $this->department = null;
            $this->outsourceCompany = null;
        }

        if ($value == 'region') {
            unset($this->branches);
            $this->reset('branch');
        }
        if ($value == 'company') {
            unset($this->regions);
            $this->reset('region');
            if (!count($this->regions)) {
                unset($this->branches);
                $this->reset('branch');
            }
        }
    }

    #[Title('Employee List')]
    #[Layout('layouts.app')]
    public function render()
    {
        return view('livewire.employees.employee-list');
    }

    private function listQuery()
    {

        $query = Employee::leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin("companies as comp", "comp.id", "=", "employees.company_id")
            ->leftJoin("branches as branch", "branch.id", "=", "employee_org.branch_id")
            ->leftJoin("departments as dep", "dep.id", "=", "employee_org.department_id")
            ->leftJoin("emp_statuses as emp_status", "emp_status.id", "=", "employee_org.employee_status_id")
            ->leftJoin("outsource_companies as outsource", "outsource.id", "=", "employee_org.outsource_company_id")
            ->leftJoin("shifts as shift", "shift.id", "=", "employee_org.shift_id")
            ->leftJoin("users as user", "user.id", "=", "employees.user_id")
            ->leftJoin("units as unit", "unit.id", "=", "employee_org.unit_id")
            ->leftJoin("payslips as payslip", "employees.id", "=", "payslip.employee_id")
            ->leftJoin("designations as des", "des.id", "=", "payslip.designation_id")
            ->leftJoin("employee_pgrades as pgrade", "pgrade.id", "=", "payslip.pgrade_id")
            ->leftJoin("employee_bands as band", "band.id", "=", "payslip.band_id")
            ->leftJoin("jobs as j", "payslip.job_id", "=", "j.id")
            ->when($this->showTerminatedEmployee, function ($query) {
                $query->withTrashed();
            })
            ->when(count($this->employee_ids), function ($query) {
                $query->whereIn('employees.id', $this->employee_ids);
            })
            ->when(!$this->showTerminatedEmployee, function ($query) {
                $query->whereNull("employee_org.termination_date");
            })
            ->when($this->company, function ($query) {
                $query->where("employees.company_id", $this->company);
            })
            ->when($this->region, function ($query) {
                $query->where('employee_org.region_id', $this->region);
            })
            ->when($this->branch, function ($query) {
                $query->where("employee_org.branch_id", $this->branch);
            })
            ->when($this->maritalStatus, function ($query) {
                $query->where("employees.mstat", $this->maritalStatus);
            })
            ->when($this->category, function ($query) {
                $query->where("employee_org.employee_category_id", $this->category);
            })
            ->when($this->gender, function ($query) {
                $query->where("employees.gender", $this->gender);
            })
            ->when($this->department, function ($query) {
                $query->where("employee_org.department_id", $this->department);
            })
            ->when($this->job, function ($query) {
                $query->where("payslip.job_id", $this->job);
            })
            ->when($this->outsourceCompany, function ($query) {
                $query->where("outsource.id", $this->outsourceCompany);
            })
            ->when($this->ageGroup, function ($query) {
                $currentDate = now();
                switch ($this->ageGroup) {
                    case AgeGroups::AGE_45_49:
                        $dateLimitStart = $currentDate->copy()->subYears(50)->addDay()->toDateString();
                        $dateLimitEnd = $currentDate->copy()->subYears(45)->toDateString();
                        $query->whereBetween('employees.dob', [$dateLimitStart, $dateLimitEnd]);
                        break;
                    case AgeGroups::AGE_50_54:
                        $dateLimitStart = $currentDate->copy()->subYears(value: 55)->addDay()->toDateString();
                        $dateLimitEnd = $currentDate->copy()->subYears(50)->toDateString();
                        $query->whereBetween('employees.dob', [$dateLimitStart, $dateLimitEnd]);
                        break;
                    case AgeGroups::AGE_55_59:
                        $dateLimitStart = $currentDate->copy()->subYears(60)->addDay()->toDateString();
                        $dateLimitEnd = $currentDate->copy()->subYears(55)->toDateString();
                        $query->whereBetween('employees.dob', [$dateLimitStart, $dateLimitEnd]);
                        break;
                    case AgeGroups::AGE_60_AND_ABOVE:
                        $dateLimit = $currentDate->copy()->subYears(60)->toDateString();
                        $query->where('employees.dob', '<=', $dateLimit);
                        break;
                }
            })
            ->when($this->type === 'inhouse', function ($query) {
                $query->whereNull('employee_org.outsource_company_id');
            })
            ->when($this->type === 'outsource', function ($query) {
                $query->whereNotNull('employee_org.outsource_company_id');
            })
            ->where(function ($query) {
                $query->whereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Active"
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereRaw('payslip.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Expired"
                    AND NOT EXISTS (
                        SELECT 1 FROM payslips active
                        WHERE active.employee_id = p.employee_id
                        AND active.status = "Active"
                    )
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereNull('payslip.id');
            })
            ->where(function ($query) {
                $query->where('employee_org.employee_code', 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            })
            ->where(function ($query) {
                if (!auth()->user()->can(PermissionList::EMPLOYEE_HIDDEN_PROFILE_VIEW) || isImpersonated())
                    $query->where("hide_profile", 0);
            });

        // if (!permissionViewAll()) {
        //     if (permissionViewBranchOnly() && permissionViewDepartmentOnly()) {
        //         $query = $query->where(function ($query) {
        //             $query->where('employee_org.branch_id', currentEmployee()?->organizationInfo?->branch_id)
        //                 ->orWhere('employee_org.department_id', currentEmployee()?->organizationInfo?->department_id);
        //         });
        //     } elseif (permissionViewBranchOnly()) {
        //         $query = $query->where('employee_org.branch_id', currentEmployee()?->organizationInfo?->branch_id);
        //     } elseif (permissionViewDepartmentOnly()) {
        //         $query = $query->where('employee_org.department_id', currentEmployee()?->organizationInfo?->department_id);
        //     } else {
        //         $query = $query->where('employees.id', currentEmployee()?->id);
        //     }
        // }

        $query = filterEmployeesByScope($query, 'employee_org');
        $query->select(
            'employees.id as id',
            Employee::selectNameRawQuery(),
            DB::raw("CONCAT(comp.code, '-', employee_org.employee_code) as emp_code"),
            'user.username as username'
        );

        // Added as per requested by HR team and provided permission by Sysops team.
        if (Schema::hasColumn('employee_org', 'iops_id')) {
            $query->addSelect('employee_org.iops_id as iops_id');
        } else {
            $query->addSelect(DB::raw('NULL as iops_id'));
        }

        $query->addSelect(
            'employees.gender',
            'employees.dob',
            'employees.dob_nepali',
            'employees.mstat as marital_status',
            'employees.email',
            'employees.phone',
            'employees.nationality',
            'employees.citizenship',
            'employees.user_id',
            'employee_org.email as org_email',
            'employee_org.cug',
            'employees.permanent_address',
            'employees.temporary_address',
            'employees.father',
            'employees.mother',
            'employees.spouse',
            'employees.contact_phone',
            'employees.contact_person',
            'employee_org.doj as date_of_joining',
            'employee_org.doj_inhouse',
            'employee_org.bank',
            'employee_org.bank_account_no',
            'employee_org.rf_no',
            'employee_org.cit_no',
            'employee_org.pan_no',
            'employee_org.biometric_id',

            'emp_status.name as emp_status',
            DB::raw("IFNULL(outsource.name, comp.name) as vendor"),
            'branch.name as branch_name',
            'dep.name as dep_name',
            'unit.name as unit_name',
            'j.name as job',
            'des.title as designation',
            DB::raw('CONCAT(band.name, "-", pgrade.name) as grade_band'),
            'outsource.name as outsource_company',
            'employee_org.deleted_at',
            'employee_org.termination_date',
            'employee_org.termination_request_date',
            'employee_org.termination_reason',
            'shift.start_time as shift_start_time',
            'shift.end_time as shift_end_time',
            'shift.name as shift_name',
            DB::raw("DAYNAME(DATE_ADD(DATE('2025-04-20'), INTERVAL shift.day_off DAY)) as off_day"),
        );

        return $this->applySorting($query);
    }


    #[Computed()]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "Employee List.xlsx";
        $this->exportIgnoreColumns = ['id', 'user_id', 'outsource_company', 'deleted_at'];
        $this->exportColumnHeadersMap = [
            'full_name' => 'Employee Name',
            'emp_code' => 'Employee Code',
            'username' => 'Username',
            'email' => 'Email',
            'phone' => 'Phone',
            'org_email' => 'Organization Email',
            'cug' => 'CUG',
            'emp_status' => 'Status',
            'vendor' => 'Vendor',
            'branch_name' => 'Branch',
            'dep_name' => "Department",
            'unit_name' => "Unit",
            'job' => "Job",
            'grade_band' => "Grade",
            'off_day' => "Day Off"
        ];

        if (vianetHrm()) {
            $this->exportColumnHeadersMap = array_merge($this->exportColumnHeadersMap, [
                'iops_id' => 'IOPS ID',
            ]);
        } else {
            $this->exportIgnoreColumns = array_merge($this->exportIgnoreColumns, ['iops_id']);
        }
    }

    public function applyMoreFilters()
    {
        unset($this->list);
    }

    public function resetMoreFilters()
    {
        $this->reset(['showTerminatedEmployee']);
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return (new ScopeFetcher())->fetchCompany();
    }

    protected function getEnumValues(string $table, string $column): array
    {
        $result = DB::select(
            "SHOW COLUMNS FROM `{$table}` 
        WHERE Field = ?",
            [$column]
        );

        if (empty($result)) return [];

        preg_match('/^enum\((.*)\)$/', $result[0]->Type, $matches);

        return isset($matches[1])
            ? explode(',', str_replace("'", "", $matches[1]))
            : [];
    }

    #[Computed(persist: true)]
    public function genderOptions()
    {
        return $this->getEnumValues('employees', 'gender');
    }

    #[Computed(persist: true)]
    public function typeOptions()
    {
        return [
            'inhouse' => 'Inhouse',
            'outsource' => 'Outsource',
        ];
    }

    #[Computed(persist: true)]
    public function maritalStatusList()
    {
        return $this->getEnumValues('employees', 'mstat');
    }
    #[Computed(persist: true)]
    public function employeeCategoryList()
    {
        return EmployeeCategory::all()->where('is_countable', true)->pluck('type', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function regions()
    {
        return (new ScopeFetcher())->fetchRegion(company: $this->company, pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function branches()
    {
        $this->region = $this->region == "" ? null : $this->region;
        return (new ScopeFetcher())->fetchBranch($this->company, $this->region);
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return (new ScopeFetcher())->fetchDepartment($this->company);
    }

    #[Computed()]
    public function jobList()
    {
        return \App\Models\configs\Job::leftJoin('job_departments', 'jobs.id', '=', 'job_departments.job_id')
            ->when($this->company, function ($query) {
                $query->where('job_departments.company_id', $this->company);
            })
            ->when($this->region, function ($query) {
                $query->where('job_departments.region_id', $this->region);
            })
            ->when($this->branch, function ($query) {
                $query->where('job_departments.branch_id', $this->branch);
            })
            ->when($this->department, function ($query) {
                $query->where('job_departments.department_id', $this->department);
            })
            ->orderBy('jobs.name')
            ->pluck('jobs.name', 'jobs.id');
    }

    #[Computed(persist: true)]
    public function outsourceCompanyList()
    {
        return OutsourceCompany::when($this->company, function ($query) {
            $query->where('company_id', $this->company);
        })
            ->where('status', 1)->pluck('name', 'id');
    }

    public function impersonate($userId)
    {
        $userRepo = new \App\Http\Repositories\UserRepository;
        $response = $userRepo->loginAsAnotherUser($userId);
        if (!$response['status'])
            $this->notify($response['message'])->type("error")->send();
        else
            return \redirect(route('dashboard'));
    }

    public function removeTwoFactor($employeeId)
    {
        try {
            $employee = Employee::find($employeeId);
            if (!$employee)
                $this->notify("Employee not found")->type("error")->send();
            $user_id = $employee->user?->id;
            if (!$user_id)
                $this->notify("User not found for this employee")->type("error")->send();
            OauthAnd2fa::removeTwoFactorAuthentication($user_id);
            $this->notify("Two Factor Authentication removed successfully")->send();
        } catch (\Exception $e) {
            Log::error("Error while removing two-factor authentication: " . $e->getMessage());
            $this->notify("Error while removing two-factor authentication. Please try again later.")->type("error")->send();
        }
    }

    public function exportAllEmployees()
    {
        $data = $this->listQuery()->get();
        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);

                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];
                // Provide headings for your export file
                // You can customize this based on your data structure
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return $headings;
            }
        }, $this->exportFileName);
    }

    public function restoreEmployee($employeeId, $type)
    {
        $terminationRepo = new TerminationRepository;
        $response = $terminationRepo->restoreEmployee($employeeId, $type);

        if ($response['status']) {
            $message = ($type === 'permanent' || $type === 'iops_permanent')
                ? "Employee restored successfully"
                : "Employee restored for a day.";
            return $this->notify($message)->send();
        }

        return $this->notify($response['message'])->type('error')->send();
    }
}
