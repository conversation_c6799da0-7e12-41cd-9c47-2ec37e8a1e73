<?php

namespace App\Livewire\Employees\Forms;

use App\Models\Employee\Employee;
use App\Models\OptionalFields\OptionalField;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Validate;
use Livewire\Form;
use PermissionList;

class PersonalInfoForm extends Form
{
    public Employee $personalInfo;

    public $phone, $email, $mstat, $nationality, $citizenship, $permanent_address, $temporary_address, $blood_group, $private_vehicle_identity, $optionalFieldList = ["blood_group", "private_vehicle_identity"];

    public function set(Employee $personalInfo)
    {
        $this->phone = $personalInfo->phone;
        $this->email = $personalInfo->email;
        $this->mstat = $personalInfo->mstat;
        $this->nationality = $personalInfo->nationality;
        $this->citizenship = $personalInfo->citizenship;
        $this->permanent_address = $personalInfo->permanent_address;
        $this->temporary_address = $personalInfo->temporary_address;
        $this->personalInfo = $personalInfo;

        foreach ($this->optionalFieldList as $field) {
            $conditions = [
                'model_type' => get_class(new Employee()),
                'model_id' => $this->personalInfo->id,
                'field_key' => $field,
            ];

            $optionalField = OptionalField::where($conditions)->first();

            if ($optionalField) {
                $this->$field = $optionalField->field_value;
            }
        }
    }

    public function rules()
    {
        return [
            'phone' => [
                'required',
                'regex:/^(\+\d{1,4})?(\d{10})$/',
            ],
        ];
    }

    public function update()
    {
        $this->validate();

        DB::beginTransaction();
        try {
            $this->personalInfo->update([
                "phone" => $this->phone,
                "email" => $this->email,
                ...(auth()?->user()?->can(PermissionList::PROFILE_MARITAL_STATUS_EDIT) ? [
                    "mstat" => $this->mstat,
                ] : []),
                "nationality" => $this->nationality,
                "citizenship" => $this->citizenship,
                "permanent_address" => $this->permanent_address,
                "temporary_address" => $this->temporary_address,
            ]);

            foreach ($this->optionalFieldList as $field) {
                $conditions = [
                    'model_type' => get_class(new Employee()),
                    'model_id' => $this->personalInfo->id,
                    'field_key' => $field,
                ];

                $optionalField = OptionalField::where($conditions)->first();

                if ($optionalField) {
                    $optionalField->update([
                        'field_value' => $this->$field,
                    ]);
                } else {
                    OptionalField::create([
                        "model_type" => get_class(new Employee()),
                        "model_id" => $this->personalInfo->id,
                        "field_key" => $field,
                        "field_value" => $this->$field
                    ]);
                }
            }
            DB::commit();
            return ["status" => true, "message" => "Personal information updated successfully!!"];
        } catch (\Exception $e) {
            DB::rollBack();
            return ["status" => false, "message" => "Failed to updated personal information!!"];
        }
    }

    public function resetPersonalInfoValue()
    {
        $this->reset(["phone", "email", "mstat", "nationality", "citizenship", "permanent_address", "blood_group", "private_vehicle_identity"]);
        $this->resetValidation(["phone", "email", "mstat", "nationality", "citizenship", "permanent_address", "blood_group", "private_vehicle_identity"]);
    }
}
