<?php

namespace App\Livewire\Employees\Forms;

use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use Livewire\Attributes\Validate;
use Livewire\Form;


class MiscInfoForm extends Form
{
    public Employee $personalInfo;

    #[Validate('required')]
    public $biometricId;

    public $bank;

    public $acc_no;

    public $outsource_company;

    public function set(Employee $personalInfo)
    {
        $this->biometricId = $personalInfo->organizationInfo?->biometric_id;
        $this->bank = $personalInfo->organizationInfo?->bank;
        $this->acc_no = $personalInfo->organizationInfo?->bank_account_no;
        $this->outsource_company = $personalInfo->organizationInfo?->outsource_company_id;
        $this->personalInfo = $personalInfo;
    }

    public function update()
    {
        $this->validate();

        try {
            $employeeOrg = EmployeeOrg::where('employee_id', $this->personalInfo->id)->first();

            if ($employeeOrg) {
                $employeeOrg->update([
                    "biometric_id" => $this->biometricId,
                    "bank" => $this->bank,
                    "bank_account_no" => $this->acc_no,
                    "outsource_company_id" => $this->outsource_company,
                ]);
            } else {
                return ["status" => false, "message" => "EmployeeOrg record not found!!"];
            }

            return ["status" => true, "message" => "Misc information updated successfully!!"];
        } catch (\Exception $e) {
            return ["status" => false, "message" => "Failed to update misc information!!"];
        }
    }

    public function resetMiscInfoValue()
    {
        $this->reset(["biometricId", "bank", "acc_no", "outsource_company"]);
        $this->resetValidation(["biometricId", "bank", "acc_no", "outsource_company"]);
    }
}
