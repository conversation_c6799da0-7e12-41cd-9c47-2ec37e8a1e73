<?php

namespace App\Livewire\Employees\Forms;

use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use Livewire\Form;


class RetirementInfoForm extends Form
{
    public Employee $personalInfo;

    public $rf_no;

    public $cit_no;

    public function set(Employee $personalInfo)
    {
        $this->rf_no = $personalInfo->organizationInfo?->rf_no;
        $this->cit_no = $personalInfo->organizationInfo?->cit_no;
        $this->personalInfo = $personalInfo;
    }

    public function update()
    {
        try {
            $employeeOrg = EmployeeOrg::where('employee_id', $this->personalInfo->id)->first();

            if ($employeeOrg) {
                $employeeOrg->update([
                    "rf_no" => $this->rf_no,
                    "cit_no" => $this->cit_no,
                ]);
                return ["status" => true, "message" => "Retirement information updated successfully!!"];
            } else {
                return ["status" => false, "message" => "Failed to update retirement information!!"];
            }
        } catch (\Exception $e) {
            return ["status" => false, "message" => "Failed to update retirement information!!"];
        }
    }

    public function resetRetirementInfoValue()
    {
        $this->reset(["rf_no", "cit_no"]);
        $this->resetValidation(["rf_no", "cit_no"]);
    }
}
