<?php

namespace App\Livewire\Employees\Forms;

use App\Models\Employee\Employee;
use App\Models\EmployeeFamilyInformation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Validate;
use Livewire\Form;

class FamilyInfoForm extends Form
{
    public Employee $personalInfo;

    #[Validate('required')]
    public array $relations = [];

    public $removedDocuments = [];

    public function mount()
    {
        $relations = [];

        foreach ($this->personalInfo->familyInformation as $family) {
            $relations[$family->relation_type] = [
                'id'            => $family->id,
                'name'          => $family->name,
                'dob_eng'       => $family->dob_eng,
                'remarks'       => $family->remarks,
                'documents'     => $family->documents->keyBy('document_path', 'type')->toArray(),
                'new_documents' => [],
            ];
        }

        $this->relations = $relations;
    }


    public function setFamilyInfo(Employee $employee)
    {
        $this->personalInfo = $employee;
        $this->relations = [];

        $nameMap = [
            'grandfather' => $employee->grandfather,
            'father'      => $employee->father,
            'mother'      => $employee->mother,
        ];

        if ($employee->mstat == 'married') {
            $nameMap['spouse'] = $employee->spouse;
        }

        foreach ($nameMap as $relation => $name) {

            $family = $employee->familyInformation()->where('relation_type', $relation)->first();

            if (!$family) {
                $this->relations[$relation] = [
                    'id'            =>  null,
                    'name'          => $nameMap[$relation] ?? '',
                    'dob_eng'       =>  null,
                    'remarks'       =>  null,
                    'documents'     =>  [],
                    'new_documents' =>  [],

                ];
            } else {

                $this->relations[$relation] = [
                    'id'            => $family->id ?? null,
                    'name'          => $family->name ?? '',
                    'dob_eng'       => $family->dob_eng ?? null,
                    'remarks'       => $family->remarks ?? null,
                    'documents'     => $family ? $family->documents->pluck('document_path', 'type')->toArray() : [],
                    'new_documents' => [],
                ];
            }
        }
    }
    public function update()
    {
        $this->validate();

        $rules = [
            'relations.*.name'    => ['required'],
            'relations.*.dob_eng' => ['required', 'date', 'before_or_equal:' . Carbon::now()->subYears(18)->toDateString()]
        ];

        foreach ($this->relations as $key => $relation) {

            $existing = $relation['documents']      ?? [];
            $new      = $relation['new_documents']  ?? [];

            $combined = array_merge(
                array_keys(array_filter($existing)),
                array_keys(array_filter($new))
            );

            $hasNid = in_array('nid_front', $combined);
            $hasCitizenFront  = in_array('citizen_front', $combined);
            $hasCitizenBack  = in_array('citizen_back', $combined);

            if (!$hasNid && !$hasCitizenFront && !$hasCitizenBack) {
                $rules["relations.$key.new_documents.nid_front"]      = 'required_without_all:relations.' . $key . '.new_documents.citizen_front,relations.' . $key . '.new_documents.citizen_back';
                $rules["relations.$key.new_documents.citizen_front"]  = 'required_without:relations.' . $key . '.new_documents.nid_front';
                $rules["relations.$key.new_documents.citizen_back"]   = 'required_without:relations.' . $key . '.new_documents.nid_front';
            }

            if ($hasCitizenFront && !$hasCitizenBack) {
                $rules["relations.$key.new_documents.citizen_back"] = 'required';
            }

            if ($hasCitizenBack && !$hasCitizenFront) {
                $rules["relations.$key.new_documents.citizen_front"] = 'required';
            }
        }

        $messages = [
            'relations.*.new_documents.nid_front.required_without_all' =>
            'Please upload either NID OR both Citizen Front & Back.',

            'relations.*.new_documents.citizen_front.required_without' =>
            'Citizen Front is required when NID is missing.',

            'relations.*.new_documents.citizen_back.required_without' =>
            'Citizen Back is required when NID is missing.',

            'relations.*.new_documents.citizen_back.required' =>
            'Citizen Back is required when Citizen Front is uploaded.',

            'relations.*.new_documents.citizen_front.required' =>
            'Citizen Front is required when Citizen Back is uploaded.',

            'relations.*.dob_eng.before_or_equal' =>
            'Date of birth must be at least 18 years ago.',
        ];

        $attributes = [
            'relations.grandfather.name' => 'grandfather name',
            'relations.father.name'      => 'father name',
            'relations.mother.name'      => 'mother name',
            'relations.spouse.name'      => 'spouse name',

            'relations.grandfather.dob_eng' => 'grandfather date of birth',
            'relations.father.dob_eng'      => 'father date of birth',
            'relations.mother.dob_eng'      => 'mother date of birth',
            'relations.spouse.dob_eng'      => 'spouse date of birth',
        ];


        if (!empty($rules)) {
            $this->validate($rules, $messages, $attributes);
        }

        foreach ($this->relations as $relation => $data) {

            $family = $this->personalInfo
                ->familyInformation()
                ->where('relation_type', $relation)
                ->first();

            if (!$family) {
                $family = EmployeeFamilyInformation::create([
                    'employee_id'   => $this->personalInfo->id,
                    'relation_type' => $relation,
                    'name'          => $data['name'],
                    'dob_eng'       => $data['dob_eng'],
                    'remarks'       => $data['remarks'] ?? null,
                ]);
            } else {
                $family->update([
                    'name'    => $data['name'],
                    'dob_eng' => $data['dob_eng'],
                ]);
            }

            $newDocs = $data['new_documents'] ?? [];

            foreach ($newDocs as $type => $file) {
                if (!$file) continue;

                $old = $family->documents()->where('type', $type)->first();
                if ($old && $old->document_path) {
                    Storage::disk('public')->delete($old->document_path);
                }

                $path = $file->store('family_documents', 'public');

                $family->documents()->updateOrCreate(
                    ['type' => $type],
                    [
                        'document_path' => $path,
                        'status'        => 'Approved',
                        'actioned_by'   => currentEmployeeId(),
                    ]
                );
            }
        }

        return [
            "status"  => true,
            "message" => "Family information updated successfully!"
        ];
    }
}
