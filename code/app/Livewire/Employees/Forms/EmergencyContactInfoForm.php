<?php

namespace App\Livewire\Employees\Forms;

use App\Models\Employee\Employee;
use Livewire\Attributes\Validate;
use Livewire\Form;

class EmergencyContactInfoForm extends Form
{
    public Employee $personalInfo;

    #[Validate('required')]
    public $contact_person;

    public $contact_phone;

    #[Validate('required')]
    public $contact_relation;

    public function rules() {
        return [
            'contact_phone' => [
                'required',
                'regex:/^(\+\d{1,4})?(\d{7,10})$/',
            ],
        ];
    }

    public function set(Employee $personalInfo)
    {
        $this->contact_person = $personalInfo->contact_person;
        $this->contact_phone = $personalInfo->contact_phone;
        $this->contact_relation = $personalInfo->contact_relation;
        $this->personalInfo = $personalInfo;
    }

    public function update()
    {
        $this->validate();

        try {
            $this->personalInfo->update([
                "contact_person" => $this->contact_person,
                "contact_phone" => $this->contact_phone,
                "contact_relation" => $this->contact_relation,
            ]);
            return ["status" => true, "message" => "Emergency contact information updated successfully!!"];
        } catch (\Exception $e) {
            return ["status" => false, "message" => "Failed to update emergency contact information!!"];
        }
    }

    public function resetEmergencyContactInfoValue()
    {
        $this->reset(["contact_person", "contact_phone", "contact_relation"]);
        $this->resetValidation(["contact_person", "contact_phone", "contact_relation"]);
    }
}
