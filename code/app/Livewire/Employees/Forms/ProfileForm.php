<?php

namespace App\Livewire\Employees\Forms;

use App\Http\Helpers\Constant;
use App\Models\configs\Unit;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\OptionalFields\OptionalField;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;
use Livewire\Form;
use Livewire\Attributes\Computed;
use PermissionList;

class ProfileForm extends Form
{
  public Employee $personalInfo;

  public $profile_picture;
  public $profile_picture_new;

  #[Validate('required')]
  public $first_name;

  #[Validate('required')]
  public $last_name;

  public $department;

  #[Validate('required')]
  public $employee_code;

  #[Validate('required')]
  public $pan_no;

  public $supervisor;

  public $branch;

  public $designation_id;

  #[Validate('required')]
  public $company;

  public $phone;

  #[Validate('required')]
  public $dob;

  #[Validate('required|in:male,female,other')]
  public $gender;

  #[Validate('required')]
  public $shift;

  #[Validate('required')]
  public $doj;

  public $middle_name = "", $email, $address, $sub_branch, $unit, $cug, $dob_nepali = "", $office_vehicle_identity;

  public $employee_status_id;
  public $employee_category_id;

  public $optionalFieldList = ["office_vehicle_identity"];

  public function set(Employee $personalInfo)
  {
    $this->first_name = $personalInfo->first_name;
    $this->middle_name = $personalInfo->middle_name;
    $this->last_name = $personalInfo->last_name;
    $this->unit = $personalInfo->organizationInfo?->unit_id;
    $this->department = $personalInfo->organizationInfo?->department_id;
    $this->employee_code = $personalInfo->organizationInfo?->employee_code;
    $this->pan_no = $personalInfo->organizationInfo?->pan_no;
    $this->cug = $personalInfo->organizationInfo?->cug;
    $this->supervisor = $personalInfo->organizationInfo?->supervisor_id;
    $this->branch = $personalInfo->organizationInfo?->branch_id;
    $this->sub_branch = $personalInfo->organizationInfo?->sub_branch_id;
    $this->company = $personalInfo->company_id;
    $this->phone = $personalInfo->phone;
    $this->email = $personalInfo->organizationInfo?->email;
    $this->dob = $personalInfo->dob;
    $this->address = $personalInfo->permanent_address;
    $this->gender = $personalInfo->gender;
    $this->profile_picture = $personalInfo->profile_picture;
    $this->shift = $personalInfo->organizationInfo?->shift_id;
    $this->doj = $personalInfo->organizationInfo?->doj;
    $this->designation_id = $personalInfo->organizationInfo?->designation_id;
    $this->employee_status_id = $personalInfo->organizationInfo?->employee_status_id;
    $this->personalInfo = $personalInfo;
    $this->employee_category_id = $personalInfo->organizationInfo?->employee_category_id;

    $this->optionalFieldTransaction();
  }

  public function rules()
  {
    $employeeOrgId = EmployeeOrg::where('employee_id', $this->personalInfo->id)->select('id')->first();
    return [
      'phone' => [
        'required',
        'regex:/^(\+\d{1,4})?(\d{7,10})$/',
      ],
      'cug' => $this->cug ? [
        'regex:/^(\+\d{1,4})?(\d{10})$/',
      ] : ['nullable'],
      'unit' => count(Unit::where('department_id', $this->department)->get()) > 0 ? ['required'] : ['nullable'],
      "email" => ['nullable', 'email', Rule::unique('employee_org', 'email')->ignore($employeeOrgId->id)],
      "employee_category_id" => ['required', 'exists:employee_categories,id'],
      ...(
        auth()->user()->can(PermissionList::PROFILE_EMPLOYEE_STATUS_EDIT) ?
        ['employee_status_id' => ['required']] : []
      ),
      ...(
        auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN) ? [
          'branch' => ['required'],
          'department' => ['required'],
          'designation_id' => ['required', 'exists:designations,id']
        ] : []
      )

    ];
  }

  public function validationAttributes()
  {
    return [
      'employee_category_id' => 'employee category',
    ];
  }

  public function update()
  {
    $validated = $this->validate();

    DB::beginTransaction();
    try {
      logInfo("Parameters for updating employee: ", $validated);
      logInfo("Employee ID: " . $this->personalInfo->id);

      $employee = Employee::find($this->personalInfo->id);
      if ($employee) {
        $employee->update([
          "first_name" => $this->first_name,
          "middle_name" => $this->middle_name,
          "last_name" => $this->last_name,
          "company_id" => $this->company,
          "phone" => $this->phone,
          //"email" => $this->email,
          "dob" => $this->dob,
          ...(authorizePermission(PermissionList::PROFILE_GENDER_EDIT) ? [
            "gender" => $this->gender
          ] : [])
        ]);
      }

      $employeeOrg = EmployeeOrg::where('employee_id', $this->personalInfo->id)->first();
      if ($employeeOrg) {
        $employeeOrg->update([
          "employee_code" => $this->employee_code,
          "email" => $this->email,
          "branch_id" => $this->branch,
          "sub_branch_id" => $this->sub_branch,
          "department_id" => $this->department,
          "unit_id" => $this->unit,
          "cug" => $this->cug,
          "pan_no" => $this->pan_no,
          "supervisor_id" => $this->supervisor,
          "shift_id" => $this->shift,
          "doj" => $this->doj,
          "designation_id" => $this->designation_id,
          "employee_status_id" => $this->employee_status_id,
          "employee_category_id" => (int)$this->employee_category_id,
        ]);
      }


      $this->optionalFieldTransaction('update');
      DB::commit();
      return ["status" => true, "message" => "Profile information updated successfully!!"];
    } catch (\Exception $e) {
      DB::rollBack();
      logError("Error while updating employee ", $e);
      return ["status" => false, "message" => "Failed to update profile information!!"];
    }
  }

  public function optionalFieldTransaction($type = 'read')
  {
    foreach ($this->optionalFieldList as $field) {
      $conditions = [
        'model_type' => get_class(new Employee()),
        'model_id' => $this->personalInfo->id,
        'field_key' => $field,
      ];

      $optionalField = OptionalField::where($conditions)->first();

      if ($optionalField) {
        if ($type == 'update')
          $optionalField->update([
            'field_value' => $this->$field,
          ]);
        else
          $this->$field = $optionalField->field_value;
      } else {
        OptionalField::create([
          "model_type" => get_class(new Employee()),
          "model_id" => $this->personalInfo->id,
          "field_key" => $field,
          "field_value" => $this->$field
        ]);
      }
    }
  }

  public function updateProfilePicture()
  {
    $this->validate([
      'profile_picture_new' => 'required|file|mimes:jpeg,png,jpg,gif,webp,bmp|max:2048',
    ], messages: ['profile_picture_new.max' => 'The profile picture must not be greater than 2MB.'], attributes: ['profile_picture_new' => 'profile picture']);

    logInfo("Parameters for uploading profile image " . $this->profile_picture_new);

    if ($this->personalInfo->profile_picture) {
      File::delete(public_path('storage/' . $this->personalInfo->getOriginal('profile_picture')));
    }

    try {
      $this->personalInfo->update(["profile_picture" => $this->profile_picture_new->storeAs("uploads/employee/document/" . $this->personalInfo->id, 'profile_picture-' . time() . '.' . $this->profile_picture_new->extension(), "public")]);
      $this->reset(['profile_picture_new']);
      return ["status" => true, "message" => "Profile picture updated successfully!!"];
    } catch (\Exception $e) {
      logError("Error while uploading profile picture " . $e);
      return ["status" => false, "message" => "Failed to update profile picture!!"];
    }
  }

  public function setSupervisor($supervisor_id)
  {
    $this->supervisor = $supervisor_id;
  }

  #[Computed(persist: true)]
  public function employeeCategory()
  {
    return \App\Models\EmployeeCategory::pluck('id', 'type')->toArray();
  }

  public function resetProfileValue()
  {
    $this->reset(['office_vehicle_identity']);
    $this->resetValidation(['office_vehicle_identity']);
    $this->reset(["first_name", "middle_name", "last_name", "unit", "department", "employee_code", "employee_status_id", "supervisor", "pan_no", "branch", "sub_branch", 'company', "employee_category_id", "phone", "email", "dob", "address", "gender", "doj", "shift", "profile_picture_new"]);
    $this->resetValidation(["first_name", "middle_name", "last_name", "unit", "department", "employee_code", "employee_status_id", "supervisor", "pan_no", "branch", "sub_branch", 'company', "employee_category_id", "phone", "email", "dob", "address", "gender", "doj", "shift", "profile_picture_new"]);
  }
}
