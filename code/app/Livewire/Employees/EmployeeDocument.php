<?php

namespace App\Livewire\Employees;

use App\Http\Helpers\Constant;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use App\Http\Repositories\EmployeeDocumentRepository;

class EmployeeDocument extends Component
{
    use WithDataTable, WithNotify, MultiselectEmployeeSearch;

    private EmployeeDocumentRepository $employeeDocumentRepo;
    private \App\Models\Employee\EmployeeDocument $model;
    
    public string $status = "", 
        $documentType = "";
    
    public int $employee_ids = 0;

    public function __construct()
    {
        $this->model = new \App\Models\Employee\EmployeeDocument;
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
        $this->employeeDocumentRepo = new EmployeeDocumentRepository;
    }
    
    public function mount() {
        $this->singleSelectAttributes = ['employee_ids'];
    }
    
    #[Computed(persist: true)]
    public function statusList() {
        return [
            "Submitted",
            "Approved",
            "Rejected",
        ];
    }

    #[Computed(persist: true)]
    public function documentTypeList() {
        return Constant::DOCUMENT_TYPES;
    }

    public function render()
    {
        return view('livewire.employees.employee-document');
    }

    #[Computed()]
    public function list()
    {
        return $this->applySorting(
            $this->model->select("employee_documents.id", "name", "type", "status", DB::raw('CONCAT(e.first_name, " ", e.middle_name, " ", e.last_name) as employee'),'actioned_by', 'employee_documents.created_at',
                                DB::raw('CONCAT(actioned_by.first_name, " ", actioned_by.middle_name, " ", actioned_by.last_name) as actioned_by_name')
                                )
                ->leftJoin("employees as e", "e.id", "employee_documents.employee_id")
                ->leftJoin("employees as actioned_by", "actioned_by.id", "employee_documents.actioned_by")
                ->search($this->search)
                ->when($this->status, function ($query) {
                    $query->where('status', $this->status);
                })
                ->when($this->documentType, function ($query) {
                    $query->where('type', $this->documentType);
                })->when($this->employee_ids, function ($query) {
                    $query->where('actioned_by', $this->employee_ids);
                })
        )->paginate($this->perPage);
    }
    
    public function changeDocumentStatus($id, $status) {
        $employeeId = auth()->user()?->employee?->id;
        if(isset($employeeId) && $employeeId != null) {
            $params = [
                "id"          => $id,
                "status"      => $status,
                "actioned_by" => $employeeId,
            ];
            $documentAction = $this->employeeDocumentRepo->documentAction($params);
            logInfo("Change Document Status. Params => ".print_r($params, true)." Response =>".print_r($documentAction, true));
            if($documentAction) {
                $this->notify("Document status changed successfully")->type('success')->send();
            } else {
                $this->notify("Error changing status of the document")->type('error')->send();
            }    
        } else {
            $this->notify("Please login from user")->type('error')->send();
        }
    }
}
