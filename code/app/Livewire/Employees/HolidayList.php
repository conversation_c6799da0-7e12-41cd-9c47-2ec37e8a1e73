<?php

namespace App\Livewire\Employees;

use App\Models\configs\Branch;
use App\Models\configs\Holiday;
use App\Models\configs\HolidayBranch;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;
use App\Models\configs\FiscalYear;
use App\Http\Repositories\HolidayRepository;

#[Title('Holiday List')]
class HolidayList extends Component
{
    use WithDataTable;
    public $model;
    public $branch_id;
    public $fiscal_year_id;

    public function mountWithDataTable()
    {
        $this->perPage = 25;
        $this->branch_id = session('employee')?->branch->id ?? "";
        $this->fiscal_year_id = session('current_fiscal_year') ??"";
    }

    #[Computed()]
    public function list()
    {
        $holidayRepo = new HolidayRepository;
        return $this->applySorting(
            $holidayRepo->list($this->all())
        )->paginate($this->perPage);
    }

    #[Computed()]
    public function branchList()
    {
        return Branch::where('company_id', session('company')->id)->whereNull('deleted_at')->pluck('name', 'id');
    }
 
    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::select('id', 'name', 'is_active')
            ->whereNull('deleted_at')
            ->orderBy('id', 'desc')
            ->get();
    }
    

    public function render()
    {

        return view('livewire.employees.holiday-list');
    }
}
