<?php

namespace App\Livewire\Employees;

use <PERSON>Bright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Livewire\Employees\Forms\EdfForm;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\EmployeeShift;
use App\Models\configs\EmpStatus;
use App\Models\configs\OutsourceCompany;
use App\Models\configs\SubBranch;
use App\Models\configs\Unit;
use App\Models\Payroll\Designation;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;

#[Title('Add Employee')]
class Edf extends Component
{
    use WithNotify, WithFileUploads;

    public Company $company;

    public EdfForm $edfForm;

    public $search = "";

    public function mount()
    {
        $this->company = session(Constant::SESSION_COMPANY);
        $this->edfForm->doj = Carbon::now()->format('Y-m-d');
    }

    public function save()
    {
        $result = $this->edfForm->save();
        if ($result["status"]) {
            $this->redirect('profile/' . $result["employee_id"], navigate: true);
            $this->notify("Employee added successfully")->send();
        } else
            $this->notify("Some error occurred!!")->type("error")->send();
    }

    public function updatedEdfFormMstat()
    {
        $this->edfForm->spouse = null;
    }

    public function updatedEdfFormDobEng($value)
    {
        $this->edfForm->dob_nepali = $this->convertToNepDate($value);
    }

    public function updatedEdfFormCompany()
    {
        $this->edfForm->resetOfficialInfoField();
        $this->edfForm->doj = Carbon::now()->format('Y-m-d');
    }

    public function updatedEdfFormDepartment()
    {
        $this->edfForm->reset(["unit"]);
    }

    private function convertToNepDate($engDate)
    {
        return LaravelNepaliDate::from($engDate)->toNepaliDate();
    }

    #[On("employee-dropdown-select")]
    public function setSupervisor($supervisor_id)
    {
        $this->edfForm->setSupervisor($supervisor_id);
    }

    #[Computed()]
    public function unitList()
    {
        return Unit::where('department_id', $this->edfForm->department)->whereNull('deleted_at')->get();
    }

    #[Computed()]
    public function subBranchList()
    {
        return SubBranch::where('branch_id', $this->edfForm->branch)->whereNull('deleted_at')->get();
    }

    #[Computed()]
    public function shiftList()
    {
        return EmployeeShift::where('company_id', $this->edfForm->company)->where('is_active', '1')->whereNull('deleted_at')->get();
    }

    #[Computed()]
    public function departmentList()
    {
        return Department::where('company_id', $this->edfForm->company)->whereNull('deleted_at')->get();
    }

    #[Computed(persist: true)]
    public function designations()
    {
        return Designation::pluck("title", "id");
    }

    #[Computed()]
    public function branchList()
    {
        return Branch::where('company_id', $this->edfForm->company)->whereNull('deleted_at')->get();
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::whereNull('deleted_at')->get();
    }

    #[Computed()]
    public function outsourceCompanyList()
    {
        return OutsourceCompany::where('company_id', $this->edfForm->company)->where('status', 1)->get();
    }

    #[Computed(persist: true)]
    public function employeeStatusList()
    {
        return EmpStatus::all();
    }

    #[Computed(persist: true)]
    public function employeeCategory()
    {
        return \App\Models\EmployeeCategory::isCountable()->pluck('type', 'id')->toArray();
    }
}
