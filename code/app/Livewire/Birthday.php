<?php

namespace App\Livewire;

use App\Models\Employee\Employee;
use App\Http\Services\CelebrationService;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Component;

class Birthday extends Component
{
    public $showPopUp = false;
    public $userName;

    public function mount()
    {
        if (!isSuperAdmin() && !session('isBdCelebrated', false)) {
            $employee = currentEmployee();
            $this->userName = $employee->first_name;

            $celebration = new CelebrationService();

            if ($celebration->isBirthdayOnly($employee)) {
                $this->showPopUp = true;
                session()->put('isBdCelebrated', true);
            }
        }
    }


    public function closePopup()
    {
        $this->showPopUp = false;
    }

    public function render()
    {
        return view('livewire.birthday');
    }
}
