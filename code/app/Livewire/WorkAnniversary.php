<?php

namespace App\Livewire;

use App\Http\Services\CelebrationService;
use Carbon\Carbon;
use Livewire\Component;

class WorkAnniversary extends Component
{
    public $showPopUp = false;
    public $userName;
    public $yearsCompleted;

    public function mount()
    {
        if (!isSuperAdmin() && !session('isWorkAnnCelebrated', false)) {
            $employee = currentEmployee();
            $this->userName = $employee->first_name;

            $celebration = new CelebrationService();

            if ($celebration->isWorkAnniversaryOnly($employee)) {
                $doj = Carbon::parse($employee->organizationInfo->doj);
                $years = $doj->diffInYears(Carbon::today());
                if ($years >= 1) {  
                    $this->yearsCompleted = $years;

                    $this->showPopUp = true;
                    session()->put('isWorkAnnCelebrated', true);
                }
            }
        }
    }
    public function closePopup()
    {
        $this->showPopUp = false;
    }
    public function render()
    {
        return view('livewire.work-anniversary');
    }
}
