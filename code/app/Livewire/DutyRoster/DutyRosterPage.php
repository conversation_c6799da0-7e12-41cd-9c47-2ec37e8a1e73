<?php

namespace App\Livewire\DutyRoster;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\DutyRosterExport;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\Company;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;

#[Title('Duty Roster')]
class DutyRosterPage extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $filterWeek = 'currentWeek';
    public $employees = [];
    public $departmentId = "";
    public $currentWeekDates, $nextWeekDates; // ['2020-02-02' => 'Sunday', '2021-01-01' => 'Saturday'.....]
    public $dutyRosterArr = [];

    // filters
    public $employee_ids = [];
    public $branch_id = "";
    public $department_id = "";
    public $companyId = "";

    public $showFilters = true;

    public function mount()
    {
        if (!scopeAll() && (scopeCompany() || scopeBranch())) {
            $this->companyId = currentEmployee()?->company_id;
        }
        $this->mountWithDataTable(); // for list item to run properly; mount inside trait is run at default after mount of component

        $this->setPermissionForEmployeeDropdown();
        // if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN)) {
        //     if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_ALL)) {
        //         $this->showFilters = true;
        //         $this->branch_id = "";
        //         $this->department_id = "";
        //         //
        //     } else if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_BRANCH)) {
        //         $this->showFilters = false;
        //         $this->branch_id = auth()->user()->employee?->organizationInfo?->branch_id;
        //         $this->employeeSearchFilter['branch_ids'] = [$this->branch_id];
        //     } else if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_DEPARTMENT)) {
        //         $this->showFilters = false;
        //         $this->department_id = auth()->user()->employee?->organizationInfo?->department_id;
        //         $this->employeeSearchFilter['department_ids'] = [$this->department_id];
        //     } else {
        //         abort('401');
        //     }
        //     $this->singleSelectAttributes = ['employeeId'];
        // }

        $this->multiSelectAttributes = ['employee_ids'];
        $startOfCurrentWeek = Carbon::now()->startOfWeek(Carbon::SUNDAY);
        $startOfNextWeek = $startOfCurrentWeek->copy()->addWeek();
        $this->currentWeekDates = $this->getWeekDates($startOfCurrentWeek);
        $this->nextWeekDates = $this->getWeekDates($startOfNextWeek);
        $this->listItem();

        $this->sortBy = "first_name";
        $this->sortDirection = 'asc';
    }

    function getWeekDates(Carbon $startOfWeek)
    {
        $dates = [];
        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $dates[$date->format('Y-m-d')] = $date->format('l');
        }
        return $dates;
    }

    public function updated($attr)
    {
        if (\in_array($attr, ['employee_ids', 'branch_id', 'department_id', 'filterWeek','companyId'])) {
            unset($this->list);
            $this->extraRefresh();
            $this->resetPage();
        }
        if (\in_array($attr, ['companyId'])) {
            unset($this->branchList, $this->departmentList);
            $this->branch_id = "";
            $this->department_id = "";
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->when($this->branch_id, fn ($query) => $query->where('org.branch_id', $this->branch_id))
            ->when($this->department_id, fn ($query) => $query->where('department_id', $this->department_id))
            ->when(count($this->employee_ids), fn ($query) => $query->whereIn('employees.id', $this->employee_ids))
            ->when($this->companyId, function ($query) {
                $query->where("employees.company_id", $this->companyId);
            })
            ->select("employees.id", "employees.first_name", "employees.last_name", "employees.middle_name");

        $query = filterEmployeesByScope($query, 'org');

        return $query
            ->when($this->sortBy && $this->sortDirection, fn ($query) => $query->orderBy($this->sortBy, $this->sortDirection))
            ->paginate($this->perPage ?? 10);
    }

    public function extraRefresh()
    {
        $this->listItem();
    }

    public function export()
    {
        $dates = $this->filterWeek == 'currentWeek' ? $this->currentWeekDates : $this->nextWeekDates;
        $startDate = \array_keys($dates)[0];
        $endDate = last(array_keys($dates));

        $title = "Duty Roster ($startDate - $endDate)";
        if ($this->branch_id) {
            $title .= " - " . Branch::find($this->branch_id)->name . " branch";
        } else if ($this->department_id) {
            $title .= " - " . Department::find($this->department_id)->name . " department";
        }

        return Excel::download(
            new DutyRosterExport($dates, $this->list->items(), $this->dutyRosterArr, $title),
            "duty-roster ($startDate - $endDate).xlsx"
        );
    }

    public function listItem()
    {
        $dates = $this->filterWeek === 'currentWeek' ? $this->currentWeekDates : $this->nextWeekDates;
        $startDate = array_key_first($dates);
        $endDate = array_key_last($dates);

        $employeeIds = $this->list->pluck('id')->toArray();

        $dutyRosters = DB::table('duty_rosters as dr')
            ->leftJoin('shifts as sh', 'dr.shift_id', '=', 'sh.id')
            ->whereBetween('dr.date_en', [$startDate, $endDate])
            ->whereIn('dr.employee_id', $employeeIds)
            ->select('dr.id', 'dr.employee_id', 'dr.date_en', 'sh.start_time', 'sh.end_time', 'dr.day_off', 'sh.day_off as shift_day_off')
            ->get()
            ->groupBy('employee_id');

        $defaultShifts = DB::table('employee_org as org')
            ->leftJoin('shifts as sh', 'org.shift_id', '=', 'sh.id')
            ->whereIn('org.employee_id', $employeeIds)
            ->select('sh.start_time', 'sh.end_time', 'sh.day_off', 'org.employee_id')
            ->get()
            ->keyBy('employee_id');

        $this->dutyRosterArr = $this->prepareDutyRoster($dates, $dutyRosters, $defaultShifts);
    }

    public function convertToNepaliDate($date)
    {
        return LaravelNepaliDate::from($date)->toNepaliDate('Y-m-d');
    }

    private function prepareDutyRoster($dates, $dutyRosters, $defaultShifts)
    {
        $dutyRosterArr = [];

        foreach ($this->list as $employee) {
            $employeeId = $employee->id;
            $defaultShift = $defaultShifts->get($employeeId);

            foreach ($dates as $date => $day) {
                $dutyRoster = $dutyRosters->get($employeeId)?->firstWhere('date_en', $date);
                $dayOff = $dutyRoster?->day_off ?? $defaultShift?->day_off == Carbon::parse($date)->dayOfWeek;
                $shift = $dutyRoster?->start_time ?? $defaultShift?->start_time;
                $endShift = $dutyRoster?->end_time ?? $defaultShift?->end_time;

                $dutyRosterArr[$employeeId][] = [
                    'date' => $date,
                    'day' => $day,
                    'shift' => Carbon::parse($shift)->format('H:i'),
                    'endShift' => Carbon::parse($endShift)->format('H:i'),
                    'dayOff' => $dayOff,
                ];
            }
        }

        return $dutyRosterArr;
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }
    #[Computed(persist: true)]
    public function branchList()
    {
        return Branch::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return Department::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.duty-roster.duty-roster-page');
    }
}
