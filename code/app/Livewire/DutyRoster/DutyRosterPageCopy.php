<?php

namespace App\Livewire\DutyRoster;

use App\Livewire\Admin\Company\Company;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\DutyRoster;
use App\Models\configs\EmployeeShift;
use App\Models\Employee\Employee;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Duty Roster')]
class DutyRosterPageCopy extends Component
{
    use WithNotify, WithRowSelect;

    public $editingId = null, $isEditing = false, $shift_id, $employees, $start_date, $end_date, $day_off, $name, $employeeId;

    private $model;

    public $tableFilters = [
        "department" => "",
        "branch" => ""
    ];

    public function __construct()
    {
        $this->model = new \App\Models\configs\DutyRoster;
    }

    public function mount()
    {
        $this->perPage = 10;
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function rules()
    {
        return [
            "shift_id" =>  ["required"],
            "employees" =>  [$this->isEditing ? "nullable" : "required"],
            "start_date" =>  ["required", "date", $this->isEditing ? "" : "before_or_equal:end_date", function ($attribute, $value, $fail) {
                if (is_array($this->employees)) {
                    $existingRecords = DutyRoster::with('employee:id,first_name,middle_name,last_name')
                        ->where('end_date', '>=', $value)
                        ->whereIn('employee_id', $this->employees)
                        ->first();
                    $failMessages = [];

                    if ($existingRecords) {
                        foreach ($existingRecords as $existingRecord) {
                            $fullName = $existingRecord->employee->first_name . ' ' . $existingRecord->employee->middle_name . ' ' . $existingRecord->employee->last_name;
                            $failMessages[] = "The start date must be after the existing end date for $fullName.";
                        }
                    }

                    if (!empty($failMessages)) {
                        $fail(implode(' ', $failMessages));
                    }
                }
            }],
            "end_date" =>  [
                "required", "date", "after_or_equal:start_date", 'after_or_equal:today', function ($attribute, $value, $fail) {
                    if ($this->isEditing) {
                        $existingRecord = DutyRoster::where([['employee_id', $this->employeeId], ['start_date', '<=', $value], ['end_date', '>=', $value]])
                            ->whereNot('id', $this->editingId)
                            ->count();

                        if ($existingRecord) {
                            $fail("The end date must be before the existing end date for $this->name.");
                        }
                    }
                }
            ],
            "day_off" => ["required"]
        ];
    }

    public function save()
    {
        $this->validate();

        DB::beginTransaction();
        try {
            if ($this->isEditing) {
                $dutyRoster = $this->model->findOrFail($this->editingId);
                $dutyRoster->shift_id = $this->shift_id;
                $dutyRoster->day_off = $this->day_off;
                $dutyRoster->start_date = $this->start_date;
                $dutyRoster->end_date = $this->end_date;
                $dutyRoster->save();
                $this->notify('Duty roster edited successfully!!')->send();
            } else {
                $data = [
                    "shift_id" => $this->shift_id,
                    "start_date" => $this->start_date,
                    "end_date" => $this->end_date,
                    "day_off" => $this->day_off,
                ];
                foreach ($this->employees as $employee) {
                    $dutyRoster = $this->model->create([...$data, "employee_id" => $employee]);
                }
                $this->notify('Duty roster added successfully!!')->send();
            }
            DB::commit();
            unset($this->dutyRosterList);
        } catch (Exception $e) {
            DB::rollback();
            Log::error("Error while " . ($this->isEditing ? 'editing' : 'adding') . " duty roster: ", ['error_message' => $e->getMessage()]);
            $this->notify("Failed to " . ($this->isEditing ? 'edit' : 'add') . " duty roster!!")->type("error")->send();
        }

        $this->dispatch('hide-model');
    }

    public function delete(int $id)
    {
        DB::beginTransaction();
        try {
            $this->model->findOrFail($id)->delete();
            $this->notify('Duty roster deleted successfully!!')->send();
            unset($this->dutyRosterList);
        } catch (Exception $e) {
            DB::rollback();
            Log::error("Error while deleting roster: ", $e->getMessage());
            $this->notify('Failed to delete duty roster!!')->type("error")->send();
        }
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['shift_id', 'employees', 'start_date', 'end_date', 'day_off', 'isEditing', 'name', 'employeeId']);
        $this->resetErrorBag();
        $this->dispatch("toggle-employees", []);
    }

    public function fillFormModel($id)
    {
        $row = $this->model->with(["employee:id,first_name,middle_name,last_name"])->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->name = $row->employee?->name;
        $this->employeeId = $row->employee_id;
    }

    public function updatedShiftId()
    {
        unset($this->employeeList);
        $items = [];
        $index = 0;
        foreach ($this->employeeList as $employee) {
            $items[$index] =  [
                'value' => $employee['id'],
                'label' => $employee->organizationInfo?->company_employee_code . " - " . $employee->name,
            ];
            $index += 1;
        }

        if (!$this->isEditing) $this->reset(['employees']);

        $this->dispatch("updateMultiselectItem", ["id" => "duty-roster-assign", "items" => $items]);
        $this->dispatch("toggle-employees", []);
    }

    #[On("employees-changed")]
    public function employeesChanged($values)
    {
        $this->employees = $values;
    }

    #[Computed()]
    public function dutyRosterList()
    {
        $today = Carbon::now()->toDateString();

        return $this->applySorting(
            $this->model::with([
                'employee:id,first_name,middle_name,last_name',
                'employee.branch:branches.id,branches.name',
                'employee.department:departments.id,departments.name'
            ])
                ->leftJoin('employees', 'employees.id', '=', 'duty_rosters.employee_id')
                ->leftJoin('employee_org as org', 'employees.id', '=', 'org.employee_id')
                ->when($departmentId = $this->tableFilters['department'], function ($query) use ($departmentId) {
                    $query->where('org.department_id', $departmentId);
                })
                ->when($branchId = $this->tableFilters['branch'], function ($query) use ($branchId) {
                    $query->where('org.branch_id', $branchId);
                })
                ->where('end_date', '>=', $today)
                ->where(function ($query) {
                    $query
                        ->Where(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                        ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                        ->orWhere('org.employee_code', 'LIKE', '%' . $this->search . '%');
                })
                ->select("duty_rosters.id", "duty_rosters.shift_id", "duty_rosters.employee_id", "day_off", "start_date", "end_date")
                ->orderBy('duty_rosters.created_at', 'desc')
        )->paginate($this->perPage, pageName: "duty-roster-list");
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->select("id", "name")->get();
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return Department::orderBy('name', 'asc')->pluck("name", "id");
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return Branch::orderBy('name', 'asc')->pluck("name", "id");
    }

    #[Computed()]
    public function shiftList()
    {
        return EmployeeShift::where('is_active', '1')->orderBy('name', 'asc')->get();
    }

    #[Computed(persist: true)]
    public function employeeList()
    {
        $today = Carbon::now()->toDateString();

        if (!$this->shift_id) return [];

        $company_id = EmployeeShift::where('id', $this->shift_id)->where('is_active', '1')->select('company_id')->value('company_id');

        return Employee::with('organizationInfo:employee_org.id,employee_org.employee_code,employee_org.employee_id')
            ->where('company_id', $company_id)
            ->select("employees.id", "first_name", "middle_name", "last_name")
            ->get();
    }

    public function render()
    {
        return view('livewire.duty-roster.duty-roster-page');
    }
}
