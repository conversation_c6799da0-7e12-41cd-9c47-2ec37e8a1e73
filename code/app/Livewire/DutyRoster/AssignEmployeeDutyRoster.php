<?php

namespace App\Livewire\DutyRoster;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Repositories\DutyRosterRepository;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\EmployeeShift;
use App\Models\configs\Unit;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use PermissionList;

class AssignEmployeeDutyRoster extends Component
{
    use WithRowSelect, WithNotify, MultiselectEmployeeSearch;

    public array $employee_ids = [];
    public array $currentWeekDates = [];
    public array $nextWeekDates = [];

    public $department_id = "";
    public $branch_id = "";
    public $unit_id = "";
    public $showUnit = false;
    public $showFilters = false;
    public $filterEmployeeIds = [];

    public array $shiftIds = [];

    protected DutyRosterRepository $dutyRosterRepo;

    public function __construct()
    {
        $this->dutyRosterRepo = new DutyRosterRepository;
    }

    public function mount()
    {
        $startOfCurrentWeek = Carbon::now()->startOfWeek(Carbon::SUNDAY);
        $startOfNextWeek = $startOfCurrentWeek->copy()->addWeek();
        // $this->currentWeekDates = $this->getWeekDates($startOfCurrentWeek);
        $this->nextWeekDates = $this->getWeekDates($startOfNextWeek);
        $this->setPermissionForEmployeeDropdown();
        $this->multiSelectAttributes = ['filterEmployeeIds'];

        // if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN)) {
        //     if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_ALL)) {
        //         $this->showFilters = true;
        //     } else if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_BRANCH)) {
        //         $this->branch_id = \currentEmployee()?->organizationInfo?->branch_id;
        //     } else if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN_DEPARTMENT)) {
        //         $this->department_id = \currentEmployee()?->organizationInfo?->department_id;
        //         $this->showUnit = true;
        //     }
        // }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query =  Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin('branches as brch', 'brch.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            // ->leftJoin('units', 'units.id', 'org.unit_id')
            ->leftJoin('companies as comp', 'comp.id', 'employees.company_id')
            ->when($this->branch_id, fn ($query) => $query->where('org.branch_id', $this->branch_id))
            ->when($this->department_id, fn ($query) => $query->where('org.department_id', $this->department_id))
            ->when($this->unit_id, fn ($query) => $query->where('org.unit_id', $this->unit_id))
            ->when(count($this->filterEmployeeIds), fn ($query) => $query->whereIn('employees.id', $this->filterEmployeeIds))
            ->when(
                $this->search,
                fn ($query) =>
                $query->where(DB::raw("CONCAT_WS(' ', first_name, middle_name, last_name)"), 'LIKE', "%{$this->search}%")
                    ->orWhere(DB::raw("CONCAT(first_name, ' ', last_name)"), 'LIKE', "%{$this->search}%")
                    ->orWhere(DB::raw("CONCAT(comp.code,'-',org.employee_code)"), 'LIKE', "%{$this->search}%")
            )
            ->select(
                [
                    'brch.name as branch',
                    'dept.name as department',
                    // 'units.name as unit',
                    "employees.id",
                    DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                    DB::raw("CONCAT(comp.code,'-',org.employee_code) as code")
                ]
            );

        $query = filterEmployeesByScope($query, 'org');

        return $query
            ->when($this->sortBy && $this->sortDirection, fn ($query) => $query->orderBy($this->sortBy, $this->sortDirection))
            ->paginate($this->perPage);
    }

    function getWeekDates(Carbon $startOfWeek)
    {
        $dates = [];

        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $dateKey = $date->format('Y-m-d');
            $dates[$dateKey] = $date->format('l');

            // Initialize with default values matching EmployeeDutyRosterPage structure
            $this->shiftIds[$dateKey] = [
                'shift_id' => null,
                'is_day_off' => false,
                'has_shift' => true
            ];
        }

        return $dates;
    }

    #[Computed(persist: true)]
    public function shiftList()
    {
        $shiftList = [];
        foreach (EmployeeShift::where('is_active', 1)->get() as $shift) {
            $shiftList["$shift->name ({$shift->start_time} - {$shift->end_time})"] = $shift->id;
        }
        return $shiftList;
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return Branch::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return Department::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function unitList()
    {
        return Unit::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function updated($attr)
    {
        if (\in_array($attr, ['unit_id', 'department_id', 'branch_id', 'filterEmployeeIds'])) {
            $this->resetPage();
            unset($this->list);
        }
    }

    public function convertToNepaliDate($date)
    {
        return LaravelNepaliDate::from($date)->toNepaliDate('Y-m-d');
    }

    public function assign()
    {
        DB::beginTransaction();
        try {
            if (!count($this->selectedRows)) return $this->notify("You must select employees to assign")->type('error')->send();

            // Count day offs using the new structure
            $dayOffs = array_filter($this->shiftIds, fn($value) => isset($value['is_day_off']) && $value['is_day_off']);
            $maxDayOff = (int)config('app.duty_roster_max_day_off', 1);
            if (count($dayOffs) > $maxDayOff)
                return $this->notify('Day offs cannot be more than "' . $maxDayOff . '" day (s)')->type("error")->send();

            foreach ($this->selectedRows as $employee_id) {
                foreach ($this->shiftIds as $date => $value) {
                    $isLessThanToday = Carbon::parse($date)->lessThanOrEqualTo(Carbon::today());
                    if ($isLessThanToday) continue;

                    // Extract values
                    $dayOff = $value['is_day_off'] ?? false;

                    // Handle the three possible scenarios as in EmployeeDutyRosterPage:
                    // 1. Regular working day: is_day_off = false, shift_id = some ID
                    // 2. Day off with shift: is_day_off = true, has_shift = true, shift_id = some ID
                    // 3. Full day off: is_day_off = true, has_shift = false, shift_id = null

                    $hasShift = (!$dayOff) || ($value['has_shift'] ?? false);
                    $shiftId = $hasShift ? ($value['shift_id'] ?? null) : null;

                    // Validate shift ID if we're supposed to have one
                    if ($hasShift && $shiftId) {
                        $shift = EmployeeShift::find($shiftId);
                        if (!$shift) {
                            return $this->notify("Shift not found")->type('error')->send();
                        }
                    }

                    // For regular working days, ensure there's a shift assigned
                    if (!$dayOff && !$shiftId) {
                        return $this->notify("Regular working days must have a shift assigned")->type('error')->send();
                    }

                    $this->dutyRosterRepo->saveDutyRoster($employee_id, $date, $shiftId, $dayOff);
                }
            }

            DB::commit();
            $this->dispatch('duty-roster-saved');
            $this->notify("Duty Roster Updated")->send();
            return \redirect(route('dutyRoster'));
        } catch (\Exception $e) {
            \logError("Error saving duty roster", $e);
            DB::rollBack();
            $this->notify("Error Occured")->type("error")->send();
        }
    }

    public function render()
    {
        return view('livewire.duty-roster.assign-employee-duty-roster');
    }
}
