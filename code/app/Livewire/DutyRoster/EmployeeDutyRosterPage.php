<?php

namespace App\Livewire\DutyRoster;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Repositories\DutyRosterRepository;
use App\Livewire\Config\Shift\Shift;
use App\Models\configs\DutyRoster;
use App\Models\configs\EmployeeShift;
use App\Models\configs\Holiday;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use PermissionList;

class EmployeeDutyRosterPage extends Component
{
    use WithNotify, MultiselectEmployeeSearch;

    public array $currentWeekDates = [];
    public array $nextWeekDates = [];

    public $title = "Duty Roster";
    public $singleDutyRoster = false;

    public $employeeId;

    public $shiftIds = [];
    public $showDutyRoster = false;
    public $showOnly = true;

    protected DutyRosterRepository $dutyRosterRepo;

    public function __construct()
    {
        $this->dutyRosterRepo = new DutyRosterRepository;
    }

    public function mount($employee_id = null)
    {
        $employee = null;
        // for props
        if ($this->employeeId) {
            $this->title = "My Duty Roster";
            $this->showDutyRoster = true;
            $this->singleDutyRoster = true;
        }

        if ($employee_id) {
            $employee = \App\Models\Employee\Employee::findOrFail($employee_id);
            $orgInfo = $employee->organizationInfo;
            $currOrgInfo = currentEmployee()?->organizationInfo;
            if (!scopeAll()) {
                $abortMessage = "";
                if (scopeCompany()) {
                    if ($employee->company_id != currentEmployee()?->company_id) {
                        $abortMessage = "Can't access people outside of the company";
                    }
                }else if (scopeBranch()) {
                    if ($orgInfo->branch_id != $currOrgInfo->branch_id) {
                        $abortMessage = "Can't access people outside of the branch";
                    }
                }else if (scopeDepartment()) {
                    if ($orgInfo->department_id != $currOrgInfo->department_id) {
                        $abortMessage =  "Can't access people outside of the branch";
                    }
                }

                if ($abortMessage) abort(403, $abortMessage);
            }
            $this->employeeId = $employee_id;
            $this->title = "Duty Roster of {$employee->name} ({$employee->employeeCode})";
            $this->singleDutyRoster = true;
            $this->showDutyRoster = true;
        }


        // authorize if the user can edit or assign the duty roster or not
        $this->setPermissionForEmployeeDropdown();
        if (auth()->user()->can(PermissionList::DUTY_ROSTER_ASSIGN)) {
            if (scopeAll()) {
                $this->showOnly = false;
            } else if (scopeCompany()) {
                if (\currentEmployee()->company_id == $employee?->company_id) {
                    $this->showOnly = false;
                }
            } else if (scopeBranch()) {
                if (\currentEmployee()->organizationInfo?->branch_id == $employee?->organizationInfo?->branch_id) {
                    $this->showOnly = false;
                }
            } else if (scopeDepartment()) {
                if (\currentEmployee()->organizationInfo?->department_id == $employee?->organizationInfo?->department_id) {
                    $this->showOnly = false;
                }
            }
            $this->singleSelectAttributes = ['employeeId'];
        }
    }

    function getWeekDates(Carbon $startOfWeek)
    {
        $dates = [];

        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $dates[$date->format('Y-m-d')] = $date->format('l');
        }

        return $dates;
    }

    #[Computed(persist: true)]
    public function shiftList()
    {
        $shiftList = [];
        foreach (EmployeeShift::where('is_active', 1)->get() as $shift) {
            $shiftList["$shift->name ({$shift->start_time} - {$shift->end_time})"] = $shift->id;
        }
        return $shiftList;
    }

    #[Computed(persist: true)]
    public function currentWeekDutyRoster()
    {
        return $this->getRooster($this->currentWeekDates);
    }

    #[Computed(persist: true)]
    public function nextWeekDutyRoster()
    {
        return $this->getRooster($this->nextWeekDates);
    }

    public function getRooster($dates)
    {
        if (!$this->employeeId) {
            return [];
        }
        $startDate = array_keys($dates)[0];
        $endDate = last(array_keys($dates));
        $dutyRosters = $this->dutyRosterRepo->getDutyByEmployeeAndDateRange($this->employeeId, $startDate, $endDate);

        $formattedRosters = [];

        $defaultShift = EmployeeOrg::where('employee_id', $this->employeeId)->first()?->shift;

        $employee = Employee::find($this->employeeId);
        $employeeGender = $employee?->gender;

        foreach ($dates as $date => $day) {
            $dutyRoster = $dutyRosters->filter(function ($dutyRoster) use ($date) {
                return $dutyRoster->date_en == $date;
            })->first();

            // Default settings
            $shiftId = $defaultShift?->id;
            $isDayOff = false;

            // Check if there's a day off in the default shift for this day of week
            if (($defaultShift?->day_off % 7) == Carbon::parse($date)->dayOfWeek) {
                $isDayOff = true;
                // For default day offs, don't automatically assign a shift
                if (!$dutyRoster) {
                    $shiftId = null;
                }
            }

            // Override with any custom duty roster settings
            if ($dutyRoster) {
                $shiftId = $dutyRoster->shift_id; // Can be null for full day off
                $isDayOff = $dutyRoster->day_off ?? $isDayOff;
            }

            // Initialize the shift data with the new structure
            $this->shiftIds[$date] = [
                'shift_id' => $shiftId,
                'is_day_off' => $isDayOff,
                'has_shift' => $shiftId !== null
            ];

            $holidays = Employee::find($this->employeeId)?->branch->holidays;
            $holiday = $holidays->filter(function ($holiday) use ($date, $employeeGender) {
                return $holiday->eng_date == $date && (strtolower($holiday->gender) == $employeeGender || $holiday->gender == 'All');
            })->first();

            // Get shift details for display - only if we have a shift
            $shift = null;
            if ($shiftId) {
                $shift = EmployeeShift::find($shiftId);
            }

            // For display purposes
            $displayStartTime = $shift?->start_time ?? $defaultShift?->start_time ?? 'No Shift';
            $displayEndTime = $shift?->end_time ?? $defaultShift?->end_time ?? '';
            $displayLeisureStart = $shift?->leisure_start_time ?? $defaultShift?->leisure_start_time ?? '';
            $displayLeisureEnd = $shift?->leisure_end_time ?? $defaultShift?->leisure_end_time ?? '';

            // If full day off (no shift), don't show times
            $showTimes = !($isDayOff && $shiftId === null);

            array_push($formattedRosters, [
                'date' => $date,
                'nep_date' => LaravelNepaliDate::from($date)->toNepaliDate('Y-m-d'),
                'day' => $day,
                'start_time' => $showTimes ? $displayStartTime : 'No Shift',
                'end_time' => $showTimes ? $displayEndTime : '',
                'day_off' => $isDayOff,
                'has_shift' => $shiftId !== null,
                'leisure_start' => $showTimes ? $displayLeisureStart : '',
                'leisure_end' => $showTimes ? $displayLeisureEnd : '',
                'holiday' => $holiday?->name,
                'editable' => Carbon::parse($date)->greaterThan(Carbon::today()),
            ]);
        }
        return $formattedRosters;
    }

    public function show()
    {
        if ($this->showOnly) return;
        $this->validate(['employeeId' => 'required|exists:employees,id'], attributes: ['employeeId' => 'employee']);
        $this->showDutyRoster = true;
        unset($this->currentWeekDutyRoster, $this->nextWeekDutyRoster);
    }

    public function save($shiftIds = [])
    {
        if ($this->showOnly) return;
        DB::beginTransaction();
        try {
            $dayOffs = array_filter($shiftIds, fn($value) => isset($value['is_day_off']) && $value['is_day_off']);
            $maxDayOff = (int)config('app.duty_roster_max_day_off', 1);
            if (count($dayOffs) > $maxDayOff)
                return $this->notify('Day offs cannot be more than "' . $maxDayOff . '" day (s)')->type("error")->send();

            foreach ($shiftIds as $date => $value) {
                $isLessThanToday = Carbon::parse($date)->lessThanOrEqualTo(Carbon::today());
                if ($isLessThanToday) continue;

                // Extract values
                $dayOff = $value['is_day_off'] ?? false;

                // Handle the three possible scenarios:
                // 1. Regular working day: is_day_off = false, shift_id = some ID
                // 2. Day off with shift: is_day_off = true, has_shift = true, shift_id = some ID
                // 3. Full day off: is_day_off = true, has_shift = false, shift_id = null

                $hasShift = (!$dayOff) || ($value['has_shift'] ?? false);
                $shiftId = $hasShift ? ($value['shift_id'] ?? null) : null;

                // Validate shift ID if we're supposed to have one
                if ($hasShift && $shiftId) {
                    $shift = EmployeeShift::find($shiftId);
                    if (!$shift) {
                        return $this->notify("Shift not found")->type('error')->send();
                    }
                }

                // For regular working days, ensure there's a shift assigned
                if (!$dayOff && !$shiftId) {
                    return $this->notify("Regular working days must have a shift assigned")->type('error')->send();
                }

                $this->dutyRosterRepo->saveDutyRoster($this->employeeId, $date, $shiftId, $dayOff);
            }

            DB::commit();
            $this->dispatch('duty-roster-saved');
            unset($this->nextWeekDutyRoster);
            unset($this->currentWeekDutyRoster);
            $this->notify("Duty Roster Updated")->send();
        } catch (\Exception $e) {
            \logError("Error saving duty roster", $e);
            DB::rollBack();
            $this->notify("Error Occured")->type("error")->send();
        }
    }

    public function render()
    {
        $startOfCurrentWeek = Carbon::now()->startOfWeek(Carbon::SUNDAY);
        $startOfNextWeek = $startOfCurrentWeek->copy()->addWeek();
        $this->currentWeekDates = $this->getWeekDates($startOfCurrentWeek);
        $this->nextWeekDates = $this->getWeekDates($startOfNextWeek);
        return view('livewire.duty-roster.employee-duty-roster-page');
    }
}
