<?php

namespace App\Livewire\Manual;

use App\Http\Helpers\Constant;
use App\Models\Admin\MenuItem;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Computed;
use Livewire\Component;
use App\Traits\WithDataTable;
use Livewire\Attributes\Title;
use App\Models\UserManual;

#[Title('User Manual')]


class UserManualViewer extends Component
{
    use WithDataTable;

    public function mount()
    {

    }
    #[Computed(true)]
    public function list()
    {
        $query = UserManual::query()->with('roles', 'userManualRoles');

        if ($this->search) {
            $query->where(function ($query) {
                $query->where('title', 'like', "%{$this->search}%")
                    ->orWhere('category', 'like', "%{$this->search}%");
            });
        }

        // Role-based access        
        if (!auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN)) {
            $userRoleIds = auth()->user()->roles->pluck('id');

            $query->whereHas('roles', function ($q) use ($userRoleIds) {
                $q->whereIn('roles.id', $userRoleIds);
            });
        }

        if ($this->sortBy) {
            $query->orderBy($this->sortBy, $this->sortDirection);
        }
        $docs = $query->paginate($this->perPage, pageName: $this->pageName);

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

        $docs->getCollection()->transform(function ($doc) use ($imageExtensions) {
            $filePath = $doc->document;
            $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            $exists = Storage::disk('public')->exists($filePath);

            $doc->is_image = $exists && in_array($extension, $imageExtensions);
            $doc->preview_url = $doc->is_image ? asset('storage/' . $filePath) : null;
            $doc->file_extension = $extension;

            $doc->icon_class = match ($extension) {
                'pdf' => 'bi-file-earmark-pdf text-danger',
                'doc', 'docx' => 'bi-file-earmark-word text-primary',
                'xls', 'xlsx' => 'bi-file-earmark-excel text-success',
                default => 'bi-file-earmark text-secondary',
            };

            return $doc;
        });

        return $docs;
    }

    public function checkRoles(UserManual $doc): bool
    {
        return auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN) ||
            auth()->user()->hasAnyRole($doc->roles->pluck('name')->toArray());
    }

    public function render()
    {
        return view('livewire.manual.user-manual-viewer');
    }
}
