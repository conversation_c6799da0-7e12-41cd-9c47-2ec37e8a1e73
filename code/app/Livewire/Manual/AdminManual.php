<?php

namespace App\Livewire\Manual;

use App\Http\Helpers\Constant;
use App\Http\Repositories\MenuRepository;
use App\Models\Admin\MenuItem;
use App\Models\Admin\Role;
use App\Models\UserManualRoles;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Component;
use App\Traits\WithDataTable;
use Livewire\Attributes\Title;
use App\Models\UserManual;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\File;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Illuminate\Support\Facades\Storage;

#[Title('Admin Manual')]


class AdminManual extends Component
{
    use WithDataTable, WithFileUploads, WithNotify;

    public $editingId = null;

    public $isEditing = false;

    public $title, $description, $category, $document, $roles = [], $categories = [];
    public $removeDocument = false;
    public $selectedCategory = '';
    public $selectedRole = '';


    public function rules()
    {
        return [
            'title' => 'required|string|max:30',
            'description' => 'required|string|max:300',
            'category' => 'required|exists:menu_items,id',
            'roles' => 'required|array|min:1',
            'document' => ($this->isEditing && !$this->removeDocument && is_string($this->document))
                ? 'nullable'
                : 'required|file|mimes:pdf,doc,docx,jpg,png,jpeg|max:20480', // 20MB in KB
        ];
    }

    public function resetForm()
    {
        $this->reset([
            'title',
            'description',
            'category',
            'document',
            'roles',
            'editingId',
            'isEditing'
        ]);
        $this->dispatch('clear-document-uploads');

        $this->dispatch('toggle-choices', []);
    }
    public function mount()
    {
        $this->categoryList = $this->categoryList();
        $this->sortBy = null;
        $this->sortDirection = 'asc';
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = UserManual::with([
            'menuItemLabel:id,label', 'roles',
        ]);

        if ($this->search) {
            $query->where(function ($query) {
                $query->where('title', 'like', "%{$this->search}%");
            });
        }

        if ($this->selectedCategory) {
            $query->where('menu_item_id', $this->selectedCategory);
        }

        if ($this->selectedRole) {
            $query->whereHas('roles', function ($q) {
                $q->where('roles.id', $this->selectedRole);
            });
        }

        if ($this->sortBy) {
            $query->orderBy($this->sortBy, $this->sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query->paginate($this->perPage, pageName: $this->pageName);
    }

    #[Computed(persist: true)]
    public function roleList()
    {
        return Role::select("id", "name")->where("name", "!=", Constant::ROLE_SUPER_ADMIN)->get();
    }

    #[Computed(persist: true)]
    public function categoryList()
    {
        return MenuItem::whereNull('parent_id')->select('id', 'label')->get();
    }

    public function addDocument()
    {
        $filePath = null;
        if ($this->document) {
            $filePath = $this->document->store('uploads/user-manual','public');
        }

        $userManual = UserManual::create([
            'title' => $this->title,
            'description' => $this->description,
            'menu_item_id' => $this->category,
            'document' => $filePath,
        ]);
        $userManual->roles()->attach($this->roles);
        $this->notify("Manual added successfully.")->type('success')->send();
    }

    public function editAdminManual($id)
    {
        $manual = UserManual::with('roles')->findOrFail($id);
        $this->editingId = $manual->id;
        $this->title = $manual->title;
        $this->category = $manual->menu_item_id;
        $this->description = $manual->description;
        $this->roles = $manual->roles->pluck('id')->toArray();
        $this->document = $manual->document;

        $this->isEditing = true;
        $this->showModal = true;
        $this->dispatch("toggle-choices", $manual->roles->pluck("id")->toArray());
    }

    public function updateDocument()
    {
        $manual = UserManual::findOrFail($this->editingId);

        $data = [
            'title' => $this->title,
            'description' => $this->description,
            'menu_item_id' => $this->category,
        ];

        if (is_object($this->document) && $this->document->isValid()) {
            if ($manual->document && Storage::exists($manual->document)) {
                Storage::delete($manual->document);
            }

            $data['document'] = $this->document->store('uploads/user-manual','public');
        }

        $manual->update($data);
        $manual->roles()->sync($this->roles);

        $this->notify("Manual updated successfully.")->type('success')->send();
    }

    public function save()
    {
        $this->validate();

        if ($this->isEditing) {
            $this->updateDocument();
        } else {
            $this->addDocument();
        }

        $this->resetForm();
        $this->dispatch('hide-model');
    }

    public function deleteAdminManual($id)
    {
        $manual = UserManual::findOrFail($id);
        if ($manual->document && File::exists(storage_path('app/' . $manual->document))) {
            File::delete(storage_path('app/' . $manual->document));
        }
        $manual->delete();
        $this->notify("Manual deleted successfully.")->type('success')->send();
        unset($this->list);
    }

    #[On('hidden.bs.modal')]
    public function hidden()
    {
        $this->resetForm();
    }
    public function render()
    {
        return view('livewire.manual.admin-manual', [
            'userManuals' => $this->list()
        ]);
    }
}
