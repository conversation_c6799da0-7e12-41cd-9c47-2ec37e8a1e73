<?php

namespace App\Livewire\EmployeeTicket;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\EmployeeTicketProcess as EnumsEmployeeTicketProcess;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\EmployeeTicketRepository;
use App\Http\Repositories\PayslipApprovalRepository;
use App\Http\Repositories\PayslipRepository;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Models\Payroll\PayslipRequest;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Reactive;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\WithFileUploads;

class TransferForm extends Component
{
    use WithNotify, WithFileUploads;

    #[Reactive]
    public $employeeId;
    public $localEmployeeId;

    public $payslip_change = false;

    public $bands = [], $levels = [], $salaryDetails = [];

    public $company_id, $department_id, $branch_id, $sub_branch_id, $unit_id, $next_owner_id, $documents = [], $type = "transfer";

    public $selectedAdditionalAllowance = [], $existingDocuments = [];

    public PayslipForm $payslipForm;

    private array $dataValidationAttributes = [];
    public $showDojInhouse = false;

    public function rules()
    {
        return [
            "company_id" => "required|exists:companies,id",
            "branch_id" => "required|exists:branches,id",
            "sub_branch_id" => "nullable|exists:sub_branches,id",
            "department_id" => "required|exists:departments,id",
            "unit_id" => "nullable|exists:units,id",
            "next_owner_id" => "required",
            "type" => "required|in:transfer,role_change",
        ];
    }

    public function validationAttributes()
    {
        return [
            "company_id" => "company",
            "branch_id" => "branch",
            "sub_branch_id" => "sub branch",
            "department_id" => "department",
            "unit_id" => "unit",
            "next_owner_id" => "verifier",
        ];
    }

    private EmployeeTicketRepository $repo;

    public function __construct()
    {
        $this->repo = new EmployeeTicketRepository;
    }

    // public function mount()
    // {
    //     $this->loadDefaultAllowances();
    // }

    #[Computed(persist: true)]
    public function companyList()
    {
        if (scopeAll()) {
            return \App\Models\configs\Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
        } else {
            return \App\Models\configs\Company::orderBy('name', 'asc')->where('id', $this->company_id)->pluck("name", "id")->toArray();
        }
    }


    #[Computed(persist: true)]
    public function branchList()
    {
        if (!$this->company_id)
            return [];
        return \App\Models\configs\Branch::where("company_id", $this->company_id)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function subBranchList()
    {
        if (!$this->branch_id)
            return [];
        return \App\Models\configs\SubBranch::where("branch_id", $this->branch_id)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return \App\Models\configs\Department::where('company_id', $this->company_id)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function currentPayslip()
    {
        $payslipRepo = new PayslipApprovalRepository;
        return $payslipRepo->getTransferPayslipData($this->employeeId);
    }

    #[Computed(persist: true)]
    public function unitList()
    {
        if (!$this->department_id)
            return [];
        return \App\Models\configs\Unit::where("department_id", $this->department_id)->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function nextOwners()
    {
        return (new EmployeeTicket)->getNextOwners(employeeId: $this->employeeId, isSubmitting: true, workflow: WorkflowName::EMPLOYEE_TRANSFER);
    }

    public function updatedCompanyId()
    {
        $this->reset('branch_id');
        unset($this->branchList);
        $this->updatedBranchId();
    }

    public function updatedBranchId()
    {
        $this->reset('sub_branch_id');
        unset($this->subBranchList);
    }

    public function updatedDepartmentId()
    {
        $this->reset('unit_id');
        $this->reset([
            'payslipForm.designation_id',
            'payslipForm.band_id',
            'payslipForm.level_id',
            'payslipForm.job_id'
        ]);
        unset($this->unitList, $this->designationsList, $this->jobList);
    }

    public function getDefaultData()
    {
        if ($this->employeeId) {
            $response = $this->repo->getDefaultData((int) $this->employeeId, EnumsEmployeeTicketProcess::Transfer);
            if ($response['status']) {
                $data = $response['data'];
                $this->company_id = $data['company_id'];
                $this->department_id = $data['department_id'];
                $this->branch_id = $data['branch_id'];
                $this->sub_branch_id = $data['sub_branch_id'];
                $this->unit_id = $data['unit_id'];
                unset($this->branchList, $this->subBranchList, $this->unitList, $this->designationsList, $this->jobList, $this->departmentList);
            } else {
                abort(404, $response['message']);
            }
        }
    }

    public function updatedPayslipChange()
    {
        if ($this->payslip_change) {
            $this->payslipForm->reset();
            $this->payslipForm->resetValidation();
            $this->loadDefaultAllowances();
        }
        // if ($this->payslip_change)
        //     $this->dispatch("reinitialize-date-picker");
    }

    public function updatedType()
    {
        if ($this->type == "role_change")
            $this->payslip_change = true;
    }

    #[Computed(persist: true)]
    public function designationsList()
    {
        if ($this->department_id)
            return \App\Models\configs\Department::find($this->department_id)?->designations()->pluck('designations.title', 'designations.id')->toArray();
        return [];
    }

    #[Computed(persist: true)]
    public function employeeStatusList()
    {
        return \App\Models\configs\EmpStatus::where('is_active', true)->pluck('name', 'id')->toArray();
    }

    public function updatedPayslipFormDesignationId()
    {
        $gradeStructure = GradeStructure::where('grade_id', $this->payslipForm->designation_id)->get();
        $bandIds = $gradeStructure->map(fn($structure) => $structure->band_id)->unique()->toArray();
        $this->bands = EmployeeBand::whereIn('id', $bandIds)->where('status', true)->pluck('name', 'id')->toArray();
        $this->levels = [];
    }

    public function updatedPayslipFormBandId()
    {
        $gradeStructure = GradeStructure::where([
            ['grade_id', $this->payslipForm->designation_id],
            ['band_id', $this->payslipForm->band_id],
        ])->get();
        $levelIds = $gradeStructure->map(fn($structure) => $structure->pgrade_id)->unique()->toArray();
        $this->levels = EmployeePgrade::whereIn('id', $levelIds)->pluck('name', 'id')->toArray();
    }

    public function loadDefaultAllowances()
    {
        foreach ($this->allowanceList as $allowance) {
            $this->payslipForm->perks['required'][$allowance->id] = [$allowance->name => $allowance->default_value ? $allowance->default_value : ""];
        }
        $this->loadDefaultAdditionalAllowances();
    }

    public function updated($attr)
    {
        if ($attr === 'payslipForm.designation_id' || $attr === 'payslipForm.band_id' || $attr === 'payslipForm.level_id') {
            if ($attr == 'payslipForm.designation_id') {
                $this->reset('payslipForm.band_id');
                $this->reset('payslipForm.level_id');
            } elseif ($attr == 'payslipForm.band_id') {
                $this->reset('payslipForm.level_id');
            }
            $this->calculateSalaryDetail();
        }

        if ($attr === 'payslipForm.payslip_for') {
            $this->reset(['payslipForm.stipend', 'payslipForm.payment_frequency']);
        }

        if ($attr === 'payslipForm.enroll_status') {
            $this->reset('payslipForm.doj_inhouse');
            $this->showDojInhouse = app(PayslipRepository::class)->needDojInhouse($this->employeeId, $this->payslipForm->enroll_status);
        }
    }

    public function calculateSalaryDetail()
    {
        if ($this->payslipForm->designation_id && $this->payslipForm->band_id && $this->payslipForm->level_id) {
            $response = $this->getSalaryDetail($this->payslipForm->designation_id, $this->payslipForm->band_id, $this->payslipForm->level_id);
            if ($response['status']) {
                $this->salaryDetails = $response['data'];
            } else {
                $this->salaryDetails = [];
            }
        } else {
            $this->salaryDetails = [];
        }
    }

    #[Computed(persist: true)]
    public function jobList()
    {
        return app(\App\Http\Repositories\Configs\Interfaces\JobRepositoryInterface::class)->getJobs($this->branch_id, $this->department_id);
    }

    #[Computed(persist: true)]
    public function payslipNextOwners()
    {
        return (new PayslipRequest)->getNextOwners($this->employeeId, true);
    }

    #[Computed(persist: true)]
    public function allowanceList()
    {
        return \App\Models\Payroll\Perk::where('is_required', '=', '1')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceList()
    {
        return \App\Models\Payroll\Perk::where('is_required', '=', '0')->get();
    }

    #[Computed(persist: true)]
    public function additionalAllowanceFormList()
    {
        $formData = [];
        foreach ($this->additionalAllowanceList as $allowance) {
            if (in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                array_push($formData, $allowance);
            }
        }

        return $formData;
    }

    public function updatedSelectedAdditionalAllowance()
    {
        unset($this->additionalAllowanceFormList);
        $this->loadDefaultAdditionalAllowances();
        $this->unsetAdditionalAllowanceErrors();
    }

    public function loadDefaultAdditionalAllowances()
    {
        $this->additionalAllowanceList
            ->filter(fn($allowance) => !in_array($allowance->id, $this->selectedAdditionalAllowance))
            ->each(function ($allowance) {
                $this->payslipForm->perks['optional'][$allowance->id] = [$allowance->name => $allowance->default_value ? $allowance->default_value : ""];
            });
        // dd($this->payslipForm->perks);
    }

    public function unsetAdditionalAllowanceErrors()
    {
        foreach ($this->allowanceList as $allowance) {
            if (!in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                $this->payslipForm->resetValidation("perks.required.{$allowance->id}.{$allowance->name}");
            }
        }
        foreach ($this->additionalAllowanceList as $allowance) {
            if (!in_array($allowance->id, $this->selectedAdditionalAllowance)) {
                $this->payslipForm->resetValidation("perks.optional.{$allowance->id}.{$allowance->name}");
            }
        }
    }

    public function setAdditionalAllowance()
    {
        $perksKeyAndAmount = [];

        if (!$this->payslipForm->perks) {
            return;
        }

        foreach ($this->payslipForm->perks as $perksArray) {
            foreach ($perksArray as $key => $value) {
                if ($this->additionalAllowanceList->filter(fn($allowance) => $allowance->id == $key)->isNotEmpty()) {
                    if (in_array($key, $this->selectedAdditionalAllowance)) {
                        $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                    }
                } else {
                    $perksKeyAndAmount[$key] = [array_key_first($value) => last($value)];
                }
            }
        }
        $this->payslipForm->additionalAllowance = json_encode($perksKeyAndAmount);
    }

    public function getSalaryDetail($designationId, $bandId, $levelId)
    {
        // Get grade structure
        $gradeStructure = GradeStructure::where([
            ['grade_id', $designationId],
            ['band_id', $bandId],
            ['pgrade_id', $levelId]
        ])->first();

        // Check if grade structure exists
        if (!$gradeStructure) {
            return ["status" => false, "data" => "Grade structure not found for given band and designation"];
        }
        $salaryDetails = [
            "basic_salary" => $gradeStructure->basic_salary,
            "pf" => $gradeStructure->pf,
            "ssf" => $gradeStructure->ssf,
            "allowance" => $gradeStructure->allowance,
            "gross_salary" => $gradeStructure->gross_salary,
            "gratuity" => $gradeStructure->gratuity,
            "ctc" => $gradeStructure->ctc,
        ];

        // prepare salary_metadata
        return [
            "status" => true,
            "data" => $salaryDetails,
        ];
    }

    public function boot()
    {
        if ($this->localEmployeeId != $this->employeeId) {
            $this->localEmployeeId = $this->employeeId;
            $this->getDefaultData();
            $this->reset('next_owner_id', 'payslip_change');
            unset($this->nextOwners);
            unset($this->currentPayslip);
            unset($this->payslipNextOwners);
        }
    }

    public function render()
    {
        return view('livewire.employee-ticket.transfer-form');
    }

    public function save()
    {
        $this->payslipForm->employee_id = $this->employeeId;
        if ($this->payslip_change) {
            $this->validate();
            $this->setAdditionalAllowance();
            $payslipRequestTicket = PayslipRequest::where([['employee_id', $this->payslipForm->employee_id]])
                ->whereNotIn('state', ArflowHelper::getFinalStates(WorkflowName::PAYSLIP_APPROVAL))
                ->get();
            if (count($payslipRequestTicket)) {
                $this->notify("A payslip has already been requested.")->type("error")->duration(5)->send();
                return;
            }
            $this->payslipForm->validateFormData();
        } else {
            $this->validate($this->rules(), attributes: $this->validationAttributes());
        }

        try {
            $response = $this->repo->createTransferTicket(
                (int) $this->employeeId,
                [
                    'payslip_change' => $this->payslip_change,
                    'company_id' => $this->company_id,
                    'branch_id' => $this->branch_id,
                    'sub_branch_id' => $this->sub_branch_id,
                    'department_id' => $this->department_id,
                    'unit_id' => $this->unit_id,
                    'next_owner_id' => $this->next_owner_id,
                    'type' => $this->type,
                    ...($this->payslip_change ? [
                        ...$this->payslipForm->all(),
                        'salary_details' => $this->salaryDetails,
                    ] : []),
                    'documents' => $this->documents, // documents key word also exists in $this->payslipForm so the document is kept in last so it is not override.
                ],
                $this->dataValidationAttributes
            );
            if ($response['status']) {
                $this->notify($response['message'])->send();
                \redirect(route('employeeTicketList'));
            } else {
                $this->notify($response['message'])->type("error")->send();
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = $e->validator->errors();

            foreach ($errors->messages() as $field => $messages) {
                foreach ($messages as $message) {
                    $this->addError('data.' . $field, $message);
                }
            }
        }
    }
}
