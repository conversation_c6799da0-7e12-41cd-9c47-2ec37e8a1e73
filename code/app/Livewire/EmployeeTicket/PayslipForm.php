<?php

namespace App\Livewire\EmployeeTicket;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Repositories\PayslipRepository;
use Illuminate\Support\Facades\Log;
use Livewire\Form;

class PayslipForm extends Form
{
    public $company, $employee_id, $employee_status, $perks = [], $documents = [];
    public $job_id, $designation_id, $band_id, $level_id, $enroll_status, $stipend = 0, $cit = 0, $insurance = 0, $allow_ot = false,
        $payment_type, $payslip_type, $payslip_remarks, $additionalAllowance, $verifier_id, $insurance_expiry_date,
        $start_date_nep, $stop_date_nep, $doj_inhouse;
    public $payslip_for = "Full_time", $payment_frequency;
    public $showStipendFor = Constant::STIPEND_FOR;

    public function rules()
    {
        $ruleArray = [
            "payslip_for" => ['required', 'in:Full_time,Part_time,Intern,Trainee,Hourly'],
            "job_id" => ['required'],
            "designation_id" => ['required'],
            "band_id" => ['required'],
            "level_id" => ['required'],
            "enroll_status" => ['required'],
            "stipend" => ['numeric', 'min:0'],
            "cit" => ['numeric', 'min:0'],
            "insurance" => ['numeric', 'min:0'],
            "insurance_expiry_date" => ['required_if:insurance,>0'],
            "allow_ot" => ['boolean'],
            "payment_type" => ['required'],
            "payslip_type" => ['required'],
            "stop_date_nep" => ['required'],
            "start_date_nep" => ['required', function ($attribute, $value, $fail) {
                $startDate = LaravelNepaliDate::from($value)->toEnglishDate();
                if ($this->stop_date_nep !==  null) {
                    $stopDate = LaravelNepaliDate::from($this->stop_date_nep)->toEnglishDate();
                    if ($startDate >= $stopDate) {
                        $fail('The start date should be earlier than the stop date.');
                    }
                }
            }],
            "payslip_remarks" => ['nullable', 'max:250'],
            "verifier_id" => ['required'],
            "documents.*" => 'file|mimes:pdf,docx,doc,jpg,jpeg,png',
            "employee_status" => "required"
        ];

        if (in_array($this->payslip_for, $this->showStipendFor)) {
            $rules = [
                'stipend' => ['required', 'numeric', 'min:1'],
                "designation_id" => ['nullable'],
                "band_id" => ['nullable'],
                "level_id" => ['nullable'],
                'payment_frequency' => ['required', 'in:Day,Hour,Month']
            ];

            $ruleArray = [...$ruleArray, ...$rules];
        }

        if (count($this->perks['required'] ?? [])) {
            $perksRequiredRule = [];
            foreach ($this->perks['required'] as $key => $allowance) {
                foreach ($allowance as $name => $value) {
                    $ruleProperty = "perks.required.{$key}.{$name}";
                    $rule = ['required', 'numeric', 'min:0'];
                    $perksRequiredRule[$ruleProperty] = $rule;
                }
            }
            $ruleArray = [...$ruleArray, ...$perksRequiredRule];
        }

        if (count($this->perks['optional'] ?? [])) {
            $perksOptionalRule = [];
            foreach ($this->perks['optional'] as $key => $allowance) {
                foreach ($allowance as $name => $value) {
                    $ruleProperty = "perks.optional.{$key}.{$name}";
                    $rule = ['nullable', 'numeric', 'min:0'];
                    $perksRequiredRule[$ruleProperty] = $rule;
                }
            }
            $ruleArray = [...$ruleArray, ...$perksOptionalRule];
        }


        if (app(PayslipRepository::class)->needDojInhouse($this->employee_id, $this->enroll_status)) {
            $ruleArray['doj_inhouse'] = ['required'];
        }

        return $ruleArray;
    }

    public function validationAttributes()
    {
        $perksAttribute = [];

        if (count($this->perks['required'] ?? [])) {
            foreach ($this->perks['required'] as $key => $allowance) {
                foreach ($allowance as $name => $value) {
                    $ruleProperty = "perks.required.{$key}.{$name}";
                    $perksAttribute[$ruleProperty] = strtolower($name);
                }
            }
        }

        return [
            'job_id'            => 'job',
            'designation_id'    => 'designation',
            'band_id'           => 'band',
            'level_id'          => 'level',
            'start_date_nep'    => 'start date',
            'stop_date_nep'     => 'stop date',
            'verifier_id'       => 'payslip approver',
            'employee_status'   => 'employee status',
            ...$perksAttribute,
        ];
    }

    public function validateFormData()
    {
        $validated = $this->validate();
    }

    public function resetFormField()
    {
        $this->reset(['company', 'employee_status', 'perks', 'documents']);
        $this->reset([
            'job_id',
            'designation_id',
            'band_id',
            'level_id',
            'enroll_status',
            'cit',
            'insurance',
            'insurance_expiry_date',
            'allow_ot',
            'payment_type',
            'payslip_type',
            'start_date_nep',
            'stop_date_nep',
            'payslip_remarks',
            'additionalAllowance',
            'verifier_id',
            'payslip_for',
            'stipend',
            'payment_frequency'
        ]);
        $this->resetErrorBag();
    }
}
