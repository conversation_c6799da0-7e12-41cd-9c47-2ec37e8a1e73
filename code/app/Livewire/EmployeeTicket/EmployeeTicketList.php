<?php

namespace App\Livewire\EmployeeTicket;

use App\Http\Helpers\Enums\WorkflowName;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Traits\WithDataTable;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use App\Models\configs\Company;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use PermissionList;

#[Title('Employee Ticket List')]
class EmployeeTicketList extends Component
{
    use WithDataTable;

    public $selectedId, $selectedWorkflow;

    // filters
    public $process;
    public $state;
    public $companyId, $branchId, $departmentId, $regionId;

    public function mount()
    {
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        if (!scopeAll()) {
            $this->companyId = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->regionId = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->branchId = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
            $this->departmentId = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $accessibleProcessIds = \App\Models\EmployeeTicket\EmployeeTicketProcess::getAccessibleProcesses();
        $query = EmployeeTicket::with('metaData')
            ->leftJoin("employee_ticket_processes as process", 'employee_tickets.process_id', '=', 'process.id')
            ->leftJoin("request_tickets", function (JoinClause $join) {
                $join->on("employee_tickets.id", '=', "request_tickets.model_id")
                    ->where('model_type', EmployeeTicket::class);
                $join->leftJoin("employees as emp", 'request_tickets.employee_id', '=', 'emp.id');
                $join->leftJoin("employees as submitted_by", 'request_tickets.submitted_by', '=', 'submitted_by.id');
                $join->leftJoin("employees as currentOwner", 'request_tickets.current_owner_id', '=', 'currentOwner.id');
            })
            ->leftJoin('employee_org as org', 'emp.id', '=', 'org.employee_id')
            ->leftJoin('departments', 'org.department_id', '=', 'departments.id')
            ->leftJoin('branches', 'org.branch_id', '=', 'branches.id')
            ->select(
                "employee_tickets.id",
                "process.name as process",
                "employee_tickets.state",
                "employee_tickets.workflow",
                DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as emp_name"),
                DB::raw("CONCAT_WS(' ', submitted_by.first_name, submitted_by.middle_name, submitted_by.last_name) as submitted_by"),
                DB::raw("CONCAT_WS(' ', currentOwner.first_name, currentOwner.middle_name, currentOwner.last_name) as current_owner"),
                'org.department_id as department_id',
                'departments.name as department_name',
                'org.branch_id as branch_id',
                'branches.name as branch_name'
            )
            ->when($this->process, function ($query) {
                $query->where('process.id', $this->process);
            })
            ->when($this->state, function ($query) {
                $query->where('employee_tickets.state', $this->state);
            })
            ->when($this->companyId, function ($query) {
                $query->where('branches.company_id', $this->companyId);
            })
            ->when($this->branchId, function ($query) {
                $query->where('branches.id', $this->branchId);
            })
            ->when($this->departmentId, function ($query) {
                $query->where('departments.id', $this->departmentId);
            })
            ->when($this->search, function ($query) {
                $query->where(DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name)"), 'LIKE', "%$this->search%")
                    ->orWhere(DB::raw("CONCAT_WS(' ', emp.first_name, emp.last_name)"), 'LIKE', "%$this->search%");
            })
            ->when($this->sortBy, function ($query) {
                $query->orderBy($this->sortBy, $this->sortDirection);
            })
            ->when(!$this->sortBy, function ($query) {
                $query->orderBy('employee_tickets.created_at', 'desc');
            })
            ->whereIn('employee_tickets.process_id', $accessibleProcessIds);

        $query = filterEmployeesByScope($query, 'org');
        $result = $this->applySorting($query)->paginate($this->perPage);
        foreach ($result->getCollection() as $item) {
            $metaData = $item->metaData->pluck('meta_value', 'meta_key');
            $item->payslip_change = $metaData['payslip_change'] ?? false;
            // $item->has_payslip = 
        }
        return $result;
    }

    #[Computed(persist: true)]
    public function processList()
    {
        return \App\Models\EmployeeTicket\EmployeeTicketProcess::getListForSelect();
    }

    #[Computed(persist: true)]
    public function stateList()
    {
        return \array_merge(
            \App\Http\Helpers\ArflowHelper::getStatesArray(WorkflowName::EMPLOYEE_TRANSFER)
        );
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->companyId);
            if (!is_null($this?->regionId)) {
                $query->where('region_id', $this?->regionId);
            }
            return $query->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        }
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return Department::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }
    public function updated($attr)
    {
        if ($attr == 'process' || $attr == 'state') {
            unset($this->list);
        }

        if (in_array($attr, ['companyId', 'branchId', 'departmentId'])) {
            unset($this->list);
        }

        if (in_array($attr, ['companyId'])) {
            unset($this->branches, $this->departments);
            $this->branchId = null;
            $this->departmentId = null;
        }
    }

    public function setDetail($id, $workflow)
    {
        $this->selectedId = $id;
        $this->selectedWorkflow = $workflow;
    }

    public function refreshList()
    {
        unset($this->list);
    }


    public function render()
    {
        return view('livewire.employee-ticket.employee-ticket-list');
    }
}
