<?php

namespace App\Livewire\EmployeeTicket;

use App\Http\Repositories\EmployeeTicketRepository;
use App\Models\Employee\Employee;
use App\Models\EmployeeTicket\EmployeeTicketProcess;
use App\Models\Payroll\Designation;
use App\Models\Payroll\EmployeeBand;
use App\Models\Payroll\EmployeePgrade;
use App\Models\Payroll\GradeStructure;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Employee Ticket Form')]
class EmployeeTicketFormPage extends Component
{
    use WithNotify, MultiselectEmployeeSearch;

    #[Url(as: 'id')]
    public $urlEmployeeId;

    #[Url(as: 'process')]
    public $urlProcess = "Transfer";

    #[Validate('required')]
    public $process = "";

    #[Validate("required")]
    public $employee_id;

    public $employeeName;

    private EmployeeTicketRepository $repo;

    public function __construct()
    {
        $this->repo = new EmployeeTicketRepository;
    }

    #[Computed(persist: true)]
    public function processList()
    {
        $data = \App\Models\EmployeeTicket\EmployeeTicketProcess::getListForSelect();
        return \array_values($data);
    }

    public function mount()
    {
        $this->singleSelectAttributes = ['employee_id'];
        if ($this->urlEmployeeId) {
            $this->checkEmployee($this->urlEmployeeId);
        }
        if ($this->urlProcess) {
            $this->checkProcess($this->urlProcess);
        }
        $this->setPermissionForEmployeeDropdown();

    }

    public function render()
    {
        return view('livewire.employee-ticket.employee-ticket-form-page');
    }

    public function checkEmployee($employeeId)
    {
        $employee = Employee::findOrFail($employeeId);
        $this->employee_id = $employee->id;
        $this->employeeName = "$employee->name ($employee->employeeCode)";
        // dd($this->employeeName);
    }

    public function checkProcess($process)
    {
        $process = EmployeeTicketProcess::where('name', $process)->first();
        if (!$process) {
            abort("404", "Process not found");
        }
        $this->process = $process->name;
    }
}
