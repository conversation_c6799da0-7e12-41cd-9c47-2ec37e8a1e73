<?php

namespace App\Livewire;

use App\Models\configs\MailConfigurationSetting;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Jobs\SendWhistleBlowerEmailJob;
use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Title('Whistleblower Page')]
#[Layout('layouts.blank')]
class Whistleblower extends Component
{
    use WithFileUploads, WithNotify;

    #[Validate('required')]
    public $isAnonymous, $contactPermission;

    public $name, $department, $contact;

    public $observation, $date_of_incident, $location, $person_involved, $witness, $description, $declaration;

    public $selectedConcerns = [];
    public $evidence = [];

    public $concerns = [
        'Ethical Concerns' => [
            'Corruption, fraud, conflicts of interest, or breaches of professional ethics by employees within the ISP context'
        ],
        'Safety Concerns' => [
            'Workplace safety issues, including concerns about employee safety at installations, data centers, or during fieldwork'
        ],
        'Legal Concerns' => [
            'Violations of laws, regulatory non-compliance, or breaches of company policies directly involving employees'
        ],
        'Financial Concerns' => [
            'Mismanagement of company funds, fraudulent activities, or improper financial practices by employees'
        ],
        'Quality Concerns' => [
            'Non-compliance with internal quality standards by employees in service delivery or customer support processes'
        ],
        'HR Concerns' => [
            'Workplace discrimination, harassment, retaliation, or other unfair employee treatment issues'
        ],
        'Data Security and Privacy Concerns' => [
            'Employee involvement in breaches of customer data security, privacy violations, or mishandling of sensitive organizational information'
        ],
        'Customer Service Concerns' => [
            'Improper handling of customer interactions or service issues by employees, impacting customer satisfaction or the company’s reputation.'
        ],
        'Operational Concerns' => [
            'Inefficiencies or misconduct in internal operations, such as service delivery delays or misuse of resources, involving employees.'
        ],
        'Technology Concerns' => [
            'Improper use of company technology, cybersecurity threats caused by employee actions, or technical negligence affecting ISP services'
        ],
        'Other Unethical Conduct' => [
            'Any unethical practices by employees not covered above, such as misuse of company assets or non-adherence to organizational values.'
        ]
    ];

    // Validation Rules
    protected function rules()
    {
        return [
            'isAnonymous' => 'required|in:Yes,No',
            'name' => $this->isAnonymous === 'No' ? 'required|max:255' : 'nullable',
            'department' => $this->isAnonymous === 'No' ? 'required|max:255' : 'nullable',
            'contactPermission' => 'required|in:Yes,No',
            'contact' => $this->contactPermission === 'Yes' ? 'required|max:255' : 'nullable',
            'observation' => 'nullable|max:255',
            'date_of_incident' => ['nullable', function ($attribute, $value, $fail) {
                $today = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y-m-d');
                if ($this->date_of_incident) {
                    if ($this->date_of_incident > $today) {
                        $fail('The date of incident cannot be a future date.');
                    }
                }
            }],
            'location' => 'required|max:255',
            'person_involved' => 'nullable|max:255',
            'witness' => 'nullable|max:255',
            'selectedConcerns' => 'required|array|min:1',
            'description' => 'required|min:10',
            'evidence.*' => 'nullable|file|mimes:jpeg,jpg,png,pdf,doc,docx,mp4,mpeg,avi|max:20480', // 20MB for each file
            'declaration' => ['required']
        ];
    }

    // Custom Messages
    protected $messages = [
        'isAnonymous.required' => 'Please select if you wish to remain anonymous or not.',
        'name.required' => 'The name is required when you are not be an anonymous.',
        'department.required' => 'Department/Position is required when you are not anonymous.',
        'contactPermission.required' => 'Please indicate if we can contact you or not.',
        'contact.required' => 'The contact information is required if permission is granted.',
        'description.required' => 'Description of the concern is required.',
        'selectedConcerns.required' => 'Please select at least one concern.',
        'evidence.*.mimes' => 'Only JPEG, JPG, PNG, PDF, DOC, DOCX, MP4, MPEG or AVI files are allowed.',
        'evidence.*.max' => 'File size should not exceed 20MB.',
        'declaration.required' => 'You must agree to submit the form.',
    ];

    public function submit()
    {
        $this->validate();

        $attachments = [];
        foreach ($this->evidence as $file) {
            $attachments[] = [
                'name' => $file->getClientOriginalName(),
                'mime' => $file->getMimeType(),
                'content' => base64_encode(file_get_contents($file->getRealPath())),
            ];
        }
        $formData = [
            'isAnonymous' => $this->isAnonymous,
            'name' => $this->name,
            'department' => $this->department,
            'contactPermission' => $this->contactPermission,
            'contact' => $this->contact,
            'observation' => $this->observation,
            'date_of_incident' => $this->date_of_incident,
            'location' => $this->location,
            'person_involved' => $this->person_involved,
            'witness' => $this->witness,
            'selectedConcerns' => $this->selectedConcerns,
            'description' => $this->description,
            'evidence' => $attachments,
        ];

        $settings = Setting::where('namespace', 'whistleBlower')->pluck('value', 'key')->toArray();
        $emailAddress = "<EMAIL>";
        if (vianetHrm()) {
            $emailAddress = json_decode($settings["to_address"], true) ?? "<EMAIL>";
        } else if (konnectHrm()) {
            $emailAddress = json_decode($settings["to_address"], true) ?? "<EMAIL>";
        } else if (fedexHrm()) {
            $emailAddress = json_decode($settings["to_address"], true) ??  "<EMAIL>";
        }

        SendWhistleBlowerEmailJob::dispatch($formData, $emailAddress, currentEmployee()?->company_id ? currentEmployee()?->company_id : 1);
        $this->notify('Your concern has been submitted successfully. Thank you for your submission.')->send();
        return redirect('/');
    }
    public function render()
    {
        return view('livewire.whistleblower');
    }
}
