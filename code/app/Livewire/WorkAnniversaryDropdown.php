<?php

namespace App\Livewire;

use App\Models\Employee\EmployeeOrg;
use Carbon\Carbon;
use Livewire\Component;

class WorkAnniversaryDropdown extends Component
{
    public $anniversariesToday = [];
    public $anniversaryCount = 0;
    public $upcomingAnniversaries = [];
    public $upcomingWindowDays = 2;

    public function mount(): void
    {
        $todayAD = Carbon::today();
        $todayMonthDay = $todayAD->format('m-d');
        $currentEmployee = currentEmployee();

        $query = EmployeeOrg::with('employee.organizationInfo.department')
            ->whereNotNull('doj')
            ->whereHas('employee', function ($q) use ($currentEmployee) {
                if (!isSuperAdmin()) {
                    $q->where('company_id', $currentEmployee->company_id);
                }
            });

        $records = $query->get();

        $today = $records->filter(function ($rec) use ($todayMonthDay, $todayAD) {
            try {
                $doj = Carbon::parse($rec->doj);
                $yearsCompleted = $doj->diffInYears($todayAD);

                return $doj->format('m-d') === $todayMonthDay && $yearsCompleted >= 1;
            } catch (\Exception $e) {
                return false;
            }
        });

        $this->anniversaryCount = $today->count();

        $this->anniversariesToday = $today
            ->groupBy(fn($rec) => $rec->employee->organizationInfo?->department?->name ?? 'No Department')
            ->map(fn($group) => $group->map(fn($rec) => [
                'first_name' => $rec->employee->first_name,
                'middle_name' => $rec->employee->middle_name,
                'last_name' => $rec->employee->last_name,
                'id' => $rec->employee->id,
                'years_completed' => Carbon::parse($rec->doj)->diffInYears($todayAD),
            ])->values())
            ->toArray();

        $upcomingItems = $records->map(function ($rec) use ($todayAD) {
            try {
                if (!$rec->doj) {
                    return null;
                }

                $doj = Carbon::parse($rec->doj);

                $nextAnniversary = $doj->copy()->year($todayAD->year);

                if ($nextAnniversary->lessThan($todayAD)) {
                    $nextAnniversary->addYear();
                }

                $daysLeft = $todayAD->diffInDays($nextAnniversary);

                if ($daysLeft <= 0 || $daysLeft > $this->upcomingWindowDays) {
                    return null;
                }

                $yearsToBeCompleted = $doj->diffInYears($nextAnniversary);

                if ($yearsToBeCompleted < 1) {
                    return null;
                }

                return [
                    'date' => $nextAnniversary->toDateString(),
                    'days_left' => $daysLeft,
                    'first_name' => $rec->employee->first_name,
                    'middle_name' => $rec->employee->middle_name,
                    'last_name' => $rec->employee->last_name,
                    'id' => $rec->employee->id,
                    'department' => $rec->employee->organizationInfo?->department?->name ?? 'No Department',
                    'years_completed' => $yearsToBeCompleted, // Years they will complete on anniversary
                ];
            } catch (\Exception $e) {
                return null;
            }
        })
            ->filter()
            ->sortBy('days_left')
            ->values();

        $this->upcomingAnniversaries = $upcomingItems->groupBy('date')->toArray();
    }
    public function render()
    {
        return view('livewire.work-anniversary-dropdown');
    }
}
