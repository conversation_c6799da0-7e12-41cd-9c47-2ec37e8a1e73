<?php

namespace App\Livewire;

use App\Models\Employee\Employee;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Component;

class BirthdayDropdown extends Component
{
    public $birthdaysToday = [];
    public $birthdayCount = 0;
    public $upcomingBirthdays = [];
    public $upcomingWindowDays = 2;

    public function mount(): void
    {
        $todayAD = Carbon::today();
        $todayMonthDay = $todayAD->format('m-d'); 
        $currentEmployee = currentEmployee();

        $query = Employee::with('organizationInfo.department')
            ->whereNotNull('dob');

        if (!isSuperAdmin()) {
            $query->where('company_id', $currentEmployee->company_id);
        }

        $employees = $query->get();

        // TODAY’S BIRTHDAYS
        $todayEmployees = $employees->filter(function ($emp) use ($todayMonthDay) {
            try {
                $dobMonthDay = Carbon::parse($emp->dob)->format('m-d'); 
                return $dobMonthDay === $todayMonthDay;
            } catch (\Exception $e) {
                return false;
            }
        });

        $this->birthdayCount = $todayEmployees->count();

        $this->birthdaysToday = $todayEmployees
            ->groupBy(fn($emp) => $emp->organizationInfo?->department?->name ?? 'No Department')
            ->map(fn($group) => $group->map(fn($emp) => [
                'first_name'  => $emp->first_name,
                'middle_name' => $emp->middle_name,
                'last_name'   => $emp->last_name,
                'id'          => $emp->id,
            ])->values())
            ->toArray();

        // UPCOMING BIRTHDAYS
        $items = $employees->map(function ($emp) use ($todayAD) {
            try {
                $dobCarbon = Carbon::parse($emp->dob)->year($todayAD->year);

                if ($dobCarbon->lessThan($todayAD)) {
                    $dobCarbon->addYear();
                }

                $daysLeft = $todayAD->diffInDays($dobCarbon);

                return [
                    'date'       => $dobCarbon->toDateString(),
                    'days_left'  => $daysLeft,
                    'first_name' => $emp->first_name,
                    'middle_name'=> $emp->middle_name,
                    'last_name'  => $emp->last_name,
                    'id'         => $emp->id,
                    'department' => $emp->organizationInfo?->department?->name ?? 'No Department',
                ];
            } catch (\Exception $e) {
                return null;
            }
        })
            ->filter()
            ->filter(fn($x) => $x['days_left'] > 0 && $x['days_left'] <= $this->upcomingWindowDays)
            ->sortBy('days_left')
            ->values();

        $this->upcomingBirthdays = $items->groupBy('date')->toArray();
    }

    public function render()
    {
        return view('livewire.birthday-dropdown');
    }
}
