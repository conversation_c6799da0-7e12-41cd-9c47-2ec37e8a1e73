<?php

namespace App\Livewire\Tickets;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Models\TimeRequest;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Time Requests List')]
class TimeRequestList extends Component
{
    use WithDataTable, WithDefaultFilter;
    public $startDate, $endDate, $status, $selectedId;

    public function mount()
    {
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->startDate = LaravelNepaliDate::from(Carbon::now()->subDay(30))->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();
        $orgInfo = $currentEmployee?->organizationInfo;

        $this->filterCompanyId = $currentEmployee?->company_id;

        if (scopeCompany() || scopeRegion()) {
            // No additional action needed since filterCompanyId is already set
        } elseif (scopeBranch()) {
            $this->filterBranchId = $orgInfo?->branch_id;
        } else {
            $this->filterBranchId = $orgInfo?->branch_id;
            $this->filterDepartmentId = $orgInfo?->department_id;
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = TimeRequest::query()
            ->leftJoin('employees as emp', 'time_requests.employee_id', 'emp.id')
            ->leftJoin('companies as company', 'company.id', 'emp.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'time_requests.employee_id')
            ->leftJoin("branches as branch", "branch.id", "org.branch_id")
            ->leftJoin("departments as department", "department.id", "org.department_id");

        $this->defaultFilterQuery($query, 'emp', 'org');
        filterEmployeesByScope($query, 'org', 'emp');
        Employee::searchEmployee($this->search, $query, 'company', 'org', 'emp');

        $query->when($this->status, fn($query) => $query->where('time_requests.state', $this->status))
            ->when(
                $this->startDate && $this->endDate,
                fn($query) => $query->whereBetween('time_requests.nep_date', [$this->startDate, $this->endDate])
            );

        $query->select(
            'time_requests.id',
            Employee::selectNameRawQuery('emp', 'emp_name'),
            Employee::selectEmpCodeRawQuery('company', 'org', 'emp_code'),
            'branch.name as branch',
            'department.name as department',
            'time_requests.nep_date',
            'time_requests.in_time',
            'time_requests.in_note',
            'time_requests.out_time',
            'time_requests.out_note',
            'time_requests.state',
            'time_requests.created_at',
        );
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::TIME_REQUEST_APPROVAL);
    }

    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
        if (in_array($attr, ['startDate', 'endDate', 'status'])) {
            unset($this->list);
        }
    }

    public function render()
    {
        return view('livewire.tickets.time-request-list');
    }
}
