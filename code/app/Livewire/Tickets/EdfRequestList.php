<?php

namespace App\Livewire\Tickets;

use App\Facades\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Tickets\EdfRequest;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\Attributes\Title;

#[Title('EDF Request List')]

class EdfRequestList extends Component
{
    use WithDataTable, WithDefaultFilter;

    public $status, $selectedId;

    public function mount()
    {
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();
        $orgInfo = $currentEmployee?->organizationInfo;

        $this->filterCompanyId = $currentEmployee?->company_id;

        if (scopeCompany() || scopeRegion()) {
            // No additional action needed since filterCompanyId is already set
        } elseif (scopeBranch()) {
            $this->filterBranchId = $orgInfo?->branch_id;
        } else {
            $this->filterBranchId = $orgInfo?->branch_id;
            $this->filterDepartmentId = $orgInfo?->department_id;
        }
    }


    #[Computed(persist: true)]
    public function list()
    {
        $query = EdfRequest::query()
            ->leftJoin('companies as company', 'company.id', 'edf_requests.company_id')
            ->leftJoin('departments as department', 'department.id', 'edf_requests.department_id')
            ->leftJoin('branches as branch', 'branch.id', 'edf_requests.branch_id')
            ->leftJoin('employees as employee', 'employee.id', 'edf_requests.employee_id')
            ->leftJoin('employee_org as employee_org', 'employee_org.employee_id', 'edf_requests.employee_id');

        $query->when($this->status, fn($query) => $query->where('edf_requests.state', $this->status));

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if (scopeCompany()) {
                $query->where('company.id', currentEmployee()?->company_id);
            } else if (scopeRegion()) {
                $region = currentEmployee()?->organizationInfo?->region;
                if ($region) {
                    $branchIds = $region->branches->pluck('id')->toArray();
                    $query = $query->whereIn("employee_org.branch_id", $branchIds);
                }
            } else if (scopeBranch()) {
                $query->where('employee_org.branch_id', currentEmployee()?->organizationInfo?->branch_id);
            } else if (scopeDepartment()) {
                $query->where('employee_org.department_id', currentEmployee()?->organizationInfo?->department_id);
            } else {
                $query->where("employee_org.branch_id", $orgInfo->branch_id);;
                // $query->where('edf_requests.employee_id', currentEmployee()?->id);
            }
        }

        $query
            ->when($this->filterCompanyId, fn($query) => $query->where('edf_requests.company_id', $this->filterCompanyId))
            ->when($this->filterRegionId, function ($query) {
                $region = \App\Models\configs\Region::find($this->filterRegionId);
                if ($region) {
                    $branchIds = $region->branches->pluck('id')->toArray();
                    $query = $query->whereIn("edf_requests.branch_id", $branchIds);
                }
            })
            ->when($this->filterBranchId, fn($query) => $query->where('edf_requests.branch_id', $this->filterBranchId))
            ->when($this->filterDepartmentId, fn($query) => $query->where('edf_requests.department_id', $this->filterDepartmentId))
            ->when(
                $this->search,
                fn($query) =>
                $query->where(DB::raw("CONCAT_WS(' ', edf_requests.first_name, edf_requests.middle_name, edf_requests.last_name)"), "like", "%$this->search%")
                    ->orWhere(DB::raw("CONCAT_WS(' ', edf_requests.first_name, edf_requests.last_name)"), "like", "%$this->search%")
            );

        $query->select(
            'edf_requests.id',
            'edf_requests.workflow',
            DB::raw("CONCAT_WS(' ', edf_requests.first_name, edf_requests.middle_name, edf_requests.last_name) as employee_name"),
            'company.name as company',
            'department.name as department',
            'branch.name as branch',
            DB::raw("CONCAT_WS(' ', employee.first_name, employee.middle_name, employee.last_name) as submitted_by"),
            'edf_requests.state',
            'edf_requests.created_at',
        );
        $query = filterEmployeesByScope($query, 'employee_org', 'employee');

        return $this->applySorting($query)->paginate(10);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::EDF_REQUEST);
    }

    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
        if (in_array($attr, ['status'])) {
            unset($this->list);
        }
    }

    public function render()
    {
        return view('livewire.tickets.edf-request-list');
    }
}
