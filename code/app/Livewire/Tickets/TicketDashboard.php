<?php

namespace App\Livewire\Tickets;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\RequestTicket;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Ticket Dashboard')]
class TicketDashboard extends Component
{
    #[Computed(persist: true)]
    public function ticketCounts()
    {
        $query =  RequestTicket::query()
            ->select('workflow')
            ->selectRaw('COUNT(*) as total_count')
            ->selectRaw("SUM(CASE 
                WHEN state IN (
                    '" . WorkflowState::APPROVED . "', 
                    '" . WorkflowState::ASSIGNED . "'
                ) THEN 1 
                ELSE 0 
            END) as approved_count")
            ->selectRaw("SUM(CASE 
                WHEN state = '" . WorkflowState::REJECTED . "' THEN 1 
                ELSE 0 
            END) as rejected_count")
            ->selectRaw("SUM(CASE 
                WHEN state NOT IN (
                    '" . WorkflowState::APPROVED . "', 
                    '" . WorkflowState::ASSIGNED . "',
                    '" . WorkflowState::REJECTED . "',
                    '" . WorkflowState::CANCELLED . "',
                    '" . WorkflowState::REVERTED . "'
                ) THEN 1 
                ELSE 0 
            END) as pending_count");

        $query = $query->leftJoin('employee_org as org', 'org.employee_id', 'request_tickets.employee_id');
        // $query = $query->leftJoin('employees', 'employees.id', 'org.employee_id');
        $query = filterEmployeesByScope($query, 'org');

        $result = $query->orderBy('workflow')->groupBy('workflow')
            ->get();
        foreach ($result as $item) {
            $item->link = $this->ticketLinks[$item->workflow] ?? '#';
        }
        return $result;
    }

    #[Computed()]
    public function ticketLinks()
    {
        $ticketLinks = [];
        foreach(config('arflow-config.ticketListPage') ?? [] as $key => $value) {
            $ticketLinks[$key] = route($value);
        }
        return $ticketLinks;
    }

    public function render()
    {
        return view('livewire.tickets.ticket-dashboard');
    }
}
