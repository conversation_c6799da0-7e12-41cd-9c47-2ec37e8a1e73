<?php

namespace App\Livewire\Tickets;

use App\Http\Helpers\ArflowHelper;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\RequestTicket;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;

class TicketTransfer extends Component
{
    use WithDataTable, WithNotify;

    public $from_owner_id, $to_owner_id, $workflow;

    public $generated = false;

    public $list = [];

    #[Computed(persist: true)]
    public function employeeListWithTerminated()
    {
        return Employee::list(['with_trashed' => true]);
    }

    #[Computed(persist: true)]
    public function employeeList()
    {
        return Employee::list();
    }

    #[Computed(persist: true)]
    public function workflowList()
    {
        return ArflowHelper::getWorkflows();
    }

    public function updated($attr)
    {
        if (in_array($attr, ['workflow', 'from_owner_id'])) {
            $this->resetList();
        }
    }

    public function listQuery()
    {
        return RequestTicket::query()
            ->where('workflow', $this->workflow)
            ->where('current_owner_id', $this->from_owner_id);
    }

    public function viewList()
    {
        $this->validate([
            'from_owner_id' => 'required|exists:employees,id',
            'workflow' => 'required'
        ]);
        $this->list = $this->listQuery()->get();
        $this->generated = true;
    }

    public function transfer()
    {
        $this->validate([
            'from_owner_id' => 'required|exists:employees,id',
            'to_owner_id' => 'required|exists:employees,id',
            'workflow' => 'required'
        ]);
        $requestTickets = $this->listQuery()->get();

        if (!$requestTickets->count()) {
            return $this->notify("No tickets found")->type('error')->send();
        }

        DB::transaction(function () use ($requestTickets) {
            foreach ($requestTickets as $requestTicket) {
                $requestTicket->current_owner_id = $this->to_owner_id;
                $requestTicket->save();

                $existingPerformers = TransitionPerformer::where([
                    ['recipient_id', '=', $requestTicket->submitted_by],
                    ['performer_id', '=', $this->from_owner_id],
                    ['workflow', '=', $this->workflow],
                ])->get();

                foreach ($existingPerformers as $existing) {
                    TransitionPerformer::updateOrCreate([
                        'recipient_id' => $requestTicket->submitted_by,
                        'performer_id' => $this->to_owner_id,
                        'workflow' => $this->workflow,
                        'state' => $existing->state,
                        'level' => $existing->level,
                    ]);
                }
            }
        });
        $this->notify("Transfer successful")->send();
        $this->resetList();
    }

    public function resetList()
    {
        $this->generated = false;
        $this->list = [];
        $this->to_owner_id = null;
        $this->dispatch('toggle-to-owner', ['']);
    }

    public function render()
    {
        return view('livewire.tickets.ticket-transfer');
    }
}
