<?php

namespace App\Livewire\Tickets;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Exports\OtExport;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Models\OtRequests;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

#[Title('OT Request List')]
class OtRequestList extends Component
{
    use WithDataTable, WithDefaultFilter;
    public $startDate, $endDate, $status, $selectedId;

    public function mount()
    {
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->startDate = LaravelNepaliDate::from(Carbon::now()->subDay(30))->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
    }

    public function getOtQuery()
    {
        $query = OtRequests::query()
            ->leftJoin('employees as emp', 'ot_requests.employee_id', 'emp.id')
            ->leftJoin('companies as company', 'company.id', 'emp.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'ot_requests.employee_id')
            ->leftJoin('branches as branch', 'branch.id', 'org.branch_id')
            ->leftJoin('outsource_companies as outsource', 'outsource.id', 'org.outsource_company_id')
            ->leftJoin('departments as department', 'department.id', 'org.department_id');

        $this->defaultFilterQuery($query, 'emp', 'org');
        filterEmployeesByScope($query, 'org', 'emp');
        Employee::searchEmployee($this->search, $query, 'company', 'org', 'emp');

        $query->when($this->status, fn($query) => $query->where('ot_requests.state', $this->status))
            ->when(
                $this->startDate && $this->endDate,
                fn($query) => $query->whereBetween('ot_requests.nep_date', [$this->startDate, $this->endDate])
            );
        $query->select(
            'ot_requests.id',
            Employee::selectNameRawQuery('emp', 'emp_name'),
            Employee::selectEmpCodeRawQuery('company', 'org', 'emp_code'),
            'branch.name as branch',
            'department.name as department',
            'outsource.name as outsource',
            'company.name as vendor',
            'ot_requests.nep_date',
            'ot_requests.duty_start',
            'ot_requests.duty_end',
            'ot_requests.in_time',
            'ot_requests.out_time',
            'ot_requests.total_working_hours',
            'ot_requests.total_ot_hours',
            'ot_requests.type',
            'ot_requests.remarks',
            'ot_requests.state',
            'ot_requests.created_at as created_at',
        );
        return $query;
    }

    public function convertToEnglishDate($nep_date) {
        return LaravelNepaliDate::from($nep_date)->toEnglishDate('Y-m-d');
    }

    #[Computed()]
    public function list()
    {
        return $this->getOtQuery()
            ->orderBy($this->sortBy, $this->sortDirection ?? 'asc')
            ->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::OT_REQUEST);
    }

    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
        if (in_array($attr, ['startDate', 'endDate', 'status'])) {
            $this->validate();
            unset($this->list);
        }
    }

    public function rules()
    {
        return [
            'startDate' => 'required',
            'endDate' => ['required', function ($attribute, $value, $fail) {
                $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
                $endDate = LaravelNepaliDate::from($value)->toEnglishDate();
                if ($startDate > $endDate) {
                    $fail("End date should be greater than start date.");
                }
                $maxDifference = 32;

                $startDate = \Carbon\Carbon::parse($startDate);
                $endDate = \Carbon\Carbon::parse($endDate);
                $totalDays = $endDate->diffInDays($startDate) + 1; // diffInDays gives count 1 day less so 1 day is added.

                if ($totalDays > $maxDifference) {
                    $fail("The difference between start date and end date cannot be greater than {$maxDifference} days.");
                }
            },],
        ];
    }

    public function exportOt($type = null)
    {
        $title = "OT Request List";

        $heading = [
            'Employee Code',
            'Name',
            'Branch',
            'Department',
            'Vendor',
            'English Date',
            'Nepali Date',
            'Duty Start',
            'Duty End',
            'In Time',
            'Out Time',
            'Working Hours',
            'OT Hours',
            'Type',
            'Remarks',
            'State',
        ];

        ini_set('memory_limit', '512M');
        set_time_limit(300);

        $dataToReturn = [];

        if ($type === 'all') {
            $otData = $this->getOtQuery()
                ->whereBetween('ot_requests.nep_date', [$this->startDate, $this->endDate])
                ->get();
        } else if ($type === 'fixed') {
            $otData = $this->list()->items();
        } else {
            return response()->json(['error' => 'Invalid export type'], 400);
        }

        foreach ($otData as $data) {
            $toReturn = [
                $data->emp_code,
                $data->emp_name,
                $data->branch,
                $data->department,
                $data->outsource ?? $data->vendor,
                $this->convertToEnglishDate($data->nep_date),
                $data->nep_date,
                $data->duty_start,
                $data->duty_end,
                $data->in_time,
                $data->out_time,
                $data->total_working_hours,
                $data->total_ot_hours,
                $data->type,
                $data->remarks,
                $data->state,
            ];
            $dataToReturn[] = $toReturn;
        }

        return Excel::download(
            new OtExport($title, $heading, $dataToReturn),
            "$title.xlsx"
        );
    }

    public function render()
    {
        return view('livewire.tickets.ot-request-list');
    }
}
