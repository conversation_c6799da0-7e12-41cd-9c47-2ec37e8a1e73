<?php

namespace App\Livewire\Tickets;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Models\EmployeeTicket\DutyChangeTicket;
use App\Models\TimeRequest;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Duty Change Ticket List')]
class DutyChangeRequestList extends Component
{
    use WithDataTable, WithDefaultFilter;
    public $startDate, $endDate, $status, $selectedId;

    public function mount()
    {
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->startDate = LaravelNepaliDate::from(Carbon::now()->subDay(30))->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }
        if (!scopeAll()) {
            $this->filterCompanyId = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->filterRegionId = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->filterBranchId = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
            $this->filterDepartmentId = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = DutyChangeTicket::query()
            ->leftJoin('shifts as shift', 'shift.id', 'duty_change_tickets.shift_id')
            ->leftJoin('shifts as existing_shift', 'existing_shift.id', 'duty_change_tickets.existing_shift_id');

        $query
            ->leftJoin('employees as emp', 'duty_change_tickets.employee_id', 'emp.id')
            ->leftJoin('companies as company', 'company.id', 'emp.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'duty_change_tickets.employee_id')
            ->leftJoin("branches as branch", "branch.id", "org.branch_id")
            ->leftJoin("departments as department", "department.id", "org.department_id");

        $this->defaultFilterQuery($query, 'emp', 'org');
        filterEmployeesByScope($query, 'org', 'emp');
        Employee::searchEmployee($this->search, $query, 'company', 'org', 'emp');

        $query->when($this->status, fn($query) => $query->where('duty_change_tickets.state', $this->status))
            ->when(
                $this->startDate && $this->endDate,
                fn($query) => $query->whereBetween('duty_change_tickets.date_np', [$this->startDate, $this->endDate])
            );

        $query->select(
            'duty_change_tickets.id',
            Employee::selectNameRawQuery('emp', 'emp_name'),
            Employee::selectEmpCodeRawQuery('company', 'org', 'emp_code'),
            'branch.name as branch',
            'department.name as department',
            'duty_change_tickets.date_np',
            // 'duty_change_tickets.reason',
            'duty_change_tickets.state',
            'shift.name as shift',
            'shift.start_time as shift_start_time',
            'shift.end_time as shift_end_time',
            'existing_shift.name as existing_shift',
            'existing_shift.start_time as existing_shift_start_time',
            'existing_shift.end_time as existing_shift_end_time',
            'duty_change_tickets.created_at as created_at',
        );
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::TIME_REQUEST_APPROVAL);
    }

    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
        if (in_array($attr, ['startDate', 'endDate', 'status'])) {
            unset($this->list);
        }
    }
    public function refreshList(){
        unset($this->list);
    }

    public function render()
    {
        return view('livewire.tickets.duty-change-request-list');
    }
}
