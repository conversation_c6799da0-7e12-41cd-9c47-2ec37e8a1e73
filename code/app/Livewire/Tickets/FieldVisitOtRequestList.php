<?php

namespace App\Livewire\Tickets;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\ExportTable;
use App\Models\Employee\Employee;
use App\Models\FieldVisitTicket;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Field Visit OT Request List')]
class FieldVisitOtRequestList extends Component
{
    use WithDataTable, WithDefaultFilter;
    public $startDate, $endDate, $status, $selectedId = null;

    public function mount()
    {
        $this->startDate = LaravelNepaliDate::from(Carbon::now()->subDays(30))->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
    }

    public function getOtQuery()
    {
        $query = FieldVisitTicket::with('fieldVisit', 'fiscalYear')
            ->leftJoin('employees as emp', 'field_visit_tickets.employee_id', '=', 'emp.id')
            ->leftJoin('companies as company', 'company.id', '=', 'emp.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'field_visit_tickets.employee_id')
            ->leftJoin('field_visits', 'field_visits.id', '=', 'field_visit_tickets.field_visit_id')
            ->whereIn('field_visit_tickets.state', ['Approved', 'Submitted'])
            ->select([
                'field_visit_tickets.id',
                'field_visit_tickets.employee_id',
                'field_visit_tickets.total_hours',
                'field_visit_tickets.state',
                'field_visits.name',
                'field_visits.location',
                'field_visits.type',
                'field_visits.purpose',
                'field_visits.message',
                'field_visits.description',
                'field_visits.check_in_lat',
                'field_visits.check_in_lon',
                'field_visits.check_out_lat',
                'field_visits.check_out_lon',
                'field_visits.check_in_ip',
                'field_visits.check_out_ip',
                'field_visits.check_in_date_np',
                'field_visits.check_out_date_np',
                'field_visits.check_in_time',
                'field_visits.check_out_time',
                Employee::selectNameRawQuery('emp', 'emp_name'),
                Employee::selectEmpCodeRawQuery('company', 'org', 'emp_code'),
            ]);

        $this->defaultFilterQuery($query, 'emp', 'org');
        filterEmployeesByScope($query, 'org', 'emp');
        Employee::searchEmployee($this->search, $query, 'company', 'org', 'emp');

        $query->when($this->status, fn($query) => $query->where('field_visit_tickets.state', $this->status))
            ->when(
                $this->startDate && $this->endDate,
                fn($query) => $query->whereBetween('field_visits.check_in_date_np', [$this->startDate, $this->endDate])
            );

        return $query;
    }

    #[Computed()]
    public function list()
    {
        return $this->applySorting($this->getOtQuery())
            ->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::FIELD_VISIT_OT_TICKET);
    }

    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);
        if (in_array($attr, ['startDate', 'status'])) {
            unset($this->list);
        }
    }

    public function exportFieldVisitOt($type = null)
    {
        ini_set('memory_limit', '512M');
        set_time_limit(300);

        $dataToReturn = [];

        if ($type === 'all') {
            $otData = $this->getOtQuery()
                ->whereBetween('field_visits.check_in_date_np', [$this->startDate, $this->endDate])
                ->get();
        } else if ($type === 'fixed') {
            $otData = $this->list()->items();
        } else {
            return response()->json(['error' => 'Invalid export type'], 400);
        }

        foreach ($otData as $data) {
            $toReturn = [
                'emp_name' => $data->emp_name ,
                'emp_code' => $data->emp_code ,
                'name' => $data->name,
                'type' => $data->type,
                'location' => $data->location,
                'purpose' => $data->purpose,
                'message' => $data->message,
                'description' => $data->description,
                'check_in_date_np' => $data->check_in_date_np,
                'check_out_date_np' => $data->check_out_date_np,
                'check_in_time' => $data->check_in_time,
                'check_out_time' => $data->check_out_time,
                'total_hours' => $data->total_hours,
                'check_in_ip' => $data->check_in_ip,
                'check_out_ip' => $data->check_out_ip,
                'check_in_lat' => $data->check_in_lat,
                'check_in_lon' => $data->check_in_lon,
                'check_out_lat' => $data->check_out_lat,
                'check_out_lon' => $data->check_out_lon,
                'state' => $data->state,
            ];
            $dataToReturn[] = $toReturn;
        }

        return ExportTable::exportDataTable(
            $dataToReturn,
            exportFileName: 'Field Visit OT Request List.xlsx',
            exportIgnoreColumns: ['id'],
            exportColumnHeadersMap: [
                'emp_name' => 'Employee Name',
                'emp_code' => 'Employee Code',
                'name' => 'Name',
                'type' => 'Type',
                'location' => 'Location',
                'purpose' => 'Purpose',
                'message' => 'Message',
                'description' => 'Description',
                'check_in_date_np' => 'Check In Date',
                'check_out_date_np' => 'Check Out Date',
                'check_in_time' => 'Check In Time',
                'check_out_time' => 'Check Out Time',
                'total_hours' => 'Total Hours',
                'check_in_ip' => 'Check In IP',
                'check_out_ip' => 'Check Out IP',
                'check_in_lat' => 'Check In Lat',
                'check_in_lon' => 'Check In Lng',
                'check_out_lat' => 'Check Out Lat',
                'check_out_lon' => 'Check Out Lng',
                'state' => 'State',
            ]
        );
    }
    public function render()
    {
        return view('livewire.tickets.field-visit-ot-request-list');
    }
}
