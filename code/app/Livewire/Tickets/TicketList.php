<?php

namespace App\Livewire\Tickets;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithDateRangeFilter;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class TicketList extends Component
{
    use WithDataTable, WithDateRangeFilter, MultiselectEmployeeSearch;

    public $ticketNo, $status, $workflow, $employee_id = "", $submitted_by = "", $current_owner;

    public function mount()
    {
        $this->sortBy = "created_at";
        $this->sortDirection = 'desc';
        $this->singleSelectAttributes = ['employee_id', 'submitted_by', 'current_owner'];
        // dd($this->ticketCounts);
    }

    #[Computed(persist: true)]
    public function list()
    {
        $results = DB::table('request_tickets as rt')
            ->leftJoin('employees as emp', 'emp.id', '=', 'rt.employee_id')
            ->leftJoin('employees as cur_own', 'cur_own.id', '=', 'rt.current_owner_id')
            ->leftJoin('employees as sub_by', 'sub_by.id', '=', 'rt.submitted_by')
            ->when($this->status, function ($query) {
                $query->where('rt.state', $this->status);
            })
            ->when($this->workflow, function ($query) {
                $query->where('rt.workflow', $this->workflow);
            })
            ->when($this->startDate && $this->endDate, function ($query) {
                $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
                $endDate = LaravelNepaliDate::from($this->endDate)->toEnglishDate();
                $endDate = Carbon::parse($endDate)->endOfDay();
                $query->whereBetween('rt.created_at', [$startDate, $endDate]);
            })
            ->when($this->employee_id, function ($query) {
                $query->where('rt.employee_id', $this->employee_id);
            })
            ->when($this->submitted_by, function ($query) {
                $query->where('rt.submitted_by', $this->submitted_by);
            })
            ->when($this->current_owner, function ($query) {
                $query->where('rt.current_owner_id', $this->current_owner);
            })
            ->when($this->ticketNo, function ($query) {
                $query->where('rt.id', $this->ticketNo);
            })
            ->select(
                'rt.id',
                'rt.state',
                'rt.workflow',
                'rt.model_id',
                'rt.created_at',
                DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as employee"),
                DB::raw("CONCAT_WS(' ', cur_own.first_name, cur_own.middle_name, cur_own.last_name) as current_owner"),
                DB::raw("CONCAT_WS(' ', sub_by.first_name, sub_by.middle_name, sub_by.last_name) as submitted_by"),
            )
            ->when(!scopeAll(), function ($query) {
                $currentEmployee = currentEmployee();
                if ($currentEmployee) {
                    if (scopeCompany()) {
                        $query->where('emp.company_id', $currentEmployee->company_id);
                    }
                }
            })
            ->orderBy($this->sortBy, $this->sortDirection ?? 'asc')
            ->paginate($this->perPage);
        $results->getCollection()->each(function ($item) {
            $item->created_at = LaravelNepaliDate::from($item->created_at)->toNepaliDate('Y-m-d');
        });

        return $results;
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        $data = array_values(WorkflowState::getConstants());
        sort($data);
        return $data;
    }

    #[Computed(persist: true)]
    public function workflowList()
    {
        $data = array_values(WorkflowName::getConstants());
        sort($data);
        return $data;
    }

    #[On('employee_id-changed')]
    public function employeeIdChange($value)
    {
        $this->employee_id = $value;
        $this->refreshList();
    }

    #[On('submitted_by-changed')]
    public function submittedByChanged($value)
    {
        $this->submitted_by = $value;
        $this->refreshList();
    }

    #[On('current_owner-changed')]
    public function currentOwnerChanged($value)
    {
        $this->current_owner = $value;
        $this->refreshList();
    }

    #[On('refresh-list')]
    public function refreshList()
    {
        unset($this->list);
    }

    public function updated($attr)
    {
        if (\in_array($attr, ["status", "ticketNo", "endDate", "startDate", "workflow", "submitted_by", "employee_id", "current_owner"])) {
            if ($this->startDate && $this->endDate) {
                $this->validate();
            }
            unset($this->list);
        }
    }

    public function render()
    {
        return view('livewire.tickets.ticket-list');
    }
}
