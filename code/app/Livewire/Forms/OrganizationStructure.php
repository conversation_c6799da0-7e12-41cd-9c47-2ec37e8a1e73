<?php

namespace App\Livewire\Forms;

use App\Traits\MultiselectEmployeeSearch;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Organization Chart')]
class OrganizationStructure extends Component
{
    use MultiselectEmployeeSearch;

    public $employeeId = null;

    public function mount($employeeId = null)
    {
        $this->employeeId = $employeeId ?? currentEmployee()?->id;
    }


    public function employeeDispatch()
    {
        $this->dispatch('employeeId-Dispatch', $this->employeeId);
    }

    public function updatedEmployeeId($value)
    {
        $this->employeeId = $value;
        $this->employeeDispatch();
    }

    public function render()
    {
        return view('livewire.forms.organization-structure');
    }
}
