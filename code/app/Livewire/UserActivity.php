<?php

namespace App\Livewire;

use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Spatie\Activitylog\Models\Activity;

#[Title('User Activity')]
class UserActivity extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $activityId, $event, $modelType, $employeeId, $subjectId, $subjectType, $timelineId;

    protected $model;

    public function __construct()
    {
        $this->model = new Activity;
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->perPage = 10;
    }

    public function mount()
    {
        $this->multiSelectAttributes = ['employeeId'];
    }

    public function setActivityDescription($activityId)
    {
        $this->activityId = $activityId;
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset([
            'activityId',
            'timelineId',
            'subjectId',
            'subjectType',
        ]);
    }

    #[Computed()]
    public function activityProperties()
    {
        return Cache::remember("activity_properties_{$this->activityId}", 60, function () {
            return $this->model::where('id', $this->activityId)->get('properties');;
        });
    }

    #[Computed()]
    public function timelineProperties()
    {
        return Cache::remember("timeline_properties_{$this->timelineId}", 60, function () {
            return $this->model::where('id', $this->timelineId)->get('properties');;
        });
    }

    public function setTimeline($activityId)
    {
        $activity = $this->model::find($activityId);
        if ($activity) {
            $this->subjectId = $activity->subject_id;
            $this->subjectType = $activity->subject_type;
        }
    }

    public function setTimelineId($id)
    {
        $this->timelineId = $this->timelineId === $id ? null : $id;
    }

    #[Computed()]
    public function getTimelineHistory()
    {
        return $this->model::where('subject_id', $this->subjectId)
            ->where('subject_type', $this->subjectType)
            ->leftJoin('employees', 'employees.user_id', '=', 'causer_id')
            ->select('activity_log.*', 'employees.first_name', 'employees.middle_name', 'employees.last_name', 'employees.id as emp_id')
            ->orderBy('created_at', 'desc');
    }

    #[Computed()]
    public function events()
    {
        return Cache::remember('events', 60, function () {
            return [
                'created' => 'Created',
                'updated' => 'Updated',
                'deleted' => 'Deleted',
            ];
        });
    }

    #[Computed()]
    public function modelTypes()
    {
        return Cache::remember('model_types', 60, function () {
            return Activity::select('subject_type')->distinct()->pluck('subject_type')->toArray();
        });
    }

    #[Computed()]
    public function list()
    {
        $query = $this->applySorting(
            $this->model::where('causer_type', 'App\Models\User')
                ->leftJoin('employees', 'employees.user_id', '=', 'causer_id')
                ->select('activity_log.*', 'employees.first_name', 'employees.middle_name', 'employees.last_name', 'employees.id as emp_id')
        );
        if ($this->employeeId) {
            $query->where('employees.id', $this->employeeId);
        }
        if ($this->event) {
            $query->where('activity_log.event', $this->event);
        }
        if ($this->modelType) {
            $query->where('activity_log.subject_type', $this->modelType);
        }

        if ($this->search) {
            return $query->where('subject_id', 'like', '%' . $this->search . '%')
                ->orWhere(DB::raw("CONCAT(employees.first_name, ' ', employees.middle_name, ' ', employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                ->orWhere(DB::raw("CONCAT(employees.first_name, ' ', employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                ->orWhereRaw('LOWER(properties) LIKE ?', ['%' . strtolower($this->search) . '%'])
                ->paginate($this->perPage);
        }
        return $query->paginate($this->perPage);
    }
}
