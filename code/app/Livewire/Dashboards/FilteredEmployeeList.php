<?php

namespace App\Livewire\Dashboards;

use App\Http\Helpers\Enums\WorkflowState;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use CodeBright\LaravelNepaliDate\Traits\NepaliDateTrait;
use App\Models\configs\Company;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use CodeBright\LaravelNepaliDate\Traits\IsLeapYearTrait;
use Livewire\Component;

#[Title('Filtered Employee List')]
class FilteredEmployeeList extends Component
{
    use WithDataTable, MultiselectEmployeeSearch, NepaliDateTrait;

    // #[Url('type')]
    public $filterType = ""; // newJoin, terminating, terminated;
    public $title = "";

    // filters
    public $companyId, $branchId, $departmentId, $employeeIds, $year, $month, $day;

    public function mount($type)
    {
        $this->filterType = $type;
        if (!in_array($type, ['newJoin', 'terminated', 'terminating'])) {
            abort(403, "Invalid type " . $type);
        }
        if (!scopeAll()) $this->companyId = currentEmployee()?->company_id;
        switch ($type) {
            case 'newJoin':
                $this->title = "New Joined";
                break;
            case 'terminating':
                $this->title = 'Terminating';
                break;
            case 'terminated':
                $this->title = 'Terminated';
                break;
        }
        $this->multiSelectAttributes = ['employeeIds'];
        $this->withTrashed = true;

    }

    #[Computed()]
    public function isNewJoin()
    {
        return $this->filterType === 'newJoin';
    }

    #[Computed()]
    public function isTerminating()
    {
        return $this->filterType === 'terminating';
    }

    #[Computed()]
    public function isTerminated()
    {
        return $this->filterType === 'terminated';
    }
    public function getNepaliMonthDateRange()
    {
        $today = now()->format('Y-m-d'); // Current date
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate(); // Convert to Nepali date
        $nepaliDateParts = explode('-', $nepaliDate);

        // Calculate start and end dates of the Nepali month
        $daysInMonth = LaravelNepaliDate::daysInMonth($nepaliDateParts[1], $nepaliDateParts[0]);
        $startOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], 1);
        $endOfMonth = new LaravelNepaliDate($nepaliDateParts[0], $nepaliDateParts[1], $daysInMonth);

        return [
            'today' =>$today,
            'start' => $startOfMonth->toEnglishDate(),
            'end' => $endOfMonth->toEnglishDate(),
        ];
    }

    public function listQuery()
    {
        $selectArray = [
            'employees.id',
            Employee::selectNameRawQuery(),
            DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
            'dept.name as department',
            'branch.name as branch',
        ];

        $query = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
            ->leftJoin('companies as comp', 'comp.id', 'employees.company_id')
            ->leftJoin('branches as branch', 'branch.id', 'org.branch_id')
            ->leftJoin('departments as dept', 'dept.id', 'org.department_id')
            ->when($this->departmentId, function ($query) {
                $query->where('org.department_id', $this->departmentId);
            })
            ->when($this->branchId, function ($query) {
                $query->where('org.branch_id', operator: $this->branchId);
            })
            ->when($this->companyId, function ($query) {
                $query->where('employees.company_id', $this->companyId);
            })
            ->when($this->employeeIds, function ($query) {
                $query->whereIn('employees.id', $this->employeeIds);
            });
            $dateRange = $this->getNepaliMonthDateRange();


        switch ($this->filterType) {
            case 'newJoin':
                $dateRange = $this->getNepaliMonthDateRange();
                $query = $query->whereBetween('org.doj',  [$dateRange['start'], $dateRange['today']])->orderBy('org.doj', 'desc');
                array_push($selectArray, 'org.doj');

                $count = $query->count();
                $list = $query->get();

                break;

            case 'terminating':
                $query = $query->leftJoin('employee_terminations as termination', 'termination.employee_id', 'employees.id')
                    ->where('termination.state', WorkflowState::APPROVED)
                    ->whereNull('termination.deleted_at')
                    ->whereBetween('termination.termination_date', [$dateRange['today'], $dateRange['end']]);

                if (fedexHrm())
                    array_push($selectArray, 'termination.termination_request_date', 'termination.termination_date');

                else
                    array_push(
                        $selectArray,
                        'termination.termination_request_date as termination_request_date',
                        'termination.termination_date as termination_date'

                    );

                break;

            case 'terminated':
                $query = $query->onlyTrashed()
                    ->whereBetween('org.termination_date', [$dateRange['start'], $dateRange['today']]);
                array_push($selectArray, 'org.termination_date', 'org.termination_reason', 'org.termination_request_date');
                break;
        }

        $query = filterEmployeesByScope($query);
        $query->select($selectArray);
        return $query;
    }

    #[Computed]
    public function list()
    {
        $query = $this->listQuery();
        return $this->applySorting($query)->paginate($this->perPage);
    }

    public function setExcelOptions()
    {
        $this->exportFileName = "{$this->title} Employee List.xlsx";
        $this->exportIgnoreColumns = ['id'];
        $this->exportColumnHeadersMap = [
            'full_name' => 'Employee Name',
            'emp_code' => 'Employee Code',
        ];
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return \App\Models\configs\Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return \App\Models\configs\Branch::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return \App\Models\configs\Department::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function updatedCompanyId()
    {
        unset($this->branchList, $this->departmentList);
    }

    public function render()
    {
        return view('livewire.dashboards.filtered-employee-list');
    }
}
