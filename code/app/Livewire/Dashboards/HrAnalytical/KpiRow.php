<?php

namespace App\Livewire\Dashboards\HrAnalytical;

use Livewire\Attributes\On;
use Livewire\Component;

class KpiRow extends Component
{
    public ?int $branchId = null;
    public ?int $departmentId = null;
    public ?int $companyId = null;

    public array $kpis = [];

    public function mount(?int $branchId = null, ?int $departmentId = null, ?int $companyId = null): void
    {
        $this->branchId = $branchId;
        $this->departmentId = $departmentId;
        $this->companyId = $companyId;

        $this->loadKpis();
    }

    #[On('hr-analytics-filters:changed')]
    public function onFiltersChanged($branchId = null, $departmentId = null, $companyId = null): void
    {
        $this->branchId = $branchId ?: null;
        $this->departmentId = $departmentId ?: null;
        $this->companyId = $companyId ?: null;

        $this->loadKpis();
    }

    private function loadKpis(): void
    {
        $svc = app(\App\Http\Services\Dashboard\HrAnalyticsKPIService::class);

        $active = $svc->averageActiveWorkforceKPI(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $abs = $svc->absenteeismRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $turn = $svc->turnoverRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $present = $svc->presentRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $absent = $svc->absentRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $lateIn = $svc->lateInRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $earlyOut = $svc->earlyOutRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $punc = $svc->punctualityRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $hire = $svc->hiringRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $term = $svc->terminationRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);
        $leave = $svc->leaveRateKpi(branchId: $this->branchId, departmentId: $this->departmentId, companyId: $this->companyId);

        $this->kpis = [
            'active_workforce' => [
                'label' => 'Avg. Active Workforce',
                'value' => $active['value'],
                'delta' => $active['label_pp'],
                'type' => $active['type'],
                'icon' => 'bi bi-people-fill',
                'popupId' => '#activeWorkforceTrendModal'
            ],
            'absenteeism_rate' => [
                'label' => 'Absenteeism Rate',
                'value' => $abs['value'],
                'delta' => $abs['label'],
                'type' => $abs['type'],
                'icon' => 'bi bi-exclamation-triangle-fill',
                'popupId' => '#absentTrendModal'
            ],
            'turnover_rate' => [
                'label' => 'Turnover Rate',
                'value' => $turn['value'],
                'delta' => $turn['label'],
                'type' => $turn['type'],
                'icon' => 'bi bi-arrow-left-right',
                'popupId' => '#turnOverTrendModal'
            ],
            // 'offer_acceptance' => [
            //     'label' => 'Offer Acceptance',
            //     'value' => '--%',
            //     'delta' => null,
            //     'icon' => 'bi bi-check2-circle'
            // ],
            'avg_present' => [
                'label' => 'Avg. Present',
                'value' => $present['value'],
                'delta' => $present['label_pp'],
                'type' => $present['type'],
                'icon' => 'bi bi-person-check-fill',
                'popupId' => '#presentTrendModal'
            ],
            'avg_absent' => [
                'label' => 'Avg. Absent',
                'value' => $absent['value'],
                'delta' => $absent['label_pp'],
                'type' => $absent['type'],
                'icon' => 'bi bi-person-x-fill',
                'popupId' => '#absentTrendModal'
            ],
            'avg_late_in' => [
                'label' => 'Avg. Late In',
                'value' => $lateIn['value'],
                'delta' => $lateIn['label_pp'],
                'type' => $lateIn['type'],
                'icon' => 'bi bi-clock-history',
                'popupId' => '#lateInTrendModal'
            ],
            'avg_early_out' => [
                'label' => 'Avg. Early Out',
                'value' => $earlyOut['value'],
                'delta' => $earlyOut['label_pp'],
                'type' => $earlyOut['type'],
                'icon' => 'bi bi-door-open-fill',
                'popupId' => '#earlyOutTrendModal'
            ],
            'punctuality_index' => [
                'label' => 'Avg. Punctual',
                'value' => $punc['value'],
                'delta' => $punc['label_pp'],
                'type' => $punc['type'],
                'icon' => 'bi bi-stopwatch',
                'popupId' => '#punctualTrendModal'
            ],
            'avg_leave' => [
                'label' => 'Avg. Leave',
                'value' => $leave['value'],
                'delta' => $leave['label_pp'],
                'type' => $leave['type'],
                'icon' => 'bi bi-calendar-event-fill',
                'popupId' => '#leaveTrendModal'
            ],
            'avg_hiring' => [
                'label' => 'Avg. Hiring',
                'value' => $hire['value'],
                'delta' => $hire['label_pp'],
                'type' => $hire['type'],
                'icon' => 'bi bi-person-plus-fill',
                'popupId' => '#hiringTrendModal'
            ],
            'avg_termination' => [
                'label' => 'Avg. Termination',
                'value' => $term['value'],
                'delta' => $term['label_pp'],
                'type' => $term['type'],
                'icon' => 'bi bi-person-dash-fill',
                'popupId' => '#terminationTrendModal'
            ],
        ];
    }

    public function render()
    {
        return view('livewire.dashboards.hr-analytical.kpi-row');
    }
}
