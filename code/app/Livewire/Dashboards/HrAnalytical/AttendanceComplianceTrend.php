<?php

namespace App\Livewire\Dashboards\HrAnalytical;

use App\Http\Services\Dashboard\HrAnalyticsService;
use Livewire\Attributes\On;
use Livewire\Component;

class AttendanceComplianceTrend extends Component
{
    public ?int $branchId = null;
    public ?int $departmentId = null;
    public ?int $companyId = null;
    
    public array $chartPayload = [
        'categories' => [],
        'dates' => [],
        'dividerIndex' => 7,
        'series' => [],
    ];

    protected $listeners = ['filters:changed' => 'onFiltersChanged'];

    public function mount(HrAnalyticsService $svc, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null)
    {
        $this->branchId = $branchId;
        $this->departmentId = $departmentId;
        $this->companyId = $companyId;
        $this->reload($svc);
    }

    #[On('hr-analytics-filters:changed')]
    public function onFiltersChanged($branchId = null, $departmentId = null, $companyId = null): void
    {
        $this->branchId = $branchId ?: null;
        $this->departmentId = $departmentId ?: null;
        $this->companyId = $companyId ?: null;
        $this->reload(app(HrAnalyticsService::class));
        $this->dispatch('chart-attendance-updated', payload: $this->chartPayload);
    }

    private function reload(HrAnalyticsService $svc): void
    {
        $this->chartPayload = $svc->twoWeekAttendanceCompliance($this->branchId, $this->departmentId, $this->companyId);
    }

    public function render()
    {
        return view('livewire.dashboards.hr-analytical.attendance-compliance-trend');
    }
}
