<?php
namespace App\Livewire\Dashboards\HrAnalytical;

use App\Http\Services\Dashboard\HrAnalyticsService;
use Livewire\Attributes\On;
use Livewire\Component;

class ActiveWorkforceTrend extends Component
{
    public ?int $departmentId = null;
    public ?int $branchId = null;
    public ?int $companyId = null;

    public array $chartPayload = [
        'categories' => [],
        'series' => [],
        'dividerIndex' => 7,
        'dates' => [],
    ];

    public function mount(HrAnalyticsService $svc, ?int $branchId = null, ?int $departmentId = null, ?int $companyId = null)
    {
        $this->branchId = $branchId;
        $this->departmentId = $departmentId;
        $this->companyId = $companyId;
        $this->reload($svc);
    }

    #[On('hr-analytics-filters:changed')]
    public function onFiltersChanged($branchId = null, $departmentId = null, $companyId = null)
    {
        $this->branchId = $branchId ?: null;
        $this->departmentId = $departmentId ?: null;
        $this->companyId = $companyId ?: null;
        $this->reload(app(HrAnalyticsService::class));
        $this->dispatch('chart-active-workforce-updated', payload: $this->chartPayload);
    }

    private function reload(HrAnalyticsService $svc): void
    {
        $this->chartPayload = $svc->twoWeekActiveTrend($this->branchId, $this->departmentId, $this->companyId);
    }

    public function render()
    {
        return view('livewire.dashboards.hr-analytical.active-workforce-trend');
    }
}
