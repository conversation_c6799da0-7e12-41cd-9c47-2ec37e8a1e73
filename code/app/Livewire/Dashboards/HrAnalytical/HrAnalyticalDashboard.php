<?php

namespace App\Livewire\Dashboards\HrAnalytical;

use App\Http\Services\Dashboard\HrAnalyticsCache;
use App\Http\Services\Dashboard\HrAnalyticsService;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\DailyEmployeeCount;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Carbon\CarbonPeriod;
use Carbon\Carbon;
use App\Models\Employee\Employee;

use App\Models\configs\Company;
use PermissionList;

class HrAnalyticalDashboard extends Component
{
    use WithNotify;

    public array $companyList = [];
    public array $branchList = [];
    public array $departmentList = [];

    public $filterCompanyId, $filterBranchId, $filterDepartmentId;

    public function mount()
    {
        if (!auth()->user()->can(PermissionList::HR_ANALYTICS_VIEW_ALL)) {
            $this->filterCompanyId = currentEmployee()?->company_id;
        } else {
            $this->setCompanyList();
        }
        $this->setBranchAndDepartmentList();
    }

    public function setCompanyList()
    {
        $this->companyList = Company::pluck('name', 'id')->toArray();
    }

    public function setBranchAndDepartmentList()
    {
        $this->branchList = Branch::when($this->filterCompanyId, fn($q) => $q->where('company_id', $this->filterCompanyId))->pluck('name', 'id')->toArray();
        $this->departmentList = Department::when($this->filterCompanyId, fn($q) => $q->where('company_id', $this->filterCompanyId))->pluck('name', 'id')->toArray();
    }

    public function updated($attr)
    {
        if ($attr == 'filterCompanyId') {
            $this->setBranchAndDepartmentList();
        }

        if (in_array($attr, ['filterCompanyId', 'filterBranchId', 'filterDepartmentId'])) {
            $this->dispatch('hr-analytics-filters:changed', branchId: $this->filterBranchId, departmentId: $this->filterDepartmentId, companyId: $this->filterCompanyId);
        }
    }

    public function clearCache()
    {
        HrAnalyticsCache::flushCache();
        $this->dispatch('hr-analytics-filters:changed', branchId: $this->filterBranchId, departmentId: $this->filterDepartmentId, companyId: $this->filterCompanyId);
        $this->notify("Cache cleared successfully.")->type('success')->send();
    }

    public function render()
    {
        return view('livewire.dashboards.hr-analytical.hr-analytical-dashboard');
    }
}
