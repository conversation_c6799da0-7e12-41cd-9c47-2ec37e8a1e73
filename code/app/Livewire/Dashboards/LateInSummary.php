<?php

namespace App\Livewire\Dashboards;

use App\Models\configs\LateInCategory;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Component;

class LateInSummary extends Component
{
    public $employees = [];
    public $lateInCategory = "Late In Category";

    #[Computed(persist: true)]
    public function lateInCategories()
    {
        return LateInCategory::where('is_active', true)->get();
    }

    #[Computed(persist: true)]
    public function lateInSummary()
    {
        $result = [];
        foreach ($this->lateInCategories as $lateInCategory) {
            // dd($lateInCategory);
            $lateAttendance = DB::table('attendance as att')
                ->leftJoin('employees as emp', 'emp.id', 'att.employee_id')
                ->leftJoin('employee_org as org', 'org.employee_id', 'emp.id')
                ->where('att.date_en', Carbon::now()->format('Y-m-d'))
                ->where('org.department_id', currentEmployee()?->organizationInfo?->department_id)
                ->where('att.in_remarks', 'like', "%$lateInCategory->name%")
                ->select(
                    'emp.id as employee_id',
                    DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as employee_name"),
                    'emp.profile_picture',
                    'emp.gender'
                )
                ->get();
            if (count($lateAttendance))
                $result[$lateInCategory->name] = [
                    'count' => count($lateAttendance),
                    'employees' => $lateAttendance->map(fn ($att) => ([
                        'id' => $att->employee_id,
                        'name' => $att->employee_name,
                        'gender' => $att->gender,
                        'profile_picture' => $att->profile_picture,
                    ]))->toArray(),
                ];
        }
        return $result;
    }

    public function setLateInCategory($lateInCategory)
    {
        $this->lateInCategory = $lateInCategory;
        $this->employees = $this->lateInSummary[$this->lateInCategory]['employees'];
    }

    public function render()
    {
        return view('livewire.dashboards.late-in-summary');
    }
}
