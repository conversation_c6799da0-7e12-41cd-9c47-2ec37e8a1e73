<?php

namespace App\Livewire\Dashboards\Hradmin;

use App\Http\Repositories\TicketRepository;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class MyTicketsChart extends Component
{
    public $icons = [];

    public function mount() {
        $this->icons = config('arflow-config.icons') ?? [];
    }

    #[Computed(persist: true)]
    public function myTickets()
    {
        $repo = new TicketRepository();
        return $repo->getMyTickets(currentEmployee()?->id);
    }

    #[On('request-saved')]
    public function resetMyTickets()
    {
        unset($this->myTickets);
    }
    
    public function render()
    {
        return view('livewire.dashboards.hradmin.my-tickets-chart');
    }
}
