<?php

namespace App\Livewire\Dashboards\Hradmin;

use Livewire\Attributes\Computed;
use Livewire\Component;

class ContractChart extends Component
{
    public $contractData = [];

    public function mount($contractDetail)
    {
        $this->contractData = [
            'labels' => ['Active', 'Expiring', 'Expired'],
            'series' => [
                $contractDetail['active'],
                $contractDetail['expiring'],
                $contractDetail['expired'],
            ],
            'links' => [
                'active',
                'expiring',
                'expired'
            ]
        ];
        $this->hasContractDetails();
    }

    #[Computed]
    public function hasContractDetails()
    {
        return (array_sum($this->contractData['series']) > 0);
    }

    #[\Livewire\Attributes\On('updatePayslipChartData')]
    public function updateContractChartData($payslipDetail)
    {
        $this->contractData = [
            'labels' => ['Active', 'Expiring', 'Expired'],
            'series' => [
                $payslipDetail['active'],
                $payslipDetail['expiring'],
                $payslipDetail['expired'],
            ],
             'links' => [
                'active',
                'expiring',
                'expired'
            ]
        ];
        $this->dispatch('updateContractChart', $this->contractData);
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.contract-chart');
    }
}
