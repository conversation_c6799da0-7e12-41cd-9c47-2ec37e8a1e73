<?php

namespace App\Livewire\Dashboards\Hradmin;

use App\Http\Repositories\TicketRepository;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class TicketsChart extends Component
{
    public $icons = [], $pendingTickets;
    public $chartData = [];

    public function mount() {
        $this->icons = config('arflow-config.icons') ?? [];
        $this->pendingRequest();
        $this->loadChart();
    }

    public function loadChart() {
        $tickets = $this->pendingTickets;

        $this->chartData = [
            'labels' => [],
            'series' => [],
            'icons' => [],
            'tickets' => [],
        ];

        foreach ($tickets as $ticket) {
            $label = implode(' ', explode('_', $ticket->workflow));
            $this->chartData['labels'][] = ucfirst($label);
            $this->chartData['series'][] = $ticket->count;
            $this->chartData['icons'][] = $this->icons[$ticket->workflow] ?? '';

            $this->chartData['tickets'][] = [
                'workflow' => $ticket->workflow,
                'count' => $ticket->count,
                'label' => ucfirst($label)
            ];
        }
    }

    #[Computed(persist: true)]
    public function pendingRequest()
    {
        $ticketRepo = new TicketRepository;
        $this->pendingTickets = $ticketRepo->getPendingTicket()->sortByDesc('count')->values();
        return $this->pendingTickets;
    }

    #[Computed]
    public function hasAnyTodo()
    {
        return count($this->pendingRequest);
    }

    #[On('request-saved')]
    public function resetMyTickets()
    {
        unset($this->pendingRequest);
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.tickets-chart');
    }
}
