<?php

namespace App\Livewire\Dashboards\Hradmin;

use Livewire\Attributes\Computed;
use Livewire\Component;

class Demographic<PERSON><PERSON> extends Component
{
   public $genderMarital = [];

    public function mount($genderMaritalStatusCount)
    {
        $this->genderMarital = $genderMaritalStatusCount;
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.demographic-chart');
    }
}
