<?php

namespace App\Livewire\Dashboards\Hradmin;

use App\Http\Repositories\DashboardRepository;
use App\Models\configs\Department;
use App\Models\configs\Job;
use Livewire\Attributes\Computed;
use Livewire\Component;
use <PERSON><PERSON><PERSON><PERSON>n\CodeCoverage\Report\Html\Dashboard;

class DepartmentJobChart extends Component
{

    public $selectedDepartmentId = null;
    public $selectedJobId = null;
    public $chartData = [];

    protected $listeners = ['departmentChanged' => 'updateChart'];

    public function mount(DashboardRepository $repo)
    {
        $this->selectedDepartmentId = currentEmployee()?->organizationInfo?->department_id ?? array_key_first($this->departments()); 
        $this->loadChart($repo);
    }

    public function updatedSelectedDepartmentId()
    {
        $this->loadChart(app(DashboardRepository::class));

    }
    public function updatedJobId()
    {
        $this->loadChart(app(DashboardRepository::class));
    }

    #[Computed(true)]
    public function departments()
    {
        return Department::all()->pluck('name', 'id')->toArray();
    }

    public function loadChart(DashboardRepository $repo)
    {
        $counts = $repo->getFilteredEmployeeCountsInJob($this->selectedDepartmentId, $this->selectedJobId);
        $this->chartData = $this->formatChartData($counts);
        $this->dispatch('chart-updated', chartData: $this->chartData);
    }

    public function formatChartData(array $counts): array
    {
        if (!count($counts ?? [])) return [];

        $departmentJobs = reset($counts); // get the first (and only) department's jobs
           $jobs = Job::whereIn('name', array_keys($departmentJobs))
               ->pluck('id', 'name');

        return [
            'categories' => array_keys($departmentJobs), // Job names on X-axis
            'jobIds' => $jobs,
            'series' => [
                [
                    'name' => 'Employees',
                    'data' => array_values($departmentJobs) // Employee counts on Y-axis
                ]
            ],
        ];
    }


    public function render()
    {
        return view('livewire.dashboards.hradmin.department-job-chart');
    }
}
