<?php

namespace App\Livewire\Dashboards\Hradmin;

use Livewire\Component;
use Illuminate\Support\Str;
use App\Models\EmployeeCategory;

class EmployeeTypeAttendanceChart extends Component
{
    public $attendanceData = [];
    public $attendanceDataLinks = [];
    public $rowTotals = [];
    public $rowTotalLinks = [];
    public $columnTotals = [];
    public $columnTotalLinks = [];
    public $grandTotal = 0;
    public $grandTotalLink = null;

    public $employeeTypes = [];
    public $statuses = ['Present', 'Absent', 'Shift Not Started', 'Day Off', 'On Leave'];

    public function mount($attendanceDataCount)
    {
        $this->employeeTypes = EmployeeCategory::isCountable()->pluck('type')->toArray();

        $this->attendanceData = [];
        $this->attendanceDataLinks = [];

        foreach ($attendanceDataCount as $item) {
            $status = $item['label'] ?? null;
            if (!$status) continue;

            $types = $item['category'] ?? [];
            $baseLink = $item['link'] ?? null;

            foreach ($this->employeeTypes as $type) {
                $this->attendanceData[$type][$status] = $types[$type] ?? 0;

                // Build link for each cell
                if ($baseLink) {
                    $this->attendanceDataLinks[$type][$status] = $this->buildLink($baseLink, [
                        'category' => Str::slug($type),
                        'status' => $status
                    ]);
                }
            }
        }

        // Row totals (counts)
        $this->rowTotals = [];
        $this->rowTotalLinks = [];
        foreach ($this->employeeTypes as $type) {
            $this->rowTotals[$type] = array_sum($this->attendanceData[$type] ?? []);

            // Build link for row total
            if (!empty($attendanceDataCount)) {
                $firstItem = $attendanceDataCount[0];
                $baseLink = $firstItem['link'] ?? null;

                if ($baseLink) {
                    $urlParts = parse_url($baseLink);
                    $existingParams = [];
                    if (isset($urlParts['query'])) {
                        parse_str($urlParts['query'], $existingParams);
                    }

                    unset($existingParams['status']);
                    $this->rowTotalLinks[$type] = $this->buildLink($baseLink, [
                        'category' => Str::slug($type),
                    ], ['status']);
                }
            }
        }

        // Column totals (counts)
        $this->columnTotals = [];
        $this->columnTotalLinks = [];
        foreach ($this->statuses as $status) {
            $this->columnTotals[$status] = 0;

            foreach ($this->employeeTypes as $type) {
                $this->columnTotals[$status] += $this->attendanceData[$type][$status] ?? 0;
            }

            // Build link for column total
            if (!empty($attendanceDataCount)) {
                $firstItem = $attendanceDataCount[0];
                $baseLink = $firstItem['link'] ?? null;
                if ($baseLink) {
                    $urlParts = parse_url($baseLink);
                    $existingParams = [];
                    if (isset($urlParts['query'])) {
                        parse_str($urlParts['query'], $existingParams);
                    }
                    unset($existingParams['category']);
                    $this->columnTotalLinks[$status] = $this->buildLink($baseLink, [
                        'status' => $status,
                    ], ['category']);
                }
            }
        }

        $this->grandTotal = array_sum($this->columnTotals);

        if (!empty($attendanceDataCount)) {
            $firstItem = $attendanceDataCount[0];
            $baseLink = $firstItem['link'] ?? null;
            if ($baseLink) {
                $urlParts = parse_url($baseLink);
                $existingParams = [];
                if (isset($urlParts['query'])) {
                    parse_str($urlParts['query'], $existingParams);
                }
                unset($existingParams['category'], $existingParams['status']);
                $this->grandTotalLink = $this->buildLink($baseLink, [], ['category', 'status']);
            }
        }
    }

    private function buildLink($baseUrl, $params = [], $removeParams = [])
    {
        if (!$baseUrl) {
            $baseUrl = request()->fullUrl();
        }

        $urlParts = parse_url($baseUrl);
        $existingParams = [];

        if (isset($urlParts['query'])) {
            parse_str($urlParts['query'], $existingParams);
        }

        // Remove specified parameters
        foreach ($removeParams as $param) {
            unset($existingParams[$param]);
        }

        // Merge new parameters with existing ones
        $allParams = array_merge($existingParams, $params);

        // Rebuild URL
        $scheme = $urlParts['scheme'] ?? request()->getScheme();
        $host = $urlParts['host'] ?? request()->getHost();
        $path = $urlParts['path'] ?? '/';
        $port = isset($urlParts['port']) ? ':' . $urlParts['port'] : '';

        $queryString = http_build_query($allParams);

        // Reconstruct full URL
        $fullUrl = $scheme . '://' . $host . $port . $path;

        // Append query string only if it's not empty
        if ($queryString) {
            $fullUrl .= '?' . $queryString;
        }
        return $fullUrl;
    }
    public function render()
    {
        return view('livewire.dashboards.hradmin.employee-type-attendance-chart');
    }
}
