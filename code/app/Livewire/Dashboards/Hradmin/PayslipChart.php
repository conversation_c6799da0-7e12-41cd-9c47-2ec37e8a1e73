<?php

namespace App\Livewire\Dashboards\Hradmin;

use Livewire\Attributes\Computed;
use Livewire\Component;

class Payslip<PERSON>hart extends Component
{
    public $payslipData, $payslipProgress, $totalEmployees;

    public function mount($payslipDetail, $totalActiveEmployees)
    {
        $this->prepareData($payslipDetail, $totalActiveEmployees);
    }

    public function prepareData($payslipDetail, $totalEmployees)
    {
        $this->totalEmployees = $totalEmployees;
        if ($totalEmployees == 0)
            return;

        $this->payslipData = [
            'labels' => ['Active', 'No Payslip', 'Pending Request'],
            'series' => [
                $payslipDetail['active'],
                $payslipDetail['no_payslip'],
                $payslipDetail['pending_request'],
            ]
        ];

        $this->payslipProgress = [
            [
                'label' => 'Active',
                'value' => $payslipDetail['active'],
                'percent' => $this->calculatePercentage($payslipDetail['active'], $totalEmployees),
                'color' => '#28a745',
                'link' => route('payslipList') . '?active=' . true,
            ],
            [
                'label' => 'Not Created',
                'value' => $payslipDetail['no_payslip'],
                'percent' => $this->calculatePercentage($payslipDetail['no_payslip'], $totalEmployees),
                'color' => '#e14b3e',
                'link' => route('noPayslipList')
            ],
            [
                'label' => 'On Approval',
                'value' => $payslipDetail['pending_request'],
                'percent' => $this->calculatePercentage($payslipDetail['pending_request'], $totalEmployees),
                'color' => '#ffc107',
                'link' => route('payslipList') . '?pending=' . true,
            ],
        ];
    }

    #[Computed]
    public function hasPayslipData()
    {
        return ($this->totalEmployees > 0);
    }

    public function calculatePercentage(int $count, int $total)
    {
        if ($total == 0)
            return '0.00';
        $percentage = ($count / $total) * 100;
        return number_format($percentage, 2);
    }

    #[\Livewire\Attributes\On('updatePayslipChartData')]
    public function updatePayslipChartData($payslipDetail, $totalActiveEmployees)
    {
        $this->prepareData($payslipDetail, $totalActiveEmployees);
        $this->dispatch('updatePayslipChart', $this->payslipData);
    }


    public function render()
    {
        return view('livewire.dashboards.hradmin.payslip-chart');
    }
}
