<?php

namespace App\Livewire\Dashboards\Hradmin;

use App;
use App\Facades\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Repositories\DashboardRepository;
use App\Models\configs\Branch;
use App\Models\configs\Region;
use App\Models\EmployeeCategory;
use App\Traits\WithNotify;
use Cache;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;

class HrDashboard extends Component
{
    use WithNotify;

    private DashboardRepository $dashboardRepo;

    public $empDetails, $attDetails, $empTypeDetails, $inhouseAndOutsourceAttDetails;

    public $genderMaritalStatusCount, $ageGroupCount, $payslipDetail, $tardinessDetail, $leaveDetail;

    public $filterRegionId = null;
    public $filterBranchId = null;
    public $filterSelectedDate = null;

    public $activeEmployeeCount = 0;

    public bool $showFilter = false;

    public function __construct()
    {
        $this->dashboardRepo = new DashboardRepository();
    }

    public function mount()
    {
        $this->getEmployeeData($this->filterRegionId, $this->filterBranchId);
        $this->getAttendanceData($this->filterRegionId, $this->filterBranchId);
        $this->getEmployeeTypeData($this->filterRegionId, $this->filterBranchId);
        $this->getDemographicData($this->filterRegionId, $this->filterBranchId);
        $this->getPayslipData($this->filterRegionId, $this->filterBranchId);
        $this->getTardinessData($this->filterRegionId, $this->filterBranchId);
    }

    public function toggleFilter()
    {
        $this->showFilter = !$this->showFilter;
    }

    public function scopeWiseFilters()
    {
        if (!scopeAll()) {
            if (scopeCompany())
                return;

            $this->filterRegionId = currentEmployee()?->organizationInfo?->region_id;
            if (scopeRegion())
                return;

            $this->filterBranchId = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
        }
    }

    #[Computed(persist: true)]
    public function regionList()
    {
        $query = Region::query();

        $companyId = currentEmployee()?->company_id;

        if (!scopeAll()) {
            $query->where('company_id', $companyId);

            if (!scopeCompany()) {
                $regionId = currentEmployee()?->organizationInfo?->region_id;
                $query->where('id', $regionId);
            }
        } else {
            $query->where('company_id', $companyId);
        }

        return $query->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        $query = Branch::query();

        $companyId = currentEmployee()?->company_id;

        if (!scopeAll()) {
            $query->where('company_id', $companyId);

            if (!scopeCompany()) {
                $branchId = currentEmployee()?->organizationInfo?->branch_id;
                $query->where('id', $branchId);
            }
        } else {
            $query->where('company_id', $companyId);
        }

        // Add filter conditions
        if ($this->filterRegionId) {
            $query->where('id', $this->filterRegionId);
        }

        return $query->orderBy('name')->pluck('name', 'id')->toArray();
    }


    public function updated($value)
    {
            // Refresh branch list when region is changed
            if ($value === 'filterRegionId') {
                $this->reset('filterBranchId');
                unset($this->branchList);
                $this->branchList();
            }

            if (in_array($value, ['filterRegionId', 'filterBranchId', 'filterSelectedDate'])) {
                $this->filterRegionId = $this->filterRegionId ?: null;
                $this->filterBranchId = $this->filterBranchId ?: null;

                // Update the data and charts
                $this->getEmployeeData($this->filterRegionId, $this->filterBranchId, $this->filterSelectedDate);
                $this->getAttendanceData($this->filterRegionId, $this->filterBranchId, $this->filterSelectedDate);
                $this->getEmployeeTypeData($this->filterRegionId, $this->filterBranchId);
                $this->getDemographicData($this->filterRegionId, $this->filterBranchId);
                $this->getPayslipData(filterRegionId: $this->filterRegionId, filterBranchId: $this->filterBranchId);
                $this->getTardinessData($this->filterRegionId, $this->filterBranchId);
            }   
        
    }

    // New method to get branch list for a given region
    public function getBranchListForRegion($regionId)
    {
        $query = Branch::query();

        if ($regionId) {
            $query->where('region_id', $regionId);
        }

        return $query->orderBy('name')->pluck('name', 'id')->toArray();
    }

    public function getEmployeeData(int $filterRegionId = null, int $filterBranchId = null , $filterSelectedDate = null)
    {
        $empDetail = $this->dashboardRepo->getEmployeeDetails($filterRegionId, $filterBranchId, $filterSelectedDate);
        $employeeTypes = $this->getEmployeeCategoryForChart();
        $dynamicFields = [];
        foreach ($employeeTypes as $type) {
            $dynamicFields[] = [
                'label' => $type,
                'subField' => true,
                'count' => $empDetail[$type] ?? 0,
                'link' => route('employeelist', ['category' => $type]),
            ];
        }        
        $this->empDetails = [
            [
                'label' => 'Employees',
                'subField' => false,
                'count' => $empDetail['total_employees'],
                'link' => route('employeelist'),
            ],
            [
                'label' => 'New Joins',
                'subField' => false,
                'count' => $empDetail['new_employees'],
                'link' => route('filteredEmployeeList', ['type' => 'newJoin']),
            ],
            [
                'label' => 'On Notice',
                'subField' => false,
                'count' => $empDetail['terminating'],
                'link' => route('filteredEmployeeList', ['type' => 'terminating']),
            ],
            [
                'label' => 'Terminated',
                'subField' => false,
                'count' => $empDetail['terminated'],
                'link' => route('filteredEmployeeList', ['type' => 'terminated']),
            ],
            ...$dynamicFields
        ];        
        $this->activeEmployeeCount = $empDetail['total_employees'];
    }

    public function getEmployeeCategoryForChart()
    {
        return EmployeeCategory::where('is_countable', true)->pluck('type', 'id')->toArray();
    }

    public int $employeeTypeAttendance = 0;
    public function getAttendanceData(int $filterRegionId = null, int $filterBranchId = null, $filterSelectedDate = null)
    {
        $attDetail = $this->dashboardRepo->getAttendanceDetails($filterRegionId, $filterBranchId, $filterSelectedDate);
        $this->leaveDetail = $attDetail['on_leave_details'];
        $this->employeeTypeAttendance += 1;
        $this->attDetails = [
            [
                'label' => 'Present',
                'count' => $attDetail['present']['total'],
                'category' => $attDetail['present']['types'],
                'link' => route('attendanceDashboard', ['status' => 'Present']),
            ],
            [
                'label' => 'Absent',
                'count' => $attDetail['absent']['total'],
                'category' => $attDetail['absent']['types'],
                'link' => route('attendanceDashboard', ['status' => 'Absent']),
            ],
            [
                'label' => 'Shift Not Started',
                'count' => $attDetail['shift_not_started']['total'] ?? 0,
                'category' => $attDetail['shift_not_started']['types'] ?? 0,
                'link' => route('attendanceDashboard', ['status' => 'Shift Not Started']),
            ],
            [
                'label' => 'Day Off',
                'count' => $attDetail['day_off']['total'],
                'category' => $attDetail['day_off']['types'],
                'link' => route('attendanceDashboard', ['status' => 'Day Off']),
            ],
            [
                'label' => 'On Leave',
                'count' => $attDetail['on_leave']['total'],
                'category' => $attDetail['on_leave']['types'],
                'link' => route('attendanceDashboard', ['status' => 'On Leave']),
            ],
        ];
    }

    public function getEmployeeTypeData(int $filterRegionId = null, int $filterBranchId = null)
    {
        $empTypeDetail = $this->dashboardRepo->getInhouseAndOutsourceCounts($filterRegionId, $filterBranchId, dateEn: $this->filterSelectedDate);
        $regularCategoryId = $this->dashboardRepo->getRegularCategoryId();
        $this->empTypeDetails = [
            [
                'label' => 'Inhouse',
                'count' => $empTypeDetail['inhouse'],
                'link' => route('employeelist', ['type' => 'inhouse',  'category' => $regularCategoryId,]),
            ],
            [
                'label' => 'Out source',
                'count' => $empTypeDetail['outsource'],
                'link' => route('employeelist', ['type' => 'outsource',  'category' => $regularCategoryId,]),
            ]
        ];

        $this->inhouseAndOutsourceAttDetails = [
            'inhousePresent' => $empTypeDetail['inhouse_present'],
            'outsourcePresent' => $empTypeDetail['outsource_present'],
            'inhouseAbsent' => $empTypeDetail['inhouse_absent'],
            'outsourceAbsent' => $empTypeDetail['outsource_absent'],
        ];
    }

    public function getDemographicData(int $filterRegionId = null, int $filterBranchId = null)
    {
        $this->genderMaritalStatusCount = $this->dashboardRepo->getGenderMaritalStatusCounts($filterRegionId, $filterBranchId);
        $this->ageGroupCount = $this->dashboardRepo->getGenderAgeGroupCounts($filterRegionId, $filterBranchId);
    }

    public function getPayslipData(int $filterRegionId = null, int $filterBranchId = null)
    {
        $this->payslipDetail = $this->dashboardRepo->getPayslipDetails($filterRegionId, $filterBranchId);
    }

    public function getTardinessData(int $filterRegionId = null, int $filterBranchId = null)
    {
        $this->tardinessDetail = $this->dashboardRepo->getLateInEarlyOutDetails($filterRegionId, $filterBranchId);
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.hr-dashboard');
    }

    public function refreshDashboard()
    {
        Cache::store('dashboard')->flush();
        redirect(route('dashboard'));
        $this->notify('Dashboard refreshed successfully')->send();
    }
}
