<?php

namespace App\Livewire\Dashboards\Hradmin;

use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Livewire\Component;

class ClearServerCache extends Component
{
    use WithNotify;

    public function clearServerCache()
    {
        try {
            Artisan::call("optimize:clear");
            Artisan::call("view:clear");
            Artisan::call("route:clear");
            $this->notify('Successfully cleared server cache.')->send();
        } catch (Exception $e) {
            logError("Filed to cleared server cache. ", $e);
            $this->notify('Failed to cleared server cache')->type('error')->send();
        }
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.clear-server-cache');
    }
}
