<?php

namespace App\Livewire\Dashboards\Hradmin;

use Livewire\Component;

class Tardiness<PERSON>hart extends Component
{
    public $tardinessData, $tardinessProgress;

    public function mount($tardinessCounts, $totalActiveEmployees) {
        $onTimeCount = $totalActiveEmployees - ($tardinessCounts['late_in_count'] + $tardinessCounts['early_out_count']);

        $this->tardinessProgress = [
            [
                'label' => 'Late In', 
                'value' => $tardinessCounts['late_in_count'], 
                'percent' => $this->calculatePercentage($tardinessCounts['late_in_count'], $totalActiveEmployees), 
                'color' => '#e14b3e',
                'route' => route('attendanceDashboard', ['status' => 'Late In']),
            ],
            [
                'label' => 'Early Out', 
                'value' => $tardinessCounts['early_out_count'], 
                'percent' => $this->calculatePercentage($tardinessCounts['early_out_count'], $totalActiveEmployees), 
                'color' => '#f77d3b',
                'route' => route('attendanceDashboard', ['status' => 'Early Out']),
            ],
            [
                'label' => 'Punctual', 
                'value' => $tardinessCounts['punctual_count'], 
                'percent' => $this->calculatePercentage($tardinessCounts['punctual_count'], $totalActiveEmployees),
                'color' => '#28a745',
                'route' => route('attendanceDashboard', ['status' => 'Punctual']),
            ]
        ];
    }

    public function calculatePercentage(int $count, int $total) {
        if($total == 0) return '0.00';
        $percentage = ($count / $total) * 100;
        return number_format($percentage, 2);
    }

    #[\Livewire\Attributes\On('updateTardinessChartData')]
    public function updateTardinessChartData($tardinessCounts, $totalActiveEmployees){
        $this->tardinessProgress = [
            [
                'label' => 'Late In', 
                'value' => $tardinessCounts['late_in_count'], 
                'percent' => $this->calculatePercentage($tardinessCounts['late_in_count'], $totalActiveEmployees), 
                'color' => '#e14b3e',
                'route' => route('attendanceDashboard', ['status' => 'Late In']),
            ],
            [
                'label' => 'Early Out', 
                'value' => $tardinessCounts['early_out_count'], 
                'percent' => $this->calculatePercentage($tardinessCounts['early_out_count'], $totalActiveEmployees), 
                'color' => '#f77d3b',
                'route' => route('attendanceDashboard', ['status' => 'Early Out']),
            ],
        ];
    }

    public function render()
    {
        return view('livewire.dashboards.hradmin.tardiness-chart');
    }
}
