<?php

namespace App\Livewire\Dashboards\Hradmin;

use App\Models\configs\LeaveType;
use Livewire\Component;

class LeaveType<PERSON>hart extends Component
{
    public $leaveDetail = [];
    public $status;

    public function mount($leaveDetail)
    {
        $this->leaveDetail = $leaveDetail;
         $this->status = request()->query('status');
    }

    #[\Livewire\Attributes\On('updateOnLeaveTypeData')]
    public function updateOnLeaveTypeData($leaveDetail)
    {
        $this->leaveDetail = $leaveDetail;
    }
    // public function goToLeaveDashboard($filter)
    // {
    //     return redirect()->to("/leaves/dashboard?tab=range&filter={$filter}");
    // }

    public function render()
    {
        return view('livewire.dashboards.hradmin.leave-type-chart');
    }
}



