<?php

namespace App\Livewire\HrAdmin\Attendance;

use Livewire\Component;
use Livewire\Attributes\Title;
use Livewire\Attributes\Computed;
use App\Models\configs\Company;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Traits\WithDataTable;
use App\Traits\MultiselectEmployeeSearch;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use Carbon\Carbon;
use App\Models\Attendance\IrregularityTicket;
use Illuminate\Support\Facades\DB;
use App\Http\Repositories\Attendance\AttendanceDashboardRepository;

#[Title('Attendance Irregularity Ticket')]
class AttendanceIrregurality extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $startDate, $endDate, $selectedCompany, $selectedBranch, $selectedDepartment, $selectedStatus, $irregularityId, $selectedState, $regionId;
    public $attendanceDetails = [];
    public $employee_ids = [];

    private AttendanceDashboardRepository $repo;

    public function __construct()
    {
        $this->repo = new AttendanceDashboardRepository;
    }
    public function mount()
    {
        $this->initializeData();
        $this->attendanceIrreguralityCount();
        $this->multiSelectAttributes = ['employee_ids'];
        $this->setPermissionForEmployeeDropdown();
        $this->scopeWiseFilters();
    }

    public function rules()
    {
        return [
            'startDate' => 'required|before_or_equal:endDate',
            'endDate' => 'required|after_or_equal:startDate',
        ];
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }
        if (!scopeAll()) {
            $this->selectedCompany = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->regionId = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->selectedBranch = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
            $this->selectedDepartment = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    public function updated($property)
    {
        if (in_array($property, ['startDate', 'endDate'])) {
            $this->validate();
        }
        if (in_array($property, ['startDate', 'endDate', 'selectedBranch', 'selectedDepartment', 'employee_ids'])) {
            unset($this->irreguralityAttendanceList);
            $this->attendanceIrreguralityCount();
        }
        if ($property === 'selectedCompany') {
            unset($this->branchList, $this->departmentList);
            $this->selectedBranch = null;
            $this->selectedDepartment = null;
            $this->attendanceIrreguralityCount();
        }
        if ($property === 'selectedStatus') {
            unset($this->irreguralityAttendanceList);
            $this->filterAttendance($this->selectedStatus);
        }
    }

    #[Computed()]
    public function irreguralityAttendanceList()
    {
        $selectedStatus = $this->selectedStatus;
        $selectedBranch = $this->selectedBranch;
        $selectedDepartment = $this->selectedDepartment;
        $selectedState = $this->selectedState;

        $query = IrregularityTicket::leftJoin('employees', 'irregularity_tickets.employee_id', '=', 'employees.id')
            ->leftJoin('companies', 'employees.company_id', '=', 'companies.id')
            ->leftJoin('employee_org as org', 'org.employee_id', '=', 'irregularity_tickets.employee_id')
            ->leftJoin('departments', 'org.department_id', '=', 'departments.id')
            ->leftJoin('branches', 'org.branch_id', '=', 'branches.id')
            ->join('employee_org', function ($join) use ($selectedBranch, $selectedDepartment) {
                $join->on('employees.id', '=', 'employee_org.employee_id');
                if ($selectedBranch !== null && $selectedBranch != "-1") {
                    $join->where('employee_org.branch_id', $selectedBranch);
                }
                if ($selectedDepartment !== null && $selectedDepartment != "-1") {
                    $join->where('employee_org.department_id', $selectedDepartment);
                }
            })
            ->when($this->employee_ids, function ($query) {
                $query->whereIn('employees.id', $this->employee_ids);
            })
            ->when($this->selectedStatus, function ($query) use ($selectedStatus) {
                if ($selectedStatus == 'Early Out') {
                    return $query->where('irregularity_tickets.type', 'Early Out');
                } elseif ($selectedStatus == 'Late In') {
                    return $query->where('irregularity_tickets.type', 'Late In');
                } else {
                    return $query->where('irregularity_tickets.type', 'like', "%{$selectedStatus}%");
                }
            })
            ->when($this->selectedState, function ($query) use ($selectedState) {
                return $query->whereNotIn('irregularity_tickets.state', ArflowHelper::getFinalStates(WorkflowName::IRREGULARITY_TICKET));
            })
            ->when($this->startDate && $this->endDate, function ($query) {
                $query->where('irregularity_tickets.date_np', '>=', $this->startDate)
                      ->where('irregularity_tickets.date_np', '<=', $this->endDate);
            })
            ->select(
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                DB::raw("CONCAT(companies.code, '-', org.employee_code) as company_employee_code"),
                'irregularity_tickets.id as id',
                'irregularity_tickets.employee_id as employee_id',
                'irregularity_tickets.date_np as date_np',
                'irregularity_tickets.state as state',
                'irregularity_tickets.type as type',
                'departments.name as department_name',
                'branches.name as branch_name',
            );

        $query = filterEmployeesByScope($query, 'org', 'employees');

        return $this->applySorting($query)->paginate($this->perPage);
    }
    public function initializeData()
    {
        $this->search = '';
        $this->sortBy = 'date_en';
        $this->sortDirection = 'desc';
        $this->startDate = LaravelNepaliDate::from(Carbon::now()->subDay(30))->toNepaliDate();
        $this->endDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate();
    }

    public function showDetail(int $id): void
    {
        $this->irregularityId = $id;
    }

    #[Computed()]
    public function companyList()
    {
        return Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }
    #[Computed()]
    public function branchList()
    {
        if (scopeAll()) {
            return Branch::orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } elseif (scopeCompany()) {
            return Branch::where('company_id', $this?->selectedCompany)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } else {
            $query = Branch::where('company_id', $this?->selectedCompany);

            if (!is_null($this?->regionId)) {
                $query->where('region_id', $this->regionId);
            }

            return $query->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        }
    }

    #[Computed()]
    public function departmentList()
    {
        return Department::where('company_id', $this->selectedCompany)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }
    #[Computed(persist: true)]
    public function attendanceStatusList()
    {
        return [
            'Early Out' => 'Early Out',
            'Late In' => 'Late In',
        ];
    }

    public function attendanceIrreguralityCount()
    {
        $this->attendanceDetails = [
            'Early Out' => [
                'count' => $this->repo->irregularityEarlyOutCount($this->selectedBranch, $this->selectedDepartment, $this->startDate, $this->endDate),
            ],
            'Late In' => [
                'count' => $this->repo->irregularityLateInCount($this->selectedBranch, $this->selectedDepartment, $this->startDate, $this->endDate),
            ],
            'Pending Ticket Count' => [
                'count' => $this->repo->irregularityTicketCount($this->selectedBranch, $this->selectedDepartment, $this->startDate, $this->endDate)
            ]
        ];
    }

    public function filterAttendance($status)
    {
        if ($status == 'Early Out' || $status == 'Late In') {
            $this->selectedStatus = $status;
            $this->selectedState = null;
        } else {
            $this->selectedState = $status;
            $this->selectedStatus = null;
        }
    }
    public function render()
    {
        return view('livewire.hr-admin.attendance.attendance-irregurality');
    }
}
