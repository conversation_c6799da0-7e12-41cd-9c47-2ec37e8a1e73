<?php

namespace App\Livewire\HrAdmin\Attendance;

use App\Exports\ConsecutiveAttendanceReportExport;
use App\Jobs\ConsecutiveAttendanceReportJob;
use App\Models\configs\Setting;
use App\Traits\WithDefaultFilter;
use Livewire\Component;
use Livewire\Attributes\Title;
use Livewire\Attributes\Computed;
use App\Models\Leaves\Attendance;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Consecutive Attendance Issue')]
class ConsecutiveAttendanceIssue extends Component
{
    use WithDataTable, WithDefaultFilter;

    // public $records = [];
    public $startDate, $endDate, $selectedDate;
    public $regionId;

    // public $perPage = 10;


    public $selectedStatus = '';
    public $issueTypeOptions = [
        '' => 'All',
        'absent' => 'Absent',
        'late in' => 'Late In',
        'early out' => 'Early Out',
    ];

    public function mount()
    {
        $this->initializeData();

        $this->loadRecords();
    }

    public function initializeData()
    {
        $this->selectedDate = Carbon::today()->format('Y-m-d');
        $this->startDate = Carbon::parse($this->selectedDate)->subDays(2)->format('Y-m-d');
        $this->endDate = Carbon::parse($this->selectedDate)->subDays(1)->format('Y-m-d');
        $this->selectedStatus = array_key_first($this->issueTypeOptions);
    }


    public function updated($property)
    {
        if ($property === 'selectedDate') {
            $this->startDate = Carbon::parse($this->selectedDate)->subDays(2)->format('Y-m-d');
            $this->endDate = Carbon::parse($this->selectedDate)->subDays(1)->format('Y-m-d');
        }

        if (in_array($property, ['filterCompanyId', 'filterBranchId', 'filterDepartmentId', 'startDate', 'endDate', 'selectedDate', 'selectedStatus', 'search'])) {
            unset($this->records);
            $this->resetPage();
        }
    }

    public function updateStatusFilter($status)
    {
        if ($this->selectedStatus === $status) {
            $this->selectedStatus = '';
            $this->resetPage();
        } else {
            $this->selectedStatus = $status;
        }
        unset($this->records);
    }

    protected function loadRecords()
    {
        $consecutiveStatuses = ['Absent', 'Late In', 'Early Out'];
        $startDate = $this->startDate;
        $endDate   = $this->endDate;

        $whereInLike = function ($query, $column, $values) {
            $query->where(function ($subQuery) use ($column, $values) {
                foreach ($values as $value) {
                    $subQuery->orWhere($column, 'like', "%{$value}%");
                }
            });
        };

        $query = Attendance::query()
            ->select([
                'attendance.id',
                'attendance.employee_id',
                DB::raw("CONCAT(c.code, '-', emp_org.employee_code) as employee_code"),
                'attendance.date_en',
                'attendance.in_time',
                'attendance.out_time',
                'attendance.status',
                'attendance.remarks',
                'b.name as branch_name',
                'd.name as department_name',
                DB::raw("CONCAT(e.first_name, ' ', COALESCE(e.middle_name, ''), ' ', e.last_name) as employee_name"),
            ])
            ->join('employees as e', 'attendance.employee_id', '=', 'e.id')
            ->join('employee_org as emp_org', 'attendance.employee_id', '=', 'emp_org.employee_id')
            ->join('companies as c', 'c.id', '=', 'e.company_id')
            ->leftJoin('branches as b', 'emp_org.branch_id', '=', 'b.id')
            ->leftJoin('departments as d', 'emp_org.department_id', '=', 'd.id')
            ->whereBetween('attendance.date_en', [$startDate, $endDate])
            ->where(function ($query) use ($whereInLike, $consecutiveStatuses) {
                $whereInLike($query, 'attendance.status', $consecutiveStatuses);
            })
            ->whereIn('attendance.employee_id', function ($sub) use ($whereInLike, $consecutiveStatuses, $startDate, $endDate) {
                $sub->select('a1.employee_id')
                    ->from('attendance as a1')
                    ->join('attendance as a2',function($join){
                        $join->on('a1.employee_id', '=', 'a2.employee_id')
                            ->on(DB::raw('DATE(a1.date_en)'), '=', DB::raw('DATE_ADD(a2.date_en, INTERVAL 1 DAY)'));
                    })->whereBetween('a1.date_en',[$startDate,$endDate])
                    ->where(function($query) use ($consecutiveStatuses){
                        foreach($consecutiveStatuses as $status){
                            $query->orWhere('a1.status','like',"%{$status}");
                        }
                    })
                    ->where(function($query) use ($consecutiveStatuses){
                        foreach($consecutiveStatuses as $status){
                            $query->orWhere('a2.status','like', "%{$status}");
                        }
                    })
                    ->groupBy('a1.employee_id');
                });

        $query = $this->defaultFilterQuery($query, 'e', 'emp_org');

        return $query->orderBy('employee_name')->orderBy('attendance.date_en');
    }

    #[Computed()]
    public function counts()
    {
        $cacheKey = 'consecutive_attendance_issue_counts_' . md5($this->selectedDate . '_' . $this->filterCompanyId . '_' . $this->filterBranchId . '_' . $this->filterDepartmentId);
        return Cache::remember($cacheKey, now()->addMinutes(30), function () {
            return  [
                [
                    'label' => 'Absent',
                    'count' => $this->loadRecords()->where('attendance.status', 'Absent')->distinct('e.id')->count(),
                ],
                [
                    'label' => 'Late In',
                    'count' => $this->loadRecords()->where('attendance.status', 'like', '%Late In%')->distinct('e.id')->count(),
                ],
                [
                    'label' => 'Early Out',
                    'count' => $this->loadRecords()->where('attendance.status', 'like', '%Early Out%')->distinct('e.id')->count(),
                ],
            ];
        });
    }

    #[Computed()]
    public function records()
    {
        $query = $this->loadRecords();
        if ($this->selectedStatus) {
            switch (strtolower($this->selectedStatus)) {
                case 'absent':
                    $query = $query->where('attendance.status', 'Absent');
                    break;
                case 'late in':
                    $query = $query->where('attendance.status', 'like', '%Late%');
                    break;
                case 'early out':
                    $query = $query->where('attendance.status', 'like', '%Early Out%');
                    break;
            }
        } else {
            $query->where(function ($q) {
                $q->where('attendance.status', 'Absent')
                    ->orWhere('attendance.status', 'like', '%Late In%')
                    ->orWhere('attendance.status', 'like', '%Early Out%');
            });
        }
        return $query->when(
            $this->search,
            function ($q) {
                $q->where(DB::raw("CONCAT(e.first_name, ' ', e.middle_name, ' ', e.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT(e.first_name, ' ', e.last_name)"), 'LIKE', '%' . $this->search . '%');
            }
        )->paginate($this->perPage);
    }

    public function sendEmailOfConsecutiveAttendanceReport()
    {
        $records = $this->loadRecords()->get()->toArray();
        $startDate = $this->startDate;
        $endDate = $this->endDate;

        // dispatch_sync(new ConsecutiveAttendanceReportJob($records, $startDate, $endDate));
        ConsecutiveAttendanceReportJob::dispatch($records, $startDate, $endDate);
        logInfo("Data is Dispatched to jobs");
    }

    public function exportConsecutiveAttendanceReport($all = false)
    {
        if ($all) {
            $records = $this->loadRecords()->get();
            $fileName = 'Consecutive_Attendance_Report_All_' . now()->format('Y-m-d-His') . '.xlsx';
        } else {
            $records = $this->loadRecords()->paginate($this->perPage)->items();
            $fileName = 'Consecutive_Attendance_Report_Page_' . now()->format('Y-m-d-His') . '.xlsx';
        }
        return Excel::download(new ConsecutiveAttendanceReportExport($records), $fileName);
    }

    public function render()
    {
        return view('livewire.hr-admin.attendance.consecutive-attendance-issue');
    }
}
