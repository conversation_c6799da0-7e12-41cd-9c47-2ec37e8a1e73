<?php

namespace App\Livewire\HrAdmin\Attendance;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Repositories\Reports\AttendanceRepository;
use App\Http\Repositories\SyncDeviceAttendanceRepository;
use App\Http\Repositories\SyncRepository;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Facades\LaravelNepaliDate as FacadesLaravelNepaliDate;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\NepaliCalendarTrait;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Sync Attendance")]
class SyncAttendance extends Component
{
    use NepaliCalendarTrait, MultiselectEmployeeSearch, WithNotify;
    public $selectedDate, $nepYear, $nepMonth;

    public $message;

    public $employee_id = [];

    public function rules()
    {
        return [
            'selectedDate' => ['required', function ($attribute, $value, $fail) {
                $selectedDate = LaravelNepaliDate::from($value)->toEnglishDate();
                if ($selectedDate > today()) {
                    $fail('Date should not be greater than today.');
                }
            }],
        ];
    }

    public function mount()
    {
        $this->selectedDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y-m-d');
        $this->setPermissionForEmployeeDropdown();
        $this->singleSelectAttributes = ['employee_id'];
        $this->withTrashed = true;
    }

    public function syncAttendance()
    {
        $this->resetErrorBag(['selectedDate']);
        $this->validate();

        $this->reset(['message']);
        $eng_date = LaravelNepaliDate::from($this->selectedDate)->toEnglishDate('Y-m-d');

        $device_attendance_sync = new SyncDeviceAttendanceRepository;
        $device_attendance_sync->syncDeviceAttendanceToDB($eng_date);

        $attendance_sync = new SyncRepository;
        $sync = $attendance_sync->syncAttendanceFromDeviceToDb($eng_date);

        if ($sync) {
            $laravelNepaliDate = new FacadesLaravelNepaliDate;
            $engDateRange = $laravelNepaliDate->get_engdaterange_for_nepalimonth(explode('-', $this->selectedDate)[1], explode('-', $this->selectedDate)[0]);

            $terminatedEmployeeQuery = EmployeeOrg::whereBetween('termination_date', [$engDateRange['startdate'], $engDateRange['enddate']])
                ->withTrashed();

            // Delete attendance of terminated employee from termination date.
            $terminatedEmployees = $terminatedEmployeeQuery->select("employee_id", "termination_date")->get();
            foreach ($terminatedEmployees as $employee) {
                if (fedexHrm())
                    Attendance::where([['employee_id', $employee->employee_id], ['date_en', '>=', $employee->termination_date]])->forceDelete();
                else
                    Attendance::where([['employee_id', $employee->employee_id], ['date_en', '>', $employee->termination_date]])->forceDelete();
            }

            // forceDelete attendance of newly join employee before their date of join.
            $newEmployeeQuery = EmployeeOrg::whereBetween('doj', [$engDateRange['startdate'], $engDateRange['enddate']])->select("employee_id", "doj")->get();
            foreach ($newEmployeeQuery as $employee) {
                Attendance::where([['employee_id', $employee->employee_id], ['date_en', '<', $employee->doj]])->forceDelete();
            }
            $this->message = "Attendance for {$this->selectedDate} synced successfully.";
        } else {
            $this->message = "Failed to sync attendance for {$this->selectedDate}.";
        }
    }

    public function syncEmployeeAttendance()
    {
        $this->validate([
            'nepYear' => 'required',
            'nepMonth' => 'required',
        ], attributes: [
            'nepYear' => 'year',
            'nepMonth' => 'month',
        ]);
        try {
            $employee = Employee::with(['organizationInfo'])
                ->where('id', $this->employee_id)
                ->select('id')
                ->withTrashed()
                ->first();
            $engDateRange = $this->get_engdaterange_for_nepalimonth($this->nepMonth, $this->nepYear);
            $startDate = Carbon::parse($engDateRange['startdate']);
            $endDate  = Carbon::parse($engDateRange['enddate']);
            $doj = Carbon::parse($employee->organizationInfo->doj);
            $terminationDate = Carbon::parse($employee->organizationInfo->termination_date);
            $isNightShift = $employee->organizationInfo->isNightShift;

            if( $isNightShift ) {
                // For night shift employee, extend the end date by 1 day to include the first day's night shift attendance.
                $startDate->addDay();
                // For night shift employee, extend the end date by 1 day to include the last day's night shift attendance.
                $endDate->addDay();
            }

            if ($startDate < $doj) $startDate = $doj;
            if ($endDate > $terminationDate) $endDate = $terminationDate;

            $attendanceRepo = new SyncRepository;
            $startDateCopy = $startDate->copy();
            while ($startDateCopy->lte($endDate)) {
                if ($startDateCopy->gt(Carbon::today())) break;

                $date = $startDateCopy->format('Y-m-d');
                $attendanceRepo->syncAttendanceFromDeviceToDb($date, $this->employee_id);
                $startDateCopy->addDay();
            }

            // Delete the future date attendance and attendance before DOJ.
            $attendanceRepository = new AttendanceRepository();
            $attendanceRepository->deleteAttendanceBeforeDOJ($this->employee_id, $doj);
            $attendanceRepository->deleteFutureDateAttendance($this->employee_id, $terminationDate);

            $this->notify("Attendance synced")->send();
        } catch (\Exception $e) {
            $this->notify("Error while syncing Attendance")->type('error')->send();
            logError("Employee Attendance sync error.", $e);
        }
    }

    #[Computed(persist: true)]
    public function monthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    #[Computed(persist: true)]
    public function yearList()
    {
        $selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        for ($i = 0; $i < 5; $i++) {
            $yearList[] = $selectedYear - $i;
        }
        return $yearList;
    }

    public function render()
    {
        return view('livewire.hr-admin.attendance.sync-attendance');
    }
}
