<?php

namespace App\Livewire\HrAdmin\Attendance;

use App\Http\Repositories\DashboardRepository;
use App\Http\Services\ScopeFetcher;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use App\Models\Leaves\Attendance;
use App\Models\configs\Company;
use App\Models\configs\Job;
use App\Models\EmployeeCategory;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Attendance Dashboard')]
class AttendanceDashboard extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $selectedDate, $selectedBranch, $selectedDepartment, $selectedCompany, $selectedJob;
    public $selectedLocationDetailEmployee = null;
    public $attendanceDetails = [];
    public $employee_ids = [];

    #[Url(as: 'status')]
    public $selectedStatus;
    #[Url(as: 'category')]
    public $category;


    private AttendanceDashboardRepository $repo;
    private DashboardRepository $dashboardRepo;

    public function __construct()
    {
        $this->repo = new AttendanceDashboardRepository;
        $this->dashboardRepo = new DashboardRepository();
        $this->tableListVariable = "attendanceList";
        $this->tableListQueryVariable = "attendanceListQuery";
    }

    public function mount()
    {
        if (!scopeAll() && (scopeCompany() || scopeRegion() || scopeBranch())) {
            $this->selectedCompany = currentEmployee()?->company_id;
        }
        if (request()->has('category')) {
            $typeName = request()->query('category');
            $typeId = $this->getEmployeeTypeId($typeName);
            $this->category = $typeId;
        }
        $this->initializeData();
        $this->attendanceDetails();
        $this->multiSelectAttributes = ['employee_ids'];
    }

    public function getEmployeeTypeId($typeName)
    {
        return EmployeeCategory::where('is_countable', true)->where('type', $typeName)->value('id');
    }
    public function updated($property)
    {
        if (\in_array($property, ['company', 'region', 'branch', 'department', 'employee_ids'])) {
            $this->resetPage();
        }
        if (in_array($property, ['selectedDate', 'selectedBranch', 'selectedDepartment', 'employee_ids', 'selectedStatus', 'attendanceDetails', 'category'])) {
            unset($this->attendanceList);
            $this->attendanceDetails();
        }
        if ($property === 'selectedCompany') {
            unset($this->branchList, $this->departmentList);
            $this->selectedBranch = null;
            $this->selectedDepartment = null;
            $this->attendanceDetails();
        }
    }

    public function initializeData()
    {
        $this->search = '';
        $this->sortBy = 'date_en';
        $this->sortDirection = 'desc';
        $this->selectedDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y-m-d');
    }

    public function attendanceListQuery()
    {
        $selectedBranch = $this->selectedBranch;
        $selectedDepartment = $this->selectedDepartment;
        $selectedStatus = $this->selectedStatus;
        $selectedCategory = $this->category;
        $query =
            Attendance::with(['employee.department', 'employee.branch', 'employee.company:id,code', 'employee.organizationInfo:id,employee_id,employee_code'])
            ->join('employees', 'attendance.employee_id', '=', 'employees.id')
            ->join('employee_org', function ($join) use ($selectedBranch, $selectedDepartment) {
                $join->on('employees.id', '=', 'employee_org.employee_id');
                if ($selectedBranch !== null && $selectedBranch != "-1") {
                    $join->where('employee_org.branch_id', $selectedBranch);
                }
                if ($selectedDepartment !== null && $selectedDepartment != "-1") {
                    $join->where('employee_org.department_id', $selectedDepartment);
                }
            })->leftJoin('branches', 'employee_org.branch_id', '=', 'branches.id')
            ->leftJoin('departments', 'employee_org.department_id', '=', 'departments.id')
            ->leftJoin('leave_requests', 'attendance.leave_request_id', '=', 'leave_requests.id')
            ->leftJoin('leave_types', 'leave_requests.leave_type_id', '=', 'leave_types.id')
            ->leftJoin('companies', 'employees.company_id', '=', 'companies.id')
            ->leftJoin('payslips', 'payslips.employee_id', '=', 'employees.id')
            ->leftJoin('jobs', 'payslips.job_id', '=', 'jobs.id')
            ->whereNotNull('employee_org.employee_category_id')
            ->when(count($this->employee_ids), function ($query) {
                $query->whereIn('employees.id', $this->employee_ids);
            })

            ->when($this->selectedCompany, function ($query) {
                $query->where("employees.company_id", $this->selectedCompany);
            })
            ->when($selectedCategory, function ($query) use ($selectedCategory) {
                $query->where("employee_org.employee_category_id", $selectedCategory);
            })

            ->when($this->selectedStatus, function ($query) use ($selectedStatus) {
                if (str_contains($selectedStatus, 'Present')) {
                    return $query->where(function ($query) {
                        $query->whereNotNull('attendance.in_time')
                            ->orWhereNotNull('attendance.out_time');
                    });
                } elseif (str_contains($selectedStatus, 'Absent')) {
                    $query->where(function ($query) {
                        $query->whereNull('attendance.in_time')
                            ->whereNull('attendance.out_time')
                            ->where(function ($q) {
                                $q->whereNull('attendance.leave_request_id')
                                    ->orWhere(function ($q2) {
                                        $q2->where('leave_types.paid', 0)
                                            ->orwhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                                    });
                            })
                            ->where(function ($q) {
                                $q->whereNull('attendance.status')
                                    ->orWhereNotIn('attendance.status', ['Day Off', 'On Holiday', 'Shift Not Started']);
                            });
                        });
                } elseif (str_contains($selectedStatus, 'Shift Not Started')) {
                    $query->where(function ($query) {
                        $query->whereNull('attendance.in_time')
                            ->whereNull('attendance.out_time')
                            ->where(function ($q) {
                                $q->whereNull('attendance.leave_request_id')
                                    ->orWhere(function ($q2) {
                                        $q2->where('leave_types.paid', 0)
                                            ->orwhereNotIn('leave_requests.state', ['Approved', 'Assigned']);
                                    });
                            })
                            ->where('attendance.status', 'Shift Not Started');
                    });
                } elseif (str_contains($selectedStatus, 'On Leave')) {
                    $query->where(function ($query) {
                        $query->whereNull('attendance.in_time')
                            ->whereNull('attendance.out_time')
                            ->where(function ($q) {
                                $q->whereNotNull('attendance.leave_request_id')
                                    ->where('leave_types.paid', 1)
                                    ->whereIn('leave_requests.state', ['Approved', 'Assigned']);
                            });
                    });
                } elseif (str_contains($selectedStatus, 'Late In')) {
                    $query->where(function ($query) {
                        $query->where('attendance.status', 'like', '%Late%');
                    });
                } elseif (str_contains($selectedStatus, 'Punctual')) {
                    $query = $query
                        ->where('attendance.status', 'Present');
                } else {
                    return $query->where('attendance.status', 'like', "%" . $this->selectedStatus . "%");
                }
            })
            ->when($this->selectedJob, function ($query) {
                $query->where("payslips.job_id", $this->selectedJob);
            })
            ->where('attendance.date_np', "{$this->selectedDate}")
            ->where(function ($query) {
                $query->whereRaw('payslips.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Active"
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereRaw('payslips.id = (
                    SELECT id FROM payslips p
                    WHERE p.employee_id = employees.id
                    AND p.status = "Expired"
                    AND NOT EXISTS (
                        SELECT 1 FROM payslips active
                        WHERE active.employee_id = p.employee_id
                        AND active.status = "Active"
                    )
                    ORDER BY p.created_at DESC
                    LIMIT 1
                )')
                    ->orWhereNull('payslips.id');
            });


        $query = filterEmployeesByScope($query, 'employee_org', 'employees');

        $results = $query->select(
            DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
            'attendance.id as id',
            'attendance.employee_id as employee_id',
            DB::raw("CONCAT(companies.code, '-', employee_org.employee_code) as company_employee_code"),
            'branches.name as branch_name',
            'departments.name as department_name',
            'jobs.name as job',
            'attendance.duty_start as shift_start',
            'attendance.duty_end as shift_end',
            'attendance.in_time as in_time',
            'attendance.out_time as out_time',
            'attendance.remarks as remarks',
            'attendance.status as status',
            'attendance.source as source',
            'leave_types.paid as paid',
            'attendance.location_data as location_data',
        )
            ->orderBy('full_name');

        return $this->applySorting($results);
    }

    #[Computed()]
    public function attendanceList()
    {
        return $this->attendanceListQuery()->paginate($this->perPage);
    }

    #[Computed()]
    public function companyList()
    {
        return Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }
    #[Computed()]
    public function employeeCategoryList()
    {
        return EmployeeCategory::orderBy('type', 'asc')->pluck('type', 'id')->toArray();
    }
    #[Computed()]
    public function branchList()
    {
        return (new ScopeFetcher())->fetchBranch($this->selectedCompany, "")->pluck('name', 'id')->toArray();
    }

    #[Computed()]
    public function departmentList()
    {
        return (new ScopeFetcher())->fetchDepartment($this->selectedCompany)->pluck('name', 'id')->toArray();
    }

    #[Computed()]
    public function jobList()
    {
        return Job::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function attendanceStatusList()
    {
        return [
            'Absent' => 'Absent',
            'Early Out' => 'Early Out',
            'Late In' => 'Late In',
            'Missed Punch' => 'Missed Punch',
            'On Leave' => 'On Leave',
            'Day Off' => 'Day Off',
            'Present' => 'Present',
            'Punctual' => 'Punctual',
            'Shift Not Started' => 'Shift Not Started',
        ];
    }

    public function attendanceDetails()
    {
        $this->attendanceDetails = [
            'Present' => [
                'count' => $this->repo->getPresentCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Absent' => [
                'count' => $this->repo->getAbsentCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Shift Not Started' => [
                'count' => $this->repo->getShiftNotStartedCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'On Leave' => [
                'count' => $this->repo->getOnLeaveCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Day Off' => [
                'count' => $this->repo->getOnDayOffCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Punctual' => [
                'count' => $this->repo->getPunctualCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Early Out' => [
                'count' => $this->repo->getEarlyOutCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
            'Late In' => [
                'count' => $this->repo->getLateInCount($this->selectedDate, $this->selectedBranch, $this->selectedDepartment),
            ],
        ];
    }

    public function filterAttendance($status)
    {
        $this->selectedStatus = $status;
        unset($this->attendanceList);
    }

    public function setClockInOutDetail($item)
    {
        $locationData = $item['location_data'] ?? [];
        $this->selectedLocationDetailEmployee = $item['full_name'] ?? null;

        if (empty($locationData)) {
            return;
        }

        $clockInCoordinates = $locationData['clock_in'] ?? [];
        $clockOutCoordinates = $locationData['clock_out'] ?? [];
        $clockInTime = !empty($item['in_time']) ? Carbon::parse($item['in_time'])->format('h:i A') : '';
        $clockOutTime = !empty($item['out_time']) ? Carbon::parse($item['out_time'])->format('h:i A') : '';

        $markers = [];

        if (!empty($clockInCoordinates['lat']) && !empty($clockInCoordinates['lng'])) {
            $markers[] = [
                'lat' => $clockInCoordinates['lat'],
                'lng' => $clockInCoordinates['lng'],
                'title' => "Clock In<br/>$clockInTime",
                'icon' => 'green',
            ];
        }

        if (!empty($clockOutCoordinates['lat']) && !empty($clockOutCoordinates['lng'])) {
            $markers[] = [
                'lat' => $clockOutCoordinates['lat'],
                'lng' => $clockOutCoordinates['lng'],
                'title' => "Clock Out<br/>$clockOutTime",
                'icon' => 'red',
            ];
        }

        if (!empty($markers)) {
            $firstMarker = $markers[0];
            $this->dispatch('reinit-map-clock-map', [
                'lat' => $firstMarker['lat'],
                'lng' => $firstMarker['lng'],
                'markers' => $markers,
            ]);
        }
    }
    public function setExcelOptions()
    {
        $this->exportFileName = "Attendance Report.xlsx";
        $this->exportIgnoreColumns = [
            'employee',
            'paid',
            'location_data',
            'id',
            'employee_id'
        ];
        $this->exportColumnHeadersMap = [
            'full_name' => 'Employee Name',
            'duty_start' => 'Shift Start',
            'duty_end' => 'Shift End',
            'in_time' => 'In Time',
            'out_time' => 'Out Time',
            'remarks' => 'Remarks',
            'status' => 'Status',
            'source' => 'Source',
            'branch_name' => 'Branch',
            'department_name' => 'Department',
            'company_employee_code' => 'Employee Code',
        ];
    }
    public function exportAllEmployees()
    {
        return $this->exportDataTable(true);
    }


    public function render()
    {
        return view('livewire.hr-admin.attendance.attendance-dashboard');
    }
}
