<?php

namespace App\Livewire\HrAdmin\Termination;

use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TerminationRepository;
use App\Http\Repositories\TicketRepository;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\Termination\EmployeeTermination;
use App\Models\Tickets\ManpowerRequisition;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use AuroraWebSoftware\ArFlow\Facades\ArFlow;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;

#[Title('Termination Management')]
class TerminationPage extends Component
{
    use WithDataTable, WithNotify, MultiselectEmployeeSearch, WithFileUploads;

    public $editingId = null, $isEditing = false, $show_terminated_data = 0, $selectedDetailId, $status, $departmentId, $branchId, $companyId, $regionId, $use_date_filter = true;

    public $startDate, $endDate, $currentYear, $currentMonth, $employee_id, $nep_termination_request_date, $termination_request_date, $nep_termination_date, $termination_date, $termination_reason, $termination_type, $employee_name, $verifier_id;

    public $model, $documents, $terminationType, $filter_termination_type, $existingDocument, $removingDocumentId = [];

    public function __construct()
    {
        $this->model = new EmployeeTermination;
    }

    public function mount()
    {
        if (!scopeAll() && (scopeCompany() || scopeBranch())) {
            $this->companyId = currentEmployee()?->company_id;
        }
        $this->scopeWiseFilters();
        $this->singleSelectAttributes = ['employee_id'];
        $this->setPermissionForEmployeeDropdown();
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->perPage = 10;
        $this->setDate();
        $this->terminationType = Constant::TERMINATION_TYPES;
    }

    public function validationAttributes()
    {
        return [
            'employee_id' => 'employee name',
            'termination_request' => 'reason',
            'verifier_id' => 'verifier',
            'termination_request_date' => 'notice date',
            'termination_type' => 'termination type',
            'termination_date' => "last working day"
        ];
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }
        if (!scopeAll()) {
            $this->companyId = currentEmployee()?->company_id;
            if (scopeCompany())
                return;
            $this->regionId = currentEmployee()?->organizationInfo?->region_id;

            if (scopeRegion())
                return;
            $this->branchId = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;
            $this->departmentId = currentEmployee()?->organizationInfo?->department_id;
        }
    }

    public function rules()
    {
        $rules = [
            'employee_id' => ['required'],
            'verifier_id' => ['required'],
            'termination_request_date' => ['required'],
            'termination_reason' => ['required'],
            'termination_type' => ['required'],
            // 'document' => ['file', 'mimes:pdf,doc,docx,png,jpg,jpeg,webp', 'max:2048'],
        ];

        $terminationDateRule = [];

        $terminationDateRule = [
            'termination_date' => [
                'required',
                function ($attribute, $value, $fail) {
                    $termination_date = $value;
                    $termination_date = LaravelNepaliDate::from($value)->toEnglishDate();

                    if ($termination_date <= today()) {
                        $fail('Termination date must be greater than today.');
                    }
                }
            ],
        ];

        if (auth()->user()->can(PermissionList::EMPLOYEE_PAST_TERMINATION_DATE_ADD)) {
            $terminationDateRule = [
                'termination_date' => ['required'],
            ];
        }

        $rules = [
            ...$rules,
            ...$terminationDateRule
        ];

        return $rules;
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function showDetail(int $id): void
    {
        $this->selectedDetailId = $id;
    }

    public function save()
    {
        $this->validate();
        $engTerminationRequestDate = LaravelNepaliDate::from($this->termination_request_date)->toEnglishDate();
        $engTerminationDate = LaravelNepaliDate::from($this->termination_date)->toEnglishDate();
        DB::beginTransaction();
        try {
            $data = [
                'employee_id'                   => $this->employee_id,
                'termination_request_date'      => $engTerminationRequestDate,
                'nep_termination_request_date'  => $this->termination_request_date,
                'termination_date'              => $engTerminationDate,
                'nep_termination_date'          => $this->termination_date,
                'termination_reason'            => $this->termination_reason,
                'termination_type'              => $this->termination_type,
            ];

            if ($this->isEditing) {
                $terminationExist = EmployeeTermination::where([
                    ['employee_id', $this->employee_id],
                    ['employee_id', '!=', $this->editingId]
                ])
                    ->whereIn('state', [WorkflowState::SUBMITTED, WorkflowState::APPROVED])
                    ->exists();
                if ($terminationExist) {
                    $this->addError('employee_id', 'The employee is already submitted for termination.');
                    return;
                }
                $termination = $this->model->where('employee_id', $this->editingId)->first();
                if (!$termination) {
                    $this->notify("Termination not found.")->type("error")->send();
                }
                $termination->fill($data);

                $termination->save();
                $ticketRepo = new TicketRepository;
                $ticketRepo->updateRequestTicket($termination, [
                    'employee_id' => $this->employee_id,
                    'current_owner_id' => $this->verifier_id,
                ]);
                $ticketRepo->addAndUpdateDocuments(
                    $termination->requestTicket,
                    $this->documents ? [$this->documents] : [],
                    $this->removingDocumentId
                );

                $this->message = 'Termination edited successfully!!';
            } else {
                $terminationExist = EmployeeTermination::where([
                    ['employee_id', $this->employee_id]
                ])
                    ->whereIn('state', [WorkflowState::SUBMITTED, WorkflowState::APPROVED])
                    ->exists();
                if ($terminationExist) {
                    $this->addError('employee_id', 'The employee is already submitted for termination.');
                    return;
                }
                $verifierIds = (new EmployeeTermination)->getNextOwners($this->employee_id, true)->map(fn($verifier) => $verifier->id)->toArray();
                if (!in_array($this->verifier_id, $verifierIds)) {
                    $this->addError('verifier_id', 'Invalid next owner');
                    Log::error("Invalid verifier: " . $this->verifier_id);
                    return;
                }
                // $data = $this->only(['employee_id', 'termination_request_date', 'nep_termination_request_date', 'nep_termination_date', 'termination_date', 'termination_reason', 'termination_type', 'documents', 'verifier_id']);
                $employeeTerminationRequest = EmployeeTermination::create($data);
                $ticketRepo = new TicketRepository;
                $ticketRepo->createRequestTicket($employeeTerminationRequest, [
                    'employee_id' => $this->employee_id,
                    'current_owner_id' => $this->verifier_id,
                    'documents' => $this->documents ? [$this->documents] : [],
                ]);

                $this->message = 'Termination added successfully!!';
            }
            DB::commit();
            unset($this->list);
            $this->dispatch('unsetEmployeeDropdownList');

            $this->dispatch('hide-termination-modal');
            $this->notify($this->message)->send();
        } catch (\Exception $e) {
            DB::rollBack();
            \logError("Error while requesting to save termination data: ", $e);
            $this->notify("Error while processing transaction")->type("error")->send();
        }
    }

    public function cancel(int $id)
    {
        try {
            $repo = new TerminationRepository();
            $repo->cancelTermination($id);
            
            $this->notify("Termination ticket has been cancelled")->send();
            unset($this->list);
            $this->dispatch('unsetEmployeeDropdownList');

        } catch (\Exception $e) {
            $this->notify($e->getMessage())->type("error")->send();
        }

    }

    public function updated($attr)
    {
        if ($attr === 'termination_request_date') {
            if ($this->termination_request_date) {
                $terminationRequestDate = LaravelNepaliDate::from($this->termination_request_date)->toEnglishDate();
                $this->termination_date = LaravelNepaliDate::from(Carbon::parse(time: $terminationRequestDate)->addMonth()->format('Y-m-d'))->toNepaliDate();
            }
        }

        if (in_array($attr, ['startDate', 'endDate'])) {
            $this->validate([
                'startDate' => 'required',
                'endDate' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Convert startDate and endDate to English dates
                        $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
                        $endDate = LaravelNepaliDate::from($value)->toEnglishDate();

                        // Check if startDate is greater than endDate
                        if ($startDate > $endDate) {
                            $fail("End date should be greater than start date.");
                            return; // Stop further execution if validation fails
                        }

                        // Max difference check
                        $maxDifference = 32;
                        $startDate = Carbon::parse($startDate);
                        $endDate = Carbon::parse($endDate);
                        $totalDays = $endDate->diffInDays($startDate) + 1; // +1 for inclusive difference

                        // Check if the total days exceed the max allowed difference
                        if ($totalDays > $maxDifference) {
                            $fail("The difference between start date and end date cannot be greater than {$maxDifference} days.");
                        }
                    }
                ],
            ]);
        }

        if (in_array($attr, ['status', 'branchId', 'departmentId', 'show_terminated_data'])) {
            unset($this->list);
        }
        if (in_array($attr, ['companyId'])) {
            unset($this->branches, $this->departments);
            $this->branchId = null;
            $this->departmentId = null;
        }

        if ($attr == "use_date_filter") {
            if (!$this->use_date_filter) {
                $this->startDate = $this->endDate = null;
            } else {
                $this->setDate();
            }
        }
        $this->resetPage();
    }
    #[Computed()]
    public function hasData()
    {
        return ($this->listQuery()->exists());
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['employee_id', 'termination_request_date', 'termination_date', 'termination_reason', 'documents', 'termination_type', 'isEditing', 'employee_name', 'verifier_id', 'selectedDetailId']);
        $this->resetErrorBag();
        $this->dispatch("toggle-employee-id", ['']);
        $this->dispatch("clear-document");
        unset($this->verifiers);
    }

    public function fillFormModel($id)
    {
        $row = $this->model::with('requestTicket')->where('employee_id', $id)->firstOrFail();
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->termination_request_date = $row->nep_termination_request_date ?? LaravelNepaliDate::from($this->termination_request_date)->toNepaliDate();
        $this->termination_date = $row->nep_termination_date ?? LaravelNepaliDate::from($this->termination_date)->toNepaliDate();
        $this->employee_name = EmployeeOrg::where('employee_id', $id)->leftJoin('employees', 'employees.id', '=', 'employee_org.employee_id')->select(
            DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name")
        )->first()?->full_name;
        $this->dispatch("toggle-employee-id", [$row->employee_id]);
        $this->verifier_id = $row->requestTicket?->current_owner_id;
        $this->existingDocument = $row->requestTicket->getDocuments();
    }

    #[On("employee-dropdown-select")]
    public function setTerminatedEmployee(Employee $employee)
    {
        $this->employee_id = $employee->id;
        unset($this->verifiers);
    }


    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }
    public function listQuery()
    {
        $terminatedDate = $this->show_terminated_data;
        $query = EmployeeOrg::when($terminatedDate, function ($query) {
            $query->withTrashed()
                ->where(function ($query) {
                    $query->whereNotNull('employee_org.termination_request_date')
                        ->orWhereNotNull('employee_org.termination_date')
                        ->orWhereNotNull('employee_org.termination_reason');
                });
        })
            ->join('employee_terminations', 'employee_terminations.employee_id', '=', 'employee_org.employee_id')
            ->leftJoin('employees', 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin('companies', 'companies.id', '=', 'employees.company_id')
            ->leftJoin("branches as branch", "branch.id", "=", "employee_org.branch_id")
            ->leftJoin('departments', 'employee_org.department_id', '=', 'departments.id')
            ->when($this->status, fn($q) => $q->where('employee_terminations.state', $this->status))
            ->when($this->branchId, fn($q) => $q->where('employee_org.branch_id', $this->branchId))
            ->when($this->departmentId, fn($q) => $q->where('employee_org.department_id', $this->departmentId))
            ->whereNull('employee_terminations.deleted_at')
            ->where(function ($query) {
                $query->Where(DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%')
                    ->orWhere(DB::raw("CONCAT_WS(' ', employees.first_name, employees.last_name)"), 'LIKE', '%' . $this->search . '%');
            })
            ->when($this->companyId, function ($query) {
                $query->where("employees.company_id", $this->companyId);
            })
            ->when($this->startDate && $this->endDate, function ($query) {
                $query->whereBetween('employee_terminations.nep_termination_date', [
                    $this->startDate,
                    $this->endDate
                ]);
            })
            ->when($this->filter_termination_type, fn($q) => $q->where('employee_terminations.termination_type', $this->filter_termination_type))
            ->select(
                'employee_terminations.id as id',
                'employee_terminations.employee_id as employee_id',
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name"),
                DB::raw("CONCAT(companies.code,'-',employee_code) as code"),
                'branch.name as branch_name',
                'departments.name as department_name',
                'employee_terminations.termination_request_date as termination_request_date',
                'employee_terminations.nep_termination_request_date as nep_termination_request_date',
                'employee_terminations.termination_reason as termination_reason',
                'employee_terminations.termination_type as termination_type',
                'employee_terminations.termination_date as termination_date',
                'employee_terminations.nep_termination_date as nep_termination_date',
                'employee_terminations.state as state',
                'employee_terminations.created_at'
            );

        $query->orderBy('employee_terminations.created_at', 'desc');
        $query = filterEmployeesByScope($query, 'employee_org', 'employees');
        return $query;
    }

    public function exportEmployees($exportAll = false)
    {
        $this->setExcelOptions();
        if (!$this->hasData) {
            $this->notify("No Data Found to Export!.")->type("error")->send();
            return;
        }
        if ($exportAll) {
            $data = $this->listQuery()->get();
        } else {
            $data = $this->{$this->tableListVariable}(); // Retrieve the data from the computed property

            if ($data instanceof LengthAwarePaginator) {
                $data = $data->getCollection();
            }
        }

        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);

                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];
                // Provide headings for your export file
                // You can customize this based on your data structure
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return $headings;
            }
        }, $this->exportFileName);
    }
    public function setExcelOptions()
    {
        $this->exportFileName = "Termination Employee List.xlsx";
        $this->exportIgnoreColumns = ['id', 'employee_id', 'created_at', 'deleted_at'];
        $this->exportColumnHeadersMap = [
            'code' => 'Employee Code',
            'full_name' => 'Employee Name',
            'branch_name' => 'Branch',
            'department_name' => 'Department',
            'termination_request_date' => 'Notice Date [AD]',
            'nep_termination_request_date' => 'Notice date [BS]',
            'termination_reason' => 'Termination Reason',
            'termination_type' => 'Termination Type',
            'termination_date' => 'Termination Date [AD]',
            'nep_termination_date' => 'Termination Date [BS]',
            'state' => 'Status',
        ];
    }


    #[Computed(persist: true)]
    public function statusList()
    {
        return ArFlow::getStates(WorkflowName::TERMINATION_APPROVAL);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->companyId);

            if (!is_null($this?->regionId)) {
                $query->where('region_id', $this->regionId);
            }

            return $query->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        }
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return Department::where('company_id', $this->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[On('refresh-list')]
    public function refreshList()
    {
        unset($this->list);
    }

    #[Computed()]
    public function verifiers()
    {
        if (!$this->employee_id)
            return [];
        return (new EmployeeTermination)->getNextOwners(employeeId: $this->employee_id, isSubmitting: true);
    }

    public function terminationType()
    {
        return Constant::TERMINATION_TYPES;
    }

    public function render()
    {
        return view('livewire.hr-admin.termination.termination-page');
    }

    private function setDate()
    {
        $today = Carbon::today();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth] = explode('-', $nepaliDate);

        $this->startDate = "{$currentYear}-{$currentMonth}-01";
        $this->endDate = $nepaliDate;
    }
}
