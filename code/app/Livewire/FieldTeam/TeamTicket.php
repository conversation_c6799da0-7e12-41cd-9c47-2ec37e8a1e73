<?php

namespace App\Livewire\FieldTeam;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Configs\NotificationRepository;
use App\Http\Repositories\FieldTeam\TeamTicket\Interfaces\TeamOtRequestRepositoryInterface;
use App\Models\Employee\Employee;
use App\Models\User;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Add Team Ticket')]
class TeamTicket extends Component
{
    use WithNotify;
    public $teamIds = [];
    public $currentDate;
    public $startTime;
    public $endTime;
    public $verifier_id;
    public $assignVerifiers = [];
    public $confirmed = false;
    public $suggestedStartTime;
    public $suggestedEndTime;
    public $remarks;

    public $teamAttendance = []; // Will store attendance per team

    protected TeamOtRequestRepositoryInterface $ticketRepo;

    public function boot(TeamOtRequestRepositoryInterface $ticketRepo)
    {
        $this->ticketRepo = $ticketRepo;
    }

    public function mount()
    {
        $context = session('ticketContext', []);
        $this->teamIds = $context['teamIds'] ?? [];
        $this->currentDate = $context['date'] ?? now()->toDateString();
    }

    public function confirmTicket()
    {
        $this->validate([
            'startTime' => 'required|date_format:H:i',
            'endTime'   => 'required|date_format:H:i|after:startTime',
        ]);

        try {
            $this->teamAttendance = $this->ticketRepo->confirmTeamTickets(
                $this->teamIds,
                $this->currentDate,
                $this->startTime,
                $this->endTime,
            );

            foreach ($this->teamAttendance as $teamId => $teamData) {
                $leader = $teamData['leader'];
                if ($leader && $leader->in_time && $leader->out_time) {
                    $this->suggestedStartTime = Carbon::parse($leader->in_time)->format('H:i');
                    $this->suggestedEndTime = Carbon::parse($leader->out_time)->format('H:i');
                } else {
                    $this->suggestedStartTime = '09:30';
                    $this->suggestedEndTime = '17:30';
                }
            }

            $this->assignVerifiers = $this->ticketRepo->getAssignableVerifiers();
            $this->confirmed = true;
        } catch (\Throwable $th) {
            Log::error("Error confirming ticket: " . $th->getMessage(), [
                'trace' => $th->getTraceAsString(),
            ]);
            $this->addError('submit_error', 'Failed to confirm ticket. Please try again.');
            $this->notify('Failed to confirm ticket. Please try again.')->type('error')->send();
        }
    }

    public function removeMember($teamId, $index)
    {
        if (isset($this->teamAttendance[$teamId]['members'][$index])) {
            $this->teamAttendance[$teamId]['members']->forget($index);
            // Reindex collection to keep indexes sequential
            $this->teamAttendance[$teamId]['members'] = $this->teamAttendance[$teamId]['members']->values();
        }
    }

    public function submitTicket()
    {
        $this->validate([
            'startTime' => 'required|date_format:H:i',
            'endTime'   => 'required|date_format:H:i|after:startTime',
            'verifier_id' => 'required|exists:employees,id',
            'remarks' => 'required',
        ]);

        try {
            $response = $this->ticketRepo->submitOtTickets(
                $this->teamAttendance,
                $this->startTime,
                $this->endTime,
                $this->currentDate,
                $this->verifier_id,
                $this->remarks
            );

            if ($response['type'] === 'error') {
                $this->notify($response['message'])->type('error')->send();
                return;
            } else {
                foreach ($this->teamAttendance as $teamId => $teamData) {
                    $leader = $teamData['leader'];
                    $members = $teamData['members'];
                }

                try {
                    $notificationRepo = app(NotificationRepository::class);

                    foreach ($this->teamAttendance as $teamId => $teamData) {
                        // Notify leader
                        $leader = $teamData['leader'];
                        if (!empty($leader ?? null)) {
                            $userIds = Employee::where('id', $leader->id)->pluck('user_id')->toArray();
                            $employeesToNotify = User::whereIn('id', $userIds)->get();
                            $url = route('ticketPage', [
                                'workflow' => WorkflowName::TEAM_OT_TICKET,
                                'requestId' => $response['ticketIds'],
                            ]);

                            foreach ($employeesToNotify as $employee) {
                                $notificationRepo->createNotification(
                                    $employee,
                                    'Team OT',
                                    "Your OT for team '{$teamData['team']['name']}' has been recorded. Please verify.",
                                    $url,
                                    'View',
                                    []
                                );
                            }
                        }

                        // Notify members
                        foreach ($teamData['members'] as $member) {
                            if (!empty($member->id ?? null)) {
                                $userIds = Employee::where('id', $member->id)->pluck('user_id')->toArray();
                                $employeesToNotify = User::whereIn('id', $userIds)->get();

                                foreach ($employeesToNotify as $employee) {
                                    $url = route('ticketPage', [
                                        'workflow' => WorkflowName::TEAM_OT_TICKET,
                                        'requestId' => $response['ticketIds'],
                                    ]);

                                    $notificationRepo->createNotification(
                                        $employee,
                                        'Team OT',
                                        "Your OT for team '{$teamData['team']['name']}' has been recorded. Please verify.",
                                        $url,
                                        'View',
                                        []
                                    );
                                }
                            }
                        }
                    }
                } catch (\Throwable $th) {
                    Log::error("Error sending notification: " . $th->getMessage(), [
                        'trace' => $th->getTraceAsString(),
                    ]);
                }
                session()->forget('ticketContext');
                $this->notify($response['message'])->type($response['type'])->send();
                return redirect()->route('fieldTeamAttendance');
            }
        } catch (ValidationException $e) {
            $this->notify($e->getMessage())->type('error')->send();
            $this->addError('submit_error', $e->getMessage());
        }
    }


    public function render()
    {
        return view('livewire.field-team.team-ticket');
    }
}
