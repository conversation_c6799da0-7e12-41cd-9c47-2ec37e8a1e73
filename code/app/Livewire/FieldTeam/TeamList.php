<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface;
use App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface;
use App\Models\FieldTeam\TeamList as FieldTeamTeamList;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Team List')]
class TeamList extends Component
{
    use WithDataTable;
    public $team_type, $transport_type, $branch_id, $operation_center, $companyId, $regionId;
    protected TeamListRepositoryInterface $teamListRepo;
    protected TeamRepositoryInterface $teamRepo;
    public function boot(TeamListRepositoryInterface $teamListRepo, TeamRepositoryInterface $teamRepo)
    {
        $this->teamListRepo = $teamListRepo;
        $this->teamRepo = $teamRepo;
    }

    public function mount()
    {
        $this->companyId = currentEmployee()?->company_id;
        $this->regionId = currentEmployee()?->organizationInfo?->region_id;
    }
    #[Computed(persist: true)]
    public function list()
    {
        $allowedBranchIds = $this->branches()->pluck('id')->toArray();
        return  $this->applySorting(
            $this->teamListRepo->teamList($this->search, $this->team_type, $this->transport_type, $this->branch_id?: $allowedBranchIds, $this->operation_center, null)
        )->paginate($this->perPage);
    }

    public function edit($id)
    {
        return redirect()->route('fieldTeamEdit', parameters: $id);
    }

    public function model()
    {
        return FieldTeamTeamList::class;
    }

    public function toggleStatus($id)
    {
        $response = $this->teamListRepo->toggleStatus($id);
        if ($response) {
            $this->notify('Team status toggled successfully')->send();
            unset($this->list);
        } else {
            $this->notify('Failed to toggle team status')->type('error')->send();
            unset($this->list);
        }
    }


    #[Computed(persist: true)]
    public function teamTypes()
    {
        return $this->teamRepo->teamTypes();
    }
    #[Computed(persist: true)]
    public function transportTypes()
    {
        return $this->teamRepo->transportTypes();
    }
    #[Computed(persist: true)]
    public function branches()
    {
        return $this->teamRepo->branches($this->companyId, $this->regionId);
    }

    #[Computed]
    public function operationCenter()
    {
        if (!$this->branch_id) return [];
        return $this->teamRepo->operationCenters($this->branch_id);
    }

    public function updated($attr)
    {
        if (in_array($attr, ['team_type', 'transport_type', 'branch_id', 'operation_center'])) {
            unset($this->list);
        }
    }

    public function view($id)
    {
        return redirect()->route('fieldTeamView', $id);
    }
    public function render()
    {
        return view('livewire.field-team.team-list');
    }
}
