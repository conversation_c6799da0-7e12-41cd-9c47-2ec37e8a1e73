<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Add Team')]
class TeamAdd extends Component
{
    use WithNotify;
    public $isEditing = false;

    public $is_active = true, $showTeamMemberDropdown = false, $multipleEmployeeInTransport = true;
    public $team_name, $team_leader, $team_type, $transport_type, $branch_id, $operation_center_id, $companyId, $regionId;
    public $team_members = [];
    public int $transportSeats = 0;

    public array $teamLeaderList = [], $teamMemberList = [];

    protected TeamRepositoryInterface $teamRepo;

    public function boot(TeamRepositoryInterface $teamRepo)
    {
        $this->teamRepo = $teamRepo;
    }
    public function rules()
    {
        $allowedMembers = max(0, $this->transportSeats - 1);

        return [
            'team_name' => 'required|string|max:100',
            'team_leader' => 'required|exists:employees,id',
            'team_type' => 'required|exists:field_team_types,id',
            'transport_type' => 'required|exists:field_transport_types,id',
            'branch_id' => 'required|exists:branches,id',
            'team_members' => ['required', 'array', 'max:' . $allowedMembers],
            'team_members.*' => 'exists:employees,id',
        ];
    }

    public function mount()
    {
        $this->companyId = currentEmployee()?->company_id;
        $this->regionId = currentEmployee()?->organizationInfo?->region_id;
        $this->setTeamLeaderList();
        // $this->setTeamMemberList();
    }

    public function setTeamLeaderList()
    {
        $this->teamLeaderList = $this->teamRepo->getEmployeeList()->toArray();
        $this->dispatch('updateMultiselectItem', ["id" => "employee-dropdown-teamleader", "items" => $this->teamLeaderList]);
    }

    public function setTeamMemberList()
    {
        $list = collect($this->teamRepo->getEmployeeList())
            ->filter(fn($employee) => $employee['value'] != $this->team_leader)
            ->map(fn($employee) => [
                'value' => $employee['value'],
                'label' => $employee['label'],
            ])
            ->values()
            ->toArray();

        $this->teamMemberList = $list;
        $this->dispatch('updateMultiselectItem', ["id" => "employee-dropdown-teammember", "items" => $this->teamMemberList]);
    }

    public function updated($attr)
    {
        if ($attr == 'team_leader') {
            if ($this->team_leader) {
                $this->setTeamMemberList();
                $this->showTeamMemberDropdown = true;
            }
        }
        if ($attr === 'transport_type') {
            $this->checkTransportSeatCount();
        }
        // if ($attr == 'team_members') {
        //     if (is_array($this->team_members) && count($this->team_members)) {
        //         $this->setTeamMemberList();
        //     }
        // }
    }

    protected function messages()
    {
        return [
            'team_members.size' => "Only " . max(0, $this->transportSeats - 1) . " team member(s) for the selected transport type.",
        ];
    }


    public function save()
    {
        $this->validate();
        DB::beginTransaction();
        try {
            $this->teamRepo->create([
                'name' => $this->team_name,
                'team_type' => $this->team_type,
                'transport_type' => $this->transport_type,
                'branch_id' => $this->branch_id,
                'operation_center' => $this->operation_center_id,
                'created_by' => auth()->id(),
                'is_active' => $this->is_active,
                'team_leader' => $this->team_leader,
                'team_members' => $this->team_members,
                'start_date' => Carbon::now()->toDateString(),
            ]);

            DB::commit();
            $this->reset();
            $this->notify('Team created successfully')->send();
            return redirect()->route('fieldTeamList');
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->notify('Failed to create team.')->type('error')->send();
            logError($e->getMessage());
        }
    }

    public function clear()
    {
        $this->reset(['team_name', 'team_leader', 'team_type', 'transport_type', 'branch_id', 'team_members']);
    }

    #[Computed(persist: true)]
    public function teamTypes()
    {
        return $this->teamRepo->teamTypes();
    }
    #[Computed(persist: true)]
    public function transportTypes()
    {
        return $this->teamRepo->transportTypes();
    }
    #[Computed(persist: true)]
    public function branches()
    {
        return $this->teamRepo->branches($this->companyId, $this->regionId);
    }

    #[Computed]
    public function operationCenter()
    {
        if (!$this->branch_id) return [];
        return $this->teamRepo->operationCenters($this->branch_id);
    }

    public function checkTransportSeatCount()
    {
        if (!$this->transport_type) {
            $this->multipleEmployeeInTransport = false;
            return;
        }

        $transport = $this->teamRepo->getTransportTypeById($this->transport_type);

        if ($transport) {
            $this->transportSeats = $transport->total_number_of_seats;
            $this->multipleEmployeeInTransport = $this->transportSeats > 2;
        } else {
            $this->transportSeats = 0;
            $this->multipleEmployeeInTransport = false;
        }
    }

    public function render()
    {
        return view('livewire.field-team.team-add');
    }
}
