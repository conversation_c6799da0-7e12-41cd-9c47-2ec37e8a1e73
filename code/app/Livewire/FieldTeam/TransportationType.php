<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\TransportationTypes\Interfaces\TransportationTypeRepositoryInterface;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Transportation Types')]
class TransportationType extends Component
{
    use WithDataTable, WithNotify;

    public $name = '';
    public $is_active = true;
    public $total_number_of_seats = 0;
    public $editingId = null;
    public $isEditing = false;
    public $message = null;
    public $model;

    protected TransportationTypeRepositoryInterface $transportTypeRepo;

    public function boot(TransportationTypeRepositoryInterface $transportTypeRepo)
    {
        $this->transportTypeRepo = $transportTypeRepo;
    }

    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:100',
                Rule::unique('field_transport_types', 'name')->ignore($this->editingId),
            ],
            'is_active' => 'required|boolean',
            'total_number_of_seats' => 'required|numeric|min:1',
        ];
    }

    public function save()
    {
        $this->validate();
        try {
            $data = $this->only(['name', 'is_active', 'total_number_of_seats']);

            if ($this->editingId) {
                $this->transportTypeRepo->update($this->editingId, $data);
                $this->notify('Transportation type updated successfully')->send();
            } else {
                $this->transportTypeRepo->create($data);
                $this->notify('Transportation type saved successfully')->send();
            }

            $this->dispatch('hide-model');
            unset($this->list);
            $this->reset();
        } catch (\Exception $e) {
            logError($e->getMessage());
            $this->notify('Failed to save transportation type')->type('error')->send();
        }
    }
    public function edit($id)
    {
        $this->isEditing = true;
        $this->editingId = $id;

        $row = $this->transportTypeRepo->find($id);

        $this->name = $row?->name;
        $this->total_number_of_seats = $row?->total_number_of_seats;
        $this->is_active = $row?->is_active;
    }

    public function delete($id)
    {
        try {
            $message = $this->transportTypeRepo->delete($id);

            unset($this->list);
            $this->notify($message)->send();
        } catch (\Throwable $e) {
            $this->notify($e->getMessage())->type('error')->send();
            logError($e->getMessage());
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(
            $this->transportTypeRepo->all($this->search)
        )->paginate($this->perPage);
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'is_active', 'editingId', 'isEditing']);
        $this->resetErrorBag();
    }
    public function render()
    {
        return view('livewire.field-team.transportation-type');
    }
}
