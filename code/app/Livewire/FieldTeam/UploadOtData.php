<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface;
use App\Traits\WithNotify;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

#[Title('Upload Ot Data')]
class UploadOtData extends Component
{
    use WithFileUploads, WithNotify;

    protected TeamRepositoryInterface $teamRepo;
    public function boot(TeamRepositoryInterface $teamRepo)
    {
        $this->teamRepo = $teamRepo;
    }

    public $excelHeaders = [
        "team name" => 'Team A',
        "team type" => 'Support',
        "transport type" => 'Car',
        "team leader name" => '<PERSON>',
        "team leader code" => 'CB-0001',
        "member names" => '<PERSON>, <PERSON>',
        "member codes" => 'CB-0002, CB-0003',
        "branch" => 'Remote Site A',
        "operation center" => 'Remote Site A',
        "start date" => 'yyyy-mm-dd',
    ];

    public $optionalExcelHeaders = [];

    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $otExcelFile = "";
    public $excelValidationMessage = "";

    public function updatedOtExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'excelValidationMessage', 'excelUploaded');
        $this->excelData = [];
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray((object)[], $this->otExcelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        try {
            foreach (array_keys($this->excelHeaders) as $header) {
                if (!in_array(strtolower($header), $headers)) {
                    $this->notify("Missing required column: '{$header}'")->type("error")->send();
                    return false;
                }
            }
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }

        try {
            $data = transformArrayToAssociative($data[0], ['date from', 'date to', 'replaced date from', 'replaced date to']);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function validateExcelData()
    {
        $this->excelData = [];
        $dataValid = true;
        if (!($data = $this->validateFile())) {
            return;
        }
        try {
            $validationResults = $this->teamRepo->validateOtDataForExcel($data);
            foreach ($validationResults as $index => $result) {
                $errorMessage = "";
                if (!$result['status']) {
                    $dataValid = false;
                    $errorMessage = $result['message'];
                }
                $this->excelData[] = ['data' => $data[$index], 'error' => $errorMessage];
            }
            $this->excelDataValidated = $dataValid;
            if (!$this->excelDataValidated) $this->notify("Some data are not validated")->type("error")->send();
            else $this->notify("All data are validated. You can proceed to upload the data")->type("success")->send();
        } catch (\Exception $e) {
            logError("Error creating field team ot from excel", $e);
            $this->notify("Error validating fied team ot: " . $e->getMessage())->type("error")->send();
        }
    }


    public function uploadData()
    {
        if (!$this->excelDataValidated) {
            return $this->notify("Excel validation failed");
        }

        $data = collect($this->excelData)->pluck('data')->all();

        $response = $this->teamRepo->createFieldTeamFromExcel($data);

        if (!empty($response['data']))
            $this->excelValidationMessage = $response['data']['validation'];

        if (!$response['status']) {
            $this->notify($response['message'])->type('error')->duration(10)->send();
            return;
        }

        $this->excelUploaded = true;
        return $this->notify("Field Team Ot Data Uploaded Successfully")->send();
    }
    
    public function render()
    {
        return view('livewire.field-team.upload-ot-data');
    }
}
