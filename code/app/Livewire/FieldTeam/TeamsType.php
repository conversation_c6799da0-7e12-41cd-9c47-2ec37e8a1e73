<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\TeamTypes\Interfaces\TeamTypeRepositoryInterface;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Team Types')]
class TeamsType extends Component
{
    use WithDataTable, WithNotify;

    public $name = '';
    public $is_active = true;
    public $editingId = null;
    public $isEditing = false;
    public $message = null;
    public $model;

    protected TeamTypeRepositoryInterface $teamTypeRepo;

    public function boot(TeamTypeRepositoryInterface $teamTypeRepo)
    {
        $this->teamTypeRepo = $teamTypeRepo;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:100',
            'is_active' => 'required|boolean',
        ];
    }
    public function save()
    {
        $this->validate();
        try {
            $data = $this->only(['name', 'is_active']);

            if ($this->editingId) {
                $this->teamTypeRepo->update($this->editingId, $data);
                $this->notify('Team type updated successfully')->send();
            } else {
                $this->teamTypeRepo->create($data);
                $this->notify('Team type saved successfully')->send();
            }

            $this->dispatch('hide-model');
            unset($this->list);
        } catch (\Exception $e) {
            $this->notify('Failed to save team type')->type('error')->send();
            logError($e->getMessage());
        }
    }
    public function edit($id)
    {
        $this->isEditing = true;
        $this->editingId = $id;

        $row = $this->teamTypeRepo->find($id);

        $this->name = $row?->name;
        $this->is_active = $row?->is_active;
    }

    public function delete($id)
    {
        try {
            $this->teamTypeRepo->delete($id);
            unset($this->list);
            $this->notify('Team type deleted successfully')->send();
        } catch (\Throwable $e) {
            $this->notify('Failed to delete team type')->type('error')->send();
            logError($e->getMessage());
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(
            $this->teamTypeRepo->all($this->search)
        )->paginate($this->perPage);
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'is_active', 'editingId', 'isEditing']);
        $this->resetErrorBag();
    }
    public function render()
    {
        return view('livewire.field-team.teams-type');
    }
}
