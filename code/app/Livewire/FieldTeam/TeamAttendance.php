<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface;
use App\Models\Employee\Employee;
use App\Models\FieldTeam\TeamList;
use App\Models\FieldTeam\TeamMembers;
use App\Models\FieldTeam\TemporaryTeamMembers;
use App\Traits\WithDataTable;
use App\Traits\WithRowSelect;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Support\Collection;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Team Attendance')]
class TeamAttendance extends Component
{
    use WithRowSelect;
    public $selectedDate, $branch_id, $operation_center, $companyId, $regionId;
    public $permanentMembers = [], $temporaryMembers = [];
    public $teamName = '';
    public $currentTeamLeaderAttendance = null;
    public $selectAllData = false;

    public $teamLeaderAttendances = [];
    protected TeamListRepositoryInterface $teamRepo;

    public function boot(TeamListRepositoryInterface $teamRepo)
    {
        $this->teamRepo = $teamRepo;
    }

    public function mount()
    {
        $this->selectedDate = LaravelNepaliDate::getToday();
        $this->loadTeamLeaderAttendance();
        $this->companyId = currentEmployee()?->company_id;
        $this->regionId = currentEmployee()?->organizationInfo?->region_id;

    }

    public function updated($attr)
    {
        if (in_array($attr, ['selectedDate', 'branch_id', 'operation_center'])) {
            unset($this->list);
            $this->loadTeamLeaderAttendance();
        }
    }
    public function updatedSelectAllData()
    {
        if ($this->selectAllData) {
            $this->selectedRows = $this->list()->pluck('id')->toArray();
        } else {
            $this->selectedRows = [];
        }
    }
    public function createTicket()
    {
        if (!count($this->selectedRows)) {
            return $this->notify("Please select at least one team")->type('error')->send();
        }
        session()->put('ticketContext', [
            'date' => $this->selectedDate,
            'teamIds' => $this->selectedRows,
        ]);
        $this->redirect(route('fieldTeamTicket'));
    }

    #[Computed(persist: true)]
    public function list()
    {
        $allowedBranchIds = $this->branches()->pluck('id')->toArray();
        return  $this->applySorting(
            $this->teamRepo->teamList($this->search, null, null, $this->branch_id?: $allowedBranchIds, $this->operation_center, $this->selectedDate)
        )->paginate($this->perPage);
    }

    public function loadTeamLeaderAttendance()
    {
        $teams = $this->list()->getCollection(); // Only current page
        $this->teamLeaderAttendances = [];

        foreach ($teams as $team) {
            $matchingOt = $team->otRequests->firstWhere('nep_date', $this->selectedDate);
            $this->teamLeaderAttendances[$team->id] =
                $this->teamRepo->getTeamLeaderAttendance($team->id, $this->selectedDate);
        }
    }

    public function showAllMembers(int $teamId)
    {
        $this->teamName = TeamList::findOrFail($teamId)->name;

        $allMembers = $this->teamRepo->getAllTeamMembers($teamId, $this->selectedDate);
        $permanent = $allMembers->get('permanent', collect());
        $temporary = $allMembers->get('temporary', collect());

        $membersAttendance = $this->teamRepo->getAttendanceForMembers(
            $permanent->merge($temporary),
            $this->selectedDate
        );

        $this->permanentMembers = $permanent->map(function ($member) use ($membersAttendance) {
            $member->attendance = $membersAttendance->firstWhere('employee_id', $member->id);
            return $member;
        });

        $this->temporaryMembers = $temporary->map(function ($member) use ($membersAttendance) {
            $member->attendance = $membersAttendance->firstWhere('employee_id', $member->id);
            return $member;
        });
        $this->currentTeamLeaderAttendance = $this->teamRepo->getTeamLeaderAttendance($teamId, $this->selectedDate);
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return $this->teamRepo->branches($this->companyId, $this->regionId);
    }

    #[Computed]
    public function operationCenter()
    {
        return $this->branch_id
            ? $this->teamRepo->operationCenters($this->branch_id)
            : [];
    }

    public function render()
    {
        return view('livewire.field-team.team-attendance');
    }
}
