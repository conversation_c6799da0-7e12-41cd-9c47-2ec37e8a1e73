<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\Team\Interfaces\TeamRepositoryInterface;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Edit Team')]
class TeamEdit extends Component
{
    use WithNotify, MultiselectEmployeeSearch;
    public $team_name, $team_leader, $team_type, $transport_type, $branch_id, $operation_center_id, $currentDate, $companyId, $regionId;
    public $team_members = [], $allTeamLeaders = [], $allTeamMembers = [];
    public $regular_members = [];
    public $temporary_members = [];

    public int $transportSeats = 0;
    public $isTemporary = false, $multipleEmployeeInTransport = true;
    public $teamDetails;

    protected TeamRepositoryInterface $teamRepo;

    public function boot(TeamRepositoryInterface $teamRepo)
    {
        $this->teamRepo = $teamRepo;
    }
    public function mount($id): void
    {
        $this->teamDetails = \App\Models\FieldTeam\TeamList::with(['teamMembers', 'temporaryTeamMembers'])->findOrFail($id);
        $this->singleSelectAttributes = ['employee-dropdown-teamleaders'];
        $this->multiSelectAttributes = ['employee-dropdown-teamMember-regular', 'employee-dropdown-teamMember-temp'];

        $this->team_name = $this->teamDetails->name;
        $this->team_type = $this->teamDetails->team_type;
        $this->transport_type = $this->teamDetails->transport_type;
        $this->branch_id = $this->teamDetails->branch_id;
        $this->operation_center_id = $this->teamDetails->sub_branch_id;
        $this->currentDate = LaravelNepaliDate::getToday();

        $this->setTeamLeaderList();
        $this->loadTeamMemberData();
        $this->checkTransportSeatCount();
    }

    public function rules()
    {
        $allowedMembers = max(0, $this->transportSeats - 1);

        return [
            'team_leader' => 'required|exists:employees,id',
            'team_type' => 'required|exists:field_team_types,id',
            'transport_type' => 'required|exists:field_transport_types,id',
            'branch_id' => 'required|exists:branches,id',
            'team_members' => ['required', 'array', 'max:' . $allowedMembers],
            'team_members.*' => 'exists:employees,id',
        ];
    }

    protected function messages()
    {
        return [
            'team_members.size' => "Only " . max(0, $this->transportSeats - 1) . " team member(s) for the selected transport type.",
        ];
    }

    public function checkTransportSeatCount()
    {
        if (!$this->transport_type) {
            $this->multipleEmployeeInTransport = false;
            return;
        }

        $transport = $this->teamRepo->getTransportTypeById($this->transport_type);

        if ($transport) {
            $this->transportSeats = $transport->total_number_of_seats;
            $this->multipleEmployeeInTransport = $this->transportSeats > 2;
        } else {
            $this->transportSeats = 0;
            $this->multipleEmployeeInTransport = false;
        }
    }

    public function setTeamLeaderList()
    {
        $this->team_leader = $this->teamDetails->teamMembers->where('is_team_leader', true)->first()->employee_id ?? null;
        $this->allTeamLeaders = $this->teamRepo->getEmployeeList()->toArray();
        $this->dispatch("set-team-leader", [$this->team_leader]);
    }

    /**
     * Load data for both regular and temporary members on mount
     */
    public function loadTeamMemberData()
    {
        $list = collect($this->teamRepo->getEmployeeList())
            ->filter(fn($e) => $e['value'] != $this->team_leader)
            ->values()
            ->toArray();

        $this->allTeamMembers = $list;

        $this->regular_members = $this->teamDetails
            ->teamMembers
            ->where('is_team_leader', false)
            ->pluck('employee_id')
            ->toArray();

        $temporary = $this->teamDetails
            ->temporaryTeamMembers
            ->where('date', $this->currentDate)
            ->first();

        $this->temporary_members = $temporary
            ? json_decode($temporary->member_details, true)
            : [];

        $this->dispatch('updateMultiselectItem', [
            "id" => "employee-dropdown-teamMember-regular",
            "items" => $this->allTeamMembers,
        ]);

        $this->dispatch('updateMultiselectItem', [
            "id" => "employee-dropdown-teamMember-temp",
            "items" => $this->allTeamMembers,
        ]);

        $this->dispatch("toggle-set-team-member-id-regular", $this->regular_members);
        $this->dispatch("toggle-set-team-member-id-temp", $this->temporary_members);
    }

    /**
     * Update team member list when team leader changes
     */
    public function setTeamMemberList()
    {
        $list = collect($this->teamRepo->getEmployeeList())
            ->filter(fn($e) => $e['value'] != $this->team_leader)
            ->values()
            ->toArray();

        $this->allTeamMembers = $list;

        // Always update both dropdowns
        $this->dispatch('updateMultiselectItem', [
            "id" => "employee-dropdown-teamMember-regular",
            "items" => $this->allTeamMembers,
        ]);

        $this->dispatch('updateMultiselectItem', [
            "id" => "employee-dropdown-teamMember-temp",
            "items" => $this->allTeamMembers,
        ]);

        // Re-dispatch the selected values
        $this->dispatch("toggle-set-team-member-id-regular", $this->regular_members);
        $this->dispatch("toggle-set-team-member-id-temp", $this->temporary_members);
    }

    public function updated($attr, $value)
    {
        if ($attr == 'team_leader') {
            if ($this->team_leader) {
                $this->setTeamMemberList();
            }
        }

        if ($attr === 'isTemporary') {
            $this->resetErrorBag();
        }

        if ($attr === 'transport_type') {
            $this->checkTransportSeatCount();
        }
    }

    public function update()
    {
        $members = $this->isTemporary
            ? $this->temporary_members
            : $this->regular_members;

        $this->validate([
            'team_leader' => 'required|exists:employees,id',
            'team_type' => 'required|exists:field_team_types,id',
            'transport_type' => 'required|exists:field_transport_types,id',
            'branch_id' => 'required|exists:branches,id',
            'operation_center_id' => 'nullable',
            'regular_members' => 'array',
            'temporary_members' => 'array',
        ]);

        try {
            if (!$this->isTemporary) {
                $this->teamRepo->update($this->teamDetails->id, [
                    'team_type' => $this->team_type,
                    'transport_type' => $this->transport_type,
                    'branch_id' => $this->branch_id,
                    'operation_center' => $this->operation_center_id,
                    'team_leader' => $this->team_leader,
                    'team_members' => $members,
                    'date' => $this->currentDate,
                ]);
            } else {
                $this->teamRepo->updateTemporary($this->teamDetails->id, [
                    'team_type' => $this->team_type,
                    'transport_type' => $this->transport_type,
                    'branch_id' => $this->branch_id,
                    'operation_center' => $this->operation_center_id,
                    'team_leader' => $this->team_leader,
                    'team_members' => $members,
                    'date' => $this->currentDate,
                ]);
            }
            $this->notify('Team updated successfully')->send();
            return redirect()->route('fieldTeamList');
        } catch (\Throwable $e) {
            $this->notify('Failed to update team.')->type('error')->send();
            logInfo($e->getMessage());
        }
    }

    #[Computed(persist: true)]
    public function teamTypes()
    {
        return $this->teamRepo->teamTypes();
    }

    #[Computed(persist: true)]
    public function transportTypes()
    {
        return $this->teamRepo->transportTypes();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return $this->teamRepo->branches($this->companyId, $this->regionId);
    }

    #[Computed]
    public function operationCenter()
    {
        if (!$this->branch_id) return [];
        return $this->teamRepo->operationCenters($this->branch_id);
    }

    public function render()
    {
        return view('livewire.field-team.team-edit');
    }
}
