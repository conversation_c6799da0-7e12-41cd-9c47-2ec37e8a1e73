<?php

namespace App\Livewire\FieldTeam;

use App\Http\Repositories\FieldTeam\TeamList\Interfaces\TeamListRepositoryInterface;
use App\Models\Employee\Employee;
use App\Models\FieldTeam\TeamList;
use App\Models\FieldTeam\TeamMembers;
use App\Models\FieldTeam\TemporaryTeamMembers;
use App\Traits\WithNotify;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Team Details')]
class TeamDetailsView extends Component
{
    use WithNotify;
    public $teamId;
    public $team;
    public $teamLeader;
    public $teamMembers;
    public $isTemporary = false;
    public $selectedDate;

    protected TeamListRepositoryInterface $teamRepo;

    public function boot(TeamListRepositoryInterface $teamRepo)
    {
        $this->teamRepo = $teamRepo;
    }
    public function mount($id)
    {
        $this->teamId = $id;
        $this->team = TeamList::findOrFail($this->teamId);
        $this->loadTeamMembers();
        $this->selectedDate = LaravelNepaliDate::getToday();
    }

    public function updatedIsTemporary()
    {
        $this->loadTeamMembers();
    }

    private function loadTeamMembers()
    {
        if ($this->isTemporary) {
            $members = $this->teamRepo->getTemporaryMembers($this->teamId, $this->selectedDate);
            $this->teamLeader = $this->teamRepo->getTeamLeader($this->teamId);

            $this->teamMembers = $members->filter(fn($member) => $member->id != $this->teamLeader?->id);
        } else {
            $this->teamLeader = $this->teamRepo->getTeamLeader($this->teamId);
            $this->teamMembers = $this->teamRepo->getPermanentMembers($this->teamId);
        }
    }

    public function updatedSelectedDate()
    {
        $this->loadTeamMembers();
    }

    public function render()
    {
        return view('livewire.field-team.team-details-view');
    }
}
