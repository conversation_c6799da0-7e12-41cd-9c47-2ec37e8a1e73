<?php

namespace App\Livewire\Leaves;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\LeaveDashboardRepository;
use App\Traits\WithDataTable;
use AuroraWebSoftware\ArFlow\Facades\ArFlow;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use App\Models\configs\Company;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\Leaves\LeaveRequest;
use App\Traits\MultiselectEmployeeSearch;
use Livewire\Attributes\Url;
use Livewire\Component;
use Maatwebsite\Excel\Excel;

#[Title('Leave Approval Ticket')]
class LeaveApprovalList extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $selectedId, $startDate, $endDate, $leaveType, $selectedCompany, $selectedBranch, $selectedDepartment;
    public $leaveDetails = [];
    public $employee_ids = [];
    #[Url()]
    public $status = "";

    private LeaveDashboardRepository $repo;

    public function __construct()
    {
        $this->repo = new LeaveDashboardRepository;
    }

    public function mount()
    {
        $this->status = $this->status ? ucfirst($this->status): null;
        $today = Carbon::now()->format('Y-m-d');
        $thirtyDaysAgo = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->startDate = LaravelNepaliDate::from($thirtyDaysAgo)->toNepaliDate(format: 'Y-m-d');
        $this->endDate = LaravelNepaliDate::from($today)->toNepaliDate(format: 'Y-m-d');
        $this->multiSelectAttributes = ['employee_ids'];
        $this->leaveApprovalCount();
        $this->scopeWiseFilters();
        $this->setPermissionForEmployeeDropdown();
        $this->setExcelOptions();
    }

    public function setExcelOptions()
    {
        $this->exportIgnoreColumns = ['id', 'created_at'];
        $this->exportColumnHeadersMap = [
            'nep_start_date' => 'Start Date [B.S]',
            'nep_end_date' => 'End Date [B.S]',
            'start_date' => 'Start Date [A.D]',
            'end_date' => 'End Date [A.D]',
            'num_days' => 'Duration(in days)',
            'department_name' => 'Department',
            'branch_name' => 'Branch'
        ];
        $this->exportFileName = 'Leave Approval Export Report.xlsx';
    }
    public function exportEmployees($exportAll = false)
    {
        $this->setExcelOptions();
        if (!$this->hasData) {
            $this->notify("No Data Found to Export!.")->type("error")->send();
            return;
        }
        if ($exportAll) {
            $data = $this->listQuery()->get();
        } else {
            $data = $this->{$this->tableListVariable}(); // Retrieve the data from the computed property

            if ($data instanceof LengthAwarePaginator) {
                $data = $data->getCollection();
            }
        }

        return Excel::download(new class ($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);

                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];
                // Provide headings for your export file
                // You can customize this based on your data structure
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return $headings;
            }
        }, $this->exportFileName);
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();
        $orgInfo = $currentEmployee?->organizationInfo;

        $this->selectedCompany = $currentEmployee?->company_id;

        if (scopeCompany() || scopeRegion()) {
            // No additional action needed since companyId is already set
        } elseif (scopeBranch()) {
            $this->selectedBranch = $orgInfo?->branch_id;
        } else {
            $this->selectedBranch = $orgInfo?->branch_id;
            $this->selectedDepartment = $orgInfo?->department_id;
        }
    }

    // public function rules()
    // {
    //     return [
    //         'startDate' => 'required',
    //         'endDate' => ['required', function ($attribute, $value, $fail) {
    //             $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
    //             $endDate = LaravelNepaliDate::from($value)->toEnglishDate();
    //             if ($startDate > $endDate) {
    //                 $fail("End date should be greater than start date.");
    //             }
    //             $maxDifference = 32;

    //             $startDate = \Carbon\Carbon::parse($startDate);
    //             $endDate = \Carbon\Carbon::parse($endDate);
    //             $totalDays = $endDate->diffInDays($startDate) + 1; // diffInDays gives count 1 day less so 1 day is added.

    //             if ($totalDays > $maxDifference) {
    //                 $fail("The difference between start date and end date cannot be greater than {$maxDifference} days.");
    //             }
    //         },],
    //     ];
    // }

    public function messages()
    {
        return [
            'startDate.required' => 'Start date is required.',
            'endDate.required' => 'End date is required.',
            'endDate.before' => 'End date must be within 30 days of start date.'
        ];
    }
    public function listQuery()
    {
        $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
        $endDate = LaravelNepaliDate::from($this->endDate)->toEnglishDate();
        $query = \App\Models\Leaves\LeaveRequest::leftJoin('employees as emp', 'emp.id', 'leave_requests.employee_id')
            ->leftJoin('leave_types as lt', 'lt.id', 'leave_requests.leave_type_id')
            ->leftJoin('companies as comp', 'comp.id', 'emp.company_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'leave_requests.employee_id')
            ->leftjoin('branches as br', 'org.branch_id', 'br.id')
            ->leftJoin('departments as dep', 'org.department_id', 'dep.id')
            ->leftJoin('leave_options as lo', 'lo.id', 'leave_requests.leave_option_id')
            ->when(count($this->employee_ids), function ($query) {
                $query->whereIn('emp.id', $this->employee_ids);
            })
            ->when($this->status, function ($query) {
                $status = strtolower($this->status);
                if ($status === 'unapproved') {
                    $query->unapproved();
                } else {
                    $query->where('leave_requests.state', ($status));
                }
            })

            ->when($this->selectedCompany, function ($query) {
                $query->where('comp.id', $this->selectedCompany);
            })
            ->when($this->selectedBranch, function ($query) {
                $query->where('br.id', $this->selectedBranch);
            })
            ->when($this->selectedDepartment, function ($query) {
                $query->where('dep.id', $this->selectedDepartment);
            })
            ->when($this->leaveType, fn($query) => $query->where('lt.id', $this->leaveType))
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->where(function ($query) use ($startDate, $endDate) {
                    $query->where(function ($query) use ($startDate, $endDate) {
                        $query->whereBetween('leave_requests.start_date', [$startDate, $endDate]) // check whether leave start date in database fall under the range specified
                            ->orWhereBetween('leave_requests.end_date', [$startDate, $endDate]);  // check whether leave end date in database fall under the range specified
                    })->orWhere(function ($query) use ($startDate, $endDate) {                    // only when oabove one condtion is true
                        $query->where('leave_requests.start_date', '<=', $startDate)              // checks if the leave request starts before or on the start date of the specified range.
                            ->where('leave_requests.end_date', '>=', $endDate);                   // checks if the leave request ends after or on the end date of the specified range.
                    });
                });
            })
            ->select(
                DB::raw("CONCAT_WS(' ', emp.first_name, emp.middle_name, emp.last_name) as full_name"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as emp_code"),
                'leave_requests.id as id',
                'leave_requests.nep_start_date as nep_start_date',
                'leave_requests.nep_end_date as nep_end_date',
                'leave_requests.start_date as start_date',
                'leave_requests.end_date as end_date',
                'leave_requests.num_days as num_days',
                'dep.name as department_name',
                'br.name as branch_name',
                'lt.name as leave_type',
                'lo.name as leave_option',
                'leave_requests.state as state',
                'leave_requests.created_at',
            );

        $query = filterEmployeesByScope($query, 'org', 'emp');
        return $this->applySorting($query);
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function statusList()
    {
        $states = ArFlow::getStates(WorkflowName::LEAVE_APPROVAL);
        if (!in_array('Unapproved', $states)) {
            $states[] = 'Unapproved';
        }
        return $states;
    }

    #[Computed(persist: true)]
    public function fiscalYears()
    {
        return \App\Models\configs\FiscalYear::pluck("name", "id");
    }

    #[Computed(persist: true)]
    public function leaveTypes()
    {
        return \App\Models\configs\LeaveType::orderBy('name')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        return Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return Branch::where('company_id', $this->selectedCompany)->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return Department::where('company_id', $this->selectedCompany)->orderBy('name')->pluck('name', 'id')->toArray();
    }


    public function updated($attr)
    {
        if (in_array($attr, ['status', 'startDate', 'endDate', 'leaveType', 'selectedBranch', 'selectedCompany', 'selectedDepartment', 'employee_ids'])) {
            unset($this->list);
            $this->leaveApprovalCount();
        }

        if ($attr == 'startDate' || $attr == 'endDate') {
            // $this->validate();
            unset($this->list);
            $this->leaveApprovalCount();
        }

        if ($attr == 'selectedCompany') {
            unset($this->branchList, $this->departmentList, $this->list);
            $this->selectedBranch = null;
            $this->selectedDepartment = null;
            $this->leaveApprovalCount();
        }
    }

    public function leaveApprovalCount()
    {
        $leaveCounts = $this->repo->getLeaveCount($this->startDate, $this->endDate, $this->selectedBranch, $this->selectedDepartment);

        $this->leaveDetails = $leaveCounts->mapWithKeys(function ($item) {
            return [
                $item->name => [
                    'id' => $item->id,
                    'count' => $item->count,
                ]
            ];
        })->toArray();
    }


    public function showDetail($id)
    {
        $this->selectedId = $id;
    }

    public function filterAttendance($typeName)
    {
        if (array_key_exists($typeName, $this->leaveDetails)) {
            $typeId = $this->leaveDetails[$typeName]['id'];

            $this->leaveType = $typeId;

            unset($this->list);
        } else {
            $this->leaveType = null;
        }
    }

    #[On("leave-request-state-changed")]
    public function onLeaveRequestStateChanged()
    {
        unset($this->list);
    }

    public function refreshList()
    {
        unset($this->list);
    }
}
