<?php

namespace App\Livewire\Leaves;

use App\Exports\PayslipExcelSample;
use App\Http\Helpers\Constant;
use App\Http\Repositories\LeaveRequestRepository;
use App\Traits\WithNotify;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UploadLeave extends Component
{
    use WithFileUploads, WithNotify;

    public $excelHeaders = [
        "employee code" => 'CB-000',
        "name" => 'Test Employee',
        "date from" => 'mm/dd/yyyy or yyyy-mm-dd',
        "date to" => 'mm/dd/yyyy or yyyy-mm-dd',
        "leave type" => 'Sick Leave',
        "leave option" => "Full Day",
        "reason" => 'Test Reason',
    ];

    public $optionalExcelHeaders = [
        'replaced date from' => 'required if ' . Constant::REPLACEMENT_LEAVE_NAME,
        'replaced date to' => 'required if ' . Constant::REPLACEMENT_LEAVE_NAME,
    ];

    public $excelData = [];
    public $excelDataValidated = true;
    public $excelUploaded = false;

    #[Validate('required|file')]
    public $excelFile = "";
    public $excelValidationMessage = "";

    public function downloadSample()
    {
        return Excel::download(new PayslipExcelSample([
            ...$this->excelHeaders,
            ...$this->optionalExcelHeaders
        ]), 'LeaveExcelSample.xlsx');
    }

    public function uploadExcelFile()
    {
        $this->reset('excelData', 'excelDataValidated', 'excelValidationMessage', 'excelUploaded');
        $this->excelData = [];
    }

    private function validateFile()
    {
        $this->validate();
        $data = Excel::toArray((object)[], $this->excelFile);
        $headers = array_map(fn($header) => strtolower(trim($header)), $data[0][0] ?? []);
        foreach (array_keys($this->excelHeaders) as $header) {
            if (!in_array(strtolower($header), $headers)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }
        try {
            $data = transformArrayToAssociative($data[0], ['date from', 'date to', 'replaced date from', 'replaced date to']);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
        return $data;
    }

    public function validateExcelData()
    {
        $this->excelData = [];
        $dataValid = true;
        if (!($data = $this->validateFile())) {
            return;
        }

        $repo = new LeaveRequestRepository();
        try {
            $validationResults = $repo->validateLeaveDataForExcel($data);
            foreach ($validationResults as $index => $result) {
                $errorMessage = "";
                if (!$result['status']) {
                    $dataValid = false;
                    $errorMessage = $result['message'];
                }
                $this->excelData[] = ['data' => $data[$index], 'error' => $errorMessage];
            }
            $this->excelDataValidated = $dataValid;
            if (!$this->excelDataValidated) $this->notify("Some data are not validated")->type("error")->send();
            else $this->notify("All data are validated. You can proceed to upload the data")->type("success")->send();
        } catch (\Exception $e) {
            logError("Error creating leave from excel", $e);
            $this->notify("Error validating leave: " . $e->getMessage())->type("error")->send();
        }
    }


    public function uploadData()
    {
        if (!$this->excelDataValidated) {
            return $this->notify("Excel validation failed");
        }

        $data = collect($this->excelData)->pluck('data')->all();

        $repo = new LeaveRequestRepository;
        $response = $repo->createLeaveRequestsForExcel($data);

        if (!empty($response['data']))
            $this->excelValidationMessage = $response['data']['validation'];

        if (!$response['status']) {
            $this->notify($response['message'])->type('error')->duration(10)->send();
            return;
        }

        $this->excelUploaded = true;
        return $this->notify("Leaves Uploaded Successfully")->send();
    }

    public function exportOutputData()
    {
        return Excel::download(new class($this->excelData, array_keys($this->excelHeaders)) implements FromArray, WithHeadings, WithStyles
        {
            private $data, $headers;

            public function __construct($data, $headers)
            {
                $this->data = $data;
                $this->headers = $headers;
            }

            public function array(): array
            {
                return array_map(fn($item) => [
                    ...(array_intersect_key($item['data'], array_flip($this->headers))),
                    'error' => $item['error'] ?? ''
                ], $this->data);
            }

            public function headings(): array
            {
                return array_map(fn($header) => ucwords($header), [
                    ...$this->headers,
                    'error'
                ]);
            }

            public function styles(Worksheet $sheet)
            {
                $styles = [];

                $styles[1] = [
                    'font' => [
                        'bold' => true,
                    ],
                ];

                foreach ($this->data as $index => $row) {
                    if (!empty($row['error'])) {
                        // +2 because row index in Excel starts at 1 and we have 1 row for headings
                        $styles[$index + 2] = [
                            'fill' => [
                                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                'color' => ['rgb' => 'FF9999'], // light red
                            ],
                        ];
                    }
                }

                return $styles;
            }
        }, "Leave Upload Output.xlsx");
    }

    public function render()
    {
        return view('livewire.leaves.upload-leave');
    }
}
