<?php

namespace App\Livewire\Leaves;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\LeaveRepository;
use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Leaves\LeaveRequest;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Facades\Excel;

use Livewire\Component;

#[Title('Leave Details')]
class LeaveDetail extends Component
{
    use WithDataTable, WithNotify, MultiselectEmployeeSearch;

    public $editingId;
    public $showId;
    public $state;
    public $title;
    public $type; // mine, all;
    public $showData = false;

    #[Validate('required', as: 'employee')]
    public $employeeId;

    public $leaveType, $fiscalYearId;

    public function __construct() {
        $this->leaveRepo = new LeaveRepository;
    }

    public function mount($type = 'mine')
    {
        $this->withTrashed = true;
        $this->sortBy = "created_at";
        $this->sortDirection = 'desc';

        if (Route::currentRouteName() == 'leaveDetail') {
            $this->type = "mine";
            $this->title = 'My Leave Details';
            $this->employeeId = session(Constant::SESSION_EMPLOYEE_ID);
            $this->showData = true;
        } else if (Route::currentRouteName() == 'employeeLeaveDetail') {
            $this->type = "all";
            $this->title = "Employee's Leave Details";
            $this->setPermissionForEmployeeDropdown();
            $this->singleSelectAttributes = ['employeeId'];
            if (!scopeAll() && !scopeCompany() && !scopeRegion() && !scopeDepartment() && !scopeBranch()) {
                $this->type = "mine";
                $this->title = 'My Leave Details';
                $this->employeeId = session(Constant::SESSION_EMPLOYEE_ID);
                $this->showData = true;
            }
        } else {
            abort('404', "Page not found");
        }
        $this->fiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
    }

    public function showDetail(int $id): void
    {
        $this->showId = $id;
    }

    public function showEmployeeLeaveDetails()
    {
        $this->validateOnly('employeeId');
        $this->showData = true;
        unset($this->list);
        unset($this->assignedLeaves);
    }

    // public function rules()
    // {
    // return [
    //     'startDate' => 'required',
    //     'endDate' => ['required', function ($attribute, $value, $fail) {
    //         $startDate = LaravelNepaliDate::from($this->startDate)->toEnglishDate();
    //         $endDate = LaravelNepaliDate::from($value)->toEnglishDate();
    //         if ($startDate > $endDate) {
    //             $fail("End date should be greater than start date.");
    //         }
    //         $maxDifference = 32;

    //         $startDate = \Carbon\Carbon::parse($startDate);
    //         $endDate = \Carbon\Carbon::parse($endDate);
    //         $totalDays = $endDate->diffInDays($startDate) + 1; // diffInDays gives count 1 day less so 1 day is added.

    //         if ($totalDays > $maxDifference) {
    //             $fail("The difference between start date and end date cannot be greater than {$maxDifference} days.");
    //         }
    //     },],
    // ];
    // }

    public function edit(int $id)
    {
        $this->editingId = $id;
    }

    public function delete($id)
    {
        $leaveRequest = LeaveRequest::find($id);
        if ($leaveRequest->state == "Submitted") {
            $leaveRequest->delete();
            $this->notify('Leave request deleted successfully')->send();
            unset($this->list);
        } else {
            $this->notify('Leave request cannot be deleted. It has been approved.')->type('error')->send();
        }
    }

    public function resetTable()
    {
        unset($this->list);
    }

    public function updated($attrs)
    {
        if (in_array($attrs, ['leaveType', 'state'])) {
            unset($this->list);
        }

        if ($attrs == 'fiscalYearId') {
            unset($this->list);
            unset($this->assignedLeaves);
        }

        // if ($attrs == 'startDate' || $attrs == 'endDate') {
        //     $this->validate();
        //     unset($this->list);
        // }
    }

    #[Computed(persist: true)]
    public function assignedLeaves()
    {
        if ($this->employeeId) {
            $employee = Employee::withTrashed()->find($this->employeeId);
            if (!$employee) return $this->notify("Employee not found")->type("error")->send();

            $repo = new LeaveRepository;
            return $repo->getLeaveBalance($employee, $this->fiscalYearId);
        }
        return [];
    }

    #[Computed(persist: true)]
    public function leaveTypeList()
    {
        return $this->leaveRepo->leaveTypeList();
    }

    #[Computed()]
    public function stateList()
    {
        return $this->leaveRepo->stateList();
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return $this->leaveRepo->fiscalYearList();
    }

    #[On('hidden.bs.modal')]
    public function hiddenModal()
    {
        $this->reset('editingId');
        $this->reset('showId');
    }

    public function recalculateLeaveBalance()
    {
        $leaveRepo = new LeaveRepository;
        $response = $leaveRepo->recalculateLeaveBalance($this->employeeId, $this->fiscalYearId);
        if (!$response['status']) return $this->notify($response['message'])->type('error')->send();
        unset($this->assignedLeaves);
        $this->notify($response['message'])->type('success')->send();
    }
    #[Computed(persist: true)]
   public function list(){
    return $this->applySorting($this->listQuery())->paginate($this->perPage);

   }
    public function listQuery()
    {
        $query = LeaveRequest::with(['employee:id,first_name,middle_name,last_name','leaveOption:id,name', 'leaveType:id,name'])
            ->whereHas('employee', function ($q) {
                $q->where('id', $this->employeeId);
            })
            ->when($this->leaveType, fn($q) => $q->where('leave_type_id', $this->leaveType))
            ->when($this->fiscalYearId, fn($query) => $query->where('fiscal_year_id', $this->fiscalYearId))
            ->when($this->state, fn($query) => $query->where('state', $this->state));

            return $query;
    }
    #[Computed()]  
    public function hasData()
    {
        return ($this->listQuery()->exists());
    }

    public function exportAllEmployees($exportAll = false)
    {
        $this->setExcelOptions();
        if (!$this->hasData) {
            $this->notify("No Data Found to Export!.")->type("error")->send();
            return;
        }
        if ($exportAll) {
            $data = $this->listQuery()->get();
        } else {
            $data = $this->{$this->tableListVariable}(); // Retrieve the data from the computed property

            if ($data instanceof LengthAwarePaginator) {
                $data = $data->getCollection();
            }
        }

        $data = $data->map(fn($item) => collect([
            'Leave Type'        => $item->leaveType->name,
            'Remarks'           => $item->remarks,
            'Leave Duration'    =>"{$item->start_date} - {$item->end_date}",
            'Leave Period'      => $item->leaveOption->name,
            'Number of Days'    => $item->num_days . 'days',
            'Applied at'        => $item->created_at->diffForHumans(),
            'Status'            => $item->state,
        ]));

        return Excel::download(new class($data, $this->exportIgnoreColumns, $this->exportColumnHeadersMap) implements FromCollection, WithHeadings, ShouldAutoSize {
            private $data;
            private $exportIgnoreColumns;
            private $exportColumnHeadersMap;

            public function __construct($data, $exportIgnoreColumns, $exportColumnHeadersMap)
            {
                $this->data = $data;
                $this->exportIgnoreColumns = $exportIgnoreColumns;
                $this->exportColumnHeadersMap = $exportColumnHeadersMap;
            }

            public function collection()
            {
                $items = collect($this->data);

                $filteredItems = $items->map(function ($item) {
                    return collect($item)->except($this->exportIgnoreColumns);
                });

                return $filteredItems;
            }

            public function headings(): array
            {
                $headings = [];
                if (is_array($this->data) && isset($this->data[0])) {
                    $headings = array_keys($this->data[0]);
                } else {
                    $headings = array_keys($this->data->first()->toArray() ?? []);
                }

                // Filter out the ignored columns from headings
                $headings = array_diff($headings, $this->exportIgnoreColumns);

                // Map the column headers
                $headings = array_map(function ($heading) {
                    return $this->exportColumnHeadersMap[$heading] ?? ucfirst(str_replace('_', ' ', $heading));
                }, $headings);

                return $headings;
            }
        }, $this->exportFileName);
    }
    public function setExcelOptions()
    {
        $title = "Employee Leave Detail List";
        $employee = Employee::withTrashed()->find($this->employeeId);
        if ($employee) $title .= " ($employee->name)";

        return $this->exportFileName = "$title.xlsx";
    }

    public function render()
    {
        return view('livewire.leaves.leave-detail');
    }
}
