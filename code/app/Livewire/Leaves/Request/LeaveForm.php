<?php

namespace App\Livewire\Leaves\Request;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\Leaves\LeaveRequest;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class LeaveForm extends Component
{
  use WithNotify;
  private $fiscalYearId, $employeeId;

  public $isEditing, $editingId;

  public $assignedLeaves = []; // to be passed as props, since used on leave balance too..
  public $remaining = '';

  public $leave_type_id, $dateRange, $remarks, $verifier_id, $leave_option_id;
  public function rules()
  {
    return [
      'leave_type_id' => 'required',
      'dateRange' => 'required',
      'leave_option_id' => 'required',
      'remarks' => 'required',
      'verifier_id' => 'required',
    ];
  }

  public function boot()
  {
    $this->fiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
    $this->employeeId = session(Constant::SESSION_EMPLOYEE_ID);
  }

  #[Computed]
  public function leaveType()
  {
    $leaveTypes = \App\Models\configs\LeaveType::pluck('name', 'id')
      ->whereNull('deleted_at')
      ->toArray();
    return $leaveTypes;
  }

  #[Computed(persist: true)]
  public function verifiers()
  {
    return LeaveRequest::getVerifiers();
  }


  public function updatedLeaveTypeId($leaveTypeId)
  {
    if ($leaveTypeId) {
      $leave = \App\Models\configs\LeaveType::find($leaveTypeId);
      $employeeLeave = \App\Models\Leaves\EmployeeLeave::where('employee_id', $this->employeeId)
        ->where('leave_type_id', $leave->id)
        ->where('fiscal_year_id', $this->fiscalYearId)
        ->select('remaining_days')
        ->first();
      $this->remaining = ($employeeLeave?->remaining_days ?? 0) . ' days remaining';
    } else {
      $this->remaining = '';
    }
  }


  #[On('hide.bs.modal')]
  public function resetModal()
  {
    $this->reset(['leave_type_id', 'dateRange', 'period', 'remarks', 'verifier_id']);
    $this->resetErrorBag();
  }

  public function save()
  {
    $this->validate();
    try {
      Log::info("Parameters for applying leave: ", $this->all());
      $verifierIds = LeaveRequest::getVerifiers()->map(fn ($verifier) => $verifier->id)->toArray();
      if (!in_array($this->verifier_id, $verifierIds)) {
        $this->addError('verifier_id', 'Invalid verifier');
        Log::error("Invalid verifier: ", $this->verifier_id);
        return;
      }

      $dateRange = explode(' - ', $this->dateRange);
      $startDate = head($dateRange);
      $endDate = last($dateRange);
      $leaveOption = \App\Models\Leaves\LeaveOption::find($this->leave_option_id);
      if (!$leaveOption) {
        $this->notify("Leave Option Not Available")->type("error")->send();
        return;
      }
      $multiplier = $leaveOption->num_days;

      $commonFields = [
        'leave_type_id' => $this->leave_type_id,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'nep_start_date' => $this->convertToNepDate($startDate),
        'nep_end_date' => $this->convertToNepDate($endDate),
        'fiscal_year_id' => $this->fiscalYearId,
        'num_days' => (Carbon::parse($startDate)->diffInDays($endDate) + 1) * $multiplier,
        'leave_option_id' => $this->leave_option_id,
        'current_owner_id' => $this->verifier_id,
        'applied_status' => Carbon::now() < Carbon::parse($startDate) ? 'before' : 'after',
        'remarks' => $this->remarks,
      ];

      $status = false;
      if ($this->editingId) {
        $status = $this->updateLeaveRequest($commonFields);
      } else {
        $status = $this->createLeaveRequest($commonFields);
      }
      if (!$status) {
        return;
      }
    } catch (\Exception $e) {
      Log::error('Error while processing leave request: ' . $e);
      $this->notify('Error while processing leave request')->type('error')->send();
      return;
    }

    $this->dispatch('hide-model');
    Log::info('Leave request ' . ($this->editingId ? 'Updated' : 'Created') . ' Successfully');
    $this->notify('Leave request ' . ($this->editingId ? 'Updated' : 'Created') . ' Successfully')->send();
    $this->dispatch('leave-saved');
  }

  private function updateLeaveRequest($commonFields)
  {
    $leaveRequest = LeaveRequest::findOrFail($this->editingId);
    Log::info('Updating leave request with ID: ' . $this->editingId);
    Log::info('Parameters for updating leave request: ', $commonFields);
    if ($this->checkLeaveOverlap($commonFields['start_date'], $commonFields['end_date'], $leaveRequest->id)) {
      $this->addError('dateRange', 'Leave overlaps with another applied leave.');
      return false;
    }
    $leaveRequest->update($commonFields);
    return true;
  }

  private function createLeaveRequest($commonFields)
  {
    Log::info('Creating new leave request');
    Log::info('Parameters for creating leave request: ', $commonFields);
    if ($this->checkLeaveOverlap($commonFields['start_date'], $commonFields['end_date'])) {
      $this->addError('dateRange', 'Leave overlaps with another applied leave.');
      return false;
    }

    $row = array_merge($commonFields, [
      'employee_id' => $this->employeeId,
    ]);
    LeaveRequest::create($row);
    return true;
  }

  public function edit($id)
  {
    $this->editingId = $id;
    $leaveRequest = LeaveRequest::findOrFail($id);
    $this->leave_type_id = $leaveRequest->leave_type_id;
    $this->dateRange = $leaveRequest->start_date . '- ' . $leaveRequest->end_date;
    $this->leave_option_id = $leaveRequest->leave_option_id;
    $this->remarks = $leaveRequest->remarks;
  }

  private function convertToNepDate($engDate)
  {
    return LaravelNepaliDate::from($engDate)->toNepaliDate(); // Convert English date to Nepali date
  }

  private function checkLeaveOverlap($startDate, $endDate, $ignoreId = null)
  {
    $overlapExists = LeaveRequest::where('employee_id', $this->employeeId)
      ->where(function ($query) use ($startDate, $endDate) {
        // Check if the specified range overlaps with existing leave requests
        $query
          ->whereBetween('start_date', [$startDate, $endDate])
          ->orWhereBetween('end_date', [$startDate, $endDate])
          ->orWhere(function ($query) use ($startDate, $endDate) {
            $query->where('start_date', '<=', $startDate)->where('end_date', '>=', $endDate);
          });
      })
      ->when($ignoreId, function ($query) use ($ignoreId) {
        $query->where('id', '!=', $ignoreId);
      })
      ->exists();
    return $overlapExists;
  }

  public function render()
  {
    return view('livewire.leaves.request.leave-form');
  }
}
