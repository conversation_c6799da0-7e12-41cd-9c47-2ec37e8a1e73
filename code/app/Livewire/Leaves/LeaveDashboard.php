<?php

namespace App\Livewire\Leaves;

use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\ExportTable;
use App\Http\Repositories\Interfaces\ILeaveDashboardRepository;
use App\Models\configs\Department;
use App\Models\configs\LeaveType;
use App\Models\Leaves\LeaveOption;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDefaultFilter;
use App\Traits\WithNotify;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Carbon\Carbon;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;
use Symfony\Component\HttpKernel\Exception\HttpException;

#[Title('Leave Dashboard')]
class LeaveDashboard extends Component
{
    use WithDefaultFilter, MultiselectEmployeeSearch, WithPagination, WithNotify;

    private ILeaveDashboardRepository $repo;

    #[Url('tab')]
    public string $activeTab = 'day';

    // Filters (global)
    // company, department, region, branch filters from WithDefaultFilter trait
    public ?int $leaveTypeId = null;
    public ?int $leaveOptionId = null;

    // Date range filters
    public string $startDate;
    public string $endDate;
    public ?array $employee_ids_range = [];
    public ?string $state = null;
    public int $perPageRange = 10;


    // Day Filters
    public ?string $selectedDate = null;
    public ?array $employee_ids_day = [];
    public int $perPageDay = 10;

    // Requests list quick-filters (from KPI/chart clicks)
    public ?bool $reqPendingOnly = null;
    public ?array $reqStateIn = null;


    public $filterErrors = [];

    public array $chartDaily = [];
    public array $chartDonutRange = [];
    public array $chartDeptBarRange = [];
    public array $chartDonutDay = [];
    public array $chartDeptBarDay = [];

    public function boot(ILeaveDashboardRepository $repo): void
    {
        $this->repo = $repo;
    }

    public function mount(): void
    {
        $dayDiff = 30;
        $startDate = LaravelNepaliDate::from(Carbon::now()->subDays($dayDiff))->toNepaliDate('Y-m-d');
        $todayNep = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y-m-d');
        $this->startDate = $startDate;
        $this->endDate   = $todayNep;
        $this->selectedDate = $todayNep;

        $this->multiSelectAttributes = ['employee_ids_range', 'employee_ids_day'];

        $this->dispatchCharts();
    }

    protected function toEn(?string $nep): ?string
    {
        return $nep ? LaravelNepaliDate::from($nep)->toEnglishDate() : null;
    }

    protected function filtersBase(): array
    {
        return [
            'leave_type_id'   => $this->leaveTypeId,
            'leave_option_id' => $this->leaveOptionId,
            'branch_id'       => $this->filterBranchId,
            'department_id'   => $this->filterDepartmentId,
            'region_id'       => $this->filterRegionId,
        ];
    }

    protected function filtersRange()
    {

        $start = $this->toEn($this->startDate);
        $end   = $this->toEn($this->endDate);
        return [
            ...$this->filtersBase(),
            'start_en'        => $start,
            'end_en'          => $end,
            'employee_ids' => $this->employee_ids_range,
            'state' => $this->state,
        ];
    }

    protected function filtersDay()
    {
        return [
            ...$this->filtersBase(),
            'employee_ids' => $this->employee_ids_day,
        ];
    }

    #[Computed(persist: true)]
    public function kpis(): array
    {
        return $this->repo->kpisRange($this->filtersRange()) + [
            'window' => $this->startDate . ' → ' . $this->endDate,
        ];
    }

    public function onLeaveTodayQuery()
    {
        $dateEn = $this->toEn($this->selectedDate);
        return $this->repo->onLeaveForDate(
            $this->filtersDay(),
            $dateEn,
            returnQuery: true,
        );
    }

    #[Computed(persist: true)]
    public function onLeaveToday()
    {
        return $this->onLeaveTodayQuery()->paginate($this->perPageDay, pageName: 'onLeavePage');
    }

    public function onLeaveExportDataTable()
    {
        $data = array_map(
            function ($item) {
                $result = [
                    'employee_name' => $item->employee_name,
                    'emp_code' => $item->emp_code,
                    'leave_type' => $item->leave_type,
                    'leave_option' => $item->leave_option,
                    'date_en' => $item->date_en,
                    'date_np' => $item->date_np,

                ];
                return $result;
            },
            $this->onLeaveToday->items()

        );

        return ExportTable::exportDataTable(
            $data,
            exportFileName: 'Export On Leave.xlsx',
            exportColumnHeadersMap: [
                'date_en' => 'Date[A.D]',
                'date_np' => 'Date[B.S]'
            ]
        );
    }

    public function onLeaveExportAllData()
    {
        $result = $this->onLeaveTodayQuery()->get()->map(function ($item) {
            return [
                'employee_name' => $item->employee_name,
                'emp_code' => $item->emp_code,
                'leave_type' => $item->leave_type,
                'leave_option' => $item->leave_option,
                'date_en' => $item->date_en,
                'date_np' => $item->date_np,
            ];
        });

        return ExportTable::exportDataTable(
            $result,
            exportFileName: 'Export All data On Leave.xlsx',
            exportColumnHeadersMap: [
                'date_en' => 'Date[A.D]',
                'date_np' => 'Date[B.S]'
            ]
        );
    }

    public function requestsInRangeQuery()
    {
        return $this->repo->requestsOverlappingRange(
            $this->filtersRange(),
            returnQuery: true,
        );
    }
    #[Computed(persist: true)]
    public function requestsInRange()
    {
        return $this->requestsInRangeQuery()->paginate($this->perPageRange, pageName: 'requestsPage');
    }

    public function exportRequestsInRange()
    {
        $data = array_map(
            function ($item) {
                return [
                    'employee_name' => $item->employee_name,
                    'emp_code' => $item->emp_code,
                    'leave_type' => $item->leave_type,
                    'leave_option' => $item->leave_option,
                    'period' => $item->nep_start_date . ' → ' . $item->nep_end_date,
                    'days' => $item->num_days,
                    'state' => $item->state,
                    'applied' => $item->created_at->format('Y-m-d'),
                ];
            },
            $this->requestsInRange->items()
        );

        return ExportTable::exportDataTable(
            $data,
            exportFileName: 'Export Requests in Range.xlsx',
            exportColumnHeadersMap: [
                'employee_name' => 'Employee Name',
                'emp_code' => 'Employee Code',
                'leave_type' => 'Leave Type',
                'leave_option' => 'Leave Option',
                'period' => 'Period (Start → End)',
                'days' => 'No. of Days',
                'state' => 'State',
                'applied' => 'Applied On',
            ]
        );
    }
    public function exportAllRequestsInRange()
    {
        $allData = $this->requestsInRangeQuery()->get();

        $data = $allData->map(function ($item) {

            return [
                'employee_name' => $item->employee_name,
                'emp_code' => $item->emp_code,
                'leave_type' => $item->leave_type,
                'leave_option' => $item->leave_option,
                'period' => $item->nep_start_date . ' → ' . $item->nep_end_date,
                'days' => $item->num_days,
                'state' => $item->state,
                'applied' => $item->created_at->format('Y-m-d'),
            ];
        });

        return ExportTable::exportDataTable(
            $data,
            exportFileName: 'Export All Requests in Range.xlsx',
            exportColumnHeadersMap: [
                'employee_name' => 'Employee Name',
                'emp_code' => 'Employee Code',
                'leave_type' => 'Leave Type',
                'leave_option' => 'Leave Option',
                'period' => 'Period (Start → End)',
                'days' => 'No. of Days',
                'state' => 'State',
                'applied' => 'Applied On',
            ]
        );
    }


    #[Computed(persist: true)]
    public function leaveTypes(): array
    {
        return LeaveType::query()->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function leaveOptions(): array
    {
        return LeaveOption::query()->orderBy('name')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function stateList()
    {
        return ArflowHelper::getStatesArray(WorkflowName::LEAVE_APPROVAL);
    }

    /** Text summary of active Requests filters (for the badge row) */

    #[Computed()]
    public function requestsFilterText(): string
    {
        $parts = [];
        if ($this->reqPendingOnly) $parts[] = 'Pending only';
        if ($this->reqStateIn) $parts[] = 'Approved/Assigned';
        return implode(' • ', $parts);
    }

    public function updated($attr): void
    {
        if (!$this->validateFilters()) return;

        $commonAffects = [
            'filterCompanyId',
            'filterBranchId',
            'filterDepartmentId',
            'filterRegionId',
            'filterEmployeeId',
            'leaveTypeId',
            'leaveOptionId',
        ];

        if (in_array($attr, [...$commonAffects, 'startDate', 'endDate', 'state'])) {
            $this->dispatchRangeCharts();
        }

        if (in_array($attr, [...$commonAffects, 'selectedDate'])) {
            $this->dispatchDayCharts();
        }

        if (in_array($attr, ['employee_ids_range', 'perPageRange', 'state'])) {
            unset($this->requestsInRange);
            $this->setPage(1, 'requestsPage');
        }

        if (in_array($attr, ['employee_ids_day', 'perPageDay'])) {
            unset($this->onLeaveToday);
            $this->setPage(1, 'onLeavePage');
        }
    }

    public function validateFilters()
    {
        $this->filterErrors = [];
        if ($this->startDate > $this->endDate) {
            $this->filterErrors[] = 'Start date must be before end date';
        }

        return count($this->filterErrors) === 0;
    }

    public function updatedPaginators($page, $pageName): void
    {
        if ($pageName === 'onLeavePage') {
            unset($this->onLeaveToday);
        }
        if ($pageName === 'requestsPage') {
            unset($this->requestsInRange);
        }
    }

    protected function dispatchRangeCharts($mounted = false)
    {
        $f = $this->filtersRange();

        $this->chartDaily        = $this->repo->chartDailyRange($f);
        $this->chartDonutRange   = $this->repo->chartTypeDonutRange($f);
        $this->chartDeptBarRange = $this->repo->chartDeptBarRange($f);

        if (!$mounted) {
            $this->dispatch('chart-daily-updated', payload: $this->chartDaily);
            $this->dispatch('chart-donut-range-updated', payload: $this->chartDonutRange);
            $this->dispatch('chart-dept-range-updated', payload: $this->chartDeptBarRange);
            unset($this->requestsInRange);
            unset($this->kpis);
        }
    }

    protected function dispatchDayCharts($mounted = false)
    {
        $f = $this->filtersDay();
        $dateEn = $this->toEn($this->selectedDate ?? $this->startDate);
        $this->chartDonutDay     = $this->repo->chartTypeDonutOnDate($f, $dateEn);
        $this->chartDeptBarDay   = $this->repo->chartDeptBarOnDate($f, $dateEn);
        if (!$mounted) {
            $this->dispatch('chart-donut-day-updated', payload: $this->chartDonutDay);
            $this->dispatch('chart-dept-day-updated', payload: $this->chartDeptBarDay);
            unset($this->onLeaveToday);
        }
    }

    protected function dispatchCharts($mounted = false): void
    {
        $this->dispatchRangeCharts($mounted);
        $this->dispatchDayCharts($mounted);
    }

    public function clearCache()
    {
        $this->repo->clearCache();
        $this->dispatchCharts();
        $this->notify("Cache cleared successfully.")->type('success')->send();
    }

    public function render()
    {
        return view('livewire.leaves.dashboard.leave-dashboard');
    }
}
