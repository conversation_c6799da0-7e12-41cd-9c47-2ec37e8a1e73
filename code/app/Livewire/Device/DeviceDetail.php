<?php

namespace App\Livewire\Device;

use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\UserRepository;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Employee's App Login")]
class DeviceDetail extends Component
{
    use MultiselectEmployeeSearch, WithDataTable;

    public Employee $personalInfo;

    public $employeeId, $otp, $codeGenerated, $dataUri, $generated = false;

    private $userRepository;

    public function __construct()
    {
        $this->userRepository = new UserRepository();
    }

    public function mount()
    {
        $employeeId = request()->query('employee');

        if ($employeeId) {
            $this->employeeId = $employeeId;
            $this->generate();
        }
        $this->singleSelectAttributes = ['employeeId'];
        $this->setPermissionForEmployeeDropdown();
    }

    #[Computed(persist: true)]
    public function list()
    {
        if (!$this->employeeId) {
            return [];
        }
        $employee = Employee::find($this->employeeId);
        return app(IDeviceLogRepository::class)->listDeviceDetails($employee->user_id);
    }

    public function generate()
    {
        $this->validate(rules: ['employeeId' => 'required'], attributes: ['employeeId' => 'employee']);

        $this->personalInfo = $this->userRepository->personalInfo($this->employeeId);
        $this->dataUri = app(abstract: IDeviceLogRepository::class)->getCachedQrCode($this->personalInfo->user->username, $this->employeeId);
        $this->codeGenerated = ($this->dataUri == "") ? false : true;
        $this->otp = ($this->personalInfo->user->otp_expiration_date > date("Y-m-d H:i:s")) ? $this->personalInfo->user->otp : null;

        if ($this->employeeId && $this->personalInfo) {
            $this->generated = true;
            unset($this->list);
        }
    }

    #[On("regenerateQr")]
    public function regenerateQr()
    {
        return $this->qrCode();
    }

    #[On("generateOTP")]
    public function generateOTP()
    {
        $this->otp = $this->userRepository->generateOTP($this->personalInfo->user?->id);
    }

    private function qrCode()
    {
        $params = [
            'user_id'       => $this->personalInfo->user->id,
            'employee_id'   => $this->employeeId,
            'name'          => $this->personalInfo->name,
            'employee_code' => $this->personalInfo->organizationInfo?->company_employee_code,
            'department'    => $this->personalInfo->organizationInfo?->department?->name ?? '',
            'branch'        => $this->personalInfo->organizationInfo?->branch?->name,
            'contact'       => $this->personalInfo->phone,
            'username'      => $this->personalInfo->user->username,
        ];

        $result = app(abstract: IDeviceLogRepository::class)->generateQrCode($params);
        if ($result['updateLoginCode']) {
            $this->codeGenerated = true;
            $this->dataUri = $result['qrCode'];
            $this->otp = null;
        }

        return $result['qrCode'];
    }

    public function revokeDevice($deviceId)
    {
        try {
            app(abstract: IDeviceLogRepository::class)->deactivateDevice(['id' => $deviceId]);
            unset($this->list);
            $this->notify('Device revoked successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function resetMpin($deviceId)
    {
        try {
            app(IDeviceLogRepository::class)->resetMpin(['id' => $deviceId]);
            unset($this->list);
            $this->notify('MPIN reset successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function reject($deviceId)
    {
        try {
            app(IDeviceLogRepository::class)->rejectDevice(['id' => $deviceId]);
            unset($this->list);
            $this->notify('Device rejected successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function approve($deviceId)
    {
        try {
            app(IDeviceLogRepository::class)->approveDevice(['id' => $deviceId]);
            unset($this->list);
            $this->notify('Device approved successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function render()
    {
        return view('livewire.device.device-detail');
    }
}
