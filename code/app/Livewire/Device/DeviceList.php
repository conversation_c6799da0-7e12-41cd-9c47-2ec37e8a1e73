<?php

namespace App\Livewire\Device;

use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Models\Device\DeviceDetails;
use App\Traits\MultiselectEmployeeSearch;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title("Employee's App Details")]
class DeviceList extends Component
{
    use MultiselectEmployeeSearch;
    public $devices = [];
    public $status = 'pending';
    public $employee_ids = [];

    public function mount()
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->loadDevices();
    }

    public function updatedStatus()
    {
        $this->loadDevices();
    }
    public function updatedEmployeeIds()
    {
        $this->loadDevices();
    }

    public function view($id)
    {
        $device = DeviceDetails::find($id);

        if ($device && $device->user && $device->user->employee) {
            return redirect()->route('deviceLog', ['employee' => $device->user->employee->id]);
        }
    }

    public function approve($id)
    {
        try {
            app(IDeviceLogRepository::class)->resetMpin(['id' => $id]);
            $this->loadDevices();
            $this->notify('MPIN reset successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function reject($id)
    {
        try {
            app(IDeviceLogRepository::class)->rejectDevice(['id' => $id]);
            $this->loadDevices();
            $this->notify('Device rejected successfully.')->send();
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->send();
            return;
        }
    }

    public function loadDevices()
    {
        $query = DeviceDetails::with(['user.employee'])
            ->when($this->employee_ids, function ($query) {
                $query->whereHas('user.employee', function ($q) {
                    $q->whereIn('id', $this->employee_ids);
                });
            })
            ->when($this->status, function ($query) {
                $query->where('status', $this->status);
            })
            ->orderBy('login_time', 'desc');

        $this->devices = $query->get();
    }
    public function render()
    {
        return view('livewire.device.device-list');
    }
}
