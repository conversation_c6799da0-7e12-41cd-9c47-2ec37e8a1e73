<?php

namespace App\Livewire\SelfService\Attendance;

use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use App\Http\Repositories\LeaveRequestRepository;
use App\Http\Repositories\OtRequestRepository;
use App\Http\Repositories\TimeRequestRepository;
use App\Models\configs\Setting;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Http\Repositories\Reports\AttendanceRepository;
use App\Models\configs\Department;
use App\Models\configs\Holiday;
use App\Models\configs\Unit;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\ReplacementLeaveRequest;
use App\Models\OtRequests;
use App\Models\Payroll\Payslip;
use App\Models\RequestTicket;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Attendance Page')]
class AttendancePage extends Component
{
    use WithNotify, WithDataTable;

    public $selectedMonth;
    public $selectedYear;
    public $yearList = [];

    public $regionId;
    public $selectedDate;
    public $selectedBranch;
    public $selectedUnit;
    public $selectedDepartment;
    public $minInOutHourDifferenceForReplacementLeave = 4;
    public $minInOutTimeDifferenceForOt = 30;
    public $otGraceDay = 6;
    public $leaveValidDate;
    public $timeValidDate;

    protected $otRequestRepo;
    protected AttendanceRepository $attendanceRepo;

    public function __construct()
    {
        $this->attendanceRepo = new AttendanceRepository();
        $this->otRequestRepo = new OtRequestRepository();
    }

    public function mount()
    {
        $this->initializeData();

        for ($i = 0; $i < 5; $i++) {
            $this->yearList[] = $this->selectedYear - $i;
        }
        if (scopeRegion()) {
            $this->regionId = currentEmployee()?->organizationInfo?->region_id;
            $this->selectedDepartment = CurrentEmployee()?->organizationInfo?->department_id ?? "-1";
        } else {
            $this->selectedBranch = currentEmployee()?->organizationInfo?->branch_id;
            $this->selectedDepartment = CurrentEmployee()?->organizationInfo?->department_id ?? "-1";
        }
    }

    public function updated($property)
    {
        if (in_array($property, ['selectedDate', 'selectedUnit', 'selectedBranch', 'selectedDepartment'])) {
            $this->resetPage();
        }
        if ($property == 'selectedBranch') {
            unset($this->departmentList);
            $this->reset('selectedDepartment');
            $this->refreshAttendance();
            $this->resetPage();
        }
        if ($property == 'selectedDepartment') {
            unset($this->unitList);
            $this->reset('selectedUnit');
            $this->refreshAttendance();
            $this->resetPage();
        }
    }

    public function setSelectedAttendance($type, $attendance_type, $attendance)
    {
        $payload = [];

        if ($type == 'own') {
            $payload = [
                'employeeId' => session(Constant::SESSION_EMPLOYEE_ID),
                'date' => $attendance['date_np'],
                'employeeName' => session(Constant::SESSION_EMPLOYEE)->name
            ];
        }

        if ($type == 'team') {
            $payload = [
                'employeeId' => $attendance['employee_id'],
                'date' => $this->selectedDate,
                'employeeName' => $attendance['full_name']
            ];
        }

        if ($attendance_type == 'missed-punch') {
            $this->dispatch("updateSelectedMissedPunchAttendance", $payload);
        } elseif ($attendance_type == 'absent') {
            $this->dispatch("updateSelectedAbsentAttendance", $payload);
        } elseif ($attendance_type == 'replacement-leave') {
            $this->dispatch("updateSelectedReplacementLeaveAttendance", $payload);
        } elseif ($attendance_type == 'ot-request') {
            $this->dispatch("updateSelectedOtRequest", $payload);
        } elseif ($attendance_type == 'ot-request-konnect') {
            $this->dispatch("updateSelectedOtRequestKonnect", $payload);
        } else {
            $this->notify('Invalid request')->type('error')->send();
        }
    }

    #[On('hide.bs.tab')]
    public function initializeData()
    {
        $this->search = '';
        $this->sortBy = 'date_en';
        $this->sortDirection = 'desc';
        $this->selectedMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $this->selectedYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');
        $this->selectedDate = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y-m-d');
    }

    private function isEligibleForOvertime($item)
    {
        $employee = session('employee');

        if (!$employee) return false;
        $leaveOrOTSubmitted = $this->otRequestRepo->checkLeaveOrOTSubmitted($item->date_en, $employee->id);

        if ($leaveOrOTSubmitted) return false;

        $inTime = $item->in_time ? Carbon::parse($item->in_time) : null;
        $outTime = $item->out_time ? Carbon::parse($item->out_time) : null;
        $dutyStart = Carbon::parse($item->duty_start);
        $dutyEnd = Carbon::parse($item->duty_end);
        $totalShiftHours = $dutyStart->diffInMinutes($dutyEnd);

        $totalWorkingTime = 0;
        if ($inTime && $outTime) {
            $totalWorkingTime = $outTime->diffInMinutes($inTime);
        }

        // Check overtime threshold
        $minOtThreshold = konnectHrm()
            ? ($this->otRequestRepo->minDifferenceForOt($item->status))
            : $this->minInOutTimeDifferenceForOt;

        $otMinutes = $totalWorkingTime - $totalShiftHours;

        // Check if eligible based on week or month
        if (str_contains(strtolower($item->status), 'present')) {
            return $totalWorkingTime >= $totalShiftHours
                && $otMinutes >= $minOtThreshold
                && (
                    konnectHrm()
                    ? $this->otRequestRepo->checkWeekDate($item->date_en, false, $item->status)
                    : $this->isCurrentMonthYear()
                );
        } else {
            if (konnectHrm()) {
                return $totalWorkingTime >= $totalShiftHours
                    && $this->otRequestRepo->checkWeekDate($item->date_en, false, $item->status)
                    && $otMinutes >= $minOtThreshold;
            }

            return $this->isCurrentMonthYear()
                && ($totalWorkingTime / 60 >= $this->minInOutHourDifferenceForReplacementLeave);
        }
    }

    private function isEligibleForReplacementLeave($item)
    {
        // Check if leave or OT has already been submitted
        $employee = session('employee');
        $leaveOrOTSubmitted = $this->otRequestRepo->checkLeaveOrOTSubmitted($item->date_en, $employee->id);

        if ($leaveOrOTSubmitted)
            return false;

        // Check if both in_time and out_time are present
        if ($item->in_time && $item->out_time) {
            $status = strtolower($item->status);
            $inTime = Carbon::parse($item->in_time);
            $outTime = Carbon::parse($item->out_time);
            $workedHours = $inTime->diffInHours($outTime);

            if (str_contains($status, 'replacement leave')) {
                return $workedHours >= $this->minInOutHourDifferenceForReplacementLeave &&
                    $this->isCurrentMonthYear();
            }
            if (konnectHrm()) {
                return $item->total_minutes >= $this->otRequestRepo->minDifferenceForOt($item->status) &&
                    $this->otRequestRepo->checkWeekDate($item->date_en);
            } else {
                return $inTime->diffInHours($outTime) >= $this->minInOutHourDifferenceForReplacementLeave &&
                    $this->isCurrentMonthYear();
            }
        }
        return false;
    }

    public function showActionItems($item)
    {
        $actionItems = [];

        if (str_contains(strtolower($item->status), 'present') && $this->isEligibleForOvertime($item)) {
            $actionItems[] = konnectHrm() ? 'ot_request_konnect' : 'ot_request';
        }

        if ($item->time_request) {
            $actionItems[] = 'missed_punch';
        }

        if ($item->leave_request) {
            $actionItems[] = 'leave';
        }

        if (
            (str_contains(strtolower($item->status), 'work on day off') ||
                str_contains(strtolower($item->status), 'work on holiday') ||
                str_contains(strtolower($item->status), 'replacement leave')) &&
            $this->isEligibleForReplacementLeave($item)
        ) {
            if ($this->isEligibleForReplacementLeave($item)) {
                $actionItems[] = 'replacement_leave';
            }

            if ($this->isEligibleForOvertime($item)) {
                $actionItems[] = konnectHrm() ? 'ot_request_konnect' : 'ot_request';
            }
        }

        return array_unique($actionItems);
    }

    public function showActionButton($item)
    {
        $actionItems = $this->showActionItems($item);

        return !empty($actionItems);
    }

    private function checkValidDate($validDate, $date)
    {
        if (!$validDate) return true;
        if (!$date) return false;

        $validDate = $validDate instanceof Carbon ? $validDate : Carbon::parse($validDate);
        $date = $date instanceof Carbon ? $date : Carbon::parse($date);

        return $validDate->lte($date);
    }

    private function isCurrentMonthYear()
    {
        $currentNepaliDay = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'd', locale: 'en');
        $currentNepaliMonth = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'm', locale: 'en');
        $currentNepaliYear = LaravelNepaliDate::from(Carbon::now())->toNepaliDate(format: 'Y', locale: 'en');

        return ($this->selectedMonth == $currentNepaliMonth && $this->selectedYear == $currentNepaliYear) || ($currentNepaliDay <= $this->otGraceDay);
    }

    #[Computed()]
    public function myAttendanceList()
    {
        $employee = session('employee');
        $id = null;
        if ($employee) $id = $employee->id;
        else $this->redirectRoute('dashboard');

        $params = [
            "employeeId" => $id,
            "date" => "$this->selectedYear-$this->selectedMonth",
            "sortBy" => $this->sortBy ?? 'date_en',
            "sortDirection" => $this->sortDirection ?? 'desc',
        ];
        return $this->attendanceRepo->attendanceList($params);
    }

    #[Computed()]
    public function teamAttendanceList()
    {
        if (fedexHrm() && !auth()->user()->can(PermissionList::TEAM_MEMBER_ATTENDANCE))
            return [];

        $params = [
            "selectedUnit"       => $this->selectedUnit,
            "selectedDepartment" => $this->selectedDepartment,
            "branchId"           => $this->selectedBranch,
            "regionId"           => scopeRegion() ? $this->regionId : null,
            "selectedDate"       => $this->selectedDate,
            "search"             => $this->search,
        ];

        $query = $this->attendanceRepo->teamMemberAttendanceList($params);
        $results = $query->paginate($this->perPage);

        foreach ($results as $result) {
            $result->company_employee_code = $result->employee?->organizationInfo?->company_employee_code;
            $result->time_request =$this->attendanceRepo->showApplyMissedPunch($result) ?? false;
            $result->leave_request = $this->attendanceRepo->showApplyLeave($result) ?? false;
        }

        return $results;
    }

    #[Computed(persist: true)]
    public function branchList()
    {
        return $this->regionId ? \App\Models\configs\Branch::where('region_id', $this->regionId)->pluck('name', 'id')->toArray() : [];
    }

    #[Computed(persist: true)]
    public function departmentList()
    {
        return $this->attendanceRepo->departmentList();
    }

    #[Computed()]
    public function unitList()
    {
        if (!$this->selectedDepartment) return [];

        return $this->attendanceRepo->unitList($this->selectedDepartment);
    }


    #[Computed(persist: true)]
    public function nepaliMonthList()
    {
        return Constant::NEPALI_MONTH_LIST;
    }

    public function refreshAttendance()
    {
        unset($this->teamAttendanceList);
        unset($this->myAttendanceList);
    }

    public function getRowColorClass($status, $source)
    {
        $cell_color = "";
        switch ($status) {
            case 'Absent':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Leave') !== false);
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $cell_color = "bg-row-missed-punch";
                break;
            case 'Day Off':
                $cell_color = "bg-row-absent";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $cell_color = "bg-row-on-holiday";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $cell_color = "bg-row-absent";
                break;
        }
        switch ($source) {
            case 'Time Request';
                $cell_color = "bg-row-missed-punch";
                break;
        }
        return $cell_color;
    }


    public function render()
    {
        return view('livewire.self-service.attendance.attendance-page');
    }
}
