<?php

namespace App\Livewire\SelfService;

use App\Http\Helpers\QRCodeHelper;
use App\Http\Repositories\Auth\DeviceLogRepository;
use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use App\Http\Repositories\UserRepository;
use App\Livewire\Employees\Forms\ProfileForm;
use App\Models\Device\DeviceDetails;
use App\Models\Employee\Employee as EmployeeModel;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Cache;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class GenerateQrCode extends Component
{
    use WithNotify;
    public EmployeeModel $personalInfo;

    public ProfileForm $profileForm;

    public $dataUri, $codeGenerated, $otp;

    private $userRepository;

    public function render()
    {
        return view('livewire.self-service.generate-qr-code');
    }

    public function __construct()
    {
        $this->userRepository = new UserRepository();
    }

    public function mount()
    {
        $employee = session('employee');
        if (!isset($employee) || $employee == null || isImpersonated()) {
            $this->redirectRoute('dashboard');
        } else {
            $this->personalInfo = $this->userRepository->personalInfo(auth()->user()->employee?->id);
            if (!$this->personalInfo) return;
            // $code = auth()->user()?->username . auth()->user()?->employee->id . "qrCode";
            // $this->dataUri = Cache::get($code) ?? "";
            // $this->codeGenerated = ($this->dataUri == "") ? false : true;
            // $this->otp = ($this->personalInfo->user->otp_expiration_date > date("Y-m-d H:i:s")) ? $this->personalInfo->user->otp : null;
            $this->dataUri = app(abstract: IDeviceLogRepository::class)->getCachedQrCode($this->personalInfo->user->username, auth()->user()->employee?->id);
            $this->codeGenerated = ($this->dataUri == "") ? false : true;
            $this->otp = ($this->personalInfo->user->otp_expiration_date > date("Y-m-d H:i:s")) ? $this->personalInfo->user->otp : null;
            unset($this->deviceList);
        }
    }

    #[Computed(persist: true)]
    public function deviceList()
    {
        return DeviceDetails::where("user_id", session("user_id"))->get()->toArray();
    }

    #[On("regenerateQr")]
    public function regenerateQr()
    {
        return $this->qrCode();
    }

    #[On("generateOTP")]
    public function generateOTP()
    {
        $this->otp = $this->userRepository->generateOTP(auth()->user()?->id);
    }

    private function qrCode()
    {
        $params = [
            'user_id'       => $this->personalInfo->user->id,
            'employee_id'   => auth()->user()->employee?->id,
            'name'          => $this->personalInfo->name,
            'employee_code' => $this->personalInfo->organizationInfo?->company_employee_code,
            'department'    => $this->personalInfo->organizationInfo?->department?->name ?? '',
            'branch'        => $this->personalInfo->organizationInfo?->branch?->name,
            'contact'       => $this->personalInfo->phone,
            'username'      => $this->personalInfo->user->username,
        ];

        $result = app(IDeviceLogRepository::class)->generateQrCode($params);
        if ($result['updateLoginCode']) {
            $this->codeGenerated = true;
            $this->dataUri = $result['qrCode'];
            $this->otp = null;
        }

        return $result['qrCode'];
    }

    public function revokeDevice($deviceDetailId)
    {
        try {
            $revokeDevice = app(IDeviceLogRepository::class)->deactivateDevice(['id' => $deviceDetailId]);
            if ($revokeDevice) {
                unset($this->deviceList);
                $this->notify("Device Revoked successfully")->send();
            } else {
                $this->notify("Failed to revoke device")->type("error")->send();
            }
        } catch (\Exception $e) {
            $this->notify("An error occurred while revoking the device: " . $e->getMessage())->type("error")->send();
        }
    }

    public function resetMpin()
    {
        try {
            $resetMpin = app(IDeviceLogRepository::class)->resetMpin(['user_id' => auth()->user()->id]);
            if ($resetMpin) {
                $this->notify("MPIN reset successfully")->send();
            } else {
                $this->notify("Failed to reset MPIN")->type("error")->send();
            }
        } catch (\Exception $e) {
            $this->notify("An error occurred while resetting MPIN: " . $e->getMessage())->type("error")->send();
        }
    }
}
