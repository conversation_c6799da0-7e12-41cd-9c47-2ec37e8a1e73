<?php

namespace App\Livewire\SelfService;

use App\Http\Repositories\Employees\EmployeeRepository;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Title;
use Livewire\Component;

class EmployeeSearch extends Component
{

    public $search = "";
    public $perPage = 12;
    public $filter = false;
    public $companyId = "";
    public $departmentId = "";
    public $branchId = "";
    public $showTerminatedEmployee = false;

    #[Computed]
    public function employees()
    {
        if (!$this->search) return [];
        return app(EmployeeRepository::class)->searchEmployee([
            'search'          => $this->search,
            'department_id'   => $this->departmentId,
            'company_id'      => $this->companyId,
            'branch_id'       => $this->branchId,
            'show_terminated' => (bool) $this->showTerminatedEmployee,
            'limit'        => (int) $this->perPage,
        ]);
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return Department::pluck("name", "id");
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return Branch::pluck("name", "id");
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::pluck("name", "id");
    }

    public function updatedFilter()
    {
        if (!$this->filter) {
            $this->reset(['departmentId', 'branchId', 'companyId', 'showTerminatedEmployee']);
        }
    }

    #[Title('Search Employee')]
    public function render()
    {
        return view('livewire.self-service.employee-search');
    }
}
