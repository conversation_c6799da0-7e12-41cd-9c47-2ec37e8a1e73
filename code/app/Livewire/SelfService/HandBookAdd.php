<?php

namespace App\Livewire\SelfService;

use Exception;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Illuminate\Support\Facades\Storage;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use App\Models\configs\Company;
use App\Models\configs\Region;
use App\Models\configs\Branch;
use App\Models\SelfService\Handbookdocs;
use App\Models\configs\Department;
use App\Models\configs\Unit;
use Livewire\Attributes\Title;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\File;
use Livewire\Attributes\Validate;
use Livewire\Features\SupportFileUploads\WithFileUploads;

#[Title('Hand Book List')]

class HandBookAdd extends Component
{
    use WithDataTable, WithNotify, WithFileUploads;

    public $editingId = null;

    public $isEditing = false;

    protected $model;

    public $company_id;

    public $name, $region_id, $branch_id, $dept_id, $unit_id, $document, $document_type;

    public $filter_company_id, $filter_region_id, $filter_branch_id, $filter_dept_id, $filter_unit_id, $filter_document_type;
    public $companyList = [];
    public $regionList = [];
    public $branchList = [];
    public $departmentList = [];
    public $unitList = [];

    public $documentType = [
        'policy' => 'Policy',
        'notice' => 'Notice',
    ];

    public function __construct()
    {
        $this->sortDirection = 'asc';
        $this->perPage = 10;
        $this->model = new Handbookdocs;
    }

    public function clear()
    {
        $this->regionList = [];
        $this->branchList = [];
        $this->departmentList = [];
        $this->unitList = [];
    }

    public function mount()
    {
        $this->companyList = Company::all()->pluck('name', 'id')->toArray();
        $this->sortBy = "created_at";
        $this->sortDirection = "desc";
    }

    public function updated($property)
    {
        if (in_array($property, ['filter_company_id'])) {
            $this->resetPage();
        }

        if (in_array($property, ['filter_company_id', 'filter_region_id', 'filter_branch_id', 'filter_dept_it', 'filter_unit_id', 'filter_document_type'])) {
            unset($this->list);
        }
    }

    public function updatedCompanyId()
    {
        if (!$this->company_id) {
            $this->regionList = [];
            $this->branchList = [];
            $this->departmentList = [];
            $this->unitList = [];
            return [];
        }

        $this->regionList = Region::where('company_id', $this->company_id)->pluck('name', 'id')->toArray();
        $this->branchList = Branch::where('company_id', $this->company_id)->pluck('name', 'id')->toArray();
        $this->departmentList = Department::where('company_id', $this->company_id)->pluck('name', 'id')->toArray();

        // Reset dependent dropdowns (region, branch, department, and unit)
        $this->region_id = null;
        $this->branch_id = null;
        $this->dept_id = null;
        $this->unit_id = null;
    }

    public function updatedFilterCompanyId()
    {
        if (!$this->filter_company_id) {
            $this->regionList = [];
            $this->branchList = [];
            $this->departmentList = [];
            $this->unitList = [];
            return [];
        }

        $this->regionList = Region::where('company_id', $this->filter_company_id)->pluck('name', 'id')->toArray();
        $this->branchList = Branch::where('company_id', $this->filter_company_id)->pluck('name', 'id')->toArray();
        $this->departmentList = Department::where('company_id', $this->filter_company_id)->pluck('name', 'id')->toArray();

        // Reset dependent dropdowns (region, branch, department, and unit)
        $this->region_id = null;
        $this->branch_id = null;
        $this->dept_id = null;
        $this->unit_id = null;
    }

    public function updatedRegionId()
    {
        if (!$this->region_id) {
            $this->branchList = [];
            return [];
        }

        $this->branchList = Branch::where('region_id', $this->region_id)->pluck('name', 'id')->toArray();
        $this->branch_id = null;
        $this->dept_id = null;
        $this->unit_id = null;
        $this->unitList = [];
    }

    public function updatedFilterRegionId()
    {
        if (!$this->filter_region_id) {
            $this->branchList = [];
            return [];
        }

        $this->branchList = Branch::where('region_id', $this->filter_region_id)->pluck('name', 'id')->toArray();
    }

    public function updatedBranchId()
    {
        if (!$this->branch_id) {
            $this->departmentList = [];

            return [];
        }
        $this->departmentList = Department::where('company_id', $this->company_id)->pluck('name', 'id')->toArray();

        $this->dept_id = null;
        $this->unit_id = null;
        $this->unitList = [];
    }

    public function updatedFilterBranchId()
    {
        if (!$this->filter_branch_id) {
            $this->departmentList = [];
            return [];
        }

        $this->departmentList = Department::where('company_id', $this->filter_company_id)->pluck('name', 'id')->toArray();
    }

    public function updatedDeptId()
    {
        if ($this->dept_id) {
            $this->unitList = Unit::where('department_id', $this->dept_id)->pluck('name', 'id')->toArray();
        } else {
            $this->unitList = [];
        }

        $this->unit_id = null;
    }

    public function updatedFilterDeptId()
    {
        if ($this->filter_dept_id) {
            $this->unitList = Unit::where('department_id', $this->filter_dept_id)->pluck('name', 'id')->toArray();
        } else {
            $this->unitList = [];
        }

        $this->filter_unit_id = null;
    }

    public function addDocument()
    {

        $this->validate(
            [
                'name' => 'required|string|max:50',
                'document' => 'required|file|mimes:pdf,docx,png,jpeg,jpg,doc|max:10240',
                'document_type' => 'required|string|max:255',
                'company_id' => 'required|exists:companies,id',
            ],
            [
                'company_id.required' => 'The company field is required.',
            ]
        );

        try {
            if ($this->isEditing) {
                $doc = Handbookdocs::find($this->editingId);

                if ($doc->document) {
                    File::delete(public_path('storage/' . $doc->document));
                    $path = $this->document->store('uploads/handbook', 'public');
                } else {
                    $path = $doc->document;
                }
                $data = [
                    'name' => $this->name,
                    'company_id' => $this->company_id,
                    'region_id' => $this->region_id,
                    'branch_id' => $this->branch_id,
                    'dept_id' => $this->dept_id,
                    'document' => $path,
                    'document_type' => $this->document_type,
                ];
                $doc->update($data);
            } else {
                $path = $this->document->store('uploads/handbook', 'public');
                Handbookdocs::create([
                    'name' => $this->name,
                    'company_id' => $this->company_id,
                    'region_id' => $this->region_id,
                    'branch_id' => $this->branch_id,
                    'dept_id' => $this->dept_id,
                    'document' => $path,
                    'document_type' => $this->document_type,
                ]);
            }
            $this->notify("Document added successfully")->send();
            unset($this->list);
            $this->resetPage();
        } catch (Exception $e) {
            logError("Error while processing handbook", $e);
            $this->notify("Unable to add document")->type('error')->send();
        }
        $this->resetForm();
        $this->dispatch('closemodal');
    }

    #[On('hide.bs.modal')]
    public function resetForm()
    {
        $this->dispatch('clear-document-uploads');
        $this->reset([
            'isEditing',
            'name',
            'company_id',
            'region_id',
            'branch_id',
            'dept_id',
            'document',
            'document_type',
        ]);
        $this->resetErrorBag(
            [
                'name',
                'company_id',
                'region_id',
                'branch_id',
                'dept_id',
                'document',
                'document_type',
            ]
        );
    }

    private function listQuery()
    {
        $query = Handbookdocs::with(['company', 'region', 'branch', 'department', 'unit']);
        // Apply filters if they exist
        if ($this->filter_company_id) {
            $query->where('company_id', $this->filter_company_id);
        }
        if ($this->filter_region_id) {
            $query->where('region_id', $this->filter_region_id);
        }
        if ($this->filter_branch_id) {
            $query->where('branch_id', $this->filter_branch_id);
        }
        if ($this->filter_dept_id) {
            $query->where('department_id', $this->filter_dept_id);
        }
        if ($this->filter_unit_id) {
            $query->where('unit_id', $this->filter_unit_id);
        }
        if ($this->filter_document_type) {
            $query->where('document_type', $this->filter_document_type);
        }
        return $this->applySorting($query); 
    }

    #[Computed()]
    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    public function edit($id)
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function delete($id)
    {
        $doc = Handbookdocs::findOrFail($id);

        // Delete the file if it exists
        if ($doc->document && Storage::disk('public')->exists($doc->document)) {
            Storage::disk('public')->delete($doc->document);
        }
        $doc->delete();
        unset($this->list);
        $this->resetPage();

        $this->message = 'Handbook deleted successfully!!';
        $this->notify($this->message)->send();
    }

    public function resetList()
    {
        $this->reset(['name', 'company_id', 'editing_id', 'region_id', 'branch_id', 'dept_id']);
        $this->resetErrorBag();
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = $this->listQuery();

        // If a search term is present, apply the search method
        if ($this->search) {
            $query = $query->search($this->search);
        }

        return $this->applySorting($query)->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.self-service.hand-book-add');
    }
}
