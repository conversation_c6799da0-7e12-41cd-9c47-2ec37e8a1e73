<?php

namespace App\Livewire\SelfService;

use App\Http\Helpers\Constant;
use App\Http\Repositories\IopsRepository;
use App\Models\User;
use App\Traits\WithNotify;
use Closure;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Layout('layouts.guest')]
#[Title('Change Password')]
class ChangePassword extends Component
{
    use WithNotify;

    #[Rule("boolean")]
    public $terms = false;

    public $showBackButton = true;

    public $old_password;

    #[Rule("required|string|min:8|different:old_password")]
    public $new_password;

    #[Rule("required|same:new_password")]
    public $confirm_new_password;

    public function mount()
    {
        $this->showBackButton = !session(Constant::SESSION_PASSWORD_CHANGE_TIME) || auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN);
    }

    public function rules()
    {
        return [
            "old_password" => [
                "required",
                function (string $attribute, mixed $value, Closure $fail) {
                    if (!Hash::check($value, Auth::user()->getAuthPassword())) {
                        $fail("The {$attribute} field is invalid");
                    }
                },
            ]
        ];
    }

    public function changePassword()
    {
        if (!$this->terms) {
            $this->notify("Please accept terms and condition")->type("error")->send();
            return;
        }
        $this->validate();
        DB::beginTransaction();
        try {
            $user = User::find(Auth::user()->id);
            $user->password = Hash::make($this->new_password);
            $user->password_change_date = date("Y-m-d H:i:s");
            $user->save();

            $employee_iops_id = \App\Models\Employee\Employee::leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
                ->where('user_id', $user->id)
                ->pluck('employee_org.iops_id')
                ->first();

            if ((vianetHrm() || konnectHrm()) && $employee_iops_id) {
                $iopsRepo = new IopsRepository;
                $response = $iopsRepo->changePassword($this->old_password, $this->new_password, $employee_iops_id);

                if (!$response['status']) {
                    $this->notify($response['message'])->type('error')->send();
                    return;
                }
            }

            // Logout user form other device when password is changed by removing session.
            $currentSessionId = session()->getId();
            DB::table('sessions')->where('user_id', $user->id)->where('id', '!=', $currentSessionId)->delete();

            DB::commit();
            session([Constant::SESSION_PASSWORD_CHANGE_TIME => false]);
            $this->reset();
            $this->resetErrorBag();
            $this->notify("Password changed successfully")->send();
            redirect(route('dashboard'));
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Unable to change password. Error:' . $e->getMessage());
            $this->notify("Failed to change password")->type('error')->send();
        }
    }

    public function logout()
    {
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();
        redirect(route('dashboard'));
    }

    public function render()
    {
        return view('livewire.self-service.change-password');
    }
}
