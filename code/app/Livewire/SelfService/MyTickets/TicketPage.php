<?php

namespace App\Livewire\SelfService\MyTickets;

use App\Http\Repositories\TicketRepository;
use App\Traits\TicketAction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Ticket Detail')]
class TicketPage extends Component
{
    use TicketAction;

    public string $workflow;
    public int $requestId;

    public $isPage = true;

    public function mount($workflow, $requestId)
    {
        $this->workflow = $workflow;
        $this->requestId = $requestId;
        $this->setTitle();
        $this->change_owner_id = $this->detail?->current_owner_id;
        if(isset($this->detail?->previousOwners) && $this->detail?->previousOwners != null && count($this->detail?->previousOwners) == 1)
            $this->previous_owner_id = $this->detail?->previousOwners[0]['id'];

        if(isset($this->detail?->reviewers) && $this->detail?->reviewers != null && count($this->detail?->reviewers) == 1)
            $this->reviewer_id = $this->detail?->reviewers[0]['id'];
        
        if(isset($this->detail?->ownersForChanging) && $this->detail?->ownersForChanging != null && count($this->detail?->ownersForChanging) == 1)
            $this->change_owner_id = $this->detail?->ownersForChanging[0]['id'];

        if(isset($this->detail?->ownersForEscalation) && $this->detail?->ownersForEscalation != null && count($this->detail?->ownersForEscalation) == 1)
            $this->escalate_id = $this->detail?->ownersForEscalation[0]['id'];

        if(isset($this->detail?->nextOwners) && $this->detail?->nextOwners != null && count($this->detail?->nextOwners) == 1)
            $this->next_owner_id = $this->detail?->nextOwners[0]['id'];
    }

    #[Computed(persist: true)]
    public function detail()
    {
        $response = $this->repo->getTicket(workflow: $this->workflow, requestId: $this->requestId);
        if ($response['status']) {
            return $response['data'];
        }
        $this->notify($response['message'])->type("error")->send();
        abort(403, $response['message']);
        return null;
    }

    #[On('refresh-ticket')]
    public function refresh()
    {
        unset($this->detail);
        $this->dispatch('refreshDocument');
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.ticket-page');
    }
}
