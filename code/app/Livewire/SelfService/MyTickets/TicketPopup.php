<?php

namespace App\Livewire\SelfService\MyTickets;

use App\Http\Repositories\repository;
use App\Http\Repositories\TicketRepository;
use App\Models\RequestTicket;
use App\Traits\TicketAction;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Url;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithPagination;

class TicketPopup extends Component
{
    use TicketAction;

    #[Reactive]
    public $requestId;
    public $localRequestId;

    #[Reactive]
    public $workflow;
    public $localWorkflow;

    public function boot()
    {
        if ($this->workflow != $this->localWorkflow || $this->requestId != $this->localRequestId) {
            $this->localWorkflow = $this->workflow;
            $this->localRequestId = $this->requestId;
            unset($this->detail);
            $this->setTitle();
            
            $this->change_owner_id = $this->detail?->current_owner_id;
            if(isset($this->detail?->previousOwners) && $this->detail?->previousOwners != null && count($this->detail?->previousOwners) == 1)
                $this->previous_owner_id = $this->detail?->previousOwners[0]['id'];

            if(isset($this->detail?->reviewers) && $this->detail?->reviewers != null && count($this->detail?->reviewers) == 1)
                $this->reviewer_id = $this->detail?->reviewers[0]['id'];

            if(isset($this->detail?->ownersForChanging) && $this->detail?->ownersForChanging != null && count($this->detail?->ownersForChanging) == 1)
                $this->change_owner_id = $this->detail?->ownersForChanging[0]['id'];

            if(isset($this->detail?->ownersForEscalation) && $this->detail?->ownersForEscalation != null && count($this->detail?->ownersForEscalation) == 1)
                $this->escalate_id = $this->detail?->ownersForEscalation[0]['id'];

            if(isset($this->detail?->nextOwners) && $this->detail?->nextOwners != null && count($this->detail?->nextOwners) == 1)
                $this->next_owner_id = $this->detail?->nextOwners[0]['id'];
            
            $this->dispatch("reloadTooltip");
        }
    }

    #[Computed(persist: true)]
    public function detail()
    {
        if ($this->localWorkflow && $this->localRequestId) {
            $response = $this->repo->getTicket($this->localRequestId, $this->localWorkflow);
            if ($response['status']) {
                return $response['data'];
            } else {
                $this->notify($response['message'])->type("error")->send();
            }
        }
        return null;
    }

    public function refresh()
    {
        $this->dispatch("hide-model");
        $this->dispatch("refresh-list");
    }

    #[On('ticketPopupHidden')]
    public function modalHidden()
    {
        $this->reset(["title", "localWorkflow", "localRequestId"]);
        unset($this->detail);
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.ticket-popup');
    }
}
