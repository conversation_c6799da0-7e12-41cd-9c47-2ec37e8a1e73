<?php

namespace App\Livewire\SelfService\MyTickets\Components;

use <PERSON>Bright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Models\Leaves\ReplacementLeaveRequest as mReplacementLeaveRequest;
use App\Models\RequestTicket;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class ReplacementLeaveRequest extends Component
{
    use WithNotify, WithFileUploads;

    #[Locked]
    public $edit = false;

    #[Locked]
    public $disableDate = false;

    #[Modelable]
    public $editingId;
    public array $removingDocumentIds = [];
    public $existingDocuments;

    public $employeeId, $employeeName, $fiscalYearId;

    public $replacement_request_id;

    public $nep_start_date, $nep_end_date, $leave_option_id, $remarks, $verifier_id, $documents = [];
    public $payload;
    public function rules()
    {
        return [
            'nep_start_date'    => 'required',
            'nep_end_date'      => ['required', function ($attribute, $value, $fail) {
                if ($this->nep_start_date > $this->nep_end_date) {
                    $fail('End date should be greater than start date.');
                }
            }],
            'leave_option_id'   => 'required',
            'remarks'           => 'required',
            'verifier_id'       => 'required',
            'documents.*'       => 'file|mimes:pdf,docx,doc,jpg,jpeg,png'
        ];
    }

    public function validationAttributes()
    {
        return [
            'nep_start_date'    => 'start date',
            'nep_end_date'      => 'end date',
            'verifier_id'       => 'verifier',
            'remarks'           => 'reason',
        ];
    }

    public function mount()
    {
        $this->fiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
        $this->employeeId = session(Constant::SESSION_EMPLOYEE_ID);
    }

    public function boot()
    {
        if ($this->editingId && $this->replacement_request_id !== $this->editingId) {
            $this->fillForm($this->editingId);
        }
    }

    #[Computed(persist: true)]
    public function verifiers()
    {
        return (new mReplacementLeaveRequest)->getNextOwners(employeeId: $this->employeeId, isSubmitting: true);
    }

    #[Computed(persist: true)]
    public function replacementLeaveOptions()
    {
        return \App\Models\Leaves\LeaveOption::list(true);
    }

    public function fillForm($id)
    {
        $replacementRequest = mReplacementLeaveRequest::with('requestTicket')
            ->where('id', $id)
            ->firstOrFail();
        $this->replacement_request_id = $replacementRequest->id;
        $this->nep_start_date = LaravelNepaliDate::from($replacementRequest->start_date)->toNepaliDate();
        $this->nep_end_date = LaravelNepaliDate::from($replacementRequest->start_date)->toNepaliDate();
        $this->leave_option_id = $replacementRequest->leave_option_id;
        $this->remarks = $replacementRequest->remarks;
        $this->verifier_id = $replacementRequest->requestTicket->current_owner_id;
        $this->existingDocuments = $replacementRequest->requestTicket->getDocuments();
    }

    #[On('updateSelectedReplacementLeaveAttendance')]
    public function updateSelectedReplacementLeaveAttendance($payload)
    {
        $this->payload = $payload;
        if ($payload['date']) {
            $this->nep_start_date = $payload['date'];
            $this->nep_end_date = $payload['date'];
        }
        unset($this->verifiers);
    }

    #[On('hidden.bs.modal')]
    public function resetData()
    {
        $this->reset([
            'nep_start_date',
            'nep_end_date',
            'leave_option_id',
            'remarks',
            'verifier_id',
        ]);
        $this->dispatch("clear-documents");
        $this->resetValidation();
    }

    public function save()
    {
        $this->validate();
        DB::beginTransaction();
        try {
            \logInfo("Parameters for applying leave: ", $this->all());
            $verifierIds = (new mReplacementLeaveRequest)->getNextOwners(employeeId: $this->employeeId, isSubmitting: true)->map(fn($verifier) => $verifier->id)->toArray();
            if (!in_array($this->verifier_id, $verifierIds)) {
                $this->addError('verifier_id', 'Invalid next owner');
                \logError("Invalid verifier: " . $this->verifier_id);
                return;
            }

            if ($this->disableDate && $this->payload['date'] && ($this->nep_start_date != $this->payload['date'] || $this->nep_end_date != $this->payload['date'])) {
                $title = "Tampered in Replacement Leave Request Date Changed";
                $employee = \App\Models\Employee\Employee::find($this->employeeId);
                $employeeName = $employee->name;
                $employeeCode = $employee->companyEmpCode;

                $this->notify("Your activity has been reported to the developer team.")->type("error")->send();

                $message = "Employee: {$employeeName}[{$employeeCode}] has tampered in Replacement Leave Request Date Changed.<br/>Please verify the request.<br/>Old Date: {$this->payload['date']} <br/>New Start Date: {$this->nep_start_date}<br/>New End Date: {$this->nep_end_date}";
                \App\Jobs\MailToDeveloper::dispatch($title, $message);

                $this->nep_start_date = $this->payload['date'];
                $this->nep_end_date = $this->payload['date'];
            }

            $startDate = (LaravelNepaliDate::from($this->nep_start_date))->toEnglishDate();
            $endDate = (LaravelNepaliDate::from($this->nep_end_date))->toEnglishDate();

            $leaveOption = \App\Models\Leaves\LeaveOption::where([
                ['id', $this->leave_option_id],
                ['replacement_available', true]
            ])->first();
            if (!$leaveOption) {
                $this->notify("Leave Option Not Available")->type("error")->send();
                return;
            }
            $multiplier = $leaveOption->num_days;

            $commonFields = [
                'start_date'            => $startDate,
                'end_date'              => $endDate,
                'fiscal_year_id'        => $this->fiscalYearId,
                'leave_option_id'       => $this->leave_option_id,
                'remarks'               => $this->remarks,
                'num_days'              => (Carbon::parse($startDate)->diffInDays($endDate) + 1) * $multiplier,
                'documents'             => $this->documents,
                'removing_document_ids' => $this->removingDocumentIds
            ];

            $status = false;
            if ($this->editingId) {
                $status = $this->updateReplacementLeaveRequest($commonFields);
            } else {
                $status = $this->createReplacementLeaveRequest($commonFields);
            }
            if (!$status) return;

            DB::commit();
            $this->dispatch('hide-model');
            \logInfo('Replacement leave request and request ticket' . ($this->editingId ? 'Updated' : 'Created') . ' Successfully');
            $this->notify('Replacement leave request ' . ($this->editingId ? 'Updated' : 'Created') . ' Successfully')->type('success')->send();
            $this->dispatch("request-saved");
        } catch (\Exception $e) {
            DB::rollBack();
            \logError('Error while processing replacement leave request: ', $e);
            $this->notify('Error while processing replacement leave request')->type('error')->send();
        }
    }

    private function updateReplacementLeaveRequest($commonFields)
    {
        $replacementRequest = mReplacementLeaveRequest::findOrFail($this->editingId);
        \logInfo('Updating leave request with ID: ' . $this->editingId);
        \logInfo('Parameters for updating leave request: ', $commonFields);
        if ($this->checkLeaveOverlap($commonFields['start_date'], $commonFields['end_date'], $replacementRequest->id)) {
            $this->addError('nep_start_date', 'Request overlaps with another request.');
            return false;
        }
        $replacementRequest->update($commonFields);
        \logInfo("Leave Request Updated", $replacementRequest->toArray());
        $ticketRepo = new \App\Http\Repositories\TicketRepository;
        $ticketRepo->updateRequestTicket($replacementRequest, [
            'employee_id'       => $this->employeeId,
            'current_owner_id'  => $this->verifier_id,
            'documents'         => $this->documents
        ]);
        return true;
    }

    private function createReplacementLeaveRequest($commonFields)
    {
        \logInfo('Creating new replacement leave request');
        if ($this->checkLeaveOverlap($commonFields['start_date'], $commonFields['end_date'])) {
            $this->addError('nep_start_date', 'Request overlaps with another applied request.');
            return false;
        }

        $row = array_merge($commonFields, [
            'employee_id' => $this->employeeId,
        ]);
        \logInfo('Parameters for creating replacement leave request: ', $row);
        $replacementRequest = mReplacementLeaveRequest::create($row);
        \logInfo("Replacement Leave Request Created: ", $replacementRequest->toArray());
        $ticketRepo = new \App\Http\Repositories\TicketRepository;
        $ticketRepo->createRequestTicket($replacementRequest, [
            'employee_id'       => $this->employeeId,
            'current_owner_id'  => $this->verifier_id,
            'documents'         => $this->documents
        ]);
        return true;
    }

    private function checkLeaveOverlap($startDate, $endDate, $ignoreId = null)
    {
        $overlapExists = mReplacementLeaveRequest::where('employee_id', $this->employeeId)
            ->where(function ($query) use ($startDate, $endDate) {
                // Check if the specified range overlaps with existing leave requests
                $query
                    ->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate]);
            })
            ->whereNotIn('state', [WorkflowState::CANCELLED, WorkflowState::REJECTED])
            ->when($ignoreId, function ($query) use ($ignoreId) {
                $query->where('id', '!=', $ignoreId);
            })
            ->exists();
        return $overlapExists;
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.components.replacement-leave-request');
    }
}
