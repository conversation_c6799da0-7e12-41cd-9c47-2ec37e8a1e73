<?php


namespace App\Livewire\SelfService\MyTickets\Components;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Repositories\DutyRosterRepository;
use App\Models\configs\DutyRoster;
use App\Models\configs\EmployeeShift;
use App\Models\Employee\EmployeeOrg;
use App\Models\EmployeeTicket\DutyChangeTicket;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Attributes\On;

class DutyChangeRequest extends Component
{
    use WithNotify;

    public $existingShiftName = "";

    #[Validate('required|exists:employees,id')]
    public $employee_id = "";

    #[Validate('required')]
    public $date = "";

    #[Validate('nullable')]
    public $shift_id = "";

    #[Validate('nullable')]
    public $existing_shift_id = "";

    #[Validate('required', attribute: 'approver')]
    public $approver_id = "";

    #[Validate('required|max:191')]
    public $reason = "";


    public function mount()
    {
        $this->initialize();
    }
    public function initialize()
    {
        $this->employee_id = currentEmployee()?->id;
        $this->date = LaravelNepaliDate::from(Carbon::now())->toNepaliDate('Y-m-d');
        $this->updatedDate();
    }

    public function updatedDate()
    {
        if ($this->date) {
            if ($this->validateDate()) {
                $this->validateOnly('date');
                $existingShift = DutyRoster::where([['date_np', $this->date], ['employee_id', $this->employee_id]])->first()?->shift
                    ?? currentEmployee()?->organizationInfo?->shift;
                if ($existingShift) {
                    $this->existing_shift_id = $existingShift->id;
                    $this->existingShiftName = "$existingShift->name ({$existingShift->start_time} - {$existingShift->end_time})";
                } else {
                    $this->existing_shift_id = "";
                    $this->existingShiftName = "Day Off";
                }
            }
        }
    }

    #[Computed(persist: true)]
    public function shiftList()
    {
        $shiftList = [
            'day-off' => "Day Off"
        ];
        foreach (EmployeeShift::where('is_active', 1)->get() as $shift) {
            $shiftList[$shift->id] = "$shift->name ({$shift->start_time} - {$shift->end_time})";
        }
        return $shiftList;
    }

    #[On('hide.bs.modal')]
    public function hideModel()
    {
        $this->reset([
            'date',
            'shift_id',
            'existing_shift_id',
            'existingShiftName',
            'approver_id',
            'reason'
        ]);
        $this->initialize();
    }


    public function validateDate()
    {
        $engDate = LaravelNepaliDate::from($this->date)->toEnglishDate();
        if (Carbon::parse($engDate)->endOfDay()->lt(Carbon::now())) {
            $this->addError('date', 'Date must be equal or greater than today');
            return false;
        }
        return true;
    }

    public function validateShifts()
    {
        $validated = true;
        if ($this->shift_id === "day-off") {
            $shift = EmployeeShift::find($this->shift_id);
            if (!$shift) {
                $this->addError('shift_id', 'Invalid shift');
                $validated = false;
            }
        }

        if ($this->existing_shift_id === "day-off") {
            $shift = EmployeeShift::find($this->existing_shift_id);
            if (!$shift) {
                $this->addError('shift_id', 'Invalid shift');
                $validated = false;
            }
        }

        if ($this->shift_id == $this->existing_shift_id) {
            $this->addError('shift_id', 'Shift cannot be same as existing shift');
            $validated = false;
        }

        return $validated;
    }

    public function save()
    {
        if (!$this->validateDate() || !$this->validateShifts()) return;
        $this->validate();
        $nextOwners = (new DutyChangeTicket)->getNextOwners(employeeId: $this->employee_id, isSubmitting: true)->map(fn($verifier) => $verifier->id)->toArray();
        if (!in_array($this->approver_id, $nextOwners)) {
            $this->addError('approver_id', 'Invalid Approver');
            Log::error("Invalid next owner: " .  $this->approver_id);
            return;
        }

        $dutyChangeRepo = new DutyRosterRepository;
        $response = $dutyChangeRepo->createDutyRosterTicket([
            'employee_id'           => $this->employee_id,
            'date_en'               => LaravelNepaliDate::from($this->date)->toEnglishDate(),
            'existing_shift_id'     => $this->existing_shift_id,
            'shift_id'              => $this->shift_id,
            'date_np'               => $this->date,
            'approver_id'           => $this->approver_id,
            'reason'                => $this->reason,
        ]);
        if (!$response['status']) {
            $this->notify($response['message'])->type('error')->send();
            return;
        }

        $this->notify($response['message'])->send();
        $this->dispatch('hide-model');
        $this->dispatch('request-saved');
    }

    #[Computed(persist: true)]
    public function verifiers()
    {
        return (new DutyChangeTicket)->getNextOwners(employeeId: $this->employee_id, isSubmitting: true);
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.components.duty-change-request');
    }
}
