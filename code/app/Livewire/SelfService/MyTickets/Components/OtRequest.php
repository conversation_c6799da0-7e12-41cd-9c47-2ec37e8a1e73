<?php

namespace App\Livewire\SelfService\MyTickets\Components;

use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Repositories\OtRequestRepository;
use App\Http\Repositories\TicketRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\OtRequests;
use Livewire\Attributes\Locked;

class OtRequest extends Component
{
    use WithNotify;

    public $nep_date;
    public $in_time, $out_time, $duty_start, $duty_end, $type, $remarks;
    public $total_working_hours, $total_ot_hours;
    public $verifier_id, $employee_id;
    public $assignVerifiers = [];
    public $isVerifier = false;
    public $isApprover = false;
    public $minMInutesForOt = 30; //Minimum time for OT to be applied.In this case its 30minutes from shift end.

    #[Locked]
    public $disableDate = false;

    private OtRequestRepository $otRequestRepo;

    public function __construct()
    {
        $this->otRequestRepo = new OtRequestRepository;
    }

    public function mount()
    {
        $this->employee_id = session(Constant::SESSION_EMPLOYEE_ID);
        $this->total_working_hours = 0;
        $this->total_ot_hours = 0;
        $this->fetchVerifiers();
        $this->type = 'Regular';
    }

    public function fetchVerifiers()
    {
        $transitionList = TransitionPerformer::where([
            ['workflow', WorkflowName::OT_REQUEST],
            ['recipient_id', $this->employee_id],
        ])->get();

        $this->isApprover = $transitionList->contains(
            fn($transition) =>
            $transition->performer_id === currentEmployee()?->id &&
                $transition->state === WorkflowPerformer::APPROVER
        );
        $this->isVerifier = $transitionList->contains(
            fn($transition) =>
            $transition->performer_id === currentEmployee()?->id &&
                $transition->state === WorkflowPerformer::VERIFIER
        );

        $verifier_ids =  $transitionList->filter(fn($transition) => $transition->state == WorkflowPerformer::VERIFIER)
            ->pluck('performer_id')->toArray();
        $this->assignVerifiers = Employee::find($verifier_ids)?->pluck('name', 'id')->toArray();
    }

    public function updatedNepDate()
    {
        $this->attInOutTime();
        $this->fetchVerifiers();
    }
    public function attInOutTime()
    {
        try {
            if (!$this->nep_date) return;
            $date = \CodeBright\LaravelNepaliDate\LaravelNepaliDate::from($this->nep_date)->toEnglishDate();
            if ($date) {
                $attendance = \App\Models\Leaves\Attendance::where('date_en', $date)
                    ->where('employee_id', currentEmployee()?->id)
                    ->select('in_time', 'out_time', 'duty_start', 'duty_end')
                    ->first();
                $this->in_time = $attendance?->in_time;
                $this->out_time = $attendance?->out_time;
                $this->duty_start = $attendance?->duty_start;
                $this->duty_end = $attendance?->duty_end;

                if ($this->in_time && $this->out_time) {
                    $inTime = \Carbon\Carbon::parse($this->in_time);
                    $outTime = \Carbon\Carbon::parse($this->out_time);
                    $dutyendTime = \Carbon\Carbon::parse($this->duty_end);

                    $totalHours = $outTime->diffInHours($inTime);
                    $totalMinutes = $outTime->diffInMinutes($inTime) % 60;

                    $this->total_working_hours = "{$totalHours} hours {$totalMinutes} minutes";

                    if ($outTime->greaterThan($this->duty_end)) {
                        $otMinutes = $outTime->diffInMinutes($this->duty_end);
                        if ($otMinutes >= $this->minMInutesForOt) {
                            $otHours = floor($otMinutes / 60);
                            $otMinutes = $otMinutes % 60;
                            $this->total_ot_hours = "{$otHours} hours {$otMinutes} minutes";
                        } else {
                            $this->total_ot_hours = "0 hours 0 minutes";
                        }
                    } else {
                        $this->total_ot_hours = "0 hours 0 minutes";
                    }
                } else {
                    $this->total_working_hours = "0 hours 0 minutes";
                    $this->total_ot_hours = "0 hours 0 minutes";
                }
            } else {
                $this->in_time = null;
                $this->out_time = null;
            }
        } catch (\Exception $e) {
            Log::error("Error while generating attendance: " . $e->getMessage());
            $this->in_time = null;
            $this->out_time = null;
        }
    }

    public function save()
    {
        $this->validate([
            'nep_date' => 'required',
            'in_time' => 'required',
            'out_time' => 'required',
            'verifier_id' => 'required|exists:employees,id',
            'remarks'     => 'required',
        ]);
        DB::beginTransaction();
        try {
            $this->in_time = \Carbon\Carbon::createFromFormat('h:i A', $this->in_time)->format('H:i:s');
            $this->out_time = \Carbon\Carbon::createFromFormat('h:i A', $this->out_time)->format('H:i:s');

            $data = [
                "nep_date"              => $this->nep_date,
                "in_time"               => $this->in_time,
                "out_time"              => $this->out_time,
                "employee_id"           => $this->employee_id,
                "duty_start"            => $this->duty_start,
                "duty_end"              => $this->duty_end,
                "total_working_hours"   => $this->total_working_hours,
                "total_ot_hours"        => $this->total_ot_hours,
                "type"                  => $this->type,
                "remarks"               => $this->remarks,
                "verifier_id"           => $this->verifier_id,
            ];
            $this->otRequestRepo->createOtRequest($data);

            DB::commit();
            $this->notify('OT request created successfully')->send();
            $this->dispatch('hide-model');
            $this->dispatch("request-saved");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error while saving OT request: " . $e->getMessage());
            $this->notify("Error while saving OT request")->type("error")->send();
        }
    }

    #[On('hidden.bs.modal')]
    public function resetData()
    {
        $this->reset([
            'nep_date',
            'in_time',
            'out_time',
            'duty_start',
            'duty_end',
            'type',
            'remarks',
            'total_working_hours',
            'total_ot_hours',
            'verifier_id',
        ]);
        $this->resetValidation();
    }

    #[On('updateSelectedOtRequest')]
    public function updateSelectedOtRequest($payload)
    {
        if ($payload['date']) {
            $this->nep_date = $payload['date'];
            $this->attInOutTime();

        }
        unset($this->verifiers);
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.components.ot-request');
    }
}
