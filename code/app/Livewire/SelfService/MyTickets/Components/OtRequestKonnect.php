<?php

namespace App\Livewire\SelfService\MyTickets\Components;

use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Repositories\OtRequestRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\configs\Setting;
use App\Models\OtRequests;
use App\Models\Payroll\Payslip;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;
use PermissionList;

class OtRequestKonnect extends Component
{
    use WithNotify, MultiselectEmployeeSearch;

    public $in_time, $out_time, $duty_start, $duty_end, $type, $remarks;
    public $total_working_hours, $total_ot_hours, $actualWorkingHours;
    public $verifier_id, $otRequestEmployeeId, $employee_id;
    public $assignVerifiers = [], $subordinateEmployeeList = [];
    public $isVerifier = false, $isApprover = false, $showOtFields = false;
    public $nep_date_ot_apply, $nep_date_ot_assign;

    public $extraMinutesForOtApplyForKonnect = 60;
    #[Locked]
    public $disableDate = false;

    #[Locked]
    public $assign = false;

    #[Locked]
    public $edit = false;

    private OtRequestRepository $otRequestRepo;

    public function __construct()
    {
        $this->otRequestRepo = new OtRequestRepository;
    }

    public function rules()
    {
        $rules = [
            'verifier_id' => 'required',
        ];
        if ($this->assign) {
            $rules['otRequestEmployeeId'] = 'required';
            if ($this->isVerifier) {
                $rules['verifier_id'] = 'required';
            } else {
                unset($rules['verifier_id']);
            }
        }
        return $rules;
    }
    public function mount()
    {
        if ($this->assign) {
            $this->employee_id = $this->otRequestEmployeeId;
        } else {
            $this->employee_id = currentEmployee()?->id;
        }
        $this->total_working_hours = 0;
        $this->total_ot_hours = 0;
        $this->fetchVerifiers();
        $this->type = 'Regular';
        if ($this->assign) {
            $this->setPermissionForEmployeeDropdown();
            $this->setSubordinateEmployee();
        }
    }

    public function updatedOtRequestEmployeeId($value)
    {
        if ($this->assign && $value) {
            $this->employee_id = $value;
            $this->otRequestEmployeeId = $value;
            $this->reset('isApprover', 'isVerifier', 'verifier_id');
            $this->nep_date_ot_assign = null;
            $this->remarks = "Applied OT request for {$this->employeeName()}";
            $this->fetchVerifiers();
        }
    }

    public function employeeName()
    {
        $employee = Employee::find($this->otRequestEmployeeId);
        return $employee?->name . '[' . $employee?->employeeCode . ']';
    }

    public function setSubordinateEmployee()
    {
        $cacheName = 'ot-request-assign-employees' . currentEmployee()?->id;
        $result = Cache::store('arflow')->remember($cacheName, null, function () {
            //Base Query
            $employeeListQuery = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
                ->leftJoin('companies as comp', 'comp.id', 'employees.company_id');

            //Filter Logic 
            $assignAccordingToScope = auth()->user()->can(PermissionList::OT_REQUEST_ASSIGN_SCOPE);
            if ($assignAccordingToScope) {
                $employeeListQuery = filterEmployeesByScope($employeeListQuery, 'org');
            } else {
                $recipientIds = TransitionPerformer::where([['workflow', WorkflowName::OT_REQUEST], ['performer_id', currentEmployee()?->id]])
                    ->distinct('recipient_id')->pluck('recipient_id')->toArray();
                $employeeListQuery = $employeeListQuery->whereIn('employees.id', $recipientIds);
            }

            //Employee Names
            $employees = $employeeListQuery->select(
                'employees.id',
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as fullName"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as empCode")
            )
                ->orderBy('employees.first_name')->get();
            $employeeArr = [];
            foreach ($employees as $employee) {
                $employeeArr[$employee->id] = "{$employee->fullName} ({$employee->empCode})";
            }
            return $employeeArr;
        });
        $this->subordinateEmployeeList = $result;
    }

    protected function transitionPerformer($employeeId)
    {
        $transitionList = TransitionPerformer::where([
            ['workflow', WorkflowName::OT_REQUEST],
            ['recipient_id', $employeeId],
        ])->get();

        $this->isApprover = $transitionList->contains(
            fn($transition) =>
            $transition->performer_id === $employeeId &&
                $transition->state === WorkflowPerformer::APPROVER
        );
        $this->isVerifier = $transitionList->contains(
            fn($transition) =>
            $transition->performer_id === $employeeId &&
                $transition->state === WorkflowPerformer::VERIFIER
        );
        return $transitionList;
    }

    public function fetchVerifiers()
    {
        $transitionList = $this->assign
            ? $this->transitionPerformer($this->otRequestEmployeeId)
            : $this->transitionPerformer(currentEmployee()?->id);

        $verifier_ids =  $transitionList->filter(fn($transition) => $transition->state == WorkflowPerformer::VERIFIER)
            ->pluck('performer_id')->toArray();
        $this->assignVerifiers = Employee::find($verifier_ids)?->pluck('name', 'id')->toArray();
    }

    public function updated($arr, $value)
    {
        if ($arr == 'nep_date_ot_apply' && !$this->assign) {
            $this->employee_id = currentEmployee()?->id;
            $this->attInOutTime($value);
        } elseif ($arr == 'nep_date_ot_assign' && $this->assign && $this->otRequestEmployeeId) {
            $this->employee_id = $this->otRequestEmployeeId;
            $this->attInOutTime($value);
        }
    }


    public function attInOutTime($date)
    {
        try {
            if (!$date) {
                return;
            }
            $employee_id = $this->assign ? $this->employee_id : (currentEmployee()?->id);
            $attendance = $this->otRequestRepo->getAttendanceData($date, $employee_id);
            $otAllow = $this->otRequestRepo->isOtAllowed($this->employee_id);

            if (!$otAllow) {
                return $this->notify("OT request is not allowed for this employee")->type('error')->send();
            }

            if (!$attendance) {
                return $this->notify("Attendance not found for the selected date")->type('error')->send();
            }

            $otType = $attendance->status;
            $weekTillOtApply = $this->otRequestRepo->checkWeekDate($date, true, $otType);
            $assignMaxDays = (int) $this->otRequestRepo->getOtSettingMaxDays($otType);

            if ($this->assign) {
                if (!$weekTillOtApply) {
                    $this->showOtFields = false;
                    return $this->notify("OT request can only be applied within $assignMaxDays days.")->type('error')->send();
                }
            }

            $ticketExists = $this->otRequestRepo->checkLeaveOrOTSubmitted($attendance->date_en, $employee_id);
            if ($ticketExists) {
                return $this->notify("Leave or OT request already submitted for this date")->type('error')->send();
            }

            if (!$attendance) {
                $this->resetAttendanceFields();
                return;
            }

            $this->attendanceFields($attendance);

            if ($this->in_time && $this->out_time) {
                $result = $this->otRequestRepo->calculateWorkingAndOtHours([
                    'in_time'       => $this->in_time,
                    'out_time'      => $this->out_time,
                    'duty_start'    => $this->duty_start,
                    'duty_end'      => $this->duty_end,
                    'status'        => $attendance->status,
                    'total_hours'   => $attendance->total_hours,
                    'extra_time'    => $this->extraMinutesForOtApplyForKonnect
                ]);

                $this->total_working_hours = $result['total_working_hours'];
                $this->total_ot_hours = $result['total_ot_hours'];
                $this->type = $result['status'];
                $this->showOtFields = $result['is_ot_eligible'] ?? false;

                if (!$this->showOtFields && isset($result['message'])) {
                    $this->notify($result['message'])->type('error')->send();
                }
            } else {
                $this->resetWorkingHours();
            }
        } catch (\Exception $e) {
            Log::error("Error while generating attendance: " . $e->getMessage());
            $this->resetAttendanceFields();
        }
    }

    protected function attendanceFields($attendance)
    {
        $this->in_time = $attendance?->in_time;
        $this->out_time = $attendance?->out_time;
        $this->duty_start = $attendance?->duty_start;
        $this->duty_end = $attendance?->duty_end;
    }

    public function save()
    {
        if (!$this->isFormValidForSubmission()) {
            return;
        }
        if ($this->assign) {
            $date = $this->nep_date_ot_assign;
        } else {
            $date = $this->nep_date_ot_apply;
        }

        DB::beginTransaction();
        try {
            $this->in_time = \Carbon\Carbon::createFromFormat('h:i A', $this->in_time)->format('H:i:s');
            $this->out_time = \Carbon\Carbon::createFromFormat('h:i A', $this->out_time)->format('H:i:s');

            $data = [
                "nep_date"              => $date,
                "in_time"               => $this->in_time,
                "out_time"              => $this->out_time,
                "employee_id"           => $this->employee_id,
                "duty_start"            => $this->duty_start,
                "duty_end"              => $this->duty_end,
                "total_working_hours"   => $this->total_working_hours,
                "total_ot_hours"        => $this->total_ot_hours,
                "type"                  => $this->type,
                "remarks"               => $this->remarks,
                "verifier_id"           => $this->verifier_id,
            ];
            $this->otRequestRepo->createOtRequest($data);

            DB::commit();
            $this->notify('OT request created successfully')->send();
            $this->dispatch('hide-model');
            $this->dispatch("request-saved");
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error while saving OT request: " . $e->getMessage());
            $this->notify("Error while saving OT request")->type("error")->send();
        }
    }

    protected function isFormValidForSubmission(): bool
    {
        // Check OT eligibility first
        if (!$this->showOtFields) {
            $this->notify('OT request is not eligible for submission')->type('error')->send();
            return false;
        }

        // Validate required fields
        $this->validate([
            'in_time' => 'required',
            'out_time' => 'required',
            'verifier_id' => 'required|exists:employees,id',
            'remarks' => 'required',
        ]);
        if ($this->assign) {
            $this->validate([
                'otRequestEmployeeId' => 'required',
                'nep_date_ot_assign' => 'required',
            ]);
        }else{
            $this->validate([
                'nep_date_ot_apply' => 'required',
            ]);
        }

        return true;
    }

    #[On('hidden.bs.modal')]
    public function resetData()
    {
        $this->reset([
            'nep_date_ot_apply',
            'nep_date_ot_assign',
            'in_time',
            'out_time',
            'duty_start',
            'duty_end',
            'type',
            'remarks',
            'total_working_hours',
            'total_ot_hours',
            'verifier_id',
        ]);
        if ($this->assign) {
            $this->dispatch("toggle-employee-id", ['']);
            $this->otRequestEmployeeId = "";
            $this->nep_date_ot_assign = null;
        }else{
            $this->nep_date_ot_apply = null;
        }
        $this->showOtFields = false;
        $this->resetValidation();
    }

    #[On('updateSelectedOtRequestKonnect')]
    public function updateSelectedOtRequestKonnect($payload)
    {
        if ($payload['date']) {
            $this->nep_date_ot_apply = $payload['date'];
            $this->attInOutTime($this->nep_date_ot_apply);
        }
        unset($this->verifiers);
    }

    protected function resetAttendanceFields()
    {
        $this->in_time = null;
        $this->out_time = null;
        $this->duty_start = null;
        $this->duty_end = null;
        $this->showOtFields = false;
        $this->resetWorkingHours();
    }

    protected function resetWorkingHours()
    {
        $this->total_working_hours = "0 hours 0 minutes";
        $this->total_ot_hours = "0 hours 0 minutes";
    }

    protected function resetOtHours()
    {
        $this->total_ot_hours = "0 hours 0 minutes";
    }
    public function render()
    {
        return view('livewire.self-service.my-tickets.components.ot-request-konnect');
    }
}
