<?php

namespace App\Livewire\SelfService\MyTickets\Components;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Repositories\Attendance\IrregularityTicketRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;
use Livewire\Component;
use PermissionList;

class IrregularityRequest extends Component
{
    use WithNotify, MultiselectEmployeeSearch;

    #[Locked]
    public $assign = false;

    #[Locked]
    public $edit = false;

    #[Modelable]
    public $editingId;
    public $tempEditingId;

    // for fields
    public $irregularityEmployeeId;

    #[Validate('required|in:Early Out,Late In')]
    public $type;

    #[Validate('required', as: 'date')]
    public $date_np;

    #[Validate('required|max:300')]
    public $remarks;

    #[Validate(as: 'verifier')]
    public $verifier_id;

    // attendance detail
    public $attDetail = false, $in_time, $out_time, $status;

    public $isApprover = true, $isVerifier = false, $assignVerifiers = [], $subordinateEmployeeList = [];
    private IrregularityTicketRepository $repo;

    public function __construct()
    {
        $this->repo = new IrregularityTicketRepository;
    }

    public function rules()
    {
        $rules = [
            'verifier_id' => 'required',
        ];
        if ($this->assign) {
            $rules['irregularityEmployeeId'] = 'required';
            if ($this->isVerifier) {
                $rules['verifier_id'] = 'required';
            } else {
                unset($rules['verifier_id']);
            }
        }
        return $rules;
    }

    public function mount()
    {
        $this->irregularityEmployeeId = currentEmployee()?->id;
        if ($this->assign) {
            $this->setPermissionForEmployeeDropdown();
            $this->setSubordinateEmployee();
        }
    }

    public function boot()
    {
        if ($this->editingId && $this->tempEditingId !== $this->editingId) {
            $this->fillFormData($this->editingId);
        }
    }

    #[Computed(persist: true)]
    public function irregularityTypes()
    {
        return ['Late In', 'Early Out'];
    }

    public function updatedEmployeeId($value)
    {
        if ($this->assign) {
            $this->irregularityEmployeeId = $value;
            $this->reset('isApprover', 'isVerifier', 'verifier_id');
            $this->date_np = null;
            $this->attDetail = false;
            $this->setVerifiersForAssign();
        }
    }

    #[Computed(persist: true)]
    public function verifiers()
    {
        return (new \App\Models\Attendance\IrregularityTicket())
            ->getNextOwners(employeeId: $this->irregularityEmployeeId, isSubmitting: true);
    }

    public function updatedDateNp()
    {
        $this->reset(['attDetail', 'in_time', 'out_time', 'status']);
        $response = $this->repo->validateDate($this->date_np, $this->irregularityEmployeeId);
        if (!$response['status']) {
            $this->addError('date_np', $response['message']);
            return false;
        }
        $this->resetValidation('date_np');
        $attDetail = $response['data'];
        $this->attDetail = true;
        $this->in_time = $attDetail->in_time;
        $this->out_time = $attDetail->out_time;
        $this->status = $attDetail->status;
        return $response['data'];
    }

    public function fillFormData()
    {
        if (!$this->editingId) return;

        $response = $this->repo->getIrregularityTicket($this->editingId);
        if (!$response['status'])
            return $this->notify($response['message'])->type("error")->send();

        $model = $response['data'];
        $this->tempEditingId = $model->id;
        $this->irregularityEmployeeId = $model->employee_id;
        $this->type = $model->type;
        $this->date_np = $model->date_np;
        $this->updatedDateNp();
        $this->remarks = $model->remarks;
        $this->verifier_id = $model->requestTicket->current_owner_id;
    }

    public function save()
    {
        $this->validate();
        if ($this->assign && $this->irregularityEmployeeId == currentEmployee()?->id) {
            return $this->notify("Self assign cannot be done.")->type("error")->send();
        }
        $status = $this->updatedDateNp();
        if (!$status) return;
        $attendance = $status;
        // if ($this->type === 'Late In' && !$attendance->in_time) return $this->notify("In Time doesn't exist")->type("error")->send();
        // if ($this->type === 'Early Out' && !$attendance->out_time) return $this->notify("Out Time doesn't exist")->type("error")->send();

        $response = [];
        $params = [
            'employee_id'   => $this->irregularityEmployeeId,
            'type'          => $this->type,
            'date_en'       => LaravelNepaliDate::from($this->date_np)->toEnglishDate(),
            'date_np'       => $this->date_np,
            'remarks'       => $this->remarks,
            'verifier_id'   => $this->verifier_id,
        ];
        if ($this->editingId) {
            $response = $this->repo->updateIrregularityTicket($this->editingId, $params);
        } else {
            if ($this->assign) {
                $response = $this->repo->assignIrregularityTicket($params);
            } else {
                $response = $this->repo->createIrregularityTicket($params);
            }
        }

        if (!$response['status']) {
            $this->notify($response['message'])->type("error")->send();
            return;
        }
        $this->notify("Irregularity Request created")->send();
        $this->dispatch('hide-model');
        $this->dispatch("request-saved");
    }

    #[On('irregularityRequestModalHidden')]
    public function test()
    {
        $this->reset(['type', 'date_np', 'remarks', 'verifier_id', 'in_time', 'out_time', 'status', 'tempEditingId']);
        $this->resetValidation();
    }

    public function setSubordinateEmployee()
    {
        $cacheName = 'attendance-irregularity-request-assign-employees' . currentEmployee()?->id;
        $result = Cache::store('arflow')->remember($cacheName, null, function () {
            $recipientIds = TransitionPerformer::where([['workflow', WorkflowName::IRREGULARITY_TICKET], ['performer_id', currentEmployee()?->id]])
                ->distinct('recipient_id')->pluck('recipient_id')->toArray();
            $employeeListQuery = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
                ->leftJoin('companies as comp', 'comp.id', 'employees.company_id');

            $assignAccordingToScope = auth()->user()->can(PermissionList::IRREGULARITY_REQUEST_ASSIGN_SCOPE);
            if ($assignAccordingToScope) {
                $employeeListQuery = filterEmployeesByScope($employeeListQuery, 'org');
            } else {
                $recipientIds = TransitionPerformer::where([['workflow', WorkflowName::IRREGULARITY_TICKET], ['performer_id', currentEmployee()?->id]])
                    ->distinct('recipient_id')->pluck('recipient_id')->toArray();
                $employeeListQuery = $employeeListQuery->whereIn('employees.id', $recipientIds);
            }
            $employees = $employeeListQuery->select(
                'employees.id',
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as fullName"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as empCode")
            )
                ->orderBy('employees.first_name')->get();
            $employeeArr = [];
            foreach ($employees as $employee) {
                $employeeArr[$employee->id] = "{$employee->fullName} ({$employee->empCode})";
            }
            return $employeeArr;
        });
        $this->subordinateEmployeeList = $result;
    }


    public function setVerifiersForAssign()
    {
        $this->isApprover = true;
        $this->isVerifier = false;
        if (!$this->irregularityEmployeeId) {
            $this->assignVerifiers = [];
            return;
        }
        $canAssignScope = auth()->user()->can(PermissionList::IRREGULARITY_REQUEST_ASSIGN_SCOPE);
        if ($canAssignScope) return;

        $transitionList = TransitionPerformer::where([
            ['workflow', WorkflowName::IRREGULARITY_TICKET],
            ['recipient_id', $this->irregularityEmployeeId]
        ])->get();

        $isApprover = count($transitionList->filter(fn($transition) => $transition->performer_id === currentEmployee()?->id && $transition->state === WorkflowPerformer::APPROVER));

        $this->isApprover = $isApprover;
        if ($isApprover) {
            $this->assignVerifiers = currentEmployee()?->id;
            $this->verifier_id = $this->assignVerifiers;
            return;
        }

        $isVerifier = count($transitionList->filter(fn($transition) => $transition->performer_id === currentEmployee()?->id && $transition->state === WorkflowPerformer::VERIFIER));

        $this->isVerifier = $isVerifier;
        if (!$isVerifier) {
            return $this->notify("You can't assign irregularity request to this person")->type("error")->send();
        }

        $verifierIds = $transitionList->filter(fn($transition) => $transition->state === WorkflowPerformer::APPROVER)->pluck('performer_id')->toArray();

        $this->assignVerifiers = Employee::find($verifierIds)?->pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.components.irregularity-request');
    }
}
