<?php

namespace App\Livewire\SelfService\MyTickets\Components;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Repositories\Attendance\AttendanceDashboardRepository;
use Carbon\Carbon;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Http\Repositories\TimeRequestRepository;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\RequestTicket;
use App\Models\TimeRequest as mTimeRequest;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Modelable;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;
use PermissionList;

class TimeRequest extends Component
{
    use WithNotify, WithFileUploads, MultiselectEmployeeSearch;

    #[Locked]
    public $assign = false;

    #[Locked]
    public $edit = false;

    #[Modelable]
    public $editingId;

    public $employeeId, $employeeName;

    public $isApprover = true, $isVerifier = false, $assignVerifiers = [], $subordinateEmployeeList = [];

    public $attInTime, $attOutTime;

    public array $removingDocumentIds = [];

    public $existingDocuments;

    public $time_request_id, $nep_date, $in_time, $in_note, $out_time, $out_note, $verifier_id, $documents = [];

    private AttendanceDashboardRepository $attRepo;

    public function __construct() {
        $this->timeRequestRepo = new TimeRequestRepository();
        $this->attRepo = new AttendanceDashboardRepository;
    }

    public function rules()
    {
        $rules =  [
            "nep_date" => ["required", function ($attribute, $value, $fail) {
                try {
                    $todayNepaliDate = LaravelNepaliDate::from(\Carbon\Carbon::today())->toNepaliDate();
                    $nepaliDate = $value;
                    $comparison = compareNepaliDates($todayNepaliDate, $nepaliDate);
                    if ($comparison == -1) {
                        $fail("The date should not be greater than today.");
                    }
                    $englishDate = LaravelNepaliDate::from($value)->toEnglishDate();
                    $employeeId = $this->employee_id ?? currentEmployee()?->id;

                    $isValid = $this->timeRequestRepo->isTimeDateValid($englishDate, $employeeId);

                    if (! $isValid) {
                        $fail("You cannot select a date older than the allowed limit.");
                    }
                } catch (\Exception $e) {
                    Log::error($e->getMessage());
                    $fail("Invalid date format provided.");
                }
            }],
            "in_time"     => "required_if:out_time,null",
            "in_note"     => "required_with:in_time|max:255",
            "out_time"    => "required_if:in_time,null",
            "out_note"    => "required_with:out_time|max:255",
            "verifier_id" => "required",
            'documents.*' => 'file|mimes:pdf,docx,doc,jpg,jpeg,png'
        ];
        if ($this->assign) {
            $rules['employeeId'] = 'required';
            if ($this->isVerifier) {
                $rules['verifier_id'] = 'required';
            } else {
                unset($rules['verifier_id']);
            }
        }
        return $rules;
    }

    public function validationAttributes()
    {
        return [
            'nep_date'    => 'date',
            'verifier_id' => 'verifier',
            'employeeId'  => 'employee',
        ];
    }

    public function messages()
    {
        return [
            'in_time.required_if' => "Either in time or out time is required",
            'out_time.required_if' => "Either in time or out time is required"
        ];
    }

    public function mount()
    {
        // $this->singleSelectAttributes = ['leave-request-employee_id', 'time-request-employee_id'];
        if (!$this->assign) {
            $this->employeeId = session(Constant::SESSION_EMPLOYEE_ID);
        }
        if ($this->assign) {
            $this->setPermissionForEmployeeDropdown();
            $this->setSubordinateEmployee();
        }
        $this->renderEmployeeList = false;
    }

    public function boot()
    {
        if ($this->editingId && $this->time_request_id !== $this->editingId) {
            $this->fillForm($this->editingId);
            $this->setAttInOutTime();
        }
    }

    #[Computed(persist: true)]
    public function verifiers()
    {
        return (new mTimeRequest)->getNextOwners(employeeId: $this->employeeId, isSubmitting: true);
    }

    #[On('hidden.bs.modal')]
    public function resetData()
    {
        $this->reset([
            'time_request_id',
            'nep_date',
            'in_time',
            'in_note',
            'out_time',
            'out_note',
            'verifier_id',
            'attInTime',
            'attOutTime',
        ]);
        if ($this->assign) {
            $this->reset(['employeeId']);
            $this->dispatch("toggle-employee-id", ['']);
        }
        $this->dispatch("clear-documents");
        $this->resetValidation();
    }

    #[On('updateSelectedMissedPunchAttendance')]
    public function updateSelectedMissedPunchAttendance($payload)
    {
        $this->employeeId = $payload['employeeId'] ?? session(Constant::SESSION_EMPLOYEE_ID);

        $this->employeeName = $payload['employeeName'];
        if ($payload['date']) {
            $this->nep_date = $payload['date'];
            $this->setAttInOutTime();
        }
        unset($this->verifiers);
    }

    public function updatedNepDate()
    {
        $this->setAttInOutTime();
    }

    public function updatedEmployeeId()
    {
        if ($this->assign) {
            $this->setVerifiersForAssign();
            $this->setAttInOutTime();
        }
    }

    public function setAttInOutTime()
    {
        try {
            if (!$this->nep_date) return;
            $date = $this->nep_date;
            if ($date) {
                $params = [
                            'employee_id' => $this->employeeId,
                            'nep_date'    => $this->nep_date,
                        ];
                $attendance = $this->timeRequestRepo->recordedTime($params);

                $this->attInTime = $attendance?->in_time;
                $this->attOutTime = $attendance?->out_time;
            } else {
                $this->attInTime = null;
                $this->attOutTime = null;
            }
        } catch (\Exception $e) {
            Log::error("Error while loading attendance: " . $e->getMessage());
            $this->attInTime = null;
            $this->attOutTime = null;
        }
    }

    public function setSubordinateEmployee()
    {
        $cacheName = 'time-request-assign-employees' . currentEmployee()?->id;
        $result = Cache::store('arflow')->remember($cacheName, null, function () {
            $recipientIds = TransitionPerformer::where([['workflow', WorkflowName::TIME_REQUEST_APPROVAL], ['performer_id', currentEmployee()?->id]])
                ->distinct('recipient_id')->pluck('recipient_id')->toArray();
            $employeeListQuery = Employee::leftJoin('employee_org as org', 'org.employee_id', 'employees.id')
                ->leftJoin('companies as comp', 'comp.id', 'employees.company_id');

            $assignAccordingToScope = auth()->user()->can(PermissionList::TIME_REQUEST_ASSIGN_SCOPE);
            if ($assignAccordingToScope) {
                $employeeListQuery = filterEmployeesByScope($employeeListQuery, 'org');
            } else {
                $recipientIds = TransitionPerformer::where([['workflow', WorkflowName::TIME_REQUEST_APPROVAL], ['performer_id', currentEmployee()?->id]])
                    ->distinct('recipient_id')->pluck('recipient_id')->toArray();
                $employeeListQuery = $employeeListQuery->whereIn('employees.id', $recipientIds);
            }
            $employees = $employeeListQuery->select(
                'employees.id',
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as fullName"),
                DB::raw("CONCAT(comp.code, '-', org.employee_code) as empCode")
            )
                ->orderBy('employees.first_name')->get();
            $employeeArr = [];
            foreach ($employees as $employee) {
                $employeeArr[$employee->id] = "{$employee->fullName} ({$employee->empCode})";
            }
            return $employeeArr;
        });

        $this->subordinateEmployeeList = $result;
    }

    public function setVerifiersForAssign()
    {
        $this->isApprover = true;
        $this->isVerifier = false;
        if (!$this->employeeId) {
            $this->assignVerifiers = [];
            return;
        }
        $canAssignScope = auth()->user()->can(PermissionList::TIME_REQUEST_ASSIGN_SCOPE);
        if ($canAssignScope) return; // no need to show verifiers

        $transitionsList = TransitionPerformer::where([
            ['workflow', WorkflowName::TIME_REQUEST_APPROVAL],
            ['recipient_id', $this->employeeId]
        ])->get();

        $isApprover = count(
            $transitionsList->filter(
                fn($transition) =>
                $transition->performer_id === currentEmployee()?->id && $transition->state === WorkflowPerformer::APPROVER
            )
        );
        $this->isApprover = $isApprover;
        if ($isApprover) {

            $this->assignVerifiers = [];
            return;
        }

        $isVerifier = count(
            $transitionsList->filter(
                fn($transition) =>
                $transition->performer_id === currentEmployee()?->id && $transition->state === WorkflowPerformer::VERIFIER
            )
        );
        $this->isVerifier = $isVerifier;
        if (!$isVerifier) {
            return $this->notify("You can't assign time request to this person")->type("error")->send();
        }
        $verifierIds =  $transitionsList->filter(fn($transition) => $transition->state == WorkflowPerformer::APPROVER)
            ->pluck('performer_id')->toArray();
        $this->assignVerifiers = Employee::find($verifierIds)?->pluck('name', 'id')->toArray();
    }

    public function save()
    {
        $this->validate();

        try {
            $params = [
                'employee_id'           => $this->employeeId,
                'nep_date'              => $this->nep_date,
                'in_time'               => $this->in_time,
                'in_note'               => $this->in_note,
                'out_time'              => $this->out_time,
                'out_note'              => $this->out_note,
                'verifier_id'           => $this->verifier_id,
                'documents'             => $this->documents,
                'removing_document_ids' => $this->removingDocumentIds
            ];

            $timeRequestRepo = new TimeRequestRepository;

            $data = $timeRequestRepo->createOrUpdateTimeRequest($params, $this->editingId, $this->assign);

            $this->notify('Time request ' . ($this->editingId ? 'Updated' : 'Created') . ' Successfully')->send();

            if ($this->assign) {
                return redirect()->route('ticketPage', [
                    'requestId' => $data['model_id'],
                    'workflow' => $data['workflow'],
                ]);
            }

            $this->dispatch('hide-model');
            $this->dispatch('request-saved');
        } catch (\Exception $e) {
            $this->notify($e->getMessage())->type('error')->send();
        }
        $this->dispatch('reloadTooltip');
    }

    public function fillForm($id)
    {
        $timeRequest = $this->timeRequestRepo->getTimeRequest($id);
        $this->time_request_id = $timeRequest->id;
        $this->nep_date = $timeRequest->nep_date;
        $this->in_time = $timeRequest->in_time;
        $this->in_note = $timeRequest->in_note;
        $this->out_time = $timeRequest->out_time;
        $this->out_note = $timeRequest->out_note;
        $this->verifier_id = $timeRequest->requestTicket->current_owner_id;
        $this->existingDocuments = $timeRequest->requestTicket->getDocuments();
    }

    public function render()
    {
        return view('livewire.self-service.my-tickets.components.time-request');
    }
}
