<?php

namespace App\Livewire\SelfService\MyTickets;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Models\RequestTicket;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;
use PermissionList;

#[Title('My Tickets')]
class MyTickets extends Component
{
    use WithNotify, WithPagination, MultiselectEmployeeSearch;
    // for showing different buttons
    #[Url(as: "type", keep: true)]
    public $listFilter = "toBeReviewedRequests";

    #[Url(as: "state", keep: true)]
    public $selectedState = 'all';

    #[Url(as: "flow")]
    public $workflow = "all";
    public $selectedWorkflows = [];

    #[Url(as: "id")]
    public $requestId = "";

    public $selectedTitle;
    public $selectedTicketNo;

    public $selectedUuid = "";

    public $stateList = [];

    public $editingIds = [
        'leave_approval' => null,
        'time_request_approval' => null,
        'replacement_leave_request' => null,
        'attendance_irregularity_request' => null,
    ];

    // filters
    public $filters = [
        "type" => "all",
        "perPage" => 10,
        "search" => ""
    ];

    public $selectedRequestId, $selectedWorkflow, $company, $employee_ids = [];

    private TicketRepository $ticketRepo;

    #[Rule('required|max:255')]
    public $comment = "";

    public function __construct()
    {
        $this->ticketRepo = new TicketRepository;
    }

    public function mount()
    {
        // array_push($this->selectedWorkflows, $this->workflow)
        $this->workflowStateList();

        $this->multiSelectAttributes = ['employee_ids'];
        $this->withTrashed = true;
    }

    public function workflowStateList()
    {
        $constants = WorkflowState::getConstants();

        $formatted = collect($constants)->values()->sort()->mapWithKeys(fn($value) => [$value => $value])->toArray();

        if (!array_key_exists('all', $formatted)) {
            $formatted = ['all' => 'All States'] + $formatted;
        }

        $this->stateList = $formatted;
    }


    #[Computed(persist: true)]
    public function requestList()
    {
        if (in_array($this->filters['perPage'], [10, 25, 50])) {
            return $this->ticketRepo->getTickets(
                $this->listFilter,
                [...$this->filters, 'workflow' => $this->workflow, 'state' => $this->selectedState, 'requestId' => $this->requestId, 'employee_ids' => $this->employee_ids]
            );
        }
        $this->notify("Invalid per page")->type('info')->send();
        return [];
    }

    public function updated($attr)
    {
        $this->dispatch("reloadTooltip");
        if (str_starts_with($attr, 'filters')) {
            $this->resetPage();
            // $this->reset('workflow', 'requestId');
            unset($this->requestList);
        }
        if (\in_array($attr, ['workflow', 'employee_ids', 'selectedState'])) {
            $this->resetPage();
            unset($this->requestList);
        }
    }

    public function updatedPaginators($page, $pageName)
    {
        unset($this->requestList);
    }

    public function componentView($workflow)
    {
        $viewsConfig = config('arflow-config.ticketViews');

        $workflowView = $viewsConfig[$workflow];
        // dd($this->detail->workflow, $workflowView);
        if ($workflowView) {
            return $workflowView['card'] ?? '';
        }
        return "";
    }


    public function setTicketId($uuid)
    {
        $item = $this->requestList->filter(fn($item) => $item->uuid === $uuid)->first();
        if ($item) {
            $this->selectedRequestId = $item->id;
            $this->selectedWorkflow = $item->id;
        }
    }

    public function changeListFilter($name)
    {
        $this->listFilter = $name;
        $this->resetPage();
        $this->reset('filters', 'requestId');
        $this->workflow = "all";
        unset($this->requestList);
    }

    public function refreshList()
    {
        unset($this->requestList);
    }

    public function setEditingId(string $uuid)
    {
        $item = $this->requestList->filter(fn($item) => $item->uuid === $uuid)->first();
        $this->editingIds[$item->workflow] = $item->id;
    }

    public function setDetail($uuid)
    {
        $item = $this->requestList->filter(fn($item) => $item->uuid === $uuid)->first();
        if ($item) {
            $this->selectedRequestId = $item->id;
            $this->selectedWorkflow = $item->workflow;
        } else {
            $this->notify("Detail Not Found");
        }
    }

    public function setIdForAction(string $uuid, string $type)
    {
        $this->selectedUuid = $uuid;
        $this->selectedTitle = $type;
        $item = $this->requestList->filter(fn($item) => $item->uuid == $uuid)->first();
        if (!$item)
            return $this->notify("Item not found")->type("error")->send();
        $this->selectedTicketNo = $item->ticket_id;
    }

    public function changeState(string $state, string $uuid)
    {
        if (!$state == WorkflowState::CANCELLED)
            $this->validate();
        $item = $this->requestList->filter(fn($item) => $item->uuid == $uuid)->first();
        if (!$item)
            return $this->notify("Item not found")->type("error")->send();
        $response = $this->ticketRepo->changeState($item?->ticket_id, $state, "", null);
        $this->notify($response['message'])->type($response['status'] ? "success" : "error")->send();
        if ($response['status']) {
            unset($this->requestList);
            $this->dispatch('hide-model');
        }
    }

    #[On('approvePopupHidden')]
    public function clearFormData()
    {
        $this->reset(['comment']);
    }

    public function render()
    {
        if (!in_array($this->listFilter, ['toBeReviewedRequests', 'reviewedRequests', 'myRequests', 'myRequestsHistory'])) {
            abort(404);
        }
        $workflows = $this->ticketRepo->ticketWorkflows(auth()->user());
        return view('livewire.self-service.my-tickets.my-tickets', compact('workflows'));
    }
}
