<?php

namespace App\Livewire\SelfService;

use App\Http\Helpers\Enums\WorkflowState;
use App\Jobs\SendResetPasswordEmailJob;
use App\Models\Employee\Employee;
use App\Models\Termination\EmployeeTermination;
use App\Models\User;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Computed;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Employee Profile')]
class SearchedProfile extends Component
{
    use WithNotify;

    public Employee $employeeInfo;

    public $resetPasswordEmail, $password;

    public $onNoticePeriod, $noticedDate, $terminationDate;

    public function mount($id)
    {
        if (empty($id)) {
            $this->redirectRoute('dashboard');
        } else {
            $this->employeeInfo = Employee::with('organizationInfo.unit:id,name', 'organizationInfo.department:id,name', 'organizationInfo.branch:id,name', 'organizationInfo.subBranch:id,name')
                ->withTrashed()
                ->findOrFail($id);
            // Restrict viewing hidden profile.
            if ($this->employeeInfo->hide_profile && (!auth()->user()->can(PermissionList::EMPLOYEE_HIDDEN_PROFILE_VIEW) || isImpersonated()) && currentEmployee()?->id != $id) {
                $this->notify('Profile not found.')->type('error')->send();
                return abort(404);
            }

            $hasTerminationTicket = EmployeeTermination::where([['employee_id', $id], ['state', WorkflowState::APPROVED]])->first();
            $this->onNoticePeriod = $hasTerminationTicket ? true : false;
            $this->noticedDate = $hasTerminationTicket?->termination_request_date ?? null;
            $this->terminationDate = $hasTerminationTicket?->termination_date ?? null;
        }
    }

    public function resetUserPassword()
    {
        $employee = $this->employeeInfo->toArray();
        $user = User::leftJoin("employees", 'users.id', '=', 'employees.user_id')->where('user_id', '=', $employee['user_id'])->select('users.id', 'users.username', 'users.password')->first();
        if (!$user) {
            $this->notify('Unable to reset password for the user!!')->type('error')->send();
            return false;
        }

        $this->resetPasswordEmail = $employee['organization_info']['email'] ?? '';
        $this->password = Str::random(10);
        $hashedPassword = Hash::make($this->password);
        DB::beginTransaction();
        try {
            $user->password = $hashedPassword;
            $user->password_change_date = null;
            $user->save();

            if (vianetHrm()) {
                $iopsRepo = new \App\Http\Repositories\IopsRepository;
                $smsContent = "Your HRIS password has been changed. Your new password is: $this->password.";
                $response = $iopsRepo->sendSMS($employee['id'], $smsContent);
                if (!$response['status']) {
                    $this->notify('Unable to send SMS.')->type('error')->send();
                    DB::rollBack();
                    return false;
                }
            }

            DB::commit();
            DB::rollBack();
            if ($user->id !== Auth::id()) {
                // Auth::setUser($user)->logoutOtherDevices($this->password);
                $currentSessionId = session()->getId();
                DB::table('sessions')->where('user_id', $user->id)->where('id', '!=', $currentSessionId)->delete();
            }
            $this->dispatch("show-user-password-model");

            if (!vianetHrm()) {
                try {
                    if ($this->resetPasswordEmail) {
                        $name = ($employee['first_name'] ?? "") . ($employee['middle_name'] ?? "" ? " " . $employee['middle_name'] : "") . " " . ($employee['last_name'] ?? "");
                        SendResetPasswordEmailJob::dispatch($this->resetPasswordEmail, $user->username, $this->password, $name)->onQueue('resetPasswordEmail');
                    } else {
                        $this->notify('Unable to send email. You don\'t have an email.')->type('error')->send();
                    }
                } catch (\Exception $e) {
                    logError('Unable to send email for reset password: ' . $e->getMessage());
                    $this->notify('Unable to send email.')->send();
                }
            }
            $this->notify('Password reset successfully')->type('success')->send();
        } catch (\Exception $e) {
            DB::rollBack();
            logError('Unable to reset password: ' . $e->getMessage());
            $this->notify('Unable to reset password!!')->type('error')->send();
        }
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['resetPasswordEmail', 'password']);
    }

    public function render()
    {
        return view('livewire.self-service.searched-profile');
    }
}
