<?php

namespace App\Livewire\SelfService;

use App\Traits\WithDataTable;
use Livewire\Component;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use App\Models\SelfService\Handbookdocs;

#[Title('Hand Book')]
class HandBook extends Component
{
    use WithDataTable;
    public $company, $region, $branch, $department;

    public $region_id, $branch_id, $dept_id, $unit_id, $document, $document_type;
    public $perPage;

    public $filterCompanyId, $filterRegionId, $filterBranchId, $filterDepartmentId, $filterUnit;
    public $companyList = [];
    public $regionList = [];
    public $branchList = [];
    public $departmentList = [];
    public $unitList = [];
    public $documentType = [
        'policy' => 'Policy',
        'notice' => 'Notice',
    ];
    public $customFilters = [
        'company' => null,
        'branch' => [],
        'unit' => null,
        'region' => null,
        'department' => [],
    ];

    #[Computed()]
    public function data()
    {
        $data = Handbookdocs::latest();
        $currentEmpCompanyId = currentEmployee()?->company_id;
        $currentEmpRegId = currentEmployee()?->organizationInfo->region_id;
        $currentEmpBranchId = currentEmployee()?->organizationInfo->branch_id;
        $currentEmpDeptId = currentEmployee()?->organizationInfo->department_id;
        $currentEmpUnit = currentEmployee()?->organizationInfo->unit_id;

        $data = Handbookdocs::where('company_id', $currentEmpCompanyId)
            ->orWhere('region_id', $currentEmpRegId)
            ->orWhere('branch_id', $currentEmpBranchId)
            ->orWhere('dept_id', $currentEmpDeptId)
            ->orWhere('unit_id', $currentEmpUnit)
            ->latest();
        return $data->paginate($this->perPage);
    }
    private function listQuery()
    {
        $query = Handbookdocs::with(['company', 'region', 'branch', 'department', 'unit'])
            ->when($this->filterCompanyId, function ($query) {
                return $query->where('company_id', $this->filterCompanyId);
            })
            ->when($this->filterRegionId, function ($query) {
                return $query->where('region_id', $this->filterRegionId);
            })
            ->when($this->filterBranchId, function ($query) {
                return $query->where('branch_id', $this->filterBranchId);
            })
            ->when($this->filterDepartmentId, function ($query) {
                return $query->where('department_id', $this->filterDepartmentId);
            })
            ->when($this->filterUnit, function ($query) {
                return $query->where('unit_id', $this->filterUnit);
            });
        return $this->applySorting($query);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return \App\Models\configs\Company::select("id", "name")->orderBy('name', 'asc')->get();
    }

    #[Computed(persist: true)]
    public function regions()
    {
        return \App\Models\configs\Region::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if (!$this->company || !$this->region)
            return [];
        return \App\Models\configs\Branch::select("id", "name")->orderBy('name', 'asc')->where([['company_id', $this->company], ['region_id', $this->region]])->get();
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return \App\Models\configs\Department::select("id", "name")->orderBy('name', 'asc')->where('company_id', $this->company)->get();
    }
    public function render()
    {
        return view('livewire.self-service.hand-book');
    }
}
