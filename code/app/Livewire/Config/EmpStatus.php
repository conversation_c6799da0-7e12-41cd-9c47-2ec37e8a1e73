<?php

namespace App\Livewire\Config;

use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Employee Statuses')]
class EmpStatus extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null;

    public $isEditing = false;

    public $message = null;

    #[Validate('required|string|max:50')]
    public $name = '';

    #[Validate('boolean')]
    public $is_active = true;
    public $remarks;

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\configs\EmpStatus;
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function validationAttributes()
    {
        return [
            'name' => 'status name',
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save(): void
    {
        $this->validate();

        if ($this->isEditing) {
            $this->model->findOrFail($this->editingId)->fill($this->all())->save();
        } else {
            $this->editingId = $this->model->create($this->all())->id;
        }
        $this->reset($this->model->getFillable());
        //unset($this->list); 

        $this->message = 'Employee status saved successfully';
        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id): void
    {
        $empStatus = $this->model->findOrFail($id);

        $empStatusEmployees = EmployeeOrg::where('employee_status_id', $empStatus->id)->exists();
        if ($empStatusEmployees) {
            $this->notify('Cannot delete employee status assigned for employees.')->type('error')->send();
            return;
        }

        $empStatus->delete();
        unset($this->list);
        $this->message = 'Employee status deleted successfully';
        $this->notify($this->message)->send();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset(array_merge($this->model->getFillable(), ['editingId', 'isEditing']));
    }

    #[Computed()]
    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed()]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)->whereNull('deleted_at')
        )->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.config.emp-status');
    }
}
