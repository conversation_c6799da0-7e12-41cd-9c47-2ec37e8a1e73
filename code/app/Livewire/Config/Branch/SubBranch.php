<?php

namespace App\Livewire\Config\Branch;

use App\Models\configs\SubBranch as SubBranchModel;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class SubBranch extends Component
{
    use WithDataTable, WithNotify;
    public $editingId;
    public $name, $address;
    public $branchId, $allow_attendance=0;
    public $subBranchLatitude, $subBranchLongitude;

    public function rules()
    {
        return [
            "name" => "required|max:255",
            "address" => "required|max:255",
            'subBranchLatitude' => 'required',
            'subBranchLongitude' => 'required',
        ];
    }

    public function mount($branchId)
    {
        $this->branchId = $branchId;
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = SubBranchModel::search($this->search)->where("branch_id", $this->branchId);
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[On("hidden.bs.modal")]
    public function modalHidden()
    {
        $this->editingId = null;
        $this->reset(["name", "address", "subBranchLatitude", "subBranchLongitude", "allow_attendance"]);
        $this->resetValidation();
        $this->dispatch('destroySubBranchMap');
    }

    public function save()
    {
        $this->validate();
        $data = [
            'name' => $this->name,
            'address' => $this->address,
            'latitude' => $this->subBranchLatitude,
            'longitude' => $this->subBranchLongitude,
            'branch_id' => $this->branchId,
            'allow_attendance' => $this->allow_attendance
        ];

        $result = "";
        if ($this->editingId) {
            $department = SubBranchModel::findOrFail($this->editingId);
            $result = $department->fill($data)->save();
        } else {
            $result = SubBranchModel::create($data);
        }
        unset($this->list);
        $this->dispatch("hide-model");
        if ($result) {
            $this->notify("Sub branch saved successfully")->type('success')->send();
        } else {
            $this->notify("Failed to save sub branch")->type('error')->send();
        }
    }

    public function edit($id)
    {
        $this->editingId = $id;
        $row = SubBranchModel::findOrFail($id);
        if ($row) {
            $this->name = $row->name;
            $this->address = $row->address;
            $this->subBranchLatitude = $row->latitude;
            $this->subBranchLongitude = $row->longitude;
            $this->allow_attendance = $row->allow_attendance;
            $this->branchId = $row->branch_id;
            $this->dispatch('initializeSubBranch', ['latitude' => $row->latitude, 'longitude' => $row->longitude]);
        }
    }

    public function delete($id)
    {
        $subBranch = SubBranchModel::find($id);
        if (!$subBranch) {
            return $this->notify("Sub branch not found")->type("error")->send();
        }

        $subBranchEmp = EmployeeOrg::where('sub_branch_id', $subBranch)->exists();
        if ($subBranchEmp) {
            return $this->notify("can't delete sub branch that are assigned to employees")->type("error")->send();
        }
        $subBranch->delete();
        unset($this->list);
        $this->notify("Sub branch deleted successfully")->send();
    }

    public function render()
    {
        return view('livewire.config.branch.sub-branch');
    }
}
