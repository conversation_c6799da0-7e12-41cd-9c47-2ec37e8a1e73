<?php

namespace App\Livewire\Config\Branch;

use App\Http\Repositories\Configs\BranchRepository;
use App\Models\configs\Branch;
use App\Models\configs\Region as mRegion;
use App\Models\configs\RegionBranch;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

#[Title('Region')]
class Region extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null;

    public $isEditing = false;

    public $filter_company_id;

    public $name, $branch_id, $company_id;

    private mRegion $model;

    public function mount()
    {
        $this->scopeWiseFilters();
        $this->newBranchList();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();

        if ($currentEmployee) {
            $this->filter_company_id = $currentEmployee?->company_id;
            $this->company_id = $currentEmployee?->company_id;
        }
    }
    
    public function __construct()
    {
        $this->model = new mRegion;
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function rules()
    {
        $rules = [
            'name' => 'required',
            'company_id' => 'required|exists:companies,id',
            'branch_id' => 'required',
        ];

        if ($this->editingId) {
            $rules['branch_id'] = 'required|array'; // Ensure it's an array if multiple selections are allowed
            $rules['branch_id.*'] = 'exists:branches,id'; // Validate each selected branch ID
        }

        return $rules;
    }

    public function validationAttributes()
    {
        return [
            'name' => 'region name',
            'company_id' => 'company',
            'branch_id' => 'branch',
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        if (scopeAll()) {
            return \App\MOdels\configs\Company::orderBy('name')->pluck('name', 'id')->toArray();
        } else {
            return \App\MOdels\configs\Company::orderBy('name')->where('id', $this->company_id)->pluck('name', 'id')->toArray();
        }
    }

    #[Computed]
    public function branchList()
    {
        if (!$this->company_id) return [];
        $branch = Branch::where('company_id', $this->company_id)->pluck('name', 'id')->toArray();
        $preUsed = RegionBranch::distinct()->pluck("branch_id")->toArray();
        $array2Flipped = array_flip($preUsed);
        return array_diff_key($branch, $array2Flipped);
    }
    public function save()
    {
        $this->validate();
        $branchRepo = new BranchRepository;

        $type = "error";
        try {
            DB::beginTransaction();
            if ($this->editingId) {
                $region = mRegion::find($this->editingId);
                $region->fill($this->all())->save();
                $validBranchIds = Branch::where('company_id', $this->company_id)->pluck('id')->toArray();
                $params = [];
                foreach ($region->branches as $branch) {
                    if (!in_array($branch->id, $this->branch_id)) {
                        $branch->region_id = null;
                        $branch->save();
                        $branchRepo->changeRegionOfEmployee($branch);
                    }
                }
                foreach ($this->branch_id as $branch) {
                    if (in_array($branch, $validBranchIds)) {
                        $params[] = [
                            'region_id' => $region->id,
                            'branch_id' => $branch,
                        ];
                        $mBranch = Branch::find($branch);
                        $mBranch->region_id = $region->id;
                        $mBranch->save();
                        $branchRepo->changeRegionOfEmployee($mBranch);
                    }
                }
                RegionBranch::where("region_id", $region->id)->delete();
                RegionBranch::insert($params);
                DB::commit();
                $message = 'Region edited successfully';
                $type = "success";
                $this->editingId = $region->id;
            } else {
                $region = mRegion::create($this->all());
                $params = [];
                foreach ($this->branch_id as $branch) {
                    $params[] = [
                        'region_id' => $region->id,
                        'branch_id' => $branch,
                    ];
                    $mBranch = Branch::find($branch);
                    $mBranch->region_id = $region->id;
                    $mBranch->save();
                    $branchRepo->changeRegionOfEmployee($mBranch);
                }
                RegionBranch::insert($params);
                DB::commit();
                $message = 'Region saved successfully';
                $type = "success";
                $this->editingId = $region->id;
            }
            $this->reset($this->model->getFillable());
            $this->dispatch('hide-model');
            $this->notify($message)->type($type)->send();
            unset($this->list);
        } catch (\Exception $e) {
            DB::rollBack();
            if ($this->editingId) {
                $message = 'Error in editing region';
            } else {
                $message = 'Error in creating region';
            }
            logError($message, $e);
            $this->notify($message)->type($type)->send();
        }
    }

    public function delete(mRegion $region): void
    {
        // $region = mRegion::find($regionId);
        $branchRepo = new BranchRepository;
        try {
            DB::beginTransaction();
            foreach ($region->branches as $branch) {
                $branch->region_id = null;
                $branch->save();
                $branchRepo->changeRegionOfEmployee($branch);
            }
            $region->delete();

            RegionBranch::where("region_id", $region->id)->delete();
            DB::commit();
            $this->notify("Region deleted successfully")->send();
            unset($this->list);
        } catch (\Exception $e) {
            DB::rollBack();
            logError("Error deleting region", $e);
            $this->notify("Error while deleting region")->type("error")->send();
        }
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->branch_id = $row->branches->pluck('id')->toArray();
        $this->newBranchList();
        $this->dispatch("toggle-choices", $row->branch->pluck("id")->toArray());
    }

    private function newBranchList()
    {
        unset($this->branchList);
        $items = [];
        $index = 0;
        foreach ($this->branchList as $key => $value) {
            $items[$index] = [
                'value' => (string)$key,
                'label' => $value,
            ];
            $index += 1;
        }

        if ($this->editingId != null) {
            $preUsed = RegionBranch::where("region_id", $this->editingId)
                ->pluck("branch_id")
                ->toArray();

            $preUsedDetail = Branch::where('company_id', $this->company_id)->whereIn("id", $preUsed)
                ->pluck("name", "id")
                ->toArray();
            $newItems = [];
            foreach ($preUsedDetail as $key => $value) {
                $newItems[] = [
                    'value' => (string) $key,
                    'label' => $value,
                ];
            }

            $items = array_merge($items, $newItems);
        }

        if (!$this->isEditing) $this->reset(['branch_id']);

        $this->dispatch("updateMultiselectItem", ["id" => "branch-dropdown", "items" => $items]);
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = mRegion::with("branch", "company")
            ->search($this->search)
            ->select("id", "name", "company_id")
            ->when($this->filter_company_id, function ($query) {
                $query->where("regions.company_id", $this->filter_company_id);
            })
            ->orderBy('name', 'asc');

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if ($currentEmployee) {
                if (scopeCompany()) {
                    $query->where("regions.company_id", $currentEmployee->company_id);
                } elseif (scopeRegion()) {
                    $query->where('regions.id', $orgInfo->region_id); // Filter by region id
                } elseif (scopeBranch()) {
                    $query->whereHas('branches', function ($branchQuery) use ($orgInfo) {
                        $branchQuery->where('branches.id', $orgInfo->branch_id); // Filter by branch id
                    });
                } else {
                    $query->where('regions.id', $orgInfo->region_id);
                }

                // // Filter by department_id if 'scopeDepartment()' is true
                // elseif (scopeDepartment()) {
                //     $query->whereHas('branches', function ($branchQuery) use ($orgInfo) {
                //         $branchQuery->where('branches.department_id', $orgInfo->department_id); // Filter by department id
                //     });
                // }
            }
        }
        return $query->paginate($this->perPage);
    }

    public function updatedCompanyId()
    {
        // unset($this->branchList);
        $this->reset('branch_id');
        $this->newBranchList();
        if (($this->branch_id)) {
            $this->validateOnly('branch_id');
        }
    }

    public function updatedFilterCompanyId()
    {
        unset($this->list);
    }
    public function updatedBranchId()
    {
        $this->validateOnly('branch_id'); // Validate branch_id when it changes
    }

    #[on('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset(array_merge($this->model->getFillable(), ['editingId', 'isEditing']));
        $this->newBranchList();
    }

    #[Layout('layouts.app')]
    public function render()
    {
        return view('livewire.config.branch.region');
    }
}
