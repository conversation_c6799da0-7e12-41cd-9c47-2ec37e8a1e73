<?php

namespace App\Livewire\Config\Branch;

use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\Employee\Employee;
use App\Traits\WithNotify;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Branch and Sub Branch')]
class Edit extends Component
{
    use WithNotify;

    public $branchId;
    public $needSubBranch = false;

    public $name, $address, $latitude, $longitude, $phone, $branchCode, $branchManager, $need_sub_branch, $allow_attendance, $company_id, $max_distance;

    public function rules()
    {
        return [
            'name' => ['required'],
            'address' => ['required'],
            'latitude' => ['required'],
            'longitude' => ['required'],
            'phone' => ['required', 'numeric', 'digits_between:7,10'],
            'branchCode' => ['required', Rule::unique('branches', 'branch_code')->ignore($this->branchId)],
            'company_id' => ['required'],
            'max_distance' => ['required', 'numeric', 'min:1'],
        ];
    }

    public function mount($id)
    {
        $branch = Branch::findOrFail($id);
        $this->branchId = $id;
        $this->needSubBranch = $branch->need_sub_branch;
        $this->fillForm($branch);
    }

    public function update()
    {
        $this->validate();
        $branch = Branch::findOrFail($this->branchId);
        if (!$this->need_sub_branch) {
            if (count($branch->subBranch)) {
                $this->notify("You can't change need sub branch of this branch because it has sub branches associated with it")->type("error")->duration(5)->send();
                return;
            }
        }

        $branch->fill([
            "name" => $this->name,
            "address" => $this->address,
            "latitude" => $this->latitude,
            "longitude" => $this->longitude,
            "phone" => $this->phone,
            "branch_code" => $this->branchCode,
            "branch_manager" => $this->branchManager,
            "need_sub_branch" => $this->need_sub_branch,
            "allow_attendance" => $this->allow_attendance,
            "company_id" => $this->company_id,
            "max_distance" => $this->max_distance,
        ])->save();

        $this->needSubBranch = $branch->need_unit;

        $this->message = 'Branch updated successfully!!';
        $this->resetModal();

        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
        $this->redirect(route('branch', $this->branchId));
    }

    public function resetModal()
    {
        $this->resetErrorBag();
    }

    public function fillForm($branch)
    {
        $this->name = $branch->name;
        $this->address = $branch->address;
        $this->latitude = $branch->latitude;
        $this->longitude = $branch->longitude;
        $this->phone = $branch->phone;
        $this->branchCode = $branch->branch_code;
        $this->branchManager = $branch->branch_manager;
        $this->need_sub_branch = $branch->need_sub_branch;
        $this->allow_attendance = $branch->allow_attendance;
        $this->company_id = $branch->company_id;
        $this->max_distance = $branch->max_distance;
    }

    #[On("employee-dropdown-select")]
    public function setBranchManager(Employee $employee)
    {
        $this->branchManager = $employee->id;
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->select("id", "name")->get();
    }

    public function render()
    {
        return view('livewire.config.branch.edit');
    }
}
