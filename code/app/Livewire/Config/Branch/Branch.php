<?php

namespace App\Livewire\Config\Branch;

use App\Models\configs\Company as CompanyModel;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Branch Management')]
class Branch extends Component
{
    use WithDataTable, WithNotify;

    public $name = "", $address = "", $latitude = "", $longitude = "", $phone = "", $branchCode = "", $branchManager = "", $allow_attendance = 0, $need_sub_branch = 0, $company_id, $filter_company_id, $max_distance = 50;

    public $model;

    public function __construct()
    {
        $this->model = new \App\Models\configs\Branch;
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }

    public function mount()
    {
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();

        if ($currentEmployee) {
            $this->filter_company_id = $currentEmployee?->company_id;
            $this->company_id = $currentEmployee?->company_id;
        }
    }

    public function rules()
    {
        return [
            'name' => ['required'],
            'address' => ['required'],
            'latitude' => ['required'],
            'longitude' => ['required'],
            'phone' => ['required', 'numeric', 'digits_between:7,10'],
            'branchCode' => ['required', Rule::unique('branches', 'branch_code')],
            'max_distance' => ['required', 'numeric', 'min:1'],
            //'branchManager' => ['numeric'],
            'company_id' => ['required']
        ];
    }
    public function validationAttributes()
    {
        return [
            'name' => 'branch name',
            'company_id' => 'company name',
            'max_distance' => 'max distance'
        ];
    }

    public function save()
    {
        $this->validate();

        $data = $this->only(['name', 'address', 'latitude', 'longitude', 'phone', 'need_sub_branch', 'allow_attendance', 'company_id', 'max_distance']);
        $data['branch_code'] = $this->branchCode;
        $data['branch_manager'] = $this->branchManager;
        $this->model->create($data);

        $this->message = 'Branch added successfully!!';

        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id)
    {
        $branch = $this->model->findOrFail($id);

        $branchEmployees = EmployeeOrg::where('branch_id', $branch->id)->exists();
        if ($branchEmployees) {
            return $this->notify("Can't delete branch that is associated with employees")->type("error")->send();
        }

        $branch->delete();
        unset($this->list);

        $this->message = 'Branch deleted successfully!!';
        $this->notify($this->message)->send();
    }

    #[on('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'address', 'latitude', 'longitude', 'phone', 'branchCode', 'branchManager', 'need_sub_branch', 'allow_attendance', 'company_id']);
        $this->resetErrorBag();
    }

    public function updated($attrs)
    {
        if ($attrs == 'filter_company_id') {
            unset($this->list);
        }
    }

    #[On("employee-dropdown-select")]
    public function setBranchManager(Employee $employee)
    {
        $this->branchManager = $employee->id;
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = $this->model::leftJoin("companies", "companies.id", "=", "branches.company_id")
            ->leftJoin("employees", "employees.id", "=", "branches.branch_manager")
            ->whereNull('branches.deleted_at')
            ->where("branches.name", "like", '%' . $this->search . '%')
            ->when($this->filter_company_id, function ($query) {
                $query->where("branches.company_id", $this->filter_company_id);
            })
            ->select(
                "branches.id",
                "branches.name",
                "branches.address",
                "branches.latitude",
                "branches.longitude",
                "branches.phone",
                "branches.branch_code",
                "branches.need_sub_branch",
                "branches.allow_attendance",
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as branch_manager"),
                "companies.name as company_name"
            );

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;

            if (!scopeAll()) {
                if (scopeCompany()) {
                    $query->where("companies.id", $currentEmployee->company_id);
                } elseif (scopeRegion()) {
                    $query->where("branches.region_id", $orgInfo->region_id);
                } elseif (scopeBranch() || scopeDepartment()) {
                    $query->where("branches.id", $orgInfo->branch_id);
                } else {
                    $query->where("branches.id", $orgInfo->branch_id);
                }
            }
        }

        // Apply sorting and pagination
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        if (scopeAll()) {
            return CompanyModel::orderBy('name', 'asc')->select("id", "name")->get();
        } else {
            return CompanyModel::orderBy('name', 'asc')->where('id', $this->company_id)->select("id", "name")->get();
        }
    }
}
