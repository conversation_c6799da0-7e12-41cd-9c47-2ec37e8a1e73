<?php

namespace App\Livewire\Config;

use App\Models\configs\AttDevice as mAttDevice;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use TADPHP\TADFactory;
use TADPHP\TAD;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Monolog\Handler\SyslogUdp\UdpSocket;

#[Title('Attendance Devices')]
class AttDevice extends Component
{
    use WithDataTable, WithNotify;

    public $isEditing = false;

    public $editingId = null;

    #[Validate('required|string')]
    public $name;

    #[Validate('required')]
    public $company_id;

    public $branch_id;

    #[Validate('required|ip')]
    public $device_ip;

    #[Validate('required|string')]
    public $remarks;

    #[Validate('boolean')]
    public $is_active = 1;

    protected $model;
    public function rules()
    {
        $rules = [
            'name' => 'required|string',
            'company_id' => 'required',
            'device_ip' => 'required|ip',
            'remarks' => 'required|string',
        ];

        // Unique validation for device_ip based on editing state
        if ($this->isEditing) {
            $rules['device_ip'] = [
                'required',
                'ip',
                Rule::unique('att_devices', 'device_ip')->ignore($this->editingId)->whereNull('deleted_at'),
            ];
        } else {
            $rules['device_ip'] = [
                'required',
                'ip',
                'unique:att_devices,device_ip',
            ];
        }

        return $rules;
    }
    public function messages()
    {
        return [
            'name.required' => 'The device name is required.',
            'remarks.required' => 'The remark field is required.',
            'company_id.required' => 'The company field is required.',
            'device_ip.required' => 'The IP Address field is required.',
            'device_ip.ip' => 'The IP Address must be a valid IP address.',
            'device_ip.unique' => 'This IP address already exists.',
        ];
    }

    public function __construct()
    {
        $this->sortBy = "name";
        $this->sortDirection = 'asc';
        $this->search = "";
        $this->perPage = 10;
        $this->model = new \App\Models\configs\AttDevice;
    }

    public function mount()
    {
        if (session()->has('message')) {
            $this->notify(session('message'))->type(session('type'))->duration(6)->send();
            session()->forget('type');
            session()->forget('message');
        }
        $this->scopeWiseFilters();
    }
    public function scopeWiseFilters()
    {
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            if ($currentEmployee) {
                $this->company_id = $currentEmployee->company_id;
            }
        }
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }
    public function save()
    {
        // Call validate with the defined rules and messages
        $this->validate($this->rules(), $this->messages());

        if ($this->isEditing) {
            $this->model->findOrFail($this->editingId)
                ->fill($this->all())->save();
        } else {
            $this->editingId = $this->model->create($this->all())->id;
        }

        $this->reset($this->model->getFillable());
        $message = "Device saved successfully";
        $this->dispatch('hide-model');
        $this->notify($message)->send();
    }

    public function delete(int $id): void
    {
        $this->model->findOrFail($id)->delete();
        $message = 'Device deleted successfully';
        $this->notify($message)->send();
    }

    public function pingDevice(mAttDevice $attDevice)
    {
        if ($this->ensureIsNotRateLimited($attDevice->device_ip)) {
            RateLimiter::hit($this->throttleKey($attDevice->device_ip), 120);
            $result = Process::run("ping -c 1 {$attDevice->device_ip}");
            if ($result->successful()) {
                $last_ping_status = 1;
                $message = "Device is online.";
            } else {
                $last_ping_status = 2;
                $message = "Device is Offline. Please check if device is off or contact your IT team!";
                if ($result->errorOutput()) {
                    Log::error("Unable to ping the device. Error: " . $result->errorOutput());
                    $message = "Unable to ping the device.";
                }
            }
            $last_ping_date = Date('Y-m-d H:i:s');
            $attDevice->last_ping_status = $last_ping_status;
            $attDevice->last_ping_date = $last_ping_date;
            $attDevice->save();
            $message .= ' You attempted for ' . RateLimiter::attempts($this->throttleKey($attDevice->device_ip)) . ' times.';
        } else {
            $message = 'You have attempted to ping more than rate limit. Please try again after 2 mins';
        }
        $this->notify($message)->type('info')->send();
    }

    public function details($id)
    {
        $this->redirectRoute('biometric', ['attdeviceid' => $id]);
    }

    #[Computed()]
    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed()]
    public function list()
    {
        $query = $this->model
            ->search($this->search)
            ->whereNull('deleted_at');

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if (scopeCompany()) {
                $query->where("company_id", $currentEmployee->company_id);
            } else {
                $query->where("company_id", $currentEmployee->company_id)
                    ->where('branch_id', $orgInfo->branch_id);
            }
        }
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset(array_merge($this->model->getFillable(), ['editingId', 'isEditing']));
    }

    public function updatedCompanyId()
    {
        unset($this->branches);
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if ((scopeBranch() || !scopeAll() && !scopeCompany()) && !isSuperAdmin()) {
            return \App\Models\configs\Branch::select("id", "name")
                ->where([
                    ['company_id', currentEmployee()?->company_id],
                    ['id', currentEmployee()?->organizationInfo?->branch_id]
                ])
                ->orderBy('name', 'asc')
                ->pluck('name', 'id')
                ->toArray();
        }
        if (!$this->company_id)
            return [];
        return \App\Models\configs\Branch::where('company_id', $this->company_id)
            ->whereNull('deleted_at')
            ->orderBy('name', 'asc')
            ->pluck('name', 'id')
            ->toArray();
    }

    #[Computed(persist: true)]
    public function companyList()
    {
        if (scopeAll()) {
            return \App\Models\configs\Company::select("id", "name")
                ->orderBy('name', 'asc')
                ->get()
                ->pluck('name', 'id')
                ->toArray();
        } else {
            if ($this->company_id) {
                return \App\Models\configs\Company::select("id", "name")
                    ->where('id', $this->company_id)
                    ->orderBy('name', 'asc')
                    ->get()
                    ->pluck('name', 'id')
                    ->toArray();
            }
        }
    }

    #[Layout('layouts.app')]
    public function render()
    {
        if ($this->search)
            $this->resetPage();

        return view('livewire.config.att-device');
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited($ip)
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey($ip), 5)) {
            return true;
        }
        $seconds = RateLimiter::availableIn($this->throttleKey($ip));
        $this->message = "Rate Limit exceeded. You can ping device in " . ceil($seconds / 60) . ' Min. ' . ceil($seconds % 60) . ' Seconds';
        return false;
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey($ip): string
    {
        return Str::transliterate(Str::lower($ip) . '|' . Auth()->user()->email);
    }
}
