<?php

namespace App\Livewire\Config\Job;

use App\Models\configs\Job;
use Illuminate\Support\Facades\Log;
use Livewire\Form;

class JobForm extends Form
{
    protected $model;

    public $name, $description, $responsibilities, $qualifications, $skills, $remarks;
    public function rules() {
        return [
            "name" => "required|max:100",
            "description" => "required",
            "responsibilities" => "required",
            "qualifications" => "required|max:150",
            "skills" => "required",
            "remarks" => "required|max:150"
        ];
    }
    public function validationAttributes(){
        return [
            "name"=> "job name",
        ];
    }

    public function set(Job $job)
    {
        foreach ((new Job)->getFillable() as $fillable) {
            $this->{$fillable} = $job->{$fillable};
        }
    }

    public function update(Job $job)
    {
        Log::info("Params for updating leave type {$job->id}: ", $this->all());
        $validated = $this->validate();
        $job->fill($validated)->save();
        Log::info("Job Updated.");
        $this->resetForm();
    }
    
    public function create()
    {
        Log::info("Params for creating job: ", $this->all());
        $validated = $this->validate();
        Job::create($validated);
        Log::info("Job Created.");
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->reset();
        $this->resetValidation();
    }
}
