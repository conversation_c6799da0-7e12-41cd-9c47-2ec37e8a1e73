<?php

namespace App\Livewire\Config\Job;

use App\Models\configs\Department;
use App\Models\configs\Job as JobModel;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Rule;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Job Management')]
class Job extends Component
{
    use WithRowSelect, WithNotify;

    public JobForm $form;

    public $selectedDepartments;

    public $editingId = null;
    public string $title;

    #[Rule('required|integer|min:0')]
    public $seat;

    public function mount()
    {
        $this->pageName = 'jobs-list';
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(JobModel::search($this->search))->paginate($this->perPage, pageName: $this->pageName);
    }

    #[On("department-select")]
    public function departmentSelect($departments)
    {
        $this->selectedDepartments = $departments;
    }

    #[on('hide.bs.modal')]
    public function resetModal()
    {
        $this->form->resetForm();
        $this->reset(['editingId', 'title']);
        $this->dispatch("load-description", "");
        $this->dispatch("load-responsibility", "");
    }

    public function view(int $id): void
    {
        $row = JobModel::findOrFail($id);
        $this->form->set($row);
    }

    public function edit(int $id): void
    {
        $this->editingId = $id;
        $this->view($id);
        $this->dispatch("load-description", $this->form->description);
        $this->dispatch("load-responsibility", $this->form->responsibilities);
    }

    public function save(): void
    {
        if ($this->editingId) {
            authorizePermission(PermissionList::JOB_UPDATE);
            $row = JobModel::findOrFail($this->editingId);
            $this->form->update($row);
            $this->dispatch("employee-list-refresh");
        } else {
            authorizePermission(PermissionList::JOB_CREATE);
            $this->form->create();
        }
        $this->reset(['editingId', 'title']);
        $this->dispatch('hide-model');
        unset($this->list);
        $this->notify('Job saved successfully')->send();
    }

    public function delete(int $id): void
    {
        authorizePermission(PermissionList::JOB_DELETE);
        try {
            $row = JobModel::findOrFail($id);
            $row->delete();
            Log::info("Job of id $id deleted successfully");
            $this->dispatch("employee-list-refresh");
            $this->notify('Job deleted successfully')->send();
        } catch (\Exception $e) {
            Log::error("Error while deleting the row: " . $e->getMessage());
            $this->notify('Error while deleting job')->type('error')->send();
        }
        unset($this->list);
    }

    public function assignJobs()
    {
        $this->validateOnly('seat');
        if (!count($this->selectedRows ?? [])) {
            $this->notify('Please select jobs to assign')->type("error")->send();
            return;
        }
        if (!count($this->selectedDepartments ?? [])) {
            $this->notify('Please select departments to assign')->type("error")->send();
            return;
        }
        DB::beginTransaction();
        try {
            foreach ($this->selectedDepartments as $departmentId) {
                $department = Department::find($departmentId);
                if ($department instanceof Department) {
                    $department->jobs()->syncWithPivotValues($this->selectedRows, ['seat' => $this->seat], false);
                } else {
                    throw new \Exception("Department of id $departmentId not found");
                }
            }
            DB::commit();
            $this->dispatch("department-list-refresh");
            $this->notify('Jobs assigned successfully')->send();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error while assigning jobs to departments: " . $e->getMessage());
            $this->notify('Error while assigning jobs')->type("error")->send();
        }
    }

    public function render()
    {
        return view('livewire.config.job.job');
    }
}
