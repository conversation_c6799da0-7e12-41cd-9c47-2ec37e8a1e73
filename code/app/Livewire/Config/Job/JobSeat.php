<?php

namespace App\Livewire\Config\Job;

use App\Exports\JobSeatExport;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\Job;
use App\Models\configs\JobDepartment;
use App\Models\configs\Region;
use App\Traits\WithDataTable;
use App\Traits\WithDefaultFilter;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;
use PermissionList;

#[Title('Job Seats')]
class JobSeat extends Component
{
    use WithDataTable, WithDefaultFilter;

    public $editingId;
    public $isEditing = null;

    #[Validate('required|exists:companies,id')]
    public $companyId;

    public $regionId;

    #[Validate('required|exists:branches,id')]
    public $branchId;

    #[Validate('required|exists:departments,id')]
    public $departmentId;

    #[Validate('required|exists:jobs,id')]
    public $jobId;

    public $seat = 0, $occupancy = 0, $onNoticePeriod = 0;

    // filters
    public $filterCompanyId, $filterRegionId, $filterBranchId, $filterDepartmentId, $filterJobId;

    public function mount()
    {
        $this->seat = 0;
        $this->occupancy = 0;
        $this->onNoticePeriod = 0;
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (!scopeAll()) {
            $this->companyId = currentEmployee()?->company_id;
            $this->filterCompanyId = currentEmployee()?->company_id;
            if (scopeCompany())
                return;

            $this->regionId = currentEmployee()?->organizationInfo?->region_id;
            $this->filterRegionId = currentEmployee()?->organizationInfo?->region_id;
            if (scopeRegion())
                return;

            $this->branchId = currentEmployee()?->organizationInfo?->branch_id;
            $this->filterBranchId = currentEmployee()?->organizationInfo?->branch_id;
            if (scopeBranch())
                return;

            $this->departmentId = currentEmployee()?->organizationInfo?->department_id;
            $this->filterDepartmentId = currentEmployee()?->organizationInfo?->department_id;
            if (scopeDepartment())
                return;
        }
    }


    public function rules()
    {
        $rules = [];

        if (!fedexHrm() && count($this->regionLists)) {
            $rules['regionId'] = ['required', 'exists:regions,id'];
        }

        if (auth()->user()->can(PermissionList::JOBS_SEAT_UPDATE)) {
            $rules['seat'] = ['required', 'min:0'];

            if (auth()->user()->can(PermissionList::JOBS_OCCUPANCY_UPDATE)) {
                $rules['seat'][] = 'gte:occupancy';
            }
        }

        if (auth()->user()->can(PermissionList::JOBS_OCCUPANCY_UPDATE)) {
            $rules['occupancy'] = ['required', 'min:0','lte:seat'];
        }

        if (auth()->user()->can(PermissionList::JOBS_NOTICE_UPDATE)) {
            $rules['onNoticePeriod'] = ['required', 'min:0'];
        }

        return $rules;
    }

    public function listQuery()
    {
        $query = JobDepartment::query()
            ->leftJoin('companies as company', 'company.id', 'job_departments.company_id')
            ->leftJoin('regions as region', 'region.id', 'job_departments.region_id')
            ->leftJoin('branches as branch', 'branch.id', 'job_departments.branch_id')
            ->leftJoin('departments as department', 'department.id', 'job_departments.department_id')
            ->leftJoin('jobs as job', 'job.id', 'job_departments.job_id')
            ->when($this->filterCompanyId, fn($query) => $query->where('job_departments.company_id', $this->filterCompanyId))
            ->when($this->filterRegionId, fn($query) => $query->where('job_departments.region_id', $this->filterRegionId))
            ->when($this->filterBranchId, fn($query) => $query->where('job_departments.branch_id', $this->filterBranchId))
            ->when($this->filterDepartmentId, fn($query) => $query->where('job_departments.department_id', $this->filterDepartmentId))
            ->when($this->filterJobId && $this->filterJobId != "-1", fn($query) => $query->where('job_departments.job_id', $this->filterJobId))
            ->whereNull('region.deleted_at')
            ->whereNull('branch.deleted_at')
            ->whereNull('department.deleted_at')
            ->whereNull('job.deleted_at')
            ->select(
                'job_departments.id',
                'company.name as company',
                'region.name as region',
                'branch.name as branch',
                'department.name as department',
                'job.name as job',
                'job_departments.seat',
                'job_departments.occupancy',
                'job_departments.on_notice_period'
            )->orderBy('job_departments.created_at', 'desc');

        return $this->applySorting($query);
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->listQuery()->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function seatCounts()
    {
        return JobDepartment::query()
            ->leftJoin('companies as company', 'company.id', 'job_departments.company_id')
            ->leftJoin('regions as region', 'region.id', 'job_departments.region_id')
            ->leftJoin('branches as branch', 'branch.id', 'job_departments.branch_id')
            ->leftJoin('departments as department', 'department.id', 'job_departments.department_id')
            ->leftJoin('jobs as job', 'job.id', 'job_departments.job_id')
            ->whereNull('region.deleted_at')
            ->whereNull('branch.deleted_at')
            ->whereNull('department.deleted_at')
            ->whereNull('job.deleted_at')
            ->when($this->filterCompanyId, function ($query) {
                return $query->where('job_departments.company_id', $this->filterCompanyId);
            })
            ->when($this->filterRegionId, function ($query) {
                return $query->where('job_departments.region_id', $this->filterRegionId);
            })
            ->when($this->filterBranchId, function ($query) {
                return $query->where('job_departments.branch_id', $this->filterBranchId);
            })
            ->when($this->filterDepartmentId, function ($query) {
                return $query->where('job_departments.department_id', $this->filterDepartmentId);
            })
            ->when($this->filterJobId, function ($query) {
                return $query->where('job_departments.job_id', $this->filterJobId);
            })
            ->selectRaw('SUM(seat) as total_seat, SUM(occupancy) as total_occupancy, SUM(on_notice_period) as total_notice_period')
            ->get();
    }

    public function updated($attr)
    {
        $this->updatedDefaultFilter($attr);

        if (in_array($attr, ['filterCompanyId', 'filterRegionId', 'filterBranchId', 'filterDepartmentId', 'filterJobId'])) {
            unset($this->seatCounts);
            $this->resetPage();
        }

        if ($attr == 'regionId') {

            unset($this->branchLists);
        }

        if ($attr == 'companyId') {
            unset($this->regionLists);
            unset($this->departmentLists);
            if (!count($this->regionLists)) {
                unset($this->branchLists);
            }
        }
    }

    public function edit($id)
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $jobDepartment = JobDepartment::find($id);
        if (!$jobDepartment) {
            return $this->notify("Job Seat Not Found")->type("error")->send();
        }
        $this->companyId = $jobDepartment->company_id;
        $this->regionId = $jobDepartment->region_id;
        $this->branchId = $jobDepartment->branch_id;
        $this->departmentId = $jobDepartment->department_id;
        $this->jobId = $jobDepartment->job_id;
        $this->seat = $jobDepartment->seat;
        $this->occupancy = $jobDepartment->occupancy ?? 0;
        $this->onNoticePeriod = $jobDepartment->on_notice_period ?? 0;
        unset($this->regionLists);
        unset($this->branchLists);
    }

    public function exportJobs()
    {
        $title = "Jobs Seat List";

        $heading = [
            'Company',
            'Region',
            'Branch',
            'Department',
            'Job',
            'Seat',
            'Occupancy',
            'On Notice Period'
        ];

        ini_set('memory_limit', '512M');
        set_time_limit(300);

        $dataToReturn = [];

        $jobsData = $this->listQuery()->get();
        foreach ($jobsData as $job) {
            $dataToReturn[] = [
                $job->company,
                $job->region,
                $job->branch,
                $job->department,
                $job->job,
                $job->seat,
                $job->occupancy,
                $job->on_notice_period,
            ];
        }

        return Excel::download(
            new JobSeatExport($title, $heading, $dataToReturn),
            "$title.xlsx"
        );
    }

    public function save()
    {
        $this->validate();
        logInfo("Parameters for saving job seat", $this->all());
        if ($this->editingId) {
            $jobSeat = JobDepartment::find($this->editingId);
        
            $jobSeat->update([
                'seat' => $this->seat,
                'occupancy' => $this->occupancy,
                'company_id' => $this->companyId,
                'region_id' => $this->regionId,
                'branch_id' => $this->branchId,
                'department_id' => $this->departmentId
            ]);
            $this->resetPage();
            $this->dispatch('hide-model');
            logInfo("Updating job seat");
        } else {
            $jobSeatExists = JobDepartment::where([
                ['company_id', $this->companyId],
                ['region_id', $this->regionId],
                ['branch_id', $this->branchId],
                ['department_id', $this->departmentId],
                ['job_id', $this->jobId],
            ])->first();
            if ($jobSeatExists) {
                return $this->notify("Job Seat already exists for this region, branch, department, and job. Please update the existing record.")
                    ->type('error')->duration(10)->send();
            }
            $jobsData = [
                'company_id' => $this->companyId,
                'region_id' => $this->regionId,
                'branch_id' => $this->branchId,
                'department_id' => $this->departmentId,
                'job_id' => $this->jobId,
                'seat' => $this->seat ?? 0,
                'occupancy' => $this->occupancy ?? 0,
                'on_notice_period' => $this->onNoticePeriod ?? 0,
            ];
            JobDepartment::create($jobsData);
            $this->dispatch('hide-model');
            $this->resetPage();
            logInfo("Job Seat created");
        }
        return $this->notify("Job Seat saved successfully")->send();
    }

    #[On('hide.bs.modal')]
    public function resetForm()
    {
        $this->reset(['companyId', 'editingId', 'regionId', 'branchId', 'departmentId', 'seat', 'occupancy', 'onNoticePeriod', 'jobId', 'isEditing']);
        $this->resetErrorBag();
    }

    #[Computed(persist: true)]
    public function departmentLists()
    {
        if (!scopeAll()) {
            if (scopeCompany() || scopeBranch() || scopeRegion()) {
                return Department::where('company_id', $this->companyId)->pluck('name', 'id')->toArray();
            }
            return Department::where("id", currentEmployee()?->organizationInfo?->department_id)
                ->pluck('name', 'id')
                ->toArray();
        }
        return Department::where('company_id', $this->companyId)->pluck('name', 'id')->toArray();
    }
    #[Computed(persist: true)]

    public function branchLists()
    {
        $query = Branch::where('region_id', $this->regionId);
        if ($this->regionId == null || !count($this->regionLists)) {
            $query = Branch::where('company_id', $this->companyId);
        }

        if (!scopeAll()) {
            if (scopeCompany() || scopeRegion()) {
                return $query->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
            }
            return $query->where('id', currentEmployee()?->organizationInfo?->branch_id)->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
        }
        return $query->orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    #[Computed(persist: true)]
    public function regionLists()
    {
        if (!scopeAll()) {
            if (scopeCompany()) {
                return Region::where("company_id", $this->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
            }
            return Region::where("company_id", $this->companyId)->where('id', currentEmployee()?->organizationInfo?->region_id)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
        }
        return Region::where("company_id", $this->companyId)->orderBy('name', 'asc')->pluck("name", "id")->toArray();
    }

    #[Computed(persist: true)]
    public function companyLists()
    {
        if (scopeAll()) {
            return Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
        } else {
            return Company::orderBy('name', 'asc')->where('id', currentEmployee()?->company_id)->pluck("name", "id")->toArray();
        }
    }

    #[Computed(persist: true)]
    public function jobLists()
    {
        return Job::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }
    public function render()
    {
        return view('livewire.config.job.job-seat');
    }
}
