<?php

namespace App\Livewire\Config;

use App\Http\Helpers\Constant;
use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Component;

class MailConfiguration extends Component
{
    use WithNotify;

    public string $mail_type = '', $to_address = '', $cc_address = '';

    #[Computed(persist: true)]
    public function mailTypes()
    {
        return Constant::MAIL_TYPES;
    }

    public function updatedMailType($value)
    {
        $to = Setting::where('namespace', $value)->where('key', 'to_address')->value('value');
        $cc = Setting::where('namespace', $value)->where('key', 'cc_address')->value('value');

        $this->to_address = $to ? implode(', ', json_decode($to, true)) : '';
        $this->cc_address = $cc ? implode(', ', json_decode($cc, true)) : '';
    }

    public function saveMailAddress()
    {
        $this->validate([
            'mail_type' => 'required|string',
            'to_address' => [
                'required',
                'string',
                'regex:/^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(,\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$/'
            ],
            'cc_address' => [
                'nullable',
                'string',
                'regex:/^([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(,\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$/'
            ]
        ]);

        $toEmails = collect(explode(',', $this->to_address))
            ->map(fn($email) => trim($email))
            ->filter()
            ->unique()
            ->values();

        $ccEmails = collect(explode(',', $this->cc_address ?? ''))
            ->map(fn($email) => trim($email))
            ->filter()
            ->unique()
            ->values();

        // Validate emails manually
        foreach ($toEmails as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->addError('to_address', "Invalid email in To: $email");
                return;
            }
        }

        foreach ($ccEmails as $email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->addError('cc_address', "Invalid email in CC: $email");
                return;
            }
        }

        try {
            Setting::updateOrCreate(
                ['namespace' => $this->mail_type, 'key' => 'to_address'],
                ['value' => json_encode($toEmails), 'types' => 'json']
            );

            Setting::updateOrCreate(
                ['namespace' => $this->mail_type, 'key' => 'cc_address'],
                ['value' => json_encode($ccEmails), 'types' => 'json']
            );

            Log::info("Mail addresses saved for {$this->mail_type}.");
            $this->notify("Mail addresses updated successfully")->send();
        } catch (\Throwable $e) {
            Log::error("Error saving mail address", [
                'mail_type' => $this->mail_type,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
    public function render()
    {
        return view('livewire.config.mail-configuration');
    }
}
