<?php

namespace App\Livewire\Config;

use App\Http\Helpers\Constant;
use App\Models\configs\FiscalYear as mFiscalYear;
use App\Traits\SetOnlyOneActiveRecord;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Fiscal Year')]
class FiscalYear extends Component
{
    use WithDataTable, SetOnlyOneActiveRecord, WithNotify;

    public $editingId = null;

    public $isEditing = false;

    public $status = null;

    #[Validate('required|string')]
    public $name;

    #[Validate('required')]
    public $start_date;

    #[Validate('required')]
    public $end_date;

    public $is_active = 0;

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\configs\FiscalYear;
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function validationAttributes()
    {
        return [
            'name' => 'fiscal year'
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }


    public function save(): void
    {
        $this->validate();

        if ($this->editingId) {
            $fiscalYear = mFiscalYear::find($this->editingId);
            $activeFiscalYear = mFiscalYear::where([['id', '!=', $fiscalYear->id], ['is_active', true]])->first();
            if ($fiscalYear->is_active && !$this->is_active && !$activeFiscalYear) {
                $this->addError('is_active', 'Atleast one fiscal year should be active.');
                return;
            }
            $fiscalYear->fill($this->all())->save();
        } else {
            $fiscalYear = mFiscalYear::create($this->all());
            if ($fiscalYear->status) session()->put([Constant::SESSION_CURRENT_FISCAL_YEAR => $fiscalYear->id]);
            $this->editingId = $fiscalYear->id;
        }

        $this->reset($this->model->getFillable());
        $this->dispatch('hide-model');
        $this->refreshSessionFiscalYear();
        $message = 'Fiscal year saved successfully';
        $this->notify($message)->type('success')->send();
    }

    public function delete(mFiscalYear $fiscalYear): void
    {
        if ($fiscalYear->is_active) {
            $message = "You cannot delete Active Fiscal Year";
            $type = "info";
        } else {
            $fiscalYear->delete();
            $type = "success";
            $message = 'Fiscal year deleted successfully';
            $this->refreshSessionFiscalYear();
        }

        $this->notify($message)->type($type)->send();
    }

    #[Computed()]
    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed()]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)->whereNull('deleted_at')
        )->paginate($this->perPage);
    }

    #[on('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset(array_merge($this->model->getFillable(), ['editingId', 'isEditing']));
    }

    public function refreshSessionFiscalYear()
    {
        session([
            Constant::SESSION_CURRENT_FISCAL_YEAR => \App\Models\configs\FiscalYear::current()->pluck('id')->first(),
        ]);
    }

    #[Layout('layouts.app')]
    public function render()
    {
        return view('livewire.config.fiscal-year');
    }
}
