<?php

namespace App\Livewire\Config;

use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Education Level Management')]
class EducationLevel extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null, $isEditing = false, $message = null;

    #[Validate('required|string|max:50')]
    public $name = '';

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\configs\EducationLevel;
    }

    public function mount()
    {
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function validationAttributes()
    {
        return [
            'name' => 'level name'
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save(): void
    {
        $this->validate();

        if ($this->isEditing) {
            $this->model->findOrFail($this->editingId)->fill($this->all())->save();

            $this->message = 'Education level edited successfully!!';
        } else {
            $this->editingId = $this->model->create($this->all())->id;

            $this->message = 'Education level added successfully!!';
        }
        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id): void
    {
        $this->model->findOrFail($id)->delete();
        unset($this->list);

        $this->message = 'Education level deleted successfully!!';
        $this->notify($this->message)->send();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'isEditing']);
        $this->resetErrorBag();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)
        )->paginate($this->perPage);
    }
}
