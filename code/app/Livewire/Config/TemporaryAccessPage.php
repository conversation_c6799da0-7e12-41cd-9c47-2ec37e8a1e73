<?php

namespace App\Livewire\Config;

use App\Http\Repositories\Configs\Interfaces\TemporaryAccessRepositoryInterface;
use App\Http\Services\Configs\TemporaryAccessService;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class TemporaryAccessPage extends Component
{
    use WithDataTable, MultiselectEmployeeSearch;

    public $editingId;

    public $tempAccess;

    public $filter_from_employee_ids = [];
    public $filter_temporary_employee_ids = [];

    public $from_employee_id;
    public $temporary_employee_id;
    public $from;
    public $to;

    private TemporaryAccessRepositoryInterface $repository;
    private TemporaryAccessService $service;

    public function boot(
        TemporaryAccessRepositoryInterface $repository,
        TemporaryAccessService $service
    ) {
        $this->repository = $repository;
        $this->service = $service;
    }

    public function mount()
    {
        $this->singleSelectAttributes = ['from_employee_id', 'temporary_employee_id'];
        $this->multiSelectAttributes = ['filter_from_employee_ids', 'filter_temporary_employee_ids'];
    }

    public function rules()
    {
        return [
            'from_employee_id' => 'required|different:temporary_employee_id|exists:employees,id',
            'temporary_employee_id' => 'required|exists:employees,id',
            'from' => 'required|date:y-m-d',
            'to' => 'required|date:y-m-d|after_or_equal:from',
        ];
    }

    public function validationAttributes()
    {
        return [
            'from_employee_id' => 'From Employee',
            'temporary_employee_id' => 'Temporary Employee',
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->repository->paginate(
            $this->perPage,
            $this->getPage($this->pageName),
            [
                'from_employee_ids' => $this->filter_from_employee_ids,
                'temporary_employee_ids' => $this->filter_temporary_employee_ids
            ]
        );
    }

    public function updated($attr)
    {
        if (in_array($attr, ['filter_from_employee_ids', 'filter_temporary_employee_ids'])) {
            unset($this->list);
        }
    }

    public function edit($id)
    {
        $this->editingId = $id;
        $this->tempAccess = $this->repository->find($id);
        $this->from_employee_id = $this->tempAccess->from_employee_id;
        $this->temporary_employee_id = $this->tempAccess->temporary_employee_id;
        $this->from = $this->tempAccess->from->format('Y-m-d');
        $this->to = $this->tempAccess->to->format('Y-m-d');
        $this->dispatch('toggle-from-employee-id', [$this->from_employee_id]);
        $this->dispatch('toggle-temporary-employee-id', [$this->temporary_employee_id]);
    }

    public function save()
    {
        $validated = $this->validate();

        if ($this->editingId) {
            $tempAccess = $this->repository->find($this->editingId);
            if (!$tempAccess) return $this->notify("Temporary access not found")->type("error")->send();
            $this->repository->update($tempAccess, $validated);
        } else {
            $this->repository->create($validated);
        }
        $this->notify("Temporary access saved successfully")->send();
        $this->clearForm();
        $this->dispatch('hide-model');
        unset($this->list);
    }

    public function delete($id)
    {
        $tempAccess = $this->repository->find($id);
        if (!$tempAccess) return $this->notify("Temporary access not found")->type("error")->send();

        if ($tempAccess->isActive()) {
            return $this->notify("Cannot delete active temporary access")->type("error")->send();
        }

        $this->repository->delete($tempAccess);
        $this->notify("Temporary access deleted successfully")->send();
        $this->clearForm();
        unset($this->list);
    }

    public function activateNow($id)
    {
        $tempAccess = $this->repository->find($id);
        if (!$tempAccess) return $this->notify("Temporary access not found")->type("error")->send();
        $this->service->activate($tempAccess);
        $this->notify("Temporary access activated successfully")->send();
        $this->clearForm();
        unset($this->list);
    }

    public function expireNow($id)
    {
        $tempAccess = $this->repository->find($id);
        if (!$tempAccess) return $this->notify("Temporary access not found")->type("error")->send();
        $this->service->expire($tempAccess);
        $this->notify("Temporary access expired successfully")->send();
        $this->clearForm();
        unset($this->list);
    }

    #[On('hidden.bs.modal')]
    public function clearForm()
    {
        $this->reset([
            'from_employee_id',
            'temporary_employee_id',
            'from',
            'to',
            'editingId',
        ]);
        $this->dispatch("toggle-from-employee-id", ['']);
        $this->dispatch("toggle-temporary-employee-id", ['']);
    }

    public function render()
    {
        return view('livewire.temporary-access-page');
    }
}
