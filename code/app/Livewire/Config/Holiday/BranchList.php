<?php

namespace App\Livewire\Config\Holiday;

use App\Http\Helpers\Constant;
use App\Models\configs\FiscalYear;
use App\Models\configs\HolidayBranch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class BranchList extends Component
{
    use WithNotify, WithRowSelect;

    public $branchFiscalYearId = "";

    public function __construct()
    {
        $this->rowSelectEvent = "holiday-branch-select";
    }

    public function mount()
    {
        $this->branchFiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
        $this->pageName = 'branch-list';
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = \App\Models\configs\Branch::with([
            'holidays' => function ($query) {
                $query->where('fiscal_year', $this->branchFiscalYearId);
            }
        ])
            ->search($this->search)
            ->whereNull('deleted_at');
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if (scopeCompany()) {
                $query->where("company_id", $currentEmployee?->company_id);
            } else if (scopeRegion()) {
                $query->where("company_id", $currentEmployee?->company_id);
                if (!is_null($orgInfo?->region_id)){
                    $query->where("region_id", $orgInfo?->region_id);
                }
            } else {
                $query->where("company_id", $currentEmployee?->company_id)
                    ->where("id", $orgInfo?->branch_id);
            }
        }

        return $this->applySorting($query)->paginate($this->perPage, pageName: $this->pageName);
    }

    public function updatedBranchFiscalYearId()
    {
        unset($this->list);
    }

    #[On("branch-list-refresh")]
    public function refreshList()
    {
        unset($this->list);
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::select('id', 'name', 'is_active')
            ->whereNull('deleted_at')
            ->orderBy('id', 'desc')
            ->get();
    }

    public function removeHoliday($holidayId, $branchId)
    {
        $branchHoliday = HolidayBranch::where("holiday_id", $holidayId)
            ->where("branch_id", $branchId)
            ->first();
        $branchHoliday->delete();
        unset($this->list);
        $this->notify('Holiday removed successfully')->send();
    }
}
