<?php

namespace App\Livewire\Config\Holiday;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\Constant;
use App\Models\configs\Company as CompanyModel;
use App\Models\configs\FiscalYear;
use App\Models\configs\Holiday as ConfigsHoliday;
use App\Models\configs\HolidayBranch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;

use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;
use PSpell\Config;

#[Title('Holiday Management')]
class Holiday extends Component
{
    use WithNotify, WithRowSelect;

    public $editingId = null, $isEditing = false;

    #[Validate('required|max:50', as: 'holiday name')]
    public string $name = '';

    #[Validate('required|date', as: 'english date')]
    public string $eng_date = '';

    #[Validate('required', as: 'nepali date')]
    public string $nep_date = '';

    #[Validate('required')]
    public string $fiscal_year = '';

    #[Validate('numeric')]
    public float $rate = 0.0;

    public string $remarks = '';

    #[Validate('required', as: 'company')]
    public $company_id;

    protected $model;

    #[Validate('in:All,Male,Female')]
    public string $gender = 'All';

    public $holidayFiscalYearId = "";
    
    public $filter_company_id ="";

    public $selectedBranches = [];

    public function __construct()
    {
        $this->model = new \App\Models\configs\Holiday;
    }

    public function mount()
    {
        $this->sortBy = 'name';
        $this->holidayFiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
        $this->pageName = "holiday-list";
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();

        if ($currentEmployee) {
            $this->company_id = $currentEmployee?->company_id;
        }
    }

    public function updatedEngDate($value)
    {
        $this->nep_date = $this->convertToNepDate($value);
    }

    public function updatedHolidayFiscalYearId()
    {
        $this->reset('selectedRows');
        $this->resetPage($this->pageName); 
    }
    public function updatedFilterCompany()
    {
        $this->resetPage();
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save()
    {
        $this->validate();

        $fiscalYear =  \App\Models\configs\FiscalYear::where('id', $this->fiscal_year)->first();
        if (!($fiscalYear->start_date <= $this->nep_date && $fiscalYear->end_date >= $this->nep_date)) {
            $this->addError('eng_date', 'Date is not within selected fiscal year.');
            return;
        }

        if ($this->isEditing) {
            $holiday = $this->model->findOrFail($this->editingId)->fill($this->all());
            $holiday["nep_date"] = $this->convertToNepDate($this->eng_date);
            $holiday->save();

            $this->message = 'Holiday edited successfully!!';
        } else {
            $this->nep_date = $this->convertToNepDate($this->eng_date);
            $data = $this->all();
            $this->model->create($data);

            $this->message = 'Holiday added successfully!!';
        }
        unset($this->list);
        unset($this->holidayList);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id)
    {
        $this->model->findOrFail($id)->delete();
        unset($this->list);
        $this->dispatch("branch-list-refresh");

        $this->message = 'Holiday deleted successfully!!';
        $this->notify($this->message)->send();
    }

    private function convertToNepDate($engDate)
    {
        return LaravelNepaliDate::from($engDate)->toNepaliDate(); // Convert English date to Nepali date
    }

    #[on('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'eng_date', 'nep_date', 'fiscal_year', 'rate', 'remarks', 'gender', 'isEditing', 'company_id']);
        $this->resetErrorBag();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed()]
    public function list()
    {
        $query = $this->model::leftJoin("companies", "companies.id", "=", "holidays.company_id")
            ->whereNull('holidays.deleted_at')
            ->where("holidays.fiscal_year", $this->holidayFiscalYearId)
            ->where("holidays.name", "like", '%' . $this->search . '%')
            ->select(
                "holidays.id",
                "holidays.name",
                "holidays.eng_date",
                "holidays.nep_date",
                "holidays.fiscal_year",
                "holidays.rate",
                "holidays.remarks",
                "holidays.gender",
                "companies.name as company_name",
            );

        if($this->filter_company_id){
            $query->where("holidays.company_id", $this->filter_company_id);
        }

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;

            if (scopeCompany()) {
                $query->where("holidays.company_id", $currentEmployee?->company_id);
            }  else {
                $query->join("holiday_branches", "holiday_branches.holiday_id", "=", "holidays.id")
                    ->where("holidays.company_id", $currentEmployee?->company_id)
                    ->where("holiday_branches.branch_id", "=", $orgInfo?->branch_id);
            }
        }
        return $this->applySorting($query)->paginate($this->perPage, pageName: $this->pageName);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        if (scopeAll()) {
            return CompanyModel::orderBy('name', 'asc')->select("id", "name")->get();
        } else {
            return CompanyModel::orderBy('name', 'asc')->where('id', $this->company_id)->select("id", "name")->get();
        }
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return FiscalYear::select('id', 'name', 'is_active')
            ->whereNull('deleted_at')
            ->orderBy('id', 'desc')
            ->get();
    }

    #[Computed(persist: true)]
    public function holidayList()
    {
        return  ConfigsHoliday::get()->keyBy('id');
    }

    #[On("holiday-branch-select")]
    public function branchSelect($branches)
    {
        $this->selectedBranches = $branches;
    }

    public function assignHolidays()
    {
        if (!count($this->selectedRows)) {
            $this->notify('Please select holidays to assign')->type("error")->send();
            return;
        }
        if (!count($this->selectedBranches)) {
            $this->notify('Please select  branches to assign')->type("error")->send();
            return;
        }

        DB::beginTransaction();
        try {
            $activeFiscalYear = \App\Models\configs\FiscalYear::where('is_active', true)->first();
            foreach ($this->selectedRows as $holidayId) {
                if ($activeFiscalYear->start_date > $this->holidayList[$holidayId]->nep_date) {
                    \logError('Holiday assign request is of past fiscal year.');
                    $this->notify("Request holiday ({$this->holidayList[$holidayId]->name}) to be assigned is of past fiscal year")->type("error")->send();
                    return;
                }
                foreach ($this->selectedBranches as $branchId) {
                    HolidayBranch::firstOrCreate([
                        'holiday_id' => $holidayId,
                        'branch_id' => $branchId,
                    ]);
                }
            }
            DB::commit();
            $this->dispatch("branch-list-refresh");
            $this->notify('Holiday assigned successfully')->send();
        } catch (\Exception $e) {
            Log::error("Error while assigning holiday to branches: " . $e->getMessage());
            DB::rollBack();
            $this->notify('Error while assigning holiday')->type("error")->send();
        }
    }
}
