<?php

namespace App\Livewire\Config\Department;

use App\Models\configs\Department;
use App\Models\configs\Unit as UnitModel;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class Unit extends Component
{
    use WithDataTable, WithNotify;
    public $editingId;
    public $name, $remarks;
    public $departmentId;

    public function rules()
    {
        return [
            "name" => "required|max:255",
            "remarks" => "required",
        ];
    }

    public function mount($departmentId)
    {
        $this->departmentId = $departmentId;
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = UnitModel::search($this->search)->where("department_id", $this->departmentId);
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[On("hidden.bs.modal")]
    public function modalHidden()
    {
        $this->editingId = null;
        $this->reset(["name", "remarks"]);
        $this->resetValidation();
    }

    public function save()
    {
        $this->validate();
        if ($this->editingId) {
            $department = UnitModel::findOrFail($this->editingId);
            $department->fill($this->all())->save();
        } else {
            UnitModel::create(array_merge($this->all(), ["department_id" => $this->departmentId]));
        }
        unset($this->list);
        $this->dispatch("hide-model");
        $this->notify("Unit saved successfully")->send();
    }

    public function edit($id)
    {
        $this->editingId = $id;
        $row = UnitModel::findOrFail($id);
        foreach ((new UnitModel)->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    public function delete($id)
    {
        $unit = UnitModel::find($id);
        if (!$unit) {
            return $this->notify("Unit not found")->type("error")->send();
        }

        $unitEmp = EmployeeOrg::where('unit_id', $unit->id)->exists();
        if ($unitEmp) {
            return $this->notify('Cannot delete unit assigned for employees.')->type('error')->send();
        }
        $unit->delete();

        unset($this->list);
        $this->notify("Unit deleted successfully")->send();
    }

    public function render()
    {
        return view('livewire.config.department.unit');
    }
}
