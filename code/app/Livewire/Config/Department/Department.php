<?php

namespace App\Livewire\Config\Department;

use App\Models\configs\Company;
use App\Models\configs\Department as DepartmentModal;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Departments')]
class Department extends Component
{
    use WithDataTable, WithNotify;
    public $name, $abbreviation, $remarks, $hod, $need_unit = 0, $company_id, $filter_company_id;

    public function rules()
    {
        return [
            "name" => "required",
            "abbreviation" => ["required", Rule::unique('departments', 'abbreviation')->whereNull('deleted_at')],
            // "abbreviation" => "required|unique:departments,abbreviation,NULL,deleted_at",
            "remarks" => "required",
            "hod" => "nullable|exists:employees,id",
            "need_unit" => "required|boolean",
            "company_id" => "required|exists:companies,id",
        ];
    }
    public function validationAttributes()
    {
        return [
            'name' => 'department name',
            'company_id' => 'company'
        ];
    }

    public function updated($attrs)
    {
        if ($attrs == 'filter_company_id') {
            unset($this->list);
        }
    }

    public function mount()
    {
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();

        if ($currentEmployee) {
            $this->filter_company_id = $currentEmployee?->company_id;
            $this->company_id = $currentEmployee?->company_id;
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        $query = DepartmentModal::search($this->search)
            ->when($this->filter_company_id, function ($query) {
                $query->where("company_id", $this->filter_company_id);
            });

        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if ($currentEmployee) {
                if (scopeCompany() || scopeBranch() || scopeRegion()) {
                    $query->where("company_id", $currentEmployee->company_id);
                } else if (scopeDepartment()) {
                    $query->where('id', $orgInfo->department_id);
                } else {
                    $query->where('id', $orgInfo->department_id);
                }
            }
        }
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        if (scopeAll()) {
            return Company::orderBy('name', 'asc')->select("id", "name")->get();
        } else {
            return Company::where('id', $this->company_id)->orderBy('name', 'asc')->select("id", "name")->get();
        }
    }

    #[On("hidden.bs.modal")]
    public function modalHidden()
    {
        $this->reset(["name", "remarks", "hod", "need_unit", "company_id"]);
        $this->resetValidation();
    }

    #[On("employee-dropdown-select")]
    public function selectEmployee($employeeId)
    {
        $this->hod = $employeeId;
    }

    public function save()
    {
        $this->validate();
        DepartmentModal::create(array_merge(
            $this->all(),
            ["abbreviation" => $this->abbreviation ? strtoupper($this->abbreviation) : ""]
        ));

        unset($this->list);
        $this->dispatch("hide-model");
        $this->notify("Department saved successfully")->send();
    }


    public function delete($id)
    {
        $department = DepartmentModal::find($id);
        if (!$department) {
            return $this->notify("Department not found")->type("error")->send();
        }
        $departmentEmp = EmployeeOrg::where('department_id', $department->id)->exists();
        if ($departmentEmp) {
            return $this->notify('Cannot delete department which are assigned to employees.')->type('error')->send();
        }

        $department->delete();
        unset($this->list);
        $this->notify("Department deleted successfully")->send();
    }

    public function render()
    {
        return view('livewire.config.department.department');
    }
}
