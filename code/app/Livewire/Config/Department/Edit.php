<?php

namespace App\Livewire\Config\Department;

use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Traits\WithNotify;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Department Edit')]
class Edit extends Component
{
    use WithNotify;

    public $departmentId;
    public $needUnit = false;

    // form fields
    public $name, $remarks, $hod, $need_unit, $company_id, $abbreviation;
    public function rules()
    {
        return [
            "name" => "required",
            "remarks" => "required",
            "hod" => "nullable|exists:employees,id",
            "abbreviation" => ["required", "max:10", Rule::unique("departments", "abbreviation")->ignore($this->departmentId)],
            "need_unit" => "required",
            "company_id" => "required|exists:companies,id"
        ];
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return Company::orderBy('name', 'asc')->select("id", "name")->get();
    }

    public function mount($id) {
        $department = Department::findOrFail($id);
        $this->departmentId = $id;
        $this->needUnit = $department->need_unit;
        $this->fillForm($department);
    }
    
    #[On("employee-dropdown-select")]
    public function selectEmployee($employeeId)
    {
        $this->hod = $employeeId;
    }


    public function fillForm($department) {
        $this->name = $department->name;
        $this->remarks = $department->remarks;
        $this->abbreviation = $department->abbreviation;
        $this->hod = $department->hod;
        $this->need_unit = $department->need_unit;
        $this->company_id = $department->company_id;
    }

    public function update() {
        $this->validate();
        $department = Department::findOrFail($this->departmentId);
        if (!$this->need_unit) {
            if(count($department->units)) {
                $this->notify("You can't change need unit of this department because it has units associated with it")->type("error")->duration(5)->send();
                return;
            }
        }
        $department->fill([
            "name" => $this->name,
            "remarks" => $this->remarks,
            "hod" => $this->hod,
            "need_unit" => $this->need_unit,
            "company_id" => $this->company_id,
            "abbreviation" => $this->abbreviation ? strtoupper($this->abbreviation) : null,
        ])->save();
        $this->needUnit = $department->need_unit;
        $this->dispatch("hide-model");
        $this->notify("Department updated successfully")->send();
    }

    public function backToList()
    {
        $this->redirect(route("department"),true);
    }

    public function render()
    {
        return view('livewire.config.department.edit');
    }
}
