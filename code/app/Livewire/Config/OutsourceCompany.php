<?php

namespace App\Livewire\Config;

use App\Models\configs\Company as ComponyModel;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Title('Outsource Company Management')]
class OutsourceCompany extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null, $isEditing = false, $message = null;

    #[Validate('required|string')]
    public $name = "";

    public $phone = "";
    public $service_charge = 0;

    public $address = "",  $pan_number = "", $status = false, $company_id;

    protected $model;

    public function rules()
    {
        return [
            'phone' => $this->phone ? [
                'regex:/^(\+\d{1,4})?(\d{7,10})$/',
            ] : ['nullable'],
        ];
    }

    public function __construct()
    {
        $this->model = new \App\Models\configs\OutsourceCompany;
    }

    public function mount()
    {
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
        $this->scopeWiseFilters();
    }


    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();

        if ($currentEmployee) {
            $this->company_id = $currentEmployee?->company_id;
        }
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }
    public function validationAttributes()
        {
            return[
                'name' =>  'company name'
            ];
        }

    public function save()
    {
        $this->validate();

        if ($this->isEditing) {
            $data = $this->model->findOrFail($this->editingId)->fill($this->all());
            $data->company_id = $this->company_id;
            $data->save();

            $this->message = 'Outsource company edited successfully!!';
        } else {
            $data = $this->all();
             $data['company_id'] = $this->company_id;
            $this->model->create($data);

            $this->message = "Outsource company saved successfully!!";
        }
        unset($this->list);

        $this->dispatch('hide-model');
        $this->notify($this->message)->send();
    }

    public function delete(int $id)
    {
        $outsourceCompany = $this->model->findOrFail($id);

        $outsourceCompanyEmp = EmployeeOrg::where('outsource_company_id', $outsourceCompany->id)->exists();
        if ($outsourceCompanyEmp) {
            $this->notify('Cannot delete outsource company assigned for employees.')->type('error')->send();
            return;
        }

        $outsourceCompany->delete();
        unset($this->list);

        $this->message = 'Outsource company deleted successfully!!';
        $this->notify($this->message)->send();
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(['name', 'phone', 'address', 'pan_number', 'service_charge', 'status', 'isEditing']);;
        $this->resetErrorBag();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
    }

    #[Computed(persist: true)]
    public function list()
    {
        if(scopeAll())
        {
            $query = $this->model;
        } else {
            $query = $this->model->where('company_id', $this->company_id);
        }
        return  $this->applySorting(
           $query->search($this->search)
        )->paginate($this->perPage);
    }
}
