<?php

namespace App\Livewire\Config\Leave;

use App\Models\Leaves\LeaveOption as mLeaveOption;
use App\Models\Leaves\LeaveRequest;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;

#[Title('Leave Option Management')]
class LeaveOption extends Component
{
    use WithDataTable, WithNotify;

    public $name, $num_days, $replacement_available = false, $is_active = true;

    public $editingId = null;

    private mLeaveOption $model;

    public function __construct()
    {
        $this->model = new mLeaveOption;
    }

    public function rules()
    {
        return [
            'name' => 'required|max:191',
            'num_days' => 'required|numeric|max:1.00|min:0.01',
            'replacement_available' => 'required|boolean',
            'is_active' => 'required|boolean'
        ];
    }

    public function messages()
    {
        return [
            'num_days.max' => "The :attribute can't be more than 1 day"
        ];
    }

    public function validationAttributes()
    {
        return [
            'num_days' => 'no of day',
        ];
    }

    #[Computed(persist: true)]
    public function list()
    {
        $leaveOptions = mLeaveOption::paginate($this->perPage);
        $leaveOptions->getCollection()->transform(function ($leaveOption) {
            $leaveOption->has_leave_request = LeaveRequest::where('leave_option_id', $leaveOption->id)->exists();
            return $leaveOption;
        });
        return $leaveOptions;
    }

    #[On("hidden.bs.modal")]
    public function resetForm()
    {
        $this->reset();
        $this->resetValidation();
        $this->editingId = null;
    }

    public function edit(int $id): void
    {
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function changeStatus(int $id): void
    {
        $leaveOption = mLeaveOption::findOrFail($id);

        $leaveOption->is_active = !$leaveOption->is_active;
        $leaveOption->save();
        $this->notify('Leave Option status updated successfully!!')->send();
        unset($this->list);
    }

    public function delete(int $id): void
    {
        $leaveOption = mLeaveOption::findOrFail($id);
        $leaveOption->delete();
        unset($this->list);
        $this->model->removeCache();
        $this->notify('Leave Option deleted successfully!!')->send();
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);
        $this->name = $row->name;
        $this->num_days = $row->num_days;
        $this->replacement_available = (bool)$row->replacement_available;
        $this->is_active = (bool)$row->is_active;
    }

    public function save(): void
    {
        $this->validate();
        $message = "";
        if ($this->editingId) {
            $this->model->findOrFail($this->editingId)->fill($this->all())->save();
            $message = 'Leave Option edited successfully!!';
        } else {
            $this->editingId = $this->model->create($this->all())->id;
            $message = 'Leave Option added successfully!!';
        }
        unset($this->list);

        $this->dispatch('hide-model');
        $this->model->removeCache();
        $this->notify($message)->send();
    }

    public function render()
    {
        return view('livewire.leaves.leave-option');
    }
}
