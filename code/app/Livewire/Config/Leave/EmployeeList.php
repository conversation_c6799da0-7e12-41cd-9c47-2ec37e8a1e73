<?php

namespace App\Livewire\Config\Leave;

use App\Models\Employee\Employee;
use App\Models\Leaves\EmployeeLeave;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class EmployeeList extends Component
{
    use WithNotify, WithRowSelect, MultiselectEmployeeSearch;

    public $departmentId = "", $branchId = "", $gender, $companyId, $employeeStatusId, $fiscalYearId, $employee_ids = [], $regionId;
    public $selectAllData = false;

    public $fiscalYears = [];

    public function mount($fiscalYears)
    {
        $this->multiSelectAttributes = ['employee_ids'];
        $this->rowSelectEvent = "leave-employee-select";
        $this->pageName = "employees-page";
        $this->fiscalYears = $fiscalYears;
        $this->scopeWiseFilters();
        $this->fiscalYearId = \App\Models\configs\FiscalYear::where('is_active', 1)->whereNull('deleted_at')->pluck('id')->first();
    }

    public function scopeWiseFilters()
    {
        if (scopeAll()) {
            return;
        }

        $currentEmployee = currentEmployee();
        $orgInfo = $currentEmployee?->organizationInfo;

        $this->companyId = $currentEmployee?->company_id;

        if (scopeCompany() || scopeRegion()  || scopeBranch()) {
            $this->regionId = $orgInfo?->region_id;
            // No additional action needed since companyId is already set
        } elseif (scopeBranch()) {
            $this->branchId = $orgInfo?->branch_id;
        } else {
            $this->regionId = $orgInfo?->region_id;
            $this->branchId = $orgInfo?->branch_id;
            $this->departmentId = $orgInfo?->department_id;
        }
    }
    public function updatedEmployeeIds()
    {
        unset($this->list);
        $this->resetPage($this->pageName);
    }

    #[On("update-fiscal-year-filter")]
    public function updateFiscalYearFilter($fiscalYearId)
    {
        $this->fiscalYearId = $fiscalYearId;
    }

    public function recipientListQuery()
    {
        $fiscalYearId = $this->fiscalYearId;
        return Employee::with(['assignedLeaves' => function ($query) use ($fiscalYearId) {
            $query->where("fiscal_year_id", $fiscalYearId);
        }])
            ->leftJoin("employee_org", 'employees.id', '=', 'employee_org.employee_id')
            ->leftJoin("companies as comp", "comp.id", '=', "employees.company_id")
            ->when($this->departmentId, function ($query) {
                return $query->where("employee_org.department_id", $this->departmentId);
            })
            ->when($this->gender, function ($query) {
                return $query->where('employees.gender', $this->gender);
            })
            ->when($this->branchId, function ($query) {
                return $query->where("employee_org.branch_id", $this->branchId);
            })
            ->when($this->employeeStatusId, function ($query) {
                return $query->where("employee_org.employee_status_id", $this->employeeStatusId);
            })
            ->when($this->companyId, fn($query) => $query->where('employees.company_id', $this->companyId))
            ->when(count($this->employee_ids), function ($query) {
                $query->whereIn('employees.id', $this->employee_ids);
            })
            ->select(
                'employees.id as id',
                DB::raw("CONCAT(comp.code,'-',employee_org.employee_code) as code"),
                DB::raw("CONCAT_WS(' ', employees.first_name, employees.middle_name, employees.last_name) as full_name")
            );
    }

    #[Computed(persist: true)]
    public function list()
    {
        return $this->applySorting($this->recipientListQuery())->paginate($this->perPage, pageName: $this->pageName);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return \App\Models\configs\Company::orderBy('name', 'asc')->where('is_active', '1')->pluck("name", "id", "is_active")->ToArray();
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return \App\Models\configs\Department::orderBy('name', 'asc')->select("id", "name")->where('company_id', $this->companyId)->get();
    }

    #[Computed(persist: true)]
    public function branches()
    {
        if (scopeAll()) {
            return \App\Models\configs\Branch::orderBy('name', 'asc')->select("name", "id")->get();
        } elseif (scopeCompany()) {
            return \App\Models\configs\Branch::where('company_id', $this?->companyId)->orderBy('name', 'asc')->select("name", "id")->get();
        } else {
            $query = \App\Models\configs\Branch::where('company_id', $this?->companyId);
            if (!is_null($this?->regionId)) {
                $query->where('region_id', $this?->regionId);
            }
            return $query->orderBy('name', 'asc')->select("name", "id")->get();
        }
    }

    #[Computed(persist: true)]
    public function employeeStatus()
    {
        return \App\Models\configs\EmpStatus::orderBy('name', 'asc')->select("id", "name")->get();
    }

    #[Computed(persist: true)]
    public function fiscalYearList()
    {
        return \App\Models\configs\FiscalYear::select("id", "name")
            ->whereNull('deleted_at')
            ->orderBy("name", "desc")
            ->get();
    }

    #[Computed()]
    public function genders()
    {
        return [
            "male" => "Male",
            "female" => "Female",
            "other" => "Other",
        ];
    }

    #[On("employee-list-refresh")]
    public function refreshList()
    {
        unset($this->list);
    }

    public function updated($attr)
    {
        if (in_array($attr, ['departmentId', 'branchId', 'gender', 'employeeStatusId', 'companyId', 'fiscalYearId'])) {
            $this->resetPage($this->pageName);
            $this->removeAllSelectedRows();
            $this->selectAllData = false;
            $this->updatedSelectAllData();
            unset($this->list);
        }
        if (in_array($attr, ['companyId'])) {
            unset($this->branches, $this->departments);
            $this->branchId = null;
            $this->departmentId = null;
        }
    }

    public function updatedSelectAllData()
    {
        if ($this->selectAllData) {
            $this->selectedRows = $this->recipientListQuery()->pluck('employees.id')->toArray();
        } else {
            $this->selectedRows = [];
        }
        $this->dispatch($this->rowSelectEvent, $this->selectedRows);
    }

    public function removeLeave($leaveTypeId, $employeeId, $fiscalYearId)
    {
        $empLeave = EmployeeLeave::where("leave_type_id", $leaveTypeId)
            ->where("employee_id", $employeeId)
            ->where("fiscal_year_id", $fiscalYearId)
            ->first();
        $empLeave->delete();
        unset($this->list);
        $this->notify('Leave removed successfully')->send();
    }
}
