<?php

namespace App\Livewire\Config\Leave;

use App\Http\Helpers\Constant;
use App\Models\configs\LeaveType;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;
use Livewire\Form;

class LeaveTypeForm extends Form
{
    protected $model;

    public bool $isReplacementLeave = false;

    #[Validate('required|max:50')]
    public $name = '';

    #[Validate('required|numeric')]
    public $default_days = 0;

    #[Validate('required')]
    public $halfday = "0";

    #[Validate('required|boolean')]
    public $carry_forward = false;

    #[Validate('required|numeric')]
    public $carry_forward_days = 0;

    #[Validate('required|boolean')]
    public $paid = false;

    #[Validate('required|boolean')]
    public $monthly = false;

    #[Validate('required|boolean')]
    public $pro_rata_basis = false;

    #[Validate('required|boolean')]
    public $substitute_mandatory = false;

    #[Validate('required|boolean')]
    public $enable_substitute = false;

    #[Validate('required|boolean')]
    public $ignore_joining_date = false;

    #[Validate('required|boolean')]
    public $cashable = false;

    #[Validate('required|boolean')]
    public $assign_employee = false;

    #[Validate('boolean')]
    public $is_active = true;

    public $has_expiry = false;

    #[Validate('nullable|numeric|min:0')]
    public $expiry_day_count = null;

    public function set(LeaveType $leaveType)
    {
        foreach ((new LeaveType)->getFillable() as $fillable) {
            $this->{$fillable} = $leaveType->{$fillable};
            if ($this->expiry_day_count) $this->has_expiry = true;
            $this->expiry_day_count = $leaveType->expiry_day_count;
            if ($leaveType->name == Constant::REPLACEMENT_LEAVE_NAME) {
                $this->isReplacementLeave = true;
            }
            $this->halfday = $leaveType->halfday ? "1" : "0";
            // dd($leaveType);
        }
    }
    public function validationAttributes()
    {
        return [
            'name'  => 'leave name',
        ];
    }

    public function update(LeaveType $leaveType)
    {
        Log::info("Params for updating leave type {$leaveType->id}: ", $this->all());
        $validated = $this->validate();
        if ($leaveType->name == Constant::REPLACEMENT_LEAVE_NAME) {
            unset($validated['name']);
            unset($validated['halfday']);
        }
        $this->validate([
            'name' => Rule::unique("leave_types")->ignore($leaveType->id),
        ]);
        if (!$this->has_expiry) $validated['expiry_day_count'] = null;
        $validated['expiry_day_count'] = $validated['expiry_day_count'] ? $validated['expiry_day_count'] : null;
        $leaveType->fill($validated)->save();
        Log::info("Leave Type Updated.");
        $this->resetForm();
    }

    public function create()
    {
        Log::info("Params for creating leave type: ", $this->all());
        $validated = $this->validate();
        $this->validate([
            'name' => Rule::unique("leave_types"),
        ]);
        if (!$this->has_expiry) $validated['expiry_day_count'] = null;
        $validated['expiry_day_count'] = $validated['expiry_day_count'] ? $validated['expiry_day_count'] : null;
        LeaveType::create($validated);
        Log::info("Leave Type Created.");
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->reset();
        $this->resetValidation();
    }
}
