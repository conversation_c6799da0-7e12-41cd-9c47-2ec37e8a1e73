<?php

namespace App\Livewire\Config\Leave;

use App\Http\Helpers\Constant;
use App\Http\Repositories\LeaveRepository;
use App\Models\configs\LeaveType as LeaveTypeModel;
use App\Traits\WithNotify;
use App\Traits\WithRowSelect;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use PermissionList;

#[Title('Leave Type Management')]
class LeaveType extends Component
{
    use WithNotify, WithRowSelect;

    protected $model;
    public LeaveTypeForm $form;

    /** For editing popup title */
    public $title = '';

    public $isEditing = false;
    public $editingId = null;
    public $fiscalYearId = "";

    public $selectedEmployees = [];

    public function mount()
    {
        $this->fiscalYearId = session(Constant::SESSION_CURRENT_FISCAL_YEAR);
        $this->pageName = 'leave-types';
    }

    #[Computed(persist: true)]
    public function fiscalYears()
    {
        return \App\Models\configs\FiscalYear::select('id', 'name', 'is_active')
            ->whereNull('deleted_at')
            ->orderBy('name', 'desc')
            ->pluck("name", "id");
    }

    #[Computed(persist: true)]
    public function list()
    {
        return  $this->applySorting(LeaveTypeModel::search($this->search))->paginate($this->perPage, pageName: $this->pageName);
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->form->resetForm();
        $this->reset(['editingId', 'isEditing', 'title']);
    }

    #[On("leave-employee-select")]
    public function employeeSelect($employees)
    {
        $this->selectedEmployees = $employees;
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $row = LeaveTypeModel::findOrFail($id);
        $this->form->set($row);
        $this->title = $row->name;
    }

    public function save(): void
    {
        if ($this->isEditing) {
            authorizePermission(PermissionList::LEAVE_TYPE_UPDATE);
            $row = LeaveTypeModel::findOrFail($this->editingId);
            $this->form->update($row);
            $this->dispatch("employee-list-refresh");
        } else {
            authorizePermission(PermissionList::LEAVE_TYPE_CREATE);
            $this->form->create();
        }
        $this->reset(['editingId', 'isEditing', 'title']);
        $this->dispatch('hide-model');
        unset($this->list);
        $this->notify('Leave type saved successfully')->send();
    }

    public function delete(int $id): void
    {
        authorizePermission(PermissionList::LEAVE_TYPE_DELETE);
        try {
            $row = LeaveTypeModel::findOrFail($id);
            $row->delete();
            Log::info("Leave Type of id $id deleted successfully");
            $this->dispatch("employee-list-refresh");
            $this->notify('Leave type deleted successfully')->send();
        } catch (\Exception $e) {
            Log::info("Error while deleting the row: ", $e->getMessage());
            $this->notify('Error while deleting leave type')->type('error')->send();
        }
        unset($this->list);
    }

    public function assignLeaves()
    {
        if (!count($this->selectedRows)) {
            $this->notify('Please select leave types to assign')->type("error")->send();
            return;
        }
        if (!count($this->selectedEmployees)) {
            $this->notify('Please select employees to assign')->type("error")->send();
            return;
        }
        DB::beginTransaction();
        try {
            foreach ($this->selectedRows as $leaveTypeId) {
                $leaveRepo = new LeaveRepository($this->fiscalYearId);
                $leave = LeaveTypeModel::findOrFail($leaveTypeId);
                $response = $leaveRepo->assignLeave($leave, $this->selectedEmployees);
                if (!$response['status']) {
                    $this->notify($response['message'])->type("error")->send();
                    DB::rollBack();
                    return;
                   
                }
            }
            DB::commit();
            $this->dispatch("employee-list-refresh");
            $this->dispatch("update-fiscal-year-filter", $this->fiscalYearId);
            $this->notify($response['message'])->send();
        } catch (\Exception $e) {
            \logError("Error while assigning leave to employees: ", $e);
            DB::rollBack();
            $this->notify('Error while assigning leave')->type("error")->send();
        }
    }

    public function unassignLeaves()
    {
        if (!count($this->selectedRows)) {
            $this->notify('Please select leave types to assign')->type("error")->send();
            return;
        }
        if (!count($this->selectedEmployees)) {
            $this->notify('Please select employees to assign')->type("error")->send();
            return;
        }
        $leaveRepo = new LeaveRepository($this->fiscalYearId);
        $response = $leaveRepo->unassignLeaves($this->fiscalYearId, $this->selectedRows, $this->selectedEmployees);
        if (!$response['status']) {
            $this->notify($response['message'])->type("error")->send();
        } else {
            $this->dispatch("employee-list-refresh");
            $this->notify($response['message'])->send();
        }
    }
}
