<?php

namespace App\Livewire\Config\Setting\Section;

use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class SetLeave extends Component
{
    use WithNotify;
    public $days;
    public function mount()
    {
        $this->days = Setting::where('key', 'leave.max_days_to_apply')->value('value') ?? null;
    }
    public function getValidationAttributes()
    {
        return ['days' => 'Days Range Valid For Leave Request'];
    }

    public function rules()
    {
        return [
            'days' => 'required|integer|min:0',
        ];
    }

    public function save()
    {
        $this->validate();

        if (!isSuperAdmin()) {
            return $this->notify('You are not allowed to update Leave settings.')->type('error')->send();
        }

        try {
            $params = [
                'namespace' => 'leave',
                'key' => 'leave.max_days_to_apply',
                'value' => $this->days,
                'types' => 'integer', 
            ];
    
            $setting = Setting::where('key', 'leave.max_days_to_apply')->first();

            if ($setting) {
                $setting->update($params);
                $this->notify('Leave setting updated successfully')->send();
                return;
            }

            Setting::create($params);
            $this->notify('Leave setting saved successfully')->send();

        } catch (\Throwable $e) {
            Log::error('Error saving leave setting: ' . $e->getMessage());
            $this->notify('Error saving leave setting')->type('error')->send();
        }
    }

    public function render()
    {
        return view('livewire.config.setting.section.set-leave');
    }
}