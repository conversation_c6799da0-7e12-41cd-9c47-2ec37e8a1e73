<?php

namespace App\Livewire\Config\Setting\Section;

use App\Models\configs\MailConfigurationSetting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Component;

class Mail extends Component
{
    use WithNotify;

    public $company_id;
    public $mail_mailer, $mail_host, $mail_port, $mail_username, $mail_password, $mail_encryption, $mail_from_address, $mail_from_name;
    public function mount()
    {
        foreach ($this->settingKeys() as $key => $type) {
            $this->{$key} = MailConfigurationSetting::where('key', $key)->where('company_id', $this->company_id)->value('value');
        }
    }

    public function save()
    {
        $this->validate([
            'mail_mailer' => 'required|string',
            'mail_host' => 'required|string',
            'mail_port' => 'required|numeric|min:1',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        try {
            foreach ($this->settingKeys() as $key => $type) {
                MailConfigurationSetting::updateOrCreate(
                    ['company_id' => $this->company_id, 'key' => $key],
                    [
                        'value' => ($this->{$key} === '' ? null : $this->{$key}),
                        'type' => $type
                    ]
                );
            }

            Log::info("Mail settings updated successfully.");
            $this->notify("Mail settings updated successfully")->send();
        } catch (\Throwable $e) {
            Log::error("Error saving mail settings", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    private function settingKeys()
    {
        return [
            'mail_mailer' => 'string',
            'mail_host' => 'string',
            'mail_port' => 'integer',
            'mail_username' => 'string',
            'mail_password' => 'password',
            'mail_encryption' => 'string',
            'mail_from_address' => 'string',
            'mail_from_name' => 'string',
        ];
    }

    

    public function updatedCompanyId($value)
    {
        foreach ($this->settingKeys() as $key => $type) {
            $this->{$key} = MailConfigurationSetting::where('company_id', $value)->where('key', $key)->value('value');
        }
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return \App\Models\configs\Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function render()
    {
        return view('livewire.config.setting.section.mail');
    }
}
