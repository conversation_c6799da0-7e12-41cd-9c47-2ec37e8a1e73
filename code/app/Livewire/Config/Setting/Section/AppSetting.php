<?php

namespace App\Livewire\Config\Setting\Section;

use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Repositories\Setting\Interfaces\AppSettingRepositoryInterface;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Livewire\Component;

class AppSetting extends Component
{
    use WithNotify;

    /** @var array<string,bool> */
    public array $toggles = [];

    /** @var array<string,bool> */
    public array $features = [
        'otp' => false,
        'mpin' => false,
        'sms' => false,
        'biometric' => false,
    ];

    public bool $firebase_notification = false;
    public ?string $baseUrl;

    #[Validate('nullable|string|max:50')]
    public ?string $firebase_unique_topic = null;

    protected AppSettingRepositoryInterface $appSettings;

    public function boot(AppSettingRepositoryInterface $appSettings): void
    {
        $this->appSettings = $appSettings;
    }

    public function mount()
    {
        // $this->appSettings->initializeDefaults();
        $this->toggles = $this->appSettings->all();
        $this->features = $this->appSettings->getFeatures();
        $this->firebase_notification = $this->appSettings->getFirebaseNotificationEnabled();
        $this->firebase_unique_topic  = $this->appSettings->getFirebaseUniqueTopic('');
        $this->baseUrl = $this->appSettings->getBaseUrl();

    }

    public function save()
    {
        $this->validate();

        $this->appSettings->setMany($this->toggles);
        $this->appSettings->setFeatures($this->features);
        $this->appSettings->setFirebaseNotificationEnabled($this->firebase_notification);
        $this->appSettings->setFirebaseUniqueCode($this->firebase_unique_topic ?? '');
        $this->appSettings->setBaseUrl($this->baseUrl);

        $this->notify('Settings saved successfully')->send();
    }

    public function render()
    {
        return view('livewire.config.setting.section.app-setting');
    }
}
