<?php

namespace App\Livewire\Config\Setting\Section;

use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Component;

class PayslipSetting extends Component
{
    use WithNotify;
    public $show_cancel_payslip = false;

    public function mount()
    {
        $this->show_cancel_payslip = (bool)\App\Models\configs\Setting::where('key', 'show_cancel_payslip')
            ->where('namespace', 'payslip_setting')
            ->value('value') ?? false;
    }

    public function save()
    {
        \App\Models\configs\Setting::updateOrCreate(
            ['key' => 'show_cancel_payslip', 'namespace' => 'payslip_setting'],
            ['value' => $this->show_cancel_payslip ? '1' : '0']
        );

        $this->notify('Payslip settings saved successfully!')->type('success')->send();
    }

    public function render()
    {
        return view('livewire.config.setting.section.payslip-setting');
    }
}
