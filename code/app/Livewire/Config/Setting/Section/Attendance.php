<?php

namespace App\Livewire\Config\Setting\Section;

use App\Http\Helpers\Constant;
use App\Livewire\Config\Setting\Settings;
use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Attendance extends Component
{
    use WithNotify;

    public $attendanceChunkSize, $syncWithJob, $attendanceSyncInterval, $attendanceDeviceSyncInterval;

    public function mount()
    {
        $this->attendanceChunkSize = Setting::where([['namespace', 'attendance'], ['key', 'attendanceChunkSize']])->first()?->value;
        $this->syncWithJob = Setting::where([['namespace', 'attendance'], ['key', 'syncWithJob']])->first()?->value;
        $this->attendanceSyncInterval = Setting::where([['namespace', 'attendance'], ['key', 'attendanceSyncInterval']])->first()?->value;
        $this->attendanceDeviceSyncInterval = Setting::where([['namespace', 'attendance'], ['key', 'attendanceDeviceSyncInterval']])->first()?->value;
    }

    public function save()
    {
        $this->validate([
            'attendanceChunkSize' => 'required|integer|min:10|max:500',
            'syncWithJob' => 'required|boolean',
            'attendanceSyncInterval' => 'required|integer|min:2|max:120',
            'attendanceDeviceSyncInterval' => 'required|integer|min:2|max:120',
        ]);

        try {
            $this->saveSettings();
            $this->notify("Attendance settings saved successfully.")->send();
            Log::info("Attendance settings updated by user ID: " . auth()->id());
        } catch (\Exception $e) {
            Log::error("Error saving attendance settings: " . $e->getMessage());
            $this->notify("An error occurred while saving attendance settings.")->type('error')->send();
        }
    }

    public function saveSettings()
    {
        Setting::updateOrCreate(
            ['namespace' => 'attendance', 'key' => 'attendanceChunkSize'],
            ['value' => $this->attendanceChunkSize, 'types' => 'integer']
        );

        Setting::updateOrCreate(
            ['namespace' => 'attendance', 'key' => 'syncWithJob'],
            ['value' => $this->syncWithJob, 'types' => 'boolean']
        );

        Setting::updateOrCreate(
            ['namespace' => 'attendance', 'key' => 'attendanceSyncInterval'],
            ['value' => $this->attendanceSyncInterval, 'types' => 'integer']
        );

        Setting::updateOrCreate(
            ['namespace' => 'attendance', 'key' => 'attendanceDeviceSyncInterval'],
            ['value' => $this->attendanceDeviceSyncInterval, 'types' => 'integer']
        );
    }

    public function initializeValue()
    {
        $this->attendanceChunkSize = Setting::where([['namespace', 'attendance'], ['key', 'attendanceChunkSize']])->first()?->value;
        $this->syncWithJob = Setting::where([['namespace', 'attendance'], ['key', 'syncWithJob']])->first()?->value;
        $this->attendanceSyncInterval = Setting::where([['namespace', 'attendance'], ['key', 'attendanceSyncInterval']])->first()?->value;
        $this->attendanceDeviceSyncInterval = Setting::where([['namespace', 'attendance'], ['key', 'attendanceDeviceSyncInterval']])->first()?->value;
    }

    public function render()
    {
        return view('livewire.config.setting.section.attendance');
    }
}
