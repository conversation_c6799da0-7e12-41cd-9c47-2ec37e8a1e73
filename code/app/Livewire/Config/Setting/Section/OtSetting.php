<?php

namespace App\Livewire\Config\Setting\Section;

use App\Http\Helpers\Constant;
use App\Livewire\Config\Setting\Settings;
use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Livewire\Component;

class OtSetting extends Component
{
    use WithNotify;

    public $durationData = [];


    public function mount()
    {
        $this->durationData = Setting::where('namespace', 'ot')->pluck('value', 'key')->toArray();
        foreach ($this->otTypes() as $key => $label) {
            $this->durationData["{$key}_max_claim_days"] = $this->durationData["{$key}_max_claim_days"] ?? Constant::DEFAULT_OT_CLAIM_DAYS;
        }
    }

    #[Computed(persist: true)]
    public function otTypes()
    {
        return Constant::OT_SETTING_NAME_LIST;
    }

    public function getValidationAttributes()
    {
        $attributes = [];

        foreach ($this->otTypes() as $key => $label) {
            $attributes["durationData.$key"] = "$label Total Minutes";
            $attributes["durationData.{$label}_max_claim_days"] = "$label Max Claim Days";
        }

        return $attributes;
    }

    public function saveOtSetting($ot_type)
    {
        Log::info("Starting OT setting save for type: $ot_type");

        $this->validate([
            "durationData.$ot_type" => 'required|numeric|min:1|max:600.99',
            "durationData.{$ot_type}_max_claim_days" => 'required|numeric|min:1|max:90',
        ]);

        try {
            $minutesValue = $this->durationData[$ot_type];
            $maxClaimDaysValue = $this->durationData["{$ot_type}_max_claim_days"];

            Log::info("Validated values => Minutes: $minutesValue, MaxClaimDays: $maxClaimDaysValue");

            $minutesSetting = Setting::updateOrCreate(
                ['namespace' => 'ot', 'key' => $ot_type],
                ['value' => $minutesValue, 'types' => 'integer']
            );
            Log::info("OT minutes setting updated/created:", $minutesSetting->toArray());

            $daysSetting = Setting::updateOrCreate(
                ['namespace' => 'ot', 'key' => "{$ot_type}_max_claim_days"],
                ['value' => $maxClaimDaysValue, 'types' => 'integer']
            );
            Log::info("OT max claim days setting updated/created:", $daysSetting->toArray());

            $this->notify('OT setting updated successfully')->send();
        } catch (\Throwable $e) {
            Log::error("Error while saving OT setting for type: $ot_type", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function render()
    {
        return view('livewire.config.setting.section.ot-setting');
    }
}
