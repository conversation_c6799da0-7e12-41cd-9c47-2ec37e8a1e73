<?php

namespace App\Livewire\Config\Setting\Section;

use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;
use Livewire\Component;

class Snapshots extends Component
{
    use WithNotify;

    public $snapshotTypes = ['attendance', 'employee'];
    public $companyList = [];
    public $branchList = [];
    public $departmentList = [];

    public $startDate = "";
    public $endDate = "";
    public $snapshotType = "";

    public $companyId = "";
    public $branchId = "";
    public $departmentId = "";

    public function mount()
    {
        $this->endDate = Carbon::now()->format('Y-m-d');
        $this->startDate = Carbon::now()->subDays(7)->format('Y-m-d');
        $this->loadCompanyList();
        $this->loadBranchList();
        $this->loadDepartmentList();
    }

    public function updatedCompanyId()
    {
        $this->branchId = "";
        $this->departmentId = "";
        $this->loadBranchList();
        $this->loadDepartmentList();
    }

    public function loadCompanyList()
    {
        $this->companyList = Company::pluck('name', 'id')->toArray();
    }


    public function loadBranchList()
    {
        $this->branchList = Branch::when($this->companyId, fn($q) => $q->where('company_id', $this->companyId))->pluck('name', 'id')->toArray();
    }

    public function loadDepartmentList()
    {
        $this->departmentList = Department::when($this->companyId, fn($q) => $q->where('company_id', $this->companyId))->pluck('name', 'id')->toArray();
    }

    public function sync()
    {

        $this->validate([
            'snapshotType' => 'required|in:attendance,employee',
            'startDate' => 'required|date|before_or_equal:endDate|before_or_equal:today',
            'endDate' => 'required|date|after_or_equal:startDate|before_or_equal:today',
            'branchId' => 'nullable|exists:branches,id',
            'departmentId' => 'nullable|exists:departments,id',
        ]);

        Artisan::call(
            "snapshot {$this->snapshotType} --from={$this->startDate} --to={$this->endDate}"
                . ($this->companyId ? " --company={$this->companyId}" : "")
                . ($this->branchId ? " --branch={$this->branchId}" : "")
                . ($this->departmentId ? " --department={$this->departmentId}" : "")
        );

        return $this->notify(message: "Snapshot has been scheduled through job.")->type('success')->send();
    }

    public function render()
    {
        return view('livewire.config.setting.section.snapshots');
    }
}
