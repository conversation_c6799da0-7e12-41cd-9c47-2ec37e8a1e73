<?php

namespace App\Livewire\Config\Setting\Section;

use App\Models\configs\Setting;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use \App\Http\Repositories\Setting\Interfaces\SettingRepositoryInterface;

class SetTime extends Component
{
    use WithNotify;
    public $days;

    public function mount()
    {
        $this->days = Setting::where('key', 'time.max_days_to_apply')->value('value') ?? null;
        $this->days = app(SettingRepositoryInterface::class)->getValue('time', 'time.max_days_to _apply', null);
    }
    protected function rules()
    {
        return [
            'days' => 'required|integer|min:0',
        ];
    }

    public function getValidationAttributes()
    {
        return [
            'days' => 'Time Request Days',
        ];
    }
    public function save()
    {
        $this->validate();

        if (!isSuperAdmin()) {
            return $this->notify('You are not allowed to update time settings.')->type('error')->send();
        }

        try {
                app(SettingRepositoryInterface::class)->createOrUpdate('time', 'time.max_days_to_apply', $this->days, 'integer');
    
            $this->notify('Time setting saved successfully')->send();

        } catch (\Throwable $e) {
            Log::error('Error saving time setting: ' . $e->getMessage());
            $this->notify('Error saving time setting')->type('error')->send();
        }
    }
    public function render()
    {
        return view('livewire.config.setting.section.set-time');
    }
}