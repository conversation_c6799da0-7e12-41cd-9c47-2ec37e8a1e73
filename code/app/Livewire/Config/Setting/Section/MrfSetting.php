<?php

namespace App\Livewire\Config\Setting\Section;

use App\Models\configs\MailConfigurationSetting;
use App\Models\configs\Setting;
use App\Models\configs\SystemSetting; // Add this import for system settings
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Component;

class MrfSetting extends Component
{
    use WithNotify;

    public $company_id;
    public $emailForm = false;

    public $company_name, $address, $authorized_signatory, $signatory_designation, $edf_email, $edf_add_employee, $contact_mail, $terms_and_conditions;
    public $url, $public_key;

    #[Computed(persist: true)]
    public function companies()
    {
        return \App\Models\configs\Company::orderBy('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function mount()
    {
        $this->company_id = 1;
        $this->loadMailSettings();
        $this->loadSystemSettings();
    }

    public function updatedCompanyId()
    {
        $this->loadMailSettings();
    }

    public function loadMailSettings()
    {
        foreach ($this->settingKeys() as $key => $type) {
            $value = MailConfigurationSetting::where('key', $key)
                ->where('company_id', $this->company_id)
                ->value('value');

            $this->{$key} = $this->castValue($value, $type);
        }

        $this->emailForm = (bool)$this->edf_email;
    }
    public function loadSystemSettings()
    {
        // Load URL and Public Key from system settings (not bound to company)
        $this->public_key = Setting::where('key', 'mrf_public_key')->value('value') ?? '';
    }

    public function updated($property, $value)
    {
        if ($property === 'edf_email') {
            $this->emailForm = (bool)$value;
        }
    }

    public function generatePublicKey()
    {
        $this->public_key = bin2hex(random_bytes(16)); // Generates 32 character alphanumeric key
    }

    public function save()
    {
        if ($this->emailForm) {
            $this->validate([
                'company_name' => 'required|string|max:255',
                'address' => 'required|string|max:500',
                'authorized_signatory' => 'required|string|max:255',
                'signatory_designation' => 'required|string|max:255',
                'contact_mail' => 'required|string|max:255',
                'edf_email' => 'boolean',
                'edf_add_employee' => 'boolean',
                'url' => 'nullable|url|max:500',
                'public_key' => 'nullable|string|max:255',
                'terms_and_conditions' => 'required',
            ]);
        } else {
            $this->validate([
                'edf_email' => 'boolean',
                'edf_add_employee' => 'boolean',
                'public_key' => 'nullable|string|max:255',
            ]);
        }

        try {
            DB::transaction(function () {
                // Save company-specific settings
                foreach ($this->settingKeys() as $key => $type) {
                    $value = $this->{$key};

                    if ($type === 'boolean') {
                        $value = $value ? '1' : '0';
                    }

                    MailConfigurationSetting::updateOrCreate(
                        [
                            'company_id' => $this->company_id,
                            'key' => $key
                        ],
                        [
                            'value' => $value === '' ? null : $value,
                            'type' => $type
                        ]
                    );
                }

                Setting::updateOrCreate(
                    ['namespace' => 'mrf', 'key' => 'mrf_public_key'],
                    ['value' => $this->public_key, 'type' => 'string']
                );
            });

            Log::info("MRF settings updated successfully for company: {$this->company_id}");
            $this->notify("MRF settings updated successfully")->type('success')->send();
        } catch (\Throwable $e) {
            Log::error("Error saving MRF settings", [
                'company_id' => $this->company_id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->notify("Error saving settings: " . $e->getMessage())->type('error')->send();
        }
    }

    private function settingKeys()
    {
        return [
            'edf_email' => 'boolean',
            'edf_add_employee' => 'boolean',
            'company_name' => 'string',
            'address' => 'string',
            'authorized_signatory' => 'string',
            'signatory_designation' => 'string',
            'url' => 'string',
            'contact_mail' => 'string',
            'terms_and_conditions' => 'text'
        ];
    }

    private function castValue($value, string $type)
    {
        if ($value === null) {
            return $type === 'boolean' ? false : '';
        }

        return match ($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            default => (string) $value,
        };
    }

    public function render()
    {
        return view('livewire.config.setting.section.mrf-setting');
    }
}
