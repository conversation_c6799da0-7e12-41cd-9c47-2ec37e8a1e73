<?php

namespace App\Livewire\Config\Setting;

use App\Http\Helpers\Constant;
use App\Traits\WithDataTable;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use App\Models\configs\Setting;
use App\Traits\WithNotify;

#[Title('Setting')]
class Settings extends Component
{
    use WithDataTable, WithNotify;

    #[Url('section')]
    public $activeSection = 'mrf_setting';

    public function mount()
    {
        // $this->activeSection = 'ot_request';
    }
    public function setActiveSection($section)
    {
        if ($section === 'mrf_setting') {
            redirect()->to(route('setting', ['section' => 'mrf_setting']));
        } else {
            $this->activeSection = $section;
        }

    }
    public function render()
    {
        return view('livewire.config.setting.settings');
    }
}
