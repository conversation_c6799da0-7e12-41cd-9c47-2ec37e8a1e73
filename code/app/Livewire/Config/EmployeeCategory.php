<?php

namespace App\Livewire\Config;

use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Component;

class EmployeeCategory extends Component
{
    use WithDataTable, WithNotify;
    public $editingId = null;
    public $isEditing = false;
    public $type;
    public $is_countable = true;
    public $company_id;
    public function rules()
    {
        return [
            "type" => "required|max:255",
            "is_countable" => "required|boolean",
        ];
    }

    public function mount()
    {
        $this->company_id = currentEmployee()?->company_id;
    }
    public function save()
    {
        $this->validate();
        if (isSuperAdmin()) {
            return $this->notify("Admin is not allowed to add employee category")->type("error")->send();
        }
        try {
            DB::beginTransaction();
            if ($this->editingId) {
                $category = \App\Models\EmployeeCategory::findOrFail($this->editingId);
                $category->fill($this->all())->save();
            } else {
                \App\Models\EmployeeCategory::create($this->all());
            }
            DB::commit();
            $this->notify("Employee category saved successfully")->send();
            $this->reset(["type", "is_countable"]);
            $this->dispatch("hide-model");
            unset($this->list);
        } catch (\Exception $th) {
            DB::rollBack();
            Log::error("Failed to save employee category: " . $th->getMessage());
            $this->notify("Failed to save employee category")->type("error")->send();
            $this->dispatch("hide-model");
        }
    }
    public function edit($id)
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $category = \App\Models\EmployeeCategory::findOrFail($id);
        $this->type = $category->type;
        $this->is_countable = $category->is_countable;
        $this->company_id = $category->company_id;
    }
    public function delete($id)
    {
        if (isSuperAdmin()) {
            return $this->notify("Admin is not allowed to delete employee category")->type("error")->send();
        }
        $categoryUsed = \App\Models\Employee\EmployeeOrg::where('employee_category_id', $id)->exists();
        if ($categoryUsed) {
            return $this->notify("Cannot delete employee category which are assigned to employees")->type("error")->send();
        }

        \App\Models\EmployeeCategory::findOrFail($id)->delete();
        $this->notify("Employee category deleted successfully")->send();
        unset($this->list);
    }
    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->reset(["type", "is_countable"]);
        $this->resetValidation();
    }
    #[Computed(persist: true)]
    public function list()
    {
        $query = \App\Models\EmployeeCategory::search($this->search);
        if (!isSuperAdmin()) {
            $query = $query->where('company_id', $this->company_id);
        }
        return $this->applySorting($query)->paginate($this->perPage);
    }
    public function render()
    {
        return view('livewire.config.employee-category');
    }
}
