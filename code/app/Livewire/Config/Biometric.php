<?php

namespace App\Livewire\Config;

use App\Http\Helpers\Constant;
use App\Http\Repositories\SyncRepository;
use App\Models\BiometricDeviceMapping;
use App\Models\configs\AttLog;
use App\Models\Leaves\Attendance;
use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Url;
use Livewire\Component;
use Rats\Zkteco\Lib\Helper\Util;

#[Title('Biometric Settings')]
class Biometric extends Component
{
    use WithNotify;
    #[Url]
    public $attdeviceid;

    public $device;

    public $users;

    public $attendanceData;

    public $editedUser = [
        'uid' => "",
        'userid' => "",
        'name' => "",
        'role' => "",
        'password' => "",
        'cardno' => "",
    ], $deviceUsers;

    public function rules()
    {
        return [
            "editedUser.uid" => ["nullable", "numeric", "min:1", "max:65535"],
            "editedUser.userid" => ["nullable", "numeric", "digits_between:1,9"],
            "editedUser.name" => ["nullable", "string", "max:24"],
            "editedUser.password" => ["nullable", "numeric", "digits_between:1,8"],
            "editedUser.role" => ["nullable", "in:" . Util::LEVEL_ADMIN . "," . Util::LEVEL_USER],
            "editedUser.cardno" => ["nullable", "numeric", "digits_between:1,10"]
        ];
    }

    public function __construct()
    {
    }

    public function mount()
    {
        $this->device = \App\Models\configs\AttDevice::findOrFail($this->attdeviceid);
    }

    public function  getAttendance()
    {
        $this->reset('attendanceData', 'users');
        $attendance = $this->device->getAttendance();
        $thumbids = $attendance->pluck('id');
        $employees = \App\Models\Employee\EmployeeOrg::whereIn('biometric_id', $thumbids)->get();

        $attendanceInDevice = collect();

        $attendance->each(function ($item) use (&$attendanceInDevice, $employees) {
            $employee = $employees->filter(fn ($employee) => $employee->biometric_id == $item->id)->first();

            $row['employee_id'] = $employee->employee_id ?? 'N/A';
            $row['employee_code'] = $employee->companyEmployeeCode ?? 'N/A';
            $row['name'] = $employee->employee->name ?? 'N/A';
            $row['biometric_id'] = $item->id;
            $row['state'] = $item->state;
            $row['inout_mode'] = $item->type;
            $row['date_time'] = $item->timestamp;
            $row['type'] = $item->type;
            $attendanceInDevice->push((object)$row);
        });
        $this->attendanceData = $attendanceInDevice;
        if ($this->attendanceData->count() == 0) {
            $this->notify('No Attendance Record found in Device')
                ->type('info')
                ->send();
        }
    }

    public function removeUser($uid, $bioId = null)
    {
        $result = $this->device->removeUser($uid);
        if ($result['status']) {
            try {
                if ($bioId) {
                    BiometricDeviceMapping::where("biometric_id", $bioId)->delete();
                    Log::info('Deleted the biometric id in biometric device mapping table.');
                }
            } catch (Exception $e) {
                Log::info('Unable to deleted the biometric id in biometric device mapping table. ', $e);
            }
            $this->notify($result['message'])
                ->type('success')
                ->position(Constant::NOTIFY_POSITION)
                ->duration(5)
                ->send();
        } else {
            $this->notify($result['message'])
                ->type('error')
                ->position(Constant::NOTIFY_POSITION)
                ->duration(5)
                ->send();
        }
        $this->getUsers();
    }

    public function changeAccess($bioId, $access = 'unset')
    {
        $result = $this->device->adminAccess($bioId, $access);
        if ($result['status']) {
            $this->notify($result['message'])
                ->type('success')
                ->position(Constant::NOTIFY_POSITION)
                ->duration(5)
                ->send();
        } else {
            $this->notify($result['message'])
                ->type('error')
                ->position(Constant::NOTIFY_POSITION)
                ->duration(5)
                ->send();
        }
        $this->getUsers();
    }

    public function getUsers()
    {
        $this->reset('attendanceData', 'users');
        $this->deviceUsers = $this->device->getUser();

        $thumbids = $this->deviceUsers->pluck('userid');
        $employees = \App\Models\Employee\EmployeeOrg::with('branch')->whereIn('biometric_id', $thumbids)->get();

        $deviceUsers = collect();

        $this->deviceUsers->each(function ($user) use (&$deviceUsers, $employees) {
            $employee = $employees->filter(fn ($employee) => $employee->biometric_id == $user->userid)->first();
            $status = empty($employee) ? 'N/A' : (is_null($employee->deleted_at) ? 'Active' : 'Terminated');
            $row['employee_id'] = $employee->employee_id ?? 'N/A';
            $row['employee_code'] = $employee->companyEmployeeCode ?? 'N/A';
            $row['name'] = $employee->employee->name ?? 'N/A';
            $row['username'] = $user->name ? $user->name : 'N/A';
            $row['thumbid'] = $user->userid;
            $row['branch'] = $employee->branch->name ?? 'N/A';
            $row['status'] = $status;
            $row['access_level'] = $user->role == Util::LEVEL_ADMIN ? 'Admin' : 'User';
            $row['bioUserId'] = $user->uid;
            $deviceUsers->push((object)$row);
        });
        $this->users = $deviceUsers;
        if ($this->users->count() == 0) {
            $this->notify('No users found in Device')
                ->type('info')
                ->send();
        }
    }

    public function editUser($index)
    {
        $user = $this->deviceUsers[$index];
        $this->editedUser['uid'] = $user->uid;
        $this->editedUser['userid'] = $user->userid;
        $this->editedUser['name'] = $user->name;
        $this->editedUser['role'] = $user->role;
        $this->editedUser['password'] = $user->password;
        $this->editedUser['cardno'] = trim($user->cardno);
    }

    public function saveUser()
    {
        $this->validate();

        $user = $this->editedUser;
        $result = $this->device->editUser(
            $user["uid"],
            $user["userid"],
            $user["name"],
            $user["password"],
            $user["role"],
            $user["cardno"]
        );

        if ($result) {
            Log::info('Updated the biometric id in biometric device mapping table.');
            $this->getUsers();
            $this->dispatch('hide-model');
            $this->notify('User edited successfully!!')->send();
        } else {
            $this->notify('Failed to edit user!!')->type('error')->send();
        }
    }

    #[On('hide.bs.modal')]
    public function resetUser()
    {
        $this->reset(['editedUser']);
        $this->resetErrorBag(['editedUser']);
    }

    public function deviceAction($action)
    {
        $this->device->setAction($action);
        $this->notify('Device Action ' . $action . ' has been executed')
            ->send();
    }

    public function pingDevice()
    {

        if ($this->ensureIsNotRateLimited($this->device->device_ip)) {
            RateLimiter::hit($this->throttleKey($this->device->device_ip), 120);
            $result = Process::run("ping -c 1 {$this->device->device_ip}");
            if ($result->successful()) {
                $last_ping_status = 1;
                $message = "Device is online.";
            } else {
                $last_ping_status = 2;
                $message = "Device is Offline. Please check if device is off Or contact Your IT team!";
                if ($result->errorOutput()) {
                    $message = $result->errorOutput();
                }
            }
            $last_ping_date = Date('Y-m-d H:i:s');
            $this->device->last_ping_status = $last_ping_status;
            $this->device->last_ping_date = $last_ping_date;
            $this->device->save();
            $message .= ' You attempted for ' . RateLimiter::attempts($this->throttleKey($this->device->device_ip)) . ' times.';
        } else {
            $message = 'You have attempted to ping more than rate limit. Please try again after 2 mins';
        }
        $this->notify($message)->type('info')->position('bottom-right')->duration(5)->send();
    }

    public function syncAttendanceLogAndAttendance()
    {
        $result = $this->device->syncAttendanceToDb(true);
        if ($result) {
            $syncRepo = new SyncRepository;
            $sync = $syncRepo->syncAttendanceFromDeviceToDb();
            if ($sync) {
                $this->notify('Attendance log and attendance synced successfully')->send();
            } else {
                $this->notify('Only attendance log is synced')->send();
            }
        } else {
            $this->notify('Failed to sync attendance log and attendance, please try again later')->type('error')->send();
        }
    }

    #[Computed]
    public function biometricDevice()
    {
        $deviceInfo = $this->device->deviceInfo();
        $logSync = AttLog::latest('fetch_date')->value('fetch_date');
        $attendanceSync = date(Attendance::latest('created_at')->value('created_at'));
        if ($deviceInfo) {
            $deviceInfo->put('ip', $this->device->device_ip)
                ->put('logSync', $logSync)
                ->put('attendanceSync', $attendanceSync);
            return $deviceInfo;
        } else {
            $this->notify('Unable to connect to deice')->type('error')->send();
            return [
                'ip' => '',
                'deviceName' => '',
                'version' => '',
                'osVersion' => '',
                'fmVersion' => '',
                'serialNumber' => '',
                'deviceTime' => '',
                'user' => '',
                'logSync' => '',
                'attendanceSync' => ''
            ];
        }
    }

    #[Computed(persist: true)]
    public function userRole()
    {
        return [
            [
                'id' => Util::LEVEL_USER,
                'value'  => 'User'
            ],
            [
                'id' => Util::LEVEL_ADMIN,
                'value'  => 'Admin'
            ],
        ];
    }

    public function render()
    {
        return view('livewire.config.biometric');
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited($ip)
    {
        if (!RateLimiter::tooManyAttempts($this->throttleKey($ip), 5)) {
            return true;
        }
        $seconds = RateLimiter::availableIn($this->throttleKey($ip));
        $this->message = "Rate Limit exceeded. You can ping device in " . ceil($seconds / 60) . ' Min. ' . ceil($seconds % 60) . ' Seconds';
        return false;
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey($ip): string
    {
        return Str::transliterate(Str::lower($ip) . '|' . Auth()->user()->email);
    }
}
