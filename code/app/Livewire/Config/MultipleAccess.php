<?php

namespace App\Livewire\Config;

use App\Http\Helpers\Constant;
use App\Http\Repositories\Configs\MultipleAccessRepository;
use App\Http\Services\ScopeFetcher;
use App\Models\configs\Branch;
use App\Models\configs\Company;
use App\Models\configs\Department;
use App\Models\configs\Region;
use App\Models\configs\SubBranch;
use App\Models\Employee\Employee;
use App\Traits\MultiselectEmployeeSearch;
use App\Traits\WithDataTable;
use Illuminate\Support\Facades\Validator;
use Livewire\Attributes\Computed;
use Livewire\Component;

class MultipleAccess extends Component
{
    use MultiselectEmployeeSearch, WithDataTable;
    
    public $access_type, $company, $region, $branch, $sub_branch, $department, $from, $to, $employee_id;
    public $sortBy;
    public $sortDirection;
    public $perPage;
    private MultipleAccessRepository $multiAccessRepo;

    public $edit = [
        'id',         
        'access_type',
        'company',    
        'region',     
        'branch',     
        'sub_branch', 
        'department', 
        'from',       
        'to',          
    ];

    public function __construct() {
        $this->sortBy = '';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
        $this->multiAccessRepo = new MultipleAccessRepository();
    }
    
    public function rules() {
        return [
            "employee_id"    => "required|exists:employees,id",
            "access_type"    => "required|in:company,region,branch,sub_branch,department",
            "company"        => "required_if:access_type,company|nullable|exists:companies,id",
            "region"         => "required_if:access_type,region|nullable|exists:regions,id",
            "branch"         => "required_if:access_type,branch|nullable|exists:branches,id",
            "sub_branch"     => "required_if:access_type,sub_branch|nullable|exists:sub_branches,id",
            "department"     => "required_if:access_type,department|nullable|exists:departments,id",
            "from"           => "required|date:y-m-d",
            "to"             => "nullable|date:y-m-d|after_or_equal:from",
        ];
    }
    
    public function mount() {
        $this->multiSelectAttributes = ['employee_id'];

        $this->edit = [
            'id'          => null,
            'access_type' => null,
            'company'     => null,
            'region'      => null,
            'branch'      => null,
            'sub_branch'  => null,
            'department'  => null,
            'from'        => null,
            'to'          => null,
        ];
    }
    
    public function render()
    {
        return view('livewire.config.multiple-access');
    }

    #[Computed(persist: true)]
    public function employees()
    {
        $this->dispatch("updateMultiselectItem", ["id" => "assign-employee-dropdown-request", "items" => [
            [
                'value' => "",
                'label' => "Select Employee",
                'selected' => true,
                'disabled' => true,
            ],
            ...Employee::list()
        ]]);
    }

    #[Computed(persist: true)]
    public function accessTypes(): array
    {
        return [
                Constant::COMPANY    => Constant::COMPANY,
                Constant::REGION     => Constant::REGION,
                Constant::BRANCH     => Constant::BRANCH,
                Constant::SUB_BRANCH => Constant::SUB_BRANCH,
                Constant::DEPARTMENT => Constant::DEPARTMENT,
            ];
    }

    #[Computed(persist: true)]
    public function companies()
    {
        return (new ScopeFetcher())->fetchCompany("id_name");
    }

    #[Computed(persist: true)]
    public function regions()
    {
        return (new ScopeFetcher())->fetchRegion(pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function branches()
    {
        return (new ScopeFetcher())->fetchBranch(pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function subBranches()
    {
        return (new ScopeFetcher())->fetchSubBranch(pluck: "id_name");
    }

    #[Computed(persist: true)]
    public function departments()
    {
        return (new ScopeFetcher())->fetchDepartment(pluck: "id_name");
    }
    
    #[Computed()]
    public function multipleAccessList()
    {
        return $this->applySorting(
            \App\Models\Config\MultipleAccess::with(['employee', 'modelable'])->search($this->search)->orderBy("created_at", "desc")
        )->paginate($this->perPage);
    }

    public function assignMultipleAccess() {
        $this->validate();

        try {
            $request = $this->all();
            $request['expired'] = "0";
            switch ($this->access_type) {
                case "company":
                    $request['model_type'] = Company::class;
                    $request['model_id'] = $request['company'];
                    break;
                case "region":
                    $request['model_type'] = Region::class;
                    $request['model_id'] = $request['region'];
                    break;
                case "branch":
                    $request['model_type'] = Branch::class;
                    $request['model_id'] = $request['branch'];
                    break;
                case "sub_branch":
                    $request['model_type'] = SubBranch::class;
                    $request['model_id'] = $request['sub_branch'];
                    break;
                case "department":
                    $request['model_type'] = Department::class;
                    $request['model_id'] = $request['department'];
                    break;
            }

            $create = $this->multiAccessRepo->create($request);

            if ($create) {
                $this->notify("Multiple access assigned successfully")->type('success')->send();
                $this->reset(["employee_id", "access_type", "company", "region", "branch", "sub_branch", "department", "from", "to"]);
            } else {
                $this->notify("Error assigning multiple access")->type('error')->send();
            }
            unset($this->multipleAccessList);
            $this->dispatch("toggle-selected-employee-id", ['']);
        } catch (\Exception $e) {
            $this->notify("Error assigning multiple access.")->type('error')->send();
        }
    }
    
    public function delete($id) {
        $deleteAccess = $this->multiAccessRepo->delete($id);
        if($deleteAccess) {
            $this->notify("Multiple access removed successfully")->type('success')->send();
            unset($this->multipleAccessList);
        } else {
            $this->notify("Error removing the multiple access.")->type('error')->send();
        }
    }
    
    public function editAccess($id) {
        $multipleAccess = $this->multiAccessRepo->edit($id);
        $property = $multipleAccess->type;
        
        $this->edit = [
            'access_type' => $multipleAccess->type,
            'from' => $multipleAccess->from,
            'to' => $multipleAccess->to,
        ];
        $this->edit[$property] = $multipleAccess->model_id;
        $this->edit['id'] = $id;
    }
    
    public function updateAccess() {
        $data = [
            "model_id"  => $this->edit[$this->edit['access_type']],
            "from"      => $this->edit['from'],
            "to"        => null,
            "expired"   =>  "1",
        ];
        if(isset($this->edit['to']) && $this->edit['to'] != "") {
            $data["to"] = $this->edit['to'];
            $data["expired"] = ($this->edit['to'] >= date("Y-m-d")) ? "0" : "1";
        }

        $validator = Validator::make($data, [
            "from"           => "required|date:y-m-d",
            "to"             => "nullable|date:y-m-d|after_or_equal:from",
        ]);

        if ($validator->fails()) {
            $this->notify($validator->errors()->first())->type('error')->send();
        } else {
            $update = $this->multiAccessRepo->update($this->edit['id'], $data);
            if($update) {
                $this->notify("Multiple access updated successfully")->type('success')->send();
                $this->dispatch('hide-model');
                unset($this->multipleAccessList);
            } else {
                $this->notify("Error updating the multiple access.")->type('error')->send();
            }
        }
    }
}
