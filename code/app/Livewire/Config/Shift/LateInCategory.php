<?php

namespace App\Livewire\Config\Shift;

use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Validate;
use Livewire\Component;

class LateInCategory extends Component
{
    use WithDataTable, WithNotify;

    public $editingId = null;

    public $isEditing = false;

    public $title = '';

    #[Validate('required | max:100')]
    public string $name = '';

    #[Validate('boolean')]
    public $is_active = true;

    protected $model;

    public function __construct()
    {
        $this->model = new \App\Models\configs\LateInCategory;

        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function validationAttributes()
    {
        return [
            'name'  => 'category name',
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function save(): void
    {
        $this->validate();

        try {
            DB::beginTransaction();
            if ($this->isEditing) {
                $lateInCategory = $this->model->find($this->editingId);
                $lateInCategory->fill($this->all());
                $lateInCategory->save();
            } else {
                $lateInCategory = $this->model->create($this->all());
                $this->editingId = $lateInCategory->id;
            }
            DB::commit();
            $this->notify('Late in category' . ($this->isEditing ? "updated" :  "saved") . ' successfully')->send();
            $this->dispatch('hide-edit-category-model');
            unset($this->list);
        } catch (Exception $e) {
            DB::rollBack();
            $message = "Unable to " . ($this->isEditing ? "update" :  "add") . " late in category";
            logError($message, $e);
            $this->notify($message)->type("error")->send();
        }
    }

    public function delete(int $id): void
    {
        $lateInCategory = $this->model->findOrFail($id);

        $lateInCategory->delete();
        $this->message = 'Late in category deleted successfully';
        $this->notify($this->message)->send();

        unset($this->list);
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset(['editingId', 'isEditing', 'title', 'name', 'is_active']);
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);

        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }

        $this->is_active = $row->is_active ? true : false;

        $this->title = $row->name;
    }

    #[Computed()]
    public function list()
    {
        return  $this->applySorting(
            $this->model->search($this->search)
        )->paginate($this->perPage, pageName: 'late-in-category');
    }

    public function render()
    {
        return view('livewire.config.shift.late-in-category');
    }
}
