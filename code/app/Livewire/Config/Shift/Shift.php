<?php

namespace App\Livewire\Config\Shift;

use App\Models\configs\Company;
use App\Models\configs\DutyRoster;
use App\Models\configs\LateInCategory;
use App\Models\Employee\EmployeeOrg;
use App\Traits\WithDataTable;
use App\Traits\WithNotify;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Layout;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

// this file is for FedEx
#[Title('Shift Management')]
class Shift extends Component
{
    use WithDataTable, WithNotify;

    public $message = null;

    public $editingId = null;

    public $isEditing = false;

    public $title = '';

    #[Validate('required | max:100')]
    public string $name = '';

    #[Validate('required', attribute: 'shift type')]
    public string $type = 'regular';

    #[Validate('required')]
    public string $start_time = '09:30';

    #[Validate('required')]
    public string $end_time = '17:30';

    #[Validate('integer')]
    public int $day_off = 7;

    #[Validate('string')]
    public string $leisure_start_time = '13:00';

    #[Validate('string')]
    public string $leisure_end_time = '14:00';

    #[Validate('integer')]
    public int $grace_time = 0;

    #[Validate('integer')]
    public int $is_default = 0;

    #[Validate('integer')]
    public int $is_active = 1;

    public float $total_working_hour;

    public float $actual_working_hour;

    #[Validate('required')]
    public $company_id;

    public $filter_company_id;

    public $lateInCategories = [];

    protected $model, $lateInTimeCategoryTimeModel;

    public function __construct()
    {
        $this->model = new \App\Models\configs\EmployeeShift;
        $this->lateInTimeCategoryTimeModel = new \App\Models\configs\LateInCategoryTime();

        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->perPage = 10;
    }
    public function validationAttributes()
    {
        return [
            'name' => 'shift name',
            'company_id' => 'company name'
        ];
    }

    public function edit(int $id): void
    {
        $this->isEditing = true;
        $this->editingId = $id;
        $this->fillFormModel($id);
    }

    public function rules()
    {
        return [
            'lateInCategories.*.*' => [
                function ($attribute, $value, $fail) {
                    $index = explode('.', $attribute)[1];
                    $lateInCategoryTime = $this->lateInCategories[$index]['time'] ?? null;
                    $lateInCategoriesCondition = $this->lateInCategories[$index]['condition'] ?? null;

                    if (empty($lateInCategoryTime) && empty($lateInCategoriesCondition)) {
                        return;
                    }

                    if ($lateInCategoryTime && empty($lateInCategoriesCondition)) {
                        $fail('Time field should be null when condition field is null.');
                    } else if (empty($lateInCategoryTime) && $lateInCategoriesCondition) {
                        $fail('Condition field should be null when time field is null.');
                    }
                }
            ]
        ];
    }

    public function save(): void
    {
        $this->validate();
        $this->type = $this->type == 'parttime' ? 'halfday' : $this->type;
        try {
            DB::beginTransaction();
            if ($this->isEditing) {
                $shift = $this->model->find($this->editingId);
                $shift->fill($this->all());
                $shift->total_working_hour = round(abs(strtotime($shift->end_time) - strtotime($shift->start_time)) / 3600, 2);
                $shift->actual_working_hour = $shift->total_working_hour - ($shift->leisure_start_time != '' ? round(abs(strtotime($shift->leisure_end_time) - strtotime($shift->leisure_start_time)) / 3600, 2) : 0);
                $shift->save();

                $this->lateInTimeCategoryTimeModel->whereNotIn('id', array_keys($this->lateInCategories))->where('shift_id', $this->editingId)->delete();

                foreach ($this->lateInCategories as $key => $value) {
                    if ($value['time'] && $value['condition']) {
                        $lateInCategoriesTimeData = [
                            'shift_id' => $this->editingId,
                            'late_in_category_id' => $key,
                            'late_time' => $value['time'],
                            'condition' => $value['condition'],
                        ];
                        $this->lateInTimeCategoryTimeModel->updateOrCreate(
                            ['shift_id' => $this->editingId, 'late_in_category_id' => $key],
                            $lateInCategoriesTimeData
                        );
                    }
                }
            } else {
                $this->total_working_hour = round(abs(strtotime($this->end_time) - strtotime($this->start_time)) / 3600, 2);
                $this->actual_working_hour = $this->total_working_hour - ($this->leisure_start_time != '' ? round(abs(strtotime($this->leisure_end_time) - strtotime($this->leisure_start_time)) / 3600, 2) : 0);
                $shift = $this->model->create($this->all());

                $lateInCategoriesTimeData = [];
                foreach ($this->lateInCategories as $key => $value) {
                    if ($value['time'] && $value['condition']) {
                        $lateInCategoriesTimeData[] = [
                            'shift_id' => $shift->id,
                            'late_in_category_id' => $key,
                            'late_time' => $value['time'],
                            'condition' => $value['condition'],
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                    }
                }

                $this->lateInTimeCategoryTimeModel->insert($lateInCategoriesTimeData);
                $this->editingId = $shift->id;
            }
            DB::commit();
            $this->notify('Shift saved successfully')->send();
            $this->dispatch('hide-model');
        } catch (Exception $e) {
            DB::rollBack();
            $message = "Unable to " . ($this->isEditing ? "update" : "add") . " shift";
            logError($message, $e);
            $this->notify($message)->type("error")->send();
        }
    }

    public function delete(int $id): void
    {
        $shift = $this->model->findOrFail($id);
        if ($shift->is_default) {
            $this->message = 'Default shift cannot be deleted';
            $this->notify($this->message)->type('info')->send();
        } else {
            $isShiftExists = EmployeeOrg::where('shift_id', $shift->id)->exists();
            if ($isShiftExists) {
                $this->notify('Cannot delete shift assigned for employees.')->type('error')->send();
                return;
            }

            $lastWeekDate = Carbon::now()->subDay(7)->format('Y-m-d');
            $dutyRosterExists = DutyRoster::where([
                ['shift_id', $shift->id],
                ['date_en', '>', $lastWeekDate]
            ])->exists();
            if ($dutyRosterExists) {
                $this->notify("Cannot delete shift assigned for employees on duty roster within last 7 days.")->duration(10)->type('error')->send();
                return;
            }

            $shift->delete();
            $this->message = 'Employee shift deleted successfully';
            $this->notify($this->message)->send();
        }
        unset($this->list);
    }
    public function mount()
    {
        $this->scopeWiseFilters();
    }

    public function scopeWiseFilters()
    {
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            if ($currentEmployee) {
                $this->company_id = $currentEmployee?->company_id;
            }
        }
    }

    #[On('hide.bs.modal')]
    public function resetModal()
    {
        $this->resetErrorBag();
        $this->reset($this->model->getFillable());
        $this->reset(['editingId', 'isEditing', 'title', 'lateInCategories']);
    }

    public function updated($attrs)
    {
        if ($attrs == 'filter_company_id') {
            unset($this->list);
        }
    }

    public function fillFormModel($id)
    {
        $row = $this->model->findOrFail($id);

        foreach ($this->model->getFillable() as $fillable) {
            $this->{$fillable} = $row->{$fillable};
        }
        $this->title = $row->title;

        $lateInCategoriesTimeRow = $this->lateInTimeCategoryTimeModel->where("shift_id", $id)->get();
        foreach ($lateInCategoriesTimeRow as $dataRow) {
            $this->lateInCategories[$dataRow->late_in_category_id] = [
                "id" => $dataRow->id,
                "time" => $dataRow->late_time,
                "condition" => $dataRow->condition
            ];
        }
    }

    public function resetLateInCategoryField($id)
    {
        unset($this->lateInCategories[$id]);
    }

    #[Computed()]
    public function list()
    {
        $query = $this->model
            ->with('company')
            ->search($this->search)
            ->whereNull('deleted_at')
            ->when($this->filter_company_id, function ($query) {
                $query->where("company_id", $this->filter_company_id);
            });
        if (!scopeAll()) {
            $currentEmployee = currentEmployee();
            $orgInfo = $currentEmployee?->organizationInfo;
            if (scopeCompany() || scopeRegion() || scopeBranch() || scopeDepartment()) {
                $query->where("company_id", $currentEmployee->company_id);
            } else {
                $query->where("id", $orgInfo->shift_id);
            }
        }
        return $this->applySorting($query)->paginate($this->perPage);
    }

    #[Computed(persist: true)]
    public function companies()
    {
        if (scopeAll()) {
            return Company::orderBy('name', 'asc')->select("id", "name")->get();
        } else {
            return Company::orderBy('name', 'asc')->where('id', $this->company_id)->select("id", "name")->get();
        }
    }

    #[Computed()]
    public function lateInCategoriesList()
    {
        return LateInCategory::where('is_active', true)->get();
    }

    #[Layout('layouts.app')]
    public function render()
    {
        return view('livewire.config.shift.shift');
        ;
    }
}
