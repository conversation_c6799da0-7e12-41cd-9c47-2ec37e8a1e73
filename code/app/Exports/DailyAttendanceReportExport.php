<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class DailyAttendanceReportExport implements FromView
{
    public $data;
    public $title;

    public function __construct($data, $title)
    {
        $this->data = $data;
        $this->title = $title;
    }

    public function view(): View
    {
        return view('exports.daily-attendance-report', [
            'data' => $this->data,
            'title' => $this->title,
        ]);
    }
}
