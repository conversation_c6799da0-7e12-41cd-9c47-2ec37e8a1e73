<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AdditionalDeductionExcelSample implements FromView
{
    public $headers;
    public $values;

    public function __construct($header, $values)
    {
        $this->headers = $header;
        $this->values = $values;
    }
    public function view(): View
    {
        return view('exports.excel-sample', [
            'headers'   => $this->headers,
            'values'    => $this->values,
        ]);
    }
}
