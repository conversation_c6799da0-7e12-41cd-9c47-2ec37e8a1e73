<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OtReportExport implements FromView, ShouldAutoSize
{
    public $title;
    public $heading;
    public $data;

    public function __construct($title, $heading, $data)
    {
        $this->title = $title;
        $this->heading = $heading;
        $this->data = $data;
    }

    public function view(): View
    {
        return view('exports.ot-report-export', [
            'title' => $this->title,
            'heading' => $this->heading,
            'data' => $this->data,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Make all cells centered
            'A1:Z1000' => [
                'alignment' => [
                    'horizontal' => 'center',
                    'vertical' => 'center'
                ]
            ],

            // Header row styling
            '1:2' => [
                'font' => [
                    'bold' => true
                ]
            ],

            // Auto size columns
            'A:Z' => [
                'autoSize' => true
            ]
        ];
    }
}
