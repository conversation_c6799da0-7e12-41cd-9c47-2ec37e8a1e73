<?php

namespace App\Exports;

use App\Models\Payroll\AttendanceCount;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AttendanceForSalaryExport implements FromView
{
    public $attendanceData = [], $monthDate = [], $headings;

    public function __construct($attendances, $monthDate, $year, $month)
    {
        $log_hours = AttendanceCount::where([['nep_year', $year], ['nep_month', $month]])
            ->select('employee_id', 'log_hour')
            ->get()
            ->groupBy('employee_id')->map(function ($group) {
                return $group->first();
            })
            ->toArray();

        $this->monthDate = $monthDate;
        $this->initialize();

        $data = [];
        $monthDays = count($this->monthDate ?? []);
        foreach ($attendances as $attendance) {
            $data[] = [
                'employee_name' => $attendance->employee,
                'employee_code' => $attendance->code,
                'vendor' => $attendance->vendor ?? $attendance->company,
                'branch' => $attendance->branch,
                'department' => $attendance->department,
                'total_days' => $monthDays,
                'leave_days' => $attendance->leave_days,
                'present_days' => $attendance->present_days,
                'absent_days' => $attendance->absent_days,
                'day_off' => $attendance->day_off,
                'salary_days' => $attendance->salary_days,
                'log_hours' => $log_hours[$attendance->employee_id]['log_hour'] ?? 0,
                ...array_merge(...array_map(function ($date, $data) {
                    return [$date => ['status_short' => $data['status_short'], 'style' => $this->getTooltipInfo($data['status'])]];
                }, array_keys($attendance->meta->daily_attendance ?? $attendance->daily_attendance ?? []), $attendance->meta->daily_attendance ?? $attendance->daily_attendance ?? []))
            ];
        }

        $this->attendanceData = $data;
    }

    public function getTooltipInfo($status)
    {
        if ($status == "") return "";

        $style = "";
        switch ($status) {
            case (strpos($status, 'Present') !== false):
                $style = "background-color: #9966D960;";
                break;
            case 'Absent':
                $style = "background-color: #99D96060;";
                break;
            case (strpos($status, 'Missed Punch') !== false):
                $style = "background-color: #66b9b610;";
                break;
            case 'Day Off':
                $style = "background-color: #6600a6ff;";
                break;
            case (strpos($status, 'Work On Day Off') !== false):
                $style = "background-color: #66ff9800;";
                break;
            case (strpos($status, 'Work On Holiday') !== false):
                $style = "background-color: #66ff0080;";
                break;
            case (strpos($status, 'On Holiday') !== false):
                $style = "background-color: #668000ff;";
                break;
            case ($status == ""):
                $style = "";
        }
        return $style;
    }

    public function initialize()
    {
        $this->headings = [
            'employee_name' => 'Employee Name',
            'employee_code' => 'Employee Code',
            'vendor' => 'Vendor',
            'branch' => 'Branch',
            'department' => 'Department',
            'total_days' => 'Total Days',
            'leave_days' => 'Leave Days',
            'present_days' => 'Present Days',
            'absent_days' => 'Absent Days',
            'day_off' => 'Day Off',
            'salary_days' => 'Salary Days',
            'log_hours' => 'Log Hours',
            ...$this->monthDate
        ];
    }

    public function view(): View
    {
        return view('livewire.payroll.salary-structure.utils.attendance-for-salary', [
            'attendanceData' => $this->attendanceData,
            'headings' => $this->headings,
            'monthDate' => $this->monthDate,
        ]);
    }
}
