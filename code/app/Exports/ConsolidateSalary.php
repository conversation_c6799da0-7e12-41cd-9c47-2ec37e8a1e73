<?php

namespace App\Exports;

use App\Models\Payroll\AdditionalDeduction;
use App\Models\Payroll\AdditionalIncome;
use App\Models\Payroll\Payslip;
use App\Models\Payroll\Perk;
use Exception;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ConsolidateSalary implements FromArray, WithHeadings
{
    private $salaryData, $headings, $activePayslipList;
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct(array $salaryStructures)
    {
        $this->initialize();

        $salaryDataKeys = array_combine(array_keys($this->headings), array_fill(0, count($this->headings), ''));
        $salaryDataWithAllKeys = [];
        foreach ($salaryStructures as $salaryStructure) {
            try {
                $additional_incomes = [];
                foreach ($salaryStructure['additional_income'] as $additional_income) {
                    $additional_incomes[$additional_income['name']] = $additional_income['amount'];
                }

                $actual_additional_allowance = [];
                foreach ($salaryStructure['additional_allowance']['actual'] as $key => $value) {
                    $actual_additional_allowance['actual_' . $key] = $value;
                }

                $actual_incomes = [
                    'stipend' => $salaryStructure['stipend']['actual'] ?? 0,
                    'actual_basic_salary' => $salaryStructure['basic_salary']['actual'] ?? 0,
                    'actual_ssf' => $salaryStructure['ssf_contribution']['actual'] ?? 0,
                    'actual_allowance' => $salaryStructure['allowance']['actual'] ?? 0,
                    ...$actual_additional_allowance
                ];

                if (fedexHrm()) {
                    $actual_incomes = [
                        'stipend' => $salaryStructure['stipend']['actual'] ?? 0,
                        'actual_basic_salary' => ($salaryStructure['basic_salary']['actual'] ?? 0) - ($salaryStructure['added_basic_salary'] ?? 0),
                        'added_basic_salary' => $salaryStructure['added_basic_salary'] ?? 0,
                        'total_basic_salary' => $salaryStructure['basic_salary']['actual'] ?? 0,
                        'actual_ssf' => $salaryStructure['ssf_contribution']['actual'] ?? 0,
                        'actual_allowance' => $salaryStructure['allowance']['actual'] ?? 0,
                        ...$actual_additional_allowance
                    ];
                }

                $total_actual_gross_income_amount = 0;
                foreach ($actual_incomes as $value) {
                    $total_actual_gross_income_amount += (float)$value;
                }

                if (fedexHrm()) {
                    $total_actual_gross_income_amount -= $actual_incomes['total_basic_salary'] ?? 0;
                    $total_actual_gross_income_amount -= $salaryStructure['non_cash_deduction'] ?? 0;
                }

                $gross_incomes = [
                    'basic_salary' => $salaryStructure['basic_salary']['earned'] ?? 0,
                    'stipend' => $salaryStructure['stipend']['earned'] ?? 0,
                    'allowance' => $salaryStructure['allowance']['earned'] ?? 0,
                    ...$salaryStructure['additional_allowance']['earned'],
                    'ssf_contribution' => $salaryStructure['ssf_contribution']['earned'] ?? 0,
                    ...$additional_incomes
                ];
                $total_gross_income_amount = 0;
                foreach ($gross_incomes as $value) {
                    $total_gross_income_amount += (float)$value;
                }

                $additional_deductions = [];
                foreach ($salaryStructure['additional_deduction'] as $additional_deduction) {
                    $additional_deductions[$additional_deduction['name']] = $additional_deduction['amount'];
                }

                $non_cash_deduction_actual = [];
                $non_cash_deduction = [];
                if (fedexHrm()) {
                    $non_cash_deduction_actual = [
                        'non_cash_deduction_actual' => $salaryStructure['non_cash_deduction'] ?? 0,
                    ];
                    $non_cash_deduction = [
                        'non_cash_deduction' => $salaryStructure['non_cash_deduction'] ?? 0,
                    ];
                }

                $total_deduction = [
                    'advance_salary_deduct' => $salaryStructure['advance_salary_deduct'] ?? 0,
                    'emergency_fund' => $salaryStructure['emergency_fund'] ?? 0,
                    'cit' =>  $salaryStructure['cit']['earned'] ?? 0,
                    'ssf_deduction' => $salaryStructure['ssf_deduction']['earned'] ?? 0,
                    'sst' => $salaryStructure['paid_sst'] ?? 0,
                    'tds' => ($salaryStructure['paid_tds'] ?? 0),
                    ...$additional_deductions,
                    ...$non_cash_deduction
                ];

                $total_deduction_amount = 0;
                foreach ($total_deduction as $value) {
                    $total_deduction_amount += (float)$value;
                }

                $name = $salaryStructure['employee']['first_name'] . ' ' . ($salaryStructure['employee']['middle_name'] ? ($salaryStructure['employee']['middle_name'] . ' ') : '') . $salaryStructure['employee']['last_name'];
                $employee_code = $salaryStructure['employee']['company']['code'] . '-'  . $salaryStructure['employee']['organization_info']['employee_code'];

                $salaryData = [
                    'name' => $name,
                    'employee_code' => $employee_code,
                    'gender' => $salaryStructure['employee']['gender'],
                    'mstat' => $salaryStructure['employee']['mstat'],
                    'branch' =>  $salaryStructure['employee']['branch']['name'] ?? "",
                    'department' => $salaryStructure['employee']['department']['name'] ?? "",
                    'employee_role' => $salaryStructure['job']['name'] ?? "",
                    'vendor' => $salaryStructure['employee']['outsource_company']['name'] ?? $salaryStructure['employee']['company']['name'] ?? "",
                    'designation' => (isset($salaryStructure['employee']['designation']['title']) ? $salaryStructure['employee']['designation']['title'] : ($this->activePayslipList[$salaryStructure['employee_id']] ?? '')),
                    'band_level' => (isset($salaryStructure['band']['name'])
                        ? $salaryStructure['band']['name']
                        : '') . (isset($salaryStructure['level']['name']) ? $salaryStructure['level']['name'] : ''),
                    'doj' => $salaryStructure['employee']['organization_info']['doj'] ?? '',
                    'bank_account_number' => $salaryStructure['employee']['organization_info']['bank_account_no'] ?? '',
                    'bank_name' => $salaryStructure['employee']['organization_info']['bank'] ?? '',
                    'pan_no' => $salaryStructure['employee']['organization_info']['pan_no'] ?? '',
                    ...$actual_incomes,
                    ...$non_cash_deduction_actual,
                    'actual_ctc' => $total_actual_gross_income_amount,
                    'salary_days' => $salaryStructure['total_salary_days'] ?? '',
                    ...$gross_incomes,
                    'gross_income' => $total_gross_income_amount ?? '',
                    ...$total_deduction,
                    'total_deduction' => $total_deduction_amount ?? '',
                    'net_salary' => ($total_gross_income_amount-$total_deduction_amount) ?? '',
                ];

                $salaryDataWithAllKeys[] = [...$salaryDataKeys, ...$salaryData];
            } catch (Exception $e) {
                logError("Error in data: ", $e);
                logError("Data: " . print_r($salaryStructure, true));
            }
        }
        $this->salaryData = $salaryDataWithAllKeys;
    }

    public function initialize()
    {
        $perk_heading = Perk::whereNull('deleted_at')->pluck('name')->mapWithKeys(function ($name) {
            return [$name => $name];
        })->toArray();
        $actual_perk_heading = Perk::whereNull('deleted_at')->pluck('name')->mapWithKeys(function ($name) {
            return ['actual_' . $name => $name];
        })->toArray();
        $additional_income_heading = AdditionalIncome::pluck('name')->mapWithKeys(function ($name) {
            return [$name => $name];
        })->toArray();
        $additional_deduction_heading = AdditionalDeduction::pluck('name')->mapWithKeys(function ($name) {
            return [$name => $name];
        })->toArray();
        $this->activePayslipList = Payslip::leftJoin('designations', 'designations.id', '=', 'payslips.designation_id')
            ->where('payslips.status', 'Active')
            ->pluck('designations.title', 'payslips.employee_id')
            ->toArray();

        $basic_salary = [];
        if (fedexHrm()) {
            $basic_salary = [
                'added_basic_salary' => 'Added Basic Salary',
                'total_basic_salary' => 'Total Basic Salary',
            ];
        }

        $non_cash_deduction_actual = [];
        $non_cash_deduction = [];
        if (fedexHrm()) {
            $non_cash_deduction_actual = [
                'non_cash_deduction_actual' => 'Non Cash Deduction',
            ];
            $non_cash_deduction = [
                'non_cash_deduction' => 'Non Cash Deduction',
            ];
        }

        $this->headings = [
            'name' => 'Name',
            'employee_code' => 'Employee Code',
            'gender' => 'Gender',
            'mstat' => 'Married Status',
            'branch' => 'Branch',
            'department' => 'Department',
            'employee_role' => 'Employee Role',
            'vendor' => 'Vendor',
            'designation' => 'Designation',
            'band_level' => 'Band/Level',
            'doj' => 'Date of Join',
            'bank_account_number' => 'Bank Account Number',
            'bank_name' => 'Bank Name',
            'pan_no' => 'PAN Number',
            'actual_stipend' => 'Stipend',
            'actual_basic_salary' => 'Basic Salary',
            ...$basic_salary,
            'actual_ssf' => 'SSF 20%',
            'actual_allowance' => 'Allowance',
            ...$actual_perk_heading,
            ...$non_cash_deduction_actual,
            'actual_ctc' => 'Total CTC',
            'salary_days' => 'Salary Days',
            'basic_salary' => 'Basic Salary',
            'stipend' => 'Stipend',
            'allowance' => 'Allowance',
            ...$perk_heading,
            'ssf_contribution' => 'SSF Contribution',
            ...$additional_income_heading,
            'gross_income' => 'Gross Income',
            'advance_salary_deduct' => 'Salary Advance',
        ];

        if (vianetHrm() || konnectHrm()) {
            $this->headings = [
                ...$this->headings,
                'emergency_fund' => 'Emergency Fund',
            ];
        }

        $this->headings = [
            ...$this->headings,
            'cit' =>  'CIT',
            'ssf_deduction' => 'SSF Deduction',
            'sst' => 'SST',
            'tds' => 'TDS',
            ...$additional_deduction_heading,
            ...$non_cash_deduction,
            'total_deduction' => 'Total Deduction',
            'net_salary' => 'Net Salary',
        ];
    }

    public function array(): array
    {
        $salaryDataValues = array_values($this->salaryData);
        return $salaryDataValues;
    }

    public function headings(): array
    {
        // Optionally define your headers here
        // For example:
        // return ['Column 1', 'Column 2', 'Column 3', ..., 'Column 40'];
        $headingValues = array_values($this->headings);
        return $headingValues;
    }
}
