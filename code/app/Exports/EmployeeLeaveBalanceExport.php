<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmployeeLeaveBalanceExport implements FromView, WithStyles, WithEvents
{
    public $data;
    public $leaveTypes;

    public function __construct($data, $leaveTypes)
    {
        $this->data = $data;
        $this->leaveTypes = $leaveTypes;
    }

    public function view(): View
    {
        return view('exports.employee-leave-balance', [
            'data' => $this->data,
            'leaveTypes' => $this->leaveTypes,
        ]);
    }

    public function styles(Worksheet $sheet)
    {
        $totalColumns = 3 + (count($this->leaveTypes) * 4);
        $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($totalColumns);
        $lastRow = count($this->data) + 2;

        $range = "A1:{$columnLetter}{$lastRow}";

        return [
            $range => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
                'alignment' => [
                    'vertical' => 'center',
                    'horizontal' => 'center',
                ],
            ],
            'A1' => ['font' => ['bold' => true]],
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                // Freeze top 2 rows and first 3 columns
                $event->sheet->getDelegate()->freezePane('D3'); // Column D (4th), Row 3 (below headers)
            },
        ];
    }
}
