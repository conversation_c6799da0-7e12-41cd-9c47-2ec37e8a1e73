<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class SalaryAdjustmentExcelSample implements FromView
{
    public $title;
    public $headers;
    public $values;

    public function __construct(string $title, array $headers, array $values)
    {
        $this->title = $title;
        $this->headers = $headers;
        $this->values = $values;
    }

    public function view(): View
    {
        return view('exports.salary-adjustment-excel-sample', [
            'heading'   => $this->headers,
            'values'    => $this->values,
        ]);
    }
}
