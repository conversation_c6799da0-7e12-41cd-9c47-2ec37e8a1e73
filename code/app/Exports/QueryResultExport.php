<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithCustomCsvSettings;

class QueryResultExport implements FromArray, WithHeadings, WithCustomCsvSettings
{
    /** @var array<array<string,mixed>> */
    protected array $rows;

    /** @var string[] */
    protected array $headings;

    /**
     * @param array<int,\stdClass|array> $result
     */
    public function __construct(array $result)
    {
        // Normalize stdClass rows to arrays
        $rows = array_map(fn ($r) => (array) $r, $result);

        $this->headings = !empty($rows) ? array_keys($rows[0]) : [];
        $this->rows = array_map(function ($row) {
            return array_map(function ($v) {
                if ($v instanceof \DateTimeInterface) return $v->format('Y-m-d H:i:s');
                if (is_bool($v)) return $v ? '1' : '0';
                if (is_array($v) || is_object($v)) return json_encode($v, JSON_UNESCAPED_UNICODE);
                return $v;
            }, $row);
        }, $rows);
    }

    public function array(): array
    {
        return $this->rows;
    }

    public function headings(): array
    {
        return $this->headings;
    }

    public function getCsvSettings(): array
    {
        return [
            'delimiter'        => ',',
            'enclosure'        => '"',
            'line_ending'      => PHP_EOL,
            'use_bom'          => false,
            'include_separator_line' => false,
            'excel_compatibility'    => false,
        ];
    }
}
