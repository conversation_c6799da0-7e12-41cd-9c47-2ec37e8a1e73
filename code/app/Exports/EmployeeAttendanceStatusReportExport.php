<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Contracts\View\View;

class EmployeeAttendanceStatusReportExport implements FromView
{
    public $data;
    public $title;

    public function __construct($data, $title)
    {
        $this->data = $data;
        $this->title = $title;
    }

    public function view():View
    {
        return view('exports.employee-attendance-status-report',[
            'data' => $this->data,
            'title' => $this->title
        ]);
        
    }
}
