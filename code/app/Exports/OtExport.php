<?php

namespace App\Exports;

use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OtExport implements FromCollection, WithHeadings, WithTitle, ShouldAutoSize, WithCustomStartCell, WithEvents
{
    protected $title;
    protected $heading;
    protected $data;

    public function __construct($title, $heading, $data)
    {
        $this->title = $title;
        $this->heading = $heading;
        $this->data = $data;
    }

    // Return data as collection
    public function collection()
    {
        return collect($this->data);
    }

    // Return column headings
    public function headings(): array
    {
        return $this->heading;
    }

    // Title for the Excel sheet/tab name
    public function title(): string
    {
        return $this->title;
    }

    // Start from cell A2 to leave space for title at A1
    public function startCell(): string
    {
        return 'A2';
    }

    // Apply styles like merging and bold formatting
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                $lastColumn = chr(ord('A') + count($this->heading) - 1); // e.g., 'P' for 16 columns
                $titleCellRange = "A1:{$lastColumn}1";

                // Merge A1 to last column and set the title
                $sheet->mergeCells($titleCellRange);
                $sheet->setCellValue('A1', $this->title);

                // Style the title
                $sheet->getStyle('A1')->applyFromArray([
                    'font' => ['bold' => true, 'size' => 14],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                // Style the headings row (row 2)
                $sheet->getStyle("A2:{$lastColumn}2")->applyFromArray([
                    'font' => ['bold' => true],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical'   => Alignment::VERTICAL_CENTER,
                    ],
                ]);
            },
        ];
    }
}
