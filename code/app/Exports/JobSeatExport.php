<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;


class JobSeatExport implements FromView
{
    public $title;
    public $heading;
    public $data;

    public function __construct($title, $heading, $data)
    {
        $this->title = $title;
        $this->heading = $heading;
        $this->data = $data;
    }

    public function view(): View
    {
        return view('exports.job-seat-export', [
            'title' => $this->title,
            'heading' => $this->heading,
            'data' => $this->data,
        ]);
    }
}
