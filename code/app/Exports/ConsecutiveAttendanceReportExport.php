<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ConsecutiveAttendanceReportExport implements FromView
{
    public $records;

    public function __construct($records)
    {
        $this->records = $records;
    }

    public function view(): View
    {
        return view('exports.consecutive-attendance-report-export', [
            'data' => $this->records,
        ]);
    }
}
