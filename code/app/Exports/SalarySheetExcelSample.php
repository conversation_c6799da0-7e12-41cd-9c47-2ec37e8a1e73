<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class SalarySheetExcelSample implements FromView
{
    public $headers;
    public $values;

    public function __construct(array $data)
    {
        $this->headers = array_keys($data);
        $this->values = array_values($data);
    }

    public function view(): View
    {
        return view('exports.payslip-excel-sample', [
            'headers'   => $this->headers,
            'values'    => $this->values,
        ]);
    }
}
