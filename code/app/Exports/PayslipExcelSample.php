<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PayslipExcelSample implements FromView, ShouldAutoSize
{
    public $headers; // ['2020-02-02' => 'Sunday']
    public $values;
    public $requiredHeaders = [];

    public function __construct(array $data, array $requiredHeaders = [])
    {
        $this->headers = array_keys($data);
        $this->values = array_values($data);
        $this->requiredHeaders = array_values($requiredHeaders);
    }
    public function view(): View
    {
        return view('exports.payslip-excel-sample', [
            'headers'   => $this->headers,
            'values'    => $this->values,
            'requiredHeaders' => $this->requiredHeaders
        ]);
    }
}
