<?php

namespace App\Exports;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Invoice;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class DutyRosterExport implements FromView
{
    public $dates; // ['2020-02-02' => 'Sunday']
    public $dutyRosterData;
    public $employees;
    public $title;

    public function __construct(array $dates, array $employees, array $data, string $title = "")
    {
        $this->dates = $dates;
        $this->employees = $employees;
        $this->dutyRosterData = $data;
        $this->title = $title;
    }
    public function view(): View
    {
        $headers = [];
        foreach ($this->dates as $date => $day)
            $headers[LaravelNepaliDate::from($date)->toNepaliDate('Y-m-d')] = $day;
        return view('exports.duty-roster', [
            'headers' => $headers,
            'dates' => $this->dates,
            'dutyRosterData' => $this->dutyRosterData,
            'employees' => $this->employees,
            'title' => $this->title,
        ]);
    }
}
