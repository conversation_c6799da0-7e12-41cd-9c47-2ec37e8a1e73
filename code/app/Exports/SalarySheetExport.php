<?php

namespace App\Exports;

use App\Models\Payroll\AdditionalIncome;
use App\Models\Payroll\Perk;
use Exception;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class SalarySheetExport implements FromView
{
    public $salaryData, $headings;

    public function __construct(array $salaryStructures)
    {
        $this->initialize();

        $salaryDataKeys = array_combine(array_keys($this->headings), array_fill(0, count($this->headings), ''));
        $salaryDataWithAllKeys = [];
        foreach ($salaryStructures as $salaryStructure) {
            try {
                $additional_incomes = [];
                foreach ($salaryStructure['current_month_additional_income'] as $additional_income) {
                    $additional_incomes[$additional_income['name']] = $additional_income['amount'];
                }

                $actual_incomes = [
                    'stipend' => $salaryStructure['stipend']['actual'] ?? 0,
                    'actual_basic_salary' => $salaryStructure['basic_salary']['actual'] ?? 0,
                    'actual_ssf' => $salaryStructure['ssf_contribution']['actual'] ?? 0,
                    'actual_allowance' => $salaryStructure['allowance']['actual'] ?? 0,
                    ...$salaryStructure['additional_allowance']['actual']
                ];
                $total_actual_gross_income_amount = 0;
                foreach ($actual_incomes as $value) {
                    $total_actual_gross_income_amount += (float)$value;
                }

                $gross_incomes = [
                    'stipend' => $salaryStructure['stipend']['current'] ?? 0,
                    'basic_salary' => $salaryStructure['basic_salary']['current'] ?? 0,
                    'ssf_contribution' => $salaryStructure['ssf_contribution']['current'] ?? 0,
                    'allowance' => $salaryStructure['allowance']['current'] ?? 0,
                    // 'dashain_allowance' => '',
                    // 'hour_based' => '',
                    // 'overtime' => '',
                    ...$salaryStructure['additional_allowance']['current'],
                    // 'leave_encashment' => "",
                    ...$additional_incomes
                ];
                $total_gross_income_amount = 0;
                foreach ($gross_incomes as $value) {
                    $total_gross_income_amount += (float)$value;
                }

                $total_deduction = [
                    'advance_salary_deduct' => "",
                    'emergency_fund' => $salaryStructure['emergency_fund'] ?? 0,
                    'cit' =>  $salaryStructure['cit']['current'] ?? 0,
                    'ssf_deduction' => $salaryStructure['ssf_deduction']['current'] ?? 0,
                    'sst' => "",
                    'tds' => $salaryStructure['tax'] ?? 0,
                ];
                $total_deduction_amount = 0;
                foreach ($total_deduction as $value) {
                    $total_deduction_amount += (float)$value;
                }

                $name = $salaryStructure['employee']['first_name'] . ' ' . ($salaryStructure['employee']['middle_name'] ? ($salaryStructure['employee']['middle_name'] . ' ') : '') . $salaryStructure['employee']['last_name'];
                $employee_code = $salaryStructure['employee']['company']['code'] . '-'  . $salaryStructure['employee']['organization_info']['employee_code'];

                $salaryData = [
                    'name' => $name,
                    'employee_code' => $employee_code,
                    'gender' => $salaryStructure['employee']['gender'],
                    'mstat' => $salaryStructure['employee']['mstat'],
                    'branch' =>  $salaryStructure['employee']['branch']['name'] ?? "",
                    'department' => $salaryStructure['employee']['department']['name'] ?? "",
                    'employee_role' => $salaryStructure['job']['name'] ?? "",
                    'vendor' => $salaryStructure['employee']['outsource_company']['name'] ?? $salaryStructure['employee']['company']['name'] ?? "",
                    'band_level' => (isset($salaryStructure['band']['name'])
                        ? $salaryStructure['band']['name']
                        : '') . (isset($salaryStructure['level']['name']) ? $salaryStructure['level']['name'] : ''),
                    'doj' => $salaryStructure['employee']['organization_info']['doj'] ?? '',
                    'bank_account_number' => $salaryStructure['employee']['organization_info']['bank_account_no'] ?? '',
                    'bank_name' => $salaryStructure['employee']['organization_info']['bank'] ?? '',
                    ...$actual_incomes,
                    'actual_ctc' => $total_actual_gross_income_amount,
                    'salary_days' => $salaryStructure['total_salary_days'] ?? '',
                    ...$gross_incomes,
                    'gross_income' => $total_gross_income_amount ?? '',
                    ...$total_deduction,
                    'total_deduction' => $total_deduction_amount ?? '',
                    'net_salary' => $salaryStructure['cash_in_hand'] ?? '',
                ];

                $salaryDataWithAllKeys[] = [...$salaryDataKeys, ...$salaryData];
            } catch (Exception $e) {
                logError("Error in data: ", $e);
                logError("Data: " . print_r($salaryStructure, true));
            }
        }
        $this->salaryData = $salaryDataWithAllKeys;
    }

    public function initialize()
    {
        $perk_heading = Perk::whereNull('deleted_at')->pluck('name')->mapWithKeys(function ($name) {
            return [$name => $name];
        })->toArray();
        $actual_perk_heading = Perk::whereNull('deleted_at')->pluck('name')->mapWithKeys(function ($name) {
            return ['actual_' . $name => $name];
        })->toArray();
        $additional_income_heading = AdditionalIncome::pluck('name')->mapWithKeys(function ($name) {
            return [$name => $name];
        })->toArray();

        $this->headings = [
            'name' => 'Name',
            'employee_code' => 'Employee Code',
            'gender' => 'Gender',
            'mstat' => 'Married Status',
            'branch' => 'Branch',
            'department' => 'Department',
            'employee_role' => 'Employee Role',
            'vendor' => 'Vendor',
            'band_level' => 'Band/Level',
            'doj' => 'Date of Join',
            'bank_account_number' => 'Bank Account Number',
            'bank_name' => 'Bank Name',
            'pan_no' => 'PAN Number',
            'actual_stipend' => 'Stipend',
            'actual_basic_salary' => 'Basic Salary',
            'actual_ssf' => 'SSF 20%',
            'actual_allowance' => 'Allowance',
            ...$actual_perk_heading,
            'actual_ctc' => 'Total CTC',
            'salary_days' => 'Salary Days',
            'stipend' => 'Stipend',
            'basic_salary' => 'Basic Salary',
            'ssf_contribution' => 'SSF Contribution',
            'allowance' => 'Allowance',
            // 'dashain_allowance' => 'Dashain Allowance',
            // 'hour_based' => 'Hour Based',
            // 'overtime' => 'Overtime',
            ...$perk_heading,
            // 'leave_encashment' => 'Leave Encashment',
            ...$additional_income_heading,
            'gross_income' => 'Gross Income',
            'advance_salary_deduct' => 'Salary Advance',
            'emergency_fund' => 'Emergency Fund',
            'cit' =>  'CIT',
            'ssf_deduction' => 'SSF Deduction',
            'sst' => 'SST',
            'tds' => 'TDS',
            'total_deduction' => 'Total Deduction',
            'net_salary' => 'Net Salary',
        ];
    }

    public function view(): View
    {
        return view('livewire.payroll.salary-structure.utils.salary-sheet', [
            'salaryData' => $this->salaryData,
            'headings' => $this->headings,
        ]);
    }
}
