<?php

namespace App\Exports;

use App\Facades\LaravelNepaliDate;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromView;

class YearlyAttendanceReport implements FromView
{
    public array $leaveDetailHeaders;
    public Collection $data;
    public array $monthList;
    public string $title;

    public function __construct(Collection $data, array $leaveDetailHeaders, string $title)
    {
        $this->data = $data;
        $this->leaveDetailHeaders = $leaveDetailHeaders;
        $this->monthList = (new LaravelNepaliDate)->nep_months();
        $this->title = $title;
    }

    public function view(): View
    {
        return view(
            'exports.yearly-attendance-report',
            [
                'data' => $this->data,
                'leaveDetailHeaders' => $this->leaveDetailHeaders,
                'title' => $this->title,
                'monthList' => $this->monthList,
            ]
        );
    }
}
