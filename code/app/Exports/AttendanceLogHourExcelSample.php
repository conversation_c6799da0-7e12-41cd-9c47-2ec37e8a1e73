<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AttendanceLogHourExcelSample implements FromView
{
    public $headers;
    public $values;

    public function __construct($header, $values)
    {
        $this->headers = $header;
        $this->values = $values;
    }
    public function view(): View
    {
        return view('exports.payslip-excel-sample', [
            'headers'   => $this->headers,
            'values'    => $this->values,
        ]);
    }
}
