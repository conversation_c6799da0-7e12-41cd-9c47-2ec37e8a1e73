<?php

namespace App\Models;

use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeFamilyInformation extends Model
{
    use HasFactory;
    protected $table = 'employee_family_information';
    
    protected $fillable =[
        'employee_id',
        'relation_type',
        'name',
        'dob_eng',
        'remarks'
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }
    public function documents()
    {
        return $this->hasMany(EmployeeFamilyDocument::class, 'family_id');
    }
}
