<?php

namespace App\Models;

use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FieldVisit extends Model
{
    use HasFactory;

    protected $table = 'field_visits';

    protected $fillable = [
        'employee_id',
        'name',
        'location',
        'type',
        'purpose',
        'message',
        'description',
        'check_in_lat',
        'check_in_lon',
        'check_out_lat',
        'check_out_lon',
        'form_meta',
        'check_in_time',
        'check_out_time',
        'check_in_date_en',
        'check_in_date_np',
        'check_out_date_en',
        'check_out_date_np',
        'check_in_ip',
        'check_out_ip',
    ];

    protected $casts =
    [
        'form_meta' => 'array',
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }
}
