<?php

namespace App\Models\Attendance;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\EducationLevel;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class IrregularityTicket extends Model implements StateableModelContract, ArflowModelInterface
{
    use SoftDeletes, HasStateExtended, ArflowModel;

    protected $fillable = [
        'employee_id',
        'type',
        'date_en',
        'date_np',
        'remarks',
    ];

    public static function create(array $attributes = [])
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::IRREGULARITY_TICKET);
        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::IRREGULARITY_TICKET];
    }
}
