<?php

namespace App\Models\configs;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class RegionBranch extends Model
{
    use LogsActivity;
    protected $table = "region_branch";

    protected $fillable = [
        'region_id', 'branch_id'
    ];

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.region_branch" : "region_branch";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
