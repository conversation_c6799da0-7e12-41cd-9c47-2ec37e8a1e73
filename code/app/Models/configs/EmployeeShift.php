<?php

namespace App\Models\configs;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EmployeeShift extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table="shifts";

    protected $fillable =[
        'name', 'type',
        'start_time', 'end_time', 
        'leisure_start_time', 'leisure_end_time',
        'total_working_hour', 'actual_working_hour',
        'grace_time',
        'day_off', 'is_default','is_active',
        'company_id'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getTitleAttribute()
    {
        return $this->name.' ('.$this->getFormattedStartTimeAttribute().' - '.$this->getFormattedEndTimeAttribute().')';
    }

    public function getOffdayAttribute(){
        return  date('l', strtotime("Sunday +{$this->day_off} days"));
    }

    public function getStatusAttribute(){
        return $this->is_active?'Active':'Disabled';
    }

    // This function has been added  to display partime when halfday value is found in type.
    public function getTypeAttribute($value)
    {
        return $value == 'halfday' ? 'parttime' : $value;
    }

    public function scopeSearch($query, $value)
    {
        $query->where('name','like',"%{$value}%");
    }

    public function getFormattedStartTimeAttribute()
    {
        return Carbon::createFromFormat('H:i:s', $this->attributes['start_time'])->format('g:i a');
    }

    public function getFormattedEndTimeAttribute()
    {
        return Carbon::createFromFormat('H:i:s', $this->attributes['end_time'])->format('g:i a');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.employee_shift" : "employee_shift";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
