<?php

namespace App\Models\configs;

use App\Models\configs\Holiday;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\SetOnlyOneActiveRecord;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class FiscalYear extends Model
{
    use HasFactory, SoftDeletes, SetOnlyOneActiveRecord, LogsActivity;

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($fiscalYear) {
            if ($fiscalYear->is_active) {
                static::where([['id', '!=', $fiscalYear->id], ['is_active', true]])->update(['is_active' => false]);
            } else {
                $result = static::where([['id', '!=', $fiscalYear->id], ['is_active', true]])->first();
                if (!$result) {
                    $fiscalYear->is_active = true;
                }
            }
        });
    }

    protected $table = "fiscal_years";

    protected $fillable = [
        'name',
        'start_date',
        'end_date',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");
    }

    public function scopeCurrent($query)
    {
        $query->where('is_active', 1);
    }

    public static function activeFiscalYearId()
    {
        return FiscalYear::where('is_active', 1)->first()?->id;
    }

    public static function getFiscalYearFromYearAndMonth(string $year, string $month)
    {
        $month = str_pad($month, 2, '0', STR_PAD_LEFT);
        $fiscalYear = FiscalYear::whereRaw("'$year-$month-01' BETWEEN start_date AND end_date")->first();
        return $fiscalYear;
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.fiscal_year" : "fiscal_year";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
