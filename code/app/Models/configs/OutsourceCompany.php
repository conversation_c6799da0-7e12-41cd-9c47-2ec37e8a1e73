<?php

namespace App\Models\configs;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OutsourceCompany extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'outsource_companies';

    protected $fillable = [
        'name',
        'address',
        'phone',
        'pan_number',
        'service_charge',
        'status',
        'company_id',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name','like',"%{$value}%");
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.outsource_company" : "outsource_company";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
