<?php

namespace App\Models\configs;

use App\Http\Helpers\Constant;
use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Branch extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table = 'branches';

    protected $fillable = [
        'name',
        'address',
        'latitude',
        'longitude',
        'phone',
        'branch_code',
        'branch_manager',
        'need_sub_branch',
        'allow_attendance',
        'company_id',
        'max_distance',
    ];

    protected $casts = [
        'latitude' => 'double',
        'longitude' => 'double',
        'need_sub_branch' => 'boolean',
        'allow_attendance' => 'boolean',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%")->with('branchManager:id,first_name,middle_name,last_name');
    }

    public function holidayBranches()
    {
        return $this->hasMany(HolidayBranch::class, 'branch_id');
    }

    public function holidays()
    {
        return $this->hasManyThrough(Holiday::class, HolidayBranch::class, 'branch_id', 'id', 'id', 'holiday_id');
    }

    public function assignedHoliday($fiscalYearId = "")
    {
        $fiscal_year_id = $fiscalYearId ? $fiscalYearId : session(Constant::SESSION_CURRENT_FISCAL_YEAR);
        $result = $this->hasMany(HolidayBranch::class, 'branch_id')
            ->whereHas('holiday', function ($query) use ($fiscal_year_id) {
                $query->where('fiscal_year', $fiscal_year_id);
            });

        return $result;
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }
    public function region()
    {
        return $this->belongsTo(Region::class, "region_id");
    }

    public function branchManager()
    {
        return $this->belongsTo(Employee::class, "branch_manager", "id");
    }

    public function regions()
    {
        return $this->hasManyThrough(Region::class, RegionBranch::class, 'branch_id', 'id', 'id', 'region_id');
    }

    public function subBranch()
    {
        return $this->hasMany(SubBranch::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.branch" : "branch";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
