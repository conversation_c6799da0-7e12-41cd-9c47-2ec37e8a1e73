<?php

namespace App\Models\configs;

use App\Models\User;
use App\Traits\ZKTDevice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class AttDevice extends Model
{
    use HasFactory, SoftDeletes, LogsActivity, ZKTDevice;

    protected $table = 'att_devices';

    protected $fillable = [
        'name', 'branch_id', 'device_ip', 'remarks', 'is_active', 'company_id'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public $pingStatus = [
        '0' => 'Not configured',
        '1' => 'Online',
        '2' => 'Offline'
    ];

    public function getPingStatusLabelAttribute(){
        return ($this->pingStatus[$this->last_ping_status]);
    }

    public function scopeSearch($query, $value)
    {
        $query->where('name','like',"%{$value}%")->orWhere('device_ip','like',"%{$value}%");
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.att_device" : "att_device";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
