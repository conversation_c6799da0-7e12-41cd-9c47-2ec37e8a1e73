<?php

namespace App\Models\configs;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class HolidayBranch extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'holiday_branches';

    protected $fillable = [
        'branch_id',
        'holiday_id',
    ];

    public function scopeSearch($query, $value)
    {
        $query->whereHas('holiday', function($q) use ($value){
            $q->where('name','like',"%{$value}%");
        });
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function holiday()
    {
        return $this->belongsTo(Holiday::class, 'holiday_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.holiday_branches" : "holiday_branches";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
