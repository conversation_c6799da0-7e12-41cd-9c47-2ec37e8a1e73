<?php

namespace App\Models\configs;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Holiday extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table = 'holidays';

    protected $fillable = [
        'name',
        'eng_date',
        'nep_date',
        'fiscal_year',
        'rate',
        'remarks',
        'gender',
        'company_id',
    ];

    protected $casts = [
        'rate' => 'float',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%")->with('fiscalYear:id,name');
    }

    public function holidayBranches()
    {
        return $this->hasMany(HolidayBranch::class, 'holiday_id');
    }

    public static function isHoliday($date) {
        return Holiday::where('eng_date', $date)->first();
    }

    public function branch()
    {
        return $this->through('holidayBranches')->has('branch');
    }

    public function fiscalYear(): BelongsTo
    {
        return $this->belongsTo(FiscalYear::class, 'fiscal_year', 'id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.holiday" : "holiday";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
