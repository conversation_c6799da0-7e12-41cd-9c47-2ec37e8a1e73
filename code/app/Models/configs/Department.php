<?php

namespace App\Models\configs;

use App\Models\Employee\Employee;
use App\Models\Payroll\Designation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Department extends Model
{
    use HasFactory, LogsActivity, SoftDeletes;

    protected $fillable = ["name", "remarks", "hod", "need_unit", "company_id", "abbreviation"];

    protected $casts = [
        'need_unit' => 'boolean',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.department" : "department";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }

    public function scopeSearch($query, $value) {
        $query->where("name", "like", '%'. $value . '%');
    }

    public function HOD() {
        return $this->belongsTo(Employee::class, 'hod', 'id');
    }

    public function Company() {
        return $this->belongsTo(Company::class);
    }

    public function units()
    {
        return $this->hasMany(Unit::class);
    }

    public function jobs()
    {
        return $this->belongsToMany(Job::class, "job_departments", "department_id", "job_id")
            ->withPivot('id', 'seat', 'occupancy');
    }

    public function designations()
    {
        return $this->belongsToMany(Designation::class, "designation_department", "department_id", "designation_id");
    }
}
