<?php

namespace App\Models\configs;

use App\Livewire\Config\Shift;
use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class DutyRoster extends Model
{
    use HasFactory,  LogsActivity;

    protected $fillable = [
        'shift_id',
        'employee_id',
        'date_en',
        'date_np',
        'shift_id',
        'day_off',
    ];

    protected $casts = [
        'day_off' => 'boolean'
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function shift()
    {
        return $this->belongsTo(EmployeeShift::class, 'shift_id', 'id')->withTrashed();
    }

    public function getOffdayAttribute()
    {
        return  date('l', strtotime("Sunday +{$this->day_off} days"));
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.duty_roster" : "duty_roster";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
