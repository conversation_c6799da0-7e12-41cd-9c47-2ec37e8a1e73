<?php

namespace App\Models\configs;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EmpStatus extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table = "emp_statuses";

    protected $fillable = ['name', 'remarks', 'is_active'];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name','like',"%{$value}%");
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.emp_status" : "emp_status";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
