<?php

namespace App\Models\configs;

use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class LeaveType extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;
    protected $table = "leave_types";

    protected $fillable = [
        'name',
        'default_days',
        //'max_carry_forward_days', 'cashable', 'substiute_mandatory',
        'carry_forward',
        'paid',
        'halfday',
        'monthly',
        'pro_rata_basis',
        'ignore_joining_date',
        'enable_substitute',
        'cashable',
        'assign_employee',
        'is_active',
        'expiry_day_count',
    ];

    protected $casts = [
        'paid' => 'boolean',
        'halfday' => 'boolean',
        'cashable' => 'boolean',
        'monthly' => 'boolean',
        'substitute_mandatory' => 'boolean',
        'pro_rata_basis' => 'boolean',
        'enable_substitute' => 'boolean',
        'assign_employee' => 'boolean',
        'is_active' => 'boolean',
        'ignore_joining_date' => 'boolean'
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");
    }

    public function employees()
    {
        return $this->belongsToMany(Employee::class, 'employee_leaves', 'leave_type_id', 'employee_id')
            ->withPivot(['assigned_leave', 'leave_taken', 'remaining_days', 'excess_leave'])
            ->withTimestamps();
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.leave_type" : "leave_type";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
