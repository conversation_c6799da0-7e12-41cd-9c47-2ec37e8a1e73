<?php

namespace App\Models\configs;

use Illuminate\Database\Eloquent\Model;
use App\Models\configs\Branch;
use App\Models\User;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Region extends Model
{
    use LogsActivity;

    protected $fillable = [
        'name', 'company_id',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where("name", "like", '%' . $value . '%');
    }

    public function branch()
    {
        return $this->belongsToMany(Branch::class, 'region_branch', "region_id", "branch_id");
    }

    public function branches()
    {
        return $this->hasMany(Branch::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.region" : "region";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;    }
}
