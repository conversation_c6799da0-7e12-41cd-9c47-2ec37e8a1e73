<?php

namespace App\Models\EmployeeTicket;

use App\Http\Helpers\Constant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeTicketProcess extends Model
{
    use HasFactory;

    protected $table = "employee_ticket_processes";

    protected $fillable = ['name', 'workflow', 'permission_id'];

    public function permission()
    {
        return $this->belongsTo(\App\Models\Admin\Permission::class, 'permission_id');
    }

    public function getPermissionNameAttribute()
    {
        return $this->permission?->name;
    }

    public static function getListForSelect()
    {
        $details = [];
        EmployeeTicketProcess::select('id', 'name', 'permission_id')->get()->each(function ($data) use (&$details) {
            if (auth()->user()->hasPermissionTo($data->permission_id)) {
                $details[$data->id] = $data->name;
            }
        });
        return $details;
    }

    public static function getAccessibleProcesses()
    {
        $ids = [];
        EmployeeTicketProcess::select('id', 'permission_id')->get()->each(function ($data) use (&$ids) {
            if (auth()->user()->hasPermissionTo($data->permission_id) || auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN)) {
                array_push($ids, $data->id);
            }
        });
        return $ids;
    }
}
