<?php

namespace App\Models\EmployeeTicket;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeTicketMeta extends Model
{
    use HasFactory;

    protected $table = "employee_ticket_metas";
    protected $fillable = ["employee_ticket_id", "meta_key", "meta_value"];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($metaData) {
            $value = $metaData->meta_value;
            if (is_array($value)) {
                $metaData->meta_value = \json_encode($value);
            } else {
                $metaData->meta_value = $value ?? "";
            }
        });
    }

    // Accessor to automatically decode JSON when getting meta_value
    public function getMetaValueAttribute($value)
    {
        // Check if the stored value is valid JSON
        if (\is_string($value)) {
            if (json_decode($value) !== null) {
                return json_decode($value, true); // return as array
            }
        }

        return $value; // return as string
    }
}
