<?php

namespace App\Models\EmployeeTicket;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\EmployeeShift;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DutyChangeTicket extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, ArflowModel;

    protected $fillable = [
        'date_en', 'date_np', 'employee_id', 'shift_id', 'existing_shift_id', 'reason'
    ];

    public function shift()
    {
        return $this->belongsTo(EmployeeShift::class, 'shift_id', 'id')->withTrashed();
    }

    public function existingShift()
    {
        return $this->belongsTo(EmployeeShift::class, 'existing_shift_id', 'id')->withTrashed();
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::DUTY_CHANGE_REQUEST];
    }
}
