<?php

namespace App\Models\EmployeeTicket;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeTicket extends Model implements StateableModelContract, ArflowModelInterface
{
  use HasFactory, HasStateExtended, ArflowModel;

  protected $fillable = ['employee_id', 'process_id'];

  public function process()
  {
    return $this->belongsTo(EmployeeTicketProcess::class, 'process_id');
  }

  public function metaData()
  {
    return $this->hasMany(EmployeeTicketMeta::class, 'employee_ticket_id');
  }

  public static function supportedWorkflows(): array
  {
    return [WorkflowName::EMPLOYEE_TRANSFER];
  }

  public function canApprove(): bool
  {
    return $this->canTransitionTo(WorkflowState::ENDORSED);
  }

  public function canReject(): bool
  {
    $canReject = $this->canTransitionTo(WorkflowState::REJECTED);
    if ($this->state === WorkflowState::ENDORSED) $canReject = false;
    return $canReject;
  }

  public function canRevert(): bool
  {
    $canRevert = $this->canTransitionTo(WorkflowState::REVERTED);
    $payslipChanged = $this->metaData->pluck('meta_value', 'meta_key')['payslip_change'] ?? false;
    if ($payslipChanged) $canRevert = false;
    return $canRevert;
  }
}
