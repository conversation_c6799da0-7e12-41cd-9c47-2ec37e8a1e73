<?php

namespace App\Models;

use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeFamilyDocument extends Model
{
    use HasFactory;
    protected $table = 'employee_family_documents';
    protected $casts = [
        'documents' => 'array',
    ];

    protected $fillable = [
        'family_id',
        'document_path',
        'type',
        'remarks',
        'status',
        'actioned_by',
    ];
    public function family()
    {
        return $this->belongsTo(EmployeeFamilyInformation::class, 'family_id');
    }

    public function getPath()
    {
        return $this->document_path;
    }
    public function actionedBy()
    {
        return $this->belongsTo(Employee::class, 'actioned_by');
    }
}
