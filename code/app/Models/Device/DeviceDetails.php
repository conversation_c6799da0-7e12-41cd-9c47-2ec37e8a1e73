<?php

namespace App\Models\Device;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class DeviceDetails extends Model
{
    use LogsActivity;
    protected $table = "device_details";

    protected $fillable = [
        "user_id",
        "agent",
        "device",
        "device_brand",
        "device_platform",
        "device_version",
        "device_model",
        "app_version",
        "app_version_code",
        "login_time",
        "status",
        "last_activity",
        "mpin",
        "app_login_code",
        "fcm_token"
    ];

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id', 'id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.device_details" : "device_details";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (\App\Models\User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
