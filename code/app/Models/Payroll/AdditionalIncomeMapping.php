<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdditionalIncomeMapping extends Model
{
    use HasFactory;

    protected $table = 'additional_income_mapping';

    public $timestamps = false;

    protected $fillable = [
        'uuid',
        'payslip_id',
        'employee_id',
        'additional_income_id',
        'amount',
        'date_nep',
        'date_eng'
    ];

    public function payslip()
    {
        return $this->belongsTo(Payslip::class, 'payslip_id', 'id');
    }

    public function additional_income()
    {
        return $this->belongsTo(AdditionalIncome::class, 'additional_income_id', 'id');
    }
}
