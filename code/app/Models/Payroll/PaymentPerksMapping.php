<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentPerksMapping extends Model
{
    use HasFactory;

    protected $table = 'payment_perks_mapping';

    protected $fillable = [
        'payment_uuid',
        'perks_id',
        'actual_amount',
        'earned_amount',
        'current_amount'
    ];

    public function perks()
    {
        return $this->belongsTo(Perk::class, 'perks_id', 'id');
    }
}
