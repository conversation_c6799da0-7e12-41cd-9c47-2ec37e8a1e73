<?php

namespace App\Models\Payroll;

use App\Http\Helpers\Constant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use PermissionList;

class Payslip extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ["deleted_at", "created_at", "updated_at"];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($payslip) {
            if ($payslip->status == "Active") {
                static::where([['employee_id', $payslip->employee_id], ['status', 'Active']])->update(['status' => "Expired"]);
            }
        });
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'approved_by', 'id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\Company::class, 'company_id', 'id');
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\Job::class, 'job_id', 'id');
    }

    public function designation(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\Designation::class, 'designation_id', 'id');
    }

    public function band(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeeBand::class, 'band_id', 'id');
    }

    public function level(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeePgrade::class, 'pgrade_id', 'id');
    }

    public function getCanViewLowLevelPayslipDetailAttribute()
    {
        $canView = false;
        $currentEmployeeDesignationLevel = Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', currentEmployee()->id ?? ''], ['status', 'Active']])
            ->value('designation.level');
        $modelEmployeeDesignationLevel = Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', $this->employee_id ?? ''], ['status', 'Active']])
            ->value('designation.level');

        if (($modelEmployeeDesignationLevel ?? 0 <= $currentEmployeeDesignationLevel ?? 0) || auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP)) {
            $canView = true;
        }

        if (auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN) || auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP)) {
            $canView = true;
        }

        return $canView;
    }

    public function banded_grade_structure(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\BandedGradeStructure::class, 'banded_grade_structure_id', 'id');
    }

    public function grade_structure(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\GradeStructure::class, 'grade_structure_id', 'id');
    }

    public function grade_structure_relation(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\GradeStructure::class, 'grade_structure_id', 'id');
    }

    // Override the `grade_structure` attribute accessor
    public function getGradeStructureAttribute()
    {
        if ($this->banded_grade_structure_id) {
            return $this->banded_grade_structure;
        }
        return $this->grade_structure_relation;
    }

    public function employee_status(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\EmpStatus::class, 'employee_status_id', 'id');
    }

    public function payslip_perks()
    {
        return $this->hasMany(PayslipPerksMapping::class, 'payslip_id', 'id');
    }

    public function additional_income_mapping()
    {
        return $this->hasMany(AdditionalIncomeMapping::class, 'employee_id', 'employee_id');
    }

    public function additional_deduction_mapping()
    {
        return $this->hasMany(AdditionalDeductionMapping::class, 'employee_id', 'employee_id');
    }

    public function getAllowanceDetailAttribute()
    {
        return json_decode($this->allowance_metadata, true) ?? [];
    }
}
