<?php

namespace App\Models;

use App\Models\configs\Department;
use App\Models\Payroll\Designation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DesignationDepartment extends Model
{
    use HasFactory;

    protected $table = 'designation_department';

    protected $fillable = [
        'designation_id',
        'department_id',
    ];

    public function designation()
    {
        return $this->belongsTo(Designation::class, 'designation_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }
}
