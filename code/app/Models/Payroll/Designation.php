<?php

namespace App\Models\Payroll;

use App\Models\configs\Department;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Designation extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        "title",
        "description",
        "level",
        "role"
    ];

    public function scopeSearch($query, $value)
    {
        $query->where("title", "LIKE", "%$value%");
    }

    public function departments()
    {
        return $this->belongsToMany(Department::class, 'designation_department', 'designation_id', 'department_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.designation" : "designation";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
