<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class GradePerks extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'perks_id',
        'grade_structure_id',
        'amount',
    ];

    public function perk()
    {
        return $this->belongsTo(Perk::class, 'perks_id', 'id');
    }
}
