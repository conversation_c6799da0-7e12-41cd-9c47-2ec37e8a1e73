<?php

namespace App\Models\Payroll;

use App\Models\configs\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class GradeStructure extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = [
        'grade_id',
        'band_id',
        'pgrade_id',
        'basic_salary',
        'pf',
        'ssf',
        'allowance',
        'gross_salary',
        'gratuity',
        'ctc',
    ];

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class, 'grade_id', 'id');
    }

    public function band()
    {
        return $this->belongsTo(EmployeeBand::class, 'band_id', 'id');
    }

    public function pgrade()
    {
        return $this->belongsTo(EmployeePgrade::class, 'pgrade_id', 'id');
    }

    public function perks()
    {
        return $this->belongsToMany(\App\Models\Payroll\Perk::class, 'grade_perks', 'grade_structure_id', 'perk_id')
            ->withPivot(['amount'])
            ->withTimestamps();
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.grade_structure" : "grade_structure";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
