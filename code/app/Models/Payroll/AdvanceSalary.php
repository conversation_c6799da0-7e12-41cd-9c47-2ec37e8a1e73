<?php

namespace App\Models\Payroll;

use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdvanceSalary extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'payslip_id',
        'nep_year',
        'nep_month',
        'amount',
        'is_settled',
        'nep_settlement',
        'type',
        'remarks',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_settled' => 'boolean',
    ];

    public function employee()
    {
        return $this->hasOne(Employee::class, 'id', 'employee_id')->withTrashed();
    }

    public function payslip()
    {
        return $this->hasOne(Payslip::class, 'id', 'payslip_id');
    }
}
