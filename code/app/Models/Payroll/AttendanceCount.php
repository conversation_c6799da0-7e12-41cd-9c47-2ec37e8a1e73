<?php

namespace App\Models\Payroll;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class AttendanceCount extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'nep_year',
        'nep_month',
        'employee_id',
        'attendance_days',
        'salary_days',
        'present_days',
        'absent_days',
        'day_off',
        'deduction_days',
        'leave_days',
        'total_working_hour',
        'log_hour',
        'is_uploaded',
        'is_locked',
        'meta'
    ];

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.attendance_count" : "attendance_count";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
