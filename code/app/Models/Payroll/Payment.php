<?php

namespace App\Models\Payroll;

use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $guarded = [
        'created_at',
        'updated_at',
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function employeeOrg()
    {
        return $this->belongsTo(EmployeeOrg::class, 'employee_id', 'employee_id');
    }

    public function payslip()
    {
        return $this->belongsTo(Payslip::class, 'payslip_id', 'id');
    }

    public function payment_perks()
    {
        return $this->hasMany(PaymentPerksMapping::class, 'payment_uuid', 'uuid');
    }

    public function payment_deduction()
    {
        return $this->hasMany(PaymentDeductionMapping::class, 'payment_uuid', 'uuid');
    }

    public function additionalIncomeMapping()
    {
        return $this->hasMany(AdditionalIncomeMapping::class, 'payslip_id', 'payslip_id');
    }

    public function additionalDeductionMapping()
    {
        return $this->hasMany(AdditionalDeductionMapping::class, 'payslip_id', 'payslip_id');
    }

}
