<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\ValidationException;

class PerkModel extends Model
{
    use HasFactory;

    protected $table = "perks_model";

    protected $fillable = [
        'model_type',
        'model_id',
        'perk_id',
        'amount',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($perkModel) {
            if (!$perkModel->isValidModelId()) {
                throw ValidationException::withMessages([
                    'model_id' => 'The specified model_id does not exist for the given model_type.',
                ]);
            }
        });
    }

    protected function isValidModelId()
    {
        $modelName = $this->model_type;
        $model = app()->make($modelName);

        return $model->where('id', $this->model_id)->exists();
    }

    public function model()
    {
        return $this->morphTo();
    }

    public function perk()
    {
        return $this->belongsTo(\App\Models\Payroll\Perk::class, 'perk_id', 'id');
    }
}
