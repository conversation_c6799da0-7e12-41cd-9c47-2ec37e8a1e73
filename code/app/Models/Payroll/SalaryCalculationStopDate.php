<?php

namespace App\Models\Payroll;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class SalaryCalculationStopDate extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'employee_id',
        'initiator_id',
        'nep_start_date',
        'nep_stop_date',
        'eng_start_date',
        'eng_stop_date',
        'is_active',
        'remarks',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function employee(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function initiator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'initiator_id', 'id')->withTrashed();
    }

    /**
     * Scope for getting active record of an employee
     */
    public function scopeActiveForEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId)->where('is_active', true);
    }

    /**
     * Ensure only one active record per employee
     */
    protected static function booted()
    {
        static::saving(function ($model) {
            if ($model->is_active) {
                static::where('employee_id', $model->employee_id)
                    ->where('id', '!=', $model->id)
                    ->update(['is_active' => false]);
            }
        });
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.salary_calculation_stop_date" : "salary_calculation_stop_date";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
