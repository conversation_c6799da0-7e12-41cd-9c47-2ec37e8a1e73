<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdditionalDeductionMapping extends Model
{
    use HasFactory;

    protected $table = 'additional_deduction_mappings';

    public $timestamps = false;

    protected $fillable = [
        'payslip_id',
        'additional_deduction_id',
        'amount',
        'date_nep',
        'date_eng'
    ];

    public function payslip()
    {
        return $this->belongsTo(Payslip::class, 'payslip_id', 'id');
    }

    public function additional_deduction()
    {
        return $this->belongsTo(AdditionalDeduction::class, 'additional_deduction_id', 'id');
    }
}
