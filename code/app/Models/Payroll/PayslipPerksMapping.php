<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayslipPerksMapping extends Model
{
    use HasFactory;

    protected $table = 'payslip_perks_mapping';

    protected $fillable = [
        'payslip_id',
        'perks_id',
        'amount'
    ];

    public function perks()
    {
        return $this->belongsTo(Perk::class, 'perks_id', 'id');
    }
}
