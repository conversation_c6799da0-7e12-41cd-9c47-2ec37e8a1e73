<?php

namespace App\Models\Payroll;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentDeductionMapping extends Model
{
    use HasFactory;

    protected $table = 'payment_deduction_mapping';

    protected $fillable = [
        'payment_uuid',
        'name',
        'actual_amount',
        'deducted_amount',
        'current_amount'
    ];
}
