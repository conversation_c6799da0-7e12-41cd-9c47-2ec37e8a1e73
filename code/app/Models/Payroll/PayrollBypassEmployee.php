<?php

namespace App\Models\Payroll;

use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PayrollBypassEmployee extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'payroll_bypass_employees';

    protected $fillable = [
        'employee_id',
        'bypass_type',
    ];

    public function bypassType()
    {
        return $this->belongsTo(PayrollBypassType::class, 'bypass_type', 'id');
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.payroll_bypass_employee" : "payroll_bypass_employee";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
