<?php

namespace App\Models\Payroll;

use App\Livewire\Admin\Company\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BandedGradeStructure extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ["deleted_at", "created_at", "updated_at"];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class, 'grade_id', 'id');
    }

    public function band()
    {
        return $this->belongsTo(EmployeeBand::class, 'band_id', 'id');
    }

    public function pgrade()
    {
        return $this->belongsTo(EmployeePgrade::class, 'pgrade_id', 'id');
    }

    public function perks()
    {
        return $this->belongsToMany(\App\Models\Payroll\Perk::class, 'grade_perks', 'grade_structure_id', 'perk_id')
            ->withPivot(['amount'])
            ->withTimestamps();
    }
}
