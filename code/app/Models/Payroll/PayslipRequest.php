<?php

namespace App\Models\Payroll;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use PermissionList;

class PayslipRequest extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, SoftDeletes, ArflowModel;

    protected $fillable = [
        "company_id",
        "employee_id",
        "job_id",
        "designation_id",
        "band_id",
        "pgrade_id",
        "grade_structure_id",
        "banded_grade_structure_id",
        "allowance_metadata",
        "enroll_status",
        "doj_inhouse",
        "employee_status_id",
        "stipend",
        "cit",
        "ssf_employer",
        "ssf_employee",
        "insurance",
        "insurance_expiry_date",
        "medical_insurance",
        "medical_insurance_expiry_date",
        "house_insurance",
        "house_insurance_expiry_date",
        "allow_ot",
        "payment_type",
        "payslip_type",
        "payslip_for",
        "payment_frequency",
        "non_cash_deduction",
        "remarks",
        "start_date_eng",
        "stop_date_eng",
        "start_date_nep",
        "stop_date_nep",
        "employee_ticket_id",
        "position_meta",
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\Company::class, 'company_id', 'id');
    }

    public function orgInfo(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Employee\EmployeeOrg::class, 'employee_id', 'employee_id');
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\Job::class, 'job_id', 'id');
    }

    public function designation(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\Designation::class, 'designation_id', 'id');
    }

    public function band(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeeBand::class, 'band_id', 'id');
    }

    public function level(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeePgrade::class, 'pgrade_id', 'id');
    }

    public function getCanViewLowLevelPayslipDetailAttribute()
    {
        $canView = false;
        $currentEmployeeDesignationLevel = Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', currentEmployee()->id ?? ''], ['status', 'Active']])
            ->value('designation.level');
        $modelEmployeeDesignationLevel = Payslip::leftJoin('designations as designation', 'designation.id', '=', 'payslips.designation_id')
            ->where([['employee_id', $this->employee_id ?? ''], ['status', 'Active']])
            ->value('designation.level');

        if (($modelEmployeeDesignationLevel ?? 0 <= $currentEmployeeDesignationLevel ?? 0) || auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP)) {
            $canView = true;
        }

        if (auth()->user()->hasRole(Constant::ROLE_SUPER_ADMIN) || auth()->user()->can(PermissionList::PAYROLL_VIEW_ALL_LEVEL_PAYSLIP)) {
            $canView = true;
        }


        return $canView;
    }

    public function banded_grade_structure(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\BandedGradeStructure::class, 'banded_grade_structure_id', 'id');
    }

    public function grade_structure(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\GradeStructure::class, 'grade_structure_id', 'id');
    }

    public function grade_structure_relation(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Payroll\GradeStructure::class, 'grade_structure_id', 'id');
    }

    // Override the `grade_structure` attribute accessor
    public function getGradeStructureAttribute()
    {
        if ($this->banded_grade_structure_id) {
            return $this->banded_grade_structure;
        }
        return $this->grade_structure_relation;
    }

    public function employee_status(): BelongsTo
    {
        return $this->belongsTo(\App\Models\configs\EmpStatus::class, 'employee_status_id', 'id');
    }

    public function getAllowanceDetailAttribute()
    {
        return json_decode($this->allowance_metadata, true) ?? [];
    }

    public function payslip()
    {
        return $this->hasOne(\App\Models\Payroll\Payslip::class, 'id', 'id');
    }

    public function documents()
    {
        return $this->hasManyThrough(
            \App\Models\Tickets\TicketDocument::class,
            \App\Models\RequestTicket::class,
            'ticket_id',
            'model_id',
            'id',

        );
    }

    public static function create(array $attributes = [])
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::PAYSLIP_APPROVAL);
        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::PAYSLIP_APPROVAL];
    }
}
