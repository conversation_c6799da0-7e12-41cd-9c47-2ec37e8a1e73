<?php

namespace App\Models;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OtRequests extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, ArflowModel, HasStateExtended;
    protected $guarded = [
        'created_at',
        'updated_at',
    ];

    public static function create(array $attributes = [])
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::OT_REQUEST);

        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::OT_REQUEST];
    }

    public function getInitiatorIdAttribute()
    {
        $metadata = json_decode($this->state_metadata, true);

        if (!empty($metadata['latest_actions'])) {
            foreach ($metadata['latest_actions'] as $action) {
                if (
                    is_array($action) &&
                    isset($action[0]) &&
                    $action[0] === "AuroraWebSoftware\\ArFlow\\TransitionActions\\LogHistoryTransitionAction" &&
                    isset($action[1]['metadata'])
                ) {

                    $nested = json_decode($action[1]['metadata'], true);
                    return $nested['initiator_id'] ?? null;
                }
            }
        }
        return null;
    }

    public function getInitiatorNameAttribute()
    {
        $initiatorId = $this->getInitiatorIdAttribute();

        if ($initiatorId) {
            $employee = Employee::find($initiatorId);
            return $employee ? $employee->name . '[' . $employee->employeeCode . ']' : null;
        }

        return null;
    }

    public function getApprovedDateAttribute()
    {
        return getNepaliDate($this->created_at, "Y-m-d", addTime: false);
    }

    public function attendance()
    {
        return $this->hasMany(Attendance::class, 'date_np', 'nep_date');
    }

    public function employees()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }
}
