<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Passport\HasApiTokens as PassportHasApiTokens;
use Laravel\Sanctum\HasApiTokens;
use MBarlow\Megaphone\HasMegaphone;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use PassportHasApiTokens, HasFactory, Notifiable, HasRoles, HasMegaphone, SoftDeletes, TwoFactorAuthenticatable;

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [
            'username'        => $this->username,
            'user_id'         => $this->id,
            'employee_id'     => $this->employee->id,
            'app_login_code'  => request()->input('app_login_code') ?? $this->app_login_code,
            'device'          => request()->header('User-Device'),
            'device_model'    => request()->header('User-Device-Model'),
        ];
    }
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'password',
        'last_login'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'password' => 'hashed',
        'suspend_account' => 'boolean',
    ];

    public function employee()
    {
        return $this->hasOne(Employee::class);
    }

    
}
