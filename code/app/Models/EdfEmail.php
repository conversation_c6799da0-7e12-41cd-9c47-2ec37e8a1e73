<?php

namespace App\Models;

use App\Models\Tickets\ManpowerRequisition;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EdfEmail extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'edf_mail_details';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;
    protected $fillable = [
        'id',
        'mrf_id',
        'email',
        'status',
        'edf_meta',
        'reject_reason',
        'doj',
        'name',
        'deadline',
        'shortcut',
        'mail_meta',
        'response_meta',
    ];

    protected $casts = [
        'edf_meta' => 'array',
        'mail_meta' => 'array',
        'response_meta' => 'array',
    ];
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }

    public function manpowerRequisition()
    {
        return $this->belongsTo(ManpowerRequisition::class, 'mrf_id');
    }

    // public function getActivitylogOptions(): LogOptions
    // {
    //     $isImpersonating = session('user_id_main');

    //     $option = LogOptions::defaults();
    //     $option->logName = $isImpersonating ? "impersonated.edf_email" : "edf_email";
    //     $option->setDescriptionForEvent(
    //         fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
    //             (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
    //     );
    //     $option->logAttributes = ['*'];
    //     return $option;
    // }
}
