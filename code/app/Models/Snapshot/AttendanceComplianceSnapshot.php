<?php

namespace App\Models\Snapshot;

use Illuminate\Database\Eloquent\Model;

class AttendanceComplianceSnapshot extends Model
{
    protected $fillable = [
        'date_en',
        'date_np',
        'company_id',
        'branch_id',
        'department_id',
        'employee_category_id',
        'active_employee_count',
        'present_count',
        'absent_count',
        'punctual_count',
        'late_in_count',
        'early_out_count',
        'leave_count',
        'day_off_count',
        'employment_type',
    ];

    protected $casts = [
        'date_en' => 'date',
        'company_id' => 'integer',
        'branch_id' => 'integer',
        'department_id' => 'integer',
        'active_employee_count' => 'integer',
        'present_count' => 'integer',
        'absent_count' => 'integer',
        'punctual_count' => 'integer',
        'late_in_count' => 'integer',
        'early_out_count' => 'integer',
        'leave_count' => 'integer',
        'attendance_rate' => 'float',
        'absenteeism_rate' => 'float',
    ];

    public function branch()
    {
        return $this->belongsTo(\App\Models\configs\Branch::class);
    }

    public function department()
    {
        return $this->belongsTo(\App\Models\configs\Department::class);
    }

    public function employeeCategory()
    {
        return $this->belongsTo(\App\Models\EmployeeCategory::class);
    }
}
