<?php

namespace App\Models\Snapshot;

use Illuminate\Database\Eloquent\Model;

class EmployeeCountSnapshot extends Model
{
    protected $fillable = [
        'date_en',
        'date_np',
        'company_id',
        'branch_id',
        'department_id',
        'employee_category_id',
        'gender',
        'marital_status',
        'employment_type',
        'age_group',
        'active_employees_count',
        'new_join_employees_count',
        'terminated_employees_count',
        'terminating_employees_count',
    ];

    protected $casts = [
        'date_en' => 'date',
        'company_id' => 'integer',
        'branch_id' => 'integer',
        'department_id' => 'integer',
        'active_employees_count' => 'integer',
        'new_join_employees_count' => 'integer',
        'terminated_employees_count' => 'integer',
        'terminating_employees_count' => 'integer',
    ];

    public function branch()
    {
        return $this->belongsTo(\App\Models\configs\Branch::class);
    }

    public function department()
    {
        return $this->belongsTo(\App\Models\configs\Department::class);
    }
}