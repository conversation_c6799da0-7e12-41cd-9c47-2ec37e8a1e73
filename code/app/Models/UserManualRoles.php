<?php

namespace App\Models;

use App\Models\Admin\Role;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class UserManualRoles extends Model
{
    use HasFactory, LogsActivity;
    protected $table = 'user_manual_roles';

    protected $fillable = [
        'user_manual_id',
        'role_id',
    ];

    public function roles()
    {
        return $this->belongsTo(Role::class, 'role_id');
    }
    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.user_manual_roles" : "user_manual_roles";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
            (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
                                                                                                                                                                                                                      