<?php

namespace App\Models\Config;

use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class TemporaryAccess extends Model
{
    use HasFactory;

    protected $table = 'temporary_access';

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            $model->created_by = auth()->user()->id;
            $model->updated_by = auth()->user()->id;
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->user()?->id;
        });
    }

    protected $fillable = [
        'temporary_employee_id',
        'from_employee_id',
        'role_ids',
        'transition_performers',
        'from',
        'to',
        'status',
        'action_meta',
    ];

    protected $casts = [
        'role_ids' => 'array',
        'transition_performers' => 'array',
        'from' => 'datetime',
        'to' => 'datetime',
        'action_meta' => 'array',
    ];

    public function temporaryEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'temporary_employee_id');
    }

    public function fromEmployee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'from_employee_id');
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isExpired()
    {
         return $this->status === 'expired';
    }

    public function canBeActivate(): bool
    {
        return $this->status === 'pending' && Carbon::now()->between($this->from->startOfDay(), $this->to->endOfDay());
    }

    public function canBeExpire(): bool
    {
        return ($this->isActive() || $this->to < Carbon::now()->format('Y-m-d')) && !$this->isExpired();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function updatedByName() 
    {
        $updatedBy = $this->updatedBy;
        return $updatedBy?->employee?->name ?? $updatedBy?->username;
    }

    public function updateActionMeta(array $meta) {
        $this->update([
            'action_meta' => array_merge($this->action_meta ?? [], $meta)
        ]);
    }
}
