<?php

namespace App\Models\Config;

use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Activitylog\LogOptions;
use \Spatie\Activitylog\Traits\LogsActivity;

class MultipleAccess extends Model
{
    use LogsActivity;
    protected $table = "multiple_access";

    protected $fillable = [
        "employee_id",
        "type",
        "model_type",
        "model_id",
        "from",
        "to",
        "expired"
    ];

    public function scopeSearch($query, $value)
    {
        $query->with(['employee', 'modelable'])
            ->whereHas('employee', function ($q) use ($value) {
                $q->where('first_name', 'LIKE', '%' . $value . '%')
                    ->orWhere('middle_name', 'LIKE', '%' . $value . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $value . '%')
                    ->orWhereRaw("CONCAT(first_name, ' ', middle_name, ' ', last_name) LIKE ?", ["%{$value}%"]);
            });
    }
    
    public function employee(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    // Polymorphic relation
    public function modelable(): MorphTo
    {
        return $this->morphTo(null, 'model_type', 'model_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.multi_access" : "multi_access";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
