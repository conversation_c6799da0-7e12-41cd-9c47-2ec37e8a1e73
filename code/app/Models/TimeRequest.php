<?php

namespace App\Models;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Models\Arflow\ArflowHistory;
use App\Models\Arflow\TransitionPerformer;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TimeRequest extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, ArflowModel;
    public bool $autoSubmit = true;

    protected $fillable = [
        'employee_id',
        'date',
        'nep_date',
        'fiscal_year_id',
        'in_time',
        'out_time',
        'in_note',
        'out_note',
        'current_owner_id',
    ];

    public static function create(array $attributes = [], $employeeId = null)
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::TIME_REQUEST_APPROVAL, employeeId: $employeeId);
        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::TIME_REQUEST_APPROVAL];
    }

    // for eager loading on tickets can't apply where condition here so only added date not employee id, and the data of employee id will be more than date
    public function dateAttendance()
    {
        return $this->hasMany(Attendance::class, 'date_en', 'date');
    }

    public function canBeViewed(): bool
    {
        return isSuperAdmin() || $this->isRelatedToTicket() || $this->isWithinScope();
    }
}
