<?php

namespace App\Models\FieldTeam;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TeamOtRequest extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, ArflowModel, HasStateExtended;
    protected $table = 'field_team_ot_requests';
    protected $fillable = [
        'team_id',
        'employee_id',
        'nep_date',
        'start_time',
        'end_time',
        'leader_attendance',
        'members_attendance',
        'total_working_hours',
        'total_ot_hours',
        'remarks',
        'type',
    ];

    protected $casts = [
        'leader_attendance' => 'array',
        'members_attendance' => 'array',
    ];

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::TEAM_OT_TICKET];
    }

    public static function create(array $attributes = [], $employeeId = null)
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::TEAM_OT_TICKET, employeeId: $employeeId);

        return self::find($request->id);
    }
    public function team()
    {
        return $this->belongsTo(TeamList::class, 'team_id', 'id');
    }

    public function employees()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function verifier()
    {
        return $this->belongsTo(Employee::class, 'verifier_id', 'id');
    }
}
