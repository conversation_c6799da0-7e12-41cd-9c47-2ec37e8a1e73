<?php

namespace App\Models\FieldTeam;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TemporaryTeamMembers extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'field_team_members_temporary';
    protected $guarded = ['id', 'created_at', 'updated_at', 'deleted_at'];

    public function team()
    {
        return $this->belongsTo(TeamList::class, 'team_id', 'id');
    }
}
