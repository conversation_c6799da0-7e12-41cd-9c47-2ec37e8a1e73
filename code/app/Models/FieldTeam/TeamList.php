<?php

namespace App\Models\FieldTeam;

use App\Models\configs\Branch;
use App\Models\configs\SubBranch;
use App\Models\Employee\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class TeamList extends Model
{
    use LogsActivity;

    protected $table = 'field_teams';
    protected $guarded = ['id', 'created_at', 'updated_at', 'deleted_at'];
    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function teamType()
    {
        return $this->belongsTo(TeamType::class, 'team_type', 'id');
    }

    public function transportType()
    {
        return $this->belongsTo(TransportType::class, 'transport_type', 'id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id', 'id');
    }
    public function operationCenter()
    {
        return $this->belongsTo(SubBranch::class, 'operation_center', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Employee::class, 'created_by', 'id');
    }

    public function teamMembers()
    {
        return $this->hasMany(TeamMembers::class, 'team_id', 'id');
    }

    public function employees()
    {
        return $this->belongsToMany(Employee::class, 'field_team_members_main', 'team_id', 'employee_id')->withPivot('is_team_leader')->withTrashed();
    }

    public function temporaryTeamMembers()
    {
        return $this->hasMany(TemporaryTeamMembers::class, 'team_id', 'id');
    }
    public function otRequests()
    {
        return $this->hasMany(TeamOtRequest::class, 'team_id', 'id');
    }

    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.team_list" : "team_list";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
