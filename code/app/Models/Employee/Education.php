<?php

namespace App\Models\Employee;

use App\Models\configs\EducationLevel;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Education extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $table = 'employee_education';

    protected $fillable = ['employee_id', 'level_id', 'institute_name', 'course', 'joined_date', 'completed_date', 'score'];

    public function educationLevel()
    {
        return $this->belongsTo(EducationLevel::class, "level_id", "id");
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.profile_education" : "profile_education";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
