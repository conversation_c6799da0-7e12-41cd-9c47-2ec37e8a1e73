<?php

namespace App\Models\Employee;

use App\Http\Helpers\Constant;
use App\Models\configs\Region;
use App\Models\configs\Branch;
use App\Models\configs\Department;
use App\Models\configs\EmployeeShift as ConfigsEmployeeShift;
use App\Models\configs\EmpStatus;
use App\Models\configs\OutsourceCompany;
use App\Models\configs\SubBranch;
use App\Models\configs\Unit;
use App\Models\EmployeeCategory;
use App\Models\Payroll\Designation;
use App\Models\Payroll\Payslip;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EmployeeOrg extends Model
{
    use HasFactory, LogsActivity, SoftDeletes;

    protected $table = 'employee_org';

    protected $guarded = ['created_at', 'updated_at'];

    public function region()
    {
        return $this->belongsTo(Region::class, 'region_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function subBranch()
    {
        return $this->belongsTo(SubBranch::class, 'sub_branch_id');
    }

    public function department()
    {
        return $this->belongsTo(Department::class, 'department_id');
    }

    public function supervisor()
    {
        return $this->belongsTo(Employee::class, "supervisor_id", "id");
    }

    public function shift()
    {
        return $this->belongsTo(ConfigsEmployeeShift::class, 'shift_id');
    }

    public function getIsNightShiftAttribute()
    {
        return $this->shift->type == "overnight";
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function outsource_company()
    {
        return $this->belongsTo(OutsourceCompany::class, 'outsource_company_id', 'id');
    }

    public function employee_status()
    {
        return $this->belongsTo(EmpStatus::class, 'employee_status_id');
    }

    public function designation()
    {
        return $this->belongsTo(Designation::class, 'designation_id', 'id');
    }

    public function getFormattedJoinDateAttribute()
    {
        return Carbon::parse($this->attributes['doj'])->format('jS M Y');
    }

    public function getFormattedJoinDateInHouseAttribute()
    {
        if (!$this->attributes['doj_inhouse'])
            return "";
        return Carbon::parse($this->attributes['doj_inhouse'])->format('jS M Y');
    }

    public function getCompanyEmployeeCodeAttribute()
    {
        $code = $this->employee?->company?->code;
        return $code . '-' . $this->attributes["employee_code"];
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.employee_org" : "employee_org";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
            (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id')->withTrashed();
    }

    public function activePayslips()
    {
        return $this->hasMany(Payslip::class, 'employee_id', 'employee_id')->where('status', 'Active');
    }
    public function employeeCategory()
    {
        return $this->belongsTo(EmployeeCategory::class, 'employee_category_id', 'id');
    }
}
