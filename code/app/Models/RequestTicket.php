<?php

namespace App\Models;

use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Employee\Employee;
use App\Models\Leaves\LeaveRequest;
use App\Models\Leaves\ReplacementLeaveRequest;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Tickets\ManpowerRequisition;
use App\Models\Tickets\TicketDocument;
use App\Models\Termination\EmployeeTermination;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class RequestTicket extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    //Need to uncomment it in case of petty cash v2
    public const IGNORE_WORKFLOWS = [WorkflowName::PETTY_CASH_BILL];

//    public const IGNORE_WORKFLOWS = [];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (!currentEmployee()) throw new \Exception("Admin can't perform this action");
            $ticket->submitted_by = \currentEmployee()->id;
        });
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function submittedBy()
    {
        return $this->belongsTo(Employee::class, 'submitted_by', 'id')->withTrashed();
    }

    public function currentOwner()
    {
        return $this->belongsTo(Employee::class, 'current_owner_id', 'id')->withTrashed();
    }

    public function model()
    {
        return $this->morphTo('model', 'model_type', 'model_id', 'id');
    }

    public function documents()
    {
        return $this->hasMany(TicketDocument::class, 'ticket_id', 'id');
    }

    public function employeeTermination()
    {
        return $this->belongsTo(EmployeeTermination::class, 'model_id', 'id')->withTrashed();
    }

    public function scopeToBeReviewedRequests(Builder $query)
    {
        return $query->where('current_owner_id', currentEmployee()?->id);
    }

    public function scopeWithoutIgnoredWorkflows(Builder $query)
    {
        return $query->whereNotIn('workflow', self::IGNORE_WORKFLOWS);
    }

    public function scopeMyRequests($query)
    {
        $finalStates = config('arflow-config.globalFinalStates', []);
        return $query->where('submitted_by', currentEmployee()?->id)
            ->whereNotIn('state', $finalStates);
    }

    public function scopeMyRequestsHistory($query)
    {
        $finalStates = config('arflow-config.globalFinalStates', []);
        return $query->where('submitted_by', currentEmployee()?->id)
            ->whereIn('state', $finalStates);
    }


    public function scopeReviewedRequests(Builder $query)
    {
        return $query->whereHasMorph('model', config('arflow-config.ticketModels') ?? [], function ($query) {
            return $query->whereHas('stateHistory', function ($query) {
                $query->where('actor_model_id', currentEmployee()?->id)
                    ->whereNotIn('to', [WorkflowState::SUBMITTED, WorkflowState::CANCELLED]);
            });
        });
    }

    public function addDocuments(array $documents, $arflowId = null,): array
    {
        $fileNames = [];
        foreach ($documents as $document) {
            $originalName = explode(".", $document->getClientOriginalName())[0];
            $uniqueHex = time();
            $fileName = $document->storeAs(
                "uploads/tickets/{$this->workflow}",
                "{$this->id}-$originalName.$uniqueHex.{$document->extension()}",
                "public"
            );
            array_push($fileNames, $fileName);
            $this->documents()->create([
                "document" => $fileName,
                'owner_id' => currentEmployee()?->id,
                'arflow_id' => $arflowId
            ]);
        }
        return $fileNames;
    }

    public function removeDocuments(array $removingDocumentIds)
    {
        $query = $this->documents()->whereIn('id', $removingDocumentIds);
        $removingDocuments = $query->get();
        foreach ($removingDocuments as $document) {
            Storage::delete("public/" . $document->document);
        }
        return $query->delete();
    }

    public function scopeFilterModelType(Builder $query, string $value)
    {
        $modelTypes = [
            "leave_request" => LeaveRequest::class,
            "time_request" => TimeRequest::class,
        ];
        return $query->when($value, function ($query) use ($modelTypes, $value) {
            $query->where('model_type', $modelTypes[$value]);
        });
    }

    public function getDocuments()
    {
        if ($this->documents) {
            return $this->documents?->map(function ($document) {
                return [
                    "id" => $document->id,
                    "name" => $document->name,
                    "path" => url("/storage/{$document->document}"),
                    "owner_name" => $document->owner->name,
                    "icon" => $document->icon,
                    "state" => $document->arflow?->to,
                    "date_added" => LaravelNepaliDate::from($document->created_at)->toNepaliDate("F j, Y D"),
                    'canRemove' => $document->canRemove,
                    'extension' => last(explode(".", $document->document))
                ];
            });
        } else {
            return [];
        }
    }

    public function isCurrentOwner()
    {
        return $this->current_owner_id === currentEmployee()?->id;
    }
}
