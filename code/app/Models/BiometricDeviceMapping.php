<?php

namespace App\Models;

use App\Models\configs\AttDevice;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class BiometricDeviceMapping extends Model
{
    use HasFactory, LogsActivity;

    protected $table = 'biometric_device_mapping';

    protected $fillable = [
        "biometric_id",
        "device_id"
    ];

    public function attDevice()
    {
        return $this->belongsTo(AttDevice::class, 'device_id', 'id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.biometric_device_mapping" : "biometric_device_mapping";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
