<?php

namespace App\Models\Arflow;

use App\Models\Employee\Employee;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ArflowHistory extends Model
{
    use SoftDeletes;

    protected $table = "arflow_state_transitions";

    protected $fillable = [
        'workflow',
        'model_type',
        'model_id',
        'from',
        'to',
        'actor_model_type',
        'actor_model_id',
        'comment'
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'actor_model_id', 'id')->withTrashed();
    }

    public function actor()
    {
        return $this->morphTo('actor', 'actor_model_type', 'actor_model_id', 'id')->withTrashed();
    }

    public function documents()
    {
        return $this->hasMany(\App\Models\Tickets\TicketDocument::class, 'arflow_id', 'id');
    }

    public function scopeGetPerformer($query, $state)
    {
        return $query->where('to', $state);
    }

    public function getDocuments()
    {
        if ($this->documents) {
            return $this->documents?->map(function ($document) {
                return [
                    "id" => $document->id,
                    "name" => $document->name,
                    "path" => url("/storage/{$document->document}"),
                    "icon" => $document->icon,
                    "date_added" => LaravelNepaliDate::from($document->created_at)->toNepaliDate("F j, Y D"),
                ];
            });
        } else {
            return [];
        }
    }
}
