<?php

namespace App\Models\Leaves;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\configs\LeaveType;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use App\Models\Arflow\ArflowHistory;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LeaveRequest extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, ArflowModel, SoftDeletes;

    protected $table = "leave_requests";
    public bool $autoSubmit = true;
    protected $fillable = [
        'leave_type_id',
        'employee_id',
        'start_date',
        'end_date',
        'nep_start_date',
        'nep_end_date',
        'replaced_start_date',
        'replaced_end_date',
        'fiscal_year_id',
        'num_days',
        'leave_option_id',
        'applied_status',
        'remarks',
        'current_owner_id',
    ];

    public function scopeSearch($query, $value)
    {
        return $query->Where(function ($query) use ($value) {
            $query->where('state', 'like', '%' . $value . '%')
                ->orWhereHas('employee', function ($q) use ($value) {
                    $q->where('first_name', 'like', '%' . $value . '%')
                        ->orWhere('middle_name', 'like', '%' . $value . '%')
                        ->orWhere('last_name', 'like', '%' . $value . '%');
                });
        });
    }

    public function scopeUnapproved($query)
    {
        return $query->whereNotIn('leave_requests.state', ArflowHelper::getFinalStates(WorkflowName::LEAVE_APPROVAL));
    }

    public function leaveType()
    {
        return $this->belongsTo(LeaveType::class);
    }

    public function leaveOption()
    {
        return $this->belongsTo(LeaveOption::class)->withTrashed();
    }

    public function canBeViewed(): string|bool
    {
        return isSuperAdmin() || $this->isRelatedToTicket() || $this->isWithinScope();
    }

    //override create method to apply workflow on create
    public static function create(array $attributes = [])
    {
        $leaveRequest = (new static)->newQuery()->create($attributes);
        $leaveRequest->applyWorkFlow(WorkflowName::LEAVE_APPROVAL, $leaveRequest->remarks ?? null);

        if (!(fedexHrm() && $leaveRequest->leaveType?->name === Constant::REPLACEMENT_LEAVE_NAME) && $leaveRequest->leaveType?->name !== Constant::UNPAID_LEAVE) {
            $employeeLeave = \App\Models\Leaves\EmployeeLeave::where('employee_id', $leaveRequest->employee_id)
                ->where('leave_type_id', $leaveRequest->leave_type_id)
                ->where('fiscal_year_id', $leaveRequest->fiscal_year_id)
                ->first();
            if (!$employeeLeave) {
                throw new \Exception("Leave not assigned to the employee");
            }
            $employeeLeave->pending_leave = $employeeLeave->pending_leave + $leaveRequest->num_days;
            $employeeLeave->save();
        }

        return self::find($leaveRequest->id);
    }

    //workflow
    public static function supportedWorkflows(): array
    {
        return [WorkflowName::LEAVE_APPROVAL];
    }

    public function replacedDates()
    {
        return $this->hasMany(ReplacementLeaveDate::class);
    }
}
