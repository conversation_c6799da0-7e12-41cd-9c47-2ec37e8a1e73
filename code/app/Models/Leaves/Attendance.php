<?php

namespace App\Models\Leaves;

use App\Models\Employee\Employee;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attendance extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = "attendance";

    protected $guarded = ['id'];

    protected $casts = [
        'location_data' => 'array',
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id');
    }

    public function getDateAttribute()
    {
        $eng_date = Carbon::parse($this->date_en)->format('l d M Y');
        return [
            'eng_date' => $eng_date,
            'nep_date' => $this->date_np,
        ];
    }

    public function getTotalMinutesAttribute()
    {
        if ($this->in_time && $this->out_time) {
            return Carbon::parse($this->in_time)->diffInMinutes(Carbon::parse($this->out_time));
        }
        return 0;
    }
}
