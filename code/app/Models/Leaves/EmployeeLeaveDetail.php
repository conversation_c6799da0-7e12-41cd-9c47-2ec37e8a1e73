<?php

namespace App\Models\Leaves;

use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeLeaveDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_request_id',
        'date',
        'nep_date',
        'fiscal_year_id',
        'leave_type_id',
        'leave_option_id',
        'num_days',
        'replaced_date',
        'remarks',
        'performers'
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($data) {
            $value = $data->performers;
            if (is_array($value)) {
                $data->performers = \json_encode($value);
            } else {
                $data->performers = $value ?? "";
            }
        });
    }

    public function leaveOption()
    {
        return $this->belongsTo(LeaveOption::class, 'leave_option_id')->withTrashed();
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'employee_id');
    }

    public function fiscalYear()
    {
        return $this->belongsTo(FiscalYear::class, 'fiscal_year_id');
    }

    public function leaveType()
    {
        return $this->belongsTo(LeaveType::class, 'leave_type_id')->withTrashed();
    }

    public function leaveRequest()
    {
        return $this->belongsTo(LeaveRequest::class, 'leave_request_id')->withTrashed();
    }

    public function getPerformersAttribute($value)
    {
        if (\is_string($value)) {
            if (json_decode($value) !== null) {
                return json_decode($value, true);
            }
        }
        return $value;
    }
}
