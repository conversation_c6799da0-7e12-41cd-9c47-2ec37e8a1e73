<?php

namespace App\Models\Leaves;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class LeaveOption extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    protected $fillable = ['name', 'num_days', 'replacement_available', 'is_active'];
    public $timestamps = false;

    public static function list($replacement = false)
    {
        $cacheName = $replacement ? 'replacement-leave-options' : 'leave-options';
        return Cache::remember($cacheName, null, function () use ($replacement) {
            if ($replacement) {
                return LeaveOption::where('replacement_available', $replacement)
                    ->where('is_active', true)->pluck('name', 'id')->toArray();
            } else {
                return LeaveOption::where('is_active', true)->pluck('name', 'id')->toArray();
            }
        });
    }

    public static function removeCache()
    {
        Cache::deleteMultiple(['replacement-leave-options', 'leave-options']);
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.leave_option" : "leave_option";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
