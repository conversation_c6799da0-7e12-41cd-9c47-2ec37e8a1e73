<?php

namespace App\Models\Leaves;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReplacementLeaveRequest extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, ArflowModel;

    protected $fillable = [
        'fiscal_year_id',
        'employee_id',
        'submitted_by',
        'start_date',
        'end_date',
        'num_days',
        'leave_option_id',
        'remarks'
    ];

    public static function create(array $attributes = [])
    {
        $model = (new static)->newQuery()->create($attributes);
        $model->applyWorkFlow(WorkflowName::REPLACEMENT_LEAVE_REQUEST, $model->remarks ?? null);
        return self::find($model->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::REPLACEMENT_LEAVE_REQUEST];
    }

    public function leaveOption()
    {
        return $this->belongsTo(LeaveOption::class)->withTrashed();;
    }
}
