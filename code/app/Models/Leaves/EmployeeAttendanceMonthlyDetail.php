<?php

namespace App\Models\Leaves;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeAttendanceMonthlyDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id', 'year', 'month', 'total_days', 'holiday', 'work_on_holiday', 'fiscal_year_id',
        'duty_days', 'present_days', 'absent_days', 'missed_punch', 'approved_leave', 'leave_details'
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($metaData) {
            $value = $metaData->leave_details;
            if (is_array($value)) {
                $metaData->leave_details = \json_encode($value);
            } else {
                $metaData->leave_details = $value ?? "";
            }
        });
    }

    public function getLeaveDetailAttribute()
    {
        return \json_decode($this->leave_details, true);
    }
}
