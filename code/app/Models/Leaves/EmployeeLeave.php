<?php

namespace App\Models\Leaves;

use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeLeave extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'fiscal_year_id',
        'assigned_leave',
        'leave_taken',
        'remaining_days',
        'excess_leave',
        'pending_leave',
        'expiry_date'
    ];

    public function fiscalYear()
    {
        return $this->belongsTo(FiscalYear::class);
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function leaveType()
    {
        return $this->belongsTo(LeaveType::class);
    }
}
