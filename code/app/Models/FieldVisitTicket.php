<?php

namespace App\Models;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\configs\FiscalYear;
use App\Models\Employee\Employee;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FieldVisitTicket extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, ArflowModel, HasStateExtended;

    protected $guarded = ['id'];
    protected $table = 'field_visit_tickets';

    public static function create(array $attributes = [], $employeeId = null)
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::FIELD_VISIT_OT_TICKET, employeeId: $employeeId);
        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::FIELD_VISIT_OT_TICKET];
    }

    public function employees()
    {
        return $this->belongsTo(Employee::class, 'employee_id', 'id')->withTrashed();
    }

    public function fieldVisit()
    {
        return $this->belongsTo(FieldVisit::class, 'field_visit_id', 'id');
    }

    public function fiscalYear()
    {
        return $this->belongsTo(FiscalYear::class, 'fiscal_year_id', 'id');
    }
}
