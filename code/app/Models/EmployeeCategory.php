<?php

namespace App\Models;

use App\Models\Employee\EmployeeOrg;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeCategory extends Model
{
    use HasFactory, SoftDeletes;
    protected $fillable = [
        'type',
        'is_countable',
        'company_id'
    ];

    protected $casts = [
        'is_countable' => 'boolean',
    ];

    public function scopeIsCountable($query)
    {
        return $query->where('is_countable', true);
    }
    public function scopeSearch($query, $value)
    {
        $query->where("type", "like", '%' . $value . '%');
    }
    public function employees()
    {
        return $this->hasMany(EmployeeOrg::class, 'employee_category_id');
    }
    public function scopeCountable($query)
    {
        return $query->where('is_countable', true);
    }

}
