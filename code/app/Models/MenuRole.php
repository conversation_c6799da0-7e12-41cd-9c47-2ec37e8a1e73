<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;

class MenuRole extends Model
{
    use HasFactory;
    
    public $timestamps = false;
    
    protected $table = "menus_roles";

    protected $guarded = ["id"];

    public function getActivitylogOptions(): LogOptions
    {

        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.menu_role" : "menu_role";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
