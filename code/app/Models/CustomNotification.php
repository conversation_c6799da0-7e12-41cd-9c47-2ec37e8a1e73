<?php

namespace App\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CustomNotification extends Model
{
    use HasFactory, LogsActivity;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'body',
        'status',
        'platforms',
        'target',
        'target_ids',
        'payload',
        'scheduled_at',
        'error',
        'sent_at',
        'created_by'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'platforms'     => 'array',
        'target_ids'    => 'array',
        'payload'       => 'array',
        'scheduled_at'  => 'datetime',
        'sent_at'       => 'datetime',
    ];

    /**
     * Relationship to the user who created the notification
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter notifications by status
     */
    public function scopeStatus($query, $status)
    {
        if (!in_array($status, ['draft', 'failed', 'scheduled', 'sent'])) {
            throw new Exception('Invalid status: ' . $status);
        }
        return $query->where('status', $status);
    }

    /**
     * Check if the notification is scheduled
     */
    public function isScheduled()
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if the notification has been sent
     */
    public function isSent()
    {
        return $this->status === 'sent';
    }

    public function markAsDraft()
    {
        $this->update([
            'status' => 'draft',
        ]);
    }

    public function markAsScheduled(\DateTime $scheduledAt)
    {
        $this->update([
            'status' => 'scheduled',
            'scheduled_at' => $scheduledAt
        ]);
    }

    public function markAsPending()
    {
        $this->update([
            'status' => 'pending',
        ]);
    }

    // Method to mark as sent
    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    // Method to mark as failed
    public function markAsFailed(string $error)
    {
        $this->update([
            'status' => 'failed',
            'error' => $error
        ]);
    }

    public function getTargets()
    {
        if ($this->target === 'all') {
            return 'all';
        }
        switch ($this->target) {
            case 'companies':
                return \App\Models\configs\Company::whereIn('id', $this->target_ids)->get();
            case 'regions':
                return \App\Models\configs\Region::whereIn('id', $this->target_ids)->get();
            case 'branches':
                return \App\Models\configs\Branch::whereIn('id', $this->target_ids)->get();
            case 'departments':
                return \App\Models\configs\Department::whereIn('id', $this->target_ids)->get();
            case 'users':
                return \App\Models\User::whereIn('id', $this->target_ids)->get();
            case 'employees':
                return \App\Models\Employee\Employee::whereIn('id', $this->target_ids)->get();
            default:
                return null;
        }
    }

    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.custom_notification" : "custom_notification";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
