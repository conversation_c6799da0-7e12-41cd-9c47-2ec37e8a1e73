<?php

namespace App\Models\SelfService;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Handbookdocs extends Model
{
    use HasFactory, LogsActivity;
    protected $table = 'hand_book_docs';

    protected $fillable = [
        'name',
        'company_id',
        'region_id',
        'branch_id',
        'dept_id',
        'unit_id',
        'document',
        'document_type'
    ];
    public function company()
    {
        return $this->belongsTo(\App\Models\configs\Company::class, 'company_id', 'id');
    }
    public function region()
    {
        return $this->belongsTo(\App\Models\configs\Region::class, "region_id");
    }
    public function branch()
    {
        return $this->belongsTo(\App\Models\configs\Branch::class, "branch_id");
    }
    public function department()
    {
        return $this->belongsTo(\App\Models\configs\Department::class, "dept_id");
    }
    public function unit()
    {
        return $this->belongsTo(\App\Models\configs\Unit::class, "unit_id");
    }
    public function scopeSearch($query, $value)
    {
        $query->where('name', 'like', "%{$value}%");
    }
    public function getActivitylogOptions(): LogOptions
    {
        $isImpersonating = session('user_id_main');

        $option = LogOptions::defaults();
        $option->logName = $isImpersonating ? "impersonated.handbook" : "handbook";
        $option->setDescriptionForEvent(
            fn(string $eventName) => ($isImpersonating ? "Impersonated action performed by: " : "Action performed by: ") .
                (User::find($isImpersonating ? session('user_id_main') : session('user_id'))->employee?->name ?? ($isImpersonating ? 'Unknown User' : 'Admin'))
        );
        $option->logAttributes = ['*'];
        return $option;
    }
}
