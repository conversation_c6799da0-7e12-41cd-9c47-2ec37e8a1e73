<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use NumberToWords\Legacy\Numbers\Words\Locale\Id;

class Image extends Model
{
    use HasFactory;

    protected $table = 'images';

    protected $fillable = [
        'field_visit_id',
        'image_path',
        'alt'
    ];

    public function FieldVisit()
    {
        $this->belongsTo(FieldVisit::class, 'field_visit_id', 'id');
    }
}
