<?php

namespace App\Models\Termination;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Models\Employee\Employee;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmployeeTermination extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, HasStateExtended, SoftDeletes, ArflowModel;

    protected $fillable = [
        'employee_id',
        'termination_request_date',
        'nep_termination_request_date',
        'termination_date',
        'nep_termination_date',
        'termination_reason',
        'termination_type',
    ];


    public static function create(array $attributes = [])
    {
        $request = (new static)->newQuery()->create($attributes);
        $request->applyWorkFlow(WorkflowName::TERMINATION_APPROVAL);
        return self::find($request->id);
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::TERMINATION_APPROVAL];
    }

    public function employees()
    {
        return $this->hasOne(Employee::class, "id", "employee_id")->withTrashed();
    }

    public function scopeWithActiveEmployees($query)
    {
        return $query->whereHas('employees', function ($q) {
            $q->whereNull('deleted_at');
        });
    }
}
