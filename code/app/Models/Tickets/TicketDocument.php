<?php

namespace App\Models\Tickets;

use App\Http\Helpers\Enums\WorkflowName;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class TicketDocument extends Model
{
    use HasFactory;

    protected $fillable = ['ticket_id', 'document', 'owner_id', 'arflow_id'];

    public function ticket()
    {
        return $this->belongsTo(\App\Models\RequestTicket::class, 'ticket_id', 'id');
    }

    public function owner()
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'owner_id', 'id')->withTrashed();
    }

    public function arflow()
    {
        return $this->belongsTo(\App\Models\Arflow\ArflowHistory::class, 'arflow_id', 'id');
    }

    public function getNameAttribute()
    {
        $nameArr = explode('.', last(explode('/', $this->document)));
        $originalName = explode('-', $nameArr[0])[1];
        $extension = last($nameArr);
        $imageName = "$originalName.$extension";
        return $imageName;
    }

    public function getIconAttribute()
    {
        $filePath = "public/" . $this->document;
        $fileExist = Storage::exists($filePath);
        $mime = "";
        if ($fileExist) {
            $mime = Storage::mimeType($filePath);
        } else {
            $mime = "";
        }
        return \getDocumentIcon($mime);
    }

    public function getCanRemoveAttribute()
    {
        if ($this->ticket->workflow == WorkflowName::FIELD_VISIT_OT_TICKET) {
            return false;
        }
        return ($this->owner_id === currentEmployee()?->id &&
            !$this->arflow_id && $this->ticket->isCurrentOwner()) || $this->ticket->model->canCancel();
    }
}
