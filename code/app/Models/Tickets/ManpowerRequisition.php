<?php

namespace App\Models\Tickets;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Traits\ArflowModel;
use App\Traits\HasStateExtended;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ManpowerRequisition extends Model implements StateableModelContract, ArflowModelInterface
{
    use HasFactory, SoftDeletes, HasStateExtended, ArflowModel;

    protected $fillable = [
        'company_id',
        'job_id',
        'designation_id',
        'band_id',
        'level_id',
        'branch_id',
        'sub_branch_id',
        'department_id',
        'unit_id',
        'position_type_id',
        'incumbent_id',
        'employee_id',
        'type',
        'number_vacancy',
        'responsibilities',
        'experience',
        'timeline',
        'justification',
        'qualification',
        'skills',
        'salary_metadata',
        'head_of_dept',
        'appointment_type',
        'location',
        'required_position',
        'reports_to',
        'roi_calculation_metadata',
        'min_year_of_experience',
        'max_year_of_experience',
        'min_age_limit',
        'max_age_limit',
        'number_of_males',
        'number_of_females',
        'number_of_personnel_required',
        'infrastructure_metadata',
        'reallocation_justification',
        'sanction_consequences',
        'kra'
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($perkModel) {
            foreach ($perkModel->attributes as $key => $value) {
                if ($key !== 'number_fulfilled') {
                    if (empty($value)) {
                        $perkModel->{$key} = null;
                    }
                }
            }
        });
    }


    // public function updateNumberFulfilled(int $number)
    // {
    //     $this->number_fulfilled = $number;
    //     $this->save();
    // }

    public function company()
    {
        return $this->belongsTo(\App\Models\configs\Company::class, 'company_id', 'id');
    }

    public function getSalaryDetailAttribute()
    {
        return json_decode($this->salary_metadata, true) ?? [];
    }

    public function getAllowanceDetailAttribute()
    {
        $data = [];
        foreach ($this->perks as $perk) {
            $data[$perk->perk->name] = "Rs." . $perk->amount;
        }
        return $data;
    }

    public function job()
    {
        return $this->belongsTo(\App\Models\configs\Job::class);
    }

    public function designation()
    {
        return $this->belongsTo(\App\Models\Payroll\Designation::class);
    }

    public function branch()
    {
        return $this->belongsTo(\App\Models\configs\Branch::class);
    }

    public function headOfDepartement()
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'head_of_dept', 'id')->withTrashed();
    }

    public function replacementFor()
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'incumbent_id', 'id')->withTrashed();
    }

    public function subBranch()
    {
        return $this->belongsTo(\App\Models\configs\SubBranch::class, 'sub_branch_id');
    }

    public function department()
    {
        return $this->belongsTo(\App\Models\configs\Department::class);
    }

    public function unit()
    {
        return $this->belongsTo(\App\Models\configs\Unit::class);
    }

    public function replacedEmployee()
    {
        return $this->belongsTo(\App\Models\Employee\Employee::class, 'incumbent_id', 'id')->withTrashed();
    }

    public function positionType()
    {
        return $this->belongsTo(\App\Models\configs\EmpStatus::class, 'position_type_id', 'id');
    }

    public function band()
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeeBand::class, 'band_id', 'id');
    }

    public function perks()
    {
        return $this->morphMany(\App\Models\Payroll\PerkModel::class, 'model');
    }

    public function level()
    {
        return $this->belongsTo(\App\Models\Payroll\EmployeePgrade::class, 'level_id', 'id');
    }

    public static function supportedWorkflows(): array
    {
        return [WorkflowName::MANPOWER_REQUISITION];
    }

    public function documents()
    {
        return $this->hasManyThrough(
            \App\Models\Tickets\TicketDocument::class,
            \App\Models\RequestTicket::class,
            'ticket_id',
            'model_id',
            'id',

        );
    }

    public function employees()
    {
        return $this->belongsToMany(\App\Models\Employee\Employee::class, 'edf_requests', 'manpower_requisition_id', 'added_employee_id')->withPivot('id')->withTrashed();
    }
}
