<?php

namespace App\Models\Tickets;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketTempOldDetail extends Model
{
    use HasFactory;

    protected $table = "ticket_temp_old_details";

    protected $fillable = ['ticket_id', 'details'];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($data) {
            $value = $data->details;
            if (is_array($value)) {
                $data->details = \json_encode($value);
            } else {
                $data->details = $value ?? "";
            }
        });
    }

    public function getDetailsAttribute($value)
    {
        if (\is_string($value)) {
            if (json_decode($value) !== null) {
                return json_decode($value, true);
            }
        }

        return $value;
    }
}
