<?php

namespace App\Guards;

use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class EscalateRequestGuard extends TicketGuard implements TransitionGuardContract
{
  public function __construct()
  {
  }

  public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
  {
    $this->model = $model;
    $this->from = $from;
    $this->to = $to;
    $this->parameters = $parameters;
    $this->requestTicket = $this->model->requestTicket;
  }

  public function handle(): TransitionGuardResultDTO
  {
    $canEscalate = $this->isCurrentOwner();

    if ($canEscalate) {
      return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
    }

    return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
  }
}
