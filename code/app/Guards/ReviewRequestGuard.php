<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class ReviewRequestGuard extends TicketGuard implements TransitionGuardContract
{

    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canReview = $this->isCurrentOwner();
        if ($canReview) {
            $exist = $this->model->transitionPerformers->filter(function ($performer) {
                return $performer->performer_id == currentEmployee()?->id
                    && $performer->recipient_id == $this->model->employee_id
                    && $performer->state == WorkflowPerformer::REVIEWER;
            });
            $canReview = (bool)$exist;
        }

        if ($canReview) {
            // if ($this->model->canReview()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
