<?php

namespace App\Guards;

use App\Http\Helpers\Enums\WorkflowPerformer;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class EmployeeTicketApproveRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canApprove = $this->isCurrentOwner();
        if ($canApprove) {
            $metaData = $this->model->metaData->pluck('meta_value', 'meta_key');
            if ($metaData['payslip_change']) {
                $canApprove = true;
            } else {
                $exist =  $this->model->transitionPerformers->filter(function ($transitionPerformer) {
                    return $transitionPerformer->performer_id == currentEmployee()?->id
                        && $transitionPerformer->recipient_id == $this->model->employee_id
                        && $transitionPerformer->state == WorkflowPerformer::APPROVER;
                });
                $canApprove = (bool)count($exist);
            }
        }
        if ($canApprove) {
            // if ($this->model->canApprove()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
