<?php

namespace App\Guards;

use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Attendance\IrregularityTicket;
use App\Models\Leaves\LeaveRequest;
use App\Models\TimeRequest;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;
use PermissionList;

class AssignRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct() {}

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canAssign = false;
        if ($this->model instanceof LeaveRequest) {
            $canAssign = auth()->user()->can(PermissionList::LEAVE_REQUEST_ASSIGN_SCOPE);
        } else if ($this->model instanceof TimeRequest) {
            $canAssign = auth()->user()->can(PermissionList::TIME_REQUEST_ASSIGN_SCOPE);
        } else if ($this->model instanceof IrregularityTicket) {
            $canAssign = auth()->user()->can(PermissionList::IRREGULARITY_REQUEST_ASSIGN_SCOPE);
        }
        if (!$canAssign) {
            if ($this->model->state === WorkflowState::ALLOCATED) {
                $canAssign = $this->isCurrentOwner();
                if ($canAssign) {
                    $exist =  $this->model->transitionPerformers->filter(function ($transitionPerformer) {
                        return $transitionPerformer->performer_id == currentEmployee()?->id
                            && $transitionPerformer->recipient_id == $this->model->employee_id
                            && $transitionPerformer->state == WorkflowPerformer::APPROVER;
                    });
                    $canAssign = (bool)count($exist);
                }
            } else {
                $exist =  $this->model->transitionPerformers->filter(function ($transitionPerformer) {
                    return $transitionPerformer->performer_id == currentEmployee()?->id
                        && $transitionPerformer->recipient_id == $this->model->employee_id
                        && $transitionPerformer->state == WorkflowPerformer::APPROVER;
                });
                $canAssign = (bool)count($exist);
            }
        }
        if ($canAssign) {
            // if ($this->model->canAssign()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
