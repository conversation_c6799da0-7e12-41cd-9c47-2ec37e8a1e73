<?php

namespace App\Guards;

use App\Http\Helpers\Enums\WorkflowPerformer;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class VerifyRequestGuard extends TicketGuard implements TransitionGuardContract
{

    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $this->model->requestTicket;
        // You can perform any initialization here.
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canVerify = $this->isCurrentOwner();
        if ($canVerify) {
            $exist = $this->model->transitionPerformers
                ->filter(function ($transitionPerformer) {
                    return $transitionPerformer->performer_id == currentEmployee()?->id
                        && $transitionPerformer->recipient_id == $this->model->employee_id
                        && $transitionPerformer->state == WorkflowPerformer::VERIFIER
                        && $transitionPerformer->level == ($this->requestTicket->verification_level + 1);
                });
            if (count($exist)) {
                $canVerify = !$this->model->canApprove();
            } else {
                $canVerify = false;
            }
        }
        if ($canVerify) {
            // if ($this->model->canVerify()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
