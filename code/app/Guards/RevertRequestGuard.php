<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowState;
// use App\Models\Tickets\TicketTempOldDetail;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;
use PermissionList;

class RevertRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $this->model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        $approver = $this->model->getPerformer(WorkflowState::APPROVED) ?? $this->model->getPerformer(WorkflowState::ASSIGNED);
        $canRevert = (auth()->user() ?? currentEmployee()->user)->can(PermissionList::TICKET_REVERT) || $approver?->id == currentEmployee()?->id;

        if ($canRevert) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
