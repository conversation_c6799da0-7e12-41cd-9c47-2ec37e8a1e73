<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class SendForReviewRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $this->model->requestTicket;

        // You can perform any initialization here.
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canSendForReview = $this->isCurrentOwner();
        if ($canSendForReview) {
            $exist = $this->model->transitionPerformers->filter(function ($performer) {
                $canSend = $performer->performer_id == currentEmployee()?->id
                && $performer->recipient_id == $this->model->employee_id;
                if ($canSend) {
                    if ($performer->state == WorkflowPerformer::VERIFIER) {
                        $canSend = $performer->level == ($this->requestTicket->verification_level + 1);
                    } else {
                        $canSend = $performer->state == WorkflowPerformer::APPROVER;
                    }
                }
                return $canSend;
            });
            $canSendForReview = count($exist);
        }

        if ($canSendForReview) {
            if (ArflowHelper::hasReviewer($this->model->workflow)) {
                return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
            }
        }

        // If the permission check fails, deny the transition:
        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
