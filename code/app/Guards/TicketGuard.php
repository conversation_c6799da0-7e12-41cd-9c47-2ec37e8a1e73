<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Models\RequestTicket;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;

class TicketGuard
{
  protected StateableModelContract & ArflowModelInterface $model;
  protected string $from;
  protected string $to;
  protected array $parameters;

  protected RequestTicket | null $requestTicket = null;

  public function isCurrentOwner()
  {
    return $this->requestTicket?->current_owner_id == currentEmployee()?->id;
  }
}
