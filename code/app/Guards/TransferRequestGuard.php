<?php

namespace App\Guards;

use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use Codebright\PettyCash\Http\Helpers\PermissionList;
use Illuminate\Database\Eloquent\Model;

class TransferRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        if (auth()->user()->can(PermissionList::TOPUP_TRANSFER)) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
