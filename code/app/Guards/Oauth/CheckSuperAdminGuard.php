<?php

namespace App\Guards\Oauth;

use App\Http\Helpers\Constant;
use CodeBright\OauthAnd2fa\Contracts\PreAuthorizeDenial;
use CodeBright\OauthAnd2fa\Contracts\PreAuthorizeGuard;
use Illuminate\Http\Request;
use Illuminate\Contracts\Auth\Authenticatable;
use Laravel\Passport\Bridge\Client;

class CheckSuperAdminGuard implements PreAuthorizeGuard
{
    public function check(
        Authenticatable $user,
        Client $client,
        array $scopes,
        string $deviceStr,
        Request $request
    ): ?PreAuthorizeDenial {
        if ($user->hasRole(Constant::ROLE_SUPER_ADMIN)) {
            return new PreAuthorizeDenial(
                error: 'access_denied',
                error_description: 'Super admin access is not allowed',
                hint: 'Super admin users are not allowed to use OAuth clients for security reasons.',
                message: 'Please contact support if you believe this is an error.'
            );
        }

        return null;
    }
}
