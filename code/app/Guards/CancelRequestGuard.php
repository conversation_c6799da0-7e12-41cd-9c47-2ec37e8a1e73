<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowState;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;
use PermissionList;

class CancelRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct() {}

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;

        // You can perform any initialization here.
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canCancel = $this->model->requestTicket->submitted_by === currentEmployee()?->id;

        // seperate logic for termination tickets
        $isTerminationWorkflow = $this->model->requestTicket->workflow === WorkflowName::TERMINATION_APPROVAL;
        if ($isTerminationWorkflow && $this->model->state === WorkflowState::APPROVED) {
            $canCancel = auth()->user()->can(PermissionList::EMPLOYEE_RESTORE_PERMANENT)
                || auth()->user()->can(PermissionList::EMPLOYEE_RESTORE_FOR_A_DAY)
                || auth()->user()->can(PermissionList::EMPLOYEE_RESTORE_WITH_IOPS)
                || auth()->user()->can(PermissionList::EMPLOYEE_TERMINATION_CANCEL);
            // || auth()->user()->can(PermissionList::EMPLOYEE_RESTORE_WITH_IOPS);
            // TODO: check permission of iops in viasoft hrm
        }

        if ($canCancel) {
            // if ($this->model->canCancel()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
