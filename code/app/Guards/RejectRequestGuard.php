<?php

namespace App\Guards;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowState;
use Illuminate\Database\Eloquent\Model;
use AuroraWebSoftware\ArFlow\DTOs\TransitionGuardResultDTO;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionGuardContract;

class RejectRequestGuard extends TicketGuard implements TransitionGuardContract
{
    public function __construct()
    {
    }

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
        $this->requestTicket = $this->model->requestTicket;
    }

    public function handle(): TransitionGuardResultDTO
    {
        $canReject = $this->isCurrentOwner();
        if ($canReject) {
            if($this->model->canApprove()) {
                $canReject = true;
            } else {
                $currentState = $this->model->currentState();
                $ticketVerificationLevel = $this->requestTicket->verification_level;
                $configVerificationLevel = ArflowHelper::getVerificationLevel($this->model->workflow);
                if ($ticketVerificationLevel >= $configVerificationLevel) {
                    if ($currentState === WorkflowState::VERIFIED && !$this->model->canApprove()) {
                        $canReject = false;
                    }
                } else {
                    if ($currentState == WorkflowState::VERIFIED && !$this->model->canVerify()) {
                        $canReject = false;
                    }
                }
                // can't reject if the state is SentForReview and user can't review the
                if ($currentState == WorkflowState::SENT_FOR_REVIEW && !$this->model->canReview()) {
                    $canReject = false;
                }
            }
        }

        if ($canReject) {
        // if ($this->model->canReject()) {
            return TransitionGuardResultDTO::build(TransitionGuardResultDTO::ALLOWED);
        }

        return TransitionGuardResultDTO::build(TransitionGuardResultDTO::DISALLOWED, 'Permission denied.');
    }
}
