<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all the caches';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Clear Redis cache
        Cache::flush();

        $cacheStoreList = ['arflow', 'employee'];

        foreach ($cacheStoreList as $cacheStore) {
            $result = Cache::store($cacheStore)->flush();
            if ($result)
                $this->info("{$cacheStore} cache cleared successfully.");
            else
                $this->error("Failed to clear {$cacheStore} cache.");

            echo "\n";
        }

        try {
            app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
            $this->info("Cached permissions cleared successfully.");
        } catch (\Exception $e) {
            logCronError("Failed to clear cached permissions.", $e);
            $this->error("Failed to clear cached permissions");
        }
    }
}
