<?php

namespace App\Console\Commands;

use App\Models\DailyEmployeeCount;
use Illuminate\Console\Command;
use App\Models\Employee\Employee;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Exception;
use App\Http\Helpers\Enums\WorkflowState;

class StoreDailyEmployeeCounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:daily-employee-counts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = now()->toDateString();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();

        // Get counts for the day
        $activeCount = $this->getActiveEmployeesCount();
        $newJoinCount = $this->getNewEmployeesCount();
        $terminatingCount = $this->getTerminatingEmployeesCount();
        $terminatedCount = $this->getTerminatedEmployeesCount();

        try {
            $existingRecord = DailyEmployeeCount::where('eng_date', $today)
                ->where('nep_date', $nepaliDate)
                ->first();
            if ($existingRecord) {
                $existingRecord->update([
                    'eng_date' => $today,
                    'nep_date' => $nepaliDate,
                    'active_employees_count' => $activeCount,
                    'new_joins_count' => $newJoinCount,
                    'terminating_employees_count' => $terminatingCount,
                    'terminated_employees_count' => $terminatedCount,
                ]);
                logInfo("Daily counts for {$today} '{$nepaliDate}' have been updated. {$activeCount} {$newJoinCount}  {$terminatingCount}  {$terminatedCount}");
            } else {
                DailyEmployeeCount::create([
                    'eng_date' => $today,
                    'nep_date' => $nepaliDate,
                    'active_employees_count' => $activeCount,
                    'new_joins_count' => $newJoinCount,
                    'terminating_employees_count' => $terminatingCount,
                    'terminated_employees_count' => $terminatedCount,
                ]);
                logInfo("Daily counts for {$today} '{$nepaliDate}' have been stored. {$activeCount} {$newJoinCount}  {$terminatingCount}  {$terminatedCount}");
            }
            $this->info('Daily employee count stored.');
        } catch (Exception $err) {
            logError('Error while storing data', $err);
        }
    }

    public function getActiveEmployeesCount()
    {
        return Employee::leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereNull('employees.deleted_at')
            ->count();
    }

    public function getNewEmployeesCount()
    {
        return Employee::leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereDate('employee_org.created_at', today())
            ->count();
    }
    public function getTerminatingEmployeesCount()
    {
        $today = now()->toDateString();
        $nepaliDate = LaravelNepaliDate::from($today)->toNepaliDate();
        [$currentYear, $currentMonth, $currentDay] = explode('-', $nepaliDate);

        if (!fedexHrm()) {
            $startDate = new LaravelNepaliDate($currentYear, $currentMonth, $currentDay);
        } else {
            $startDate = new LaravelNepaliDate($currentYear, $currentMonth, $currentDay + 1);
        }

        $daysInMonth = LaravelNepaliDate::daysInMonth($currentMonth, $currentYear);
        $endDate = new LaravelNepaliDate($currentYear, $currentMonth, $daysInMonth);
        $startDateEng = $startDate->toEnglishDate();
        $endDateEng = $endDate->toEnglishDate();
        $query = Employee::leftJoin('employee_terminations as termination', 'termination.employee_id', '=', 'employees.id')
            ->where('termination.state', WorkflowState::APPROVED)
            ->whereNull('termination.deleted_at')
            ->whereBetween('termination.termination_date', [$startDateEng, $endDateEng])
            ->count();
        return $query;
    }
    public function getTerminatedEmployeesCount()
    {
        return Employee::leftJoin('employee_org', 'employee_org.employee_id', '=', 'employees.id')
            ->whereDate('employees.deleted_at', today())
            ->onlyTrashed()
            ->count();
    }
}
