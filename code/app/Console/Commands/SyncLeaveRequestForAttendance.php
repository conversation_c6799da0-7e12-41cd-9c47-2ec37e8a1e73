<?php

namespace App\Console\Commands;

use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\EmployeeLeaveDetail;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandCommand;

class SyncLeaveRequestForAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-leave-request-for-attendance
                            {nepYear : The Nepali year (e.g., 2081)}
                            {nepMonth : The Nepali month (1-12)}
                            {employeeId? : Optional employee ID to sync}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync leave requests into attendance records for a given Nepali year and month';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $nepYear = $this->argument('nepYear');
        $nepMonth = $this->argument('nepMonth');
        $employeeId = $this->argument('employeeId');

        // Validate nepMonth
        if (!is_numeric($nepMonth) || $nepMonth < 1 || $nepMonth > 12) {
            $this->error("Invalid nepMonth. It should be a number between 1 and 12.");
            return CommandCommand::FAILURE;
        }

        // Pad month to 2 digits
        $nepMonth = str_pad($nepMonth, 2, '0', STR_PAD_LEFT);

        // Validate employeeId if provided
        if ($employeeId && !Employee::find($employeeId)) {
            $this->error("Invalid employeeId: $employeeId. No employee found.");
            return CommandCommand::FAILURE;
        }

        $this->info("Processing for Nepali Year: $nepYear, Month: $nepMonth" . ($employeeId ? ", Employee ID: $employeeId" : ""));
        $this->info("");

        $monthlyAttendance = Attendance::where('date_np', 'like', "$nepYear-$nepMonth-%")
            ->when($employeeId, fn($query) => $query->where('employee_id', $employeeId))
            ->get();

        $employeeIds = $monthlyAttendance->pluck('employee_id')->toArray();
        $nepDates = $monthlyAttendance->pluck('date_np')->toArray();

        $leaveDetails = EmployeeLeaveDetail::whereIn('employee_id', $employeeIds)
            ->whereIn('nep_date', $nepDates)
            ->get();

        foreach ($monthlyAttendance as $attendance) {
            $leaveDetail = $leaveDetails->where('employee_id', $attendance->employee_id)
                ->where('nep_date', $attendance->date_np)
                ->first();

            $leaveType = LeaveType::where('id', $leaveDetail?->leave_type_id)->where('paid', 1)->exists();

            if ($leaveDetail) {
                $this->info("Updating attendance for Employee ID: {$attendance->employee_id} on Date: {$attendance->date_np}");
                $attendance->leave_request_id = $leaveDetail->leave_request_id;
                $attendance->leave_status = $leaveType ? 1 : 0;
                $attendance->save();
                $this->info("→ Updated successfully");
                $this->info("");
            }
        }

        $this->info("Done syncing leave requests.");
        return CommandCommand::SUCCESS;
    }
}
