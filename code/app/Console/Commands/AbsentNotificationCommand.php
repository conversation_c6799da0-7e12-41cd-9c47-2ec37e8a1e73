<?php

namespace App\Console\Commands;

use App\Jobs\FirebaseFcmNotificationJob;
use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Models\Leaves\LeaveRequest;
use Carbon\Carbon;
use Illuminate\Console\Command;

class AbsentNotificationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify-absent-employees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Notification for the employees if the employee was absent and hasn't applied leave request yet";

    /**
     * Execute the console command.
     */
    public function handle(\App\Http\Repositories\Configs\Interfaces\INotificationRepository $notificationRepo)
    {
        \logCronInfo("Notifying absent employees");
        $yesterdayDay =  Carbon::yesterday()->format('l');
        $yesterdayDate = Carbon::yesterday()->format('Y-m-d');
        $absentEmployeeIds = Attendance::where('date_en', $yesterdayDate)->where('status', 'like', '%Absent%')->select('employee_id')->distinct()->pluck('employee_id')->toArray();
        $leaveRequestedEmployeeIds = LeaveRequest::where([
            ['start_date', '<=', $yesterdayDate],
            ['end_date', '>=', $yesterdayDate]
        ])
            ->whereIn('employee_id', $absentEmployeeIds)
            ->select('employee_id')
            ->distinct()
            ->pluck('employee_id')->toArray();

        $absentEmployeeIds = array_diff($absentEmployeeIds, $leaveRequestedEmployeeIds);
        $employees = Employee::find($absentEmployeeIds);
        $notificationTitle = "Apply Leave Request";
        $notificationMessage = "You were absent on the date $yesterdayDate ($yesterdayDay). Please apply your leave request.";
        $notificationMeta = [
            'type' => "apply-leave-request",
            'date' => $yesterdayDate
        ];

        foreach ($employees as $employee) {
            $notificationRepo->createAndSendDeviceNotification(
                $employee?->user,
                $notificationTitle,
                $notificationMessage,
                "#",
                "#",
                $notificationMeta
            );
            // Send email or notification to the employee
            \logCronInfo("Notification sent to " . $employee->name);
        }
        \logCronInfo("Absent Notification sent to total of " . count($absentEmployeeIds) . " employees");
        echo "Absent Notification sent to " . count($absentEmployeeIds) . " employees\n\n";
    }
}
