<?php

namespace App\Console\Commands;

use App\Models\Tickets\TicketTempOldDetail;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DeleteTicketOldTempData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-ticket-old-temp-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete the old temporary ticket data from ticket_old_temp_details table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = 30; // Change this to the number of days you want
        $specifiedDaysAgo = Carbon::now()->subDays($days);
        TicketTempOldDetail::where('created_at', '<', $specifiedDaysAgo)->delete();
        $this->info("Temorary data of tickets older than $days days are deleted");
    }
}
