<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ClearOldSnapshots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'snapshot:clear-old {type} {--days=90 : Retain this many days of data} ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old snapshots older than N days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');

        if (!in_array($type, ['attendance', 'employee'])) {
            $this->error('Invalid type. Supported types: attendance, employee');
            return;
        }

        $days = (int) $this->option('days');

        switch ($type) {
            case 'attendance':
                $repo = new \App\Http\Repositories\Snapshots\GenerateAttendanceComplianceSnapshotRepository;
                $repo->clearOldSnapshots($days);
                $this->info('Deleted old attendance_snapshots');
                break;
            case 'employee':
                $repo = new \App\Http\Repositories\Snapshots\GenerateEmployeeCountSnapshotRepository();
                $repo->clearOldSnapshots($days);
                $this->info('Deleted old employee_snapshots');
                break;
        }
    }
}
