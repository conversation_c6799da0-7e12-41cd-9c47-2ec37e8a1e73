<?php

namespace App\Console\Commands;

use App\Http\Repositories\SyncDeviceAttendanceRepository;
use Illuminate\Console\Command;

class SyncDeviceAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync-device-attendance:sync-to-db {date?} {device_ip?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync the device attendance to db.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $stringDate = strToTime($this->parseDate($this->argument('date')));
        $date = $stringDate ? date('Y-m-d', $stringDate) : null;
        $syncRepo = new SyncDeviceAttendanceRepository;

        $syncRepo->syncDeviceAttendanceToDB($date, $this->parseDeviceIp($this->argument('device_ip')));
    }

    /**
     * Parse the date input.
     *
     * @param  string|null  $date
     * @return string|null
     */
    protected function parseDate($date)
    {
        return $date ? substr($date, strlen('date=')) : null;
    }

    /**
     * Parse the device ip input.
     *
     * @param  string|null  $deviceIp
     * @return string|null
     */
    protected function parseDeviceIp($deviceIp)
    {
        return $deviceIp ? substr($deviceIp, strlen('device_ip=')) : null;
    }
}
