<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Spatie\Activitylog\Models\Activity;

class DeleteOldUserActivityRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-old-user-activity-records';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete records from the user activity logs that are older than 1 year.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $oneYearAgo = Carbon::now()->subYear();

        // Delete records where created_at is older than 1 year
        $deletedCount = Activity::where('created_at', '<', $oneYearAgo)->delete();

        // Output the number of deleted records
        $this->info("Deleted {$deletedCount} old timeline records.");
    }
}
