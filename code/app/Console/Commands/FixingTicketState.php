<?php

namespace App\Console\Commands;

use App\Models\RequestTicket;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixingTicketState extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fixing-ticket-state {table} {state}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $table = $this->argument('table');
        $state = $this->argument('state');

        $models = DB::table($table)->where('state', $state)->get();

        $modelIds = $models->pluck('id')->toArray();

        $this->info("Model Ids: " . implode(',', $modelIds));
        $this->info("State: " . $state);
        $this->info("Count: " . $models->count());

        $workflow = $models->first()->workflow;

        RequestTicket::query()
            ->whereIn('model_id', $modelIds)
            ->where("workflow", $workflow)
            ->update(['state' => $state]);

        $this->info("Tickets updated");
    }
}
