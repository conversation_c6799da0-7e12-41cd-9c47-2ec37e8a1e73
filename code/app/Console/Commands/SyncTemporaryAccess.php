<?php

namespace App\Console\Commands;

use App\Http\Services\Configs\TemporaryAccessService;
use Illuminate\Console\Command;

class SyncTemporaryAccess extends Command
{
    protected $signature = 'temporary-access:sync';

    protected $description = 'Activate and expire temporary access based on from/to dates';

    public function handle(TemporaryAccessService $service): int
    {
        $this->info('Running temporary delegations sync ...');

        $service->runCron();

        $this->info('Temporary delegations sync completed.');

        return self::SUCCESS;
    }
}
