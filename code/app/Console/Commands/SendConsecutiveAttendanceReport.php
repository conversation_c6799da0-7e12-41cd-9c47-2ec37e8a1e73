<?php

namespace App\Console\Commands;

use App\Livewire\HrAdmin\Attendance\ConsecutiveAttendanceIssue;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendConsecutiveAttendanceReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-consecutive-attendance-report';

    /**
     * The console command description.
     * 
     * @var string
     */
    protected $description = 'Sends Consecutive Attendance Report Email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("Starting Consecutive Attendance Report dispatch ....");


        $controller = app(ConsecutiveAttendanceIssue::class);

        $controller->startDate = Carbon::now()->subDays(2)->format('Y-m-d');
        $controller->endDate = Carbon::now()->subDays(1)->format('Y-m-d');

        $controller->sendEmailOfConsecutiveAttendanceReport();

        $this->info("Consecutive Attendance Report send Successfully");
    }
}
