<?php

namespace App\Console\Commands;

use App\Http\Repositories\Configs\Interfaces\ICustomNotificationRepository;
use App\Http\Services\NotificationTopicService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendScheduledNotificationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-scheduled-notification-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(
        ICustomNotificationRepository $customNotificationRepo,
    ) {
        // dd(Carbon::now()->format('Y-m-d H:i'));
        $scheduledNotifications = $customNotificationRepo->getScheduledNotifications(Carbon::now());
        foreach ($scheduledNotifications as $notification) {
            dispatch(new \App\Jobs\CustomNotificationJob($notification));
        }
    }
}
