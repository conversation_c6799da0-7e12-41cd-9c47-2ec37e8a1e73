<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Repositories\SyncRepository;

class getBreakInOut extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attn:breakinout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the details of break in and out of employees';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $syncRepo = new SyncRepository;
        $breakOutput = $syncRepo->getBreakInOut();
        dd($breakOutput);
    }
}
