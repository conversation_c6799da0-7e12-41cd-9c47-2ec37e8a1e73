<?php

namespace App\Console\Commands;

use App\Models\configs\FiscalYear;
use Carbon\Carbon;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class CheckFiscalYear extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:fiscal-year {eng_date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To check the fiscal year and set it as active.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $date = $this->argument('eng_date') ? substr($this->argument('eng_date'), strlen('eng_date=')) : null;
        $today = $date ? $date : Carbon::today();
        $todayNepaliDate = LaravelNepaliDate::from($today)->toNepaliDate('Y-m-d');
        $fiscalYear = FiscalYear::where([['start_date', '<=', $todayNepaliDate], ['end_date', '>=', $todayNepaliDate]])->first();
        $activeFiscalYear = FiscalYear::where('is_active', 1)->first();
        if ($fiscalYear && ($fiscalYear?->id != $activeFiscalYear?->id)) {
            DB::beginTransaction();
            try {
                $fiscalYear->is_active = 1;
                $fiscalYear->save();
                DB::table('sessions')->truncate();
                Redis::flushall();
                DB::commit();
            } catch (Exception $e) {
                DB::rollback();
                logError('Failed to update fiscalyear.', $e);
            }
        }
    }
}
