<?php

namespace App\Console\Commands;

use App\Http\Repositories\Auth\Interfaces\IDeviceLogRepository;
use Illuminate\Console\Command as ConsoleCommand;
use Symfony\Component\Console\Command\Command;

class DeactivateInactiveDevices extends ConsoleCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:deactivate-inactive-devices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deactivate devices that have been inactive for a specified number of days [days of expiry of refresh token]';

    /**
     * Execute the console command.
     */
    public function handle(IDeviceLogRepository $deviceLogRepo)
    {
        // expiring the devices that hasn't used in the timeframe of refresh token expiry
        $days = config('jwt.refresh_ttl') / 60 / 24;

        $cutoffDate = \Carbon\Carbon::now()->subDays($days);
        $deviceLogRepo->deactivateDevices($cutoffDate);

        ConsoleCommand::info("Deactivated device(s) inactive since {$cutoffDate->toDateTimeString()}.");

        return Command::SUCCESS;
    }
}
