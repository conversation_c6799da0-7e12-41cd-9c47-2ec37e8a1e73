<?php

namespace App\Console\Commands;

use App\Models\Employee\Employee;
use App\Models\Leaves\Attendance;
use App\Models\TimeRequest;
use Illuminate\Console\Command;
use Carbon\Carbon;

class MissedPunchNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify-missed-punch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Notification for employee if they had missed the punch and haven't applied the time request yet";

    /**
     * Execute the console command.
     */
    public function handle(\App\Http\Repositories\Configs\Interfaces\INotificationRepository $notificationRepo)
    {
        \logCronInfo("Notifying employees about missed punch");
        $yesterdayDay =  Carbon::yesterday()->format('l');
        $yesterdayDate = Carbon::yesterday()->format('Y-m-d');
        $missedPunchEmployeeIds = Attendance::where('date_en', $yesterdayDate)->where('status', 'like', '%Missed Punch%')->select('employee_id')->distinct()->pluck('employee_id')->toArray();
        $timeRequestedEmployeeIds = TimeRequest::where('date', '=', $yesterdayDate)
            ->whereIn('employee_id', $missedPunchEmployeeIds)
            ->select('employee_id')
            ->distinct()
            ->pluck('employee_id')->toArray();

        $missedPunchedEmployeeIds = array_diff($missedPunchEmployeeIds, $timeRequestedEmployeeIds);
        $employees = Employee::find($missedPunchedEmployeeIds);
        $notificationTitle = "Apply Time Request";
        $notificationMessage = "You missed the punch on the date $yesterdayDate ($yesterdayDay). Please apply your time request.";
        $notificationMeta = [
            'type' => "apply-time-request",
            'date' => $yesterdayDate
        ];
        $notification = new \App\Notifications\TaskTicket(
            $notificationTitle,
            $notificationMessage,
            "#",
            "#",
            $notificationMeta
        );
        foreach ($employees as $employee) {
            $notificationRepo->createAndSendDeviceNotification(
                $employee?->user,
                $notificationTitle,
                $notificationMessage,
                "#",
                "#",
                $notificationMeta
            );
            \logCronInfo("Missed punch notification sent to " . $employee->name);
        }

        \logCronInfo("Missed Punches Notification sent to total of " . count($missedPunchedEmployeeIds) . " employees");

        $this->info("Missed punch notification sent to total of " . count($missedPunchedEmployeeIds) . " employees");
    }
}
