<?php

namespace App\Console\Commands;

use App\Models\Config\MultipleAccess;
use Illuminate\Console\Command;

class MultipleAccessExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:multiple-access-expiration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command to expire the access';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        \logCronInfo("Expiring the multiple access.");
        $multipleAccess = MultipleAccess::where("to", "<=", date("Y-m-d"))
                                        ->update([
                                            "expired" => "1"
                                        ]);
        \logCronInfo("Total Multiple access expired: " . $multipleAccess);
        $this->info("Total Multiple access expired: " . $multipleAccess);
    }
}
