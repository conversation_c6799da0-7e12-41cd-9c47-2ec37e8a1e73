<?php

namespace App\Console\Commands;

use App\Http\Helpers\Enums\HRMPackages;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\IopsRepository;
use App\Http\Repositories\Reports\AttendanceRepository;
use App\Jobs\EmployeeTerminationEmailJob;
use App\Models\Employee\EmployeeOrg;
use App\Models\Leaves\Attendance;
use App\Models\RequestTicket;
use App\Models\Termination\EmployeeTermination;
use Carbon\Carbon;
use Codebright\CugManagement\CugManagement;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CheckEmployeeTermination extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:termination-dates {employee_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for terminated employees and perform different transaction';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $iopsRepo = new IopsRepository;

        logCronInfo("Checking employee termination");
        $today = Carbon::now()->toDateString();
        $terminatingQuery = EmployeeTermination::withActiveEmployees()
            ->with(['employees'  => function ($query) {
                $query->whereNull('deleted_at');
            }], 'employees.company:id,name,code', 'employees.branch:id,name', 'employees.organizationInfo:id,employee_id,email', 'employees.designation')
            ->where([["state", "=", WorkflowState::APPROVED]]);

        $employeeId = $this->parseEmployeeId($this->argument('employee_id'));
        if ($employeeId) {
            $terminatingQuery->where("employee_id", "=", $employeeId);
        } else {
            if (fedexHrm())
                $terminatingQuery->whereDate('termination_date', '<=', $today);
            else
                $terminatingQuery->whereDate('termination_date', '<', $today);
        }

        $terminatingEmployees = $terminatingQuery->get();

        $dataToSendEmail = [];

        foreach ($terminatingEmployees as $terminatedEmployee) {
            if (!isset($terminatedEmployee->employee)) {
                continue;
            }

            $employeeOrg = EmployeeOrg::where("employee_id", "=", $terminatedEmployee->employee_id)->first();

            if ($employeeOrg) {
                $employeeOrg->termination_request_date = $terminatedEmployee->termination_request_date;
                $employeeOrg->termination_date = $terminatedEmployee->termination_date;
                $employeeOrg->termination_reason = $terminatedEmployee->termination_reason;
                $result = $employeeOrg->save();
                logCronInfo('Employee terminated data updated: ' . $result);

                $requestTickets = RequestTicket::where('current_owner_id', $employeeOrg->employee->id)->get();
                foreach ($requestTickets as $ticket) {
                    $stateHistory = $ticket->model->stateHistory;
                    $count = count($stateHistory);
                    $employee = $stateHistory[$count - 1]?->employee;

                    // check the last employee who is not terminated in case if there is multiple performers terminated
                    while ($employee?->deleted_at && $count == 0) {
                        $count--;
                        $employee = $stateHistory[$count - 1]?->employee;
                    }

                    $title = \ticketNotificationTitle($ticket->workflow) . " - {$ticket->employee?->name}";
                    $notification = new \App\Notifications\TaskTicket(
                        $title,
                        "Your next owner '{$ticket->model->currentOwner->name}' for this ticket has been terminated. Please change the owner of the ticket",
                        route('ticketPage', ['workflow' => $ticket->workflow, 'requestId' => $ticket->model_id]),
                        "View",
                    );
                    $employee->user->notify($notification);
                }
                $employee = $employeeOrg->employee;
                if ($employee && $employee->getJobId()) {
                    $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
                    try {
                        $jobSeatRepo->decreaseOnNotice([
                            'company_id' => $employee->company_id,
                            'branch_id' => $employeeOrg->branch_id,
                            'department_id' => $employeeOrg->department_id,
                            'job_id' => $employee->getJobId()
                        ]);
                    } catch (\Exception $e) {
                        logError("Error for employee_id: " . $employee->id . ", company_id: " . $employee->company_id . ", branch_id: " . $employeeOrg->branch_id . ", department_id: " . $employeeOrg->department_id . ", job_id: " . $employee->getJobId() . ": " . $e->getMessage());
                    }
                }
            } else {
                return  $this->error("No EmployeeOrg found for employee ID: {$terminatedEmployee->employee_id}");
            }
            if (vianetHrm() || konnectHrm()) {
                $response = $iopsRepo->terminateEmployee($employeeOrg->employee->id, $terminatedEmployee->termination_date);
                if (!$response) {
                    logCronError("Failed to terminate employee in HRM system of employee_id: {$employeeOrg->employee_id}");
                } else {
                    logCronInfo("Employee terminated from iops repository");
                }
            }

             if (HRMPackages::CUG_MANAGEMENT) {
                 $params = [
                     "employee_id"   => $terminatedEmployee->employee_id,
                 ];
                 $freeCug = CugManagement::freeCug($params);
                 logCronInfo("Free CUG Response => ". $freeCug['message'] ?? "N/A");
             }

            // Delete the future date attendance.
            $terminationDate = Carbon::parse($employeeOrg->termination_date);
            $attendanceRepository = new AttendanceRepository();
            $attendanceRepository->deleteFutureDateAttendance($employeeOrg->employee_id, $terminationDate);

            $jobDesignation = explode('/', $terminatedEmployee->employee->getJobDesignation());
            $designation = trim($jobDesignation[1] ?? "N/A");

            $dataToSendEmail[] = [
                'employee_code' => $terminatedEmployee->employee->companyEmpCode,
                'name' => $terminatedEmployee->employee->name,
                'designation' => $designation,
                'email' => $terminatedEmployee->employee->organizationInfo->email,
                'branch' => $terminatedEmployee->employee->branch->name,
            ];

            $result = $employeeOrg->employee->user->delete();
            logCronInfo('User deleted: ' . $result);
            $result = $employeeOrg->employee->delete();
            logCronInfo('Employee deleted: ' . $result);
            $result = $employeeOrg->delete();
            logCronInfo('Organizational info deleted: ' . $result);
        }

        logCronInfo("data to send", $dataToSendEmail);
        if (!empty($dataToSendEmail) && vianetHrm()) {
            $toEmail = config('app.mailHR');
            $ccEmail = !empty(config('app.mailSystem')) ? [config('app.mailSystem')] : [];
            if (vianetHrm() && !empty($toEmail)) {
                EmployeeTerminationEmailJob::dispatchSync($dataToSendEmail, $toEmail, $ccEmail, $today);
                logCronInfo("Email sent to HR", [$dataToSendEmail, $toEmail, $ccEmail, $today]);
            } else {
                $this->info('Employee termination check completed. Please provide valid email address for sending the email.');
            }
        } else {
            $this->info('No Employee Termination Found and employee termination check completed.');
        }
        
        Cache::store('employee')->flush();
        Cache::store('arflow')->flush();
        logCronInfo("Employee cache deleted");
        $this->info('Employee termination check completed.');
        logCronInfo("Employee termination check completed\n\n");
    }

    /**
     * Parse the employeeId input.
     *
     * @param  string|null  $employeeId
     * @return string|null
     */
    protected function parseEmployeeId($employeeId)
    {
        return $employeeId ? substr($employeeId, strlen('employee_id=')) : null;
    }
}
