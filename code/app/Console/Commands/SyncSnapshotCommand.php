<?php

namespace App\Console\Commands;

use App\Http\Repositories\Snapshots\GenerateAttendanceComplianceSnapshotRepository;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SyncSnapshotCommand extends Command
{
    protected $signature = 'snapshot {type} {date?} {--from=} {--to=} {--branch=} {--department=} {--company=} {--category=}';

    protected $description = 'Run snapshot for a day or range';

    public function handle()
    {
        $type = $this->argument('type');
        if (!in_array($type, ['attendance', 'employee'])) {
            $this->error('Invalid type. Supported types: attendance, employee');
            return;
        }
        $date = $this->argument('date');
        $from = $this->option('from');
        $to = $this->option('to');

        $branchId = $this->option('branch') ?: null;
        $departmentId = $this->option('department') ?: null;
        $companyId = $this->option('company') ?: null;
        $categoryId = $this->option('category') ?: null;

        $this->info('Running ' . $type . ' snapshots...');

        switch ($type) {
            case 'attendance':
                dispatch(new \App\Jobs\Snapshot\SyncAttendanceComplianceSnapshotJob(
                    $date,
                    $from,
                    $to,
                    $branchId,
                    $departmentId,
                    $companyId,
                    //$categoryId,
                ));
                break;
            case 'employee':
                dispatch(new \App\Jobs\Snapshot\SyncEmployeeCountSnapshotJob(
                    $date,
                    $from,
                    $to,
                    $branchId,
                    $departmentId,
                    $companyId,
                    $categoryId,
                ));
                break;
        }

        $this->info('Dispatched through job queue.');
    }
}
