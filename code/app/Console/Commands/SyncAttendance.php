<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync-attendance:sync-to-db {date?} {emp_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'It syncs the attendance from the device to the database.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $syncRepo = new \App\Http\Repositories\SyncRepository;
        // $syncRepo = config('app.environment') == "development"
        //     ? new \App\Http\Repositories\Attendance\SyncRepository
        //     : new \App\Http\Repositories\SyncRepository;

        $stringDate = strToTime($this->parseDate($this->argument('date')));
        $date = $stringDate ? date('Y-m-d', $stringDate) : null;

        $empId = $this->parseEmployeeId($this->argument('emp_id'));

        $sync = $syncRepo->syncAttendanceFromDeviceToDb($date, $empId);
        if ($sync) $this->info("Data sync successful.");
        else $this->error("Failed to sync data.");
    }

    /**
     * Parse the date input.
     *
     * @param  string|null  $date
     * @return string|null
     */
    protected function parseDate($date)
    {
        return $date ? substr($date, strlen('date=')) : null;
    }

    /**
     * Parse the employee id input.
     *
     * @param  string|null  $employeeId
     * @return string|null
     */
    protected function parseEmployeeId($employeeId)
    {
        return $employeeId ? substr($employeeId, strlen('emp_id=')) : null;
    }
}
