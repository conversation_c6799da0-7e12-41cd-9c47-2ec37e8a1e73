<?php

namespace App\Console\Commands;

use App\Models\Leaves\Attendance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanDuplicateAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:clean-duplicates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Removes duplicate attendance records by keeping only the meaningful ones (with in-time, out-time, or leave status) for each employee per date.';


    /**
     * Execute the console command.
     */
    public function handle()
    {
        $duplicateAttendances =  Attendance::query()
            ->select('employee_id', 'date_en', DB::raw('COUNT(*) as count'))
            ->groupBy('employee_id', 'date_en')
            ->having('count', '>', 1)
            ->get();

        foreach ($duplicateAttendances as $group) {
            $records = Attendance::query()
                ->where('employee_id', $group->employee_id)
                ->where('date_en', $group->date_en)
                // ->orderByDesc('updated_at') // or orderByDesc('id')
                ->get();

            $keepIds = [];

            // Check others
            foreach ($records as $record) {
                $shouldKeep = $record->in_time || $record->out_time || $record->leave_status || $record->missed_punch_status;
                $this->info("Record: {$record->id}, In Time: {$record->in_time}, Out Time: {$record->out_time}, Leave Status: {$record->leave_status}, Should Keep: {$shouldKeep}\n");

                if ($shouldKeep) {
                    $keepIds[] = $record->id;
                }
            }

            if (count($keepIds) == 0) {
                $latest = $records->shift();
                $keepIds[] = $latest->id;
            }

            // Delete all except those we want to keep
            Attendance::where('employee_id', $group->employee_id)
                ->where('date_en', $group->date_en)
                ->whereNotIn('id', $keepIds)
                ->forceDelete();

            if (count($keepIds) == count($records)) {
                $this->info('All records for Employee: ' . $group->employee_id . ' on ' . $group->date_en . ' are kept.');
            } else if (count($keepIds) == 0) {
                $this->info('No records for Employee: ' . $group->employee_id . ' on ' . $group->date_en . ' are kept. All deleted.');
            } else {
                $this->info("Cleaned duplicates for Employee: {$group->employee_id} on {$group->date_en}");
            }
        }

        $this->info('Duplicate cleanup complete.');
    }
}
