<?php

namespace App\Console\Commands;

use App\Models\configs\JobDepartment;
use App\Models\Employee\EmployeeOrg;
use App\Models\Termination\EmployeeTermination;
use App\Models\Tickets\ManpowerRequisition;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncJobSeats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-job-seats {--force : Run the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // if (!$this->option('force')) {
        //     if (!$this->confirm('This operation will modify job seat allocations based on current data and remove existing data. Do you wish to continue?')) {
        //         $this->info('Operation cancelled.');
        //         return 0;
        //     }
        // }

        // JobDepartment::truncate();
        // $this->info("Job department truncated");

        $seatCounts = EmployeeOrg::leftJoin('payslips as payslip', function ($join) {
            $join->on('payslip.employee_id', '=', 'employee_org.employee_id')
                ->where('payslip.status', '=', 'Active');
        })
            ->leftJoin('employees as emp', 'emp.id', 'employee_org.employee_id')
            ->select(
                DB::raw("COUNT(DISTINCT employee_org.employee_id) AS count"),
                'employee_org.region_id',
                'employee_org.branch_id',
                'employee_org.department_id',
                'payslip.job_id',
                'emp.company_id',
            )
            ->groupBy(
                'employee_org.region_id',
                'emp.company_id',
                'employee_org.branch_id',
                'employee_org.department_id',
                'payslip.job_id'
            )
            ->get();

        $this->info("\nFrom active employees");
        /** @var \App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface */
        $jobRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
        foreach ($seatCounts as $seatCount) {
            if ($seatCount->job_id) {
                $key = [
                    'company_id' => $seatCount->company_id,
                    'branch_id' => $seatCount->branch_id,
                    'department_id' => $seatCount->department_id,
                    'job_id' => $seatCount->job_id
                ];
                $jobRepo->deleteJobSeat($key);
                $seat = $jobRepo->upsertFromSnapshot($key, $seatCount->count, true);
                $this->info("Success for " . $seatCount->region_id . ", " . $seatCount->branch_id . ", " . $seatCount->department_id . ", " . $seatCount->job_id);
            }
        }

        $this->info("\nFrom manpower requisitions");
        $approvedManpowerRequisitions = ManpowerRequisition::where([
            ['state', 'Approved']
        ])->whereColumn('number_vacancy', '>', 'number_fulfilled')->get();
        foreach ($approvedManpowerRequisitions as $requisition) {
            $response = $jobRepo->increaseSeatPlan([
                'company_id' => $requisition->company_id,
                'branch_id' => $requisition->branch_id,
                'department_id' => $requisition->department_id,
                'job_id' => $requisition->job_id
            ], $requisition->number_vacancy - $requisition->number_fulfilled);
            $this->info("Success for " . $requisition->branch?->region_id . ", " . $requisition->branch_id . ", " . $requisition->department_id . ", " . $requisition->job_id);
        }

        $onNoticeEmployees = EmployeeTermination::leftJoin('employees as emp', 'emp.id', 'employee_terminations.employee_id')
            ->leftJoin('employee_org as org', 'org.employee_id', 'employee_terminations.employee_id')
            ->leftJoin('payslips as ps', 'ps.employee_id', 'emp.id')
            ->whereNull('emp.deleted_at')
            ->where('employee_terminations.state', 'Approved')
            ->where('ps.status', 'Active')
            ->select(
                'emp.id',
                'emp.company_id',
                'org.region_id',
                'org.branch_id',
                'org.department_id',
                'ps.job_id',
            )
            ->distinct()
            ->get();

        $this->info("\nFor termination:");
        foreach ($onNoticeEmployees as $employee) {
            if ($employee->job_id === null) {
                $this->info("Skipping employee " . $employee->id . " as job_id is null");
                continue;
            }
            $key = [
                'company_id' => $employee->company_id,
                'branch_id' => $employee->branch_id,
                'department_id' => $employee->department_id,
                'job_id' => $employee->job_id
            ];
            try {
                $response = $jobRepo->increaseOnNotice($key);
                if ($response['status'])
                    $this->info("Success for " . $employee->region_id . ", " . $employee->branch_id . ", " . $employee->department_id . ", " . $employee->job_id);
                else
                    $this->info($response['message']);
            } catch (\Exception $e) {
                $this->error("Error for " . $employee->region_id . ", " . $employee->branch_id . ", " . $employee->department_id . ", " . $employee->job_id . ": " . $e->getMessage());
            }
        }
    }
}
