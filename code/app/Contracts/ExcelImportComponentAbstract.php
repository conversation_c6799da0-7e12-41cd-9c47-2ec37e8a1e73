<?php

namespace App\Contracts;

use App\Exports\PayslipExcelSample;
use App\Http\Services\Imports\ImportHandlerFactory;
use App\Traits\WithNotify;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

abstract class ExcelImportComponentAbstract extends Component
{
    use WithFileUploads, WithNotify;

    /**
     *  General Flow To Remember
     *  Excel File -> transformArrayToAssociative() [getDateFields(), getTimeFields() apply HERE] -> headerMap() -> validation
     */

    /* ----------- UI States ---------- */
    public $excelDataValidated = false; // flag to check if the excel is validated or not; for showing the upload button if it's true
    public $excelUploaded = false;  // flag to check if the excel is uploaded or not; for showing the upload message and uploaded data
    public $requireCompany = true; // flag to check if the upload requires company or not


    /* ----------- Data ---------- */
    public array $excelData = []; // for storing the data after validation
    public array $uploadedData = []; // to show the records of uploaded data


    /* ----------- Upload Data ---------- */
    public $companyId;
    public $excelFile;


    /* ----------- Validation Messages ---------- */
    public $excelValidationMessage = ""; // 

    public $uploadValidationMessage = "";


    public function rules()
    {
        return array_filter([
            'excelFile' => 'required|file',
            'companyId' => $this->requireCompany ? 'required|exists:companies,id' : null,
        ]);
    }

    abstract public function headers(): array;
    abstract protected function getSampleFileName(): string;
    abstract protected function getExportOutputFileName(): string;
    abstract protected function getHandlerType(): string;

    public function requiredHeaders(): array
    {
        // contains keys of required headers
        return [];
    }

    public function headerMap(): array
    {
        // for mapping the headers to the general name for processing like Department Name => department, and it can be accessible by department
        return [];
    }

    public function getDateFields(): array
    {
        return [];
    }

    public function getTimeFields(): array
    {
        return [];
    }


    #[Computed(persist: true)]
    public function companyList()
    {
        return \App\Models\configs\Company::all()->pluck('name', 'id')->toArray();
    }

    public function mount()
    {
        if (!scopeAll()) {
            $this->companyId = currentEmployee()->company_id;
        }
    }

    /* ----- header helpers ------ */

    protected function normalizeHeader(string $header): string
    {
        return strtolower(trim($header));
    }

    protected function normalizeHeaderMap()
    {
        $normalizedHeaders = [];
        foreach ($this->headerMap() as $key => $value) {
            $normalizedHeaders[$this->normalizeHeader($key)] = $value;
        }
        return $normalizedHeaders;
    }

    protected function getMappedRequiredExcelHeaders(): array
    {
        return array_map(
            fn($header) => $this->normalizeHeaderMap()[$this->normalizeHeader($header)] ?? $header,
            $this->requiredHeaders()
        );
    }

    protected function getMappedOptionalExcelHeaders(): array
    {
        return array_map(
            fn($header) => $this->normalizeHeaderMap()[$this->normalizeHeader($header)] ?? $header,
            array_keys($this->getOptionalExcelHeaders())
        );
    }

    public function downloadSample()
    {
        return Excel::download(new PayslipExcelSample(
            $this->headers(),
            $this->requiredHeaders()
        ), $this->getSampleFileName());
    }

    public function uploadExcelFile()
    {
        $this->reset(
            'excelData',
            'uploadedData',
            'excelDataValidated',
            'excelUploaded',
            'excelValidationMessage',
            'uploadValidationMessage'
        );
    }


    protected function parseExcelFile()
    {
        $this->validate();

        $sheets = Excel::toArray((object)[], $this->excelFile);
        $rows = $sheets[0] ?? [];

        if (empty($rows)) {
            $this->notify('Excel file is empty')->type('error')->send();
            return false;
        }

        $headers = array_map(
            fn($h) =>  $h ? $this->normalizeHeader($h) : null,
            array_shift($rows)
        );

        foreach ($this->requiredHeaders() as $header) {
            if (!in_array($this->normalizeHeader($header), $headers, true)) {
                $this->notify("Please provide all required columns as mentioned in example")->type("error")->send();
                return false;
            }
        }

        try {
            $data = transformArrayToAssociative(
                [$headers, ...$rows],
                $this->getDateFields(),
                $this->getTimeFields()
            );

            return array_map(function ($row) {
                return collect($row)
                    ->mapWithKeys(fn($value, $key) => [
                        $this->normalizeHeaderMap()[$this->normalizeHeader($key)] ?? $this->normalizeHeader($key)
                        => $value,
                    ])
                    ->toArray();
            }, $data);
        } catch (\Exception $e) {
            $this->notify("Error occurred: " . $e->getMessage())->type("error")->send();
            return false;
        }
    }


    public function validateExcelData()
    {
        $this->uploadExcelFile();

        if (!($data = $this->parseExcelFile())) {
            return;
        }

        $handler = (new ImportHandlerFactory())->make($this->getHandlerType());

        try {
            $results = $handler->validateBatch($data, $this->getMappedRequiredExcelHeaders());

            $this->excelData = collect($results)->map(
                fn($result, $i) => [
                    'data'  => $data[$i],
                    'error' => $result['status'] ? '' : $result['message'],
                ]
            )->toArray();

            $this->excelDataValidated = collect($results)->every('status');

            $this->notify(
                $this->excelDataValidated
                    ? 'All data are validated. You can proceed to upload the data'
                    : 'Some data are not validated'
            )
                ->type($this->excelDataValidated ? 'success' : 'error')
                ->send();
        } catch (\Exception $e) {
            logError("Error creating from excel", $e);
            $this->notify("Error validating: " . $e->getMessage())->type("error")->send();
        }
    }


    public function uploadData()
    {
        if (!$this->excelDataValidated) {
            return $this->notify("Excel validation failed");
        }

        $data = collect($this->excelData)
            ->pluck('data')
            ->map(
                fn($row) => $this->requireCompany
                    ? [...$row, 'company_id' => $this->companyId]
                    : $row
            )
            ->values()
            ->toArray();

        $handler = (new ImportHandlerFactory())->make($this->getHandlerType());

        try {
            $result = $handler->processBatch($data, $this->getMappedRequiredExcelHeaders());


            // Handle case where a single message is returned; usually for insert commands; otherwise an array of results per row is expected
            if (isset($result['message'])) {
                $this->excelUploaded = $result['status'];
                $this->uploadValidationMessage = $result['message'];

                return $this->notify($result['message'])
                    ->type($result['status'] ? 'success' : 'error')
                    ->send();
            }

            $this->uploadedData = collect($result)->map(
                fn($r, $i) => [
                    'data'  => $data[$i],
                    'error' => $r['status'] ? '' : $r['message'],
                ]
            )->toArray();

            if (collect($result)->contains(fn($r) => !$r['status'])) {
                return $this->notify('Upload Failed; All items are reverted')
                    ->type('error')
                    ->send();
            }
            $this->excelUploaded = true;

            return $this->notify('All data are uploaded')
                ->type('success')
                ->send();
        } catch (\Exception $e) {
            logError("Error uploading from excel", $e);
            $this->uploadValidationMessage = "Error Uploading: " . $e->getMessage();
            return $this->notify("Error Uploading: " . $e->getMessage())->type("error")->send();
        }
    }

    public function exportOutputData()
    {
        return Excel::download(new class($this->excelData, array_keys($this->getExcelHeaders())) implements FromArray, WithHeadings, WithStyles
        {
            private $data, $headers;

            public function __construct($data, $headers)
            {
                $this->data = $data;
                $this->headers = $headers;
            }

            public function array(): array
            {
                return array_map(fn($item) => [
                    ...(array_intersect_key($item['data'], array_flip($this->headers))),
                    'error' => $item['error'] ?? ''
                ], $this->data);
            }

            public function headings(): array
            {
                return array_map(fn($header) => ucwords($header), [
                    ...$this->headers,
                    'error'
                ]);
            }

            public function styles(Worksheet $sheet)
            {
                $styles = [];

                $styles[1] = [
                    'font' => [
                        'bold' => true,
                    ],
                ];

                foreach ($this->data as $index => $row) {
                    if (!empty($row['error'])) {
                        // +2 because row index in Excel starts at 1 and we have 1 row for headings
                        $styles[$index + 2] = [
                            'fill' => [
                                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                'color' => ['rgb' => 'FF9999'], // light red
                            ],
                        ];
                    }
                }

                return $styles;
            }
        }, $this->getExportOutputFileName());
    }
}
