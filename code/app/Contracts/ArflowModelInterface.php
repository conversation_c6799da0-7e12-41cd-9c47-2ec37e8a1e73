<?php

namespace App\Contracts;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection as SupportCollection;

interface ArflowModelInterface
{
  /**
   * Generally, get the employee who created the tickets (relationship)
   * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
   */
  public function employee(): BelongsTo;

  /**
   * Get the transition performers relationship.
   * 
   * @return \Illuminate\Database\Eloquent\Relations\HasMany
   */
  public function transitionPerformers(): HasMany;

  /**
   * Check if the current logged in employee is the current owner of the ticket or not
   */
  public function isCurrentOwner(): bool;

  /**
   * Get the current owner relationship through request ticket table
   * 
   * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
   */
  public function currentOwner(): HasOneThrough;

  /**
   * Get the ticket associated with the model
   * 
   * @return \Illuminate\Database\Eloquent\Relations\MorphOne
   */
  public function requestTicket(): MorphOne;

  /**
   * Get the state history relationship  of the first supported workflow ordered by desc.
   *
   * @return \Illuminate\Database\Eloquent\Relations\HasMany
   */
  public function stateHistory(): HasMany;

  /**
   * Get the performer states for the workflow history.
   *
   * @return \Illuminate\Support\Collection
   */
  public function performerHistory(): SupportCollection;

  /**
   * Check if the current user can approve the model.
   *
   * @return bool
   */
  public function canApprove(): bool;

  /**
   * Check if the current user can verify the model.
   *
   * @return bool
   */
  public function canVerify(): bool;

  /**
   * Check if the current user can reject the model.
   *
   * @return bool
   */
  public function canReject(): bool;

  /**
   * Check if the current user can cancel the model.
   *
   * @return bool
   */
  public function canCancel(): bool;

  /**
   * Check if the current user can review the model.
   *
   * @return bool
   */
  public function canReview(): bool;

  /**
   * Check if the current user can send the model for review.
   *
   * @return bool
   */
  public function canSendForReview(): bool;

  /**
   * Check if the current user can escalate the model.
   *
   * @return bool
   */
  public function canEscalate(): bool;

  /**
   * Check if the current user can return the model.
   *
   * @return bool
   */
  public function canReturn(): bool;

  /**
   * Check if the current user can recall the model.
   *
   * @return bool
   */
  public function canRecall(): bool;

  /**
   * Check if the current user can reopen the model.
   *
   * @return bool
   */
  public function canReopen(): bool;

  /**
   * Check if the current user can change the next owner of the model.
   *
   * @return bool
   */
  public function canChangeNextOwner(): bool;

  /**
   * Get a collection of next owners for the model.
   *
   * @param int|string|null $employeeId
   * @param bool $isSubmitting
   * @param string $workflow
   * 
   * @return Collection|array
   */
  public function getNextOwners(
    int | string | null $employeeId = null,
    bool $isSubmitting = false,
    string $workflow = "",
  ): Collection | array;

  /**
   * Get the reviewers for the model.
   *
   * @param int|string|null $employeeId
   * @return Collection
   */
  public function getReviewers(int | string | null $employeeId = null): Collection;

  /**
   * Get the next owners for the model for changing or escalation.
   *
   * @param int|string|null $employeeId
   * @param bool $forEscalation
   * @return Collection
   */
  public function getNextOwnersForChanging(int | string | null $employeeId = null, bool $forEscalation = false): Collection | array;

  /**
   * Get a collection of previous owners for the model.
   *
   * @return \Illuminate\Support\Collection
   */
  public function getPreviousOwners(): SupportCollection;

  /**
   * Get the role of the next owner for the model.
   *
   * @param bool $isSubmitting
   * @return string string as in WorkflowPerformer
   */
  public function nextOwnerRole(bool $isSubmitting = false): string;

  /**
   * Get the initiator ID for the given state.
   *
   * @param string $state
   * @return int | null
   */
  public function initiatorId(string $state): ?int;

  public function getPerformer(string $state);

  public function hasNavigation(): bool;
}
