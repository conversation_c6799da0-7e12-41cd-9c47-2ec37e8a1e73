<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Livewire\Employees\Edf;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Tickets\EdfRequest;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ApproveEdfFromPayslipRequest implements TransitionActionContract
{
    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        $edf = EdfRequest::where('payslip_request_id', $this->model->id)->first();
        if ($edf) {
            $ticketRepo = new TicketRepository;
            $ticket = $edf->requestTicket;
            if ($this->to === WorkflowState::APPROVED) {
                logInfo("Transitioning state to approved from action");
                $ticketRepo->ticketTransition($edf, $ticket, WorkflowState::APPROVED, "Payslip Approved");
                $ticket->current_owner_id = null;
                $ticket->current_owner_role = null;
                $ticket->save();
                logInfo("Ticket transition completed successfully");
            } else if ($this->to === WorkflowState::REJECTED) {
                logInfo("Transition state to rejected from action");
                $ticketRepo->ticketTransition($edf, $ticket, WorkflowState::REJECTED, "Payslip Rejected");
                $ticket->current_owner_id = null;
                $ticket->current_owner_role = null;
                $ticket->save();
                logInfo("Ticket transition completed successfully");
            }
        }
    }

    public function failed(): void
    {
        Log::error("Failed to create payslip ticket from action after endorsing");
        // Handle any cleanup or error logging here if the action fails.
    }
}
