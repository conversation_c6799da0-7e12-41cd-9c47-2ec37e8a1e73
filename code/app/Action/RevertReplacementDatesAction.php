<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Models\Leaves\LeaveOption;
use App\Models\Leaves\ReplacementLeaveDate;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RevertReplacementDatesAction implements TransitionActionContract
{
    private ArflowModelInterface&Model $model;
    private string $from;
    private string $to;
    private array $parameters;

    /** Map target state => which statuses to revert */
    private const STATUS_MAP = [
        'Reverted'  => ['Used'],
        'Rejected'  => ['Pending'],
        'Cancelled' => ['Pending'],
    ];

    /** Final status after reverting */
    private const REVERT_TO_STATUS = 'Not Used';

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        // Only proceed for state transitions we care about
        $targetStatuses = self::STATUS_MAP[$this->to] ?? null;
        if (!$targetStatuses) {
            Log::info('RevertReplacementDatesAction: No action for state.', [
                'model_id' => $this->model->id ?? null,
                'to'       => $this->to,
            ]);
            return;
        }

        $leaveOptionExist = LeaveOption::query()
            ->whereKey($this->model->leave_option_id)
            ->exists();

        if (!$leaveOptionExist) {
            Log::warning('RevertReplacementDatesAction: Leave option does not support replacement.', [
                'leave_request_id'  => $this->model->id ?? null,
                'leave_option_id'   => $this->model->leave_option_id ?? null,
                'to'                => $this->to,
            ]);
            return;
        }

        DB::transaction(function () use ($targetStatuses) {
            $affected = ReplacementLeaveDate::query()
                ->where('leave_request_id', $this->model->id)
                ->whereIn('status', $targetStatuses)
                ->update([
                    'status'           => self::REVERT_TO_STATUS,
                    'leave_request_id' => null,
                    'updated_at'       => now(),
                ]);

            Log::info('RevertReplacementDatesAction: Replacement dates reverted.', [
                'leave_request_id' => $this->model->id,
                'employee_id'     => $this->model->employee_id ?? null,
                'to'              => $this->to,
                'from'            => $this->from,
                'reverted_from'   => $targetStatuses,
                'reverted_to'     => self::REVERT_TO_STATUS,
                'rows_affected'   => $affected,
            ]);
        });
    }

    public function failed(): void
    {
        Log::error('RevertReplacementDatesAction failed.', [
            'model_id' => $this->model->id ?? null,
            'to'       => $this->to ?? null,
            'from'     => $this->from ?? null,
        ]);
    }
}
