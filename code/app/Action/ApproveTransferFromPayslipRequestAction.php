<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Models\EmployeeTicket\EmployeeTicket;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ApproveTransferFromPayslipRequestAction implements TransitionActionContract
{
    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        $transferId = $this->model->employee_ticket_id;
        if (!$transferId) {
            logError("From Action: Transfer Id don't exist for this payslip");
            return;
        }
        $transfer = EmployeeTicket::find($transferId);
        if (!$transfer) {
            throw new Exception("From Action: Transfer ticket don't exist for this payslip");
        }

        $ticketRepo = new TicketRepository;

        $ticket = $transfer->requestTicket;
        if ($this->to === WorkflowState::APPROVED) {
            logInfo("Transitioning state to approved from action");
            $ticketRepo->ticketTransition($transfer, $ticket, WorkflowState::APPROVED, "Payslip Approved");
            $ticket->current_owner_id = null;
            $ticket->current_owner_role = null;
            $ticket->save();
            logInfo("Ticket transition completed successfully");
            $response = $ticketRepo->approveRequest($transfer);
            if (!$response['status']) {
                if($response['message']) {
                    logError($response['message']);
                    throw new Exception($response['message']);
                }
                throw new Exception("Could not approve request from action");
            }
            logInfo("Ticket approved successfully");

            // send notification
            TicketRepository::sendNotification(
                $transfer,
                $this->from,
                $this->to,
                currentEmployee()?->id,
                "",
                $ticket->submitted_by,
                "Submitter"
            );
            logInfo("Notification sent successfully");
        } else if ($this->to === WorkflowState::REJECTED) {
            logInfo("Transition state to rejected from action");
            $ticketRepo->ticketTransition($transfer, $ticket, WorkflowState::REJECTED, "Payslip Rejected");
            $ticket->current_owner_id = null;
            $ticket->current_owner_role = null;
            $ticket->save();
            logInfo("Ticket transition completed successfully");

            // send notification
            TicketRepository::sendNotification(
                $transfer,
                $this->from,
                $this->to,
                currentEmployee()?->id,
                "",
                $ticket->submitted_by,
                "Submitter"
            );
            logInfo("Notification sent successfully");
        } else if ($this->to === WorkflowState::CANCELLED) {
            logInfo("Transition state to cancel from action");
            $ticketRepo->ticketTransition($transfer, $ticket, WorkflowState::CANCELLED, "Payslip Cancelled");
            $ticket->current_owner_id = null;
            $ticket->current_owner_role = null;
            $ticket->save();
            logInfo("Ticket transition completed successfully");

            // send notification
            TicketRepository::sendNotification(
                $transfer,
                $this->from,
                $this->to,
                currentEmployee()?->id,
                "",
                $ticket->submitted_by,
                "Submitter"
            );
            logInfo("Notification sent successfully");
        }
    }

    public function failed(): void
    {
        Log::error("Failed to create payslip ticket from action after endorsing");
        // Handle any cleanup or error logging here if the action fails.
    }
}
