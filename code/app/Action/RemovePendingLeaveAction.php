<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class RemovePendingLeaveAction implements TransitionActionContract
{
    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        $employeeLeave = \App\Models\Leaves\EmployeeLeave::where('employee_id', $this->model->employee_id)
            ->where('leave_type_id', $this->model->leave_type_id)
            ->where('fiscal_year_id', $this->model->fiscal_year_id)
            ->first();

        if ($employeeLeave) {
            $employeeLeave->pending_leave -= $this->model->num_days;
            if ($employeeLeave->pending_leave < 0) {
                $employeeLeave->pending_leave = 0;
            }
            $employeeLeave->save();
            \logInfo("Pending employee leave cleared");
        }
        \logError("Employee Leave not found for clearing pending leave");
    }

    public function failed(): void
    {
        // Handle any cleanup or error logging here if the action fails.
    }
}
