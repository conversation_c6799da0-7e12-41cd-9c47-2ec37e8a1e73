<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class SendNotificationAction implements TransitionActionContract
{
  private ArflowModelInterface $model;
  private string $from;
  private string $to;
  private array $parameters;

  public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
  {
    $this->model = $model;
    $this->from = $from;
    $this->to = $to;
    $this->parameters = $parameters;
  }

  public function handle(): void
  {
    // $ticket = $this->model->requestTicket;
    // $recipient_id = $ticket->current_owner_id;
    // if (ArflowHelper::getFinalStates($ticket->workflow)) {
    //   $recipient_id = $ticket->employee_id;
    // }
    // $message = "";
    // dd($ticket->current_owner_id, $ticket->employee_id, $recipient_id);
    // Send a notification when the transition is successful.
  }

  public function failed(): void
  {
    // Handle any cleanup or error logging here if the action fails.
  }
}
