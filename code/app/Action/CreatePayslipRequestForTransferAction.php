<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Repositories\PayslipApprovalRepository;
use App\Models\RequestTicket;
use App\Models\Tickets\TicketDocument;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class CreatePayslipRequestForTransferAction implements TransitionActionContract
{
    private ArflowModelInterface $model;
    private string $from;
    private string $to;
    private array $parameters;

    public function boot(StateableModelContract &Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->model = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        $metaData = $this->model->metaData->pluck('meta_value', 'meta_key');
        if ($metaData['payslip_change']) {
            $payslipRepo = new PayslipApprovalRepository;
            $response = $payslipRepo->createPayslipFormTransfer($metaData['new_payslip_details'], $this->model->id);
            if (!$response['status']) {
                throw new Exception("Error while creating payslip request");
            }
            $payslipRequest = $response['data'];
            $requestTicket = RequestTicket::where([['workflow', $payslipRequest->workflow], ['model_id', $payslipRequest->id]])->first();
            foreach ($this->model->requestTicket->documents as $document) {
                TicketDocument::create([
                    'ticket_id' => $requestTicket->id,
                    'document' => $document->document,
                    'owner_id' => $document->owner_id,
                ]);
            }
            $item = [
                'employee_ticket_id'    => $this->model->id,
                'meta_key'              => 'payslip_id',
                'meta_value'            => $payslipRequest->id,
            ];
            $meta = \App\Models\EmployeeTicket\EmployeeTicketMeta::create($item);
            logInfo("Added meta data for employee ticket", $meta->toArray());
        }
    }

    public function failed(): void
    {
        // Handle any cleanup or error logging here if the action fails.
    }
}
