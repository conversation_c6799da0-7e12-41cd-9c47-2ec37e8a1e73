<?php

namespace App\Action;

use App\Contracts\ArflowModelInterface;
use App\Http\Helpers\ArflowHelper;
use App\Http\Helpers\Enums\WorkflowState;
use App\Http\Repositories\TicketRepository;
use App\Models\Employee\Employee;
use App\Models\Employee\EmployeeOrg;
use App\Models\EmployeeTicket\EmployeeTicket;
use App\Models\Tickets\ManpowerRequisition;
use AuroraWebSoftware\ArFlow\Contacts\StateableModelContract;
use AuroraWebSoftware\ArFlow\Contacts\TransitionActionContract;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class CancelTerminationAction implements TransitionActionContract
{
    private ArflowModelInterface $terminationTicket;
    private string $from;
    private string $to;
    private array $parameters;

    public function boot(StateableModelContract&Model $model, string $from, string $to, array $parameters = []): void
    {
        $this->terminationTicket = $model;
        $this->from = $from;
        $this->to = $to;
        $this->parameters = $parameters;
    }

    public function handle(): void
    {
        $employee = $this->terminationTicket->employee;
        $employeeId = $employee->id;
        $employeeOrg = $employee->organizationInfo;
        $restoring = $this->terminationTicket->isRestoring ?? false;
        unset($this->terminationTicket->isRestoring);

        $requisitionApproved = ManpowerRequisition::where([['incumbent_id', $employeeId], ['state', WorkflowState::APPROVED]])->exists();

        if ($requisitionApproved) {
            throw new Exception("Termination can't be canceled as a replacement has been approved.");
        }

        $cancelledBy = currentEmployee();
        $terminationReason = isset($cancelledBy)
            ? '(Termination Cancelled by ' . ($cancelledBy->name . ' [' . $cancelledBy->employeeCode . ']') . ')'
            : '(Termination Cancelled by Admin)';

        if ($employeeOrg) {
            $employeeOrg->update([
                'termination_reason' => null,
                'termination_date' => null,
                'termination_request_date' => null
            ]);
            logCronInfo('[CheckTerminationAction] Employee Org Updated');

            $this->terminationTicket->update([
                'termination_reason' => $this->terminationTicket->termination_reason . ' ' . $terminationReason,
            ]);
            logCronInfo("[CheckTerminationAction] Termination Ticket Updated");

            $jobSeatRepo = app(\App\Http\Repositories\Configs\Interfaces\JobSeatRepositoryInterface::class);
            $jobId = $employee->getJobId();
            if (!$restoring && $jobId)
                $jobSeatRepo->revertIncreaseOnNotice([
                    'company_id' => $employee->company_id,
                    'branch_id' => $employeeOrg->branch_id,
                    'department_id' => $employeeOrg->department_id,
                    'job_id' => $jobId
                ]);
        }

        Log::info("Termination cancelled successfully for employee ID: {$employeeId}");

    }

    public function failed(): void
    {
        Log::error("Failed to create payslip ticket from action after endorsing");
        // Handle any cleanup or error logging here if the action fails.
    }
}
