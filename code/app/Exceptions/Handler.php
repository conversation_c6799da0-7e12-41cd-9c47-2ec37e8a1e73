<?php

namespace App\Exceptions;

use App\Http\Helpers\ApiResponse;
use App\Http\Helpers\ApiResponseHelper;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        if ($request->is('api/*') or $request->expectsJson()) {
            // if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            //     return response()->json([
            //         "status" => False,
            //         "status_code" => 401,
            //         "message" => "Unauthenticated"
            //     ], 401);
            // }

            // if ($e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
            //     return response()->json([
            //         "status" => False,
            //         "status_code" => 404,
            //         "message" => $e->getMessage()
            //     ], 404);
            // }

            // if ($e instanceof \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException) {
            //     return response()->json([
            //         "status" => False,
            //         "status_code" => 405,
            //         "message" => $e->getMessage()
            //     ], 405);
            // }

            if ($e instanceof \Illuminate\Validation\ValidationException) {
                return ApiResponse::validationError($e->errors());
            }

            if ($e instanceof \Error || $e instanceof \Exception) {
                return ApiResponse::serverError( $e->getMessage(), $e);
            }
        }

        return parent::render($request, $e);
    }
}
