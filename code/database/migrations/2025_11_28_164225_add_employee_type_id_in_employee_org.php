<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_org', function (Blueprint $table) {
            $table->unsignedBigInteger('employee_category_id')->nullable()->after('employee_id');
            $table->foreign('employee_category_id')->references('id')->on('employee_categories')->onDelete('RESTRICT');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_org', function (Blueprint $table) {
            $table->dropForeign(['employee_category_id']);
            $table->dropColumn('employee_category_id');
        });
    }
};
