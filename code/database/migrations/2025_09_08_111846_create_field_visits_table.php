<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_visits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->string('name', 255);
            $table->string('location', 255);
            $table->enum('type', ['customer', 'site']);
            $table->string('purpose', 255);
            $table->string('message', 255)->nullable();
            $table->decimal('check_in_lat', 10, 7)->nullable();
            $table->decimal('check_in_lon', 10, 7)->nullable();
            $table->decimal('check_out_lat', 10, 7)->nullable();
            $table->decimal('check_out_lon', 10, 7)->nullable();
            $table->text('description')->nullable();
            $table->json('form_meta')->nullable();
            $table->date('check_in_date_en');
            $table->date('check_out_date_en')->nullable();
            $table->string('check_in_date_np');
            $table->string('check_out_date_np')->nullable();
            $table->time('check_in_time');
            $table->time('check_out_time')->nullable();
            $table->string('check_in_ip', 45)->nullable();
            $table->string('check_out_ip', 45)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_visits');
    }
};
