<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            $table->foreignId('employee_category_id')
                ->after('added_employee_id')
                ->nullable()                    
                ->constrained('employee_categories')
                ->restrictOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            $table->dropForeign(['employee_category_id']);
            $table->dropColumn('employee_category_id');
        });
    }
};
