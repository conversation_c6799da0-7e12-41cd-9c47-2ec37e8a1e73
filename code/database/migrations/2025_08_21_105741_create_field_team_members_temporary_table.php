<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_team_members_temporary', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('field_teams')->onDelete('cascade');
            $table->json('member_details');
            
            $table->date('date');         
            $table->unique(['team_id', 'date'], 'temp_team_unique');
            
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_team_members_temporary');
    }
};
