<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('edf_mail_details', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('mrf_id')->constrained('manpower_requisitions')->onDelete('cascade');
            $table->string('email');
            $table->enum('status', ['Pending', 'Approved', 'Rejected','Added'])->default('Pending');
            $table->json('mail_meta')->nullable();
            $table->json('response_meta')->nullable();
            $table->text('reject_reason')->nullable();
            $table->string('doj');
            $table->string('name');
            $table->string('deadline');
            $table->string('shortcut')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('edf_mail_details');
    }
};
