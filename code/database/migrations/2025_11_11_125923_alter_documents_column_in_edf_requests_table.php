<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            if (!Schema::hasColumn('edf_requests', 'personal_documents')) {
                if (Schema::hasColumn('edf_requests', 'documents')) {
                    $table->renameColumn('documents', 'personal_documents');
                } else {
                    $table->json('personal_documents')->nullable()->after('pan_no');
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            if (Schema::hasColumn('edf_requests', 'personal_documents')) {
                $table->dropColumn('personal_documents');
            }
        });
    }
};
