<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_family_documents', function (Blueprint $table) {
            $table->id();

            $table->foreignId('family_id')
                ->constrained('employee_family_information')
                ->cascadeOnDelete();

            $table->string('document_path')->nullable();

            $table->enum('type', ['citizen_front', 'citizen_back', 'nid_front', 'nid_back']);

            $table->string('remarks')->nullable();

            $table->enum("status", ["Submitted", "Approved", "Rejected"])->default("Submitted");

            $table->foreignId('actioned_by')
                ->nullable()
                ->constrained('employees')
                ->nullOnDelete();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_family_documents');
    }
};
