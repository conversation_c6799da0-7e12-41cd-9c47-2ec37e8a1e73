<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_count_snapshots', function (Blueprint $table) {
            $table->id();
            $table->date('date_en');
            $table->string('date_np');
            $table->foreignId('branch_id')->references('id')->on('branches')->onDelete('restrict');
            $table->foreignId('department_id')->references('id')->on('departments')->onDelete('restrict');

            $table->unsignedInteger('active_employees_count')->default(0);
            $table->unsignedInteger('new_join_employees_count')->default(0);
            $table->unsignedInteger('terminated_employees_count')->default(0);
            $table->unsignedInteger('terminating_employees_count')->default(0);
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_count_snapshots');
    }
};
