<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->foreignId('employee_category_id')->nullable()->after('department_id')->references('id')->on('employee_categories')->onDelete('restrict');
            $table->dropUnique('uniq_emp_count_snap');
            $table->enum('gender', ['male', 'female'])->nullable()->after('employee_category_id');
            $table->enum('marital_status', ['married', 'unmarried'])->nullable()->after('gender');
            $table->enum('employment_type', ['inhouse', 'outsource'])->nullable()->after('marital_status');
            $table->enum('age_group', ['age_less_than_20', 'age_20_to_30', 'age_30_to_40', 'age_40_to_50', 'age_50_to_60', 'age_60_above'])->nullable()->after('employment_type');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id', 'employee_category_id', 'gender', 'marital_status', 'employment_type', 'age_group'], 'uniq_emp_count_snap');
        });

        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->foreignId('employee_category_id')->nullable()->after('department_id')->references('id')->on('employee_categories')->onDelete('restrict');
            $table->dropUnique('uniq_att_comp_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id', 'employee_category_id'], 'uniq_att_comp_snap');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->dropUnique('uniq_emp_count_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id'], 'uniq_emp_count_snap');
            $table->dropForeign(['employee_category_id']);
            $table->dropColumn('employee_category_id');
            $table->dropColumn('gender');
            $table->dropColumn('marital_status');
            $table->dropColumn('employment_type');
            $table->dropColumn('age_group');
        });

        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->dropUnique('uniq_att_comp_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id'], 'uniq_att_comp_snap');
            $table->dropForeign(['employee_category_id']);
            $table->dropColumn('employee_category_id');
        });
    }
};
