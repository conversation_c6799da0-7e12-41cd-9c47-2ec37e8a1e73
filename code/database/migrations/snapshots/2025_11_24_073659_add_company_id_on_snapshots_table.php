<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->foreignId('company_id')->after('date_np')->nullable()->references('id')->on('companies')->onDelete('restrict');

            $table->dropUnique('uniq_att_comp_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id'], 'uniq_att_comp_snap');
        });
        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->foreignId('company_id')->after('date_np')->nullable()->references('id')->on('companies')->onDelete('restrict');

            $table->dropUnique('uniq_emp_count_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id'], 'uniq_emp_count_snap');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropUnique('uniq_att_comp_snap');
            $table->dropColumn('company_id');
            $table->unique(['date_en', 'branch_id', 'department_id'], 'uniq_att_comp_snap');
        });

        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
            $table->dropUnique('uniq_emp_count_snap');
            $table->dropColumn('company_id');
            $table->unique(['date_en', 'branch_id', 'department_id'], 'uniq_emp_count_snap');
        });
    }
};
