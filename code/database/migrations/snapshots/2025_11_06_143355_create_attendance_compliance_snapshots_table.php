<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->id();
            $table->date('date_en');
            $table->string('date_np');
            $table->foreignId('branch_id')->references('id')->on('branches')->onDelete('restrict');
            $table->foreignId('department_id')->references('id')->on('departments')->onDelete('restrict');

            $table->unsignedInteger('active_employee_count')->default(0);
            $table->unsignedInteger('present_count')->default(0);
            $table->unsignedInteger('absent_count')->default(0);
            $table->unsignedInteger('punctual_count')->default(0);
            $table->unsignedInteger('late_in_count')->default(0);
            $table->unsignedInteger('early_out_count')->default(0);
            $table->unsignedInteger('leave_count')->default(0);

            $table->timestamps();

            $table->unique(['date_en', 'branch_id', 'department_id'], 'uniq_att_comp_snap');

            // Indexes for dashboard filters:
            $table->index(['date_en']);
            $table->index(['branch_id', 'date_en']);
            $table->index(['department_id', 'date_en']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_compliance_snapshots');
    }
};
