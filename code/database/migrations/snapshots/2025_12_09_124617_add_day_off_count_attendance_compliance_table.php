<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE employee_count_snapshots MODIFY gender ENUM('male', 'female', 'other') NULL");

        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->unsignedInteger('day_off_count')->default(0)->after('leave_count');
            $table->enum('employment_type', ['inhouse', 'outsource'])->nullable()->after('employee_category_id');
            $table->dropUnique('uniq_att_comp_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id', 'employee_category_id', 'employment_type'], 'uniq_att_comp_snap');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE employee_count_snapshots MODIFY gender ENUM('male', 'female') NULL");

        Schema::table('attendance_compliance_snapshots', function (Blueprint $table) {
            $table->dropColumn('day_off_count');
            $table->dropUnique('uniq_att_comp_snap');
            $table->unique(['date_en', 'branch_id', 'department_id', 'company_id', 'employee_category_id'], 'uniq_att_comp_snap');
            $table->dropColumn('employment_type');
        });
    }
};
