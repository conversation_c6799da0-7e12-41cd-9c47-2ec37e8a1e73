<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->unique(['date_en', 'branch_id', 'department_id'], 'uniq_emp_count_snap');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('employee_count_snapshots', function (Blueprint $table) {
            $table->dropUnique('uniq_emp_count_snap');
        });
    }
};
