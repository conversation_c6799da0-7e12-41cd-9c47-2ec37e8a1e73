<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_team_members_main', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('field_teams')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->boolean('is_team_leader')->default(false);
            $table->boolean('is_active')->default(true);
            $table->date('assigned_date')->nullable();

            $table->unique(['team_id', 'employee_id']);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_team_members');
    }
};
