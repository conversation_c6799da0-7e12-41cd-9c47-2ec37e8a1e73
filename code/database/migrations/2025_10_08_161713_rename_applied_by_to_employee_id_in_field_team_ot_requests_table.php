<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->dropForeign(['applied_by']);
            DB::statement("ALTER TABLE field_team_ot_requests CHANGE applied_by employee_id BIGINT UNSIGNED NOT NULL");
        });

        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->dropForeign(['employee_id']);
            DB::statement("ALTER TABLE field_team_ot_requests CHANGE employee_id applied_by BIGINT UNSIGNED NOT NULL");
        });

        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->foreign('applied_by')->references('id')->on('employees')->onDelete('cascade');
        });
    }
};
