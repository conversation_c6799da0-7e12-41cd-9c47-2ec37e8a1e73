<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->text('remarks')->nullable()->after('total_ot_hours');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_team_ot_requests', function (Blueprint $table) {
            $table->dropColumn('remarks');
        });
    }
};
