<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_teams', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('team_type')->constrained('field_team_types')->onDelete('cascade');
            $table->foreignId('transport_type')->constrained('field_transport_types')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('operation_center')->nullable()->constrained('sub_branches')->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users'); // IT department user
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_teams');
    }
};
