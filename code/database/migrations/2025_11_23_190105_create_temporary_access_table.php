<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('temporary_access', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('from_employee_id');
            $table->unsignedBigInteger('temporary_employee_id');
            $table->json('role_ids')->nullable();
            $table->json('transition_performers')->nullable();

            $table->date('from');
            $table->date('to');

            $table->string('status'); // pending, active, expired

            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();

            $table->foreign('temporary_employee_id')->on('employees')->references('id')->onDelete('cascade');
            $table->foreign('from_employee_id')->on('employees')->references('id')->onDelete('cascade');
            $table->foreign('created_by')->on('users')->references('id')->onDelete('cascade');
            $table->foreign('updated_by')->on('users')->references('id')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temporary_access');
    }
};
