<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('added_employee_id')->nullable()->after('employee_id')->comment('Employee ID who added the request');

            $table->foreign('added_employee_id')->references('id')->on('employees')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            $table->dropForeign(['added_employee_id']);
            $table->dropColumn('added_employee_id');
        });
    }
};
