<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('device_details', function (Blueprint $table) {
            DB::statement("ALTER TABLE device_details MODIFY status ENUM('active', 'inactive', 'pending', 'rejected') DEFAULT 'inactive'");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_details', function (Blueprint $table) {
            DB::statement("ALTER TABLE device_details MODIFY status ENUM('active', 'inactive') DEFAULT 'inactive'");
        });
    }
};
