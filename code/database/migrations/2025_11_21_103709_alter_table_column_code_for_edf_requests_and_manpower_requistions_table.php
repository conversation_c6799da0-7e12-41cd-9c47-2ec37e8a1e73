<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('edf_requests', 'documents') && !Schema::hasColumn('edf_requests', 'personal_documents')) {
            DB::statement("ALTER TABLE edf_requests CHANGE `documents` `personal_documents` TEXT NULL");
        }
        if (Schema::hasColumn('manpower_requisitions', 'kra')) {
            DB::statement("ALTER TABLE manpower_requisitions MODIFY `kra` TEXT NULL");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('edf_requests', 'personal_documents')) {
            DB::statement("ALTER TABLE edf_requests CHANGE `personal_documents` `documents` TEXT NULL");
        }
        if (Schema::hasColumn('manpower_requisitions', 'kra')) {
            DB::statement("ALTER TABLE manpower_requisitions MODIFY `kra` VARCHAR(255) NULL");
        }
    }
};
