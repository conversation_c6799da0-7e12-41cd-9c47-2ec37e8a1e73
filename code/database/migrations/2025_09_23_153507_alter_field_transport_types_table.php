<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_transport_types', function (Blueprint $table) {
            $table->float('total_number_of_seats')->nullable()->after('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_transport_types', function (Blueprint $table) {
            $table->dropColumn('total_number_of_seats');
        });
    }
};
