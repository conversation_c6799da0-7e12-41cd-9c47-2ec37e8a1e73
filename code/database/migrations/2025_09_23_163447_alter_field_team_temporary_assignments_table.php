<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('field_team_temporary_assignments', function (Blueprint $table) {
            $table->date('assigned_at')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('field_team_temporary_assignments', function (Blueprint $table) {
            $table->date('assigned_at')->change();
        });   
    }
};
