<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_team_ot_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('field_teams')->onDelete('cascade');
            $table->foreignId('applied_by')->nullable()->constrained('employees');
            $table->string('nep_date');
            $table->time('start_time');
            $table->time('end_time');
            $table->json('leader_attendance')->nullable();
            $table->json('members_attendance')->nullable();

            $table->string('total_working_hours')->nullable();
            $table->string('total_ot_hours')->nullable();
            $table->arflow();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_team_ot_requests');
    }
};
