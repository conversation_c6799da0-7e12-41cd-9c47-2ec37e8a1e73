<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            $table->json('transition_meta')
                ->nullable()
                ->after('optional_field_meta');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('edf_requests', function (Blueprint $table) {
            //$table->dropColumn('transition_meta');

        });
    }
};
