<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_team_temporary_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('field_teams')->onDelete('cascade');
            $table->foreignId('employee_id')->constrained('employees')->onDelete('cascade');
            $table->date('assigned_at');
            $table->date('removed_at')->nullable(); // Nullable for active assignments
            $table->timestamps();
            $table->unique(['team_id', 'employee_id', 'assigned_at'], 'temp_assignment_log');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_team_temporary_assignments');
    }
};
