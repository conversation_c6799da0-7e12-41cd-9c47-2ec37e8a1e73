<?php

namespace Database\Seeders;

use App\Http\Helpers\Constant;
use App\Http\Helpers\Enums\WorkflowName;
use App\Http\Helpers\Enums\WorkflowPerformer;
use App\Http\Helpers\Enums\WorkflowState;
use App\Models\Arflow\TransitionPerformer;
use App\Models\configs\FiscalYear;
use App\Models\configs\LeaveType;
use App\Models\Employee\Employee;
use App\Models\Leaves\EmployeeLeaveDetail;
use CodeBright\LaravelNepaliDate\LaravelNepaliDate;
use Illuminate\Database\Seeder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LeaveRequestSeeder extends Seeder
{
    // ---- Tunables ----
    private const START_DATE = '-30 days';
    private const END_DATE   = 'yesterday';

    // With this chance, a given employee-day becomes a leave start day
    private const LEAVE_REQUEST_RATE = 0.10;  // 12% of days produce a request

    // Distribution of final states (must sum to 1.0)
    private const STATE_WEIGHTS = [
        WorkflowState::APPROVED => 0.70,
        WorkflowState::REJECTED => 0.20,
        WorkflowState::CANCELLED => 0.10,
    ];

    // Leave length in days (inclusive)
    private const MIN_SPAN_DAYS = 1;
    private const MAX_SPAN_DAYS = 3;

    // Optional: avoid overlapping requests per employee (basic guard)
    private const AVOID_OVERLAP = true;

    public function run(): void
    {
        // Preload lookups
        $fiscalYearId = FiscalYear::activeFiscalYearId();

        $leaveTypeIds = LeaveType::query()
            ->where('name', '!=', Constant::REPLACEMENT_LEAVE_NAME)
            ->pluck('id')
            ->all();

        if (empty($leaveTypeIds)) {
            $this->command?->warn('No eligible leave types found. Skipping.');
            return;
        }

        $leaveOptionIds = DB::table('leave_options')->pluck('id')->all();
        if (empty($leaveOptionIds)) {
            $this->command?->warn('No leave options found. Skipping.');
            return;
        }

        // Build recipient -> approver list map once (fast lookups)
        $approverMap = TransitionPerformer::query()
            ->where('workflow', WorkflowName::LEAVE_APPROVAL)
            ->where('state', WorkflowPerformer::APPROVER)
            ->get()
            ->groupBy('recipient_id') // employee_id
            ->map(fn ($rows) => $rows->pluck('performer_id')->filter()->values()->all())
            ->all();

        $start = Carbon::parse(self::START_DATE)->startOfDay();
        $end   = Carbon::parse(self::END_DATE)->endOfDay();

        $employees = Employee::query()->select('id')->get();
        $totalLR = 0;
        $totalDetails = 0;

        foreach ($employees as $emp) {
            // Track simple occupied ranges (only if avoiding overlap)
            $occupied = [];

            $cursor = $start->copy();
            while ($cursor->lte($end)) {
                // probabilistic start of a leave
                if (!$this->chance(self::LEAVE_REQUEST_RATE)) {
                    $cursor->addDay();
                    continue;
                }

                // random span; clamp to window
                $span = random_int(self::MIN_SPAN_DAYS, self::MAX_SPAN_DAYS);
                $lrStart = $cursor->copy();
                $lrEnd   = $cursor->copy()->addDays($span - 1);
                if ($lrEnd->gt($end)) {
                    $lrEnd = $end->copy();
                }

                // avoid overlap (basic guard using Y-m-d keys)
                if (self::AVOID_OVERLAP && $this->rangeOverlaps($occupied, $lrStart, $lrEnd)) {
                    $cursor->addDay();
                    continue;
                }

                $leaveTypeId   = $this->pickRandom($leaveTypeIds);
                $leaveOptionId = $this->pickRandom($leaveOptionIds);
                $state         = $this->pickStateWeighted(self::STATE_WEIGHTS);

                DB::transaction(function () use (
                    $emp, $leaveTypeId, $leaveOptionId, $fiscalYearId,
                    $lrStart, $lrEnd, $state, $approverMap, &$totalLR, &$totalDetails
                ) {
                    // create leave_requests
                    $leaveRequestId = DB::table('leave_requests')->insertGetId([
                        'leave_type_id'   => $leaveTypeId,
                        'leave_option_id' => $leaveOptionId,
                        'employee_id'     => $emp->id,
                        'start_date'      => $lrStart->toDateString(),
                        'end_date'        => $lrEnd->toDateString(),
                        'nep_start_date'  => LaravelNepaliDate::from($lrStart)->toNepaliDate(),
                        'nep_end_date'    => LaravelNepaliDate::from($lrEnd)->toNepaliDate(),
                        'fiscal_year_id'  => $fiscalYearId,
                        'num_days'        => $lrStart->diffInDays($lrEnd) + 1,
                        'applied_status'  => 'After',
                        'remarks'         => 'From Seeder',
                        'workflow'        => WorkflowName::LEAVE_APPROVAL,
                        'state'           => $state,
                        'created_at'      => now(),
                        'updated_at'      => now(),
                    ]);

                    // request_tickets
                    DB::table('request_tickets')->insert([
                        'model_type'   => \App\Models\Leaves\LeaveRequest::class,
                        'workflow'     => WorkflowName::LEAVE_APPROVAL,
                        'model_id'     => $leaveRequestId,
                        'employee_id'  => $emp->id,
                        'submitted_by' => $emp->id,
                        'state'        => $state,
                        'created_at'   => now(),
                        'updated_at'   => now(),
                    ]);

                    // submitted transition
                    DB::table('arflow_state_transitions')->insert([
                        'workflow'         => WorkflowName::LEAVE_APPROVAL,
                        'model_type'       => \App\Models\Leaves\LeaveRequest::class,
                        'model_id'         => $leaveRequestId,
                        'actor_model_type' => Employee::class,
                        'actor_model_id'   => $emp->id,
                        'comment'          => 'Applied from seeder',
                        'from'             => '',
                        'to'               => WorkflowState::SUBMITTED,
                        'created_at'       => now(),
                        'updated_at'       => now(),
                    ]);

                    // approval/reject/cancel transition
                    $approverId = $this->pickApproverId($approverMap, $emp->id) ?? $emp->id;

                    DB::table('arflow_state_transitions')->insert([
                        'workflow'         => WorkflowName::LEAVE_APPROVAL,
                        'model_type'       => \App\Models\Leaves\LeaveRequest::class,
                        'model_id'         => $leaveRequestId,
                        'actor_model_type' => Employee::class,
                        'actor_model_id'   => $approverId,
                        'comment'          => $state.' from seeder',
                        'from'             => WorkflowState::SUBMITTED,
                        'to'               => $state,
                        'created_at'       => now(),
                        'updated_at'       => now(),
                    ]);

                    // details if approved
                    if ($state === WorkflowState::APPROVED) {
                        $details = [];
                        $d = $lrStart->copy();
                        while ($d->lte($lrEnd)) {
                            $details[] = [
                                'employee_id'      => $emp->id,
                                'leave_request_id' => $leaveRequestId,
                                'date'             => $d->toDateString(),
                                'nep_date'         => LaravelNepaliDate::from($d)->toNepaliDate(),
                                'leave_type_id'    => $leaveTypeId,
                                'leave_option_id'  => $leaveOptionId,
                                'num_days'         => 1,
                                'created_at'       => now(),
                                'updated_at'       => now(),
                                'fiscal_year_id'   => $fiscalYearId,
                                'remarks'          => 'From Seeder',
                                'performers'       => json_encode(['approved' => $approverId]),
                            ];
                            $d->addDay();
                        }
                        if (!empty($details)) {
                            // bulk insert for speed
                            EmployeeLeaveDetail::insert($details);
                            $totalDetails += count($details);
                        }
                    }

                    $totalLR++;
                });

                // mark occupied range (basic overlap avoidance)
                if (self::AVOID_OVERLAP) {
                    $this->occupyRange($occupied, $lrStart, $lrEnd);
                }

                // move cursor forward to end of leave + 1
                $cursor = $lrEnd->copy()->addDay();
            }
        }

        $this->command?->info("Seeded {$totalLR} leave requests; {$totalDetails} leave details.");
    }

    // ---------- helpers ----------

    private function chance(float $p): bool
    {
        return mt_rand() / mt_getrandmax() < $p;
    }

    private function pickRandom(array $arr)
    {
        return $arr[array_rand($arr)];
    }

    private function pickStateWeighted(array $weights): string
    {
        // weights like ['APPROVED'=>0.7, 'REJECTED'=>0.2, 'CANCELLED'=>0.1]
        $r = mt_rand() / mt_getrandmax();
        $c = 0.0;
        foreach ($weights as $state => $w) {
            $c += $w;
            if ($r <= $c) return $state;
        }
        // fallback (floating errors)
        return array_key_first($weights);
    }

    private function pickApproverId(array $approverMap, int $employeeId): ?int
    {
        $list = $approverMap[$employeeId] ?? null;
        if (empty($list)) return null;
        return $list[array_rand($list)];
    }

    private function rangeOverlaps(array $occupied, Carbon $from, Carbon $to): bool
    {
        $d = $from->copy();
        while ($d->lte($to)) {
            if (isset($occupied[$d->toDateString()])) return true;
            $d->addDay();
        }
        return false;
    }

    private function occupyRange(array &$occupied, Carbon $from, Carbon $to): void
    {
        $d = $from->copy();
        while ($d->lte($to)) {
            $occupied[$d->toDateString()] = true;
            $d->addDay();
        }
    }
}
