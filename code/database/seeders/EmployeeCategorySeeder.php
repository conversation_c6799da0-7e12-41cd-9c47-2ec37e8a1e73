<?php

namespace Database\Seeders;

use App\Models\Employee\EmployeeOrg;
use App\Models\EmployeeCategory;
use Illuminate\Database\Seeder;

class EmployeeCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            [
                "id" => 1,
                "type" => "Regular",
                "is_countable" => true,
            ],
            [
                "id" => 2,
                "type" => "Intern",
                "is_countable" => true,
            ],
            [
                "id" => 3,
                "type" => "House Keeping",
                "is_countable" => true,
            ],
            [
                "id" => 4,
                "type" => "Auditor",
                "is_countable" => true,
            ]
        ];

        foreach ($types as $t) {
            EmployeeCategory::firstOrCreate(
                ['id' => $t['id']],    
                [
                    'id' => $t['id'],
                    'type' => $t['type'],
                    'is_countable' => $t['is_countable'],
                    'company_id' => 1,
                    
                ]
            );
        }
        
        EmployeeOrg::whereNull("employee_category_id")
                    ->update(["employee_category_id" => "1"]);
    }

}
